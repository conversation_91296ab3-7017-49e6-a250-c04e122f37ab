#pragma raygen PathTracing

#pragma only_renderers vulkan

#define DEFERRED_SHADING
#define RAY_TRACING_PASS
#define USE_IN_COMPUTE_SHADER
#define CE_USE_DOUBLE_TRANSFORM

#include "ShaderLibrary/Common.hlsl"
#include "../RayTracing/RayTracingCommonStruct.hlsl"
#include "../RayTracing/RayTracingCommonResource.hlsl"
#include "../ShaderLibrary/Common.hlsl"
#include "../Material/NormalBuffer.hlsl"
#include "../Material/MaterialCommon.hlsl"
#include "ShaderLibrary/GlobalModelVariables.hlsl"
#include "Material/Lit/LitUEVariables.hlsl"
#include "../Material/Lit/GbufferEncoderDecoder.hlsl"
#include "../Material/BSDF.hlsl"
#include "PathTracingCore.hlsl"


cbuffer PTConstant : register(space0) 
{
    uint numAccumulatedFrames;
    uint maxAccumulatedFrames;
    uint lightCount;
    uint maxBounce;
    uint frameCount;
    float maxPathIntensity;
    bool ENABLE_SKY_LIGHT_REALTIME_CAPTURE;
    uint sampleMode;
    bool hasSkyLight;
    bool enableTemporalAccumulation;
}

RWTexture2D<float4> PathTracingResult;
Texture2D<float4> LastFramePathTracingResult;

[shader("raygeneration")]
void PathTracing()
{
    using namespace CE_PathTracing;

    uint2 index = DispatchRaysIndex().xy;
    uint2 dim   = DispatchRaysDimensions().xy;

    uint seed = tea(index.x * dim.y + index.y, numAccumulatedFrames + frameCount);
    float2 jitter = rnd2(seed) / 2.f;
    float2 screenUV = (index + 0.5f + jitter) / dim;

    float3 directLighting = 0.xxx;
    float3 indirectLighting = 0.xxx;

    float2 screenCoord = float2(screenUV.x * 2.f - 1.f, (1 - screenUV.y) * 2.f - 1.f);
    float3 cameraRayDir = ScreenPositionToWorldDirection(float3(screenCoord, 1.f), ce_InvView, ce_InvProjection);

    if (numAccumulatedFrames >= maxAccumulatedFrames)
    {
        PathTracingResult[index] = float4(LastFramePathTracingResult[index].rgb, 1.f);
    }
    else
    {
        PathTracingPayload ptPayload = PathTracingCore(ce_CameraPos, cameraRayDir, sampleMode, lightCount, hasSkyLight, ENABLE_SKY_LIGHT_REALTIME_CAPTURE, 
            seed, maxBounce, 1.f, MAX_TRACE_DISTANCE, maxPathIntensity);

        float3 lastFrameResult = LastFramePathTracingResult[index].rgb;
        float factor = enableTemporalAccumulation ? 1.f / (numAccumulatedFrames + 1) : 1.f;
        float3 currentFrameResult = 0.xxx;
        if (ptPayload.IsHit || ptPayload.HitSky)
        {
            currentFrameResult = ptPayload.Radiance;
        }
        // currentFrameResult = ptPayload.Debug;
        float3 result = lastFrameResult + (currentFrameResult - lastFrameResult) * factor;
        PathTracingResult[index] = float4(result, 1.f);
    }
}
