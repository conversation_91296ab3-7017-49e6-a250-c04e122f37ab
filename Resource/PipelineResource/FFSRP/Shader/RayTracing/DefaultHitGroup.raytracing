#pragma raygen raygen
#pragma closesthit default_closesthit
#pragma anyhit default_anyhit
#pragma miss default_miss

#pragma only_renderers vulkan

#define RAY_TRACING_PASS

#include "../RayTracing/RayTracingCommonStruct.hlsl"
#include "../ShaderLibrary/BindlessCommon.hlsl"


[shader("closesthit")]
void default_closesthit(inout RayTracingPayload payload, in RayTracingAttributes attrib) 
{
    payload.SurfaceData.EncodeBaseColor(float3(1.f, 0.f, 1.f));
    payload.HitT = RayTCurrent();
    payload.Bary = attrib.bary;
    payload.Seed = 0xffffffff;
}

[shader("anyhit")]
void default_anyhit(inout RayTracingPayload payload, in RayTracingAttributes attrib)
{

}

[shader("miss")]
void default_miss(inout RayTracingPayload payload)
{
    payload.HitT = -1.f;
}
