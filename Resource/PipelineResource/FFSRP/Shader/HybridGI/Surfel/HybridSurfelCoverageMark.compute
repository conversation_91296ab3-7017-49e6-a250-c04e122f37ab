#pragma compute SurfelCoverageMark

#include "../Common.hlsl"
// #include "../FastMath.ush"
#include "../ScreenTextures.hlsl"

#define VF_SUPPORTS_PRIMITIVE_SCENE_DATA 1
//#include "../SceneData.hlsl"

#define OCTAHEDRAL_COMMON 1
#include "../HybridGICommon.hlsl"
#include "HybridSurfelCommon.hlsl"
#include "../../ShaderLibrary/RandomNumberUtils.hlsl"


StructuredBuffer<uint> SurfelCellBuffer;
StructuredBuffer<FSurfelGridCell> SurfelGridBuffer;

RWStructuredBuffer<FSurfel> SurfelBuffer;

RWByteAddressBuffer SurfelStatsBuffer;
//FRWTexture2D<float2> SurfelMomentsTexture;
//SamplerState LinearSampler;
RWStructuredBuffer<uint>SurfelCoverageMarkBuffer;



groupshared uint GroupMinSurfelCount;
float GHybridSurfelMinCoverage;

/** 
Module:
	Surfelization - Pass3_1

Description:
	Calculcate coverage and generate new surfel

Input: 
	StructuredBuffer<uint> SurfelDeadBuffer;
	StructuredBuffer<uint> SurfelCellBuffer;
	StructuredBuffer<FSurfelGridCell> SurfelGridBuffer;
	RWByteAddressBuffer SurfelStatsBuffer;

Output:
	RWStructuredBuffer<FSurfel> SurfelBuffer;
	RWStructuredBuffer<FSurfelData> SurfelDataBuffer;
	RWStructuredBuffer<FSurfelEstimatorData> SurfelEstimatorDataBuffer;
	RWStructuredBuffer<uint> SurfelAliveBuffer;
	RWByteAddressBuffer SurfelStatsBuffer;
*/

static uint2 PixelCoordOffset[4] = 
{
	uint2(1, 0),
	uint2(1, 1),
	uint2(0, 1),
	uint2(0, 0)
};

// each tile has THREADCOUNT*THREADCOUNT pixels
static const uint THREADCOUNT = 16;
[numthreads(THREADCOUNT, THREADCOUNT, 1)]
void SurfelCoverageMark(
	uint2 GroupId : SV_GroupID,
	uint2 DispatchThreadId : SV_DispatchThreadID,
	uint2 GroupThreadId : SV_GroupThreadID,
	uint groupIndex : SV_GroupIndex)
{
	if (groupIndex == 0)
	{
		GroupMinSurfelCount = ~0;
	}

	GroupMemoryBarrierWithGroupSync();

    uint2 PixelCoord = 0.xx;
    if (SURFEL_COVERAGE_HALFRES)
    {
	    //@TODO：need better jittering
        PixelCoord = DispatchThreadId.xy * 2 + _View_RectMin.xy + PixelCoordOffset[(_View_StateFrameIndexMod8) % 4];
    }
    else
    {
        PixelCoord = DispatchThreadId.xy + _View_RectMin.xy;
    }

	if (any(PixelCoord > _View_SizeAndInvSize.xy)){
		return;
	}

    float2 ScreenUV = (PixelCoord.xy + 0.5f) * _View_BufferSizeAndInvSize.zw;
    float2 ScreenPosition = (ScreenUV.xy - _View_ScreenPositionScaleBias.wz) / _View_ScreenPositionScaleBias.xy;
    FGBufferData GBufferData = GetGBufferData(ScreenUV);
	
    float DeviceZ = GBufferData.Depth; //_DepthMap[PixelCoord].x;


    if ( /*ShadingModelID == SHADINGMODELID_UNLIT ||*/DeviceZ == FarDepthValue)
	{
		return;
	}
	
	//const float2 UV = (PixelCoord + float2(0.5f, 0.5f)) / float2(PrimaryView.BufferSizeAndInvSize.xy);

    const float3 TranslatedPosition = GetWorldPositionFromScreenPosition(ScreenPosition, DeviceZ);
    const float3 WorldPosition = GetLargeCoordinateAbsolutePosition(TranslatedPosition, ce_CameraTilePosition);
								 
	int3 GridPos = SurfelCell(WorldPosition);
	if (!SurfelCellValid(GridPos))
	{
		return;
	}

	//@TODO: GBufferD no longer has emissive
	//float3 Emissive = GetGBufferEmissive(GBufferData);
	float3 Emissive = float3(0, 0, 0);

	bool bPixelHasEmissive = any(Emissive);

	int CellIndex = SurfelCellIndex(GridPos);
	FSurfelGridCell Cell = SurfelGridBuffer[CellIndex];

	//@Todo It maybe not a good method
	/*
	if(Cell.GetCount() > HYBRID_SURFEL_CELL_LIMIT){
		return;
	}
	*/

	const float3 SceneNormal = normalize(GBufferData.WorldNormal);
	const uint objCullingGUID = GBufferData.ObjectCullingGUID;

	float Coverage = 0;
	float NearestSurfelDistSq = 100 * HYBRID_SURFEL_MAX_RADIUS * HYBRID_SURFEL_MAX_RADIUS;
	
	float3 LastSurfelWorldPosition = 0;
	float3 LastSurfelWorldNormal = 0;
	bool bCellHasSameOBJCullingGUIID = false;

	for (uint i = 0 ; i < Cell.GetCount(); ++i)
	{
		uint SurfelIndex = SurfelCellBuffer[Cell.GetOffSet() + i];
		FSurfel Surfel = SurfelBuffer[SurfelIndex];

		float3 L = WorldPosition - Surfel.GetWorldPosition();
		float DistSquared = dot(L, L);
		float3 SurfelNormal = Surfel.GetWorldNormal();
		float dotN = dot(SceneNormal, SurfelNormal);

#if SURFEL_RECYCLE_ENABLE
		{
			//Determine whether generation is required based on the distance
			NearestSurfelDistSq = min(NearestSurfelDistSq, DistSquared);
			float SurfelDistLimit = 3.0f * HYBRID_SURFEL_MAX_RADIUS * HYBRID_SURFEL_MAX_RADIUS;

			//SurfelDistLimit = bPixelHasEmissive ? SurfelDistLimit / 5.0f : SurfelDistLimit;
#if BSURFEL_USE_EMISSIVE
			if(NearestSurfelDistSq < SurfelDistLimit && !bPixelHasEmissive)
#else
			if(NearestSurfelDistSq < SurfelDistLimit)
#endif
			{
				return;
			}
		}
#endif

		if (DistSquared < Square(Surfel.GetRadius()))
		{
			if (dotN > 0)
			{
				float Dist = sqrt(DistSquared);
				float Contribution = 1;
				Contribution *= saturate(dotN);
				Contribution *= saturate(1 - Dist / HYBRID_CELL_MAX_RADIUS);
				Contribution = smoothstep(0, 1, Contribution);
				Coverage += Contribution;
                bCellHasSameOBJCullingGUIID = bCellHasSameOBJCullingGUIID | (objCullingGUID == Surfel.GetObjCullingGUID());
            }
		}
	}

	// generate surfel according to different PrimID
	Coverage *= (bCellHasSameOBJCullingGUIID? 1.0f : 0.0f);
	Coverage /= (Cell.GetCount() + 1.);

	uint LinearIndex = PixelCoord.y * uint(_View_BufferSizeAndInvSize.x) + PixelCoord.x;
    FRandomContext RandContext = FRandomContext::Create(LinearIndex, _View_StateFrameIndex);
// #if BSURFEL_USE_EMISSIVE
// 	//if (Cell.GetCount() < HYBRID_SURFEL_CELL_LIMIT || bPixelHasEmissive)
// #else
// 	//if (Cell.GetCount() < HYBRID_SURFEL_CELL_LIMIT)
// #endif
	{
		uint PixelSurfelCount = 0;
		PixelSurfelCount |= (uint(Coverage * 255.) & 0xFF) << 24; // the upper bits matter most for min selection
		PixelSurfelCount |= (uint(RandContext.GenerateSample1D() * 65535) & 0xFFFF) << 8; // shuffle pixels randomly
		PixelSurfelCount |= (GroupThreadId.x & 0xF) << 4;
		PixelSurfelCount |= (GroupThreadId.y & 0xF) << 0;
		InterlockedMin(GroupMinSurfelCount, PixelSurfelCount);
	}

	GroupMemoryBarrier();

// #if BSURFEL_USE_EMISSIVE
// 	if (Cell.GetCount() < HYBRID_SURFEL_CELL_LIMIT || bPixelHasEmissive)
// #else
// 	if (Cell.GetCount() < HYBRID_SURFEL_CELL_LIMIT)
// #endif
	{
		uint2 minGroupThreadId;
		minGroupThreadId.x = (GroupMinSurfelCount >> 4) & 0xF;
		minGroupThreadId.y = (GroupMinSurfelCount >> 0) & 0xF;

		// SurfelCoverage is in the range of [0., 255.)
		float SurfelCoverage = float(GroupMinSurfelCount >> 24);
		
		if (GroupThreadId.x == minGroupThreadId.x && GroupThreadId.y == minGroupThreadId.y && SurfelCoverage < GHybridSurfelMinCoverage * 255.)
		{
			// Slow down the propagation by chance
			//	Closer surfaces have less chance to avoid excessive clumping of surfels
			float ZFar = ConvertFromDeviceZ(FarDepthValue, ce_Projection);
			const float LinearDepth = DeviceZ;
			const float GenerateSlowdown = 0.5f;

            float Chance = 1 - pow(LinearDepth, GenerateSlowdown);
            if (SURFEL_COVERAGE_HALFRES)
            {
                Chance = 1 - pow(LinearDepth, GenerateSlowdown * 2);
            }

			if (RandContext.GenerateSample1D() < Chance)
			{
				return;
			}
			
			//
			int UpdateCount = 0;
			SurfelStatsBuffer.InterlockedAdd(HYBRID_SURFEL_STATS_OFFSET_GENERATECOUNT, 1, UpdateCount);
			uint UpdateCoord = 0;
			UpdateCoord |= ( PixelCoord.x & 0xFFFF ) << 16;
			UpdateCoord |= ( PixelCoord.y & 0xFFFF ) << 0;
			SurfelCoverageMarkBuffer[UpdateCount] = UpdateCoord;

		}
	}
}