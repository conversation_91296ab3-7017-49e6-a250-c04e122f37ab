#pragma compute SurfelGridOffsets

#include "../Common.hlsl"
//#include "../FastMath.ush"

#define OCTAHEDRAL_COMMON 1
#include "../HybridGICommon.hlsl"
#include "HybridSurfelCommon.hlsl"

RWStructuredBuffer<FSurfelGridCell> SurfelGridBuffer;
RWByteAddressBuffer SurfelStatsBuffer;

#ifndef THREADGROUP_SIZE
#define THREADGROUP_SIZE 1
#endif

/** 
Module:
	Update - Pass1_3

Description:
	calculate cell offset and cell count by using SurfelGridBuffer[DTid.x];

Input: 
	RWStructuredBuffer<FSurfelGridCell> SurfelGridBuffer;		// A Buffer for SurfelGridCells (Cell Pointer and Cell count) for each Grid

Output:
	RWStructuredBuffer<FSurfelGridCell> SurfelGridBuffer;		// A Buffer for SurfelGridCells (Cell Pointer and Cell count) for each Grid
	RWByteAddressBuffer SurfelStatsBuffer;						// A Address buffer for string offset and counter
*/

[numthreads(64, THREADGROUP_SIZE, THREADGROUP_SIZE)]
void SurfelGridOffsets(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	FSurfelGridCell Cell = SurfelGridBuffer[DispatchThreadId.x];


	////////////////////////////////////////////////
	// !! Setup cell offset
	uint offset;
	SurfelStatsBuffer.InterlockedAdd(HYBRID_SURFEL_STATS_OFFSET_CELLALLOCATOR, Cell.count, offset);
	Cell.SetOffSet(offset);
	Cell.SetCount(0);
	SurfelGridBuffer[DispatchThreadId.x] = Cell;
}