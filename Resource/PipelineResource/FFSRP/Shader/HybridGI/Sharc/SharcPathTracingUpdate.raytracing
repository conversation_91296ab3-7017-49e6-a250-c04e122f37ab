#pragma raygen SharcPathTracingUpdate

#pragma only_renderers vulkan

#define DEFERRED_SHADING
#define RAY_TRACING_PASS
#define USE_IN_COMPUTE_SHADER
#define CE_USE_DOUBLE_TRANSFORM

// SHaRC specific defines
#define SHARC_UPDATE 1
#define SHARC_QUERY 0
#define SHARC_ENABLE_64_BIT_ATOMICS 1

#include "ShaderLibrary/Common.hlsl"
#include "../RayTracing/RayTracingCommonStruct.hlsl"
#include "../RayTracing/RayTracingCommonResource.hlsl"
#include "../Material/NormalBuffer.hlsl"
#include "../Material/MaterialCommon.hlsl"
#include "ShaderLibrary/GlobalModelVariables.hlsl"
#include "Material/Lit/LitUEVariables.hlsl"
#include "../Material/Lit/GbufferEncoderDecoder.hlsl"
#include "../Material/BSDF.hlsl"
#include "../PathTracing/PathTracingCore.hlsl"
#include "SharcIntegration.hlsl"

// SHaRC specific constants
cbuffer SharcUpdateConstants : register(space0)
{
    uint2 _ScreenSize;
    uint _FrameIndex;
    uint _MaxPathDepth;
    uint _SparseTracingBlockSize;
    float _SharcSceneScale;
    float _SharcLogarithmBase;
    int _SharcLevelBias;
    bool _SharcEnableAntiFirefly;
}

// SHaRC buffers (managed by C++)
RWStructuredBuffer<uint64_t> _SharcHashEntries;
RWStructuredBuffer<uint4> _SharcVoxelData;
RWStructuredBuffer<uint4> _SharcVoxelDataPrev;
RWStructuredBuffer<uint> _SharcCopyOffset;



// Helper function to compute direct lighting (simplified)
float3 ComputeDirectLighting(float3 hitPos, float3 normal, float3 baseColor, float roughness, float metallic, inout uint seed)
{
    // TODO: Implement proper direct lighting calculation
    // For now, return simple ambient + basic directional light
    float3 lightDir = normalize(float3(1, 1, 1));
    float NdotL = saturate(dot(normal, lightDir));
    float3 diffuse = baseColor * (1.0f - metallic) / 3.14159f;
    
    return diffuse * NdotL * 2.0f + baseColor * 0.1f; // Simple ambient
}

// Helper function to sample BRDF
bool SampleBRDF(float3 normal, float roughness, float3 baseColor, float metallic, float2 xi, out float3 rayDirection, out float3 throughput)
{
    // Build orthonormal basis
    float3 tangent, bitangent;
    if (abs(normal.z) < 0.999f)
    {
        tangent = normalize(cross(normal, float3(0, 0, 1)));
    }
    else
    {
        tangent = normalize(cross(normal, float3(1, 0, 0)));
    }
    bitangent = cross(normal, tangent);
    
    if (roughness > 0.7f) // Diffuse sampling for rough surfaces
    {
        // Cosine hemisphere sampling
        float cosTheta = sqrt(xi.x);
        float sinTheta = sqrt(1.0f - xi.x);
        float phi = 2.0f * 3.14159f * xi.y;
        
        float3 localDirection = float3(sinTheta * cos(phi), sinTheta * sin(phi), cosTheta);
        rayDirection = localDirection.x * tangent + localDirection.y * bitangent + localDirection.z * normal;
        
        float3 diffuseColor = baseColor * (1.0f - metallic);
        throughput = diffuseColor / 3.14159f;
        
        return dot(rayDirection, normal) > 0.01f;
    }
    else // Specular sampling for smooth surfaces
    {
        // GGX importance sampling (simplified)
        float alpha = roughness * roughness;
        float cosTheta = sqrt((1.0f - xi.x) / (1.0f + (alpha * alpha - 1.0f) * xi.x));
        float sinTheta = sqrt(1.0f - cosTheta * cosTheta);
        float phi = 2.0f * 3.14159f * xi.y;
        
        float3 halfVector = float3(sinTheta * cos(phi), sinTheta * sin(phi), cosTheta);
        halfVector = halfVector.x * tangent + halfVector.y * bitangent + halfVector.z * normal;
        
        // Reflect view direction (approximated as -normal for simplicity)
        rayDirection = reflect(-normal, halfVector);
        
        float3 F0 = lerp(0.04f.xxx, baseColor, metallic);
        throughput = F0; // Simplified
        
        return dot(rayDirection, normal) > 0.01f;
    }
}

// Sample environment radiance (simplified)
float3 SampleEnvironmentRadiance(float3 direction)
{
    // TODO: Sample from actual environment map
    // For now, return simple sky gradient
    float t = direction.y * 0.5f + 0.5f;
    return lerp(float3(0.1f, 0.15f, 0.3f), float3(0.5f, 0.7f, 1.0f), t);
}


[shader("raygeneration")]
void SharcPathTracingUpdate()
{
    using namespace CE_PathTracing;

    uint2 index = DispatchRaysIndex().xy;
    uint2 dim = DispatchRaysDimensions().xy;

    // Sparse sampling pattern - only update selected pixels
    uint2 blockCoord = index / _SparseTracingBlockSize;
    uint2 localCoord = index % _SparseTracingBlockSize;
    uint frameOffset = _FrameIndex % (_SparseTracingBlockSize * _SparseTracingBlockSize);
    uint localIndex = localCoord.y * _SparseTracingBlockSize + localCoord.x;
    
    // Only process one pixel per block per frame
    if (localIndex != frameOffset)
        return;

    // Initialize SHaRC for update pass
    SharcIntegrationState sharcState = SharcInitializePath();
    if (!sharcState.enableSharc || !sharcState.isUpdatePass)
        return;

    uint seed = tea(index.x * dim.y + index.y, _FrameIndex);
    float2 jitter = rnd2(seed) / 2.0f;
    float2 screenUV = (index + 0.5f + jitter) / dim;
    
    float2 screenCoord = float2(screenUV.x * 2.0f - 1.0f, (1 - screenUV.y) * 2.0f - 1.0f);
    float3 cameraRayDir = ScreenPositionToWorldDirection(float3(screenCoord, 1.0f), ce_InvView, ce_InvProjection);
    
    // Setup initial ray
    RayDesc ray;
    ray.Origin = ce_CameraPos;
    ray.Direction = cameraRayDir;
    ray.TMin = 0.001f; // Min ray bias
    ray.TMax = 1000.0f; // Max trace distance

    RayTracingPayload payload = CreateRayTracingPayload();
    payload.Seed = seed;
    
    float3 pathThroughput = 1.0f.xxx;
    float3 accumulatedRadiance = 0.0f.xxx;
    
    // Path tracing loop for SHaRC update
    [[loop]]
    for (uint bounce = 0; bounce < _MaxPathDepth; bounce++)
    {
        bool isFirstBounce = (bounce == 0);
        const uint RayFlag = isFirstBounce ? RAY_FLAG_CULL_BACK_FACING_TRIANGLES : RAY_FLAG_NONE;
        
        // Trace ray using hardware ray tracing
        TraceRay(ce_AccelerationStructure, RayFlag, INSTANCE_MASK_ALL, 0, 1, 0, ray, payload);
        
        if (!payload.IsHit())
        {
            // Ray missed - add environment contribution and terminate
            float3 envRadiance = SampleEnvironmentRadiance(ray.Direction);
            accumulatedRadiance += pathThroughput * envRadiance;
            break;
        }
        
        // Get hit position and surface data
        float3 hitPosition = payload.GetHitPos(ray);
        SurfaceData surfaceData = payload.SurfaceData;
        
        // Decode material properties from surface data
        float3 baseColor = surfaceData.DecodeBaseColor();
        float3 normalWS = surfaceData.GetNormalWS();
        float roughness = surfaceData.roughness;
        float metallic = surfaceData.metallic;
        
        // Calculate direct lighting at hit point
        float3 directLighting = ComputeDirectLighting(hitPosition, normalWS, baseColor, roughness, metallic, payload.Seed);
        
        // Add direct lighting contribution
        accumulatedRadiance += pathThroughput * directLighting;
        
        // SHaRC update: store radiance at this hit point
        if (bounce > 0) // Don't cache primary surface
        {
            float3 totalRadiance = directLighting;
            
            // Add indirect contribution if this isn't the first bounce
            if (bounce > 1)
            {
                totalRadiance += accumulatedRadiance;
            }
            
            // Update SHaRC cache with this hit point's radiance
            float randomValue = rnd(payload.Seed);
            float3 cachedRadiance; // Not used in update pass
            SharcHandleRayHit(sharcState, hitPosition, normalWS, totalRadiance, randomValue, cachedRadiance);
        }
        
        // Sample BRDF for next bounce
        float2 xi = rnd2(payload.Seed);
        float3 rayDirection;
        float3 brdfThroughput;
        
        if (!SampleBRDF(normalWS, roughness, baseColor, metallic, xi, rayDirection, brdfThroughput))
            break; // Invalid sample
        
        // Update path state
        pathThroughput *= brdfThroughput;
        
        // Setup next ray
        ray.Origin = hitPosition + normalWS * 0.001f; // Offset to avoid self-intersection
        ray.Direction = rayDirection;
        ray.TMin = 0.001f;
        ray.TMax = 1000.0f;
        
        // Russian roulette termination
        float maxThroughput = max3(pathThroughput.x, pathThroughput.y, pathThroughput.z);
        if (maxThroughput < 0.1f && bounce > 2)
        {
            float rrProb = rnd(payload.Seed);
            if (rrProb > maxThroughput)
                break;
            pathThroughput /= maxThroughput;
        }
    }
}


