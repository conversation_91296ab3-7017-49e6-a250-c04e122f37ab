// SHaRC Compaction Pass Compute Shader
// Performs hash map compaction to reduce memory fragmentation
#pragma compute SharcCompaction

#define SHARC_ENABLE_64_BIT_ATOMICS 1
#define SHARC_UPDATE 0
#define SHARC_QUERY 0

#include "../../ShaderLibrary/Common.hlsl"
#include "include/SharcCommon.h"

// Input/Output Resources
RWStructuredBuffer<uint64_t> _SharcHashEntries;
RWStructuredBuffer<uint> _SharcCopyOffset;

// Constants
cbuffer SharcCompactionConstants
{
    uint _SharcCacheElementCount;
}

[numthreads(64, 1, 1)]
void SharcCompaction(uint3 id : SV_DispatchThreadID)
{
    uint entryIndex = id.x;
    if (entryIndex >= _SharcCacheElementCount)
        return;
    
    // Setup hash map data
    HashMapData hashMapData;
    hashMapData.capacity = _SharcCacheElementCount;
    hashMapData.hashEntriesBuffer = _SharcHashEntries;
    
    // Execute compaction for this entry
    SharcCopyHashEntry(entryIndex, hashMapData, _SharcCopyOffset);
}
