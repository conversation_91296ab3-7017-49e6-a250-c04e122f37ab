// SHaRC Debug Visualization Compute Shader
// Provides various debug visualizations for SHaRC cache
#pragma compute SharcDebugVisualization

#define SHARC_QUERY 0
#define SHARC_UPDATE 0


#include "../../ShaderLibrary/Common.hlsl"
#include "SharcIntegration.hlsl"
#include "../ScreenTextures.hlsl"




// Output
RWTexture2D<float4> _DebugOutput;

// Constants
cbuffer SharcDebugConstants
{
    float4x4 _InvViewProjMatrix;
    uint2 _ScreenSize;
    uint _DebugMode; // 0=Hash Grid, 1=Occupancy, 2=Cache Hit, 3=Voxel Level
}

// Debug modes
#define DEBUG_MODE_HASH_GRID    0
#define DEBUG_MODE_OCCUPANCY    1  
#define DEBUG_MODE_CACHE_HIT    2
#define DEBUG_MODE_VOXEL_LEVEL  3

// Reconstruct world position from screen pixel and depth
float3 ReconstructWorldPosition(uint2 pixelCoord, float depth)
{
    float2 screenUV = (pixelCoord + 0.5f) / float2(_ScreenSize);
    float4 clipPos = float4(screenUV * 2.0f - 1.0f, depth, 1.0f);
    clipPos.y = -clipPos.y;
    float4 worldPos = mul(_InvViewProjMatrix, clipPos);
    return worldPos.xyz / worldPos.w;
}

// Hash grid colored visualization
float3 DebugHashGridColored(float3 worldPosition)
{
    SharcIntegrationState sharcState = SharcInitializePath();
    if (!sharcState.enableSharc)
        return float3(0, 0, 0);
    
    return SharcDebugHashGrid(worldPosition, sharcState);
}

// Cache occupancy visualization
float3 DebugOccupancy(uint2 pixelCoord)
{
    SharcIntegrationState sharcState = SharcInitializePath();
    if (!sharcState.enableSharc)
        return float3(0, 0, 0);
    
    return SharcDebugOccupancy(pixelCoord, _ScreenSize, sharcState);
}

// Cache hit rate visualization
float3 DebugCacheHit(float3 worldPosition, float3 worldNormal)
{
    SharcIntegrationState sharcState = SharcInitializePath();
    if (!sharcState.enableSharc)
        return float3(0, 0, 0);
    
    // Setup hit data
    SharcHitData hitData;
    hitData.positionWorld = worldPosition;
    hitData.normalWorld = worldNormal;
    
    // Try to find cached data
    float3 cachedRadiance;
    bool hasCache = SharcGetCachedRadiance(sharcState.sharcParameters, hitData, cachedRadiance, true);
    
    if (hasCache)
    {
        // Get cache info for visualization
        HashGridIndex cacheIndex = HashMapFindEntry(
            sharcState.sharcParameters.hashMapData, 
            worldPosition, 
            worldNormal, 
            sharcState.sharcParameters.gridParameters);
        
        if (cacheIndex != HASH_GRID_INVALID_CACHE_INDEX)
        {
            SharcVoxelData voxelData = SharcGetVoxelData(sharcState.sharcParameters.voxelDataBuffer, cacheIndex);
            
            // Color by sample count
            float sampleRatio = float(voxelData.accumulatedSampleNum) / 1000.0f; // Normalize to [0,1]
            sampleRatio = saturate(sampleRatio);
            
            // Green = high sample count (good cache), Red = low sample count  
            return float3(1.0f - sampleRatio, sampleRatio, 0.0f);
        }
    }
    
    return float3(0.1f, 0.1f, 0.1f); // Dark gray for no cache
}

// Voxel level visualization
float3 DebugVoxelLevel(float3 worldPosition)
{
    SharcIntegrationState sharcState = SharcInitializePath();
    if (!sharcState.enableSharc)
        return float3(0, 0, 0);
    
    // Get grid level
    uint gridLevel = HashGridGetLevel(worldPosition, sharcState.sharcParameters.gridParameters);
    float voxelSize = HashGridGetVoxelSize(gridLevel, sharcState.sharcParameters.gridParameters);
    
    // Color by level (0=fine/red, higher=coarse/blue)
    float levelNorm = saturate(float(gridLevel) / 16.0f);
    return float3(1.0f - levelNorm, 0.5f * levelNorm, levelNorm);
}

[numthreads(8, 8, 1)]
void SharcDebugVisualization(uint3 id : SV_DispatchThreadID)
{
    uint2 pixelCoord = id.xy;
    if (any(pixelCoord >= _ScreenSize))
        return;
    
    // Read depth and skip background
    float depth = _DepthMap.Load(uint3(pixelCoord, 0));
    if (depth >= 1.0f)
    {
        _DebugOutput[pixelCoord] = float4(0, 0, 0, 1);
        return;
    }
    
    // Reconstruct world position
    float3 worldPosition = ReconstructWorldPosition(pixelCoord, depth);
    
    // Get world normal from G-Buffer
    float4 gbuffer1 = _GBuffer1.Load(uint3(pixelCoord, 0));
    float3 worldNormal = normalize(gbuffer1.xyz * 2.0f - 1.0f);
    
    float3 debugColor = float3(0, 0, 0);
    
    // Select debug visualization mode
    switch (_DebugMode)
    {
        case DEBUG_MODE_HASH_GRID:
            debugColor = DebugHashGridColored(worldPosition);
            break;
            
        case DEBUG_MODE_OCCUPANCY:
            debugColor = DebugOccupancy(pixelCoord);
            break;
            
        case DEBUG_MODE_CACHE_HIT:
            debugColor = DebugCacheHit(worldPosition, worldNormal);
            break;
            
        case DEBUG_MODE_VOXEL_LEVEL:
            debugColor = DebugVoxelLevel(worldPosition);
            break;
            
        default:
            debugColor = float3(1, 0, 1); // Magenta for invalid mode
            break;
    }
    
    _DebugOutput[pixelCoord] = float4(debugColor, 1.0f);
}
