// SHaRC Integration Helper Functions
// Provides easy-to-use wrapper functions for integrating SHaRC into existing path tracers

#ifndef SHARC_INTEGRATION_HLSL
#define SHARC_INTEGRATION_HLSL

#include "include/SharcCommon.h"

// SHaRC Integration State
struct SharcIntegrationState
{
    SharcState sharcState;
    SharcParameters sharcParameters;
    bool enableSharc;
    bool isUpdatePass;
    bool isQueryPass;
};

// Global SHaRC resources (to be bound by C++ code)
RWStructuredBuffer<uint64_t> g_SharcHashEntries;
RWStructuredBuffer<uint4> g_SharcVoxelData; 
RWStructuredBuffer<uint4> g_SharcVoxelDataPrev;

// SHaRC parameters (to be set by C++ code)
cbuffer SharcIntegrationConstants : register(b10)
{
    float3 g_SharcCameraPosition;
    float g_SharcLogarithmBase;
    float g_SharcSceneScale;
    int g_SharcLevelBias;
    uint g_SharcEnableSharc;
    uint g_SharcIsUpdatePass;  // SHARC_UPDATE define equivalent
    uint g_SharcIsQueryPass;   // SHARC_QUERY define equivalent
    uint g_SharcEnableAntiFirefly;
    uint g_SharcCacheElementCount;
}

// Initialize SHaRC for a path tracing sample
SharcIntegrationState SharcInitializePath()
{
    SharcIntegrationState state;
    state.enableSharc = g_SharcEnableSharc != 0;
    state.isUpdatePass = g_SharcIsUpdatePass != 0;
    state.isQueryPass = g_SharcIsQueryPass != 0;
    
    // Always initialize SHaRC parameters to avoid conditional pointer assignment
    // Setup SHaRC parameters
    state.sharcParameters.gridParameters.cameraPosition = g_SharcCameraPosition;
    state.sharcParameters.gridParameters.logarithmBase = g_SharcLogarithmBase;
    state.sharcParameters.gridParameters.sceneScale = g_SharcSceneScale;
    state.sharcParameters.gridParameters.levelBias = g_SharcLevelBias;
    state.sharcParameters.enableAntiFireflyFilter = g_SharcEnableAntiFirefly != 0;
    
    state.sharcParameters.hashMapData.capacity = g_SharcCacheElementCount;
    state.sharcParameters.hashMapData.hashEntriesBuffer = g_SharcHashEntries;
    state.sharcParameters.voxelDataBuffer = g_SharcVoxelData;
    state.sharcParameters.voxelDataBufferPrev = g_SharcVoxelDataPrev;
    
    // Initialize SHaRC state
    SharcInit(state.sharcState);
    
    return state;
}

// Handle ray miss event (for update pass)
void SharcHandleRayMiss(inout SharcIntegrationState state, float3 missRadiance)
{
    if (state.enableSharc && state.isUpdatePass)
    {
        SharcUpdateMiss(state.sharcParameters, state.sharcState, missRadiance);
    }
}

// Handle ray hit event - returns true if should continue tracing, false if using cached radiance
bool SharcHandleRayHit(inout SharcIntegrationState state, float3 worldPosition, float3 worldNormal, float3 directLighting, float random, out float3 cachedRadiance)
{
    cachedRadiance = float3(0, 0, 0);
    
    if (!state.enableSharc)
        return true;
    
    // Setup hit data
    SharcHitData hitData;
    hitData.positionWorld = worldPosition;
    hitData.normalWorld = worldNormal;
    
    if (state.isUpdatePass)
    {
        // Update pass - add data to cache and determine if should continue tracing
        return SharcUpdateHit(state.sharcParameters, state.sharcState, hitData, directLighting, random);
    }
    else if (state.isQueryPass)
    {
        // Query pass - try to get cached radiance
        if (SharcGetCachedRadiance(state.sharcParameters, hitData, cachedRadiance, false))
        {
            return false; // Use cached radiance, terminate path
        }
    }
    
    return true; // Continue tracing
}

// Set path throughput for current bounce (for update pass)
void SharcSetPathThroughput(inout SharcIntegrationState state, float3 throughput)
{
    if (state.enableSharc && state.isUpdatePass)
    {
        SharcSetThroughput(state.sharcState, throughput);
    }
}

// Check if path segment is long enough to use cache (avoids artifacts from small segments)
bool SharcCanUseCache(float3 rayOrigin, float3 rayDirection, float rayLength, float materialRoughness, SharcIntegrationState state)
{
    if (!state.enableSharc)
        return false;
    
    // Get voxel size at hit point
    float3 hitPosition = rayOrigin + rayDirection * rayLength;
    uint gridLevel = HashGridGetLevel(hitPosition, state.sharcParameters.gridParameters);
    float voxelSize = HashGridGetVoxelSize(gridLevel, state.sharcParameters.gridParameters);
    
    // Check if ray segment is long enough
    if (rayLength < voxelSize)
        return false;
    
    // For specular surfaces, check cone spread
    if (materialRoughness < 0.5f) // Glossy/specular
    {
        float roughnessSquared = materialRoughness * materialRoughness;
        float coneSpread = 2.0f * rayLength * sqrt(0.5f * roughnessSquared / (1.0f - roughnessSquared));
        
        // Only use cache if cone spread is large enough relative to voxel size
        return coneSpread > voxelSize * 0.5f;
    }
    
    return true; // OK to use for diffuse surfaces
}

// Debug visualization helpers
float3 SharcDebugHashGrid(float3 worldPosition, SharcIntegrationState state)
{
    if (state.enableSharc)
    {
        return HashGridDebugColoredHash(worldPosition, state.sharcParameters.gridParameters);
    }
    return float3(0, 0, 0);
}

float3 SharcDebugOccupancy(uint2 pixelPosition, uint2 screenSize, SharcIntegrationState state)
{
    if (state.enableSharc)
    {
        return HashGridDebugOccupancy(pixelPosition, screenSize, state.sharcParameters.hashMapData);
    }
    return float3(0, 0, 0);
}

#endif // SHARC_INTEGRATION_HLSL
