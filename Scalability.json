{"AntiAliasingQuality": [{"mSubSampleShadingSettings.enable": false}, {"mSubSampleShadingSettings.enable": true, "mSubSampleShadingSettings.MSAASampleCount": 4, "mSubSampleShadingSettings.EnableAGAA": true}, {"mSubSampleShadingSettings.enable": true, "mSubSampleShadingSettings.MSAASampleCount": 4, "mSubSampleShadingSettings.EnableAGAA": true}], "ShadowQuality": [{"mLocalLightShadowMapSettings.LocalLightCacheSize": 1024, "mLocalLightShadowMapSettings.MaxLightShadowMapSize": 128, "mContactShadowSettings.enable": false}, {"mLocalLightShadowMapSettings.LocalLightCacheSize": 2048, "mLocalLightShadowMapSettings.MaxLightShadowMapSize": 256, "mContactShadowSettings.enable": true}, {"mLocalLightShadowMapSettings.LocalLightCacheSize": 4096, "mLocalLightShadowMapSettings.MaxLightShadowMapSize": 512, "mContactShadowSettings.enable": true}], "GlobalIlluminationQuality": [{"mIndirectLightingCompositeSettings.mHybridGISetting.enable": false}, {"mIndirectLightingCompositeSettings.mHybridGISetting.enable": true}, {"mIndirectLightingCompositeSettings.mHybridGISetting.enable": true}], "PostProcessQuality": [{}, {}, {}], "ReflectionQuality": [], "TextureQuality": [{"VTPoolSizeMax": 24, "GlobalMipBias": 3}, {"VTPoolSizeMax": 48, "GlobalMipBias": 2}, {"VTPoolSizeMax": 48, "GlobalMipBias": 2}], "EffectsQuality": [{"mCloudSetting.SpatialDown": 1, "mCloudSetting.TemporalDown": 1}, {"mCloudSetting.SpatialDown": 1, "mCloudSetting.TemporalDown": 1}, {"mCloudSetting.SpatialDown": 0, "mCloudSetting.TemporalLevel": 1}], "FoliageQuality": [], "ShadingQuality": [{"GlobalLODBias": 2}, {"GlobalLODBias": 1}, {"GlobalLODBias": 0}]}