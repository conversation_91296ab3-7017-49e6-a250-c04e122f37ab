adnj
{
    "Guid": "9dc3ea42ed74047fb8f16c3e2c5fe44c",
    "Version": 5,
    "ClassID": 24,
    "DataSize": 2644,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "0131df16701304bac89a490ba151d0bf",
        "39edfad5f429246ad90ec9bf5428c051"
    ]
}
{
    "version": 1,
    "property": {
        "Default Group": {
            "color_texture": {
                "Value": "39edfad5f429246ad90ec9bf5428c051",
                "Visible": true
            },
            "TextureSize": {
                "Value": [
                    0.0,
                    0.0
                ],
                "Visible": true
            },
            "color_texture_1": {
                "Value": "39edfad5f429246ad90ec9bf5428c051",
                "Visible": true
            }
        }
    },
    "pass": [
        {
            "enable": true,
            "name": "UI",
            "shader": "0131df16701304bac89a490ba151d0bf",
            "render_group": 4000,
            "state": {
                "blend_targets": [
                    {
                        "blend_enable": true,
                        "blend_src": "SrcAlpha",
                        "blend_dst": "InvSrcAlpha",
                        "blend_op": "Add",
                        "blend_src_alpha": "InvDestAlpha",
                        "blend_dst_alpha": "One",
                        "blend_op_alpha": "Add",
                        "color_write_mask": [
                            1.0,
                            1.0,
                            1.0,
                            1.0
                        ]
                    }
                ],
                "depth_enable": true,
                "depth_write": false,
                "depth_cmp": "GreaterEqual",
                "stencil_enable": false,
                "stencil_read_mask": 255,
                "stencil_write_mask": 255,
                "front_face_stencil_op": {
                    "fail_op": "Keep",
                    "depth_fail_op": "Keep",
                    "pass_op": "Keep",
                    "compare_op": "Always"
                },
                "back_face_stencil_op": {
                    "fail_op": "Keep",
                    "depth_fail_op": "Keep",
                    "pass_op": "Keep",
                    "compare_op": "Always"
                },
                "fill_mode": "Solid",
                "cull": "Back",
                "depth_clip": true,
                "enable_depth_bias": false,
                "depth_bias": 0,
                "slope_scaled_depth_bias": 0.0,
                "depth_bias_clamp": 0.0,
                "rasterization_mode": "DefaultRaster",
                "raster_overestimation_size": 1,
                "stencil_reference": 0
            }
        }
    ],
    "defines": {
        "Domain": 0,
        "BlendMode": 0,
        "ShadingModel": 0,
        "TwoSided": false
    }
}