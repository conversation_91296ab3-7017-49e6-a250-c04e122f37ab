{"pages": ["DockingTextMat.nda"], "chars": [{"id": 46, "index": 0, "char": ".", "width": 5, "height": 5, "xoffset": 0, "yoffset": 11, "xadvance": 5, "chnl": 15, "x": 2, "y": 2, "page": 0}, {"id": 57344, "index": 1, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 2, "y": 2, "page": 0}, {"id": 57345, "index": 2, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 20, "y": 2, "page": 0}, {"id": 57346, "index": 3, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 38, "y": 2, "page": 0}, {"id": 45, "index": 4, "char": "-", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 56, "y": 2, "page": 0}, {"id": 57347, "index": 5, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 74, "y": 2, "page": 0}, {"id": 57348, "index": 6, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 92, "y": 2, "page": 0}, {"id": 57349, "index": 7, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 110, "y": 2, "page": 0}, {"id": 57350, "index": 8, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 128, "y": 2, "page": 0}, {"id": 57351, "index": 9, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 2, "y": 20, "page": 0}, {"id": 57352, "index": 10, "char": "", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 20, "y": 20, "page": 0}, {"id": 48, "index": 11, "char": "0", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 38, "y": 20, "page": 0}, {"id": 49, "index": 12, "char": "1", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 56, "y": 20, "page": 0}, {"id": 50, "index": 13, "char": "2", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 74, "y": 20, "page": 0}, {"id": 51, "index": 14, "char": "3", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 92, "y": 20, "page": 0}, {"id": 52, "index": 15, "char": "4", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 110, "y": 20, "page": 0}, {"id": 53, "index": 16, "char": "5", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 128, "y": 20, "page": 0}, {"id": 54, "index": 17, "char": "6", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 2, "y": 38, "page": 0}, {"id": 55, "index": 18, "char": "7", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 20, "y": 38, "page": 0}, {"id": 56, "index": 19, "char": "8", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 38, "y": 38, "page": 0}, {"id": 57, "index": 20, "char": "9", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 56, "y": 38, "page": 0}, {"id": 65, "index": 21, "char": "A", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 74, "y": 38, "page": 0}, {"id": 66, "index": 22, "char": "B", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 92, "y": 38, "page": 0}, {"id": 67, "index": 23, "char": "C", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 110, "y": 38, "page": 0}, {"id": 68, "index": 24, "char": "D", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 128, "y": 38, "page": 0}, {"id": 69, "index": 25, "char": "E", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 2, "y": 56, "page": 0}, {"id": 70, "index": 26, "char": "F", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 20, "y": 56, "page": 0}, {"id": 71, "index": 27, "char": "G", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 38, "y": 56, "page": 0}, {"id": 72, "index": 28, "char": "H", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 56, "y": 56, "page": 0}, {"id": 73, "index": 29, "char": "I", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 74, "y": 56, "page": 0}, {"id": 74, "index": 30, "char": "J", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 92, "y": 56, "page": 0}, {"id": 75, "index": 31, "char": "K", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 110, "y": 56, "page": 0}, {"id": 76, "index": 32, "char": "L", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 128, "y": 56, "page": 0}, {"id": 77, "index": 33, "char": "M", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 2, "y": 74, "page": 0}, {"id": 78, "index": 34, "char": "N", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 20, "y": 74, "page": 0}, {"id": 79, "index": 35, "char": "O", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 38, "y": 74, "page": 0}, {"id": 80, "index": 36, "char": "P", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 56, "y": 74, "page": 0}, {"id": 81, "index": 37, "char": "Q", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 74, "y": 74, "page": 0}, {"id": 82, "index": 38, "char": "R", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 92, "y": 74, "page": 0}, {"id": 83, "index": 39, "char": "S", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 110, "y": 74, "page": 0}, {"id": 84, "index": 40, "char": "T", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 128, "y": 74, "page": 0}, {"id": 85, "index": 41, "char": "U", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 2, "y": 92, "page": 0}, {"id": 86, "index": 42, "char": "V", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 20, "y": 92, "page": 0}, {"id": 87, "index": 43, "char": "W", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 38, "y": 92, "page": 0}, {"id": 88, "index": 44, "char": "X", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 56, "y": 92, "page": 0}, {"id": 89, "index": 45, "char": "Y", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 74, "y": 92, "page": 0}, {"id": 90, "index": 46, "char": "Z", "width": 16, "height": 16, "xoffset": 0, "yoffset": 0, "xadvance": 16, "chnl": 15, "x": 92, "y": 92, "page": 0}, {"id": 32, "index": 47, "char": " ", "width": 0, "height": 0, "xoffset": 0, "yoffset": 0, "xadvance": 4, "chnl": 15, "x": 128, "y": 128, "page": 0}], "info": {"face": "DockingMSDF", "size": 42, "bold": 0, "italic": 0, "charset": [".", "", "", "", "-", "", "", "", "", "", "", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", " "], "unicode": 1, "stretchH": 100, "smooth": 1, "padding": [2, 2, 2, 2], "spacing": [0, 0]}, "common": {"lineHeight": 16, "base": 16, "scaleW": 146, "scaleH": 146, "pages": 1, "packed": 0, "alphaChnl": 0, "redChnl": 0, "greenChnl": 0, "blueChnl": 0}, "distanceField": {"fieldType": "msdf", "distanceRange": 4}, "kernings": [{"first": 57348, "second": 57350, "amount": -4}, {"first": 57351, "second": 57348, "amount": -4}]}