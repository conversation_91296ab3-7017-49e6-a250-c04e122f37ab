#pragma vertex VSMain
#pragma pixel PSMain

cbuffer cbPassMeta : register(space0)
{
    float3 ce_CameraTilePosition;
	matrix ce_View;
	matrix ce_Projection;
}
cbuffer cbMtl : register(space1)
{
    float2 TextureSize;
}

cbuffer cobj : register(space2)
{    
    float4 Color;
}


struct VS2PS
{
    float4 Pos :        SV_POSITION;
    float2 UV :         TEXCOORD0;
};
#define LENGTH_PER_TILE 65536.0

float3 GetLargeCoordinateReltvPosition(float3 posOffsetInModelTile, float3 modelTileInLargeWorld, float3 orgTileInLargeWorld)
{
    return posOffsetInModelTile + (modelTileInLargeWorld - orgTileInLargeWorld) * LENGTH_PER_TILE;
}
VS2PS VSMain(float3 Pos : POSITION, float3 TilePosition : NORMAL, float2 uv:TEXCOORD0)
{
    VS2PS output;
    Pos = GetLargeCoordinateReltvPosition(Pos, TilePosition, ce_CameraTilePosition);
    output.Pos = mul(ce_View, float4(Pos, 1.0));
	output.Pos = mul(ce_Projection, output.Pos);
    output.UV = uv;
    return output;
}

float median(float r, float g, float b) {
  return max(min(r, g), min(max(r, g), b));
}

SamplerState texture_sampler : register(space1);
Texture2D<float4> color_texture : register(space1);
Texture2D<float4> color_texture_1 : register(space1);

float4 PSMain(VS2PS input) : SV_TARGET
{
    
    float2 map_uv = input.UV;
    float2 sz = TextureSize;
    float deltax = fwidth(map_uv.x);
        
    if(2.0 / sz.x < deltax){
        float opacity = color_texture_1.Sample(texture_sampler,map_uv).x;
        return float4(Color.xyz,opacity);
    }

    float pxrange = 2.0;
    float3 s = color_texture.SampleLevel(texture_sampler, map_uv,0).xyz;
    float screenPxRange = max (pxrange / sz.x / deltax,1.0);
    float sigDist = median(s.r, s.g, s.b) - 0.5;
    float opacity = clamp(sigDist * screenPxRange + 0.5, 0.0, 1.0);

    return float4(Color.xyz,opacity);
}