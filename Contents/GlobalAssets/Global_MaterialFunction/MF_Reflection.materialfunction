adnj
{
    "Guid": "d795f30f444984b67b4162c6b1b6915a",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 4722,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": -2010,
            "m_EditorPositionY": -317,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "Reflection Vector",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -3079,
            "m_EditorPositionY": -324,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -3491,
            "m_EditorPositionY": -152,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNormalize",
            "m_Input": {
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -3656,
            "m_EditorPositionY": -264,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCameraVectorWS"
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -3038,
            "m_EditorPositionY": -180,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 2.0
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -2839,
            "m_EditorPositionY": -316,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -3315,
            "m_EditorPositionY": -280,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDotProduct",
            "m_A": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -2593,
            "m_EditorPositionY": -297,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": -2386,
            "m_EditorPositionY": -314,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTransform",
            "m_Source": 2,
            "m_Destination": 1,
            "m_ElementType": 0,
            "m_Input": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": -3771,
            "m_EditorPositionY": -140,
            "m_Description": "",
            "Class": "cross::MaterialExpressionWorldGeometryNormal"
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}