adnj
{
    "Guid": "4db2886ad28c78abdd4a7777c8febb07",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 15319,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": -56,
            "m_EditorPositionY": -13,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "POM UVS",
            "m_SortPriority": 1.0,
            "m_Output": {
                "m_Name": "Output",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -296,
            "m_EditorPositionY": 4,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCustom",
            "m_Name": "EasyPOm",
            "m_Code": "// 输入参数:\r\n// UV (float2) - 原始UV坐标\r\n// ViewVector (float3) - 切线空间的视角向量\r\n// HeightTexture (Texture2D) - 高度贴图\r\n// HeightScale (float) - 高度缩放,默认0.05\r\n// MinLayers (float) - 最小层数,默认8\r\n// MaxLayers (float) - 最大层数,默认32\r\n\r\n// \r\n// SamplerState ce_Sampler_Repeat;\r\n\r\nstruct ParallaxData\r\n{\r\n    float2 FinalUV;\r\n    float FinalHeight;\r\n};\r\n\r\nstruct ParallaxOcclusionMapping\r\n{\r\n    static ParallaxData CalculateParallax(\r\n        float2 UV,\r\n        float3 ViewVector,\r\n        Texture2D HeightTexture,\r\n        float2 InDDX,\r\n        float2 InDDY,\r\n        float HeightScale,\r\n        float MinLayers,\r\n        float MaxLayers\r\n    )\r\n    {\r\n        ParallaxData Result;\r\n\r\n        float NumLayers = lerp(MaxLayers, MinLayers, abs(dot(float3(0, 0, 1), ViewVector)));\r\n\r\n        float LayerDepth = 1.0 / NumLayers;\r\n        float CurrentLayerDepth = 0.0;\r\n\r\n        float2 P = ViewVector.xy / ViewVector.z * HeightScale;\r\n        float2 DeltaTexCoords = P / NumLayers;\r\n\r\n        float2 CurrentTexCoords = UV;\r\n        float CurrentDepthMapValue = HeightTexture.SampleGrad(ce_Sampler_Repeat, CurrentTexCoords, InDDX, InDDY).r;\r\n\r\n        [loop]\r\n        for (int i = 0; i < int(MaxLayers) && CurrentLayerDepth < CurrentDepthMapValue; i++)\r\n        {\r\n            CurrentTexCoords -= DeltaTexCoords;\r\n            CurrentDepthMapValue = HeightTexture.SampleGrad(ce_Sampler_Repeat , CurrentTexCoords, InDDX, InDDY).r;\r\n            CurrentLayerDepth += LayerDepth;\r\n        }\r\n\r\n        float2 PrevTexCoords = CurrentTexCoords + DeltaTexCoords;\r\n        float AfterDepth = CurrentDepthMapValue - CurrentLayerDepth;\r\n        float BeforeDepth = HeightTexture.SampleGrad(ce_Sampler_Repeat, PrevTexCoords, InDDX, InDDY).r - CurrentLayerDepth + LayerDepth;\r\n\r\n        float Weight = AfterDepth / (AfterDepth - BeforeDepth);\r\n        float2 FinalTexCoords = PrevTexCoords * Weight + CurrentTexCoords * (1.0 - Weight);\r\n        float FinalDepth = CurrentLayerDepth + BeforeDepth * Weight + AfterDepth * (1.0 - Weight);\r\n\r\n        Result.FinalUV = FinalTexCoords;\r\n        Result.FinalHeight = FinalDepth;\r\n\r\n        return Result;\r\n    }\r\n};\r\n\r\n// 主函数调用（已移除采样器实参）\r\nParallaxData POMResult = ParallaxOcclusionMapping::CalculateParallax(\r\n    UV,\r\n    ViewVector,\r\n    HeightTexture,\r\n    InDDX,\r\n    InDDY,\r\n    HeightScale,\r\n    MinLayers,\r\n    MaxLayers\r\n);\r\n\r\nreturn float3(POMResult.FinalUV, POMResult.FinalHeight);",
            "m_CustomInputs": [
                {
                    "Name": "UV",
                    "Input": {
                        "m_Name": "UV",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 6,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "ViewVector",
                    "Input": {
                        "m_Name": "ViewVector",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 5,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "HeightTexture",
                    "Input": {
                        "m_Name": "HeightTexture",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 10,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "HeightScale",
                    "Input": {
                        "m_Name": "HeightScale",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 11,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "MinLayers",
                    "Input": {
                        "m_Name": "MinLayers",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 13,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "MaxLayers",
                    "Input": {
                        "m_Name": "MaxLayers",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 18,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "InDDX",
                    "Input": {
                        "m_Name": "InDDX",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 16,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "InDDY",
                    "Input": {
                        "m_Name": "InDDY",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 17,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ],
            "m_OutputType": 2,
            "m_AdditionalCustomOutputs": []
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -992,
            "m_EditorPositionY": 36,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "CameraVector",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -744,
            "m_EditorPositionY": 36,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTransform",
            "m_Source": 2,
            "m_Destination": 0,
            "m_ElementType": 0,
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -634,
            "m_EditorPositionY": -44,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "UV",
            "m_InputType": 1,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -1181,
            "m_EditorPositionY": 36,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCameraVectorWS"
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -901,
            "m_EditorPositionY": -44,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureCoordinate",
            "m_CoordinateIndex": 0,
            "m_UTiling": 1.0,
            "m_VTiling": 1.0
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": -945,
            "m_EditorPositionY": 100,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureObject",
            "m_TextureType": 16,
            "m_TextureObjectName": "TextureObject5018",
            "m_TextureString": "EngineResource/Texture/DefaultTexture.nda"
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": -728,
            "m_EditorPositionY": 100,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 3,
            "m_InputName": "HeightTexture",
            "m_InputType": 4,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": -680,
            "m_EditorPositionY": 228,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 4,
            "m_InputName": "HeightScale",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 12,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 12,
            "m_EditorPositionX": -885,
            "m_EditorPositionY": 244,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 0.07500000298023224
        },
        {
            "m_Id": 13,
            "m_EditorPositionX": -712,
            "m_EditorPositionY": 324,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 5,
            "m_InputName": "MinSteps",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 15,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 15,
            "m_EditorPositionX": -869,
            "m_EditorPositionY": 324,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 8.0
        },
        {
            "m_Id": 16,
            "m_EditorPositionX": -545,
            "m_EditorPositionY": -60,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDDX",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 17,
            "m_EditorPositionX": -472,
            "m_EditorPositionY": 324,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDDY",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 18,
            "m_EditorPositionX": -655,
            "m_EditorPositionY": 443,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 6,
            "m_InputName": "MaxSteps",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 19,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 19,
            "m_EditorPositionX": -795,
            "m_EditorPositionY": 469,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 32.0
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}