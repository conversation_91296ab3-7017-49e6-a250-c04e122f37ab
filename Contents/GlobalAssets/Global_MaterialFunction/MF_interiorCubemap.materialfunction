adnj
{
    "Guid": "5852479d0f20659ba146f7d0db2fecd8",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 35170,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 488,
            "m_EditorPositionY": 4,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 24,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -2701,
            "m_EditorPositionY": -141,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCameraVectorWS"
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -2472,
            "m_EditorPositionY": -124,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTransform",
            "m_Source": 2,
            "m_Destination": 0,
            "m_ElementType": 0,
            "m_Input": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -1903,
            "m_EditorPositionY": -124,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 61,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -1704,
            "m_EditorPositionY": -268,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 1.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -1416,
            "m_EditorPositionY": -381,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAbs",
            "m_Input": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -1432,
            "m_EditorPositionY": -244,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 18,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -1144,
            "m_EditorPositionY": -356,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": -2629,
            "m_EditorPositionY": 132,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureCoordinate",
            "m_CoordinateIndex": 0,
            "m_UTiling": 1.0,
            "m_VTiling": 1.0
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": -2410,
            "m_EditorPositionY": 173,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "UVS",
            "m_InputType": 1,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": -2408,
            "m_EditorPositionY": 260,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Tilling",
            "m_InputType": 1,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "LinkedExpressionId": 60,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 4.0,
                "y": 4.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 12,
            "m_EditorPositionX": -2149,
            "m_EditorPositionY": 199,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 11,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 13,
            "m_EditorPositionX": -1832,
            "m_EditorPositionY": 100,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFrac",
            "m_Input": {
                "LinkedExpressionId": 12,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 14,
            "m_EditorPositionX": -1489,
            "m_EditorPositionY": 96,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 13,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 15,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 15,
            "m_EditorPositionX": -1701,
            "m_EditorPositionY": 189,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant2Vector",
            "m_X": 2.0,
            "m_Y": -2.0
        },
        {
            "m_Id": 16,
            "m_EditorPositionX": -1240,
            "m_EditorPositionY": 68,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": 14,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 17,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 17,
            "m_EditorPositionX": -1340,
            "m_EditorPositionY": 233,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant2Vector",
            "m_X": 1.0,
            "m_Y": -1.0
        },
        {
            "m_Id": 18,
            "m_EditorPositionX": -936,
            "m_EditorPositionY": 68,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAppendVector",
            "m_A": {
                "LinkedExpressionId": 16,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 19,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 19,
            "m_EditorPositionX": -1056,
            "m_EditorPositionY": 191,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": -1.0
        },
        {
            "m_Id": 20,
            "m_EditorPositionX": -740,
            "m_EditorPositionY": -356,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "4598bb98ababffa8a2480f332eb6a565",
            "m_FunctionInputs": []
        },
        {
            "m_Id": 21,
            "m_EditorPositionX": -431,
            "m_EditorPositionY": -396,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMin",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 22,
            "m_EditorPositionX": -248,
            "m_EditorPositionY": -332,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMin",
            "m_A": {
                "LinkedExpressionId": 21,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 23,
            "m_EditorPositionX": -30,
            "m_EditorPositionY": -221,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 22,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 24,
            "m_EditorPositionX": 210,
            "m_EditorPositionY": -260,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 23,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 18,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 25,
            "m_EditorPositionX": -1792,
            "m_EditorPositionY": 1130,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCeil",
            "m_Input": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 26,
            "m_EditorPositionX": -1559,
            "m_EditorPositionY": 1153,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "9ae013a010104491064f89a4211ec8b0",
            "m_FunctionInputs": []
        },
        {
            "m_Id": 27,
            "m_EditorPositionX": -1194,
            "m_EditorPositionY": 1137,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFmod",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 30,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 28,
            "m_EditorPositionX": -1178,
            "m_EditorPositionY": 1282,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFmod",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 30,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 29,
            "m_EditorPositionX": -1125,
            "m_EditorPositionY": 1399,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFmod",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 31,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 30,
            "m_EditorPositionX": -1399,
            "m_EditorPositionY": 1289,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 2.0
        },
        {
            "m_Id": 31,
            "m_EditorPositionX": -1383,
            "m_EditorPositionY": 1465,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 3.0
        },
        {
            "m_Id": 32,
            "m_EditorPositionX": -1162,
            "m_EditorPositionY": 1529,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFmod",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 31,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 33,
            "m_EditorPositionX": 646,
            "m_EditorPositionY": 1241,
            "m_Description": "",
            "Class": "cross::MaterialExpressionLerp",
            "m_A": {
                "LinkedExpressionId": 34,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 39,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Alpha": {
                "LinkedExpressionId": 27,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0,
            "m_ConstAlpha": 0.0
        },
        {
            "m_Id": 34,
            "m_EditorPositionX": -183,
            "m_EditorPositionY": 1089,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "66be45a1ebadc4a2ef4ee155dfeb26c8",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 35,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 36,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 37,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 35,
            "m_EditorPositionX": -389,
            "m_EditorPositionY": 1092,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": -1.0
        },
        {
            "m_Id": 36,
            "m_EditorPositionX": -380,
            "m_EditorPositionY": 1173,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 37,
            "m_EditorPositionX": -376,
            "m_EditorPositionY": 1244,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 38,
            "m_EditorPositionX": -206,
            "m_EditorPositionY": 1434,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": -1.0
        },
        {
            "m_Id": 39,
            "m_EditorPositionX": -9,
            "m_EditorPositionY": 1350,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "66be45a1ebadc4a2ef4ee155dfeb26c8",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 41,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 38,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 40,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 40,
            "m_EditorPositionX": -202,
            "m_EditorPositionY": 1505,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 41,
            "m_EditorPositionX": -215,
            "m_EditorPositionY": 1353,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 42,
            "m_EditorPositionX": -207,
            "m_EditorPositionY": 1733,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 43,
            "m_EditorPositionX": -10,
            "m_EditorPositionY": 1649,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "66be45a1ebadc4a2ef4ee155dfeb26c8",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 45,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 42,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 44,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 44,
            "m_EditorPositionX": -203,
            "m_EditorPositionY": 1804,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 45,
            "m_EditorPositionX": -216,
            "m_EditorPositionY": 1652,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 46,
            "m_EditorPositionX": 566,
            "m_EditorPositionY": 1665,
            "m_Description": "",
            "Class": "cross::MaterialExpressionLerp",
            "m_A": {
                "LinkedExpressionId": 43,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 50,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Alpha": {
                "LinkedExpressionId": 28,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0,
            "m_ConstAlpha": 0.0
        },
        {
            "m_Id": 47,
            "m_EditorPositionX": 17,
            "m_EditorPositionY": 1973,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 48,
            "m_EditorPositionX": 21,
            "m_EditorPositionY": 2044,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 49,
            "m_EditorPositionX": 8,
            "m_EditorPositionY": 1892,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": -1.0
        },
        {
            "m_Id": 50,
            "m_EditorPositionX": 214,
            "m_EditorPositionY": 1889,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "66be45a1ebadc4a2ef4ee155dfeb26c8",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 49,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 47,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 48,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 51,
            "m_EditorPositionX": 861,
            "m_EditorPositionY": 1418,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 33,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 46,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 52,
            "m_EditorPositionX": 472,
            "m_EditorPositionY": -220,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "4598bb98ababffa8a2480f332eb6a565",
            "m_FunctionInputs": []
        },
        {
            "m_Id": 53,
            "m_EditorPositionX": 840,
            "m_EditorPositionY": -212,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "66be45a1ebadc4a2ef4ee155dfeb26c8",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": -1,
                        "LinkedExpressionOutputIndex": -1,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": -1,
                        "LinkedExpressionOutputIndex": -1,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": -1,
                        "LinkedExpressionOutputIndex": -1,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 54,
            "m_EditorPositionX": 1000,
            "m_EditorPositionY": 979,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 51,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 55,
            "m_EditorPositionX": 1272,
            "m_EditorPositionY": 1177,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "c249c55fff2c45ba9e47a8e8d7697306",
            "m_FunctionInputs": []
        },
        {
            "m_Id": 56,
            "m_EditorPositionX": 1160,
            "m_EditorPositionY": 1457,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "c249c55fff2c45ba9e47a8e8d7697306",
            "m_FunctionInputs": []
        },
        {
            "m_Id": 57,
            "m_EditorPositionX": 1518,
            "m_EditorPositionY": 1061,
            "m_Description": "",
            "Class": "cross::MaterialExpressionLerp",
            "m_A": {
                "LinkedExpressionId": 54,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Alpha": {
                "LinkedExpressionId": 28,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0,
            "m_ConstAlpha": 0.0
        },
        {
            "m_Id": 58,
            "m_EditorPositionX": 1526,
            "m_EditorPositionY": 1415,
            "m_Description": "",
            "Class": "cross::MaterialExpressionLerp",
            "m_A": {
                "LinkedExpressionId": 51,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Alpha": {
                "LinkedExpressionId": 32,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0,
            "m_ConstAlpha": 0.0
        },
        {
            "m_Id": 59,
            "m_EditorPositionX": 1646,
            "m_EditorPositionY": 1303,
            "m_Description": "",
            "Class": "cross::MaterialExpressionShaderConstBool",
            "m_ParameterName": "If_Use_Emissive_Mask",
            "m_Name": "If_Use_Emissive_Mask",
            "m_Value": false
        },
        {
            "m_Id": 60,
            "m_EditorPositionX": -2632,
            "m_EditorPositionY": 341,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant2Vector",
            "m_X": 4.0,
            "m_Y": 4.0
        },
        {
            "m_Id": 61,
            "m_EditorPositionX": -2452,
            "m_EditorPositionY": -333,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "66be45a1ebadc4a2ef4ee155dfeb26c8",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 62,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 63,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 64,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 62,
            "m_EditorPositionX": -2637,
            "m_EditorPositionY": -403,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": -1.0
        },
        {
            "m_Id": 63,
            "m_EditorPositionX": -2635,
            "m_EditorPositionY": -321,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": -1.0
        },
        {
            "m_Id": 64,
            "m_EditorPositionX": -2638,
            "m_EditorPositionY": -233,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 65,
            "m_EditorPositionX": 911,
            "m_EditorPositionY": -556,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureObject",
            "m_TextureType": 64,
            "m_TextureObjectName": "TextureObject28157",
            "m_TextureString": "Contents/Library/Texture/General_Interior_Emissive/RT_Office_1_NightLight.nda"
        },
        {
            "m_Id": 66,
            "m_EditorPositionX": 1283,
            "m_EditorPositionY": -479,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureSample",
            "m_DefaultTexture": "907053c689747388ba4fa144d12c646d",
            "m_SamplerState": {
                "MipValueMode": 0,
                "Filter": 2,
                "AnisotropicLevel": 8,
                "AddressMode": 0
            },
            "m_DefaultTextureType": 64,
            "m_UV": {
                "LinkedExpressionId": 24,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Tex": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Level": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Bias": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DDX": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DDY": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}