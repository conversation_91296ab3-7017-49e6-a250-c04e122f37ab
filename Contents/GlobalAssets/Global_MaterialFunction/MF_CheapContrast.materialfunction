adnj
{
    "Guid": "60996d34f1de76a2f14d34242d0b0244",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 6849,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": -2458,
            "m_EditorPositionY": -2173,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "Reflection Vector",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -3894,
            "m_EditorPositionY": -2196,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "1f0456fff1bc7e826b46af4ddb5db04a",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 11,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -3649,
            "m_EditorPositionY": -2172,
            "m_Description": "",
            "Class": "cross::MaterialExpressionOneMinus",
            "m_Input": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 1,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -3449,
            "m_EditorPositionY": -2139,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "In",
            "m_InputType": 0,
            "m_SortPriority": 0.0,
            "m_Preview": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -3154,
            "m_EditorPositionY": -2203,
            "m_Description": "",
            "Class": "cross::MaterialExpressionLerp",
            "m_A": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Alpha": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0,
            "m_ConstAlpha": 0.0
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -3688,
            "m_EditorPositionY": -2332,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "Contrast",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -3432,
            "m_EditorPositionY": -2380,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -3432,
            "m_EditorPositionY": -2268,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 1.0
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": -2903,
            "m_EditorPositionY": -2178,
            "m_Description": "",
            "Class": "cross::MaterialExpressionClamp",
            "m_Value": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Min": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Max": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstMin": 0.0,
            "m_ConstMax": 1.0
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": -2664,
            "m_EditorPositionY": -2156,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": false,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": -4117,
            "m_EditorPositionY": -2188,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureCoordinate",
            "m_CoordinateIndex": 0,
            "m_UTiling": 1.0,
            "m_VTiling": 1.0
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}