adnj
{
    "Guid": "7ccbb9681a6e64b2b64f5ef629dbe63f",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 6933,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": -2200,
            "m_EditorPositionY": -2109,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -3646,
            "m_EditorPositionY": -2108,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "BaseReflectFraction",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": 12,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -3139,
            "m_EditorPositionY": -1698,
            "m_Description": "",
            "Class": "cross::MaterialExpressionScalarParameter",
            "m_ParameterName": "Param1717",
            "m_IsVisibleInMaterialInstanceEditor": true,
            "m_Name": "Exponent",
            "m_Group": "Default",
            "m_SortPriority": 1.0,
            "m_DefaultValue": 1.0,
            "m_SliderMin": 0.0,
            "m_SliderMax": 0.0
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -2968,
            "m_EditorPositionY": -1860,
            "m_Description": "",
            "Class": "cross::MaterialExpressionPower",
            "m_Base": {
                "LinkedExpressionId": 11,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Exponent": {
                "LinkedExpressionId": 13,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstExponent": 1.0
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -2424,
            "m_EditorPositionY": -2103,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -3400,
            "m_EditorPositionY": -2012,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "ea47f9666440d68a0743974056b44e6f",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 2,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -2680,
            "m_EditorPositionY": -1996,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -3316,
            "m_EditorPositionY": -1856,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDotProduct",
            "m_A": {
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": -3606,
            "m_EditorPositionY": -1779,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCameraVectorWS"
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": -3659,
            "m_EditorPositionY": -1872,
            "m_Description": "",
            "Class": "cross::MaterialExpressionWorldGeometryNormal"
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": -3143,
            "m_EditorPositionY": -1841,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "ea47f9666440d68a0743974056b44e6f",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 8,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 12,
            "m_EditorPositionX": -3845,
            "m_EditorPositionY": -2108,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 2.0
        },
        {
            "m_Id": 13,
            "m_EditorPositionX": -3454,
            "m_EditorPositionY": -1660,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Exponent",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}