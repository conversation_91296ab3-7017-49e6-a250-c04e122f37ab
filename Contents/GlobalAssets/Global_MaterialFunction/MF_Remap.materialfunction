adnj
{
    "Guid": "5c986eba79733ca57c428beec248ff91",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 6089,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 1622,
            "m_EditorPositionY": 115,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": 1203,
            "m_EditorPositionY": 132,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCustom",
            "m_Name": "Remap",
            "m_Code": "\r\n return newMin + (saturate((value - orignalMin) / (orignalMax - orignalMin)) * (newMax - newMin));\r\n",
            "m_CustomInputs": [
                {
                    "Name": "value",
                    "Input": {
                        "LinkedExpressionId": 3,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "originalMin",
                    "Input": {
                        "LinkedExpressionId": 4,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "originalMax",
                    "Input": {
                        "LinkedExpressionId": 5,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "newMin",
                    "Input": {
                        "LinkedExpressionId": 6,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "newMax",
                    "Input": {
                        "LinkedExpressionId": 7,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ],
            "m_OutputType": 0,
            "m_AdditionalCustomOutputs": []
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": 870,
            "m_EditorPositionY": 100,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Value",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": 880,
            "m_EditorPositionY": 240,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "OriginalMin",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": 899,
            "m_EditorPositionY": 348,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "OriginalMax",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": 892,
            "m_EditorPositionY": 484,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "NewMin",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": 902,
            "m_EditorPositionY": 580,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "NewMax",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}