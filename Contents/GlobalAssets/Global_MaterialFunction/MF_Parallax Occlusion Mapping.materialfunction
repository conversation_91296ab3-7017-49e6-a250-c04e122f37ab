adnj
{
    "Guid": "448c74e957cb21a0a4487887d710fd46",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 119319,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": -876,
            "m_EditorPositionY": 3172,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "Pixel Depth Offset",
            "m_SortPriority": 3.0,
            "m_Output": {
                "m_Name": "Output",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 114,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -2939,
            "m_EditorPositionY": 2677,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "Shadow Penumbra",
            "m_InputType": 0,
            "m_SortPriority": 24.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -2930,
            "m_EditorPositionY": 2590,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 3,
            "m_InputName": "Shadow Steps",
            "m_InputType": 0,
            "m_SortPriority": 23.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -3000,
            "m_EditorPositionY": 244,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 4,
            "m_InputName": "LightVector",
            "m_InputType": 2,
            "m_SortPriority": 22.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 1.0,
                "y": 1.0,
                "z": 1.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -2606,
            "m_EditorPositionY": 252,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTransform",
            "m_Source": 2,
            "m_Destination": 0,
            "m_ElementType": 0,
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 179,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -2215,
            "m_EditorPositionY": 236,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -2209,
            "m_EditorPositionY": 367,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": false,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -1860,
            "m_EditorPositionY": 236,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAppendVector",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": -1982,
            "m_EditorPositionY": 372,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 13,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": -3008,
            "m_EditorPositionY": -268,
            "m_Description": "This is how 'deep' the heightmap is in relation to its width. Typical values are between 0.05 and 0.1.",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 5,
            "m_InputName": "Height Ratio",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.07500000298023224,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": -2799,
            "m_EditorPositionY": -264,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Height Ratio",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "46a9015149e405b7cb4c5f0141b70f75"
        },
        {
            "m_Id": 12,
            "m_EditorPositionX": -2909,
            "m_EditorPositionY": 2457,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "46a9015149e405b7cb4c5f0141b70f75"
        },
        {
            "m_Id": 13,
            "m_EditorPositionX": -2177,
            "m_EditorPositionY": 444,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "46a9015149e405b7cb4c5f0141b70f75"
        },
        {
            "m_Id": 14,
            "m_EditorPositionX": -3024,
            "m_EditorPositionY": 144,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "LightVector",
            "m_Color": {
                "x": 0.9978020787239076,
                "y": 0.7483838796615601,
                "z": 1.0,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 1705,
            "m_Height": 364
        },
        {
            "m_Id": 15,
            "m_EditorPositionX": -1695,
            "m_EditorPositionY": 240,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNormalize",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 16,
            "m_EditorPositionX": -1503,
            "m_EditorPositionY": 240,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 15,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "TangentLightVector",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "6d10568d8f7ec7bdb547f0b76b340898"
        },
        {
            "m_Id": 17,
            "m_EditorPositionX": -2913,
            "m_EditorPositionY": 2521,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "6d10568d8f7ec7bdb547f0b76b340898"
        },
        {
            "m_Id": 18,
            "m_EditorPositionX": -3165,
            "m_EditorPositionY": 596,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCameraVectorWS"
        },
        {
            "m_Id": 19,
            "m_EditorPositionX": -2776,
            "m_EditorPositionY": 612,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTransform",
            "m_Source": 2,
            "m_Destination": 0,
            "m_ElementType": 0,
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 180,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 20,
            "m_EditorPositionX": -2166,
            "m_EditorPositionY": 609,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 156,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 21,
            "m_EditorPositionX": -2166,
            "m_EditorPositionY": 705,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 156,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": false,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 22,
            "m_EditorPositionX": -1917,
            "m_EditorPositionY": 609,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 20,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": -1.0
        },
        {
            "m_Id": 23,
            "m_EditorPositionX": -1695,
            "m_EditorPositionY": 684,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 22,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 21,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 24,
            "m_EditorPositionX": -1469,
            "m_EditorPositionY": 681,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 23,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 25,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 25,
            "m_EditorPositionX": -1664,
            "m_EditorPositionY": 785,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "46a9015149e405b7cb4c5f0141b70f75"
        },
        {
            "m_Id": 26,
            "m_EditorPositionX": -3004,
            "m_EditorPositionY": 552,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "View Trace Length",
            "m_Color": {
                "x": 0.7302894592285156,
                "y": 1.0,
                "z": 0.7848500609397888,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 2099,
            "m_Height": 503
        },
        {
            "m_Id": 27,
            "m_EditorPositionX": -3002,
            "m_EditorPositionY": -140,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 6,
            "m_InputName": "Min Steps",
            "m_InputType": 0,
            "m_SortPriority": 2.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 8.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 28,
            "m_EditorPositionX": -3000,
            "m_EditorPositionY": -60,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 7,
            "m_InputName": "Max Steps",
            "m_InputType": 0,
            "m_SortPriority": 3.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 32.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 29,
            "m_EditorPositionX": -2776,
            "m_EditorPositionY": -156,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 27,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Min Steps",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "f9be3008ea6b57bc234dde5a2becd7b0"
        },
        {
            "m_Id": 30,
            "m_EditorPositionX": -2785,
            "m_EditorPositionY": -60,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 28,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Max Steps",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "22fd073c8fd49e8e4d4f31b08b00b495"
        },
        {
            "m_Id": 31,
            "m_EditorPositionX": -2439,
            "m_EditorPositionY": 1685,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "f9be3008ea6b57bc234dde5a2becd7b0"
        },
        {
            "m_Id": 32,
            "m_EditorPositionX": -2437,
            "m_EditorPositionY": 1599,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "22fd073c8fd49e8e4d4f31b08b00b495"
        },
        {
            "m_Id": 33,
            "m_EditorPositionX": -2264,
            "m_EditorPositionY": 1604,
            "m_Description": "",
            "Class": "cross::MaterialExpressionLerp",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 32,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 31,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Alpha": {
                "m_Name": "Alpha",
                "m_BindedPropertyName": "m_ConstAlpha",
                "LinkedExpressionId": 37,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0,
            "m_ConstAlpha": 0.0
        },
        {
            "m_Id": 34,
            "m_EditorPositionX": -2845,
            "m_EditorPositionY": 1700,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCameraVectorWS"
        },
        {
            "m_Id": 35,
            "m_EditorPositionX": -2615,
            "m_EditorPositionY": 1784,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDotProduct",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 34,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 181,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 36,
            "m_EditorPositionX": -3049,
            "m_EditorPositionY": 1860,
            "m_Description": "",
            "Class": "cross::MaterialExpressionWorldGeometryNormal"
        },
        {
            "m_Id": 37,
            "m_EditorPositionX": -2239,
            "m_EditorPositionY": 1782,
            "m_Description": "",
            "Class": "cross::MaterialExpressionClamp",
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 165,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Min": {
                "m_Name": "Min",
                "m_BindedPropertyName": "m_ConstMin",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Max": {
                "m_Name": "Max",
                "m_BindedPropertyName": "m_ConstMax",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstMin": 0.0,
            "m_ConstMax": 1.0
        },
        {
            "m_Id": 38,
            "m_EditorPositionX": -2025,
            "m_EditorPositionY": 1597,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 33,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 1.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 39,
            "m_EditorPositionX": -1878,
            "m_EditorPositionY": 1814,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFloor",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 33,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 40,
            "m_EditorPositionX": -1800,
            "m_EditorPositionY": 1596,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 38,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Step Size",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "47440cf86d882cab37453b5fbfbeeec8"
        },
        {
            "m_Id": 41,
            "m_EditorPositionX": -1679,
            "m_EditorPositionY": 1814,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 39,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "MaxSteps",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "56d5d90142067b927a4510aa739ad7ee"
        },
        {
            "m_Id": 42,
            "m_EditorPositionX": -2935,
            "m_EditorPositionY": 2137,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "56d5d90142067b927a4510aa739ad7ee"
        },
        {
            "m_Id": 43,
            "m_EditorPositionX": -2919,
            "m_EditorPositionY": 2217,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "47440cf86d882cab37453b5fbfbeeec8"
        },
        {
            "m_Id": 44,
            "m_EditorPositionX": -2277,
            "m_EditorPositionY": -253,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 8,
            "m_InputName": "HeightMap Texture",
            "m_InputType": 4,
            "m_SortPriority": 0.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 45,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 45,
            "m_EditorPositionX": -2513,
            "m_EditorPositionY": -252,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureObject",
            "m_TextureType": 16,
            "m_TextureObjectName": "TextureObject11761",
            "m_TextureString": "EngineResource/Texture/DefaultTexture.nda"
        },
        {
            "m_Id": 46,
            "m_EditorPositionX": -2008,
            "m_EditorPositionY": -252,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 44,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Tex",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "7792853f90384c8b30484ceda038d961"
        },
        {
            "m_Id": 47,
            "m_EditorPositionX": -2935,
            "m_EditorPositionY": 2041,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "7792853f90384c8b30484ceda038d961"
        },
        {
            "m_Id": 48,
            "m_EditorPositionX": -3023,
            "m_EditorPositionY": -328,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "Function Input Init",
            "m_Color": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 1727,
            "m_Height": 384
        },
        {
            "m_Id": 49,
            "m_EditorPositionX": -3054,
            "m_EditorPositionY": 1521,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "Calculate the Steps",
            "m_Color": {
                "x": 1.0,
                "y": 0.7316380739212036,
                "z": 0.8545229434967041,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 1544,
            "m_Height": 363
        },
        {
            "m_Id": 50,
            "m_EditorPositionX": -2517,
            "m_EditorPositionY": -92,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureCoordinate",
            "m_CoordinateIndex": 0,
            "m_UTiling": 1.0,
            "m_VTiling": 1.0
        },
        {
            "m_Id": 51,
            "m_EditorPositionX": -2376,
            "m_EditorPositionY": -92,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 9,
            "m_InputName": "UVs",
            "m_InputType": 1,
            "m_SortPriority": 4.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 50,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 52,
            "m_EditorPositionX": -2184,
            "m_EditorPositionY": -92,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 51,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Uvs",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "c424dbf01b6e968c2b421f810814550d"
        },
        {
            "m_Id": 53,
            "m_EditorPositionX": -2504,
            "m_EditorPositionY": 4,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 10,
            "m_InputName": "Reference Plane",
            "m_InputType": 0,
            "m_SortPriority": 6.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 1.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 54,
            "m_EditorPositionX": -2248,
            "m_EditorPositionY": 4,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 53,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Reference Plane",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "7c822f572091629f004203ae1a8234a5"
        },
        {
            "m_Id": 55,
            "m_EditorPositionX": -3004,
            "m_EditorPositionY": 1211,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "7c822f572091629f004203ae1a8234a5"
        },
        {
            "m_Id": 56,
            "m_EditorPositionX": -2795,
            "m_EditorPositionY": 1211,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "ea47f9666440d68a0743974056b44e6f",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 55,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 57,
            "m_EditorPositionX": -2639,
            "m_EditorPositionY": 1211,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 56,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": -1.0
        },
        {
            "m_Id": 58,
            "m_EditorPositionX": -2406,
            "m_EditorPositionY": 1187,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 162,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 57,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 59,
            "m_EditorPositionX": -1284,
            "m_EditorPositionY": 681,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 24,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 60,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 60,
            "m_EditorPositionX": -1418,
            "m_EditorPositionY": 785,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "47440cf86d882cab37453b5fbfbeeec8"
        },
        {
            "m_Id": 61,
            "m_EditorPositionX": -1105,
            "m_EditorPositionY": 676,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 59,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "UVDist",
            "m_NodeColor": {
                "x": 1.0,
                "y": 0.746408998966217,
                "z": 0.8913230299949646,
                "w": 1.0
            },
            "m_VariableGuid": "cc4644e9d250aba8ac47e6d62c56b781"
        },
        {
            "m_Id": 62,
            "m_EditorPositionX": -2198,
            "m_EditorPositionY": 1187,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 58,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 63,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 63,
            "m_EditorPositionX": -2396,
            "m_EditorPositionY": 1299,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "c424dbf01b6e968c2b421f810814550d"
        },
        {
            "m_Id": 64,
            "m_EditorPositionX": -2031,
            "m_EditorPositionY": 1187,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 62,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "UV",
            "m_NodeColor": {
                "x": 0.3343545198440552,
                "y": 0.3778146505355835,
                "z": 1.0,
                "w": 1.0
            },
            "m_VariableGuid": "1645386fc654a2a7e34d5868a78aa6b6"
        },
        {
            "m_Id": 65,
            "m_EditorPositionX": -3047,
            "m_EditorPositionY": 1120,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "Calculate UV",
            "m_Color": {
                "x": 1.0,
                "y": 0.7505505084991455,
                "z": 0.44263333082199099,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 1406,
            "m_Height": 343
        },
        {
            "m_Id": 66,
            "m_EditorPositionX": -2903,
            "m_EditorPositionY": 2281,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "cc4644e9d250aba8ac47e6d62c56b781"
        },
        {
            "m_Id": 67,
            "m_EditorPositionX": -2945,
            "m_EditorPositionY": 2092,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "1645386fc654a2a7e34d5868a78aa6b6"
        },
        {
            "m_Id": 68,
            "m_EditorPositionX": -2232,
            "m_EditorPositionY": 1315,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDDX",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 62,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 69,
            "m_EditorPositionX": -2040,
            "m_EditorPositionY": 1315,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 68,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "InDDX",
            "m_NodeColor": {
                "x": 0.887604296207428,
                "y": 0.1032940149307251,
                "z": 1.0,
                "w": 1.0
            },
            "m_VariableGuid": "4dc230ce51801b83664fc7632301747e"
        },
        {
            "m_Id": 70,
            "m_EditorPositionX": -2056,
            "m_EditorPositionY": 1395,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 117,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "InDDY",
            "m_NodeColor": {
                "x": 0.13492390513420106,
                "y": 0.279720276594162,
                "z": 0.07678595185279846,
                "w": 1.0
            },
            "m_VariableGuid": "01c1d43c94ba9ba3b14ded6c36198581"
        },
        {
            "m_Id": 71,
            "m_EditorPositionX": -2903,
            "m_EditorPositionY": 2329,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "4dc230ce51801b83664fc7632301747e"
        },
        {
            "m_Id": 72,
            "m_EditorPositionX": -2903,
            "m_EditorPositionY": 2393,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "01c1d43c94ba9ba3b14ded6c36198581"
        },
        {
            "m_Id": 73,
            "m_EditorPositionX": -3042,
            "m_EditorPositionY": 2001,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "Parallax Occlusion Mapping",
            "m_Color": {
                "x": 1.0,
                "y": 0.4432032108306885,
                "z": 0.5624293088912964,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 992,
            "m_Height": 892
        },
        {
            "m_Id": 74,
            "m_EditorPositionX": -2296,
            "m_EditorPositionY": 2052,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 176,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Parallax Occlusion Mapping",
            "m_NodeColor": {
                "x": 0.7865855693817139,
                "y": 0.3721504807472229,
                "z": 1.0,
                "w": 1.0
            },
            "m_VariableGuid": "1463cace9ea182bd234c4c8556adeeb8"
        },
        {
            "m_Id": 75,
            "m_EditorPositionX": -1804,
            "m_EditorPositionY": -252,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Heightmap Channel",
            "m_InputType": 3,
            "m_SortPriority": 5.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 182,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 76,
            "m_EditorPositionX": -1515,
            "m_EditorPositionY": -252,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 75,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Heightmap Channel",
            "m_NodeColor": {
                "x": 0.4823529720306397,
                "y": 1.0,
                "z": 0.27450981736183169,
                "w": 1.0
            },
            "m_VariableGuid": "156f2c68888794be874f840546b3165b"
        },
        {
            "m_Id": 77,
            "m_EditorPositionX": -2921,
            "m_EditorPositionY": 2761,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "156f2c68888794be874f840546b3165b"
        },
        {
            "m_Id": 78,
            "m_EditorPositionX": -1830,
            "m_EditorPositionY": 2097,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCustom",
            "m_Name": "Parallax Only Mapping",
            "m_Code": "float rayheight=1;\r\nfloat oldray=1;\r\nfloat2 offset=0;\r\nfloat oldtex=1;\r\nfloat texatray;\r\nfloat yintersect;\r\nint i=0;\r\n\r\nwhile (i<MaxSteps+2)\r\n{\r\ntexatray=dot(HeightMapChannel, Tex.SampleGrad(TexSampler,UV+offset,InDDX, InDDY));\r\n\r\nif (rayheight < texatray)\r\n{\r\nfloat xintersect = (oldray-oldtex)+(texatray-rayheight);\r\nxintersect=(texatray-rayheight)/xintersect;\r\nyintersect=(oldray*(xintersect))+(rayheight*(1-xintersect));\r\noffset-=(xintersect*UVDist);\r\nbreak;\r\n}\r\n\r\noldray=rayheight;\r\nrayheight-=stepsize;\r\noffset+=UVDist;\r\noldtex=texatray;\r\n\r\n\r\ni++;\r\n}\r\n\r\nfloat4 output;\r\noutput.xy=offset;\r\noutput.z=yintersect; output.w=1;\r\nreturn output; ",
            "m_CustomInputs": [
                {
                    "Name": "Tex",
                    "Input": {
                        "m_Name": "Tex",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 79,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "UV",
                    "Input": {
                        "m_Name": "UV",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 80,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "MaxSteps",
                    "Input": {
                        "m_Name": "MaxSteps",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 81,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "stepsize",
                    "Input": {
                        "m_Name": "stepsize",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 82,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "UVDist",
                    "Input": {
                        "m_Name": "UVDist",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 83,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "InDDX",
                    "Input": {
                        "m_Name": "InDDX",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 84,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "InDDY",
                    "Input": {
                        "m_Name": "InDDY",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 85,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "HeightMapChannel",
                    "Input": {
                        "m_Name": "HeightMapChannel",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 86,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ],
            "m_OutputType": 3,
            "m_AdditionalCustomOutputs": []
        },
        {
            "m_Id": 79,
            "m_EditorPositionX": -2009,
            "m_EditorPositionY": 2093,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "7792853f90384c8b30484ceda038d961"
        },
        {
            "m_Id": 80,
            "m_EditorPositionX": -2003,
            "m_EditorPositionY": 2153,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "1645386fc654a2a7e34d5868a78aa6b6"
        },
        {
            "m_Id": 81,
            "m_EditorPositionX": -2019,
            "m_EditorPositionY": 2217,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "56d5d90142067b927a4510aa739ad7ee"
        },
        {
            "m_Id": 82,
            "m_EditorPositionX": -2019,
            "m_EditorPositionY": 2281,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "47440cf86d882cab37453b5fbfbeeec8"
        },
        {
            "m_Id": 83,
            "m_EditorPositionX": -2003,
            "m_EditorPositionY": 2345,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "cc4644e9d250aba8ac47e6d62c56b781"
        },
        {
            "m_Id": 84,
            "m_EditorPositionX": -1989,
            "m_EditorPositionY": 2424,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "4dc230ce51801b83664fc7632301747e"
        },
        {
            "m_Id": 85,
            "m_EditorPositionX": -1987,
            "m_EditorPositionY": 2489,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "01c1d43c94ba9ba3b14ded6c36198581"
        },
        {
            "m_Id": 86,
            "m_EditorPositionX": -2005,
            "m_EditorPositionY": 2553,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "156f2c68888794be874f840546b3165b"
        },
        {
            "m_Id": 87,
            "m_EditorPositionX": -2034,
            "m_EditorPositionY": 2005,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "Parallax Only Mapping No Shadow Steps",
            "m_Color": {
                "x": 0.295173704624176,
                "y": 0.9909041523933412,
                "z": 1.0,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 470,
            "m_Height": 612
        },
        {
            "m_Id": 88,
            "m_EditorPositionX": -3057,
            "m_EditorPositionY": 1945,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "Main Function",
            "m_Color": {
                "x": 1.0,
                "y": 1.0,
                "z": 1.0,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": false,
            "m_Width": 1509,
            "m_Height": 988
        },
        {
            "m_Id": 89,
            "m_EditorPositionX": -1769,
            "m_EditorPositionY": 2397,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 78,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Parallax Only Mapping",
            "m_NodeColor": {
                "x": 1.0,
                "y": 0.0676126480102539,
                "z": 0.0,
                "w": 1.0
            },
            "m_VariableGuid": "4b6f7c5f29b84b93c74202931b7425f4"
        },
        {
            "m_Id": 90,
            "m_EditorPositionX": -2973,
            "m_EditorPositionY": 3080,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "1463cace9ea182bd234c4c8556adeeb8"
        },
        {
            "m_Id": 91,
            "m_EditorPositionX": -2977,
            "m_EditorPositionY": 3176,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "4b6f7c5f29b84b93c74202931b7425f4"
        },
        {
            "m_Id": 92,
            "m_EditorPositionX": -2684,
            "m_EditorPositionY": 3111,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticSwitch",
            "m_True": {
                "m_Name": "True",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 90,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_False": {
                "m_Name": "False",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 91,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "m_DefaultValue",
                "LinkedExpressionId": 93,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DefaultValue": true
        },
        {
            "m_Id": 93,
            "m_EditorPositionX": -2832,
            "m_EditorPositionY": 3288,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 11,
            "m_InputName": "Render Shadows(Occlusion Mapping)",
            "m_InputType": 6,
            "m_SortPriority": 21.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 94,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 94,
            "m_EditorPositionX": -2973,
            "m_EditorPositionY": 3288,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticBool",
            "m_Value": false
        },
        {
            "m_Id": 95,
            "m_EditorPositionX": -2361,
            "m_EditorPositionY": 3112,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 92,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 96,
            "m_EditorPositionX": -2361,
            "m_EditorPositionY": 3192,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 92,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 97,
            "m_EditorPositionX": -2361,
            "m_EditorPositionY": 3272,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 92,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": false,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 98,
            "m_EditorPositionX": -2361,
            "m_EditorPositionY": 3352,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 92,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": false,
            "m_B": false,
            "m_A": true
        },
        {
            "m_Id": 99,
            "m_EditorPositionX": -2160,
            "m_EditorPositionY": 3352,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticSwitch",
            "m_True": {
                "m_Name": "True",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 98,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_False": {
                "m_Name": "False",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 100,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "m_DefaultValue",
                "LinkedExpressionId": 93,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DefaultValue": true
        },
        {
            "m_Id": 100,
            "m_EditorPositionX": -2365,
            "m_EditorPositionY": 3416,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 1.0
        },
        {
            "m_Id": 101,
            "m_EditorPositionX": -2160,
            "m_EditorPositionY": 3272,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "ea47f9666440d68a0743974056b44e6f",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 97,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 102,
            "m_EditorPositionX": -2115,
            "m_EditorPositionY": 3160,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "46a9015149e405b7cb4c5f0141b70f75"
        },
        {
            "m_Id": 103,
            "m_EditorPositionX": -1744,
            "m_EditorPositionY": 3200,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAppendVector",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 96,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 104,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 104,
            "m_EditorPositionX": -1959,
            "m_EditorPositionY": 3248,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 101,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 102,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 105,
            "m_EditorPositionX": -2112,
            "m_EditorPositionY": 3032,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 175,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 95,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 106,
            "m_EditorPositionX": -1832,
            "m_EditorPositionY": 3017,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 12,
            "m_OutputName": "Parallax UVs",
            "m_SortPriority": 0.0,
            "m_Output": {
                "m_Name": "Output",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 105,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 107,
            "m_EditorPositionX": -1827,
            "m_EditorPositionY": 3098,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 13,
            "m_OutputName": "Offset Only",
            "m_SortPriority": 1.0,
            "m_Output": {
                "m_Name": "Output",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 95,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 108,
            "m_EditorPositionX": -1920,
            "m_EditorPositionY": 3352,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 14,
            "m_OutputName": "Shadow",
            "m_SortPriority": 2.0,
            "m_Output": {
                "m_Name": "Output",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 99,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 109,
            "m_EditorPositionX": -1552,
            "m_EditorPositionY": 3200,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDistance",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 103,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 110,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 110,
            "m_EditorPositionX": -1725,
            "m_EditorPositionY": 3304,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant",
            "m_Const": 0.0
        },
        {
            "m_Id": 111,
            "m_EditorPositionX": -1392,
            "m_EditorPositionY": 3200,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 109,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 133,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 112,
            "m_EditorPositionX": -1392,
            "m_EditorPositionY": 3312,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 109,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 113,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 113,
            "m_EditorPositionX": -1686,
            "m_EditorPositionY": 3426,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 15,
            "m_InputName": "Manual Texture Size",
            "m_InputType": 0,
            "m_SortPriority": 12.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 1024.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 114,
            "m_EditorPositionX": -1088,
            "m_EditorPositionY": 3176,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticSwitch",
            "m_True": {
                "m_Name": "True",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 112,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_False": {
                "m_Name": "False",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 111,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "m_DefaultValue",
                "LinkedExpressionId": 116,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DefaultValue": true
        },
        {
            "m_Id": 115,
            "m_EditorPositionX": -1533,
            "m_EditorPositionY": 3528,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticBool",
            "m_Value": true
        },
        {
            "m_Id": 116,
            "m_EditorPositionX": -1388,
            "m_EditorPositionY": 3528,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 16,
            "m_InputName": "Specify Manual Texture Size",
            "m_InputType": 6,
            "m_SortPriority": 11.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 115,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 117,
            "m_EditorPositionX": -2241,
            "m_EditorPositionY": 1394,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDDY",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 62,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 118,
            "m_EditorPositionX": -2969,
            "m_EditorPositionY": 3687,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "1645386fc654a2a7e34d5868a78aa6b6"
        },
        {
            "m_Id": 119,
            "m_EditorPositionX": -2811,
            "m_EditorPositionY": 3677,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDDX",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 118,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 120,
            "m_EditorPositionX": -2825,
            "m_EditorPositionY": 3744,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDDY",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 118,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 121,
            "m_EditorPositionX": -2615,
            "m_EditorPositionY": 3688,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAbs",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 119,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 122,
            "m_EditorPositionX": -2613,
            "m_EditorPositionY": 3791,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAbs",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 120,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 123,
            "m_EditorPositionX": -2382,
            "m_EditorPositionY": 3665,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "af8fae642e8e508c0c4d654c0917de49",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "Vector 3",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": -1,
                        "LinkedExpressionOutputIndex": -1,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "Vector 2",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 121,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 124,
            "m_EditorPositionX": -2384,
            "m_EditorPositionY": 3779,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "af8fae642e8e508c0c4d654c0917de49",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "Vector 3",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": -1,
                        "LinkedExpressionOutputIndex": -1,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "Vector 2",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 122,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 125,
            "m_EditorPositionX": -2112,
            "m_EditorPositionY": 3707,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 123,
                "LinkedExpressionOutputIndex": 1,
                "LinkedExpressionOutputFunctionInoutId": 1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 129,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 126,
            "m_EditorPositionX": -3052,
            "m_EditorPositionY": 4114,
            "m_Description": "",
            "Class": "cross::MaterialExpressionWorldPosition"
        },
        {
            "m_Id": 127,
            "m_EditorPositionX": -2813,
            "m_EditorPositionY": 3866,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDDX",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 164,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 128,
            "m_EditorPositionX": -2806,
            "m_EditorPositionY": 3956,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDDY",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 164,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 129,
            "m_EditorPositionX": -2573,
            "m_EditorPositionY": 3916,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "af8fae642e8e508c0c4d654c0917de49",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "Vector 3",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 127,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "Vector 2",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": -1,
                        "LinkedExpressionOutputIndex": -1,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 130,
            "m_EditorPositionX": -2579,
            "m_EditorPositionY": 4008,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "af8fae642e8e508c0c4d654c0917de49",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "Vector 3",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 128,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "Vector 2",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": -1,
                        "LinkedExpressionOutputIndex": -1,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 131,
            "m_EditorPositionX": -2120,
            "m_EditorPositionY": 3865,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 124,
                "LinkedExpressionOutputIndex": 1,
                "LinkedExpressionOutputFunctionInoutId": 1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 130,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 132,
            "m_EditorPositionX": -1906,
            "m_EditorPositionY": 3796,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMax",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 125,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 131,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 133,
            "m_EditorPositionX": -1702,
            "m_EditorPositionY": 3876,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 132,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 137,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 134,
            "m_EditorPositionX": -2111,
            "m_EditorPositionY": 4030,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "ff3d274bd7115c954049a26f6c3ec436",
            "m_FunctionInputs": []
        },
        {
            "m_Id": 135,
            "m_EditorPositionX": -1860,
            "m_EditorPositionY": 4068,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDotProduct",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 134,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 136,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 136,
            "m_EditorPositionX": -2069,
            "m_EditorPositionY": 4129,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCameraVectorWS"
        },
        {
            "m_Id": 137,
            "m_EditorPositionX": -1697,
            "m_EditorPositionY": 4067,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAbs",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 135,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 138,
            "m_EditorPositionX": -2991,
            "m_EditorPositionY": 3610,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "UV to World Ratio",
            "m_Color": {
                "x": 0.6922762989997864,
                "y": 1.0,
                "z": 0.8881863951683044,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 1601,
            "m_Height": 628
        },
        {
            "m_Id": 139,
            "m_EditorPositionX": -1339,
            "m_EditorPositionY": 3770,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 132,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 140,
            "m_EditorPositionX": -1129,
            "m_EditorPositionY": 3770,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 139,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 141,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 141,
            "m_EditorPositionX": -1343,
            "m_EditorPositionY": 3885,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCameraVectorWS"
        },
        {
            "m_Id": 142,
            "m_EditorPositionX": -1250,
            "m_EditorPositionY": 3665,
            "m_Description": "",
            "Class": "cross::MaterialExpressionWorldPosition"
        },
        {
            "m_Id": 143,
            "m_EditorPositionX": -828,
            "m_EditorPositionY": 3733,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 166,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 140,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 144,
            "m_EditorPositionX": -615,
            "m_EditorPositionY": 3736,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 17,
            "m_OutputName": "WorldPosition",
            "m_SortPriority": 4.0,
            "m_Output": {
                "m_Name": "Output",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 143,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 145,
            "m_EditorPositionX": -3064,
            "m_EditorPositionY": 2976,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "Output",
            "m_Color": {
                "x": 0.3751916289329529,
                "y": 1.0,
                "z": 0.45400696992874148,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 2666,
            "m_Height": 1276
        },
        {
            "m_Id": 146,
            "m_EditorPositionX": -3019,
            "m_EditorPositionY": -497,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "ReadMe",
            "m_Color": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 1361,
            "m_Height": 125
        },
        {
            "m_Id": 147,
            "m_EditorPositionX": -2998,
            "m_EditorPositionY": -434,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "这个函数基于UE的自带的视差映射节点POM移植，因为部分功能CE尚未支持，有些函数没有同步移植过来，由于是手动写的重写编排了逻辑，因此可能有些错误",
            "m_Color": {
                "x": 0.07798001170158386,
                "y": 0.14685314893722538,
                "z": 0.11337205022573473,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": false,
            "m_Width": 400,
            "m_Height": 17
        },
        {
            "m_Id": 148,
            "m_EditorPositionX": -1796,
            "m_EditorPositionY": -28,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 18,
            "m_InputName": "===========================",
            "m_InputType": 6,
            "m_SortPriority": 10.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 149,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 149,
            "m_EditorPositionX": -1973,
            "m_EditorPositionY": -29,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticBool",
            "m_Value": true
        },
        {
            "m_Id": 150,
            "m_EditorPositionX": -1978,
            "m_EditorPositionY": -96,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticBool",
            "m_Value": true
        },
        {
            "m_Id": 151,
            "m_EditorPositionX": -1801,
            "m_EditorPositionY": -95,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 19,
            "m_InputName": "============================",
            "m_InputType": 6,
            "m_SortPriority": 20.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 150,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 152,
            "m_EditorPositionX": -1626,
            "m_EditorPositionY": 339,
            "m_Description": "The Light Vector Transformed into Tangent Space. Useful for custom effects using the lighting.",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 20,
            "m_OutputName": "Tangent Light Vector",
            "m_SortPriority": 5.0,
            "m_Output": {
                "m_Name": "Output",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 153,
            "m_EditorPositionX": -3013,
            "m_EditorPositionY": -462,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "CE到UE的这个WorldPosition存在差异，所以在不同情况需要看着来修正",
            "m_Color": {
                "x": 0.13286712765693665,
                "y": 0.06570542603731156,
                "z": 0.11351266503334046,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": false,
            "m_Width": 1014,
            "m_Height": 53
        },
        {
            "m_Id": 154,
            "m_EditorPositionX": -2958,
            "m_EditorPositionY": 702,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticBool",
            "m_Value": false
        },
        {
            "m_Id": 155,
            "m_EditorPositionX": -2799,
            "m_EditorPositionY": 702,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 21,
            "m_InputName": "Use World Coordinates",
            "m_InputType": 6,
            "m_SortPriority": 11.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 154,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 156,
            "m_EditorPositionX": -2404,
            "m_EditorPositionY": 658,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticSwitch",
            "m_True": {
                "m_Name": "True",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 159,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_False": {
                "m_Name": "False",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 19,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "m_DefaultValue",
                "LinkedExpressionId": 155,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DefaultValue": true
        },
        {
            "m_Id": 157,
            "m_EditorPositionX": -2716,
            "m_EditorPositionY": 789,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "77a599e021c92c8f574fb6375ab897d6",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "Z Vector",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 158,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "Center Location",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 178,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "Vector to Transform",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 18,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "CEToUEWP",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 171,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 158,
            "m_EditorPositionX": -2989,
            "m_EditorPositionY": 788,
            "m_Description": "",
            "Class": "cross::MaterialExpressionWorldGeometryNormal"
        },
        {
            "m_Id": 159,
            "m_EditorPositionX": -2412,
            "m_EditorPositionY": 789,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticSwitch",
            "m_True": {
                "m_Name": "True",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 157,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_False": {
                "m_Name": "False",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 18,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "m_DefaultValue",
                "LinkedExpressionId": 161,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DefaultValue": true
        },
        {
            "m_Id": 160,
            "m_EditorPositionX": -2908,
            "m_EditorPositionY": 994,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticBool",
            "m_Value": true
        },
        {
            "m_Id": 161,
            "m_EditorPositionX": -2737,
            "m_EditorPositionY": 994,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 22,
            "m_InputName": "Transform To VertexNormal",
            "m_InputType": 6,
            "m_SortPriority": 50.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 160,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 162,
            "m_EditorPositionX": -2588,
            "m_EditorPositionY": 1155,
            "m_Description": "",
            "Class": "cross::MaterialExpressionRerouter",
            "m_Input": {
                "m_Name": "",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 24,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 163,
            "m_EditorPositionX": -2872,
            "m_EditorPositionY": 4095,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCEToUEWorldPosition",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 126,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_CEToUE": true
        },
        {
            "m_Id": 164,
            "m_EditorPositionX": -2584,
            "m_EditorPositionY": 4128,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticSwitch",
            "m_True": {
                "m_Name": "True",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 163,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_False": {
                "m_Name": "False",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 126,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "m_DefaultValue",
                "LinkedExpressionId": 172,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DefaultValue": true
        },
        {
            "m_Id": 165,
            "m_EditorPositionX": -2448,
            "m_EditorPositionY": 1785,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAbs",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 35,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 166,
            "m_EditorPositionX": -844,
            "m_EditorPositionY": 3573,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticSwitch",
            "m_True": {
                "m_Name": "True",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 167,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_False": {
                "m_Name": "False",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 142,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "m_DefaultValue",
                "LinkedExpressionId": 173,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DefaultValue": true
        },
        {
            "m_Id": 167,
            "m_EditorPositionX": -1062,
            "m_EditorPositionY": 3572,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCEToUEWorldPosition",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 142,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_CEToUE": true
        },
        {
            "m_Id": 168,
            "m_EditorPositionX": -1737,
            "m_EditorPositionY": -172,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 24,
            "m_InputName": "CEToUEWP",
            "m_InputType": 6,
            "m_SortPriority": 40.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 170,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 169,
            "m_EditorPositionX": -1555,
            "m_EditorPositionY": -172,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteDeclaration",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 168,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Name": "Ce To Ue WorldPosition",
            "m_NodeColor": {
                "x": 1.0,
                "y": 0.6336117386817932,
                "z": 0.6884028315544128,
                "w": 1.0
            },
            "m_VariableGuid": "d1fd3348764f1b801d466406a10a4459"
        },
        {
            "m_Id": 170,
            "m_EditorPositionX": -1901,
            "m_EditorPositionY": -175,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticBool",
            "m_Value": true
        },
        {
            "m_Id": 171,
            "m_EditorPositionX": -2956,
            "m_EditorPositionY": 930,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "d1fd3348764f1b801d466406a10a4459"
        },
        {
            "m_Id": 172,
            "m_EditorPositionX": -2904,
            "m_EditorPositionY": 4205,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "d1fd3348764f1b801d466406a10a4459"
        },
        {
            "m_Id": 173,
            "m_EditorPositionX": -996,
            "m_EditorPositionY": 3886,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "d1fd3348764f1b801d466406a10a4459"
        },
        {
            "m_Id": 174,
            "m_EditorPositionX": -2808,
            "m_EditorPositionY": 236,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": -1.0
        },
        {
            "m_Id": 175,
            "m_EditorPositionX": -2338,
            "m_EditorPositionY": 3023,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNamedRerouteUsage",
            "m_DeclarationGuid": "1645386fc654a2a7e34d5868a78aa6b6"
        },
        {
            "m_Id": 176,
            "m_EditorPositionX": -2632,
            "m_EditorPositionY": 2044,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCustom",
            "m_Name": "Parallax Occlusion Mapping",
            "m_Code": "float rayheight=1;\r\nfloat oldray=1;\r\nfloat2 offset=0;\r\nfloat oldtex=1;\r\nfloat texatray;\r\nfloat yintersect;\r\nint i=0;\r\n\r\nwhile(i<MaxSteps+2)\r\n{\r\n\r\nfloat texatray=dot(HeightMapChannel, Tex.SampleGrad(TexSampler,UV+offset,InDDX, InDDY));\r\n\r\nif (rayheight < texatray)\r\n{\r\nfloat xintersect = (oldray-oldtex)+(texatray-rayheight);\r\nxintersect=(texatray-rayheight)/xintersect;\r\nyintersect=(oldray*(xintersect))+(rayheight*(1-xintersect));\r\noffset-=(xintersect*UVDist);\r\nbreak;\r\n}\r\n\r\noldray=rayheight;\r\nrayheight-=stepsize;\r\noffset+=UVDist;\r\noldtex=texatray;\r\n\r\ni++;\r\n}\r\n\r\n\r\nfloat2 saveoffset=offset;\r\nfloat shadow=1;\r\nfloat dist=0;\r\n\r\n\r\ntexatray=dot(HeightMapChannel, Tex.SampleGrad(TexSampler,UV+offset,InDDX, InDDY))+0.01;\r\nfloat finalrayz=yintersect;\r\n\r\nrayheight=texatray;\r\nfloat lightstepsize=1/ShadowSteps;\r\n\r\nint j=0;\r\nwhile(j<ShadowSteps)\r\n{\r\nif(rayheight < texatray)\r\n{\r\nshadow=0;\r\nbreak;\r\n}\r\nelse\r\n{\r\nshadow=min(shadow,(rayheight-texatray)*k/dist);\r\n}\r\n\r\noldray=rayheight;\r\nrayheight+=TangentLightVector.z*lightstepsize;\r\n\r\noffset+=TangentLightVector.xy*lightstepsize;\r\noldtex=texatray;\r\n\r\ntexatray=dot(HeightMapChannel, Tex.SampleGrad(TexSampler,UV+offset,InDDX, InDDY));\r\ndist+=lightstepsize;\r\nj++;\r\n}\r\n\r\n//shadow = 1;\r\nfloat4 finalout;\r\nfinalout.xy=saveoffset;\r\nfinalout.z=finalrayz;\r\nfinalout.w=shadow;\r\nreturn finalout;\r\n",
            "m_CustomInputs": [
                {
                    "Name": "Tex",
                    "Input": {
                        "m_Name": "Tex",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 47,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "UV",
                    "Input": {
                        "m_Name": "UV",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 67,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "MaxSteps",
                    "Input": {
                        "m_Name": "MaxSteps",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 42,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "stepsize",
                    "Input": {
                        "m_Name": "stepsize",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 43,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "UVDist",
                    "Input": {
                        "m_Name": "UVDist",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 66,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "InDDX",
                    "Input": {
                        "m_Name": "InDDX",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 71,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "InDDY",
                    "Input": {
                        "m_Name": "InDDY",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 72,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "TexHeight",
                    "Input": {
                        "m_Name": "TexHeight",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 12,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "TangentLightVector",
                    "Input": {
                        "m_Name": "TangentLightVector",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 17,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "ShadowSteps",
                    "Input": {
                        "m_Name": "ShadowSteps",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 3,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "k",
                    "Input": {
                        "m_Name": "k",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 2,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Name": "HeightMapChannel",
                    "Input": {
                        "m_Name": "HeightMapChannel",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 77,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ],
            "m_OutputType": 3,
            "m_AdditionalCustomOutputs": []
        },
        {
            "m_Id": 178,
            "m_EditorPositionX": -2940,
            "m_EditorPositionY": 855,
            "m_Description": "",
            "Class": "cross::MaterialExpressionWorldPosition"
        },
        {
            "m_Id": 179,
            "m_EditorPositionX": -2617,
            "m_EditorPositionY": 324,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCEToUEWorldPosition",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 174,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_CEToUE": true
        },
        {
            "m_Id": 180,
            "m_EditorPositionX": -3001,
            "m_EditorPositionY": 596,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCEToUEWorldPosition",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 18,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_CEToUE": true
        },
        {
            "m_Id": 181,
            "m_EditorPositionX": -2825,
            "m_EditorPositionY": 1844,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCEToUEWorldPosition",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 36,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_CEToUE": true
        },
        {
            "m_Id": 182,
            "m_EditorPositionX": -1797,
            "m_EditorPositionY": -316,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant4Vector",
            "m_Value": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 1.0
            }
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}