adnj
{
    "Guid": "77a599e021c92c8f574fb6375ab897d6",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 10668,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 774,
            "m_EditorPositionY": 83,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "m_Name": "Output",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -424,
            "m_EditorPositionY": 20,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Z Vector",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 1.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -225,
            "m_EditorPositionY": 20,
            "m_Description": "",
            "Class": "cross::MaterialExpressionNormalize",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -46,
            "m_EditorPositionY": 156,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "f593742a758734be6441043b2fb04e1f",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "Vector1",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 3,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "Vector2",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 5,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -303,
            "m_EditorPositionY": 180,
            "m_Description": "",
            "Class": "cross::MaterialExpressionConstant3Vector",
            "m_Value": {
                "x": 1.0,
                "y": 0.0,
                "z": 0.0
            }
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": 346,
            "m_EditorPositionY": 108,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "97ac4e5189e2b8a237492acd077ffe90",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "m_Name": "VectorToTransform",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 7,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "m_Name": "BasisX",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 4,
                        "LinkedExpressionOutputIndex": 1,
                        "LinkedExpressionOutputFunctionInoutId": 1
                    }
                },
                {
                    "Input": {
                        "m_Name": "BasisY",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 4,
                        "LinkedExpressionOutputIndex": 2,
                        "LinkedExpressionOutputFunctionInoutId": 2
                    }
                },
                {
                    "Input": {
                        "m_Name": "BasisZ",
                        "m_BindedPropertyName": "",
                        "LinkedExpressionId": 4,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": 273,
            "m_EditorPositionY": -44,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "m_Name": "A",
                "m_BindedPropertyName": "m_ConstA",
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "m_Name": "B",
                "m_BindedPropertyName": "m_ConstB",
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -8,
            "m_EditorPositionY": -28,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "Center Location",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": -483,
            "m_EditorPositionY": -268,
            "m_Description": "",
            "Class": "cross::MaterialExpressionWorldPosition"
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": 228,
            "m_EditorPositionY": -268,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 3,
            "m_InputName": "Vector to Transform",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 12,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": -296,
            "m_EditorPositionY": -268,
            "m_Description": "",
            "Class": "cross::MaterialExpressionCEToUEWorldPosition",
            "m_Input": {
                "m_Name": "Input",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_CEToUE": true
        },
        {
            "m_Id": 12,
            "m_EditorPositionX": 14,
            "m_EditorPositionY": -268,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticSwitch",
            "m_True": {
                "m_Name": "True",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 11,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_False": {
                "m_Name": "False",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Value": {
                "m_Name": "Value",
                "m_BindedPropertyName": "m_DefaultValue",
                "LinkedExpressionId": 14,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DefaultValue": true
        },
        {
            "m_Id": 14,
            "m_EditorPositionX": -186,
            "m_EditorPositionY": -172,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 4,
            "m_InputName": "CEToUEWP",
            "m_InputType": 6,
            "m_SortPriority": 1.0,
            "m_UsePreviewAsDefault": true,
            "m_Preview": {
                "m_Name": "Preview",
                "m_BindedPropertyName": "",
                "LinkedExpressionId": 15,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            },
            "m_IsUETexture2DInput": false
        },
        {
            "m_Id": 15,
            "m_EditorPositionX": -341,
            "m_EditorPositionY": -124,
            "m_Description": "",
            "Class": "cross::MaterialExpressionStaticBool",
            "m_Value": true
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}