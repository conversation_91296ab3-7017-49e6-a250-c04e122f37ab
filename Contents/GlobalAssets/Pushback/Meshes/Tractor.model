adnj
{
    "Guid": "519c26569b5364df0a0de60c5b5ca7bc",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 5854,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "2652797a8766d48c38dd991aac67769f",
        "81375b2984621416ea2ff11bdd29c155",
        "8466010fefb3b47e58d072b7a9a2db3d"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "863665a58dd064db980501d192beda8b"
        },
        "entities": {
            "863665a58dd064db980501d192beda8b": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "863665a58dd064db980501d192beda8b",
                "name": "Tractor",
                "prototype": 2043631013917525051,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 3,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mModels": [
                            {
                                "mSubModelProperties": [
                                    {
                                        "mMaterial": "81375b2984621416ea2ff11bdd29c155",
                                        "mVisible": true
                                    },
                                    {
                                        "mMaterial": "81375b2984621416ea2ff11bdd29c155",
                                        "mVisible": true
                                    },
                                    {
                                        "mMaterial": "81375b2984621416ea2ff11bdd29c155",
                                        "mVisible": true
                                    }
                                ],
                                "mAsset": "8466010fefb3b47e58d072b7a9a2db3d",
                                "mVisible": true,
                                "mReceiveDecals": true
                            }
                        ],
                        "mEnableGPUSkin": false,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::SkeletonComponentG": {
                        "Path": "2652797a8766d48c38dd991aac67769f",
                        "PhysicsPath": "",
                        "componentHash": 2294777672
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 524288
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Tractor",
                            "ScriptPath": "",
                            "ScriptDefaultValue": "",
                            "ScriptEditorFields": "",
                            "LocalRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            },
                            "LocalTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "LocalScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            }
        }
    }
}