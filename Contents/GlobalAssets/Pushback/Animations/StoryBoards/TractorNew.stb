<NodeGraph
  Name = "TractorNew"
  NodeID = "10629"
  ConnectionID = "21198"
  HashName = "42349342141550739095">
  <Camera MaxZoom = "5" MinZoom = "-10" ZoomCount = "1">
    <Viewport
      _WorldX = "-1055.5659"
      _WorldY = "-210.04037"
      _Height = "946.4009"
      _AspectRatio = "1.6153846"/>
    <Viewport
      _WorldX = "970"
      _WorldY = "80"
      _Height = "1183"
      _AspectRatio = "1.6153846"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "990"
    Y = "310"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21039"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_BlendByLayeredFilterNode
    ID = "10366"
    X = "-60"
    Y = "-220"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21112",
    "21121",
    "21122",
    "21135",
    "21138"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "frontwheel_l_root",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "frontwheel_r_root",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "reerwheel_l_root",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "reerwheel_r_root",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10366",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "frontwheel_l_tip"
    ID = "10421"
    X = "-560"
    Y = "490"
    NodeJsonData = ##{
  "BoneName": "frontwheel_l_tip",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21177",
    ""
  ],
  "Name": "10421",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "frontwheel_r_tip"
    ID = "10431"
    X = "-560"
    Y = "600"
    NodeJsonData = ##{
  "BoneName": "frontwheel_r_tip",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21183",
    ""
  ],
  "Name": "10431",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "frontwheel_r_root"
    ID = "10495"
    X = "-550"
    Y = "0"
    NodeJsonData = ##{
  "BoneName": "frontwheel_r_root",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21072",
    ""
  ],
  "Name": "10495",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "frontwheel_l_root"
    ID = "10528"
    X = "-550"
    Y = "-170"
    NodeJsonData = ##{
  "BoneName": "frontwheel_l_root",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21063",
    ""
  ],
  "Name": "10528",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "reerwheel_l_root"
    ID = "10529"
    X = "-560"
    Y = "160"
    NodeJsonData = ##{
  "BoneName": "reerwheel_l_root",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21091",
    ""
  ],
  "Name": "10529",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "reerwheel_r_root"
    ID = "10544"
    X = "-560"
    Y = "300"
    NodeJsonData = ##{
  "BoneName": "reerwheel_r_root",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21105",
    ""
  ],
  "Name": "10544",
  "Type": "TransformBoneNode"
}##/>
  <Anim_BlendByLayeredFilterNode
    ID = "10562"
    X = "430"
    Y = "310"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21060",
    "21049",
    "21056"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "frontwheel_l_tip",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "frontwheel_r_tip",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10562",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "WheelSpin"
    ParamType = "Vector3"
    ID = "10578"
    X = "-980"
    Y = "40"
    NodeJsonData = ##{
  "InParams": [
    "WheelSpin"
  ],
  "ReturnType": "Vector3",
  "Name": "10578",
  "Type": "ParamImplNode"
}##/>
  <Anim_SlotNode
    SlotName = "DefaultSlot"
    GroupName = "DefaultGroup"
    ID = "10591"
    X = "-560"
    Y = "-350"
    NodeJsonData = ##{
  "InPoseLinks": [],
  "SlotName": "DefaultSlot",
  "Name": "10591",
  "Type": "SlotNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "FrontWheelSteering"
    ParamType = "Vector3"
    ID = "10613"
    X = "-990"
    Y = "540"
    NodeJsonData = ##{
  "InParams": [
    "FrontWheelSteering"
  ],
  "ReturnType": "Vector3",
  "Name": "10613",
  "Type": "ParamImplNode"
}##/>
  <Connection
    ID = "21039"
    OutSlotNodeID = "10562"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
  <Connection
    ID = "21049"
    OutSlotNodeID = "10421"
    OutSlotIndex = "0"
    InSlotNodeID = "10562"
    InSlotIndex = "1"/>
  <Connection
    ID = "21056"
    OutSlotNodeID = "10431"
    OutSlotIndex = "0"
    InSlotNodeID = "10562"
    InSlotIndex = "2"/>
  <Connection
    ID = "21060"
    OutSlotNodeID = "10366"
    OutSlotIndex = "0"
    InSlotNodeID = "10562"
    InSlotIndex = "0"/>
  <Connection
    ID = "21063"
    OutSlotNodeID = "10578"
    OutSlotIndex = "0"
    InSlotNodeID = "10528"
    InSlotIndex = "2"/>
  <Connection
    ID = "21072"
    OutSlotNodeID = "10578"
    OutSlotIndex = "0"
    InSlotNodeID = "10495"
    InSlotIndex = "2"/>
  <Connection
    ID = "21091"
    OutSlotNodeID = "10578"
    OutSlotIndex = "0"
    InSlotNodeID = "10529"
    InSlotIndex = "2"/>
  <Connection
    ID = "21105"
    OutSlotNodeID = "10578"
    OutSlotIndex = "0"
    InSlotNodeID = "10544"
    InSlotIndex = "2"/>
  <Connection
    ID = "21112"
    OutSlotNodeID = "10591"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "0"/>
  <Connection
    ID = "21121"
    OutSlotNodeID = "10528"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "1"/>
  <Connection
    ID = "21122"
    OutSlotNodeID = "10495"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "2"/>
  <Connection
    ID = "21135"
    OutSlotNodeID = "10529"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "3"/>
  <Connection
    ID = "21138"
    OutSlotNodeID = "10544"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "4"/>
  <Connection
    ID = "21177"
    OutSlotNodeID = "10613"
    OutSlotIndex = "0"
    InSlotNodeID = "10421"
    InSlotIndex = "2"/>
  <Connection
    ID = "21183"
    OutSlotNodeID = "10613"
    OutSlotIndex = "0"
    InSlotNodeID = "10431"
    InSlotIndex = "2"/>
</NodeGraph>
<StbProperty ApplyMode = "0" ExtractionMode = "1">
  <Param ParamType = "CrossEditor.AnimParameter_Vector3" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": -30.0,
    "Z": 0.0
  },
  "Type": "Vector3",
  "Name": "FrontWheelSteering"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector3" ParamJson = ##{
  "Value": {
    "X": 90.0,
    "Y": 0.0,
    "Z": 0.0
  },
  "Type": "Vector3",
  "Name": "WheelSpin"
}##/>
</StbProperty>
