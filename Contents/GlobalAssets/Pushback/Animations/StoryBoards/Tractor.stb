<NodeGraph
  Name = "Tractor"
  NodeID = "10298"
  ConnectionID = "20288"
  HashName = "36246275963943689611">
  <Camera MaxZoom = "5" MinZoom = "-10" ZoomCount = "1">
    <Viewport
      _WorldX = "-1230.3035"
      _WorldY = "-275.9979"
      _Height = "982.4002"
      _AspectRatio = "1.6767101"/>
    <Viewport
      _WorldX = "627"
      _WorldY = "80"
      _Height = "1228"
      _AspectRatio = "1.6767101"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "220"
    Y = "70"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20268"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "FrontWheelTurn"
    ParamType = "Vector2"
    ID = "10199"
    X = "-1010"
    Y = "0"
    NodeJsonData = ##{
  "InParams": [
    "FrontWheelTurn"
  ],
  "ReturnType": "Vector2",
  "Name": "10199",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Pushback/Animations/Blendspaces/front_WheelTurn_BlendSpace.nda"
    ID = "10229"
    X = "-720"
    Y = "-20"
    NodeJsonData = ##{
  "BlendSpacePath": "b93efa0a4f0344da095314a0ea684ffb",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20147"
  ],
  "Name": "10229",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Pushback/Animations/Blendspaces/WheelSpin_BlendSpace.nda"
    ID = "10231"
    X = "-680"
    Y = "60"
    NodeJsonData = ##{
  "BlendSpacePath": "4f36622c151f7496b952e381e494419f",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20183"
  ],
  "Name": "10231",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_ParamImplNode
    ParamName = "WheelSpin"
    ParamType = "Vector2"
    ID = "10248"
    X = "-1020"
    Y = "170"
    NodeJsonData = ##{
  "InParams": [
    "WheelSpin"
  ],
  "ReturnType": "Vector2",
  "Name": "10248",
  "Type": "ParamImplNode"
}##/>
  <Anim_BlendByLayeredFilterNode
    ID = "10285"
    X = "-180"
    Y = "40"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20270",
    "20277"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "FrontWheel_L_tip",
          "Depth": 1
        },
        {
          "BoneName": "FrontWheel_R_tip",
          "Depth": 1
        },
        {
          "BoneName": "ReerWheel_L_tip",
          "Depth": 1
        },
        {
          "BoneName": "ReerWheel_R_tip",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10285",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Connection
    ID = "20147"
    OutSlotNodeID = "10199"
    OutSlotIndex = "0"
    InSlotNodeID = "10229"
    InSlotIndex = "1"/>
  <Connection
    ID = "20183"
    OutSlotNodeID = "10248"
    OutSlotIndex = "0"
    InSlotNodeID = "10231"
    InSlotIndex = "1"/>
  <Connection
    ID = "20268"
    OutSlotNodeID = "10285"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
  <Connection
    ID = "20270"
    OutSlotNodeID = "10229"
    OutSlotIndex = "0"
    InSlotNodeID = "10285"
    InSlotIndex = "0"/>
  <Connection
    ID = "20277"
    OutSlotNodeID = "10231"
    OutSlotIndex = "0"
    InSlotNodeID = "10285"
    InSlotIndex = "1"/>
</NodeGraph>
<StbProperty ApplyMode = "0" ExtractionMode = "1">
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "FrontWheelTurn"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "WheelSpin"
}##/>
</StbProperty>
