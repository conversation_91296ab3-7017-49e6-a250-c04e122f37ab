
PushBackAircraft = Class();
PushBackAircraft["data_table"] = {}
PushBackAircraft["data_index"] = 2
PushBackAircraft["data_fps"] = (1.0 / 60.0)
PushBackAircraft["delta_time"] = 0.0
PushBackAircraft["all_time"] = 0.0

PushBackAircraft["elevation_index"] = 5
PushBackAircraft["heading_index"] = 6
PushBackAircraft["lat_index"] = 3
PushBackAircraft["long_index"] = 4
PushBackAircraft["pitch_index"] = 7
PushBackAircraft["roll_index"] = 8

PushBackAircraft["controller"] = nil
PushBackAircraft["frontWheelOffset"] = nil
function PushBackAircraft:OnCreate()
    self.wgs84 = self.Entity:GetFFSWGS84Component()
    self.worldTransition = self.Entity:GetTransformComponent():GetWorldTranslation()
    self.localTransition = self.Entity:GetTransformComponent():GetLocalTranslation()

    if(self.Entity:HasFfsComponent()) then
        print ('---------------------Aircraft HasFfsComponent---------------------')
        self.controller = self.Entity:GetFfsComponent()
    else
        print ('---------------------Aircraft not HasFfsComponent---------------------')
    end

    --ce.Level:SetMainCamera(self.Entity)
    print ('---------------------PushBackAircraft--test--begin---------------------')

    local isFirst = true
    for line in io.lines(self.wgs84:GetLogFilePath() .. "/Contents/lua/FlightRoute/ZGSZ16-ZGGG02L_AROUND.csv") do
        -- print(line)
        if not isFirst then
            local rt = {}
            string.gsub(line, '[^' .. ',' .. ']+', function(w) table.insert(rt, w) end)
            self.data_table[#self.data_table + 1] = { tonumber(rt[self.heading_index]) * 1.0,
                tonumber(rt[self.pitch_index]) * 1.0, tonumber(rt[self.roll_index]) * -1.0,
                tonumber(rt[self.elevation_index]) * 1.0, tonumber(rt[self.lat_index]) * 1.0,
                tonumber(rt[self.long_index]) * 1.0 }
        else

            isFirst = false
        end

    end

    print ('---------------------PushBackAircraft--test--newdata1---------------------'.. #self.data_table)
    self.data_index=1



end

function PushBackAircraft:OnPreUpdate(elapsedTime)

end

function PushBackAircraft:UpdateWgs84Data(elapsedTime)
    if self.controller:IsTbusppEnabled() then
        return
    end

    if self.data_index > #self.data_table then
        self.data_index = 1
    end

    self.wgs84:SetHeading(self.data_table[self.data_index][1])
    self.wgs84:SetPitch(self.data_table[self.data_index][2])
    self.wgs84:SetRoll(self.data_table[self.data_index][3])
    self.wgs84:SetElevation(self.data_table[self.data_index][4])
    self.wgs84:OnChangeTransformByDD(self.data_table[self.data_index][5], self.data_table[self.data_index][6], true)

    -- if (ce.Input:IsPressed(CEButton.LeftControl)) then
    --     self.data_index = self.data_index + 50
    -- else
    --     self.data_index = self.data_index + 1
    -- end
end
MoveSpeed = 5
function PushBackAircraft:UpdateFrontWheel(elapsedTime)
    elapsedTime = math.min(elapsedTime, 0.05);
    local moveSpeed = elapsedTime * MoveSpeed
    --  aircraft front wheel offset
    self.frontWheelOffset = self.controller:GetControllableAircraftFrontWheelPosition()
    if ce.Input:IsPressed(CEButton.LeftShift) then
        -- self.frontWheelOffset.X = self.frontWheelOffset.X +
        --     BoolToNumber[ce.Input:IsPressed(CEButton.D)] * moveSpeed +
        --     BoolToNumber[ce.Input:IsPressed(CEButton.A)] * moveSpeed * -1.0
        self.frontWheelOffset.Y = self.frontWheelOffset.Y +
            BoolToNumber[ce.Input:IsPressed(CEButton.E)] * moveSpeed +
            BoolToNumber[ce.Input:IsPressed(CEButton.Q)] * moveSpeed * -1.0
        self.frontWheelOffset.Z = self.frontWheelOffset.Z +
            BoolToNumber[ce.Input:IsPressed(CEButton.W)] * moveSpeed +
            BoolToNumber[ce.Input:IsPressed(CEButton.S)] * moveSpeed * -1.0
    end

    -- print("frontWheelOffset: ", self.frontWheelOffset:ToString())
    self.controller:SetControllableAircraftFrontWheelPosition(self.frontWheelOffset)
end

function PushBackAircraft:OnUpdate(elapsedTime)
    self:UpdateWgs84Data(elapsedTime)
    self:UpdateFrontWheel(elapsedTime)
end

function PushBackAircraft:OnPostUpdate(elapsedTime)

end

function PushBackAircraft:GetMessage()
    return string.format("")
end
