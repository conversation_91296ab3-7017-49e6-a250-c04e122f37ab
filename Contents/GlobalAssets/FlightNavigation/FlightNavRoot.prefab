adnj
{
    "Guid": "cc0ce60810dc15bc8a44684d6a478d5b",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 27701,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "2836429a9a4717879948814ea86c76a8",
        "Contents/TypeScript/Tutorial/FlightNavigation.mts"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "f9ecc61b21d2baae364daff6cc999045"
        },
        "entities": {
            "f9ecc61b21d2baae364daff6cc999045": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "f9ecc61b21d2baae364daff6cc999045",
                "name": "FlightNavRoot",
                "prototype": 10827616371311494976,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "FlightNavRoot",
                            "ScriptPath": "Contents/TypeScript/Tutorial/FlightNavigation.mts",
                            "ScriptDefaultValue": "",
                            "ScriptEditorFields": "{\"Fields\":[]}",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "689efe0169779c83b64e24d5106f1d76",
                    "cbe586dc16cd9eaa644559eee3aec4cf",
                    "9a82f573a73833ab264da2fe13f0df34",
                    "758d0676597982bc5c4a779d7fd63e2b",
                    "0c80b0944f4719a77e408b9fe3201d86",
                    "bed0d17a38065f80a54a9a4db98c30f6",
                    "ac5af3ec63ca73823e4482bfa66d1439",
                    "d0e779d8958a96b86e4bf03ff5243515",
                    "94d95aa1fa2dad8ee346dbd1af940639",
                    "4620d4d00bc7ffa748449d76ea6fadbc",
                    "f69c1a5e2f90d686c74b6cad96a22115",
                    "cb9e6c034745a5b1a0411f9e0137e925",
                    "76bfb3c854f52cb27a4d1eca3b900d9e",
                    "a5df8b10844904886346238a08aeddc5",
                    "5679d1590fbd9fb4e4468ec46129e384",
                    "6eb07e856a2354a7ac4f7d7fb29a302b",
                    "dcc39a2d8e0c01977448b8b0cb519dbd",
                    "c705162f3f2f7880a84deb9a5ac4f641",
                    "62bd3f5c4132aaafe345bae425690f18",
                    "20f485aab3d29eb49447abd57db78f62",
                    "af194ee62bebafbeb747b7926d4acc37",
                    "b6c4110b24c45f81cc48eda18961496a",
                    "b575d033552ff583e4416506b24cc364",
                    "36b42111b7eb17b82d490d25a355848d",
                    "a99289053921718fd34c98809aa8d4ce",
                    "973fde397f492aae5e4ef03a6e3fcfc9",
                    "e6d344297ff0a180144c6b7d82cfc47e",
                    "1470bf32360fe79df54008a743ddab76",
                    "11a07e72591ae6a5344592a5c083fb06",
                    "0714dd4a105858bbb54cd48545e83399",
                    "2c94f01cb19675af134ebae5134a3547",
                    "1f09bcce04a65485fd4ba7d75d271dbb",
                    "8f3e73b1870400aa614dbfd50b94f7a9",
                    "d0216f5c184298a8d8416c9eece8b2a6",
                    "6018756534c94eb2c54c68f612702a7f",
                    "400bb4fcf89296944748c1b1b4220b21",
                    "bd03994cfae4aa837347f7320fa566bf",
                    "9ed094a9ce29728fbe47080a06f2813b",
                    "ae6e41fd672afa897144c71e45b5dffc",
                    "fe8413544b44e492d3480f74a6e30894"
                ]
            },
            "689efe0169779c83b64e24d5106f1d76": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "689efe0169779c83b64e24d5106f1d76",
                "children": []
            },
            "cbe586dc16cd9eaa644559eee3aec4cf": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "cbe586dc16cd9eaa644559eee3aec4cf",
                "name": "FlightNav1",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav1"
                        }
                    }
                },
                "children": []
            },
            "9a82f573a73833ab264da2fe13f0df34": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "9a82f573a73833ab264da2fe13f0df34",
                "name": "FlightNav2",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav2"
                        }
                    }
                },
                "children": []
            },
            "758d0676597982bc5c4a779d7fd63e2b": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "758d0676597982bc5c4a779d7fd63e2b",
                "name": "FlightNav3",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav3"
                        }
                    }
                },
                "children": []
            },
            "0c80b0944f4719a77e408b9fe3201d86": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "0c80b0944f4719a77e408b9fe3201d86",
                "name": "FlightNav4",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav4"
                        }
                    }
                },
                "children": []
            },
            "bed0d17a38065f80a54a9a4db98c30f6": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "bed0d17a38065f80a54a9a4db98c30f6",
                "name": "FlightNav5",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav5"
                        }
                    }
                },
                "children": []
            },
            "ac5af3ec63ca73823e4482bfa66d1439": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "ac5af3ec63ca73823e4482bfa66d1439",
                "name": "FlightNav6",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav6"
                        }
                    }
                },
                "children": []
            },
            "d0e779d8958a96b86e4bf03ff5243515": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "d0e779d8958a96b86e4bf03ff5243515",
                "name": "FlightNav7",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav7"
                        }
                    }
                },
                "children": []
            },
            "94d95aa1fa2dad8ee346dbd1af940639": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "94d95aa1fa2dad8ee346dbd1af940639",
                "name": "FlightNav8",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav8"
                        }
                    }
                },
                "children": []
            },
            "4620d4d00bc7ffa748449d76ea6fadbc": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "4620d4d00bc7ffa748449d76ea6fadbc",
                "name": "FlightNav9",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav9"
                        }
                    }
                },
                "children": []
            },
            "f69c1a5e2f90d686c74b6cad96a22115": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "f69c1a5e2f90d686c74b6cad96a22115",
                "name": "FlightNav10",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav10"
                        }
                    }
                },
                "children": []
            },
            "cb9e6c034745a5b1a0411f9e0137e925": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "cb9e6c034745a5b1a0411f9e0137e925",
                "name": "FlightNav11",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav11"
                        }
                    }
                },
                "children": []
            },
            "76bfb3c854f52cb27a4d1eca3b900d9e": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "76bfb3c854f52cb27a4d1eca3b900d9e",
                "name": "FlightNav12",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav12"
                        }
                    }
                },
                "children": []
            },
            "a5df8b10844904886346238a08aeddc5": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "a5df8b10844904886346238a08aeddc5",
                "name": "FlightNav13",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav13"
                        }
                    }
                },
                "children": []
            },
            "5679d1590fbd9fb4e4468ec46129e384": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "5679d1590fbd9fb4e4468ec46129e384",
                "name": "FlightNav14",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav14"
                        }
                    }
                },
                "children": []
            },
            "6eb07e856a2354a7ac4f7d7fb29a302b": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "6eb07e856a2354a7ac4f7d7fb29a302b",
                "name": "FlightNav15",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav15"
                        }
                    }
                },
                "children": []
            },
            "dcc39a2d8e0c01977448b8b0cb519dbd": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "dcc39a2d8e0c01977448b8b0cb519dbd",
                "name": "FlightNav16",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav16"
                        }
                    }
                },
                "children": []
            },
            "c705162f3f2f7880a84deb9a5ac4f641": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "c705162f3f2f7880a84deb9a5ac4f641",
                "name": "FlightNav17",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav17"
                        }
                    }
                },
                "children": []
            },
            "62bd3f5c4132aaafe345bae425690f18": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "62bd3f5c4132aaafe345bae425690f18",
                "name": "FlightNav18",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav18"
                        }
                    }
                },
                "children": []
            },
            "20f485aab3d29eb49447abd57db78f62": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "20f485aab3d29eb49447abd57db78f62",
                "name": "FlightNav19",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav19"
                        }
                    }
                },
                "children": []
            },
            "af194ee62bebafbeb747b7926d4acc37": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "af194ee62bebafbeb747b7926d4acc37",
                "name": "FlightNav20",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav20"
                        }
                    }
                },
                "children": []
            },
            "b6c4110b24c45f81cc48eda18961496a": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "b6c4110b24c45f81cc48eda18961496a",
                "name": "FlightNav21",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav21"
                        }
                    }
                },
                "children": []
            },
            "b575d033552ff583e4416506b24cc364": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "b575d033552ff583e4416506b24cc364",
                "name": "FlightNav22",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav22"
                        }
                    }
                },
                "children": []
            },
            "36b42111b7eb17b82d490d25a355848d": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "36b42111b7eb17b82d490d25a355848d",
                "name": "FlightNav23",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav23"
                        }
                    }
                },
                "children": []
            },
            "a99289053921718fd34c98809aa8d4ce": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "a99289053921718fd34c98809aa8d4ce",
                "name": "FlightNav24",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav24"
                        }
                    }
                },
                "children": []
            },
            "973fde397f492aae5e4ef03a6e3fcfc9": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "973fde397f492aae5e4ef03a6e3fcfc9",
                "name": "FlightNav25",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav25"
                        }
                    }
                },
                "children": []
            },
            "e6d344297ff0a180144c6b7d82cfc47e": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "e6d344297ff0a180144c6b7d82cfc47e",
                "name": "FlightNav26",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav26"
                        }
                    }
                },
                "children": []
            },
            "1470bf32360fe79df54008a743ddab76": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "1470bf32360fe79df54008a743ddab76",
                "name": "FlightNav27",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav27"
                        }
                    }
                },
                "children": []
            },
            "11a07e72591ae6a5344592a5c083fb06": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "11a07e72591ae6a5344592a5c083fb06",
                "name": "FlightNav28",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav28"
                        }
                    }
                },
                "children": []
            },
            "0714dd4a105858bbb54cd48545e83399": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "0714dd4a105858bbb54cd48545e83399",
                "name": "FlightNav29",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav29"
                        }
                    }
                },
                "children": []
            },
            "2c94f01cb19675af134ebae5134a3547": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "2c94f01cb19675af134ebae5134a3547",
                "name": "FlightNav30",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav30"
                        }
                    }
                },
                "children": []
            },
            "1f09bcce04a65485fd4ba7d75d271dbb": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "1f09bcce04a65485fd4ba7d75d271dbb",
                "name": "FlightNav31",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav31"
                        }
                    }
                },
                "children": []
            },
            "8f3e73b1870400aa614dbfd50b94f7a9": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "8f3e73b1870400aa614dbfd50b94f7a9",
                "name": "FlightNav32",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav32"
                        }
                    }
                },
                "children": []
            },
            "d0216f5c184298a8d8416c9eece8b2a6": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "d0216f5c184298a8d8416c9eece8b2a6",
                "name": "FlightNav33",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav33"
                        }
                    }
                },
                "children": []
            },
            "6018756534c94eb2c54c68f612702a7f": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "6018756534c94eb2c54c68f612702a7f",
                "name": "FlightNav34",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav34"
                        }
                    }
                },
                "children": []
            },
            "400bb4fcf89296944748c1b1b4220b21": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "400bb4fcf89296944748c1b1b4220b21",
                "name": "FlightNav35",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav35"
                        }
                    }
                },
                "children": []
            },
            "bd03994cfae4aa837347f7320fa566bf": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "bd03994cfae4aa837347f7320fa566bf",
                "name": "FlightNav36",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav36"
                        }
                    }
                },
                "children": []
            },
            "9ed094a9ce29728fbe47080a06f2813b": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "9ed094a9ce29728fbe47080a06f2813b",
                "name": "FlightNav37",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav37"
                        }
                    }
                },
                "children": []
            },
            "ae6e41fd672afa897144c71e45b5dffc": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "ae6e41fd672afa897144c71e45b5dffc",
                "name": "FlightNav38",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav38"
                        }
                    }
                },
                "children": []
            },
            "fe8413544b44e492d3480f74a6e30894": {
                "prefabId": "2836429a9a4717879948814ea86c76a8",
                "prefabEuid": "c6bef77b18772da5e84df327c2d61d5a",
                "euid": "fe8413544b44e492d3480f74a6e30894",
                "name": "FlightNav39",
                "components": {
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "FlightNav39"
                        }
                    }
                },
                "children": []
            }
        }
    }
}