adnj
{
    "Guid": "19c6075bdfe729bb47419cdc5a64d197",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 96805,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "60cded566dafa4687a0e2f5928ee4ea6",
        "81375b2984621416ea2ff11bdd29c155",
        "Contents/TypeScript/Tutorial/FlightCatmullPath.mts"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "d11b7290fe6b498e6d4171a19c292c12"
        },
        "entities": {
            "d11b7290fe6b498e6d4171a19c292c12": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "d11b7290fe6b498e6d4171a19c292c12",
                "name": "ZBADruisePathRoot",
                "prototype": 4034668323159335804,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "ZBADruisePathRoot",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "ebf94bbf493c4591ff49c880d2fc8813",
                    "d7ceea437ccb648f714c25f14bd44e40"
                ]
            },
            "ebf94bbf493c4591ff49c880d2fc8813": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "ebf94bbf493c4591ff49c880d2fc8813",
                "name": "19RRunwayPath",
                "prototype": 4034668323159335804,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -177253.937639028,
                                "y": 132.158546566963,
                                "z": -2055.42867565504
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "19RRunwayPath",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "6514672764b38593004ae9f3bce07928",
                    "2cc6b1b90d1c1aa9ce4db81cd26cfd6b",
                    "0232e7fbb5fb0ca3274c709746acdc82"
                ]
            },
            "6514672764b38593004ae9f3bce07928": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "6514672764b38593004ae9f3bce07928",
                "name": "CruisePath",
                "prototype": 4034668323159335804,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "CruisePath",
                            "ScriptPath": "Contents/TypeScript/Tutorial/FlightCatmullPath.mts",
                            "ScriptEditorFields": "{\"Fields\":[]}",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "mPosition": null,
                                    "mRotation": null,
                                    "ComponentType": "cegf::SplineComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "68c4a0c58f2727a68d462b0ca9e083f5",
                    "0db00c40174cab86d1404e68d3ee21af",
                    "65b0813d8010a5a4c043836aa47df9fc",
                    "2be7112ec653d08b29481817e31c2dd6"
                ]
            },
            "68c4a0c58f2727a68d462b0ca9e083f5": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "68c4a0c58f2727a68d462b0ca9e083f5",
                "name": "19Rcatmull1",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 853752.556547284,
                                "y": 91440.0,
                                "z": -2792525.7273324
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 20.0,
                                "y": 20.0,
                                "z": 20.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "19Rcatmull1",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "0db00c40174cab86d1404e68d3ee21af": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "0db00c40174cab86d1404e68d3ee21af",
                "name": "19Rcatmull2",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 2021777.31662172,
                                "y": 121920.0,
                                "z": -2650337.66516269
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 20.0,
                                "y": 20.0,
                                "z": 20.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "19Rcatmull2",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "65b0813d8010a5a4c043836aa47df9fc": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "65b0813d8010a5a4c043836aa47df9fc",
                "name": "19Rcatmull3",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 2359131.15424874,
                                "y": 121920.0,
                                "z": 2259856.2327864
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 20.0,
                                "y": 20.0,
                                "z": 20.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "19Rcatmull3",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "2be7112ec653d08b29481817e31c2dd6": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "2be7112ec653d08b29481817e31c2dd6",
                "name": "19Rcatmull4",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 449984.581386119,
                                "y": 91440.0,
                                "z": 2605095.01850764
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 20.0,
                                "y": 20.0,
                                "z": 20.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "19Rcatmull4",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "2cc6b1b90d1c1aa9ce4db81cd26cfd6b": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "2cc6b1b90d1c1aa9ce4db81cd26cfd6b",
                "name": "CruiseBegin",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 456473.958062261,
                                "y": 0.0,
                                "z": -327989.990429681
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "CruiseBegin",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "0232e7fbb5fb0ca3274c709746acdc82": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "0232e7fbb5fb0ca3274c709746acdc82",
                "name": "CruiseEnd",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 414305.134115219,
                                "y": 0.0,
                                "z": 17193.0029017362
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "CruiseEnd",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "d7ceea437ccb648f714c25f14bd44e40": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "d7ceea437ccb648f714c25f14bd44e40",
                "name": "35RRunwayPath",
                "prototype": 4034668323159335804,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -177253.937639028,
                                "y": 132.158546566963,
                                "z": -2055.42867565504
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "35RRunwayPath",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "1a8299fc1435f38285494dc41214f9de",
                    "ed07cefcf98a72ae3f4c48486bf49429",
                    "50cc8d3007b3a091d24795d8c91af76b"
                ]
            },
            "1a8299fc1435f38285494dc41214f9de": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "1a8299fc1435f38285494dc41214f9de",
                "name": "CruisePath",
                "prototype": 4034668323159335804,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "CruisePath",
                            "ScriptPath": "Contents/TypeScript/Tutorial/FlightCatmullPath.mts",
                            "ScriptEditorFields": "{\"Fields\":[]}",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "mPosition": null,
                                    "mRotation": null,
                                    "ComponentType": "cegf::SplineComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "793a85e89c2d50ba5844c89015806fc1",
                    "d8dcf6e44924268b3d469ea3e8dcb93a",
                    "7fc8a4c892debf8d414a5d15c91016bf",
                    "a9e0ad7ce7bd75b52d47f583c7b9d449"
                ]
            },
            "793a85e89c2d50ba5844c89015806fc1": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "793a85e89c2d50ba5844c89015806fc1",
                "name": "35Rcatmull1",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -36145.8569878936,
                                "y": 91440.0041677952,
                                "z": 987499.659066084
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 20.0,
                                "y": 20.0,
                                "z": 20.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "35Rcatmull1",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "d8dcf6e44924268b3d469ea3e8dcb93a": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "d8dcf6e44924268b3d469ea3e8dcb93a",
                "name": "35Rcatmull2",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -574574.703327566,
                                "y": 121920.0,
                                "z": 781032.409519341
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 20.0,
                                "y": 20.0,
                                "z": 20.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "35Rcatmull2",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "7fc8a4c892debf8d414a5d15c91016bf": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "7fc8a4c892debf8d414a5d15c91016bf",
                "name": "35Rcatmull3",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -516998.412642092,
                                "y": 121920.0,
                                "z": -1652701.16717356
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 20.0,
                                "y": 20.0,
                                "z": 20.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "35Rcatmull3",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "a9e0ad7ce7bd75b52d47f583c7b9d449": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "a9e0ad7ce7bd75b52d47f583c7b9d449",
                "name": "35Rcatmull4",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 322080.315093398,
                                "y": 91440.0,
                                "z": -1620700.74981474
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 20.0,
                                "y": 20.0,
                                "z": 20.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "35Rcatmull4",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "ed07cefcf98a72ae3f4c48486bf49429": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "ed07cefcf98a72ae3f4c48486bf49429",
                "name": "CruiseBegin",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 153064.150693208,
                                "y": 0.0,
                                "z": 194493.399494909
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "CruiseBegin",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "50cc8d3007b3a091d24795d8c91af76b": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "50cc8d3007b3a091d24795d8c91af76b",
                "name": "CruiseEnd",
                "prototype": 11480013911523908748,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 201711.46497938,
                                "y": 0.0,
                                "z": -192033.322319544
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "CruiseEnd",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            }
        }
    }
}