<NodeGraph
  Name = "718"
  NodeID = "10502"
  ConnectionID = "20406"
  HashName = "15451340781547894809">
  <Camera MaxZoom = "5" MinZoom = "-10" ZoomCount = "0">
    <Viewport
      _WorldX = "-540"
      _WorldY = "-379"
      _Height = "1183"
      _AspectRatio = "1.7582418"/>
    <Viewport
      _WorldX = "851"
      _WorldY = "80"
      _Height = "1183"
      _AspectRatio = "1.7582418"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "1460"
    Y = "280"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20223"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "EngineResource/718_CINEMA_4D____ANIM.nda"
    Loop = "true"
    ID = "10232"
    X = "410"
    Y = "110"
    NodeJsonData = ##{
  "CompositePath": "f184696f9888e44149ffeba22abe262e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20342"
  ],
  "Name": "10232",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "EngineResource/718_CINEMA_4D____ANIM.nda"
    Loop = "true"
    ID = "10259"
    X = "400"
    Y = "340"
    NodeJsonData = ##{
  "CompositePath": "f184696f9888e44149ffeba22abe262e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20378"
  ],
  "Name": "10259",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "EngineResource/718_CINEMA_4D____ANIM.nda"
    Loop = "true"
    ID = "10310"
    X = "400"
    Y = "470"
    NodeJsonData = ##{
  "CompositePath": "f184696f9888e44149ffeba22abe262e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20383"
  ],
  "Name": "10310",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_BlendByLayeredFilterNode
    ID = "10327"
    X = "770"
    Y = "400"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20244",
    "20211"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "rwing",
          "Depth": 2
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10327",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_SwitchPosesByIntNode
    NumPose = "4"
    ID = "10340"
    X = "1110"
    Y = "280"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20277",
    "20272",
    "20251",
    "20258"
  ],
  "InParamLinks": [
    "20328"
  ],
  "Name": "10340",
  "Type": "SwitchPosesByIntNode"
}##/>
  <Anim_BlendByLayeredFilterNode
    ID = "10355"
    X = "780"
    Y = "540"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20308",
    "20311"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "lwing",
          "Depth": 2
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10355",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "EngineResource/718_CINEMA_4D____ANIM.nda"
    Loop = "true"
    ID = "10364"
    X = "400"
    Y = "220"
    NodeJsonData = ##{
  "CompositePath": "f184696f9888e44149ffeba22abe262e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20359"
  ],
  "Name": "10364",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "EngineResource/718_CINEMA_4D____ANIM.nda"
    Loop = "true"
    ID = "10382"
    X = "400"
    Y = "540"
    NodeJsonData = ##{
  "CompositePath": "f184696f9888e44149ffeba22abe262e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20379"
  ],
  "Name": "10382",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "EngineResource/718_CINEMA_4D____ANIM.nda"
    Loop = "true"
    ID = "10399"
    X = "400"
    Y = "650"
    NodeJsonData = ##{
  "CompositePath": "f184696f9888e44149ffeba22abe262e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20392"
  ],
  "Name": "10399",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_ParamImplNode
    ParamName = "modeindex"
    ParamType = "Int"
    ID = "10445"
    X = "770"
    Y = "-30"
    NodeJsonData = ##{
  "InParams": [
    "modeindex"
  ],
  "ReturnType": "Int",
  "Name": "10445",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "slow"
    ParamType = "Float"
    ID = "10458"
    X = "180"
    Y = "110"
    NodeJsonData = ##{
  "InParams": [
    "slow"
  ],
  "ReturnType": "Float",
  "Name": "10458",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "fast"
    ParamType = "Float"
    ID = "10468"
    X = "180"
    Y = "220"
    NodeJsonData = ##{
  "InParams": [
    "fast"
  ],
  "ReturnType": "Float",
  "Name": "10468",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "base"
    ParamType = "Float"
    ID = "10473"
    X = "180"
    Y = "350"
    NodeJsonData = ##{
  "InParams": [
    "base"
  ],
  "ReturnType": "Float",
  "Name": "10473",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "base"
    ParamType = "Float"
    ID = "10477"
    X = "170"
    Y = "540"
    NodeJsonData = ##{
  "InParams": [
    "base"
  ],
  "ReturnType": "Float",
  "Name": "10477",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "turn"
    ParamType = "Float"
    ID = "10483"
    X = "180"
    Y = "460"
    NodeJsonData = ##{
  "InParams": [
    "turn"
  ],
  "ReturnType": "Float",
  "Name": "10483",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "turn"
    ParamType = "Float"
    ID = "10487"
    X = "200"
    Y = "640"
    NodeJsonData = ##{
  "InParams": [
    "turn"
  ],
  "ReturnType": "Float",
  "Name": "10487",
  "Type": "ParamImplNode"
}##/>
  <Connection
    ID = "20211"
    OutSlotNodeID = "10310"
    OutSlotIndex = "0"
    InSlotNodeID = "10327"
    InSlotIndex = "1"/>
  <Connection
    ID = "20223"
    OutSlotNodeID = "10340"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
  <Connection
    ID = "20244"
    OutSlotNodeID = "10259"
    OutSlotIndex = "0"
    InSlotNodeID = "10327"
    InSlotIndex = "0"/>
  <Connection
    ID = "20251"
    OutSlotNodeID = "10327"
    OutSlotIndex = "0"
    InSlotNodeID = "10340"
    InSlotIndex = "3"/>
  <Connection
    ID = "20258"
    OutSlotNodeID = "10355"
    OutSlotIndex = "0"
    InSlotNodeID = "10340"
    InSlotIndex = "4"/>
  <Connection
    ID = "20272"
    OutSlotNodeID = "10364"
    OutSlotIndex = "0"
    InSlotNodeID = "10340"
    InSlotIndex = "2"/>
  <Connection
    ID = "20277"
    OutSlotNodeID = "10232"
    OutSlotIndex = "0"
    InSlotNodeID = "10340"
    InSlotIndex = "1"/>
  <Connection
    ID = "20308"
    OutSlotNodeID = "10382"
    OutSlotIndex = "0"
    InSlotNodeID = "10355"
    InSlotIndex = "0"/>
  <Connection
    ID = "20311"
    OutSlotNodeID = "10399"
    OutSlotIndex = "0"
    InSlotNodeID = "10355"
    InSlotIndex = "1"/>
  <Connection
    ID = "20328"
    OutSlotNodeID = "10445"
    OutSlotIndex = "0"
    InSlotNodeID = "10340"
    InSlotIndex = "0"/>
  <Connection
    ID = "20342"
    OutSlotNodeID = "10458"
    OutSlotIndex = "0"
    InSlotNodeID = "10232"
    InSlotIndex = "0"/>
  <Connection
    ID = "20359"
    OutSlotNodeID = "10468"
    OutSlotIndex = "0"
    InSlotNodeID = "10364"
    InSlotIndex = "0"/>
  <Connection
    ID = "20378"
    OutSlotNodeID = "10473"
    OutSlotIndex = "0"
    InSlotNodeID = "10259"
    InSlotIndex = "0"/>
  <Connection
    ID = "20379"
    OutSlotNodeID = "10477"
    OutSlotIndex = "0"
    InSlotNodeID = "10382"
    InSlotIndex = "0"/>
  <Connection
    ID = "20383"
    OutSlotNodeID = "10483"
    OutSlotIndex = "0"
    InSlotNodeID = "10310"
    InSlotIndex = "0"/>
  <Connection
    ID = "20392"
    OutSlotNodeID = "10487"
    OutSlotIndex = "0"
    InSlotNodeID = "10399"
    InSlotIndex = "0"/>
</NodeGraph>
<StbProperty ApplyMode = "0" ExtractionMode = "1">
  <Param ParamType = "CrossEditor.AnimParameter_Int" ParamJson = ##{
  "Value": 0,
  "Type": "Int",
  "Name": "modeindex"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 1.0,
  "Type": "Float",
  "Name": "slow"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 5.0,
  "Type": "Float",
  "Name": "fast"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 3.0,
  "Type": "Float",
  "Name": "turn"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 2.0,
  "Type": "Float",
  "Name": "base"
}##/>
</StbProperty>
