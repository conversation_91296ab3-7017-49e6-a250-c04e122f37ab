<NodeGraph
  Name = "birdv2"
  NodeID = "10113"
  ConnectionID = "20130"
  HashName = "1163008483900329956">
  <Camera MaxZoom = "5" MinZoom = "-10" ZoomCount = "0">
    <Viewport
      _WorldX = "0"
      _WorldY = "0"
      _Height = "1682.9999"
      _AspectRatio = "1.5216875"/>
    <Viewport
      _WorldX = "534"
      _WorldY = "80"
      _Height = "1683"
      _AspectRatio = "1.5216875"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "1410"
    Y = "560"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20115"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/BirdTest/seagulls/seagulls_02_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10003"
    X = "690"
    Y = "570"
    NodeJsonData = ##{
  "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20000"
  ],
  "Name": "10003",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_ParamImplNode
    ParamName = "modeindex"
    ParamType = "Int"
    ID = "10016"
    X = "700"
    Y = "440"
    NodeJsonData = ##{
  "InParams": [
    "modeindex"
  ],
  "ReturnType": "Int",
  "Name": "10016",
  "Type": "ParamImplNode"
}##/>
  <Anim_SwitchPosesByIntNode
    NumPose = "5"
    ID = "10024"
    X = "1050"
    Y = "560"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20006",
    "20023",
    "20048",
    "20067",
    "20095"
  ],
  "InParamLinks": [
    "20109"
  ],
  "Name": "10024",
  "Type": "SwitchPosesByIntNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "a"
    ParamType = "Float"
    ID = "10039"
    X = "510"
    Y = "570"
    NodeJsonData = ##{
  "InParams": [
    "a"
  ],
  "ReturnType": "Float",
  "Name": "10039",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/BirdTest/seagulls/seagulls_02_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10051"
    X = "690"
    Y = "630"
    NodeJsonData = ##{
  "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20041"
  ],
  "Name": "10051",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_ParamImplNode
    ParamName = "b"
    ParamType = "Float"
    ID = "10062"
    X = "510"
    Y = "640"
    NodeJsonData = ##{
  "InParams": [
    "b"
  ],
  "ReturnType": "Float",
  "Name": "10062",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/BirdTest/seagulls/seagulls_02_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10065"
    X = "690"
    Y = "700"
    NodeJsonData = ##{
  "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20061"
  ],
  "Name": "10065",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_ParamImplNode
    ParamName = "c"
    ParamType = "Float"
    ID = "10068"
    X = "510"
    Y = "720"
    NodeJsonData = ##{
  "InParams": [
    "c"
  ],
  "ReturnType": "Float",
  "Name": "10068",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/BirdTest/seagulls/seagulls_02_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10070"
    X = "690"
    Y = "780"
    NodeJsonData = ##{
  "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20076"
  ],
  "Name": "10070",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_ParamImplNode
    ParamName = "d"
    ParamType = "Float"
    ID = "10075"
    X = "510"
    Y = "800"
    NodeJsonData = ##{
  "InParams": [
    "d"
  ],
  "ReturnType": "Float",
  "Name": "10075",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/BirdTest/seagulls/seagulls_02_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10094"
    X = "680"
    Y = "860"
    NodeJsonData = ##{
  "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20102"
  ],
  "Name": "10094",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_ParamImplNode
    ParamName = "e"
    ParamType = "Float"
    ID = "10099"
    X = "510"
    Y = "890"
    NodeJsonData = ##{
  "InParams": [
    "e"
  ],
  "ReturnType": "Float",
  "Name": "10099",
  "Type": "ParamImplNode"
}##/>
  <Connection
    ID = "20000"
    OutSlotNodeID = "10039"
    OutSlotIndex = "0"
    InSlotNodeID = "10003"
    InSlotIndex = "0"/>
  <Connection
    ID = "20006"
    OutSlotNodeID = "10003"
    OutSlotIndex = "0"
    InSlotNodeID = "10024"
    InSlotIndex = "1"/>
  <Connection
    ID = "20023"
    OutSlotNodeID = "10051"
    OutSlotIndex = "0"
    InSlotNodeID = "10024"
    InSlotIndex = "2"/>
  <Connection
    ID = "20041"
    OutSlotNodeID = "10062"
    OutSlotIndex = "0"
    InSlotNodeID = "10051"
    InSlotIndex = "0"/>
  <Connection
    ID = "20048"
    OutSlotNodeID = "10065"
    OutSlotIndex = "0"
    InSlotNodeID = "10024"
    InSlotIndex = "3"/>
  <Connection
    ID = "20061"
    OutSlotNodeID = "10068"
    OutSlotIndex = "0"
    InSlotNodeID = "10065"
    InSlotIndex = "0"/>
  <Connection
    ID = "20067"
    OutSlotNodeID = "10070"
    OutSlotIndex = "0"
    InSlotNodeID = "10024"
    InSlotIndex = "4"/>
  <Connection
    ID = "20076"
    OutSlotNodeID = "10075"
    OutSlotIndex = "0"
    InSlotNodeID = "10070"
    InSlotIndex = "0"/>
  <Connection
    ID = "20095"
    OutSlotNodeID = "10094"
    OutSlotIndex = "0"
    InSlotNodeID = "10024"
    InSlotIndex = "5"/>
  <Connection
    ID = "20102"
    OutSlotNodeID = "10099"
    OutSlotIndex = "0"
    InSlotNodeID = "10094"
    InSlotIndex = "0"/>
  <Connection
    ID = "20109"
    OutSlotNodeID = "10016"
    OutSlotIndex = "0"
    InSlotNodeID = "10024"
    InSlotIndex = "0"/>
  <Connection
    ID = "20115"
    OutSlotNodeID = "10024"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
</NodeGraph>
<StbProperty ApplyMode = "0" ExtractionMode = "1">
  <Param ParamType = "CrossEditor.AnimParameter_Int" ParamJson = ##{
  "Value": 0,
  "Type": "Int",
  "Name": "modeindex"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 4.0,
  "Type": "Float",
  "Name": "a"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 5.0,
  "Type": "Float",
  "Name": "b"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 6.0,
  "Type": "Float",
  "Name": "c"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 7.0,
  "Type": "Float",
  "Name": "d"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 8.0,
  "Type": "Float",
  "Name": "e"
}##/>
</StbProperty>
