adnj
{
    "Guid": "5d4bd9706137b4af391deb5288032eec",
    "Version": 5,
    "ClassID": 36,
    "DataSize": 7400,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "73fe1c2151f3e4d90991de95b1d37fc7"
    ]
}
{
    "StoryBoard": {
        "RootMotionMode": {
            "ExtractMode": "ExtractFromEverything",
            "ApplyMode": "Apply"
        },
        "Parameters": [
            {
                "Value": 0,
                "Type": "Int",
                "Name": "modeindex"
            },
            {
                "Value": 4.0,
                "Type": "Float",
                "Name": "a"
            },
            {
                "Value": 5.0,
                "Type": "Float",
                "Name": "b"
            },
            {
                "Value": 6.0,
                "Type": "Float",
                "Name": "c"
            },
            {
                "Value": 7.0,
                "Type": "Float",
                "Name": "d"
            },
            {
                "Value": 8.0,
                "Type": "Float",
                "Name": "e"
            }
        ],
        "Name": "1163008483900329956",
        "Nodes": [
            {
                "InPoseLinks": [
                    "20115"
                ],
                "Name": "10000",
                "Type": "RootNode"
            },
            {
                "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20000"
                ],
                "Name": "10003",
                "Type": "PlayCompositeNode"
            },
            {
                "InParams": [
                    "modeindex"
                ],
                "ReturnType": "Int",
                "Name": "10016",
                "Type": "ParamImplNode"
            },
            {
                "InPoseLinks": [
                    "20006",
                    "20023",
                    "20048",
                    "20067",
                    "20095"
                ],
                "InParamLinks": [
                    "20109"
                ],
                "Name": "10024",
                "Type": "SwitchPosesByIntNode"
            },
            {
                "InParams": [
                    "a"
                ],
                "ReturnType": "Float",
                "Name": "10039",
                "Type": "ParamImplNode"
            },
            {
                "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20041"
                ],
                "Name": "10051",
                "Type": "PlayCompositeNode"
            },
            {
                "InParams": [
                    "b"
                ],
                "ReturnType": "Float",
                "Name": "10062",
                "Type": "ParamImplNode"
            },
            {
                "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20061"
                ],
                "Name": "10065",
                "Type": "PlayCompositeNode"
            },
            {
                "InParams": [
                    "c"
                ],
                "ReturnType": "Float",
                "Name": "10068",
                "Type": "ParamImplNode"
            },
            {
                "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20076"
                ],
                "Name": "10070",
                "Type": "PlayCompositeNode"
            },
            {
                "InParams": [
                    "d"
                ],
                "ReturnType": "Float",
                "Name": "10075",
                "Type": "ParamImplNode"
            },
            {
                "CompositePath": "73fe1c2151f3e4d90991de95b1d37fc7",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20102"
                ],
                "Name": "10094",
                "Type": "PlayCompositeNode"
            },
            {
                "InParams": [
                    "e"
                ],
                "ReturnType": "Float",
                "Name": "10099",
                "Type": "ParamImplNode"
            }
        ],
        "Links": [
            {
                "Name": "20000",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10039",
                "SourceNode": "10003"
            },
            {
                "Name": "20006",
                "Type": "LocalPoseLink",
                "TargetNode": "10003",
                "SourceNode": "10024"
            },
            {
                "Name": "20023",
                "Type": "LocalPoseLink",
                "TargetNode": "10051",
                "SourceNode": "10024"
            },
            {
                "Name": "20041",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10062",
                "SourceNode": "10051"
            },
            {
                "Name": "20048",
                "Type": "LocalPoseLink",
                "TargetNode": "10065",
                "SourceNode": "10024"
            },
            {
                "Name": "20061",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10068",
                "SourceNode": "10065"
            },
            {
                "Name": "20067",
                "Type": "LocalPoseLink",
                "TargetNode": "10070",
                "SourceNode": "10024"
            },
            {
                "Name": "20076",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10075",
                "SourceNode": "10070"
            },
            {
                "Name": "20095",
                "Type": "LocalPoseLink",
                "TargetNode": "10094",
                "SourceNode": "10024"
            },
            {
                "Name": "20102",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10099",
                "SourceNode": "10094"
            },
            {
                "Name": "20109",
                "Type": "ParamImplLink<int>",
                "TargetNode": "10016",
                "SourceNode": "10024"
            },
            {
                "Name": "20115",
                "Type": "LocalPoseLink",
                "TargetNode": "10024",
                "SourceNode": "10000"
            }
        ]
    }
}