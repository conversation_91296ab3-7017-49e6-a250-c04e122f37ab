adnj
{
    "Guid": "c01c33970786c46c4823b404c1b1d3a7",
    "Version": 5,
    "ClassID": 36,
    "DataSize": 9833,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "f184696f9888e44149ffeba22abe262e"
    ]
}
{
    "StoryBoard": {
        "RootMotionMode": {
            "ExtractMode": "ExtractFromEverything",
            "ApplyMode": "Apply"
        },
        "Parameters": [
            {
                "Value": 0,
                "Type": "Int",
                "Name": "modeindex"
            },
            {
                "Value": 1.0,
                "Type": "Float",
                "Name": "slow"
            },
            {
                "Value": 5.0,
                "Type": "Float",
                "Name": "fast"
            },
            {
                "Value": 3.0,
                "Type": "Float",
                "Name": "turn"
            },
            {
                "Value": 2.0,
                "Type": "Float",
                "Name": "base"
            }
        ],
        "Name": "15451340781547894809",
        "Nodes": [
            {
                "InPoseLinks": [
                    "20223"
                ],
                "Name": "10000",
                "Type": "RootNode"
            },
            {
                "CompositePath": "f184696f9888e44149ffeba22abe262e",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20342"
                ],
                "Name": "10232",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "f184696f9888e44149ffeba22abe262e",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20378"
                ],
                "Name": "10259",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "f184696f9888e44149ffeba22abe262e",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20383"
                ],
                "Name": "10310",
                "Type": "PlayCompositeNode"
            },
            {
                "InPoseLinks": [
                    "20244",
                    "20211"
                ],
                "Layers": [
                    {
                        "Filters": [
                            {
                                "BoneName": "rwing",
                                "Depth": 2
                            }
                        ],
                        "Weight": 1.0
                    }
                ],
                "Name": "10327",
                "Type": "BlendByLayeredFilterNode"
            },
            {
                "InPoseLinks": [
                    "20277",
                    "20272",
                    "20251",
                    "20258"
                ],
                "InParamLinks": [
                    "20328"
                ],
                "Name": "10340",
                "Type": "SwitchPosesByIntNode"
            },
            {
                "InPoseLinks": [
                    "20308",
                    "20311"
                ],
                "Layers": [
                    {
                        "Filters": [
                            {
                                "BoneName": "lwing",
                                "Depth": 2
                            }
                        ],
                        "Weight": 1.0
                    }
                ],
                "Name": "10355",
                "Type": "BlendByLayeredFilterNode"
            },
            {
                "CompositePath": "f184696f9888e44149ffeba22abe262e",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20359"
                ],
                "Name": "10364",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "f184696f9888e44149ffeba22abe262e",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20379"
                ],
                "Name": "10382",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "f184696f9888e44149ffeba22abe262e",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20392"
                ],
                "Name": "10399",
                "Type": "PlayCompositeNode"
            },
            {
                "InParams": [
                    "modeindex"
                ],
                "ReturnType": "Int",
                "Name": "10445",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "slow"
                ],
                "ReturnType": "Float",
                "Name": "10458",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "fast"
                ],
                "ReturnType": "Float",
                "Name": "10468",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "base"
                ],
                "ReturnType": "Float",
                "Name": "10473",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "base"
                ],
                "ReturnType": "Float",
                "Name": "10477",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "turn"
                ],
                "ReturnType": "Float",
                "Name": "10483",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "turn"
                ],
                "ReturnType": "Float",
                "Name": "10487",
                "Type": "ParamImplNode"
            }
        ],
        "Links": [
            {
                "Name": "20211",
                "Type": "LocalPoseLink",
                "TargetNode": "10310",
                "SourceNode": "10327"
            },
            {
                "Name": "20223",
                "Type": "LocalPoseLink",
                "TargetNode": "10340",
                "SourceNode": "10000"
            },
            {
                "Name": "20244",
                "Type": "LocalPoseLink",
                "TargetNode": "10259",
                "SourceNode": "10327"
            },
            {
                "Name": "20251",
                "Type": "LocalPoseLink",
                "TargetNode": "10327",
                "SourceNode": "10340"
            },
            {
                "Name": "20258",
                "Type": "LocalPoseLink",
                "TargetNode": "10355",
                "SourceNode": "10340"
            },
            {
                "Name": "20272",
                "Type": "LocalPoseLink",
                "TargetNode": "10364",
                "SourceNode": "10340"
            },
            {
                "Name": "20277",
                "Type": "LocalPoseLink",
                "TargetNode": "10232",
                "SourceNode": "10340"
            },
            {
                "Name": "20308",
                "Type": "LocalPoseLink",
                "TargetNode": "10382",
                "SourceNode": "10355"
            },
            {
                "Name": "20311",
                "Type": "LocalPoseLink",
                "TargetNode": "10399",
                "SourceNode": "10355"
            },
            {
                "Name": "20328",
                "Type": "ParamImplLink<int>",
                "TargetNode": "10445",
                "SourceNode": "10340"
            },
            {
                "Name": "20342",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10458",
                "SourceNode": "10232"
            },
            {
                "Name": "20359",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10468",
                "SourceNode": "10364"
            },
            {
                "Name": "20378",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10473",
                "SourceNode": "10259"
            },
            {
                "Name": "20379",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10477",
                "SourceNode": "10382"
            },
            {
                "Name": "20383",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10483",
                "SourceNode": "10310"
            },
            {
                "Name": "20392",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10487",
                "SourceNode": "10399"
            }
        ]
    }
}