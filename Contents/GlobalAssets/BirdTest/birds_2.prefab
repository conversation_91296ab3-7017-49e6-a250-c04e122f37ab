adnj
{
    "Guid": "c3886516698644ee8a084a974fa36448",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 1574,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "ef887a40459dd49b79c7af574ee9e9da"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "60d5afc3219474eec866203f32e5845b"
        },
        "entities": {
            "60d5afc3219474eec866203f32e5845b": {
                "prefabId": "ef887a40459dd49b79c7af574ee9e9da",
                "prefabEuid": "173e7f632de98470db9f54d9a68305fb",
                "euid": "60d5afc3219474eec866203f32e5845b",
                "components": {
                    "cross::ControllableUnitComponentG": {
                        "Controller": "{\n    \"Entity\": \"60d5afc3219474eec866203f32e5845b\",\n    \"RotationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 1.0\n    },\n    \"TranslationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"SpawnSpot\": \"\",\n    \"InsideWorldBounds\": {\n        \"position\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        },\n        \"rotate\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0,\n            \"w\": 1.0\n        },\n        \"halfExtents\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        }\n    },\n    \"CollisionType\": 0,\n    \"BlockMask\": 65535,\n    \"bUseControllerRotationPitch\": false,\n    \"bUseControllerRotationYaw\": false,\n    \"bUseControllerRotationRoll\": false,\n    \"bUseControllerTranslate\": false,\n    \"mBirdMode\": 0,\n    \"BirdMode\": \"1\"\n}"
                    }
                },
                "children": []
            }
        }
    }
}