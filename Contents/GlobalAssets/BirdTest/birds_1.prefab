adnj
{
    "Guid": "3b9e2233985784d93b382718d4f51a4e",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 1574,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "ef887a40459dd49b79c7af574ee9e9da"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "42d85cceb084f4818a59f699621d9901"
        },
        "entities": {
            "42d85cceb084f4818a59f699621d9901": {
                "prefabId": "ef887a40459dd49b79c7af574ee9e9da",
                "prefabEuid": "bd6ff3a6c765f4ef382d9f02a09e03b2",
                "euid": "42d85cceb084f4818a59f699621d9901",
                "components": {
                    "cross::ControllableUnitComponentG": {
                        "Controller": "{\n    \"Entity\": \"42d85cceb084f4818a59f699621d9901\",\n    \"RotationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 1.0\n    },\n    \"TranslationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"SpawnSpot\": \"\",\n    \"InsideWorldBounds\": {\n        \"position\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        },\n        \"rotate\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0,\n            \"w\": 1.0\n        },\n        \"halfExtents\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        }\n    },\n    \"CollisionType\": 0,\n    \"BlockMask\": 65535,\n    \"bUseControllerRotationPitch\": false,\n    \"bUseControllerRotationYaw\": false,\n    \"bUseControllerRotationRoll\": false,\n    \"bUseControllerTranslate\": false,\n    \"mBirdMode\": 0,\n    \"BirdMode\": \"1\"\n}"
                    }
                },
                "children": []
            }
        }
    }
}