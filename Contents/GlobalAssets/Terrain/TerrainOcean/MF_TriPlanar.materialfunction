adnj
{
    "Guid": "57d54c435852ca8f9c4189bb3e1d9a81",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 28836,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 2616,
            "m_EditorPositionY": 547,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 33,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": 312,
            "m_EditorPositionY": 452,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Texture",
            "m_InputType": 5,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": 34,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -849,
            "m_EditorPositionY": -484,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "NormalWS",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -600,
            "m_EditorPositionY": -348,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 3,
            "m_InputName": "MaskRange",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -394,
            "m_EditorPositionY": 596,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 4,
            "m_InputName": "PosWS",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -10,
            "m_EditorPositionY": 324,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 5,
            "m_InputName": "UVTiling",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -618,
            "m_EditorPositionY": -484,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAbs",
            "m_Input": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -357,
            "m_EditorPositionY": -476,
            "m_Description": "",
            "Class": "cross::MaterialExpressionPower",
            "m_Base": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Exponent": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": 543,
            "m_EditorPositionY": -484,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 14,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": -122,
            "m_EditorPositionY": -308,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": false,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": -158,
            "m_EditorPositionY": -224,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 12,
            "m_EditorPositionX": -186,
            "m_EditorPositionY": -116,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": false,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 13,
            "m_EditorPositionX": 111,
            "m_EditorPositionY": -244,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 11,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 14,
            "m_EditorPositionX": 328,
            "m_EditorPositionY": -148,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 13,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 12,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 15,
            "m_EditorPositionX": -864,
            "m_EditorPositionY": -544,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "NormalMask",
            "m_Color": {
                "x": 1.0,
                "y": 1.0,
                "z": 1.0,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": true,
            "m_Width": 1558,
            "m_Height": 492
        },
        {
            "m_Id": 16,
            "m_EditorPositionX": -82,
            "m_EditorPositionY": 164,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": false,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 17,
            "m_EditorPositionX": 360,
            "m_EditorPositionY": 276,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 35,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 18,
            "m_EditorPositionX": 1080,
            "m_EditorPositionY": 356,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureSample",
            "m_UV": {
                "LinkedExpressionId": 48,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Tex": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Level": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Bias": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DDX": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DDY": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_SamplerState": {
                "MipValueMode": 0,
                "Filter": 3,
                "AnisotropicLevel": 8,
                "AddressMode": 0
            }
        },
        {
            "m_Id": 19,
            "m_EditorPositionX": 1187,
            "m_EditorPositionY": 724,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureSample",
            "m_UV": {
                "LinkedExpressionId": 50,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Tex": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Level": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Bias": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DDX": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DDY": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_SamplerState": {
                "MipValueMode": 0,
                "Filter": 3,
                "AnisotropicLevel": 8,
                "AddressMode": 0
            }
        },
        {
            "m_Id": 20,
            "m_EditorPositionX": -104,
            "m_EditorPositionY": 596,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 21,
            "m_EditorPositionX": 338,
            "m_EditorPositionY": 596,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 36,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 22,
            "m_EditorPositionX": 1299,
            "m_EditorPositionY": 988,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureSample",
            "m_UV": {
                "LinkedExpressionId": 52,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Tex": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Level": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_Bias": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DDX": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_DDY": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_SamplerState": {
                "MipValueMode": 0,
                "Filter": 3,
                "AnisotropicLevel": 8,
                "AddressMode": 0
            }
        },
        {
            "m_Id": 23,
            "m_EditorPositionX": -177,
            "m_EditorPositionY": 868,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": true,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 24,
            "m_EditorPositionX": 289,
            "m_EditorPositionY": 820,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 37,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 25,
            "m_EditorPositionX": 1551,
            "m_EditorPositionY": 196,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 26,
            "m_EditorPositionX": 1777,
            "m_EditorPositionY": 404,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 25,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 18,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 27,
            "m_EditorPositionX": 1832,
            "m_EditorPositionY": 692,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 29,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 19,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 28,
            "m_EditorPositionX": 1992,
            "m_EditorPositionY": 972,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 30,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 22,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 29,
            "m_EditorPositionX": 1624,
            "m_EditorPositionY": 548,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": false,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 30,
            "m_EditorPositionX": 1743,
            "m_EditorPositionY": 884,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": false,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 31,
            "m_EditorPositionX": -345,
            "m_EditorPositionY": 112,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComment",
            "m_CommentTitle": "TriPlanerColor",
            "m_Color": {
                "x": 1.0,
                "y": 1.0,
                "z": 1.0,
                "w": 1.0
            },
            "mShowBubbleWhenZoomed": false,
            "m_Width": 2848,
            "m_Height": 1020
        },
        {
            "m_Id": 32,
            "m_EditorPositionX": 2034,
            "m_EditorPositionY": 556,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 26,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 27,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 33,
            "m_EditorPositionX": 2226,
            "m_EditorPositionY": 748,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 32,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 28,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 34,
            "m_EditorPositionX": -97,
            "m_EditorPositionY": 436,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTextureObject",
            "m_TextureObjectName": "TextureObject26809",
            "m_TextureString": "9b31980422ebe596fc4fddee7d13b17c"
        },
        {
            "m_Id": 35,
            "m_EditorPositionX": 126,
            "m_EditorPositionY": 164,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "LinkedExpressionId": 16,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 65536.0
        },
        {
            "m_Id": 36,
            "m_EditorPositionX": 14,
            "m_EditorPositionY": 708,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "LinkedExpressionId": 20,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 65536.0
        },
        {
            "m_Id": 37,
            "m_EditorPositionX": 56,
            "m_EditorPositionY": 988,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "LinkedExpressionId": 23,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 65536.0
        },
        {
            "m_Id": 48,
            "m_EditorPositionX": 657,
            "m_EditorPositionY": 228,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 56,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 17,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 50,
            "m_EditorPositionX": 984,
            "m_EditorPositionY": 596,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 56,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 21,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 52,
            "m_EditorPositionX": 872,
            "m_EditorPositionY": 972,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 56,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 24,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 56,
            "m_EditorPositionX": 701,
            "m_EditorPositionY": 500,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "be3c00d46832409ad14c3506325ac18b",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 57,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": 58,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 57,
            "m_EditorPositionX": 472,
            "m_EditorPositionY": 676,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 8,
            "m_InputName": "SpeedX",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 58,
            "m_EditorPositionX": 518,
            "m_EditorPositionY": 788,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 8,
            "m_InputName": "SpeedY",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}