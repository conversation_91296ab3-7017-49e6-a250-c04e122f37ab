adnj
{
    "Guid": "be3c00d46832409ad14c3506325ac18b",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 4059,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 100,
            "m_EditorPositionY": 0,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -728,
            "m_EditorPositionY": 36,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "SpeedX",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -715,
            "m_EditorPositionY": 203,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "SpeedY",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -383,
            "m_EditorPositionY": -60,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -613,
            "m_EditorPositionY": -108,
            "m_Description": "",
            "Class": "cross::MaterialExpressionTime"
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -351,
            "m_EditorPositionY": 140,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -110,
            "m_EditorPositionY": 4,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAppendVector",
            "m_A": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}