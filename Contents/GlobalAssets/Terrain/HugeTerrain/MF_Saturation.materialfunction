adnj
{
    "Guid": "793ae36ff11206bba7437c300936c256",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 7357,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 1496,
            "m_EditorPositionY": 115,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "OutColor",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 12,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -344,
            "m_EditorPositionY": -92,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Color",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": 582,
            "m_EditorPositionY": 308,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "Sat",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -97,
            "m_EditorPositionY": -156,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": false,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -104,
            "m_EditorPositionY": -28,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -113,
            "m_EditorPositionY": 84,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": false,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": 145,
            "m_EditorPositionY": -108,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": 305,
            "m_EditorPositionY": 60,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": 529,
            "m_EditorPositionY": 156,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 3.0
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": 737,
            "m_EditorPositionY": -4,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": 907,
            "m_EditorPositionY": 197,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 12,
            "m_EditorPositionX": 1169,
            "m_EditorPositionY": 196,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 11,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}