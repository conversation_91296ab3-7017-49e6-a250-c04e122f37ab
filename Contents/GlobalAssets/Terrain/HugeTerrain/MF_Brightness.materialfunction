adnj
{
    "Guid": "e22cfa3f34d24585c6425acc31d8bbf3",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 6397,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 100,
            "m_EditorPositionY": 0,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -1016,
            "m_EditorPositionY": 404,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Color",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -1128,
            "m_EditorPositionY": 68,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "Brightness",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -186,
            "m_EditorPositionY": 156,
            "m_Description": "",
            "Class": "cross::MaterialExpressionIf",
            "m_A": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_AGreaterThanB": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_AEqualsB": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ALessThanB": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstB": 0.0
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -575,
            "m_EditorPositionY": 276,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -760,
            "m_EditorPositionY": 236,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 1.0
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": -527,
            "m_EditorPositionY": 484,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": -808,
            "m_EditorPositionY": 572,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 1.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": -1087,
            "m_EditorPositionY": 588,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 1.0,
            "m_ConstB": 0.0
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}