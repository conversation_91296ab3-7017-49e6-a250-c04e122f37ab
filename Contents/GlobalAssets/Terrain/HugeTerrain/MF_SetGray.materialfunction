adnj
{
    "Guid": "f14e7cfd7c1756967c4fd82691761bc3",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 6061,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 840,
            "m_EditorPositionY": 3,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -392,
            "m_EditorPositionY": -12,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "Color",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -225,
            "m_EditorPositionY": -44,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": true,
            "m_G": false,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -216,
            "m_EditorPositionY": 36,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": true,
            "m_B": false,
            "m_A": false
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -216,
            "m_EditorPositionY": 116,
            "m_Description": "",
            "Class": "cross::MaterialExpressionComponentMask",
            "m_Input": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_R": false,
            "m_G": false,
            "m_B": true,
            "m_A": false
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": 65,
            "m_EditorPositionY": -132,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.2989000082015991
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": 72,
            "m_EditorPositionY": 4,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.5870000123977661
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": 40,
            "m_EditorPositionY": 116,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 5,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.11400000005960465
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": 354,
            "m_EditorPositionY": -28,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": 533,
            "m_EditorPositionY": 96,
            "m_Description": "",
            "Class": "cross::MaterialExpressionAdd",
            "m_A": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}