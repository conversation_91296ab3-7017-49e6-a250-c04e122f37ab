adnj
{
    "Guid": "5a1c8d418afdeabcd643db2646076220",
    "Version": 5,
    "ClassID": 9,
    "DataSize": 7844,
    "ContentType": 2,
    "IsStreamFile": false
}
{
    "expression": [
        {
            "m_Id": 1,
            "m_EditorPositionX": 1135,
            "m_EditorPositionY": 195,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionOutput",
            "m_FuncInoutId": 0,
            "m_OutputName": "",
            "m_SortPriority": 1.0,
            "m_Output": {
                "LinkedExpressionId": 12,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        },
        {
            "m_Id": 2,
            "m_EditorPositionX": -783,
            "m_EditorPositionY": 28,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 1,
            "m_InputName": "LabColor",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 3,
            "m_EditorPositionX": -799,
            "m_EditorPositionY": 172,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 2,
            "m_InputName": "TgtLabColor",
            "m_InputType": 2,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 4,
            "m_EditorPositionX": -228,
            "m_EditorPositionY": 475,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionInput",
            "m_FuncInoutId": 3,
            "m_InputName": "Tolerance",
            "m_InputType": 0,
            "m_SortPriority": 1.0,
            "m_Preview": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_PreviewValue": {
                "x": 0.0,
                "y": 0.0,
                "z": 0.0,
                "w": 0.0
            }
        },
        {
            "m_Id": 5,
            "m_EditorPositionX": -518,
            "m_EditorPositionY": 84,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": 2,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 3,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 6,
            "m_EditorPositionX": -296,
            "m_EditorPositionY": 148,
            "m_Description": "",
            "Class": "cross::MaterialExpressionFunctionCall",
            "m_MaterialFunction": "2890da7d239d36bfb3441efc520cfaaa",
            "m_FunctionInputs": [
                {
                    "Input": {
                        "LinkedExpressionId": 5,
                        "LinkedExpressionOutputIndex": 0,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                },
                {
                    "Input": {
                        "LinkedExpressionId": -1,
                        "LinkedExpressionOutputIndex": -1,
                        "LinkedExpressionOutputFunctionInoutId": -1
                    }
                }
            ]
        },
        {
            "m_Id": 7,
            "m_EditorPositionX": 296,
            "m_EditorPositionY": 156,
            "m_Description": "",
            "Class": "cross::MaterialExpressionDivide",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 8,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 10.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 8,
            "m_EditorPositionX": 24,
            "m_EditorPositionY": 164,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMax",
            "m_A": {
                "LinkedExpressionId": 6,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.019999999552965165
        },
        {
            "m_Id": 9,
            "m_EditorPositionX": 600,
            "m_EditorPositionY": 220,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": 7,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 11,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 10,
            "m_EditorPositionX": 49,
            "m_EditorPositionY": 372,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSubtract",
            "m_A": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": 4,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 1.0,
            "m_ConstB": 0.0
        },
        {
            "m_Id": 11,
            "m_EditorPositionX": 290,
            "m_EditorPositionY": 372,
            "m_Description": "",
            "Class": "cross::MaterialExpressionMultiply",
            "m_A": {
                "LinkedExpressionId": 10,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_B": {
                "LinkedExpressionId": -1,
                "LinkedExpressionOutputIndex": -1,
                "LinkedExpressionOutputFunctionInoutId": -1
            },
            "m_ConstA": 0.0,
            "m_ConstB": 0.003846149891614914
        },
        {
            "m_Id": 12,
            "m_EditorPositionX": 831,
            "m_EditorPositionY": 228,
            "m_Description": "",
            "Class": "cross::MaterialExpressionSaturate",
            "m_Value": {
                "LinkedExpressionId": 9,
                "LinkedExpressionOutputIndex": 0,
                "LinkedExpressionOutputFunctionInoutId": -1
            }
        }
    ],
    "defines": {
        "ExposeToLibrary": true
    }
}