adnj
{
    "Guid": "2ef99505cac896be5b4f060da10debd6",
    "Version": 5,
    "ClassID": 5,
    "DataSize": 3841,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "00f285828e5dd4230bbfec66d54367d4",
        "1fdd64df34ac7447abd4076d7e654abb",
        "7614c268dcc798a5d6405d59c711cfb2",
        "7c4cbca343316482d9cb20fa1a66d3ba",
        "8ac3fde967516470686dccdb6466a730"
    ]
}
{
    "parent": {
        "ASSET_MAGIC_NUMBER": 778986593,
        "Path": "00f285828e5dd4230bbfec66d54367d4"
    },
    "version": 1,
    "properties": {
        "_BaseColor": [
            1.0,
            1.0,
            1.0,
            1.0
        ],
        "_Metallic": [
            0.0
        ],
        "_NormalScale": [
            1.0
        ],
        "_Specular": [
            0.5
        ],
        "_SubsurfaceColor": [
            0.0,
            0.0,
            0.0,
            1.0
        ],
        "_AO_Intensity": [
            1.0
        ],
        "_BaseMap": "8ac3fde967516470686dccdb6466a730",
        "_NormalMap": "7614c268dcc798a5d6405d59c711cfb2",
        "_MaskMap": "1fdd64df34ac7447abd4076d7e654abb",
        "_Roughness": [
            1.399999976158142
        ],
        "_MROTex": "1fdd64df34ac7447abd4076d7e654abb",
        "NIGHT": [
            0.0
        ],
        "_ScaleUV": [
            1.0,
            1.0
        ],
        "_OffsetUV": [
            0.0,
            0.0
        ],
        "_Reactivemask": [
            0.0,
            0.0
        ],
        "USE_EMISSIVE_MAP": false,
        "_BaseMap_PageTable": "default_texture",
        "ANISOTROPIC_SAMPLE": [
            1.0
        ]
    },
    "render_group": {
        "forward": 2160
    },
    "state": {
        "forward": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": false,
                "DepthCompareOp": 7,
                "EnableStencil": false,
                "StencilReadMask": 255,
                "StencilWriteMask": 255,
                "FrontFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                },
                "BackFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 2,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 1
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 0
        }
    },
    "parameter_collection": {
        "ASSET_MAGIC_NUMBER": 778986593,
        "Path": "7c4cbca343316482d9cb20fa1a66d3ba",
        "Usage": [
            "NIGHT"
        ]
    },
    "defines": {
        "BlendModeEnable": false,
        "BlendMode": 0,
        "ShadingModelEnable": false,
        "ShadingModel": 0,
        "TwoSidedEnable": false,
        "TwoSided": false,
        "RenderGroupBias": 0
    }
}