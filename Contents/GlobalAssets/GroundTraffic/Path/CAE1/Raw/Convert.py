import csv

'''
This script is to convert the format of csv file recorded from vsd
  to match with the format of ground traffic status sync file
'''

data = []
with open('TrafficDemoRaw.csv', 'r') as file:
  spamreader = csv.reader(file, delimiter=',', quotechar='|')
  for row in spamreader:
    data.append(row[2:8])

with open('TrafficDemoNew.csv', 'w') as file:
  spamwriter = csv.writer(file, delimiter=',', lineterminator='\n')
  for row in data:
    spamwriter.writerow(row)
