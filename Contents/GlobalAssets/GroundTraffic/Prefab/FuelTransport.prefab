adnj
{
    "Guid": "35ba5032f8c03410ea268bfc5e767cfe",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 10729,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "5c99abc8842d7456fa0122571c5e9f33"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "6ea8ebf6e7c694e3391304fb3e56be16"
        },
        "entities": {
            "6ea8ebf6e7c694e3391304fb3e56be16": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "6ea8ebf6e7c694e3391304fb3e56be16",
                "name": "FuelTransport",
                "prototype": 1175991460129941971,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::ControllableUnitComponentG": {
                        "Type": 1005,
                        "Controller": "{\n    \"Entity\": \"6ea8ebf6e7c694e3391304fb3e56be16\",\n    \"RotationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 1.0\n    },\n    \"TranslationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"SpawnSpot\": \"\",\n    \"InsideWorldBounds\": {\n        \"position\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        },\n        \"rotate\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0,\n            \"w\": 1.0\n        },\n        \"halfExtents\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        }\n    },\n    \"CollisionType\": 0,\n    \"BlockMask\": 65535,\n    \"bUseControllerRotationPitch\": true,\n    \"bUseControllerRotationYaw\": true,\n    \"bUseControllerRotationRoll\": true,\n    \"bUseControllerTranslate\": true\n}",
                        "componentHash": **********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "FuelTransport",
                            "ScriptPath": "",
                            "ScriptDefaultValue": "",
                            "ScriptEditorFields": "",
                            "LocalRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            },
                            "LocalTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "LocalScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "config": {
                                        "AircraftModel": "787-8",
                                        "FlightHeightBias": 0.0
                                    },
                                    "ComponentType": "cegf::JSBSimMovementComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                },
                                {
                                    "ComponentType": "cegf::ControllableUnitComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "af52311599d70492dba8028515aa1cd4"
                ]
            },
            "af52311599d70492dba8028515aa1cd4": {
                "prefabId": "5c99abc8842d7456fa0122571c5e9f33",
                "prefabEuid": "451746ba160b944a19e3615177227a4a",
                "euid": "af52311599d70492dba8028515aa1cd4",
                "prototype": 7653870818700229998,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mScale": {
                                "x": 0.800000011920929,
                                "y": 0.800000011920929,
                                "z": 0.800000011920929
                            }
                        }
                    },
                    "cross::ControllableUnitComponentG": {
                        "Type": 1004,
                        "Controller": "{\n    \"Entity\": \"af52311599d70492dba8028515aa1cd4\",\n    \"RotationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 1.0\n    },\n    \"TranslationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"SpawnSpot\": \"\",\n    \"InsideWorldBounds\": {\n        \"position\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        },\n        \"rotate\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0,\n            \"w\": 1.0\n        },\n        \"halfExtents\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        }\n    },\n    \"CollisionType\": 0,\n    \"BlockMask\": 65535,\n    \"bUseControllerRotationPitch\": true,\n    \"bUseControllerRotationYaw\": true,\n    \"bUseControllerRotationRoll\": true,\n    \"bUseControllerTranslate\": true\n}"
                    },
                    "+cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "PushbackTractor",
                            "ScriptPath": "",
                            "ScriptDefaultValue": "",
                            "ScriptEditorFields": "",
                            "LocalRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            },
                            "LocalTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "LocalScale": {
                                "x": 0.800000011920929,
                                "y": 0.800000011920929,
                                "z": 0.800000011920929
                            },
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::AnimationComponent"
                                },
                                {
                                    "ComponentType": "cegf::ControllableUnitComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "3e236efe0405d451698c7b73156d6f4d",
                    "c92ddd29b425e4942a17ebcd162d75bc",
                    "132c07346420543b7aaa738b24d1af33"
                ]
            },
            "3e236efe0405d451698c7b73156d6f4d": {
                "prefabId": "5c99abc8842d7456fa0122571c5e9f33",
                "prefabEuid": "db5f9651e16fd435b94930aa8937fff4",
                "euid": "3e236efe0405d451698c7b73156d6f4d",
                "children": []
            },
            "c92ddd29b425e4942a17ebcd162d75bc": {
                "prefabId": "5c99abc8842d7456fa0122571c5e9f33",
                "prefabEuid": "59e09086244ea42c985504798ae02d6a",
                "euid": "c92ddd29b425e4942a17ebcd162d75bc",
                "children": []
            },
            "132c07346420543b7aaa738b24d1af33": {
                "prefabId": "5c99abc8842d7456fa0122571c5e9f33",
                "prefabEuid": "3da96f2ec096246f18b3f0e00cc8a870",
                "euid": "132c07346420543b7aaa738b24d1af33",
                "children": []
            }
        }
    }
}