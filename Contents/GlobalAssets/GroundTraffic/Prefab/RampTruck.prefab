adnj
{
    "Guid": "f5a80cfcdfca143e28d22180dbb1c8a2",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 10721,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "5c99abc8842d7456fa0122571c5e9f33"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "a1812363f2e784eeb9f60865652cf82f"
        },
        "entities": {
            "a1812363f2e784eeb9f60865652cf82f": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "a1812363f2e784eeb9f60865652cf82f",
                "name": "RampTruck",
                "prototype": 1175991460129941971,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::FFSWGS84ComponentG": {
                        "mElevation": 0.0,
                        "mLatitude_Degree": 0.0,
                        "mLatitude_Minute": 0.0,
                        "mLatitude_Second": 0.0,
                        "mLongitude_Degree": 0.0,
                        "mLongitude_Minute": 0.0,
                        "mLongitude_Second": 0.0,
                        "mHeading": 0.0,
                        "mPitch": 0.0,
                        "mRoll": 0.0,
                        "mZfight": 0.0,
                        "componentHash": 1831632852
                    },
                    "cross::ControllableUnitComponentG": {
                        "Type": 1005,
                        "Controller": "{\n    \"Entity\": \"a1812363f2e784eeb9f60865652cf82f\",\n    \"RotationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 1.0\n    },\n    \"TranslationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"SpawnSpot\": \"\",\n    \"InsideWorldBounds\": {\n        \"position\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        },\n        \"rotate\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0,\n            \"w\": 1.0\n        },\n        \"halfExtents\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        }\n    },\n    \"CollisionType\": 0,\n    \"BlockMask\": 65535,\n    \"bUseControllerRotationPitch\": true,\n    \"bUseControllerRotationYaw\": true,\n    \"bUseControllerRotationRoll\": true,\n    \"bUseControllerTranslate\": true\n}",
                        "componentHash": **********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "RampTruck",
                            "ScriptPath": "",
                            "ScriptDefaultValue": "",
                            "ScriptEditorFields": "",
                            "LocalRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            },
                            "LocalTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "LocalScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "config": {
                                        "AircraftModel": "787-8",
                                        "FlightHeightBias": 0.0
                                    },
                                    "ComponentType": "cegf::JSBSimMovementComponent"
                                },
                                {
                                    "ComponentType": "cegf::WGS84Component"
                                },
                                {
                                    "ComponentType": "cegf::ControllableUnitComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "1723cf385968e414b991b16b18378c48"
                ]
            },
            "1723cf385968e414b991b16b18378c48": {
                "prefabId": "5c99abc8842d7456fa0122571c5e9f33",
                "prefabEuid": "451746ba160b944a19e3615177227a4a",
                "euid": "1723cf385968e414b991b16b18378c48",
                "prototype": 7653870818700229998,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mScale": {
                                "x": 0.800000011920929,
                                "y": 0.800000011920929,
                                "z": 0.800000011920929
                            }
                        }
                    },
                    "cross::ControllableUnitComponentG": {
                        "Type": 1004,
                        "Controller": "{\n    \"Entity\": \"1723cf385968e414b991b16b18378c48\",\n    \"RotationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 1.0\n    },\n    \"TranslationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"SpawnSpot\": \"\",\n    \"InsideWorldBounds\": {\n        \"position\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        },\n        \"rotate\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0,\n            \"w\": 1.0\n        },\n        \"halfExtents\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        }\n    },\n    \"CollisionType\": 0,\n    \"BlockMask\": 65535,\n    \"bUseControllerRotationPitch\": true,\n    \"bUseControllerRotationYaw\": true,\n    \"bUseControllerRotationRoll\": true,\n    \"bUseControllerTranslate\": true\n}"
                    },
                    "+cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "PushbackTractor",
                            "ScriptPath": "",
                            "ScriptDefaultValue": "",
                            "ScriptEditorFields": "",
                            "LocalRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            },
                            "LocalTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "LocalScale": {
                                "x": 0.800000011920929,
                                "y": 0.800000011920929,
                                "z": 0.800000011920929
                            },
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::AnimationComponent"
                                },
                                {
                                    "ComponentType": "cegf::ControllableUnitComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "5dafc093cbb2846458d2c6250d0a4086",
                    "d73e2789832f349c6a40c4e5e62fc060",
                    "93ad54469d2294c97a382064fdb35ae7"
                ]
            },
            "5dafc093cbb2846458d2c6250d0a4086": {
                "prefabId": "5c99abc8842d7456fa0122571c5e9f33",
                "prefabEuid": "db5f9651e16fd435b94930aa8937fff4",
                "euid": "5dafc093cbb2846458d2c6250d0a4086",
                "children": []
            },
            "d73e2789832f349c6a40c4e5e62fc060": {
                "prefabId": "5c99abc8842d7456fa0122571c5e9f33",
                "prefabEuid": "59e09086244ea42c985504798ae02d6a",
                "euid": "d73e2789832f349c6a40c4e5e62fc060",
                "children": []
            },
            "93ad54469d2294c97a382064fdb35ae7": {
                "prefabId": "5c99abc8842d7456fa0122571c5e9f33",
                "prefabEuid": "3da96f2ec096246f18b3f0e00cc8a870",
                "euid": "93ad54469d2294c97a382064fdb35ae7",
                "children": []
            }
        }
    }
}