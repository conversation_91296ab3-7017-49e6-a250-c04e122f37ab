
CsvFly = Class();

CsvFly["UseDefaultPos"] = true

CsvFly["CameraMode"] = {
    Fpp = 0,
    Orbit = 1,
    Free = 2,
}

-- /Contents/GlobalAssets/Scripts/FlightRoute/ZPPP_AROUND.csv
CsvFly["UseFlightRouteData"] = false

CsvFly["FlightRouteFilePath"] = "/Contents/GlobalAssets/Scripts/FlightRoute/ZGSZ_ZGGG.csv"

CsvFly["FlightRouteFileIndex"] = {
    Latitude = 3,
    Longitude = 4,
    Altitude = 5,
    Heading = 6,
    Pitch = 7,
    Roll = 8,
}

CsvFly["FlightRouteData"] = {}

CsvFly["FlightRouteCursor"] = 1

CsvFly["FlightRouteDataIndex"] = {
    Latitude = 1,
    Longitude = 2,
    Altitude = 3,
    Heading = 4,
    Pitch = 5,
    Roll = 6,
}

function CsvFly:OnCreate()
    print("------------------------------------------------CsvFly:OnCreate-------------------------------------")

    self.wgs84 = self.Entity:GetFFSWGS84Component()
    self.worldTransition = self.Entity:GetTransformComponent():GetWorldTranslation()
    self.localTransition = self.Entity:GetTransformComponent():GetLocalTranslation()

    if(self.Entity:HasFfsComponent()) then
        self.controller = self.Entity:GetFfsComponent()
    end

    -- Read flight route csv file if we provide one
    local file = io.open(self.wgs84:GetLogFilePath() .. self.FlightRouteFilePath, "r")
    if file ~= nil then
        self.UseDefaultPos = false
        self.UseFlightRouteData = true

        for line in file:lines() do
            local rt = {}
            local index = self.FlightRouteFileIndex

            string.gsub(line, '[^' .. ',' .. ']+', function(w) table.insert(rt, w) end)
            self.FlightRouteData[#self.FlightRouteData + 1] = { tonumber(rt[index.Latitude]) * 1.0,
                tonumber(rt[index.Longitude]) * 1.0, tonumber(rt[index.Altitude]) * 1.0,
                tonumber(rt[index.Heading]) * 1.0, tonumber(rt[index.Pitch]) * 1.0,
                tonumber(rt[index.Roll]) * -1.0 }
        end

        self.FlightRouteCursor = 1
        file:close()

        print("--------------CsvFly.UseFlightRouteData--------------")
    else
        self.UseDefaultPos = true

        print("--------------CsvFly.UseDefaultPos--------------")
    end
end

function CsvFly:OnPreUpdate(elapsedTime)

end

function CsvFly:GetStrByFilePath(jsonPath)
    local jsonFile = io.open(jsonPath, "r")
    local jsonString = jsonFile:read("*all")
    jsonFile:close()
    return jsonString
end

function CsvFly:GetConfigPath(jsonPath)
    local jsonString = self:GetStrByFilePath(jsonPath)
    local config_path = string.match(jsonString, '"ConfigurationPath":%s*"([^"]+)"')
    return config_path
end

function CsvFly:GetAirportPath(jsonPath)
    local jsonString = self:GetStrByFilePath(jsonPath)
    local airport_str = string.match(jsonString, '"Airport":%s*{.-}')
    local key = string.match(airport_str, '"Airport":%s*{%s*"(%w+)":')
    return key
end

function CsvFly:GetWgs84(jsonPath)
    local jsonString = self:GetStrByFilePath(jsonPath)
    local wgs84_str = string.match(jsonString, '"WGS84":%s*%[(.-)%]')
    local wgs84 = {}
    for num in string.gmatch(wgs84_str, "[%d%.]+") do
        table.insert(wgs84, tonumber(num))
    end

    for i, v in ipairs(wgs84) do
        print(i, v)
    end
    local res = function(degree, minute, second)
        local sign = 1
        if degree < 0 then
            sign = -1
            degree = -1 * degree
        end

        return sign * (tonumber(degree) + tonumber(minute) / 60 + tonumber(second) / 3600)
    end
    return res(wgs84[1], wgs84[2],wgs84[3]), res(wgs84[4], wgs84[5],wgs84[6]),wgs84[7]
end

function CsvFly:OnUpdate(elapsedTime)
    if self.controller:IsTbusppEnabled() then
        return
    end
    
    -- Freeze aircraft coordinate when using free camera
    self.camera = ce.Level:GetMainCamera()
    if (self.camera and self.camera:GetControllableUnitComponent():ControllableCameraGetMode() == self.CameraMode.Free) then
        return
    end

    -- if self.UseDefaultPos then
    --     --local jsoncodePath = self.wgs84:GetLogFilePath().."/"..debug.getinfo(1, "S").source
    --     --jsoncodePath = string.gsub(jsoncodePath, "CsvFly.lua","Json.lua")
    --     --loadfile(jsoncodePath)()
    --     local configurationPath = self:GetConfigPath(self.wgs84:GetLogFilePath().."/ProjectConfig.json")
    --     configurationPath = self.wgs84:GetLogFilePath().."/Contents/GlobalAssets/Settings/Configuration/"..configurationPath
    --     print(configurationPath)
    --     local airportjson = self.wgs84:GetLogFilePath().."/Contents/DataBase/"..self:GetAirportPath(configurationPath).."/manifest.json"
    --     print(airportjson)
    --     local lat, lon = self:GetWgs84(airportjson)
    --     lat = 23
    --     lon = 113
    --     local alt = 0.0
    --     self.controller:CheckReposition(lat, lon, alt)
    --     self.controller:SetControllableGeographicAircraft(lat, lon, alt, 0.0, 0.0, 0.0)
    --     self.UseDefaultPos = false
    --     print("--------------CsvFly.UseDefaultPos--------------")
    -- else
    if self.UseFlightRouteData then
        if self.FlightRouteCursor > #self.FlightRouteData or self.FlightRouteCursor < 1 then
            self.FlightRouteCursor = 1
        end
        
        local currentData = self.FlightRouteData[self.FlightRouteCursor]
        local index = self.FlightRouteDataIndex
        
        self.controller:CheckReposition(currentData[index.Latitude], currentData[index.Longitude], currentData[index.Altitude])
        self.controller:SetControllableGeographicAircraft(currentData[index.Latitude], currentData[index.Longitude], currentData[index.Altitude], currentData[index.Heading], currentData[index.Pitch], currentData[index.Roll])

        -- Speed up
        if (ce.Input:IsPressed(CEButton.LeftControl)) then
            self.FlightRouteCursor = self.FlightRouteCursor + 50
        -- Rewind
        elseif (ce.Input:IsPressed(CEButton.LeftAlt)) then
            self.FlightRouteCursor = self.FlightRouteCursor - 50
        else
            self.FlightRouteCursor = self.FlightRouteCursor + 1
        end
    end
end

function CsvFly:OnPostUpdate(elapsedTime)

end
