import socket
import threading
import struct
import argparse

def recv_handler(sock):
    while True:
        try:
            data, addr = sock.recvfrom(4096)
            print(f"\n[Raw Data] Received {len(data)} bytes from {addr}: {data}")  # Print raw data
            if len(data) == 0:
                print(f"Empty packet from {addr}")
                continue

            try:
                decoded = data.decode('utf-8')
                if ':' in decoded:
                    str1, str2 = decoded.split(':', 1)
                    print(f"[Duplex Received] Name: {str1}")
                    print(f"[Duplex Received] Value: {str2}")
                else:
                    print(f"[Simple Message]: {decoded}")

            except UnicodeDecodeError:
                print(f"Decode error from {addr}")
            except struct.error:
                print(f"Structure error from {addr}")

        except (ConnectionResetError, OSError) as e:
            print(f"Receive error: {str(e)}")
            break

def send_handler(sock, server_addr):
    while True:
        try:
            message = input("\n[Input] Enter message (exit to quit): ")
            if message.lower() == 'exit':
                break
            sock.sendto(message.encode(), server_addr)
        except (KeyboardInterrupt, EOFError):
            break

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('-s', '--server', required=True)
    parser.add_argument('-p', '--port', type=int, default=8888)
    args = parser.parse_args()


    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.bind(('0.0.0.0', 0))  


    recv_thread = threading.Thread(target=recv_handler, args=(sock,))
    recv_thread.daemon = True
    recv_thread.start()

    send_thread = threading.Thread(target=send_handler, 
                                 args=(sock, (args.server, args.port)))
    send_thread.start()

    send_thread.join()
    sock.close()

if __name__ == '__main__':
    main()