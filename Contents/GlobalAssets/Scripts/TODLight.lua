
TODLight = Class();

function TODLight:OnCreate()
    print ("TODLight Lua Script")
    self.tod_state = 0
	local todComp = self.Entity:GetTODLightComponent()
	--self.StashedYear   = todComp:GetTODLightYear()   
	--self.StashedMonth  = todComp:GetTODLightMonth()     
	--self.StashedDay    = todComp:GetTODLightDay()       
	--self.StashedHour   = todComp:GetTODLightHour()      
	--self.StashedMinute = todComp:GetTODLightMinute()    
	--self.StashedSecond = todComp:GetTODLightSecond()   
	self.StashedConfig = todComp:GetTODLightConfig()  
	
end

function  TODLight:OnUpdate(elapsedTime)
    if(self.Entity:HasTODLightComponent()) then 
        local todComp = self.Entity:GetTODLightComponent()
        local changed = false
		
        -- if (ce.Input:WasJustReleased(CEButton.T)) then
        --     if (self.tod_state == 0) then
        --         self.tod_state = 1
        --         todComp:SetTODLightHour(6)
        --         todComp:SetTODLightMinute(0)
        --         changed = true
        --     elseif (self.tod_state == 1) then
        --         self.tod_state = 2
        --         todComp:SetTODLightHour(12)
        --         todComp:SetTODLightMinute(10)
        --         changed = true
        --     elseif (self.tod_state == 2) then
        --         self.tod_state = 3
        --         todComp:SetTODLightHour(18)
        --         todComp:SetTODLightMinute(50)
        --         changed = true
        --     elseif (self.tod_state == 3) then
        --         self.tod_state = 0
        --         todComp:SetTODLightHour(24)
        --         todComp:SetTODLightMinute(00)
        --         changed = true
        --     end
        -- end
		if (ce.Input:WasJustReleased(CEButton.T)) then
			-- print(self.StashedYear  )-- 
			-- print(self.StashedMonth )-- 	todComp:SetTODLightYear(self.StashedYear)
			-- print(self.StashedDay   )-- 	todComp:SetTODLightMonth(self.StashedMonth)
			-- print(self.StashedHour  )-- 	todComp:SetTODLightDay(self.StashedDay)
			-- print(self.StashedMinute)-- 	todComp:SetTODLightHour(self.StashedHour)
			-- print(self.StashedSecond)-- 	todComp:SetTODLightMinute(self.StashedMinute)
			-- todComp:SetTODLightSecond(self.StashedSecond)
			todComp:SetTODLightConfig(self.StashedConfig)
			changed = true
        end
        if (changed) then
            todComp:OnChangeConfig()
            todComp:SyncAllTODLightComponents()
            print("TODLight refreshed")
            print(string.format("Current date is %d/%d/%d: %d:%d:%d", todComp:GetTODLightYear(), todComp:GetTODLightMonth(), todComp:GetTODLightDay(), todComp:GetTODLightHour(), todComp:GetTODLightMinute(), todComp:GetTODLightSecond()))
        end
    end
end

function TODLight:OnPostUpdate(elapsedTime)
    --print ('HackFly.OnPostUpdate')
end
