Tractor = Class()
Tractor["Timer"] = 0.0

---------------------------------------------------------------
--------------------- Animator Parameters ---------------------
---------------------------------------------------------------
ParameterType = {
  PlayRate = 0,
  Axis1D = 1,
  Axis2D = 2,
}
-- Axis 1D: Axis X: Angle
Tractor["FrontWheelTurn"] = {
  Name = "FrontWheelTurn",
  Value = 0.0,
  Min = -45.0,
  Max = 45.0,
  Type = ParameterType.Axis1D,
}

-- Axis 1D: Axis X: Angle
Tractor["WheelSpin"] = {
  Name = "WheelSpin",
  Value = 0.0,
  Min = 0.0,
  Max = 360.0,
  Type = ParameterType.Axis1D,
}

function Tractor:OnCreate()
  self.Animator = self.Entity:GetAnimatorComponent()
  self.Timer = 0.0
end

function Tractor:OnPreUpdate(elapsedTime)

end

function Tractor:OnUpdate(elapsedTime)
  self.Timer = self.Timer + elapsedTime
  self.FrontWheelTurn.Value = (self.Timer * 10) % 90 + self.FrontWheelTurn.Min
  -- ce.Level:log("Elapsed Time: " .. tostring(elapsedTime) .. ", Timer: " .. tostring(self.Timer) .. ", Angle: " .. tostring(self.FrontWheelTurn.Value))
  self:SetAnimatorParameter(self.FrontWheelTurn)

  self.WheelSpin.Value = (self.Timer * 100) % 360
  self:SetAnimatorParameter(self.WheelSpin)
end

function Tractor:OnPostUpdate(elapsedTime)
end

--------------------------------------------------------------
--------------------- Animation Settings ---------------------
--------------------------------------------------------------

function Tractor:SetAnimatorParameter(parameter)
  if parameter.Value < parameter.Min then
    parameter.Value = parameter.Min
    ce.Level:log("Parameter " .. parameter.Name .. " value exceeds the MIN limit. Value will be set to MIN value " .. tostring(parameter.Min))
  end

  if parameter.Value > parameter.Max then
    parameter.Value = parameter.Max
    ce.Level:log("Parameter " .. parameter.Name .. " value exceeds the MAX limit. Value will be set to Max value " .. tostring(parameter.Max))
  end

  if parameter.Type == ParameterType.PlayRate then
    self.Animator:SetParameter(parameter.Name, parameter.Value)
  elseif parameter.Type == ParameterType.Axis1D then
    self.Animator:SetParameter(parameter.Name, ce.math.Vector2(parameter.Value, 0.0))
  end
end

function Tractor:GetMessage()
  return string.format("")
end
