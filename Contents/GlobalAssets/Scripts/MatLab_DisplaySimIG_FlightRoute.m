[filepath, ~, ~] = fileparts(mfilename('fullpath'));
SimFilename = fullfile(filepath, 'StatusSync/27_HostedAircraft_Raw.csv');
disp(SimFilename);

IGFilename = fullfile(filepath, 'StatusSync/27_HostedAircraft_Lerp.csv');
disp(IGFilename);

SimOpts = detectImportOptions(SimFilename);
SimOpts.VariableTypes = {'double','double','double','double','double','double','double'};
SimOpts.VariableNames = {'Frame','SimX','SimY','SimZ','SimYaw','SimPitch','SimRoll',};
SimData = readtable(SimFilename, SimOpts);

IGOpts = detectImportOptions(IGFilename);
IGOpts.VariableTypes = {'double','double','double','double','double','double','double'};
IGOpts.VariableNames = {'Frame','IGX','IGY','IGZ','IGYaw','IGPitch','IGRoll'};
IGData = readtable(IGFilename, IGOpts);


%剔除ig多余数据和sim齐平
SimFirstFrame = SimData.Frame(1,:);
logicalIndex = IGData.Frame >= 10.0;
IGData = IGData(logicalIndex, :);

% 距离 m
SimDis = sqrt(diff(SimData.SimX).^2 + diff(SimData.SimY).^2 + diff(SimData.SimZ).^2) * 0.01;
IGDis = sqrt(diff(IGData.IGX).^2 + diff(IGData.IGY).^2 + diff(IGData.IGZ).^2)* 0.01;


% 差分时间 s
SimDiffTime = diff(SimData.Frame) * 0.016666;
IGDiffTime = diff(IGData.Frame) * 0.016666;

% 速度  m/s
SimSpeed = SimDis ./ SimDiffTime;
IGSpeed = IGDis ./ IGDiffTime;

% 加速度 m/s^2
SimAcceleration = diff(SimSpeed) ./ SimDiffTime(2:end);
IGAcceleration = diff(IGSpeed) ./ IGDiffTime(2:end);


% 计算sim和ig  Roll角速度  °/s
SimRollVelocity = diff(SimData.SimRoll) ./ SimDiffTime;
IGRollVelocity = diff(IGData.IGRoll) ./ IGDiffTime;

% 计算sim和ig Roll角加速度
SimRollAcceleration = diff(SimRollVelocity) ./ SimDiffTime(2:end);
IGRollAcceleration = diff(IGRollVelocity) ./ IGDiffTime(2:end);

IG_legend = '-r';
Sim_legend = '-b';

figure
%sim/IG position
posUint = 0.00001;
plot3(SimData.SimX*posUint, SimData.SimZ*posUint, SimData.SimY*posUint, Sim_legend);
hold on;
plot3(IGData.IGX*posUint, IGData.IGZ*posUint, IGData.IGY*posUint, IG_legend);
hold off;
title('IG/Sim World Position');
xlabel('World_X');
ylabel('World_Z');
zlabel('World_Y');
legend('Sim', 'IG');
set(gcf, 'WindowState', 'maximized');


figure
%Speed
subplot(2, 1, 1);
plot(SimData.Frame(2:end), SimSpeed, Sim_legend);
hold on;
plot(IGData.Frame(2:end), IGSpeed, IG_legend);
hold off;
xlabel('Frame');
ylabel('Speed');
title('IG/Sim Speed');
legend('Sim', 'IG');

%Acceleration
subplot(2, 1, 2);
plot(SimData.Frame(3:end), SimAcceleration, Sim_legend);
hold on;
plot(IGData.Frame(3:end), IGAcceleration, IG_legend);
hold off;
xlabel('Frame');
ylabel('Acceleration');
title('IG/Sim Acceleration');
legend('Sim', 'IG');

figure
% 绘制roll角度
subplot(3, 1, 1);
plot(SimData.Frame, SimData.SimRoll, Sim_legend);
hold on;
plot(IGData.Frame, IGData.IGRoll, IG_legend);
hold off;
xlabel('Frame');
ylabel('Angle');
title('Roll Angle');
legend('Sim', 'IG');

% 绘制角速度
subplot(3, 1, 2);
plot(SimData.Frame(2:end), SimRollVelocity, Sim_legend);
hold on;
plot(IGData.Frame(2:end), IGRollVelocity, IG_legend);
hold off;
xlabel('Frame');
ylabel('Velocity');
title('Roll Angular Velocity');
legend('Sim', 'IG');

% 绘制角加速度
subplot(3, 1, 3);
plot(SimData.Frame(3:end), SimRollAcceleration, Sim_legend);
hold on;
plot(IGData.Frame(3:end), IGRollAcceleration, IG_legend);
hold off;
xlabel('Frame');
ylabel('Acceleration');
title('Roll Angular Acceleration');
legend('Sim', 'IG');
