<NodeGraph
  Name = "Tractor"
  NodeID = "10626"
  ConnectionID = "21182"
  HashName = "35536392591926417151">
  <Camera MaxZoom = "5" MinZoom = "-10" ZoomCount = "-1">
    <Viewport
      _WorldX = "-1270.5272"
      _WorldY = "-537.5632"
      _Height = "1478.7515"
      _AspectRatio = "1.6153846"/>
    <Viewport
      _WorldX = "970"
      _WorldY = "80"
      _Height = "1183"
      _AspectRatio = "1.6153846"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "890"
    Y = "310"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21039"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_BlendByLayeredFilterNode
    ID = "10366"
    X = "10"
    Y = "-160"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21148",
    "21135",
    "21112",
    "21114",
    "21123"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "def-wheel.ft.l",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "def-wheel.ft.r",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "def-wheel.bk.l",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "def-wheel.bk.r",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10366",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "def-piston.ft.l"
    ID = "10421"
    X = "-560"
    Y = "550"
    NodeJsonData = ##{
  "BoneName": "def-piston.ft.l",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21167",
    ""
  ],
  "Name": "10421",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "def-piston.ft.r"
    ID = "10431"
    X = "-560"
    Y = "660"
    NodeJsonData = ##{
  "BoneName": "def-piston.ft.r",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21168",
    ""
  ],
  "Name": "10431",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "def-wheel.ft.r"
    ID = "10495"
    X = "-550"
    Y = "0"
    NodeJsonData = ##{
  "BoneName": "def-wheel.ft.r",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21072",
    ""
  ],
  "Name": "10495",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "def-wheel.ft.l"
    ID = "10528"
    X = "-550"
    Y = "-170"
    NodeJsonData = ##{
  "BoneName": "def-wheel.ft.l",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21063",
    ""
  ],
  "Name": "10528",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "def-wheel.bk.l"
    ID = "10529"
    X = "-560"
    Y = "160"
    NodeJsonData = ##{
  "BoneName": "def-wheel.bk.l",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21091",
    ""
  ],
  "Name": "10529",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "def-wheel.bk.r"
    ID = "10544"
    X = "-560"
    Y = "300"
    NodeJsonData = ##{
  "BoneName": "def-wheel.bk.r",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21105",
    ""
  ],
  "Name": "10544",
  "Type": "TransformBoneNode"
}##/>
  <Anim_BlendByLayeredFilterNode
    ID = "10562"
    X = "330"
    Y = "310"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21060",
    "21049",
    "21056"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "def-piston.ft.l",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "def-piston.ft.r",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10562",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "WheelSpin"
    ParamType = "Vector3"
    ID = "10578"
    X = "-980"
    Y = "40"
    NodeJsonData = ##{
  "InParams": [
    "WheelSpin"
  ],
  "ReturnType": "Vector3",
  "Name": "10578",
  "Type": "ParamImplNode"
}##/>
  <Anim_SlotNode
    SlotName = "DefaultSlot"
    GroupName = "DefaultGroup"
    ID = "10591"
    X = "-580"
    Y = "-410"
    NodeJsonData = ##{
  "InPoseLinks": [],
  "SlotName": "DefaultSlot",
  "Name": "10591",
  "Type": "SlotNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "FrontWheelSteering"
    ParamType = "Vector3"
    ID = "10602"
    X = "-1010"
    Y = "630"
    NodeJsonData = ##{
  "InParams": [
    "FrontWheelSteering"
  ],
  "ReturnType": "Vector3",
  "Name": "10602",
  "Type": "ParamImplNode"
}##/>
  <Connection
    ID = "21039"
    OutSlotNodeID = "10562"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
  <Connection
    ID = "21049"
    OutSlotNodeID = "10421"
    OutSlotIndex = "0"
    InSlotNodeID = "10562"
    InSlotIndex = "1"/>
  <Connection
    ID = "21056"
    OutSlotNodeID = "10431"
    OutSlotIndex = "0"
    InSlotNodeID = "10562"
    InSlotIndex = "2"/>
  <Connection
    ID = "21060"
    OutSlotNodeID = "10366"
    OutSlotIndex = "0"
    InSlotNodeID = "10562"
    InSlotIndex = "0"/>
  <Connection
    ID = "21063"
    OutSlotNodeID = "10578"
    OutSlotIndex = "0"
    InSlotNodeID = "10528"
    InSlotIndex = "2"/>
  <Connection
    ID = "21072"
    OutSlotNodeID = "10578"
    OutSlotIndex = "0"
    InSlotNodeID = "10495"
    InSlotIndex = "2"/>
  <Connection
    ID = "21091"
    OutSlotNodeID = "10578"
    OutSlotIndex = "0"
    InSlotNodeID = "10529"
    InSlotIndex = "2"/>
  <Connection
    ID = "21105"
    OutSlotNodeID = "10578"
    OutSlotIndex = "0"
    InSlotNodeID = "10544"
    InSlotIndex = "2"/>
  <Connection
    ID = "21112"
    OutSlotNodeID = "10495"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "2"/>
  <Connection
    ID = "21114"
    OutSlotNodeID = "10529"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "3"/>
  <Connection
    ID = "21123"
    OutSlotNodeID = "10544"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "4"/>
  <Connection
    ID = "21135"
    OutSlotNodeID = "10528"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "1"/>
  <Connection
    ID = "21148"
    OutSlotNodeID = "10591"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "0"/>
  <Connection
    ID = "21167"
    OutSlotNodeID = "10602"
    OutSlotIndex = "0"
    InSlotNodeID = "10421"
    InSlotIndex = "2"/>
  <Connection
    ID = "21168"
    OutSlotNodeID = "10602"
    OutSlotIndex = "0"
    InSlotNodeID = "10431"
    InSlotIndex = "2"/>
</NodeGraph>
<StbProperty ApplyMode = "0" ExtractionMode = "1">
  <Param ParamType = "CrossEditor.AnimParameter_Vector3" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0,
    "Z": 0.0
  },
  "Type": "Vector3",
  "Name": "FrontWheelSteering"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector3" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0,
    "Z": 0.0
  },
  "Type": "Vector3",
  "Name": "WheelSpin"
}##/>
</StbProperty>
