adnj
{
    "Guid": "74b6a473f54f4f9a9b4d3599f75e7544",
    "Version": 5,
    "ClassID": 5,
    "DataSize": 10950,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "360d22289e2c24b92c4cdee1a3914b98",
        "894744095b40d888c8467bfcfe0ff567"
    ]
}
{
    "parent": {
        "ASSET_MAGIC_NUMBER": 778986593,
        "Path": "360d22289e2c24b92c4cdee1a3914b98"
    },
    "version": 2,
    "properties": {
        "DOUBLE_SIDED": true,
        "_DoubleSidedConstants": [
            -1.0,
            -1.0,
            -1.0,
            -1.0
        ],
        "Param26871": [
            0.9553767442703247,
            1.0,
            0.8251748085021973,
            1.0
        ],
        "Param22524": "894744095b40d888c8467bfcfe0ff567",
        "Param21576": [
            0.34445568919181826,
            0.440559446811676,
            0.11707176268100739,
            1.0
        ],
        "Param3652": [
            0.22576241195201875,
            0.3076923191547394,
            0.09252285957336426,
            1.0
        ],
        "Param18698": [
            1.2999999523162842
        ],
        "Param12851": [
            0.699999988079071
        ]
    },
    "state": {
        "forward": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": true,
                        "EnableLogicOp": false,
                        "SrcBlend": 5,
                        "DestBlend": 6,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 5,
                        "DestBlendAlpha": 6,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    },
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 0
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": false,
                "DepthCompareOp": 7,
                "EnableStencil": false,
                "StencilReadMask": 0,
                "StencilWriteMask": 0,
                "FrontFace": {
                    "StencilFailOp": 0,
                    "StencilDepthFailOp": 0,
                    "StencilPassOp": 0,
                    "StencilCompareOp": 0
                },
                "BackFace": {
                    "StencilFailOp": 0,
                    "StencilDepthFailOp": 0,
                    "StencilPassOp": 0,
                    "StencilCompareOp": 0
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 0,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 0
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 3500
        },
        "gpass": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    },
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    },
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": true,
                "DepthCompareOp": 7,
                "EnableStencil": false,
                "StencilReadMask": 255,
                "StencilWriteMask": 255,
                "FrontFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                },
                "BackFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 0,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 0
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 2000
        },
        "VSMDepth": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": true,
                "DepthCompareOp": 2,
                "EnableStencil": false,
                "StencilReadMask": 255,
                "StencilWriteMask": 255,
                "FrontFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                },
                "BackFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 0,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 0
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 2000
        },
        "shadow_all": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": true,
                "DepthCompareOp": 2,
                "EnableStencil": false,
                "StencilReadMask": 255,
                "StencilWriteMask": 255,
                "FrontFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                },
                "BackFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 0,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 0
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 2000
        }
    },
    "defines": {
        "BlendModeEnable": false,
        "BlendMode": 0,
        "ShadingModelEnable": false,
        "ShadingModel": 0,
        "TwoSidedEnable": true,
        "TwoSided": true,
        "RenderGroupBias": 0
    }
}