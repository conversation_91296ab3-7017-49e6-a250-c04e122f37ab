adnj
{
    "Guid": "ab0b99cb957e1391644c1a36804ec558",
    "Version": 5,
    "ClassID": 5,
    "DataSize": 10932,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "360d22289e2c24b92c4cdee1a3914b98",
        "894744095b40d888c8467bfcfe0ff567"
    ]
}
{
    "parent": {
        "ASSET_MAGIC_NUMBER": 778986593,
        "Path": "360d22289e2c24b92c4cdee1a3914b98"
    },
    "version": 2,
    "properties": {
        "DOUBLE_SIDED": true,
        "_DoubleSidedConstants": [
            -1.0,
            -1.0,
            -1.0,
            -1.0
        ],
        "Param26871": [
            0.9553767442703247,
            1.0,
            0.8251748085021973,
            1.0
        ],
        "Param22524": "894744095b40d888c8467bfcfe0ff567",
        "Param21576": [
            0.8143771886825562,
            0.867132842540741,
            0.2668101489543915,
            1.0
        ],
        "Param3652": [
            0.7352443933486939,
            0.7622377872467041,
            0.17590101063251496,
            1.0
        ],
        "Param18698": [
            1.0
        ],
        "Param12851": [
            0.699999988079071
        ]
    },
    "state": {
        "forward": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": true,
                        "EnableLogicOp": false,
                        "SrcBlend": 5,
                        "DestBlend": 6,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 5,
                        "DestBlendAlpha": 6,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    },
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 0
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": false,
                "DepthCompareOp": 7,
                "EnableStencil": false,
                "StencilReadMask": 0,
                "StencilWriteMask": 0,
                "FrontFace": {
                    "StencilFailOp": 0,
                    "StencilDepthFailOp": 0,
                    "StencilPassOp": 0,
                    "StencilCompareOp": 0
                },
                "BackFace": {
                    "StencilFailOp": 0,
                    "StencilDepthFailOp": 0,
                    "StencilPassOp": 0,
                    "StencilCompareOp": 0
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 0,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 0
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 3500
        },
        "gpass": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    },
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    },
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": true,
                "DepthCompareOp": 7,
                "EnableStencil": false,
                "StencilReadMask": 255,
                "StencilWriteMask": 255,
                "FrontFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                },
                "BackFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 0,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 0
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 2000
        },
        "VSMDepth": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": true,
                "DepthCompareOp": 2,
                "EnableStencil": false,
                "StencilReadMask": 255,
                "StencilWriteMask": 255,
                "FrontFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                },
                "BackFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 0,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 0
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 2000
        },
        "shadow_all": {
            "BlendStateDesc": {
                "EnableAlphaToCoverage": false,
                "EnableIndependentBlend": false,
                "TargetBlendStateVector": [
                    {
                        "EnableBlend": false,
                        "EnableLogicOp": false,
                        "SrcBlend": 1,
                        "DestBlend": 2,
                        "BlendOp": 1,
                        "SrcBlendAlpha": 1,
                        "DestBlendAlpha": 2,
                        "BlendOpAlpha": 1,
                        "LogicOp": 5,
                        "WriteMask": 15
                    }
                ]
            },
            "DepthStencilStateDesc": {
                "EnableDepth": true,
                "EnableDepthWrite": true,
                "DepthCompareOp": 2,
                "EnableStencil": false,
                "StencilReadMask": 255,
                "StencilWriteMask": 255,
                "FrontFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                },
                "BackFace": {
                    "StencilFailOp": 1,
                    "StencilDepthFailOp": 1,
                    "StencilPassOp": 1,
                    "StencilCompareOp": 8
                }
            },
            "RasterizationStateDesc": {
                "FillMode": 2,
                "CullMode": 0,
                "FaceOrder": 1,
                "EnableDepthClip": true,
                "EnableAntialiasedLine": false,
                "EnableDepthBias": false,
                "DepthBias": 0,
                "SlopeScaledDepthBias": 0.0,
                "DepthBiasClamp": 0.0,
                "ForcedSampleCount": 0,
                "LineWidth": 0.0,
                "RasterMode": 2,
                "RasterOverestimationSize": 0
            },
            "DynamicStateDesc": {
                "StencilReference": 0
            },
            "RenderGroup": 2000
        }
    },
    "defines": {
        "BlendModeEnable": false,
        "BlendMode": 0,
        "ShadingModelEnable": false,
        "ShadingModel": 0,
        "TwoSidedEnable": true,
        "TwoSided": true,
        "RenderGroupBias": 0
    }
}