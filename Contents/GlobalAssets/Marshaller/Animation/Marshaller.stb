<NodeGraph
  Name = "Marshaller"
  NodeID = "10369"
  ConnectionID = "20458"
  HashName = "22337033441771767433">
  <Camera MaxZoom = "50" MinZoom = "-10" ZoomCount = "-1">
    <Viewport
      _WorldX = "-542.81226"
      _WorldY = "-672.96124"
      _Height = "1472.5"
      _AspectRatio = "1.6349745"/>
    <Viewport
      _WorldX = "969"
      _WorldY = "80"
      _Height = "1178"
      _AspectRatio = "1.6349745"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "920"
    Y = "-190"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20195"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Marshaller/Animation/Anim_Marshaller_Stop_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10061"
    X = "-130"
    Y = "280"
    NodeJsonData = ##{
  "CompositePath": "201d3efbb76d25903f4879b3d40237b6",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10061",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_SwitchPosesByIntNode
    NumPose = "6"
    ID = "10138"
    X = "500"
    Y = "-190"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20243",
    "20448",
    "20446",
    "20338",
    "20350",
    "20334"
  ],
  "InParamLinks": [
    "20223"
  ],
  "Name": "10138",
  "Type": "SwitchPosesByIntNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Marshaller/Animation/Anim_Marshaller_Idle_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10145"
    X = "-130"
    Y = "-150"
    NodeJsonData = ##{
  "CompositePath": "ed59ec42c33c8681cb488f043e8e1482",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10145",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_ParamImplNode
    ParamName = "MarshallingSignal"
    ParamType = "Int"
    ID = "10160"
    X = "30"
    Y = "-250"
    NodeJsonData = ##{
  "InParams": [
    "MarshallingSignal"
  ],
  "ReturnType": "Int",
  "Name": "10160",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Marshaller/Animation/Anim_Marshaller_FacingMe_Take 001_ANIM.nda"
    Loop = "false"
    ID = "10178"
    X = "-160"
    Y = "180"
    NodeJsonData = ##{
  "CompositePath": "a57cf534d4dd0fbd5e4d9e81813256ac",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": false,
  "InParamLinks": [
    ""
  ],
  "Name": "10178",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Marshaller/Animation/Anim_Marshaller_StraightAhead_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10181"
    X = "-190"
    Y = "90"
    NodeJsonData = ##{
  "CompositePath": "245e36112b7315878847941c89ad16ed",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10181",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Marshaller/Animation/Anim_Marshaller_TurnLeft_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10260"
    X = "-150"
    Y = "-70"
    NodeJsonData = ##{
  "CompositePath": "8aa1d20cbcea3aafe34c7f32a160ace6",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10260",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Marshaller/Animation/Anim_Marshaller_TurnRight_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10358"
    X = "-160"
    Y = "10"
    NodeJsonData = ##{
  "CompositePath": "6f2f954867655ab29e481d654584452b",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10358",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Connection
    ID = "20195"
    OutSlotNodeID = "10138"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
  <Connection
    ID = "20223"
    OutSlotNodeID = "10160"
    OutSlotIndex = "0"
    InSlotNodeID = "10138"
    InSlotIndex = "0"/>
  <Connection
    ID = "20243"
    OutSlotNodeID = "10145"
    OutSlotIndex = "0"
    InSlotNodeID = "10138"
    InSlotIndex = "1"/>
  <Connection
    ID = "20334"
    OutSlotNodeID = "10061"
    OutSlotIndex = "0"
    InSlotNodeID = "10138"
    InSlotIndex = "6"/>
  <Connection
    ID = "20338"
    OutSlotNodeID = "10181"
    OutSlotIndex = "0"
    InSlotNodeID = "10138"
    InSlotIndex = "4"/>
  <Connection
    ID = "20350"
    OutSlotNodeID = "10178"
    OutSlotIndex = "0"
    InSlotNodeID = "10138"
    InSlotIndex = "5"/>
  <Connection
    ID = "20446"
    OutSlotNodeID = "10358"
    OutSlotIndex = "0"
    InSlotNodeID = "10138"
    InSlotIndex = "3"/>
  <Connection
    ID = "20448"
    OutSlotNodeID = "10260"
    OutSlotIndex = "0"
    InSlotNodeID = "10138"
    InSlotIndex = "2"/>
</NodeGraph>
<StbProperty ApplyMode = "0" ExtractionMode = "0">
  <Param ParamType = "CrossEditor.AnimParameter_Int" ParamJson = ##{
  "Value": 0,
  "Type": "Int",
  "Name": "MarshallingSignal"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Bool" ParamJson = ##{
  "Value": false,
  "Type": "Bool",
  "Name": "IsWalking"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 0.1,
  "Type": "Float",
  "Name": "CameraDistance"
}##/>
</StbProperty>
