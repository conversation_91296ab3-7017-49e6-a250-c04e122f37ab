adnj
{
    "Guid": "3a62fc6911b0a4924647291514fed75a",
    "Version": 5,
    "ClassID": 36,
    "DataSize": 5713,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "201d3efbb76d25903f4879b3d40237b6",
        "245e36112b7315878847941c89ad16ed",
        "6f2f954867655ab29e481d654584452b",
        "8aa1d20cbcea3aafe34c7f32a160ace6",
        "a57cf534d4dd0fbd5e4d9e81813256ac",
        "ed59ec42c33c8681cb488f043e8e1482"
    ]
}
{
    "StoryBoard": {
        "RootMotionMode": {
            "ExtractMode": "DoNotExtract",
            "ApplyMode": "Apply"
        },
        "Parameters": [
            {
                "Value": 0,
                "Type": "Int",
                "Name": "<PERSON>ingSignal"
            },
            {
                "Value": false,
                "Type": "Bool",
                "Name": "IsWalking"
            },
            {
                "Value": 0.1,
                "Type": "Float",
                "Name": "CameraDistance"
            }
        ],
        "Name": "22337033441771767433",
        "Nodes": [
            {
                "InPoseLinks": [
                    "20195"
                ],
                "Name": "10000",
                "Type": "RootNode"
            },
            {
                "CompositePath": "201d3efbb76d25903f4879b3d40237b6",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    ""
                ],
                "Name": "10061",
                "Type": "PlayCompositeNode"
            },
            {
                "InPoseLinks": [
                    "20243",
                    "20448",
                    "20446",
                    "20338",
                    "20350",
                    "20334"
                ],
                "InParamLinks": [
                    "20223"
                ],
                "Name": "10138",
                "Type": "SwitchPosesByIntNode"
            },
            {
                "CompositePath": "ed59ec42c33c8681cb488f043e8e1482",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    ""
                ],
                "Name": "10145",
                "Type": "PlayCompositeNode"
            },
            {
                "InParams": [
                    "MarshallingSignal"
                ],
                "ReturnType": "Int",
                "Name": "10160",
                "Type": "ParamImplNode"
            },
            {
                "CompositePath": "a57cf534d4dd0fbd5e4d9e81813256ac",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": false,
                "InParamLinks": [
                    ""
                ],
                "Name": "10178",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "245e36112b7315878847941c89ad16ed",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    ""
                ],
                "Name": "10181",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "8aa1d20cbcea3aafe34c7f32a160ace6",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    ""
                ],
                "Name": "10260",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "6f2f954867655ab29e481d654584452b",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    ""
                ],
                "Name": "10358",
                "Type": "PlayCompositeNode"
            }
        ],
        "Links": [
            {
                "Name": "20195",
                "Type": "LocalPoseLink",
                "TargetNode": "10138",
                "SourceNode": "10000"
            },
            {
                "Name": "20223",
                "Type": "ParamImplLink<int>",
                "TargetNode": "10160",
                "SourceNode": "10138"
            },
            {
                "Name": "20243",
                "Type": "LocalPoseLink",
                "TargetNode": "10145",
                "SourceNode": "10138"
            },
            {
                "Name": "20334",
                "Type": "LocalPoseLink",
                "TargetNode": "10061",
                "SourceNode": "10138"
            },
            {
                "Name": "20338",
                "Type": "LocalPoseLink",
                "TargetNode": "10181",
                "SourceNode": "10138"
            },
            {
                "Name": "20350",
                "Type": "LocalPoseLink",
                "TargetNode": "10178",
                "SourceNode": "10138"
            },
            {
                "Name": "20446",
                "Type": "LocalPoseLink",
                "TargetNode": "10358",
                "SourceNode": "10138"
            },
            {
                "Name": "20448",
                "Type": "LocalPoseLink",
                "TargetNode": "10260",
                "SourceNode": "10138"
            }
        ]
    }
}