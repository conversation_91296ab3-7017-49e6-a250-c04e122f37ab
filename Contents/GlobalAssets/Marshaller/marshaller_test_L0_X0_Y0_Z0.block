adnj
{
    "Guid": "8d05bd16326f6b99014bc6ae37f7b871",
    "Version": 5,
    "ClassID": 26,
    "DataSize": 31096,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "81375b2984621416ea2ff11bdd29c155",
        "8d5e163b84292ba7f24c9a1f76ce72a1",
        "8f36519c8b19c4ba2b7df0e9d8b31613",
        "9b0219907dbd546f085e7b954d9bd351",
        "Contents/lua/ZGSZ_TODLight.lua",
        "EngineResource/lua/CameraController.lua"
    ]
}
{
    "ecs": {
        "entities": {
            "75af4e9878894449da3a1b0ca5862627": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "75af4e9878894449da3a1b0ca5862627",
                "name": "Scene",
                "prototype": 15688464349044837294,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mUnesedPadding": 0,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    }
                },
                "children": [
                    "a0c03346489444c8ba9a11b4a6665f94"
                ],
                "rootParent": "e372af75000000001823c72200000000"
            },
            "a0c03346489444c8ba9a11b4a6665f94": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "a0c03346489444c8ba9a11b4a6665f94",
                "name": "GroundCube",
                "prototype": 2891678161357226382,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 35,
                            "mUnesedPadding": 0,
                            "mScale": {
                                "x": 39.99999999999999,
                                "y": 0.10000000149011602,
                                "z": 39.99999999999999
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mModels": [
                            {
                                "mSubModelProperties": [
                                    {
                                        "mMaterial": "81375b2984621416ea2ff11bdd29c155",
                                        "mVisible": true
                                    }
                                ],
                                "mAsset": "9b0219907dbd546f085e7b954d9bd351",
                                "mVisible": true,
                                "mReceiveDecals": true
                            }
                        ],
                        "mEnableGPUSkin": false,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mEnableSimulate": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mOnlyUseExtraCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 4,
                        "mCollisionMask": 1,
                        "mMaterialType": 0,
                        "mExtraCollision": null,
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 524288
                        },
                        "mNeedVoxelized": false,
                        "componentHash": 2559651570
                    }
                },
                "children": []
            },
            "dcf8363703401419bb08b4b34ad0eb40": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "dcf8363703401419bb08b4b34ad0eb40",
                "name": "GameCamera",
                "prototype": 12814079423598367123,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 52.************,
                                "y": 287.1202573318733,
                                "z": 948.5552309806348
                            },
                            "mTRSFlag": 3,
                            "mUnesedPadding": 0,
                            "mScale": {
                                "x": 1.0000000000000009,
                                "y": 0.9999999999999998,
                                "z": 1.0000000000000005
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 1.1096569118619192e-16,
                                "y": -0.9868680067255208,
                                "z": 0.1615287506965794,
                                "w": -4.064667052978321e-17
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::CameraComponentG": {
                        "mCameraInfo": {
                            "mAspectRatio": 1.7777777910232545,
                            "mFov": 1.0,
                            "mFocalLength": 21.965852737426759,
                            "mMinFocalLength": 4.0,
                            "mMaxFocalLength": 1000.0,
                            "mSensorWidth": 42.66666793823242,
                            "mSensorHeight": 24.0,
                            "mFarPlane": 10000000.0,
                            "mNearPlane": 100.0,
                            "mWidth": 1080.0,
                            "mHeight": 720.0,
                            "mOrthNearPlane": 10.0,
                            "mOrthFarPlane": 10000.0,
                            "mNormalBias": 0.0,
                            "mDepthBias": 0.0,
                            "mJitterIntensity": 1.0
                        },
                        "mCameraMask": 4294967295,
                        "mEnable": true,
                        "mProjectionMode": 0,
                        "mTargetWidth": 800,
                        "mTargetHeight": 800,
                        "mRenderTexturePath": "",
                        "componentHash": 2143649290
                    },
                    "cross::ScriptComponentG": {
                        "ScriptPath": "EngineResource/lua/CameraController.lua",
                        "EnableUpdate": true,
                        "ScriptProperties": null,
                        "componentHash": 1186449325
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    }
                },
                "children": [],
                "rootParent": "e372af75000000001823c72200000000"
            },
            "c1ce8b3e70b5845f6a909a328d113977": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "c1ce8b3e70b5845f6a909a328d113977",
                "name": "Env_Light",
                "prototype": 15688464349044837294,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 3,
                            "mUnesedPadding": 0,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    }
                },
                "children": [
                    "b2742605b6f57458987b176f524be1d3",
                    "bc37da904f20a47259bfca4648379abf",
                    "60c9b1d2f9ebd48d1a75b4afda82d969"
                ],
                "rootParent": "e372af75000000001823c72200000000"
            },
            "b2742605b6f57458987b176f524be1d3": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "b2742605b6f57458987b176f524be1d3",
                "name": "SkyLight",
                "prototype": 7624450702474648443,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 3,
                            "mUnesedPadding": 0,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::SkyLightComponentG": {
                        "Enable": true,
                        "RealTimeCapture": true,
                        "RealTimeCaptureSliceCount": 10,
                        "LightColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "GISkyLightIntensity": 1.0,
                        "LightMapIntensityDebug": 0.0,
                        "mDiffuseProbe": [
                            {
                                "x": -0.003044300014153123,
                                "y": -0.19617418944835664,
                                "z": 0.08162236958742142,
                                "w": 0.3903099000453949
                            },
                            {
                                "x": -0.0013626383151859046,
                                "y": -0.18576791882514954,
                                "z": 0.0951475203037262,
                                "w": 0.5076125264167786
                            },
                            {
                                "x": 0.002345460932701826,
                                "y": -0.1728866845369339,
                                "z": 0.13147644698619843,
                                "w": 0.7257214188575745
                            },
                            {
                                "x": 0.005995072424411774,
                                "y": -0.1167006567120552,
                                "z": -0.04524802416563034,
                                "w": -0.0023408227134495975
                            },
                            {
                                "x": 0.004320001229643822,
                                "y": -0.11656857281923294,
                                "z": -0.06405702233314514,
                                "w": -0.0018469634233042598
                            },
                            {
                                "x": 0.0011339911725372077,
                                "y": -0.11691846698522568,
                                "z": -0.09014725685119629,
                                "w": -0.000046341407141881066
                            },
                            {
                                "x": -0.04115375876426697,
                                "y": -0.039200957864522937,
                                "z": -0.03698145970702171,
                                "w": 1.0
                            }
                        ],
                        "SpecularProbe": "46d68ad3dea8d43beabd7284579f6ccf",
                        "SkyLightIntensity": 0.0,
                        "IsLowerHemisphereColor": true,
                        "TODLowerHemisphereColor": true,
                        "LowerHemisphereColor": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 1.0
                        },
                        "EnableSmartGISample": true,
                        "EnableSpecular": false,
                        "EnableSSGI": true,
                        "RayLength": 100.0,
                        "RayLength_Foliage": 20.0,
                        "ThresholdScale": 1.5,
                        "MinimalAO": 0.0,
                        "IntensityScale": 1.0,
                        "DepthThresholdNear": 5.0,
                        "DepthThresholdFar": 5.0,
                        "DepthThresholdNear_Foliage": 5.0,
                        "DepthThresholdFar_Foliage": 5.0,
                        "AOFactor": 2.0,
                        "SSGIScale": 1.0,
                        "componentHash": 3852407668
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    }
                },
                "children": []
            },
            "bc37da904f20a47259bfca4648379abf": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "bc37da904f20a47259bfca4648379abf",
                "name": "DirectionalLight",
                "prototype": 2929997794251265734,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -41.30840757572323,
                                "y": 530.5092560182251,
                                "z": -91.78252611699084
                            },
                            "mTRSFlag": 35,
                            "mUnesedPadding": 0,
                            "mScale": {
                                "x": 1.0000000000000005,
                                "y": 0.9999999999999998,
                                "z": 0.9999999999999998
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.2223797168632504,
                                "y": -0.882101433296255,
                                "z": 0.2001636756878419,
                                "w": 0.3638390108820735
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::TODLightComponentG": {
                        "config": {
                            "Year": 2023,
                            "Month": 1,
                            "Day": 10,
                            "Hour": 7,
                            "Minute": 52,
                            "Second": 46
                        },
                        "componentHash": 3512910319
                    },
                    "cross::LightComponentG": {
                        "mLightData": {
                            "mType": 0,
                            "mColor": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mIntensity": 6.0,
                            "mPrtIntensity": 1.0,
                            "mVolumetricFactor": 1.0,
                            "mSourceAngle": 0.7357000112533569,
                            "mRange": 1.0,
                            "mInnerConeHorAngle": 1.0471975803375245,
                            "mInnerConeVerAngle": 1.0471975803375245,
                            "mOuterConeHorAngle": 1.0471975803375245,
                            "mOuterConeVerAngle": 1.0471975803375245,
                            "mConeFadeIntensity": 1.0,
                            "mConeOverFlowLength": 0.0,
                            "mSpotDistanceExp": 1.0,
                            "mCastShadow": true,
                            "mCastScreenSpaceShadow": true,
                            "mShadowStrength": 1.0,
                            "mSourceWidth": 1.0,
                            "mSourceHeight": 1.0,
                            "mBarnDoorAngle": 1.0,
                            "mBarnDoorLength": 1.0
                        },
                        "mEnable": true,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 3,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.5,
                        "mShadowSlopeBias": 0.5,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": true,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0
                        },
                        "componentHash": 594056575
                    },
                    "cross::ScriptComponentG": {
                        "ScriptPath": "Contents/lua/ZGSZ_TODLight.lua",
                        "EnableUpdate": true,
                        "ScriptProperties": null,
                        "componentHash": 1186449325
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    }
                },
                "children": []
            },
            "60c9b1d2f9ebd48d1a75b4afda82d969": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "60c9b1d2f9ebd48d1a75b4afda82d969",
                "name": "Atmosphere",
                "prototype": 15006964057727017357,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mUnesedPadding": 0,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::SkyAtmosphereComponentG": {
                        "config": {
                            "MiePhase": 0.800000011920929,
                            "MieScattCoeff": {
                                "x": 0.5764706134796143,
                                "y": 0.5764706134796143,
                                "z": 0.5764706134796143
                            },
                            "MieScattScale": 0.006920000072568655,
                            "MieAbsorCoeff": {
                                "x": 0.5764706134796143,
                                "y": 0.5764706134796143,
                                "z": 0.5764706134796143
                            },
                            "MieAbsorScale": 0.0007699999841861427,
                            "RayScattCoeff": {
                                "x": 0.16078431904315949,
                                "y": 0.37254902720451357,
                                "z": 0.9137254953384399
                            },
                            "RayScattScale": 0.036240000277757648,
                            "AbsorptiCoeff": {
                                "x": 0.32549020648002627,
                                "y": 0.9450980424880981,
                                "z": 0.04313725605607033
                            },
                            "AbsorptiScale": 0.001990000018849969,
                            "PlanetRadius": 6375.0,
                            "AtmosHeight": 60.0,
                            "MieScaleHeight": 1.2000000476837159,
                            "RayScaleHeight": 8.0,
                            "GroundAlbedo3": {
                                "x": 0.0010000000474974514,
                                "y": 0.0010000000474974514,
                                "z": 0.0010000000474974514
                            },
                            "SFogMieScattScale": 0.05000000074505806
                        },
                        "outerParam": {
                            "PlanetTopAtWorldOrigin": true,
                            "RenderSunDisk": true,
                            "RenderMoonDisk": true,
                            "MoonPhase": false,
                            "AerialPerspStartDepthKM": 1.0,
                            "Exposure": 1.0,
                            "APScale": 1.0,
                            "HighQualityAP": false,
                            "ColorTransmittance": 1.0
                        },
                        "componentHash": 2589265039
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    }
                },
                "children": []
            },
            "b5a200b5d43d2598b74b4f2068567c7f": {
                "prefabId": "8d5e163b84292ba7f24c9a1f76ce72a1",
                "prefabEuid": "14b9c4ff2b633ea98e4d35b799d4a06f",
                "euid": "b5a200b5d43d2598b74b4f2068567c7f",
                "name": "01_General_Marshaller",
                "prototype": 6067459531198423532,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "y": 5.0
                            }
                        }
                    }
                },
                "children": [
                    "7e05bde10ff13899484f3aed8d0dc929",
                    "6de0cc432291269ebe4fe364f6fed9de"
                ],
                "rootParent": "e372af75000000001823c72200000000"
            },
            "7e05bde10ff13899484f3aed8d0dc929": {
                "prefabId": "8d5e163b84292ba7f24c9a1f76ce72a1",
                "prefabEuid": "bb73d822ff8a76be40485dbb5340bd97",
                "euid": "7e05bde10ff13899484f3aed8d0dc929",
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mScale": {
                                "x": 0.9999998211860657,
                                "z": 0.9999998211860657
                            }
                        }
                    },
                    "cross::SkeltSocketComponentG": {
                        "TRS": {
                            "mRotation": {
                                "y": 0.9659257531166077
                            }
                        }
                    }
                },
                "children": []
            },
            "6de0cc432291269ebe4fe364f6fed9de": {
                "prefabId": "8d5e163b84292ba7f24c9a1f76ce72a1",
                "prefabEuid": "dbc5461ffb26a1844e4ffa9c3cc3d5ca",
                "euid": "6de0cc432291269ebe4fe364f6fed9de",
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -31.759929656982423,
                                "z": -10.756800651550293
                            }
                        }
                    }
                },
                "children": []
            }
        }
    }
}