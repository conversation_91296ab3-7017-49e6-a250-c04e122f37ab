<NodeGraph
  Name = "GroundCrew"
  NodeID = "10124"
  ConnectionID = "20208"
  HashName = "32509688401675724718">
  <Camera MaxZoom = "5" MinZoom = "-10" ZoomCount = "0">
    <Viewport
      _WorldX = "-971"
      _WorldY = "-377"
      _Height = "942"
      _AspectRatio = "1.5700637"/>
    <Viewport
      _WorldX = "755"
      _WorldY = "80"
      _Height = "942"
      _AspectRatio = "1.5700637"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "500"
    Y = "240"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20085"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_SwitchPosesByIntNode
    NumPose = "5"
    ID = "10010"
    X = "-170"
    Y = "260"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20014",
    "20030",
    "20037",
    "20056",
    "20072"
  ],
  "InParamLinks": [
    "20000"
  ],
  "Name": "10010",
  "Type": "SwitchPosesByIntNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "GroundCrewSignal"
    ParamType = "Int"
    ID = "10025"
    X = "-610"
    Y = "110"
    NodeJsonData = ##{
  "InParams": [
    "GroundCrewSignal"
  ],
  "ReturnType": "Int",
  "Name": "10025",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Marshaller/Animation/Anim_Marshaller_Idle_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10040"
    X = "-620"
    Y = "230"
    NodeJsonData = ##{
  "CompositePath": "ed59ec42c33c8681cb488f043e8e1482",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10040",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Marshaller/Animation/Anim_Marshaller_Walk_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10045"
    X = "-620"
    Y = "300"
    NodeJsonData = ##{
  "CompositePath": "7430c960ab2b76bb3740204c9e96974b",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10045",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/GroundCrew/Animation/Anim_Marshaller_Turn_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10053"
    X = "-620"
    Y = "370"
    NodeJsonData = ##{
  "CompositePath": "77fd363e982def96c448e82f1efe98e8",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10053",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/GroundCrew/Animation/Anim_Marshaller_ByeBye_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10062"
    X = "-620"
    Y = "450"
    NodeJsonData = ##{
  "CompositePath": "dd74532f416c3db2814577db76de852e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10062",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/GroundCrew/Animation/Anim_Marshaller_Turn_Take 001_ANIM.nda"
    Loop = "true"
    ID = "10068"
    X = "-620"
    Y = "520"
    NodeJsonData = ##{
  "CompositePath": "77fd363e982def96c448e82f1efe98e8",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10068",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_BlendByLayeredFilterNode
    ID = "10069"
    X = "170"
    Y = "100"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20164",
    "20178",
    "20193"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "bip001",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "bip001 pelvis",
          "Depth": 1
        },
        {
          "BoneName": "bip001 pelvis",
          "Depth": 1
        },
        {
          "BoneName": "bip001 spine",
          "Depth": 1
        },
        {
          "BoneName": "bip001 spinel",
          "Depth": 1
        },
        {
          "BoneName": "bip001 spine2",
          "Depth": 1
        },
        {
          "BoneName": "bip001 neck",
          "Depth": 1
        },
        {
          "BoneName": "bip001 1 clavicle",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l upperarm",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l forearm",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l hand",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger0",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger01",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger02",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger1",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger11",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger12",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger3",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger31",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger32",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger4",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger41",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger42",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r clavicle",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r upperarm",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r forearm",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r hand",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger0",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger01",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger02",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger1",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger11",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger12",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger2",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger21",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger22",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger3",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger31",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger32",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger4",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger41",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r finger42",
          "Depth": 1
        },
        {
          "BoneName": "bip001 head",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l thigh",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l calf",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l foot",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l toe0",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r thigh",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r calf",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r foot",
          "Depth": 1
        },
        {
          "BoneName": "bip001 r toe0",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger2",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger21",
          "Depth": 1
        },
        {
          "BoneName": "bip001 l finger22",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10069",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "bip001"
    ID = "10084"
    X = "-160"
    Y = "20"
    NodeJsonData = ##{
  "BoneName": "bip001",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "20114",
    ""
  ],
  "Name": "10084",
  "Type": "TransformBoneNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "GroundCrewDirection"
    ParamType = "Vector3"
    ID = "10089"
    X = "-780"
    Y = "-40"
    NodeJsonData = ##{
  "InParams": [
    "GroundCrewDirection"
  ],
  "ReturnType": "Vector3",
  "Name": "10089",
  "Type": "ParamImplNode"
}##/>
  <Anim_SlotNode
    SlotName = "DefaultSlot"
    GroupName = "DefaultGroup"
    ID = "10117"
    X = "-130"
    Y = "-150"
    NodeJsonData = ##{
  "InPoseLinks": [],
  "SlotName": "DefaultSlot",
  "Name": "10117",
  "Type": "SlotNode"
}##/>
  <Connection
    ID = "20000"
    OutSlotNodeID = "10025"
    OutSlotIndex = "0"
    InSlotNodeID = "10010"
    InSlotIndex = "0"/>
  <Connection
    ID = "20014"
    OutSlotNodeID = "10040"
    OutSlotIndex = "0"
    InSlotNodeID = "10010"
    InSlotIndex = "1"/>
  <Connection
    ID = "20030"
    OutSlotNodeID = "10045"
    OutSlotIndex = "0"
    InSlotNodeID = "10010"
    InSlotIndex = "2"/>
  <Connection
    ID = "20037"
    OutSlotNodeID = "10053"
    OutSlotIndex = "0"
    InSlotNodeID = "10010"
    InSlotIndex = "3"/>
  <Connection
    ID = "20056"
    OutSlotNodeID = "10062"
    OutSlotIndex = "0"
    InSlotNodeID = "10010"
    InSlotIndex = "4"/>
  <Connection
    ID = "20072"
    OutSlotNodeID = "10068"
    OutSlotIndex = "0"
    InSlotNodeID = "10010"
    InSlotIndex = "5"/>
  <Connection
    ID = "20085"
    OutSlotNodeID = "10069"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
  <Connection
    ID = "20114"
    OutSlotNodeID = "10089"
    OutSlotIndex = "0"
    InSlotNodeID = "10084"
    InSlotIndex = "2"/>
  <Connection
    ID = "20164"
    OutSlotNodeID = "10117"
    OutSlotIndex = "0"
    InSlotNodeID = "10069"
    InSlotIndex = "0"/>
  <Connection
    ID = "20178"
    OutSlotNodeID = "10084"
    OutSlotIndex = "0"
    InSlotNodeID = "10069"
    InSlotIndex = "1"/>
  <Connection
    ID = "20193"
    OutSlotNodeID = "10010"
    OutSlotIndex = "0"
    InSlotNodeID = "10069"
    InSlotIndex = "2"/>
</NodeGraph>
<StbProperty ApplyMode = "1" ExtractionMode = "0">
  <Param ParamType = "CrossEditor.AnimParameter_Int" ParamJson = ##{
  "Value": 0,
  "Type": "Int",
  "Name": "GroundCrewSignal"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector3" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0,
    "Z": 0.0
  },
  "Type": "Vector3",
  "Name": "GroundCrewDirection"
}##/>
</StbProperty>
