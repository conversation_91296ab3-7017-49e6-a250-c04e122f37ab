<?xml version="1.0"?>

<!--
  File:     Continental A-65-8.xml
  Author:   AeromatiC++ v 3.1.1

  See: http://wiki.flightgear.org/JSBSim_Engines#FGPiston

  Inputs:
    name:           Continental A-65-8
    type:           Piston Engine
    power:          65 hp
-->

<piston_engine name="Continental A-65-8">
  <minmp unit="INHG">         10.0 </minmp>
  <maxmp unit="INHG">         28.5 </maxmp>
    <displacement unit="IN3"> 171.0 </displacement>
  <maxhp>        65 </maxhp>
  <cycles>         4.0 </cycles>
  <idlerpm>      700.0 </idlerpm>
  <maxrpm>      2800.0 </maxrpm>
  <sparkfaildrop>  0.1 </sparkfaildrop>
  <volumetric-efficiency> 0.85 </volumetric-efficiency>
  <man-press-lag> 0.1 </man-press-lag>
  <static-friction  unit="HP"> 0.325 </static-friction>
  <starter-torque> 52 </starter-torque>
  <starter-rpm> 1400 </starter-rpm>
 <!-- Defining <bsfc> over-rides the built-in horsepower calculations -->
 <!--<bsfc>           0.45 </bsfc>-->
  <stroke unit="IN">  3.625 </stroke>
  <bore unit="IN">    3.875 </bore>
  <cylinders>         4  </cylinders>
  <compression-ratio> 6.3 </compression-ratio>
</piston_engine>
