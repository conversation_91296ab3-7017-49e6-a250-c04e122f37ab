<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>

<fdm_config name="J3Cub" version="2.0" release="ALPHA"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
  <author> <PERSON> using Aeromatic v 3.2.0 </author>
  <filecreationdate> 18 Dec 2015 </filecreationdate>
  <version>$Revision: 1.3 $</version>
  <description> Models a J3Cub. </description>
  <note>
    This model was created using publicly available data, publicly available
    technical reports, textbooks, and guesses. It contains no proprietary or
    restricted data. If this model has been validated at all, it would be
    only to the extent that it seems to "fly right", and that it possibly
    complies with published, publicly known, performance data (maximum speed,
    endurance, etc.). Thus, this model is meant for educational and
    entertainment purposes only.

    This simulation model is not endorsed by the manufacturer.
      </note>
  <reference refID="Du_Y_Thesis_2011" author="Yongliang Du" title="Development of real-time flight control system for low-cost vehicle" date="2011"/>
 </fileheader>

<!--
  File:     J3Cub.xml
  Inputs:
    name:          J3Cub
    type:          light commuter with 1 engines
    stall speed:   33.00kts
    max weight:    1220.00 lb
    length:        22.42 ft
    wing: 
     span:         35.25 ft
     area:         178.50 sq-ft
     chord:        5.25 ft
     aspect ratio: 6.94:1
     taper ratio:  1.00:1
     incidence:    2.00 degrees
     dihedral:     5.00 degrees
     sweep:        0.00 degrees

    no. engines:   1
    engine type:   Piston Engine
    engine layout: forward fuselage

    gear type:     taildragger
    steering type: steerable
    retractable?:  no

  Outputs:
    wing loading:  6.83 lb/sq-ft
    payload:       274.44 lbs
    CL-alpha:      5.02 per radian
    CL-0:          0.25
    CL-max:        1.85
    CD-0:          0.02
    K:             0.04
    Mcrit:         0.58

   References:
   * "Development of real-time flight control system for low-cost vehicle"
     Du, Yongliang - Cranfield University, January 2011
     https://dspace.lib.cranfield.ac.uk/handle/1826/8621

   * "USA-35B AIRFOIL (usa35b-il)"
     http://airfoiltools.com/airfoil/details?airfoil=usa35b-il#polars
-->

 <metrics>
   <wingarea  unit="FT2">   178.50 </wingarea>
   <wingspan  unit="FT" >    35.25 </wingspan>
   <wing_incidence>           2.00 </wing_incidence>
   <chord     unit="FT" >     5.25 </chord>
   <htailarea unit="FT2">    24.50 </htailarea>
   <htailarm  unit="FT" >    13.20 </htailarm>
   <vtailarea  unit="FT2">   10.20 </vtailarea>
   <vtailarm  unit="FT" >    13.40 </vtailarm>
   <location name="AERORP" unit="IN">
     <x>     0.00 </x>
     <y>     0.00 </y>
     <z>     0.00 </z>
   </location>
   <location name="EYEPOINT" unit="IN">
     <x>    18.50 </x>
     <y>     0.00 </y>
     <z>   -11.81 </z>
   </location>
   <location name="VRP" unit="IN">
     <x>   -13.78 </x>
     <y>     0.00 </y>
     <z>    -3.94 </z>
   </location>
 </metrics>

 <mass_balance>
   <ixx unit="SLUG*FT2">    538.01 </ixx>
   <iyy unit="SLUG*FT2">    386.81 </iyy>
   <izz unit="SLUG*FT2">    871.03 </izz>
   <emptywt unit="LBS" >    765.00 </emptywt>
   <location name="CG" unit="IN">
     <x>    -3.94 </x>
     <y>     0.00 </y>
     <z>   -22.83 </z>
   </location>
   <pointmass name="Pilot">
    <description> 274.44 LBS should bring model up to max weight </description>
    <weight unit="LBS"> 137.22 </weight>
    <location name="POINTMASS" unit="IN">
     <x>    20.87 </x>
     <y>     0.00 </y>
     <z>   -36.22 </z>
   </location>
  </pointmass>
 </mass_balance>

 <propulsion>

   <engine file="Continental A-65-8">
    <feed> 0 </feed>

    <thruster file="CM7445 MCCauley">
     <sense> 1 </sense>
     <location unit="IN">
       <x>   -68.90 </x>
       <y>     0.00 </y>
       <z>   -24.41 </z>
     </location>
     <orient unit="DEG">
       <pitch>     0.00 </pitch>
        <roll>     0.00 </roll>
         <yaw>     0.00 </yaw>
     </orient>
    </thruster>
  </engine>

  <tank type="FUEL" number="0">
     <location unit="IN">
       <x>     0.00 </x>
       <y>     0.00 </y>
       <z>     0.00 </z>
     </location>
     <capacity unit="LBS"> 75.00 </capacity>
     <contents unit="LBS"> 65.00 </contents>
  </tank>

 </propulsion>

 <ground_reactions>

  <contact type="BOGEY" name="TAIL">
    <location unit="IN">
      <x> 189.37 </x>
      <y>   0.00 </y>
      <z> -33.86 </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <rolling_friction> 0.02 </rolling_friction>
    <spring_coeff  unit="LBS/FT">     220.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 110.00 </damping_coeff>
    <steerability> STEERABLE </steerability>
    <max_steer unit="DEG"> -30.00 </max_steer>
    <brake_group> NONE </brake_group>
    <retractable> 0 </retractable>
  </contact>

  <contact type="BOGEY" name="LEFT_MAIN">
    <location unit="IN">
      <x> -12.60 </x>
      <y> -35.83 </y>
      <z> -77.17   </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <rolling_friction> 0.02 </rolling_friction>
    <spring_coeff  unit="LBS/FT">     1000.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC">  500.00 </damping_coeff>
    <damping_coeff_rebound unit="LBS/FT/SEC">850</damping_coeff_rebound>
    <max_steer unit="DEG">0</max_steer>
    <brake_group> LEFT </brake_group>
    <retractable> 0 </retractable>
  </contact>

  <contact type="BOGEY" name="RIGHT_MAIN">
    <location unit="IN">
      <x>  -12.60 </x>
      <y>   35.83 </y>
      <z>  -77.17 </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <rolling_friction> 0.02 </rolling_friction>
    <spring_coeff  unit="LBS/FT">     1000.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC">  500.00 </damping_coeff>
    <damping_coeff_rebound unit="LBS/FT/SEC">850</damping_coeff_rebound>
    <max_steer unit="DEG">0</max_steer>
    <brake_group> RIGHT </brake_group>
    <retractable> 0 </retractable>
  </contact>

  <contact type="STRUCTURE" name="LEFT_WING">
    <location unit="IN">
     <x>     4.33 </x>
     <y>  -209.45 </y>
     <z>     0.00 </z>
    </location>
   <static_friction>  1 </static_friction>
   <dynamic_friction> 1 </dynamic_friction>
   <spring_coeff unit="LBS/FT">      12200.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 1220.00 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="RIGHT_WING">
    <location unit="IN">
     <x>     4.33 </x>
     <y>   209.45 </y>
     <z>     0.00 </z>
    </location>
   <static_friction>  1 </static_friction>
   <dynamic_friction> 1 </dynamic_friction>
   <spring_coeff unit="LBS/FT">      12200.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 1220.00 </damping_coeff>
  </contact>

 </ground_reactions>

 <system file="Conventional Controls.xml"/>

 <flight_control name="FCS: J3Cub">

 </flight_control>

 <aerodynamics>

  <function name="aero/function/kCDge">
      <description>Change_in_drag_due_to_ground_effect</description>
      <table>
          <independentVar>aero/h_b-mac-ft</independentVar>
          <tableData>
              0.0000        0.4800
              0.1000        0.5150
              0.1500        0.6290
              0.2000        0.7090
              0.3000        0.8150
              0.4000        0.8820
              0.5000        0.9280
              0.6000        0.9620
              0.7000        0.9880
              0.8000        1.0000
              0.9000        1.0000
              1.0000        1.0000
              1.1000        1.0000
          </tableData>
      </table>
  </function>

  <function name="aero/function/kCLge">
      <description>Change_in_lift_due_to_ground_effect</description>
      <table>
          <independentVar>aero/h_b-mac-ft</independentVar>
          <tableData>
              0.0000        1.2030
              0.1000        1.1270
              0.1500        1.0900
              0.2000        1.0730
              0.3000        1.0460
              0.4000        1.0550
              0.5000        1.0190
              0.6000        1.0130
              0.7000        1.0080
              0.8000        1.0060
              0.9000        1.0030
              1.0000        1.0020
              1.1000        1.0000
          </tableData>
      </table>
  </function>

  <function name="aero/function/kClge">
      <description>Change_in_drag_due_to_ground_effect</description>
      <table>
          <independentVar>aero/h_b-mac-ft</independentVar>
          <tableData>
              0.0000        0.0480
              0.1000        0.0480
              0.1500        0.0480
              0.2000        0.0480
              0.3000        0.0615
              0.4000        0.1229
              0.5000        0.2709
              0.6000        0.3815
              0.7000        0.5882
              0.8000        0.7928
              0.9000        0.9620
              1.0000        0.9880
              1.1000        1.0000
          </tableData>
      </table>
  </function>

  <function name="aero/function/velocity-induced-fps">
    <description> velocity including the propulsion induced velocity.</description>
    <sum>
      <property>velocities/u-aero-fps</property>
      <property>propulsion/engine/prop-induced-velocity_fps</property>
      <property>propulsion/engine/prop-induced-velocity_fps</property>
    </sum>
  </function>
 
  <function name="aero/function/qbar-induced-psf">
    <description> q bar including the propulsion induced velocity.</description>
    <product>
      <property>aero/function/velocity-induced-fps</property>
      <property>aero/function/velocity-induced-fps</property>
      <property>atmosphere/rho-slugs_ft3</property>
      <value>0.5</value>
    </product>
  </function>

  <axis name="LIFT">

    <function name="aero/force/Lift_propwash">
      <description>Delta lift due to propeller induced velocity</description>
      <product>
         <property>propulsion/engine[0]/thrust-coefficient</property>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>aero/function/kCLge</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>
              -0.22  0.000
               0.00  0.068
               0.31  0.313
               0.62  0.000
            </tableData>
          </table>
      </product>
    </function>

    <function name="aero/force/Lift_alpha">
      <description>Lift due to alpha</description>
      <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>aero/function/kCLge</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">aero/Re</independentVar>
            <tableData>			<!-- USA-35B -->
                      1668183  3707224
             -1.5700   0.0000   0.0000
             -0.3491  -0.0085  -0.5085
             -0.2443  -0.5085  -0.8136
             -0.1745  -0.5085  -0.5085
             -0.0873   0.1017   0.1017
              0.0000   0.5339   0.5339
              0.0873   1.2204   1.2204
              0.1309   1.4746   1.4746
              0.1745   1.5000   1.6272
              0.2182   1.6201   1.7797
              0.2618   1.5645   1.8306
              0.3054   1.4272   1.6272
              0.3491   1.3138   1.4238
              1.5700   0.0000   0.0000
            </tableData>
          </table>
      </product>
    </function>

    <function name="aero/force/Lift_pitch_rate">
        <description>Lift_due_to_pitch_rate</description>
        <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>velocities/q-aero-rad_sec</property>
          <property>aero/ci2vel</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>			<!-- Du Y -->
             -0.035  8.523602
              0.175  6.996364
            </tableData>
          </table>
        </product>
      </function>

      <function name="aero/force/Lift_alpha_rate">
        <description>Lift_due_to_alpha_rate</description>
        <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/alphadot-rad_sec</property>
           <property>aero/ci2vel</property>
           <value> 2.3459 </value>	<!-- Du Y -->
        </product>
      </function>

    <function name="aero/force/Lift_elevator">
       <description>Lift due to Elevator Deflection</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/elevator-pos-rad</property>
           <value> 0.3274 </value>	<!-- Du Y -->
       </product>
    </function>

  </axis>

  <axis name="DRAG">

    <function name="aero/force/Drag_basic">
       <description>Drag at zero lift</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">aero/Re</independentVar>
            <tableData>			<!-- USA-35B -->
                      1668183  3707224
             -1.5700   1.4091   1.4091
             -0.3491   0.1898   0.1736
             -0.2443   0.1567   0.0494
             -0.1745   0.0307   0.0290
             -0.0873   0.0216   0.0208
              0.0000   0.0189   0.0187
              0.0873   0.0216   0.0208
              0.1309   0.0289   0.0279
              0.1745   0.0332   0.0315
              0.2182   0.0435   0.0402
              0.2618   0.0757   0.0707
              0.3054   0.1408   0.1125
              0.3491   0.1898   0.1736
              1.5700   1.4091   1.4091
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/force/Drag_induced">
       <description>Induced drag</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/cl-squared</property>
           <property>aero/function/kCDge</property>
           <value> 0.0485 </value>
         </product>
    </function>

    <function name="aero/force/Drag_beta">
       <description>Drag due to sideslip</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/beta-rad</independentVar>
            <tableData>
              -1.57    1.2300
              -0.26    0.0500
               0.00    0.0000
               0.26    0.0500
               1.57    1.2300
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/force/Drag_elevator">
       <description>Drag due to Elevator Deflection</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <abs><property>fcs/elevator-pos-rad</property></abs>
           <value> 0.0400 </value>
       </product>
    </function>

    <function name="aero/force/Drag_gear">
       <description>Drag due to gear</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <value> 0.0040 </value>
         </product>
    </function>

  </axis>

  <axis name="SIDE">

    <function name="aero/force/Side_beta">
       <description>Side force due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/beta-rad</property>
           <value> -0.1340 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/force/Side_roll_rate">
       <description>Side_force_due_to_roll_rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-aero-rad_sec</property>
           <table>
            <independentVar lookup="row">aero/Re</independentVar>
            <tableData>			<!-- Du Y -->
              1668183  0.1434
              3707224  0.0022
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/force/Side_yaw_rate">
       <description>Side_force_due_to_yaw_rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value> 0.1423 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/force/Side_rudder">
       <description>Side_force_due_to_rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/rudder-pos-rad</property>
           <value> 0.0934 </value>	<!-- Du Y -->
       </product>
    </function>

  </axis>

  <axis name="PITCH">

    <function name="aero/moment/Pitch_propwash">
      <description>Pitch moment due to propeller induced velocity</description>
      <product>
          <property>propulsion/engine[0]/thrust-coefficient</property>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/bw-ft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>
              -0.01  0.000
               0.00  0.023
               0.31  0.108
               0.40  0.000
            </tableData>
          </table>
      </product>
    </function>

    <function name="aero/moment/Pitch_alpha">
       <description>Pitch moment due to alpha</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/alpha-rad</property>
           <table>
            <independentVar lookup="row">aero/Re</independentVar>
            <tableData>			<!-- Du Y -->
              1668183  -2.0327
              3707224  -1.3432
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/moment/Pitch_elevator">
       <description>Pitch moment due to elevator</description>
       <product>
          <property>aero/function/qbar-induced-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/cbarw-ft</property>
          <property>fcs/elevator-pos-rad</property>
          <value> -1.2004 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/moment/Pitch_damp">
       <description>Pitch moment due to pitch rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>velocities/q-aero-rad_sec</property>
           <value> -12.7022 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/moment/Pitch_alphadot">
       <description>Pitch moment due to alpha rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>aero/alphadot-rad_sec</property>
           <value> -7.5904 </value>	<!-- Du Y -->
       </product>
    </function>

  </axis>

  <axis name="ROLL">

    <function name="aero/moment/Roll_differential_propwash">
       <description>Roll moment due to differential propwash</description>
       <product>
           <property>propulsion/engine[0]/thrust-coefficient</property>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/alpha-rad</property>
           <value> 0.1280 </value>
       </product>
    </function>

    <function name="aero/moment/Roll_beta">
       <description>Roll moment due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <property>aero/function/kClge</property>
           <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">aero/Re</independentVar>
            <tableData>			<!-- Du Y -->
                    1128067  1668183   3707224
             -0.035 -0.1024 -0.031363 -0.010147
              0.175 -1.0147 -0.310788 -0.100549
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/moment/Roll_damp">
       <description>Roll moment due to roll rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-aero-rad_sec</property>
           <value> -0.5250 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/moment/Roll_yaw">
       <description>Roll moment due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <property>aero/function/kClge</property>
           <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">aero/Re</independentVar>
            <tableData>			<!-- Du Y -->
                       557148   1128067    1668183    3707224
             -0.035  0.719318   0.192547   0.071235   0.021888
              0.175  8.422175   2.254452   0.834059   0.256275
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/moment/Roll_aileron">
       <description>Roll moment due to aileron</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/bw-ft</property>
          <property>fcs/left-aileron-pos-rad</property>
          <value> 0.3498 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/moment/Roll_rudder">
       <description>Roll moment due to rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value> 0.0133 </value>	<!-- Du Y -->
       </product>
    </function>

  </axis>

  <axis name="YAW">

    <function name="aero/moment/Yaw_beta">
       <description>Yaw moment due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <value> 0.0602 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/moment/Yaw_rol_rate">
       <description>Yaw_moment_due_to_roll_rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-rad_sec</property>
           <property>aero/function/kClge</property>
           <table>
            <independentVar lookup="row">aero/Re</independentVar>
            <tableData>			<!-- Du Y -->
               341838  -2.1500
              1128067  -0.1974
              1668183  -0.0531
              3707224  -0.0006
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/moment/Yaw_damp">
       <description>Yaw moment due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value> -0.0810 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/moment/Yaw_rudder">
       <description>Yaw moment due to rudder</description>
       <product>
           <property>aero/function/qbar-induced-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value> -0.0565 </value>	<!-- Du Y -->
       </product>
    </function>

    <function name="aero/moment/Yaw_aileron">
       <description>Adverse yaw</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/left-aileron-pos-rad</property>
           <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">aero/Re</independentVar>
            <tableData>                 <!-- Du Y -->
                     1668183    3707224
             -0.035  0.00171    0.00150
              0.175 -0.01890   -0.00173
            </tableData>
          </table>
       </product>
    </function>

  </axis>

 </aerodynamics>

 <external_reactions>
 </external_reactions>

</fdm_config>
