<?xml version="1.0"?>

<system name="compressability">

  <property>systems/compressabilty/hertz</property>
  <property>systems/compressabilty/sine_wave</property>
  <property>systems/compressabilty/enabled</property>

  <channel name="Compressabilty">

    <switch name="fcs/compressabilty-enable">
        <default value="0"/>
        <test value="1">
            velocities/mach gt 0.7
        </test>
        <output>systems/compressabilty/enabled</output>
    </switch>

   <scheduled_gain name="fcs/frequency">
      <input>systems/compressabilty/enabled</input>
      <table>
        <independentVar lookup="row">velocities/mach</independentVar>
        <tableData>
           0         0.0
           0.76      0.0
           0.78      0.1
           0.9       0.5
        </tableData>
      </table>
      <output>systems/compressabilty/hertz</output>
   </scheduled_gain>

   <scheduled_gain name="fcs/strength">
      <input>systems/compressabilty/enabled</input>
      <table>
        <independentVar lookup="row">velocities/mach</independentVar>
        <tableData>
           0           0.0
           0.76        0.0
           0.78        0.3
           0.9         10.0
        </tableData>
      </table>
      <output>systems/compressabilty/strength</output>
   </scheduled_gain>

   <fcs_function name="fcs/sine-wave-generator">
     <function>
       <product>
       <sin>
         <product>
           <property>systems/compressabilty/hertz</property>
           <property>simulation/sim-time-sec</property>
           <value>6.283185307</value>
         </product>
       </sin>
       <property>systems/compressabilty/strength</property>
       </product>
     </function>
     <output>systems/compressabilty/sine_wave</output>
   </fcs_function>

  </channel>


</system>

