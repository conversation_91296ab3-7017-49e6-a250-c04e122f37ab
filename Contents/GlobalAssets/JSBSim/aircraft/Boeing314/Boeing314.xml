<?xml version="1.0"?>
<?xml-stylesheet href="http://jsbsim.sourceforge.net/JSBSim.xsl" type="text/xsl"?>
<fdm_config name="Boeing314A" version="2.0" release="PRODUCTION"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
        <author> </author>
        <filecreationdate> 11 September 2009 </filecreationdate>
        <version> 1.05 </version>

        <description> Boeing314A </description>

        <license>
          <licenseName>GPL (General Public License)</licenseName>
          <licenseURL>http://www.gnu.org/licenses/gpl.html</licenseURL>
        </license>
        <note>
            This model was created using publicly available data, publicly available
            technical reports, textbooks, and guesses. It contains no proprietary or
            restricted data. It has been validated only to the extent that it seems
            to "fly right", and possibly to comply to published, publicly known,
            performance data (maximum speed, endurance, etc.). Thus, this model
            is meant for educational and entertainment purposes only.
            This simulation model is not endorsed by the manufacturer. This model
            is not to be sold.
        </note>
        <note>314A model, 14.5 ft propeller, 1600 hp, 5200 miles range
              (314 model has smaller propeller, 1500 hp and 3500 miles range).</note>
        <note>center of gravity in range.</note>
        <note>takeoff flaps 1/3.</note>
        <note>real gear ratio 16:9.</note>
        <note>economic speed 110 kt.</note>
        <note>sponson lift increases the windows until flaps retraction (105 kt).</note>

        <limitation>heading autopilot can hang in a roll : the ball of the turn indicator
                    experiences a centrifugal force, in the direction of the roll.</limitation>

        <note>tilt at rest.</note>
        <note>hull drag.</note>
        <note>step lift increases the windows until flaps retraction (105 kt).</note>
        <note>step triming moment.</note>
        <note>rebound at landing.</note>
        <note>skipping at landing (empty weight).</note>
        <note>anchor compensates lack of brakes.</note>
        <note>gear height adjusted by 3D model, supposed accurate.</note>
        <note>black paint visible only at empty fuel (buoyancy).</note>

        <limitation>buoyancy by normal (equivalent to ground) gear spring.</limitation>
        <limitation>gear tilt loses brakes efficiency :
                    - rest with empty fuel 700 RPM, or full throttle.
                    - more static friction brings vibrations at rest.
                    - softer gear spring restores brakes, but loses the tilt (buoyancy).
                    - disabling all gear filters restores brakes.</limitation>
        <limitation>no waterloop when turns by cross-wind (wing diging water).</limitation>
        <limitation>weathervaning by wing, instead of the sponsons.</limitation>
        <limitation>no weathervaning at rest.</limitation>
        <limitation>no propoising at takeoff.</limitation>
        <limitation>skipping is obtained undirectly.</limitation>

        <note>Credits : J. Berndt for NACA 1103</note>

<!--
  File:     Boeing314A.xml
  Author:   Aero-Matic v 0.71

  Inputs:
    name:          Boeing314A
    type:          multi-engine prop transport
    max weight:    84000.0 lb
    wing span:     152.0 ft
    length:        106.0 ft
    wing area:     2867.0 sq-ft
    gear type:     tricycle
    retractable?:  no
    # engines:     4
    engine type:   piston
    engine layout: wings
    yaw damper?    no

  Outputs:
    wing loading:  29.30 lb/sq-ft
    CL-alpha:      4.9 per radian
    CL-0:          0.24
    CL-max:        1.4
    CD-0:          0.028
    K:             0.039
-->

        <note>
         Notation :
         - Hydrodynamic coefficients start by H.
         - Artificial coefficients start by A.
        </note>

        <documentation>
         Not possible :
         - buoyancy by weak spring, because that can rear up at rest
           (and speed makes water as hard as concrete). 
         - rebound by hard spring, because static can be unstable with empty weight.
        </documentation>

        <reference
          refID="FAA certificate, TC704, B-314"
          author="FAA"
          title="http://www.airweb.faa.gov/Regulatory_and_Guidance_Library/rgMakeModel.nsf/MainFrame?OpenFrameSet"
          date="25 January 1939"/>
        <documentation>
         Engine limits :   * maximum except take-off (straight line manifold pressure variation) :
                             33.2 in. hg. 2100 rpm (1200 hp) at 5400 ft,
                             35.0 in. hg. 2100 rpm (1200 hp) at sea level.
                           * take-off (2 minutes), 42.5 in. hg. 2400 rpm (1550 hp).
         Airspeed limits : * level flight or climb 178 mph (155 kt) true, glide or dive 212 mph (184 kt) true.
                           * flaps extended (40 or less) 121 mph (105 kt) true.
                           * flaps extended (more than 40) 105 mph (91 kt) true. 
         Weight limits :   landing 80000 lb, take-off 84000 lb.
         Fuel capacity :   5448 gallons (32688 lb) :
                             2 tanks at 600 gallons (3600 lb) each, in wing stubs;
                             1 tank 960 gallons (5760 lb) inboard;
                             and 1 tank at 1164 gallons (6984 lb) outboard, in each hydro-stabilizer.

         Note that :
         - 1 gallon = 6 lb !
         - this is the most recent content (314A), the real content of 314 could be lower.

         Datum : nose of hull.
         MAC : 258.79 in., leading edge of MAC at 398.66 in.
         CG range (flight)   : * at 80000 lb or more, 455.6 in. (22% MAC) to 478.2 in. (30.7% MAC).
                               * at less than 80000 lb, 444.3 in. (17.65% MAC) to 478.2 in. (30.7% MAC).
                  (landing)  : * at 70330 lb or more, 463.4 in. (25% MAC) to 478.2 in. (30.7% MAC).
                               * at less than 70330 lb, 455.6 in. (22% MAC) to 478.2 in. (30.7% MAC).

         Ceiling (ft)   Weight (lb)   RPM   Manifold   True IAS   Prop.   Cowl flap   De-icers
                                            pressure     (kt)     blade    opening    installed
         ======================================================================================
            8900         80000       2100   Full         100     6159A-0     ...         ...
                                            throttle
            7500         84000       2100   Full         102     6159A-0     ...         ...
                                            throttle
           10000         80000       2100   Full         100     6243A-3   5 degrees     yes
                                            throttle
            9000         84000       2100   Full         102     6243A-3   5 degrees     yes
                                            throttle

         Propeller : * Hamilton standard 3 blade constant speed, 6243A-3 to 6243A-6 inclusive,
                       diameter 14'9-3/8" maximum 14'5-5/8" minimum (1928 lb).
                     * Hamilton standard 3 blade constant speed, 6159A-0 to 6159A-3 inclusive,
                       diameter 14'3/8" maximum 13'8-3/4" minimum (1819 lb).
        </documentation>
        <reference
          refID="FAA certificate, TC704, B-314A"
          author="FAA"
          title="http://www.airweb.faa.gov/Regulatory_and_Guidance_Library/rgMakeModel.nsf/MainFrame?OpenFrameSet"
          date="2 May 1941"/>
        <documentation>
         Same model as B-314, except for engine, powerplane installation, engine mount,
         and inner structural details.

         Engine limits : * maximum except take-off (straight line manifold pressure variation) :
                           35.8 in. hg. 2300 rpm (1350 hp) at 6200 ft,
                           37.5 in. hg. 2300 rpm (1350 hp) at sea level.
                         * take-off (2 minutes), 43.5 in. hg. 2400 rpm (1600 hp).

         Ceiling (ft)   Weight (lb)   RPM   Manifold   True IAS   Prop.   Cowl flap   De-icers
                                            pressure     (kt)     blade    opening    installed
         ======================================================================================
           10000         80000       2300   Full         100     6243A-3   5 degrees     yes
                                            throttle
            9000         84000       2300   Full         102     6243A-3   5 degrees     yes
                                            throttle

         Propeller : Hamilton standard 3 blade constant speed, 6243A-3 to 6243A-6 inclusive,
                     diameter 14'9-3/8" maximum 14'5-5/8" minimum (1928 lb).
        </documentation>
        <documentation>
         - http://case.pr.erau.edu/reports/US_reports/1940/1943.02.22_PanAmericanAirways_Boeing-314.pdf
           (crash - 22 february 1943 Lisbon, Portugal) :

         Elevator range : +/-25 deg (20 + 5).

         - http://case.pr.erau.edu/reports/US_reports/1940/1941.08.16_PanAmericanAirways_Boeing-314.pdf
         (crash - 16 august 1941 Apra Harbor, Guam) :

         Takeoff : in less than 6000 ft (full load).

         - http://www.hq.nasa.gov/office/pao/History/SP-468/app-a2.htm/

         Maximum power (sea level) : 1600 hp per engine, power loading 13.1 lb / hp.
         Weights : gross 84000 lb, empty 48400 lb.
         Lengths : wing span 152.0 ft, length 106.0 ft.
         Areas : wing 2867 sq ft, wind loading 29.3 lb / sq ft, drag area (CD0 x wing area) 78.56 sq ft.
         Speeds : Vmax 201 mph at 6200 ft, Vcruise 184 mph at 11000 ft, Vstall 70 mph.
         Drag : CD0 (zero-lift) 0.0274, wing aspect ratio 8.06, maximum lift/drag ratio 13.0.

         - http://www.hq.nasa.gov/office/pao/History/SP-468/ch8-2.htm/

         Curve of a hull drag.

         - http://www.aviation-history.com/boeing/314.html/

         4 Wright GR-2600 Twin Cyclone, 1600 hp (1192 kw), 14 cylinder, air-cooled, radial engines.
         Weights : gross 82500 lb, empty 50268 lb.
         Lengths : wing span 152.0 ft, length 106.0 ft, height 27.7 ft.
         Wing area : 2867 sq ft.
         Speeds : maximum level 199 mph, cruise 183 mph.
         Service ceiling : 13400 ft.
         Normal range : 3500 miles.

         - http://www.daveswarbirds.com/usplanes/aircraft/b-314.htm/

         4 Wright GR-2600 Cyclone, 1600 hp, 14 radial.
         Weights : gross 82500 lb, empty 50268 lb.
         Lengths : wing span 152.0 ft, length 106.0 ft, height 27.7 ft.
         Wing area : 2867 sq ft.
         Speeds : maximum 193 mph at 10000 ft, cruise 183 mph.
         Service ceiling : 13400 ft.
         Normal range : 3500 miles.

         - http://www.flyingclippers.com/B314.html/
  
         Ceiling : cruise 13400 ft, maximum 19600 ft, climb 562 ft/min.
         Crew : 6-10.
         Payload : 40-74 (36 night) passengers.

         - http://www.boeing.com/history/boeing/m314.html/

         (B314-A)

         4 1600 hp Wright GR-2600 Twin Cyclone.
         Weights : gross 84000 lb.
         Lengths : wing span 152.0 ft, length 106.0 ft.
         Speeds : maximum 199 mph, cruise 184 mph.
         Ceiling : 19600 ft.
         Range : 5200 miles.
         Accommodation : 10 crew, 74 passengers.

         - http://www.southernoregonwarbirds.org/fa3.html/
         (Franck Varnum flew during WWII).
 
         The sponsons (the short fins sticking out of the sides of the fuselage) provide lateral stability
         on the water. They were filled with gasoline and as a result were not at all that buoyant.
         If you had to taxi to downwind, on the turn to downwind the wind could get under the upwind wing
         and tip the plane until the downwind wing dug into the water. In that condition a lot of water
         could flow into the wing, and once there could flow through the wings and into the fuselage,
         dousing everyone and everything. That never happened to me but I did hear of such cases.
         When we faced that situation we would have several of the crew walk into the upwind wing out
         to the outer nacelle, to provide balance during the turn. The wing at the fuselage was about
         11 feet thick and contained a catwalk that led all the way out to the outboard engine.
         Flight engineers could actually work on the engines in flight.

         Set up the cruise at 117 mph (102 kt), until 135 mph (117 kt) as one burns off gas.
         They were never anywhere near the advertised max cruise of 193 mph (168 kt) :
         KSFO - PHNL (2080 nmi) at an average speed of 126 mph (109 kt), 16 hours depending of wind.
         During "Amber Alerts", they leaned out the mixture and slowed the engine rpms to 100 mph (87 kt) :
         23-24 hours till Honolulu.

         Landing : 70-75 mph (61-65 kt).

         - TV documentary (Panam films) :

         At rest, B314 tilted on one of its lateral sponsons.

         - http://www.pilotfriend.com/flight_training/frames2/seaplane_main_frame.htm/ :
         How to fly seaplanes and amphibians.
        </documentation>
        <reference
          refID="A complete tank test of the hull of the Sikorsky S-40 flying-boat - American clipper class"
          author="John R. Dawson"
          title="http://naca.larc.nasa.gov/reports/1938/naca-tn-512/"
          date="December 1934"/>
        <reference
          refID="Generalized theory for seaplane impact"
          author="Benjamin Milwitzky"
          title="http://naca.larc.nasa.gov/reports/1952/naca-report-1103/"
          date="October 1952"/>
        <reference
          refID="Stepped hull development for amphibious aircraft"
          author="Orion Technologies"
          title="http://www.nwlink.com/~orion/Documents/Hull.pdf"
          date=""/>
        <reference
          refID="A preliminary correlation of the behavior of water rudders on seaplanes and flying boats"
          author="F. W. S. Locke Jr."
          title="http://naca.larc.nasa.gov/reports/1947/naca-tn-1387/"
          date="August 1947"/>
    </fileheader>

    <metrics>
        <documentation>
         Pilot's eyepoint location, in aircraft's own coord system, FROM cg.
         X, Y, Z, in inches

         ORIGIN (0,0,0) is at NOSE.

         CG : empty load at 24.1 % of MAC, full load at 30.1 % of MAC.
         Aero reference point at 30.7 % of MAC.

         Chord at MAC.

         Add ac_wingincidence : 1/20 of wing chord.

         Htailarm : distance from CG to horizontal tail tip.
        </documentation>
        <wingarea unit="FT2"> 2867 </wingarea>
        <wingspan unit="FT"> 152 </wingspan>
        <chord unit="FT"> 21.57 </chord>                                                 <!-- aeromatic 18.86 -->
        <wing_incidence unit="DEG"> 2.86 </wing_incidence>                               <!-- added to help the take-off -->
        <htailarea unit="FT2"> 458.72 </htailarea>
        <htailarm unit="FT"> 22.91 </htailarm>                                           <!-- aeromatic 53.00 -->
        <vtailarea unit="FT2"> 516.06 </vtailarea>
        <vtailarm unit="FT"> 17.17 </vtailarm>                                           <!-- added (turbulence) -->
        <!-- AC_LV        53.00                       -->
        <location name="AERORP" unit="IN">
            <x> 478.1 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 101.8 </x>
            <y> -24 </y>
            <z> 65 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance>
        <documentation>
         Center of gravity location, empty weight, in aircraft's own structural coord
         system. X, Y, Z, in inches; X is positive out the tail, Y positive out the
         right wing.

         Put in comment payload, to test at empty weight.
        </documentation>
        <ixx unit="SLUG*FT2"> 2.31442e+06 </ixx>
        <iyy unit="SLUG*FT2"> 1.34649e+06 </iyy>
        <izz unit="SLUG*FT2"> 3.59608e+06 </izz>
        <ixy unit="SLUG*FT2"> 0 </ixy>
        <ixz unit="SLUG*FT2"> 0 </ixz>
        <iyz unit="SLUG*FT2"> 0 </iyz>
        <emptywt unit="LBS"> 48400 </emptywt>                               <!-- aeromatic 50400 -->
        <location name="CG" unit="IN">
            <x> 459.2 </x>
            <y> 0 </y>
            <z> -31.8 </z>
        </location>
        <pointmass name="payload">
            <weight unit="LBS"> 1500 </weight>
            <location name="POINTMASS" unit="IN">
                <x> 460.0 </x>
                <y> 0 </y>
                <z> -31.8 </z>
            </location>
        </pointmass>
    </mass_balance>

    <ground_reactions>
        <documentation>
         Left/right sponsons at 70% MAC and 1/4 wing : higher at 3/4 hull height (tilt at rest).

         The hump and step gears replaces the nose and rear gears, when the hull raises on the step.

         The black painting of the hull should be visible, only if empty fuel.

         NACA 1387 : no steering, as the 314 has no water rudder; turns by differential power on engines.

         All gears have identical coefficients (hull in water).

         Disabling the filters restores the brakes.

         CAUTION : changing gear height or spring affects rest h_b-mac-ft !
        </documentation>
        <contact type="BOGEY" name="HUMP_LG">
            <location unit="IN">
                <x> 66.4 </x>
                <y> 0 </y>
                <z> -100.6 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 84000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 16800 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NOSE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 395.4 </x>
                <y> 0 </y>
                <z> -120.6 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 84000 </spring_coeff>                       <!-- aeromatic 25200 -->
            <damping_coeff unit="LBS/FT/SEC"> 16800 </damping_coeff>                 <!-- aeromatic 8400 -->
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NOSE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 495.4 </x>
                <y> -228 </y>
                <z> -110.6 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 84000 </spring_coeff>                       <!-- aeromatic 84000 -->
            <damping_coeff unit="LBS/FT/SEC"> 16800 </damping_coeff>                 <!-- aeromatic 16800 -->
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 495.4 </x>
                <y> 228 </y>
                <z> -110.6 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 84000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 16800 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="STEP_MLG">
            <location unit="IN">
                <x> 535.4 </x>
                <y> 0 </y>
                <z> -100.6 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 84000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 16800 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> TAIL </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="REAR_MLG">
            <location unit="IN">
                <x> 803.4 </x>
                <y> 0 </y>
                <z> -120.6 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 84000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 16800 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> TAIL </brake_group>
            <retractable>0</retractable>
        </contact>
    </ground_reactions>

    <propulsion>
        <documentation>
         propeller at 1/5 of length, 1/10 and 2/10 of wing span.

         1 tank feeds 1 engine, plus floor and sponson (left or right).
        </documentation>
        <engine file="WrightGR-2600">
            <feed>0</feed>
            <feed>4</feed>
            <feed>5</feed>
            <thruster file="HamiltonStd6243A-3">
                <location unit="IN">
                    <x> 254.4 </x>
                    <y> -364.8 </y>
                    <z> 40 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="WrightGR-2600">
            <feed>1</feed>
            <feed>4</feed>
            <feed>5</feed>
            <thruster file="HamiltonStd6243A-3">
                <location unit="IN">
                    <x> 254.4 </x>
                    <y> -182.4 </y>
                    <z> 40 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="WrightGR-2600">
            <feed>2</feed>
            <feed>4</feed>
            <feed>6</feed>
            <thruster file="HamiltonStd6243A-3">
                <location unit="IN">
                    <x> 254.4 </x>
                    <y> 182.4 </y>
                    <z> 40 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="WrightGR-2600">
            <feed>3</feed>
            <feed>4</feed>
            <feed>6</feed>
            <thruster file="HamiltonStd6243A-3">
                <location unit="IN">
                    <x> 254.4 </x>
                    <y> 364.8 </y>
                    <z> 40 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <documentation>
         radius : lb => kg => divide by 0.792 (depends of fuel density) => liter => radius of sphere.

         all tanks are 40 % of MAC.
        </documentation>
        <tank type="FUEL">    <!-- Tank number 0 -->
            <documentation>
              wing tanks behind the engines.
            </documentation>
            <location unit="IN">
                <x> 502 </x>                                                              <!-- aeromatic 636 -->
                <y> -364.8 </y>                                                           <!-- aeromatic 0 -->
                <z> 30 </z>                                                               <!-- aeromatic -31.8 -->
            </location>
            <radius unit="IN"> 311 </radius>                                              <!-- aeromatic 1 -->
            <capacity unit="LBS"> 3600 </capacity>
            <contents unit="LBS"> 3600 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 1 -->
            <location unit="IN">
                <x> 502 </x>
                <y> -182.4 </y>
                <z> 30 </z>
            </location>
            <capacity unit="LBS"> 3600 </capacity>
            <contents unit="LBS"> 3600 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 2 -->
            <location unit="IN">
                <x> 502 </x>
                <y> 182.4 </y>
                <z> 30 </z>
            </location>
            <capacity unit="LBS"> 3600 </capacity>
            <contents unit="LBS"> 3600 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 3 -->
            <location unit="IN">
                <x> 502 </x>
                <y> 364.8 </y>
                <z> 30 </z>
            </location>
            <capacity unit="LBS"> 3600 </capacity>
            <contents unit="LBS"> 3600 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 4 -->
            <documentation>
              inboard tank (floor).
            </documentation>
            <location unit="IN">
                <x> 502 </x>
                <y> 0 </y>
                <z> -40 </z>
            </location>
            <capacity unit="LBS"> 5760 </capacity>
            <contents unit="LBS"> 5760 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 5 -->
            <documentation>
              ouboard tanks (hydro-stabilizer).
            </documentation>
            <location unit="IN">
                <x> 502 </x>
                <y> -182.4 </y>
                <z> -40 </z>
            </location>
            <capacity unit="LBS"> 6984 </capacity>
            <contents unit="LBS"> 6984 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 6 -->
            <location unit="IN">
                <x> 502 </x>
                <y> 182.4 </y>
                <z> -40 </z>
            </location>
            <capacity unit="LBS"> 6984 </capacity>
            <contents unit="LBS"> 6984 </contents>
        </tank>
    </propulsion>

    <system name="Hull">
     <property>systems/hull/anchor</property>

     <documentation>
       anchor opposes to propeller and inertia.
     </documentation>

     <channel name="Anchor">

        <pure_gain name="systems/hull/gear-lbs">
            <gain>-1</gain>
            <input>forces/fbx-gear-lbs</input>
        </pure_gain>

        <summer name="systems/hull/total-lbs">
            <input>forces/fbx-prop-lbs</input>
            <input>systems/hull/gear-lbs</input>
        </summer>

        <pure_gain name="systems/hull/anchor-lbs">
            <input>systems/hull/total-lbs</input>
            <gain>systems/hull/anchor</gain>
            <output>external_reactions/anchor/magnitude</output>
        </pure_gain>

     </channel>
    </system>

    <flight_control name="FCS: Boeing314A">
     <channel name="Pitch">

        <summer name="Pitch Trim Sum">
            <input>fcs/elevator-cmd-norm</input>
            <input>fcs/pitch-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Elevator Control">
            <input>fcs/pitch-trim-sum</input>
            <gain>0.01745</gain>
            <range>
                <documentation>
                  replaced -0.35/0.30 by real -25/25 deg.
                </documentation>
                <min>-25</min>
                <max>25</max>
            </range>
            <output>fcs/elevator-pos-rad</output>
        </aerosurface_scale>
        <aerosurface_scale name="Elevator Position Normalizer">
            <input>fcs/pitch-trim-sum</input>
            <range>
                <min>-1</min>
                <max>1</max>
            </range>
            <output>fcs/elevator-pos-norm</output>
        </aerosurface_scale>

     </channel>
     <channel name="Roll">

        <summer name="Roll Trim Sum">
            <input>fcs/aileron-cmd-norm</input>
            <input>fcs/roll-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Left Aileron Control">
            <input>fcs/roll-trim-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/left-aileron-pos-rad</output>
        </aerosurface_scale>
        <aerosurface_scale name="Left Aileron Position Normalizer">
            <input>fcs/roll-trim-sum</input>
            <range>
                <min>-1</min>
                <max>1</max>
            </range>
            <output>fcs/left-aileron-pos-norm</output>
        </aerosurface_scale>

        <aerosurface_scale name="Right Aileron Control">
            <input>-fcs/roll-trim-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/right-aileron-pos-rad</output>
        </aerosurface_scale>
        <aerosurface_scale name="Right Aileron Position Normalizer">
            <input>fcs/roll-trim-sum</input>
            <range>
                <min>-1</min>
                <max>1</max>
            </range>
            <output>fcs/right-aileron-pos-norm</output>
        </aerosurface_scale>

     </channel>
     <channel name="Yaw">

        <summer name="Rudder Command Sum">
            <input>fcs/rudder-cmd-norm</input>
            <input>fcs/yaw-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Rudder Control">
            <input>fcs/rudder-command-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/rudder-pos-rad</output>
        </aerosurface_scale>
        <aerosurface_scale name="Rudder Position Normalizer">
            <input>fcs/rudder-command-sum</input>
            <range>
                <min>-1</min>
                <max>1</max>
            </range>
            <output>fcs/rudder-pos-norm</output>
        </aerosurface_scale>

     </channel>
     <channel name="Aeromechanical">

        <kinematic name="Flaps Control">
            <input>fcs/flap-cmd-norm</input>
            <traverse>
                <documentation>
                  changed 15/30 deg to 15/40/55 (real is above 40).
                </documentation>
                <setting>
                    <position>0</position>
                    <time>0</time>
                </setting>
                <setting>
                    <position>20</position>
                    <time>4</time>
                </setting>
                <setting>
                    <position>40</position>
                    <time>3</time>
                </setting>
                <setting>
                    <position>55</position>
                    <time>2</time>
                </setting>
            </traverse>
            <output>fcs/flap-pos-deg</output>
        </kinematic>
        <aerosurface_scale name="Flap Position Normalizer">
          <input>fcs/flap-pos-deg</input>
          <domain>
            <min>0</min>
            <max>55</max>
          </domain>
          <range>
            <min>0</min>
            <max>1</max>
          </range>
          <output>fcs/flap-pos-norm</output>
        </aerosurface_scale>

        <kinematic name="Speedbrake Control">
            <input>fcs/speedbrake-cmd-norm</input>
            <traverse>
                <setting>
                    <position>0</position>
                    <time>0</time>
                </setting>
                <setting>
                    <position>1</position>
                    <time>1</time>
                </setting>
            </traverse>
            <output>fcs/speedbrake-pos-norm</output>
        </kinematic>
     </channel>
    </flight_control>

    <aerodynamics>
        <documentation>
             Hydrodynamics depends of 4 coefficients, related to gear height and spring.

             h_b-mac-ft at rest (with the 84000 lbs/ft spring) :
             - 0.0450 empty fuel.
             - 0.0490 full load.

             h_b-mac-ft at rotation speed (find with logging), with hydrodynamic lift :
             - 0.0650 empty fuel.
             - 0.0690 full load.

             0.0690 is also 0.0490 + 0.020,
             where 3.04' (from the keel to step) / wing span (152') = 0.020;
        </documentation>

        <documentation>
             Hull resistance in water until the step :
             - maximum at 42 % of takeoff speed : hump at 42 kt full load (100 kt) and
               34 kt empty load (80 kt).
             - reduced by hydrodynamic lift.
             - constant above 70 kt.
             - 50 % of drag when hull is going out of water, and then none.
             - below ground effect.

             similar to NACA TN 512, figure 11.

           % of takeoff speed, % of hump drag :
           -   0.0/106,  0.0/59. (dock)
           -   5.0/106,  4.0/59.
           -   8.0/106,  8.0/59.
           -  11.0/106, 13.5/59.
           -  14.0/106, 21.0/59.
           -  16.5/106, 27.0/59.
           -  19.0/106, 33.0/59.
           -  21.0/106, 36.5/59.
           -  24.5/106, 42.5/59.
           -  28.0/106, 47.5/59.
           -  32.5/106, 53.0/59.
           -  35.0/106, 55.0/59.
           -  38.0/106, 56.5/59.
           -  41.5/106, 58.5/59.
           -  45.0/106, 59.0/59. (hump)
           -  48.0/106, 58.5/59.
           -  51.5/106, 57.0/59.
           -  55.0/106, 53.5/59.
           -  59.5/106, 48.5/59.
           -  64.5/106, 42.0/59.
           -  69.5/106, 36.5/59.
           -  76.0/106, 29.0/59.
           -  92.0/106, 11.5/59.
           -  96.0/106,  7.0/59.
           -  99.5/106,  5.0/59.
           - 106.0/106,  0.0/59. (lift-off)
        </documentation>
        <function name="aero/function/kHDhull">
            <description>Hull_resistance</description>
            <table>
                <independentVar lookup="row">velocities/vc-kts</independentVar>
                <independentVar lookup="column">aero/h_b-mac-ft</independentVar>
                <independentVar lookup="table">inertia/weight-lbs</independentVar>
                <tableData breakPoint="47850">
                                0.0450	0.0550	0.0650
                    0.0000	0.0000	0.0000	0.0000
                    3.7000	0.0678	0.0339	0.0000
                    6.0000	0.1356	0.0678	0.0000
                    8.3000	0.2288	0.1144	0.0000
                    10.6000	0.3559	0.1780	0.0000
                    12.5000	0.4576	0.2288	0.0000
                    14.3000	0.5593	0.2797	0.0000
                    15.9000	0.6186	0.3093	0.0000
                    18.5000	0.7203	0.3602	0.0000
                    21.1000	0.8051	0.4026	0.0000
                    24.5000	0.8983	0.4492	0.0000
                    26.4000	0.9322	0.4661	0.0000
                    28.7000	0.9576	0.4788	0.0000
                    31.3000	0.9915	0.4958	0.0000
                    34.0000	1.0000	0.5000	0.0000
                    36.2000	0.9915	0.4958	0.0000
                    38.9000	0.9961	0.4981	0.0000
                    41.5000	0.9068	0.4534	0.0000
                    44.9000	0.7119	0.3560	0.0000
                    48.7000	0.6186	0.3093	0.0000
                    52.5000	0.4915	0.2458	0.0000
                    57.4000	0.1949	0.0975	0.0000
                </tableData>
                <tableData breakPoint="84000.0000">
                                0.0490	0.0590	0.0690
                    0.0000	0.0000	0.0000	0.0000
                    4.7000	0.0678	0.0339	0.0000
                    7.5000	0.1356	0.0678	0.0000
                    10.4000	0.2288	0.1144	0.0000
                    13.2000	0.3559	0.1780	0.0000
                    15.6000	0.4576	0.2288	0.0000
                    17.9000	0.5593	0.2797	0.0000
                    19.8000	0.6186	0.3093	0.0000
                    23.1000	0.7203	0.3602	0.0000
                    26.4000	0.8051	0.4026	0.0000
                    30.7000	0.8983	0.4492	0.0000
                    33.0000	0.9322	0.4661	0.0000
                    35.8000	0.9576	0.4788	0.0000
                    39.1000	0.9915	0.4958	0.0000
                    42.0000	1.0000	0.5000	0.0000
                    45.3000	0.9915	0.4958	0.0000
                    48.6000	0.9961	0.4981	0.0000
                    51.9000	0.9068	0.4534	0.0000
                    56.1000	0.7119	0.3560	0.0000
                    60.9000	0.6186	0.3093	0.0000
                    65.6000	0.4915	0.2458	0.0000
                    71.7000	0.1949	0.0975	0.0000
                </tableData>
            </table>
        </function>

        <documentation>
             The hull goes on its step :
             - hydrodynamic lift.
             - 80 % when going out of water.
             - 1st altitude = h_b-mac-ft at rest.
             - 2nd altitude : in the middle.
             - 3rd altitude : 3.04' (from the keel) / wing span (152') = 0.020;
             the planing layer is thin.

             Not implemented :
             - drag when the bow plunges into a wave hump (nose down).
             - drag when after body digs the water (nose up).
        </documentation>
        <function name="aero/function/kHLstep">
            <description>Step_lift</description>
            <table>
                <independentVar lookup="row">aero/h_b-mac-ft</independentVar>
                <independentVar lookup="column">velocities/vc-kts</independentVar>
                <independentVar lookup="table">inertia/weight-lbs</independentVar>
                <tableData breakPoint="47850.0000">
                                0.0000	17.0000	34.0000
                    0.0450	0.0000	0.0000	1.0000
                    0.0550	0.0000	0.0000	0.8000
                    0.0650	0.0000	0.0000	0.0000
                </tableData>
                <tableData breakPoint="84000.0000">
                                0.0000	21.0000	42.0000
                    0.0490	0.0000	0.0000	1.0000
                    0.0590	0.0000	0.0000	0.8000
                    0.0690	0.0000	0.0000	0.0000
                </tableData>
            </table>
        </function>

        <documentation>
             The hull goes on its step :
             - as sponson gets out water, additional lift.
             - sponson in water reduces lift : 0.02/0.029 rad at rest.
             - on step, the possible roll is higher (sponson touching water).
             - after lift off, no loss by water.
        </documentation>
        <function name="aero/function/kHLsponson">
            <description>Sponson_lift</description>
            <table>
              <independentVar lookup="row">attitude/roll-rad</independentVar>
              <independentVar lookup="column">velocities/vc-kts</independentVar>
              <independentVar lookup="table">inertia/weight-lbs</independentVar>
              <tableData breakPoint="47850.0000">
                      	        17.000   34.0000 44.0000 60.0000
                  -0.0690	0.0000   0.0000	 0.0000  1.0000
                  -0.0590	0.0000   0.0000	 0.5000  1.0000
                  -0.0490	0.0000   0.5000	 1.0000  1.0000
                  -0.0290	0.0000   1.0000	 1.0000  1.0000
                   0.0290	0.0000   1.0000	 1.0000  1.0000
                   0.0490	0.0000   0.5000	 1.0000  1.0000
                   0.0590	0.0000   0.0000	 0.5000  1.0000
                   0.0690	0.0000   0.0000	 0.0000  1.0000
              </tableData>
              <tableData breakPoint="84000.0000">
                       	        21.000   42.0000 52.0000 80.0000
                  -0.0600	0.0000   0.0000	 0.0000  1.0000
                  -0.0500	0.0000   0.0000	 0.5000  1.0000
                  -0.0400	0.0000   0.5000	 1.0000  1.0000
                  -0.0200	0.0000   1.0000	 1.0000  1.0000
                   0.0200	0.0000   1.0000	 1.0000  1.0000
                   0.0400	0.0000   0.5000	 1.0000  1.0000
                   0.0500	0.0000   0.0000	 0.5000  1.0000
                   0.0600	0.0000   0.0000	 0.0000  1.0000
              </tableData>
            </table>
        </function>

        <documentation>
             The bow climbs over the wave hump :
             - strong pitch (positiv moment) until the hump (nose up on the hump),
             then light nose down (negativ moment).
             - the center of buoyancy shifts with speed :
             near center of gravity (step) at rest, then aft the step (afterbody)
             when ploughing the wave hump; finally forwards the step (forebody), once planing on the step.
             - 80 % when going out of water.

             similar to  NACA TN 512, figure 13.
        </documentation>
        <function name="aero/function/kHmstep">
            <description>Step_moment</description>
            <table>
                <independentVar lookup="row">velocities/vc-kts</independentVar>
                <independentVar lookup="column">aero/h_b-mac-ft</independentVar>
                <independentVar lookup="table">inertia/weight-lbs</independentVar>
                <tableData breakPoint="47850.0000">
                                0.0450	0.0550	0.0650
                    0.0000	0.0000	0.0000	0.0000
                    17.0000    -1.6000 -1.2800	0.0000
                    34.0000	0.8000	0.6400	0.0000
                    51.0000	0.0000	0.0000	0.0000
                    57.000     -0.1000 -0.0800	0.0000
                </tableData>
                <tableData breakPoint="84000.0000">
                                0.0490	0.0590	0.0690
                    0.0000	0.0000	0.0000	0.0000
                    21.0000    -1.6000 -1.2800	0.0000
                    42.0000	0.8000	0.6400	0.0000
                    63.0000	0.0000	0.0000	0.0000
                    72.0000    -0.1000 -0.0800	0.0000
                </tableData>
            </table>
        </function>

        <documentation>
            created to help the takeoff at full load.
        </documentation>
        <function name="aero/function/kCLge">
            <description>Change_in_lift_due_to_ground_effect</description>
            <table>
                <independentVar>aero/h_b-mac-ft</independentVar>
                <tableData>
                    0.0000	1.2000
                    0.1000	1.1500
                    0.1500	1.0900
                    0.2000	1.0200
                    1.1000	1.0000
                </tableData>
            </table>
        </function>

        <documentation>
             Compensates compression of nose gear by speed :
             - enough strong to make rise the bow, and have an effect on flight commands.
             - 80 % when going out of water.
             - hydrodynamic lift indirectly provides weathervaning if wing has enough alpha.
             In reality, cross wind applied on the center of gravity (CG), is opposed by the water acting on a sinking float.
             Hence a momment around the center of buoyancy (CB).
             - gear reaction code seems not appropriate in this case.
        </documentation>
        <function name="aero/function/kAmgear">
            <description>Compression_correction</description>
            <table>
                <independentVar lookup="row">velocities/vc-kts</independentVar>
                <independentVar lookup="column">aero/h_b-mac-ft</independentVar>
                <independentVar lookup="table">inertia/weight-lbs</independentVar>
                <tableData breakPoint="47850.0000">
                                0.0450	0.0550	0.0650
                    0.0000	0.0000	0.0000	0.0000
                    17.0000	2.0000	1.6000	0.0000
                    34.0000	1.0000	0.8000	0.0000
                    51.0000	0.7000	0.5600	0.0000
                    57.0000	0.7000	0.5600	0.0000
                </tableData>
                <tableData breakPoint="84000.0000">
                                0.0490	0.0590	0.0690
                    0.0000	0.0000	0.0000	0.0000
                    21.0000	2.0000	1.6000	0.0000
                    42.0000	3.0000	2.4000	0.0000
                    63.0000	0.7000	0.5600	0.0000
                    72.0000	0.7000	0.5600	0.0000
                </tableData>
            </table>
        </function>

        <documentation>
              NACA report 1103 (rebound on touch-down ) :
              - figure 2, khi 3 and 15 deg.
              gamma, velocity angle over water : 0 - 80 deg, horizontal is 0. 
              - figure 6, tau 6 deg (used as 3 deg) and tau 12 deg (used as 15 deg). 
              tau, bow pitch over water : 3 - 15 deg, horizontal is 0.

              khi = (sin(tau) / sin(gamma)) x cos(tau + gamma) :
              - no rebound when flight path near vertical (energy absorbed by water).
              - maximum near planing condition.
              - favoured by bow pitch.
        </documentation>
        <function name="aero/function/kHmkhi">
            <description>Rebound_due_to_khi</description>
            <table>
              <independentVar lookup="row">flight-path/gamma-rad</independentVar>
              <independentVar lookup="column">attitude/pitch-rad</independentVar>
              <independentVar lookup="table">velocities/vc-kts</independentVar>
              <tableData breakPoint="42.0000">
                                0.0500	0.2600
                  0.0000	0.0000	0.0000
                  0.3500	0.0000	0.0000
                  0.7000	0.0000	0.0000
                  1.0500	0.0000	0.0000
                  1.4000	0.0000	0.0000
              </tableData>
              <tableData breakPoint="52.0000">
                                0.0500	0.2600
                  0.0000       15.0000 13.0000
                  0.3500	0.3500	1.1000
                  0.7000	0.2500	0.6000
                  1.0500	0.1500	0.5000
                  1.4000	0.0500	0.4000
              </tableData>
            </table>
        </function>

        <axis name="DRAG">
            <documentation>
              - more drag inside water than air (ratio 7/1).
              - increases the takeoff distance.
              - could depend of weight.
            </documentation>
            <function name="aero/coefficient/HDhull0">
                <description>Drag_at_hump</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kHDhull</property>
                    <value>0.1200</value>
                </product>
            </function>

            <documentation>
               added.
            </documentation>
            <function name="aero/coefficient/CDo">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <value>0.0274</value>
                </product>
            </function>
            <documentation>
              - renamed from CDo.
              - substracted Cdo 0.0274.
            </documentation>
            <function name="aero/coefficient/CDalpha">
                <description>Drag_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                             -1.5700	1.4726
                             -0.2600	0.0286
                              0.0000	0.0006
                              0.2600	0.0286
                              1.5700	1.4726
                          </tableData>
                      </table>
                </product>
            </function>

            <documentation>
               1/10 of wing.
            </documentation>
            <function name="aero/coefficient/CDsponsono">
                <description>Sponson_drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kHLsponson</property>
                    <value>0.0027</value>
                </product>
            </function>
            <documentation>
               1/10 of wing.
            </documentation>
            <function name="aero/coefficient/CDsponson">
                <description>Drag_due_to_sponson</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kHLsponson</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                             -1.5700	0.1473
                             -0.2600	0.0029
                              0.0000	0.0001
                              0.2600	0.0029
                              1.5700	0.1473
                          </tableData>
                      </table>
                </product>
            </function>

            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <value>0.0390</value>
                </product>
            </function>
            <function name="aero/coefficient/CDmach">
                <description>Drag_due_to_mach</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.0000
                              0.7000	0.0000
                              1.1000	0.0230
                              1.8000	0.0150
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDflap">
                <description>Drag_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-cmd-norm</property>
                    <value>0.0350</value>
                </product>
            </function>
            <function name="aero/coefficient/CDsb">
                <description>Drag_due_to_speedbrakes</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0280</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                             -1.5700	1.2300
                             -0.2600	0.0500
                              0.0000	0.0000
                              0.2600	0.0500
                              1.5700	1.2300
                          </tableData>
                      </table>
                </product>
            </function>
            <documentation>
              - drag is always positiv.
              - divide by 10.
            </documentation>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <abs>
                    <property>fcs/elevator-pos-norm</property>
                    </abs>
                    <value>0.0035</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-1.0000</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <documentation>
              - and missing lift for flaps.
              - introducing roll quickly reduces lift at landing.
            </documentation>
            <function name="aero/coefficient/HLstep0">
                <description>Lift_at_step</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kHLsponson</property>
                    <property>aero/function/kHLstep</property>
                    <value>1.0000</value>
                </product>
            </function>

            <documentation>
               created.
            </documentation>
            <function name="aero/coefficient/CLo">
                <description>Lift_at_zero_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                    <value>0.2400</value>
                </product>
            </function>
            <documentation>
               substracted CLo 0.240.
            </documentation>
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                             -0.2000   -0.9800
                              0.0000	0.0000
                              0.2400	1.1600
                              0.6000	0.4640
                          </tableData>
                      </table>
                </product>
            </function>

            <documentation>
              - and missing lift for flaps.
              - 1/10 of wing.
            </documentation>
            <function name="aero/coefficient/CLsponsono">
                <description>Sponson_lift_at_zero_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kHLsponson</property>
                    <property>aero/function/kCLge</property>
                    <value>0.0240</value>
                </product>
            </function>
            <documentation>
              - and missing lift for flaps.
              - 1/10 of wing.
            </documentation>
            <function name="aero/coefficient/CLsponson">
                <description>Lift_due_to_sponson</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kHLsponson</property>
                    <property>aero/function/kCLge</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                             -0.2000   -0.0980
                              0.0000	0.0000
                              0.2400	0.1160
                              0.6000	0.0464
                          </tableData>
                      </table>
                </product>
            </function>

            <documentation>
              - unchanged.
              - 0.9 is the value to takeoff at 2/3 full load; water provides the missing lift.
            </documentation>
            <function name="aero/coefficient/dCLflap">
                <description>Delta_Lift_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-cmd-norm</property>
                    <property>aero/function/kCLge</property>
                    <value>0.6000</value>
                </product>
            </function>
            <function name="aero/coefficient/dCLsb">
                <description>Delta_Lift_due_to_speedbrake</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/CLde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.2000</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.1500
                              2.0000	0.0500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0100</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Hmstep0">
                <description>Trim_moment_at_step</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/function/kHmstep</property>
                    <value>1.0000</value>
                </product>
            </function>

            <documentation>
               could depend of weight.
            </documentation>
            <function name="aero/coefficient/Amgear0">
                <description>Compression</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/function/kAmgear</property>
                    <value>0.5000</value>
                </product>
            </function>

            <documentation>
             - rest 0 ft/min.
             - maximum 700 ft/min.
             - before hydrodynamic lift.
            </documentation>
            <function name="aero/coefficient/Hmrebound0">
                <description>Rebound_due_to_vertical_speed</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/function/kHmkhi</property>
                      <table>
                          <independentVar lookup="row">velocities/v-down-fps</independentVar>
                          <independentVar lookup="column">aero/h_b-mac-ft</independentVar>
                          <independentVar lookup="table">inertia/weight-lbs</independentVar>
                          <tableData breakPoint="47850.0000">
                              	        0.0650	0.0700	0.0750
                               0.0000	0.0000	0.0000	0.0000
                              11.6700	0.0000	0.3000	0.0000
                          </tableData>
                          <tableData breakPoint="84000.0000">
                              	        0.0690	0.0740	0.0790
                               0.0000	0.0000	0.0000	0.0000
                              11.6700	0.0000	0.3000	0.0000
                          </tableData>
                      </table>
                </product>
            </function>

            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmde">
                <description>Pitch_moment_due_to_elevator</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	-1.0000
                              2.0000	-0.2500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-22.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-8.0000</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.1200</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnda">
                <description>Adverse_yaw</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>-0.0080</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
        
    <external_reactions>
        <documentation>
          anchor at nose tip.
        </documentation>
        <force name="anchor" frame="BODY">
            <location unit="IN">
                <x>0 </x>
                <y>0 </y>
                <z>0 </z>
            </location>
            <direction>
                <x>-1</x>
                <y>0</y>
                <z>-1</z>
            </direction>
        </force>
    </external_reactions>

</fdm_config>
