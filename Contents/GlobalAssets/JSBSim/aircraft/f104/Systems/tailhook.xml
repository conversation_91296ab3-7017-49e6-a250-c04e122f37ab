<?xml version="1.0"?>

<system name="hook">

  <property>systems/hook/tailhook-cmd-norm</property>
  <property value="5.6">systems/hook/tailhook-length-ft</property>
  <property value="-8.0">systems/hook/tailhook-pos-min-deg</property>
  <property value="42.0">systems/hook/tailhook-pos-max-deg</property>
  <property value="376.0">systems/hook/tailhook-offset-x-in</property>
  <property value="0.0">systems/hook/tailhook-offset-y-in</property>
  <property value="-28.0">systems/hook/tailhook-offset-z-in</property>
  <property value="-8.0">systems/hook/tailhook-pos-deg</property>
  

  <channel name="Hook">

     <kinematic name="systems/hook/tailhook-control">
       <input>systems/hook/tailhook-cmd-norm</input>
       <traverse>
        <setting>
          <position>0</position>
          <time>0</time>
        </setting>
        <setting>
          <position>1</position>
          <time>1.5</time>
        </setting>
       </traverse>
       <output>systems/hook/tailhook-pos-norm</output>
     </kinematic>

     <switch name="systems/hook/ready">
          <default value="0"/>
          <test  logic="AND" value="1">
                systems/hook/tailhook-pos-norm gt 0.99
          </test>
     </switch>

     <scheduled_gain name="systems/hook/hook-decel-multiplier">
      <input>systems/hook/ready</input>
      <table>
        <independentVar lookup="row">gear/unit[1]/wheel-speed-fps</independentVar>
         <tableData>
            0     0.00
            1     0.00
            80    2.20
         </tableData>
      </table>
     </scheduled_gain>

     <pure_gain name="systems/hook/hook-decel-force">
        <input>systems/hook/hook-decel-multiplier</input>
        <gain>inertia/weight-lbs</gain>
     </pure_gain>

     <summer name="systems/hook/force">
        <input>systems/hook/hook-decel-force</input>
        <input>forces/fbx-prop-lbs</input>
     </summer>

     <fcs_function name="systems/hook/animation-norm">
       <function>
         <product>
           <sum>
            <property>systems/hook/tailhook-pos-deg</property>
            <value>8</value>
           </sum>
           <value>0.02</value>
         </product>
       </function>
       <output>gear/tailhook-pos-norm</output>
     </fcs_function>

  </channel>

</system>
