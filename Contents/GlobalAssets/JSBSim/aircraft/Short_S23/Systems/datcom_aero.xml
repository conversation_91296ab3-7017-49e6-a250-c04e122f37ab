<?xml version="1.0"?>
<!--


  ****************** IMPORTANT NOTICES **********************

   This file was generated by Datcom<PERSON>, a superset of 
    the USAF Digital DATCOM program.
   This file is considered an intermediary file in the 
   DATCOM/JSBSim process. This file may be overwritten without
   warning, so any changes that you make in here may be lost.

   If you intend to use this file as it is and modify it, make
   sure that you take the necessary precautions to prevent it 
   from being overwritten, like renaming it.


   ***********************************************************


   D<PERSON><PERSON><PERSON>IMER REQUIRED BY HQ USAF FOR PUBLIC RELEASE APPROVAL

   THIS SOFTWARE AND ANY ACCOMPANYING DOCUMENTATION
   IS RELEASED "AS IS".  THE U.S. GOVERNMENT MAKES NO
   WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, CONCERNING
   THIS SOFTWARE AND AN<PERSON> ACCOMPANYING DOCUMENTATION,
   INCLUDING, WITH<PERSON><PERSON> LIMITATION, <PERSON>Y WARRANTIES OF
   MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.
   IN NO EVENT WILL THE U.S. GOVERNMENT BE LIABLE FOR ANY
   DAMAGES, INCLUDING LOST PROFITS, LOST SAVINGS OR OTHER
   INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE 
   USE, OR INABILITY TO USE, THIS SOFTWARE OR ANY
   ACCOMPANYING DOCUMENTATION, EVEN IF INFORMED IN ADVANCE
   OF THE POSSIBILITY OF SUCH DAMAGES.


   ***********************************************************
-->



<!-- **********************************************************************
        AERODYNAMICS
     *******************************************************************-->

   <aerodynamics>

      <alphalimits unit="RAD">
         <min>  -.2793    </min>
         <max>   .4189    </max>
      </alphalimits>




      <!-- ************************* 
                Sign Conventions    
           ************************* 

           Control displacements
                Stick    FWD  + / Aft -
                Stick    Left + / Right -
                Wheel    CCW  + / CW -
                Pedal    Left + / Right -
                Elevator trim Nose Up + / Nose down -

           Surfaces
                Elevator  TED + / TEU -
                R Ail     TED + / TEU -
                L Ail     TED + / TEU -
                Rudder    TEL + / TER -

           F&M, Accel, Rates, Displacements
                Pitch     Up  + / Down -
                Roll      Rgt + / Left -
                Yaw       Rgt + / Left -

           Other
                Alpha     Up  + / Down -
                Beta Wind in right ear + / Wind in left ear -
                Slip Ball right of center + / left of center -
      -->




      <!-- ************************************** 
                ASSUMPTIONS AND LIMITATIONS

           There is no interaction between deflected surfaces modeled
           so there is no change in elevator effects with the flaps
           deflected, for example.

           Terms known to be missing from DATCOM:
              Power effects
              Ground effects
              Cyr, CyDr, Cyda, ClDr, Cndr

           **************************************
      -->


      <!--
           The following ground effect tables are NOT generated
           by DATCOM, but provide representative effects.
           These terms are not currently used in this model.
      -->


      <function name="aero/function/ground-effect-factor-lift">
         <description>Change in lift due to ground effect factor</description>
         <product>
            <table>
               <independentVar lookup="row">aero/h_b-mac-ft</independentVar>
               <tableData>
                  0.0  1.203
                  0.1  1.127
                  0.15 1.090
                  0.2  1.073
                  0.3  1.046
                  0.4  1.055
                  0.5  1.019
                  0.6  1.013
                  0.7  1.008
                  0.8  1.006
                  0.9  1.003
                  1.0  1.002
                  1.1  1.0
               </tableData>
            </table>
         </product>
      </function>

      <function name="aero/function/ground-effect-factor-drag">
         <description>Change in drag due to ground effect</description>
         <product>
            <table>
               <independentVar lookup="row">aero/h_b-mac-ft</independentVar>
               <tableData>
                  0.0  0.480
                  0.1  0.515
                  0.15 0.629
                  0.2  0.709
                  0.3  0.815
                  0.4  0.882
                  0.5  0.928
                  0.6  0.962
                  0.7  0.988
                  0.8  1.0
                  0.9  1.0
                  1.0  1.0
                  1.1  1.0
               </tableData>
            </table>
         </product>
      </function>





<!-- ********************************************************************** -->


     <axis name="LIFT">

         <function name="aero/coefficient/CLwbh">
            <description>
               Lift due to alpha
               Increase in CL decreases Period and damping,Dutch Roll damping
               CL is low for landing
            </description>
            <product>
               <property>aero/function/ground-effect-factor-lift</property>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <!-- The wing incidence is 3 deg. -->
               <!-- value>0.90</value --> <!-- Make CL at 17-3 deg. 1.65. -->
               <value>0.83</value> <!-- Make CL at 12-3 deg. 1.30. -->
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793        -1.205    
                       -.1745        -.5125    
                       -.1396        -.2906    
                       -.1047        -.7663E-01
                       -.6981E-01     .1375    
                       -.3491E-01     .3586    
                         .000         .5854    
                        .3491E-01     .8176    
                        .6981E-01     1.055    
                        .1396         1.490    
                        .1745         1.654    
                        .2094         1.776    
                        .2443         1.845    
                        .2618         1.837    
                        .2793         1.807    
                        .2967         1.554    
                        .3142         1.203    
                        .3491         .6313    
                        .3840         .1405    
                        .4189        -.2022    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CLq">
            <description>
               Basic Lift Coefficient due to pitch rate(per radian)
            </description>
            <product>
               <property>velocities/q-aero-rad_sec</property>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>aero/ci2vel</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793         7.488    
                       -.1745         7.488    
                       -.1396         7.488    
                       -.1047         7.488    
                       -.6981E-01     7.488    
                       -.3491E-01     7.488    
                         .000         7.488    
                        .3491E-01     7.488    
                        .6981E-01     7.488    
                        .1396         7.488    
                        .1745         7.488    
                        .2094         7.488    
                        .2443         7.488    
                        .2618         7.488    
                        .2793         7.488    
                        .2967         7.488    
                        .3142         7.488    
                        .3491         7.488    
                        .3840         7.488    
                        .4189         7.488    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CLad">
            <description>
               Basic Lift Coefficient due to AOA rate
               Important contributor to Short-Period damping
               For low Cla, aircraft must land at high alpha
            </description>
            <product>
               <property>aero/alphadot-rad_sec</property>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>aero/ci2vel</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793         1.783    
                       -.1745         1.841    
                       -.1396         1.857    
                       -.1047         1.881    
                       -.6981E-01     1.954    
                       -.3491E-01     2.044    
                         .000         2.118    
                        .3491E-01     2.079    
                        .6981E-01     1.933    
                        .1396         1.536    
                        .1745         1.196    
                        .2094         .7401    
                        .2443         .3574    
                        .2618        -.6955    
                        .2793        -1.752    
                        .2967        -3.991    
                        .3142        -5.246    
                        .3491        -3.057    
                        .3840        -2.436    
                        .4189        -2.063    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CLdF3L">
            <description>
               Lift Coefficient due to Left Fowler Flaps Deflection
               Positive surface deflection is trailing edge down
               Ultimately, we will feed this left flap pos
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <table>
                  <independentVar lookup="row">fcs/flap-pos-deg</independentVar>
                  <tableData>
                         .000          .000    
                        8.000         .2988E-01
                        12.00         .5975E-01
                        25.00         .1285    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CLdF3R">
            <description>
               Lift Coefficient due to Right Fowler Flaps Deflection
               Positive surface deflection is trailing edge down
               Ultimately, we will feed this right flap pos
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <table>
                  <independentVar lookup="row">fcs/flap-pos-deg</independentVar>
                  <tableData>
                         .000          .000    
                        8.000         .2988E-01
                        12.00         .5975E-01
                        25.00         .1285    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CLDe">
            <description>
               Lift Coefficient due to Elevator Deflection
               Positive surface deflection is trailing edge down
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <table>
                  <independentVar lookup="row">fcs/elevator-pos-rad</independentVar>
                  <tableData>
                       -.3491        -.1026    
                       -.2618        -.9847E-01
                       -.1745        -.7035E-01
                       -.8727E-01    -.3518E-01
                         .000         .7035E-04
                        .8727E-01     .3518E-01
                        .1745         .7035E-01
                        .2618         .9847E-01
                        .3491         .1026    
                  </tableData>
               </table>
            </product>
         </function>

     </axis>




<!-- ********************************************************************** -->


     <axis name="DRAG">

         <function name="aero/coefficient/CD">
            <description>
               Basic Drag Coefficient
               Sense: Always positive
               Main contributor to Phugoid damping: Greater Cd, Better damping
            </description>
            <product>
               <property>aero/function/ground-effect-factor-drag</property>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <value>1.23</value> <!-- Adjusted for climb time. -->
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793         .7340E-01
                       -.1745         .2602E-01
                       -.1396         .1883E-01
                       -.1047         .1537E-01
                       -.6981E-01     .1568E-01
                       -.3491E-01     .1983E-01
                         .000         .2837E-01
                        .3491E-01     .4131E-01
                        .6981E-01     .5898E-01
                        .1396         .1021    
                        .1745         .1211    
                        .2094         .1365    
                        .2443         .1458    
                        .2618         .1429    
                        .2793         .1390    
                        .2967         .1005    
                        .3142         .7088E-01
                        .3491         .4924E-01
                        .3840         .4910E-01
                        .4189         .5829E-01
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CdDf3L">
            <description>
               Drag Coefficient due to Left Fowler Flaps Deflection
               Positive surface deflection is trailing edge down
               Ultimately, we will feed this left flap pos
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <table>
                  <independentVar lookup="row">aero/alpha-deg</independentVar>
                  <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                  <tableData>
                                       .000         8.000         12.00         25.00    
                       -16.00          .000        -.2250E-02    -.4354E-02    -.8641E-02
                       -10.00          .000        -.1154E-02    -.2163E-02    -.3929E-02
                       -8.000          .000        -.7889E-03    -.1432E-02    -.2359E-02
                       -6.000          .000        -.4237E-03    -.7018E-03    -.7887E-03
                       -4.000          .000        -.5849E-04     .2865E-04     .7817E-03
                       -2.000          .000         .3067E-03     .7591E-03     .2352E-02
                         .000          .000         .6719E-03     .1489E-02     .3922E-02
                        2.000          .000         .1037E-02     .2220E-02     .5493E-02
                        4.000          .000         .1402E-02     .2950E-02     .7063E-02
                        8.000          .000         .2133E-02     .4411E-02     .1020E-01
                        10.00          .000         .2498E-02     .5142E-02     .1177E-01
                        12.00          .000         .2863E-02     .5872E-02     .1334E-01
                        14.00          .000         .3228E-02     .6602E-02     .1492E-01
                        15.00          .000         .3411E-02     .6968E-02     .1570E-01
                        16.00          .000         .3594E-02     .7333E-02     .1649E-01
                        17.00          .000         .3776E-02     .7698E-02     .1727E-01
                        18.00          .000         .3959E-02     .8063E-02     .1806E-01
                        20.00          .000         .4324E-02     .8794E-02     .1963E-01
                        22.00          .000         .4689E-02     .9524E-02     .2120E-01
                        24.00          .000         .5054E-02     .1025E-01     .2277E-01
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CdDf3R">
            <description>
               Drag Coefficient due to Right Fowler Flaps Deflection
               Positive surface deflection is trailing edge down
               Ultimately, we will feed this right flap pos
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <table>
                  <independentVar lookup="row">aero/alpha-deg</independentVar>
                  <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                  <tableData>
                                       .000         8.000         12.00         25.00    
                       -16.00          .000        -.2250E-02    -.4354E-02    -.8641E-02
                       -10.00          .000        -.1154E-02    -.2163E-02    -.3929E-02
                       -8.000          .000        -.7889E-03    -.1432E-02    -.2359E-02
                       -6.000          .000        -.4237E-03    -.7018E-03    -.7887E-03
                       -4.000          .000        -.5849E-04     .2865E-04     .7817E-03
                       -2.000          .000         .3067E-03     .7591E-03     .2352E-02
                         .000          .000         .6719E-03     .1489E-02     .3922E-02
                        2.000          .000         .1037E-02     .2220E-02     .5493E-02
                        4.000          .000         .1402E-02     .2950E-02     .7063E-02
                        8.000          .000         .2133E-02     .4411E-02     .1020E-01
                        10.00          .000         .2498E-02     .5142E-02     .1177E-01
                        12.00          .000         .2863E-02     .5872E-02     .1334E-01
                        14.00          .000         .3228E-02     .6602E-02     .1492E-01
                        15.00          .000         .3411E-02     .6968E-02     .1570E-01
                        16.00          .000         .3594E-02     .7333E-02     .1649E-01
                        17.00          .000         .3776E-02     .7698E-02     .1727E-01
                        18.00          .000         .3959E-02     .8063E-02     .1806E-01
                        20.00          .000         .4324E-02     .8794E-02     .1963E-01
                        22.00          .000         .4689E-02     .9524E-02     .2120E-01
                        24.00          .000         .5054E-02     .1025E-01     .2277E-01
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CdDe">
            <description>
               Drag Coefficient due to Elevator Deflection
               Positive surface deflection is trailing edge down
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <independentVar lookup="column">fcs/elevator-pos-rad</independentVar>
                  <tableData>
                                     -.3491        -.2618        -.1745        -.8727E-01      .000         .8727E-01     .1745         .2618         .3491    
                       -.2793         .1322E-01     .1247E-01     .7884E-02     .3304E-02    -.5331E-05    -.2029E-02    -.2784E-02    -.2468E-02    -.2352E-02
                       -.1745         .1010E-01     .9482E-02     .5748E-02     .2237E-02    -.3196E-05    -.9616E-03    -.6481E-03     .5232E-03     .7662E-03
                       -.1396         .9101E-02     .8520E-02     .5061E-02     .1893E-02    -.2508E-05    -.6180E-03     .3911E-04     .1486E-02     .1770E-02
                       -.1047         .8094E-02     .7554E-02     .4371E-02     .1548E-02    -.1819E-05    -.2732E-03     .7287E-03     .2452E-02     .2776E-02
                       -.6981E-01     .7113E-02     .6613E-02     .3699E-02     .1212E-02    -.1147E-05     .6281E-04     .1401E-02     .3393E-02     .3757E-02
                       -.3491E-01     .6175E-02     .5712E-02     .3056E-02     .8907E-03    -.5039E-06     .3843E-03     .2044E-02     .4293E-02     .4696E-02
                         .000         .5277E-02     .4851E-02     .2442E-02     .5833E-03     .1110E-06     .6917E-03     .2658E-02     .5155E-02     .5594E-02
                        .3491E-01     .4408E-02     .4017E-02     .1846E-02     .2855E-03     .7066E-06     .9895E-03     .3254E-02     .5989E-02     .6463E-02
                        .6981E-01     .3474E-02     .3121E-02     .1206E-02    -.3437E-04     .1346E-05     .1309E-02     .3894E-02     .6885E-02     .7397E-02
                        .1396         .1391E-02     .1122E-02    -.2204E-03    -.7477E-03     .2773E-05     .2023E-02     .5320E-02     .8883E-02     .9480E-02
                        .1745         .1802E-03    -.3896E-04    -.1049E-02    -.1162E-02     .3602E-05     .2437E-02     .6150E-02     .1004E-01     .1069E-01
                        .2094        -.1233E-02    -.1395E-02    -.2018E-02    -.1646E-02     .4570E-05     .2921E-02     .7118E-02     .1140E-01     .1210E-01
                        .2443        -.2847E-02    -.2943E-02    -.3123E-02    -.2199E-02     .5675E-05     .3474E-02     .8223E-02     .1295E-01     .1372E-01
                        .2618        -.3708E-02    -.3769E-02    -.3713E-02    -.2494E-02     .6265E-05     .3769E-02     .8813E-02     .1377E-01     .1458E-01
                        .2793        -.5045E-02    -.5052E-02    -.4629E-02    -.2952E-02     .7182E-05     .4227E-02     .9729E-02     .1506E-01     .1592E-01
                        .2967        -.6388E-02    -.6341E-02    -.5549E-02    -.3412E-02     .8102E-05     .4687E-02     .1065E-01     .1635E-01     .1726E-01
                        .3142        -.8765E-02    -.8621E-02    -.7177E-02    -.4226E-02     .9724E-05     .5501E-02     .1228E-01     .1863E-01     .1964E-01
                        .3491        -.1217E-01    -.1188E-01    -.9506E-02    -.5391E-02     .1206E-04     .6666E-02     .1461E-01     .2189E-01     .2304E-01
                        .3840        -.1534E-01    -.1493E-01    -.1168E-01    -.6477E-02     .1424E-04     .7752E-02     .1678E-01     .2493E-01     .2621E-01
                        .4189        -.1816E-01    -.1764E-01    -.1361E-01    -.7444E-02     .1615E-04     .8719E-02     .1871E-01     .2764E-01     .2903E-01
                  </tableData>
               </table>
            </product>
         </function>

     </axis>




<!-- ********************************************************************** -->


     <axis name="SIDE">

         <function name="aero/coefficient/Cyb">
            <description>
               Side Force coefficient due to Sideslip
                Contributes to damping of Dutch Roll mode
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <sin><property>aero/beta-rad</property></sin>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793        -.5277    
                       -.1745        -.5277    
                       -.1396        -.5277    
                       -.1047        -.5277    
                       -.6981E-01    -.5277    
                       -.3491E-01    -.5277    
                         .000        -.5277    
                        .3491E-01    -.5277    
                        .6981E-01    -.5277    
                        .1396        -.5277    
                        .1745        -.5277    
                        .2094        -.5277    
                        .2443        -.5277    
                        .2618        -.5277    
                        .2793        -.5277    
                        .2967        -.5277    
                        .3142        -.5277    
                        .3491        -.5277    
                        .3840        -.5277    
                        .4189        -.5277    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/Cyp">
            <description>
               Side Force Coefficient due to Roll Rate
            </description>
            <product>
               <property>velocities/p-aero-rad_sec</property>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>aero/bi2vel</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793        -.8364E-01
                       -.1745        -.1082    
                       -.1396        -.1154    
                       -.1047        -.1221    
                       -.6981E-01    -.1295    
                       -.3491E-01    -.1385    
                         .000        -.1496    
                        .3491E-01    -.1635    
                        .6981E-01    -.1827    
                        .1396         .1989    
                        .1745        -.1426    
                        .2094        -.1626    
                        .2443        -.1731    
                        .2618        -.1675    
                        .2793        -.1696    
                        .2967        -.1282    
                        .3142        -.7662E-01
                        .3491         .3365E-02
                        .3840         .7152E-01
                        .4189         .1194    
                  </tableData>
               </table>
            </product>
         </function>


         <!-- ************************
              Not calculated by DATCOM
              ************************
         -->

         <function name="aero/coefficient/Cyr">
            <description>
               Side Force due to Yaw Rate      (DATCOM does not calculate)
               Effect is small
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>aero/bi2vel</property>
               <property>velocities/r-aero-rad_sec</property>
               <value>    .000    </value>
            </product>
         </function>


         <!-- ************************
              Not calculated by DATCOM
              ************************
         -->

         <function name="aero/coefficient/CyDr">
            <description>
               Side Force due to rudder          (DATCOM does not calculate)
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>fcs/rudder-pos-rad</property>
               <value>    .000    </value>
            </product>
         </function>


         <!-- ************************
              Not calculated by DATCOM
              ************************
         -->

         <function name="aero/coefficient/CyDa">
            <description>
               Side Force due to aileron        (DATCOM does not calculate)
               Usually neglected
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/Sw-sqft</property>
               <property>fcs/left-aileron-pos-rad</property>
               <value>    .000    </value>
            </product>
         </function>

     </axis>




<!-- ********************************************************************** -->


     <axis name="ROLL">

         <function name="aero/coefficient/Clb">
            <description>
               Roll Moment coefficient due to Beta
               Decrease of Clb to small negative value improves Dutch Roll Damping
               High Positive value leads to excessive spiral instability
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <sin><property>aero/beta-rad</property></sin>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793        -.1357    
                       -.1745        -.1271    
                       -.1396        -.1240    
                       -.1047        -.1208    
                       -.6981E-01    -.1175    
                       -.3491E-01    -.1142    
                         .000        -.1110    
                        .3491E-01    -.1079    
                        .6981E-01    -.1048    
                        .1396        -.9820E-01
                        .1745        -.9428E-01
                        .2094        -.8986E-01
                        .2443        -.8483E-01
                        .2618        -.8170E-01
                        .2793        -.7824E-01
                        .2967        -.7208E-01
                        .3142        -.6456E-01
                        .3491        -.5134E-01
                        .3840        -.3930E-01
                        .4189        -.2910E-01
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/Clp">
            <description>
               Roll Moment coefficient due to roll rate
               Clp alone determines damping-in-roll characteristics
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <property>aero/bi2vel</property>
               <property>velocities/p-aero-rad_sec</property>
               <value>-1.0</value>
               <table>
                  <independentVar lookup="row">aero/alpha-deg</independentVar>
                  <tableData>
                       -16.00         .3294    
                       -10.00         .3162    
                       -8.000         .3085    
                       -6.000         .3066    
                       -4.000         .3176    
                       -2.000         .3313    
                         .000         .3415    
                        2.000         .3479    
                        4.000         .3366    
                        8.000         .2403    
                        10.00         .1712    
                        12.00         .9202E-01
                        14.00        -.8719E-01
                        15.00        -.1715    
                        16.00        -.6740    
                        17.00        -1.212    
                        18.00        -1.159    
                        20.00        -.8836    
                        22.00        -.6817    
                        24.00        -.4080    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/Clr">
            <description>
               Roll Moment coefficient due to yaw rate
               Considerable effect on Spiral mode. Large 
               positive values leads to strong sprial instability
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <property>aero/bi2vel</property>
               <property>velocities/r-aero-rad_sec</property>
               <table>
                  <independentVar lookup="row">aero/alpha-deg</independentVar>
                  <tableData>
                       -16.00        -.1690    
                       -10.00        -.4933E-01
                       -8.000        -.1181E-01
                       -6.000         .2396E-01
                       -4.000         .6027E-01
                       -2.000         .9837E-01
                         .000         .1379    
                        2.000         .1786    
                        4.000         .2199    
                        8.000         .2927    
                        10.00         .3160    
                        12.00         .3307    
                        14.00         .3341    
                        15.00         .3227    
                        16.00         .3091    
                        17.00         .2319    
                        18.00         .1476    
                        20.00         .2074E-01
                        22.00        -.8871E-01
                        24.00        -.1616    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/ClDs4">
            <description>
               Roll Moment coefficient due to Left Double Slotted Flaps Deflection
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <table>
                  <independentVar lookup="row">fcs/left-aileron-pos-rad</independentVar>
                  <tableData>
                       -.6981        -.7359E-01
                       -.3491        -.4245E-01
                       -.1745        -.2123E-01
                       -.8727E-01    -.1061E-01
                         .000          .000    
                        .8727E-01     .1061E-01
                        .1745         .2123E-01
                        .3491         .4245E-01
                        .6981         .7359E-01
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CldF3">
            <description>
               Roll Moment Coefficient due to Asymetrical Fowler Flaps Deflection
               calculated as difference between left and right flap lift coef,
               times distance from centerline to MAC of surface.
            </description>
            <product>
               <value>     12.24</value>
               <difference>
                  <property>aero/coefficient/CLdF3R</property>
                  <property>aero/coefficient/CLdF3L</property>
               </difference>
            </product>
         </function>


         <!-- ************************
              Not calculated by DATCOM
              ************************
         -->

         <function name="aero/coefficient/ClDr">
            <description>
               Roll moment due to rudder        (DATCOM does not calculate)
               Usually insignificant in dynamic stability considerations
               but is used in autopilot work
            </description>
            <product>
               <property>metrics/bw-ft</property>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>fcs/rudder-pos-rad</property>
               <value>    .000    </value>
            </product>
         </function>

     </axis>




<!-- ********************************************************************** -->


     <axis name="PITCH">

         <function name="aero/coefficient/Cm_basic">
            <description>
               Basic_Pitch_moment_coefficient
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/cbarw-ft</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793         .3419    
                       -.1745         .2024    
                       -.1396         .1567    
                       -.1047         .1096    
                       -.6981E-01     .6524E-01
                       -.3491E-01     .2272E-01
                         .000        -.1663E-01
                        .3491E-01    -.5525E-01
                        .6981E-01    -.9550E-01
                        .1396        -.1824    
                        .1745        -.2293    
                        .2094        -.2787    
                        .2443        -.2787    
                        .2618        -.2787    
                        .2793        -.2787    
                        .2967        -.2787    
                        .3142        -.2787    
                        .3491        -.2787    
                        .3840        -.2787    
                        .4189        -.2787    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/Cmq">
            <description>
               Pitch moment coefficient due to pitch rate(per radian)
                Pitch Damping Derivative
               Very important to Short Period damping of oscillations
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/cbarw-ft</property>
               <property>aero/ci2vel</property>
               <property>velocities/q-aero-rad_sec</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793        -11.21    
                       -.1745        -11.21    
                       -.1396        -11.21    
                       -.1047        -11.21    
                       -.6981E-01    -11.21    
                       -.3491E-01    -11.21    
                         .000        -11.21    
                        .3491E-01    -11.21    
                        .6981E-01    -11.21    
                        .1396        -11.21    
                        .1745        -11.21    
                        .2094        -11.21    
                        .2443        -11.21    
                        .2618        -11.21    
                        .2793        -11.21    
                        .2967        -11.21    
                        .3142        -11.21    
                        .3491        -11.21    
                        .3840        -11.21    
                        .4189        -11.21    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/Cmadot">
            <description>
               Pitch moment coefficient due to AOA rate(per radian)
               Negitive Cmad increase Short Period damping
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/cbarw-ft</property>
               <property>aero/ci2vel</property>
               <property>aero/alphadot-rad_sec</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793        -5.541    
                       -.1745        -5.721    
                       -.1396        -5.770    
                       -.1047        -5.845    
                       -.6981E-01    -6.073    
                       -.3491E-01    -6.351    
                         .000        -6.583    
                        .3491E-01    -6.461    
                        .6981E-01    -6.006    
                        .1396        -4.772    
                        .1745        -3.715    
                        .2094        -2.300    
                        .2443        -1.111    
                        .2618         2.161    
                        .2793         5.443    
                        .2967         12.40    
                        .3142         16.30    
                        .3491         9.499    
                        .3840         7.571    
                        .4189         6.410    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CmDe">
            <description>
               Pitch moment coefficient due to elevator deflection
               Positive surface deflection is trailing edge down
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/cbarw-ft</property>
               <table>
                  <independentVar lookup="row">fcs/elevator-pos-rad</independentVar>
                  <tableData>
                       -.3491         .2876    
                       -.2618         .2748    
                       -.1745         .1961    
                       -.8727E-01     .9804E-01
                         .000        -.1961E-03
                        .8727E-01    -.9804E-01
                        .1745        -.1961    
                        .2618        -.2748    
                        .3491        -.2880    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CmDf3L">
            <description>
               Pitch moment coefficient due to left Fowler Flaps Deflection
               Positive surface deflection is trailing edge down
               Ultimately, we will feed this left flap pos
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/cbarw-ft</property>
               <table>
                  <independentVar lookup="row">fcs/flap-pos-deg</independentVar>
                  <tableData>
                         .000          .000    
                        8.000        -.1259E-02
                        12.00        -.2519E-02
                        25.00        -.5416E-02
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CmDf3R">
            <description>
               Pitch moment coefficient due to right Fowler Flaps Deflection
               Positive surface deflection is trailing edge down
               Ultimately, we will feed this right flap pos
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/cbarw-ft</property>
               <table>
                  <independentVar lookup="row">fcs/flap-pos-deg</independentVar>
                  <tableData>
                         .000          .000    
                        8.000        -.1259E-02
                        12.00        -.2519E-02
                        25.00        -.5416E-02
                  </tableData>
               </table>
            </product>
         </function>

     </axis>




<!-- ********************************************************************** -->


     <axis name="YAW">

         <function name="aero/coefficient/Cnb">
            <description>
               Yaw moment coefficient due to sideslip(per radian)
               Determines Dutch Roll and Spiral characteristics
               Prevents side-slip and yawing moments
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <sin><property>aero/beta-rad</property></sin>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793         .7888E-01
                       -.1745         .7888E-01
                       -.1396         .7888E-01
                       -.1047         .7888E-01
                       -.6981E-01     .7888E-01
                       -.3491E-01     .7888E-01
                         .000         .7888E-01
                        .3491E-01     .7888E-01
                        .6981E-01     .7888E-01
                        .1396         .7888E-01
                        .1745         .7888E-01
                        .2094         .7888E-01
                        .2443         .7888E-01
                        .2618         .7888E-01
                        .2793         .7888E-01
                        .2967         .7888E-01
                        .3142         .7888E-01
                        .3491         .7888E-01
                        .3840         .7888E-01
                        .4189         .7888E-01
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/Cnp">
            <description>
               Yaw moment coefficient due to roll rate(per radian)
               Reduces Dutch Roll damping
               positive value desireable
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <property>aero/bi2vel</property>
               <property>velocities/p-aero-rad_sec</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793         .1331    
                       -.1745         .6090E-01
                       -.1396         .3657E-01
                       -.1047         .1245E-01
                       -.6981E-01    -.1281E-01
                       -.3491E-01    -.4026E-01
                         .000        -.7038E-01
                        .3491E-01    -.1042    
                        .6981E-01    -.1439    
                        .1396         .3362    
                        .1745        -.1310    
                        .2094        -.1723    
                        .2443        -.1992    
                        .2618        -.2013    
                        .2793        -.2039    
                        .2967        -.1671    
                        .3142        -.1321    
                        .3491        -.8233E-01
                        .3840        -.3845E-01
                        .4189        -.1051E-01
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/Cnr">
            <description>
               Yaw Moment coefficient due to yaw rate(per radian)
               Main contributor to damping of Dutch Roll
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <property>aero/bi2vel</property>
               <property>velocities/r-aero-rad_sec</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <tableData>
                       -.2793        -.1414    
                       -.1745        -.1455    
                       -.1396        -.1472    
                       -.1047        -.1490    
                       -.6981E-01    -.1509    
                       -.3491E-01    -.1528    
                         .000        -.1549    
                        .3491E-01    -.1571    
                        .6981E-01    -.1595    
                        .1396        -.1641    
                        .1745        -.1653    
                        .2094        -.1656    
                        .2443        -.1647    
                        .2618        -.1630    
                        .2793        -.1610    
                        .2967        -.1543    
                        .3142        -.1486    
                        .3491        -.1422    
                        .3840        -.1388    
                        .4189        -.1367    
                  </tableData>
               </table>
            </product>
         </function>

         <function name="aero/coefficient/CnDf3">
            <description>
               Yaw Moment Coefficient due to Asymetrical Fowler Flaps Deflection
               calculated as difference between left and right flap drag coef,
               times distance from centerline to MAC of surface.
            </description>
            <product>
               <value>     12.24</value>
               <difference>
                  <property>aero/coefficient/CdDf3R</property>
                  <property>aero/coefficient/CdDf3L</property>
               </difference>
            </product>
         </function>

         <function name="aero/coefficient/CnDa">
            <description>
               Yaw Moment coefficient due to plain flap aileron deflection
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <table>
                  <independentVar lookup="row">aero/alpha-rad</independentVar>
                  <independentVar lookup="column">fcs/left-aileron-pos-rad                </independentVar>
                  <tableData>
                                     -.6981        -.3491        -.1745        -.8727E-01      .000         .8727E-01     .1745         .3491         .6981    
                       -.2793        -.9212E-02    -.5320E-02    -.2660E-02    -.1330E-02      .000         .1330E-02     .2660E-02     .5320E-02     .9212E-02
                       -.1745        -.3389E-02    -.1957E-02    -.9785E-03    -.4893E-03      .000         .4893E-03     .9785E-03     .1957E-02     .3389E-02
                       -.1396        -.1536E-02    -.8868E-03    -.4434E-03    -.2217E-03      .000         .2217E-03     .4434E-03     .8868E-03     .1536E-02
                       -.1047         .2470E-03     .1427E-03     .7133E-04     .3566E-04      .000        -.3566E-04    -.7133E-04    -.1427E-03    -.2470E-03
                       -.6981E-01     .2059E-02     .1189E-02     .5945E-03     .2973E-03      .000        -.2973E-03    -.5945E-03    -.1189E-02    -.2059E-02
                       -.3491E-01     .3954E-02     .2283E-02     .1142E-02     .5709E-03      .000        -.5709E-03    -.1142E-02    -.2283E-02    -.3954E-02
                         .000         .5916E-02     .3416E-02     .1708E-02     .8541E-03      .000        -.8541E-03    -.1708E-02    -.3416E-02    -.5916E-02
                        .3491E-01     .7928E-02     .4578E-02     .2289E-02     .1145E-02      .000        -.1145E-02    -.2289E-02    -.4578E-02    -.7928E-02
                        .6981E-01     .9972E-02     .5759E-02     .2879E-02     .1440E-02      .000        -.1440E-02    -.2879E-02    -.5759E-02    -.9972E-02
                        .1396         .1362E-01     .7866E-02     .3933E-02     .1967E-02      .000        -.1967E-02    -.3933E-02    -.7866E-02    -.1362E-01
                        .1745         .1487E-01     .8588E-02     .4294E-02     .2147E-02      .000        -.2147E-02    -.4294E-02    -.8588E-02    -.1487E-01
                        .2094         .1574E-01     .9088E-02     .4544E-02     .2272E-02      .000        -.2272E-02    -.4544E-02    -.9088E-02    -.1574E-01
                        .2443         .1611E-01     .9301E-02     .4651E-02     .2325E-02      .000        -.2325E-02    -.4651E-02    -.9301E-02    -.1611E-01
                        .2618         .1571E-01     .9074E-02     .4537E-02     .2269E-02      .000        -.2269E-02    -.4537E-02    -.9074E-02    -.1571E-01
                        .2793         .1522E-01     .8791E-02     .4396E-02     .2198E-02      .000        -.2198E-02    -.4396E-02    -.8791E-02    -.1522E-01
                        .2967         .1194E-01     .6897E-02     .3449E-02     .1724E-02      .000        -.1724E-02    -.3449E-02    -.6897E-02    -.1194E-01
                        .3142         .8353E-02     .4824E-02     .2412E-02     .1206E-02      .000        -.1206E-02    -.2412E-02    -.4824E-02    -.8353E-02
                        .3491         .2989E-02     .1726E-02     .8630E-03     .4315E-03      .000        -.4315E-03    -.8630E-03    -.1726E-02    -.2989E-02
                        .3840        -.1619E-02    -.9348E-03    -.4674E-03    -.2337E-03      .000         .2337E-03     .4674E-03     .9348E-03     .1619E-02
                        .4189        -.4633E-02    -.2676E-02    -.1338E-02    -.6689E-03      .000         .6689E-03     .1338E-02     .2676E-02     .4633E-02
                  </tableData>
               </table>
            </product>
         </function>


         <!-- *****************************************************
              CnDr is not calculated by DATCOM, but a default value
              is supplied
              *****************************************************-->

         <function name="aero/coefficient/CnDr">
            <description>
               Yaw Coefficient due to rudder    (DATCOM does not calculate)
               High value for controllablity, low value
               for good dynamic stability
            </description>
            <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
               <property>metrics/bw-ft</property>
               <property>fcs/rudder-pos-rad</property>
               <value>  -.6000E-01</value>
            </product>
         </function>

     </axis>




<!-- ********************************************************************** -->


      <function name="aero/elev_hinge_moment_ft_lbs">
         <description>
            Elevator hinge moment in ft-lbs
            HM = q * S * Cbar * ( Ch0 + Cha*alpha + Chd*delta )
            Positive causes trailing edge down movement
            Ch0 is used to artifically trim HM to zero
         </description>
         <product>
               <property>aero/qbar-psf</property>
               <property>metrics/Sw-sqft</property>
            <value>   41.86    </value>	<!-- surface area of elevator -->
            <value>   3.113    </value>	<!-- MAC of elevator -->
            <sum>
 <!--
               <property>aero/Ch0_elev</property>	
 -->
               <product>
                  <property>aero/alpha-rad</property>
                  <table>                            	<!-- Ch-alpha -->
                     <independentVar lookup="row">
                     aero/alpha-rad</independentVar>
                     <tableData>
                          -.2793    	  -.7460E-02
                          -.1745    	  -.7460E-02
                          -.1396    	  -.7460E-02
                          -.1047    	  -.7460E-02
                          -.6981E-01	  -.7460E-02
                          -.3491E-01	  -.7460E-02
                            .000    	  -.7460E-02
                           .3491E-01	  -.7460E-02
                           .6981E-01	  -.7460E-02
                           .1396    	  -.7460E-02
                           .1745    	  -.1254E-01
                           .2094    	  -.1145E-01
                           .2443    	  -.1110E-01
                           .2618    	  -.1110E-01
                           .2793    	  -.1110E-01
                           .2967    	  -.1110E-01
                           .3142    	  -.1110E-01
                           .3491    	  -.1145E-01
                           .3840    	  -.1254E-01
                           .4189    	  -.1110E-01
                     </tableData>
                  </table>
               </product>
               <product>
                  <property>fcs/elevator-pos-rad</property>
                  <table>                            	<!-- Ch-delta elevator -->
                     <independentVar lookup="row">
                     fcs/elevator-pos-rad</independentVar>
                     <tableData>
                          -.3491    	  -.1254E-01
                          -.2618    	  -.1145E-01
                          -.1745    	  -.1110E-01
                          -.8727E-01	  -.1110E-01
                            .000    	  -.1110E-01
                           .8727E-01	  -.1110E-01
                           .1745    	  -.1110E-01
                           .2618    	  -.1145E-01
                           .3491    	  -.1254E-01
                     </tableData>
                  </table>
               </product>
            </sum>
         </product>
      </function>

   </aerodynamics>
