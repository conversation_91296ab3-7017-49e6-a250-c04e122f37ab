<?xml version="1.0"?>
<!--

  Submarine Scout airship flight model for JSBSim.

    Copyright (C) 2007 - 2024  <PERSON>  (anders(at)gidenstam.org)

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.
  
    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.
  
    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
  
-->

<system name="Submarine Scout Crew Autopilot">

  <!-- INTERFACE PROPERTIES -->
  <property>ap/yaw-damper-active</property>
  <property>ap/pitch-setpoint-deg</property>
  <property>ap/pitch-hold-active</property>

  <!-- SENSORS -->

  <!-- CHANNELS -->
  <property value="0.0">ap/rudder-cmd-norm</property>
  <channel name="Rudder">

    <pid name="ap/yaw-damper-pid"> 
      <input> aero/beta-deg </input>
      <kp>   -2.000 </kp>
      <ki>    0.000 </ki>
      <kd>    0.000 </kd>
      <clipto>
        <min>-1.0</min>
        <max> 1.0</max>
      </clipto> 
    </pid>

    <summer name="ap/heading-sum">
      <input> ap/yaw-damper-pid </input>
    </summer>

    <switch name="ap/heading-switch">
      <default value="0.0"/>
      <test logic="AND" value="ap/heading-sum">
            ap/yaw-damper-active GT 0.99
            fcs/rudder-cmd-norm GT -0.1
            fcs/rudder-cmd-norm LT 0.1
      </test>
      <output>ap/rudder-cmd-norm</output>
    </switch>

  </channel>

  <property value="0.0">ap/elevator-cmd-norm</property>
  <channel name="Elevator">

    <pure_gain name="ap/pitch-setpoint-rad">
      <input> ap/pitch-setpoint-deg </input>
      <gain> 0.017453 </gain>
    </pure_gain>

    <summer name="ap/pitch-error-rad">
      <input> attitude/theta-rad </input>
      <input> -ap/pitch-setpoint-rad </input>
    </summer>

    <pid name="ap/elevator-pid">
      <input> ap/pitch-error-rad </input>
      <kp>   5.00 </kp>
      <ki>   0.01 </ki>
      <kd>   0.00 </kd>
      <clipto>
        <min>-1.0</min>
        <max> 1.0</max>
      </clipto> 
    </pid>

    <switch name="ap/elevator-switch">
      <default value="0.0"/>
      <test logic="AND" value="ap/elevator-pid">
            ap/pitch-hold-active GT 0.99
      </test>
      <output>ap/elevator-cmd-norm</output>
    </switch>

  </channel>

</system>
