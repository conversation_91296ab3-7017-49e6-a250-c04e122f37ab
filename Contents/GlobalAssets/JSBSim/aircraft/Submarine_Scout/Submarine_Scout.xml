<?xml version="1.0"?>
<!--

  Submarine Scout airship flight model for JSBSim.

    Copyright (C) 2007 - 2024  <PERSON>  (anders(at)gidenstam.org)

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.
  
    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.
  
    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
  
-->
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="SubmarineScout" version="2.0" release="ALPHA"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>

  <description>Submarine Scout Zero class airship for JSBSim.</description>

  <author>Anders Gidenstam</author>
  <email>anders at gidenstam dot org</email>

  <filecreationdate>2007-02-11</filecreationdate>
  <version>2024-01-10</version>

  <license>
   <licenseName>GPL v2+</licenseName>
   <licenseURL>http://www.gnu.org/licenses/old-licenses/gpl-2.0.html</licenseURL>
  </license>
  <note>
   This model was created using publicly available data, publicly available
   technical reports, textbooks, and guesses. It contains no proprietary or
   restricted data. It has been validated only to the extent that it may seem
   to "fly right", and possibly to comply to published, publicly known,
   performance data (maximum speed, endurance, etc.).

   This model is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.
  </note>

  <reference refID="ADA:1917:Handbook"
             author="Air Department Admirality"
             title="Handbook on S.S. Type Airships; Facsimile; ISBN 1-84574-281-8"
             date="1917"/>
  <reference refID="Freeman:1932:Akron"
             author="Hugh B. Freeman"
             title="Force Measurements on a 1/40-scale Model of the U.S. Airship ``Akron'';  NACA Report #432"
             date="1932"/>
  <reference refID="Mowthrope:1998:Battlebags"
             author="Ces Mowthorpe"
             title="Battlebags  British Airships of the First World War"
             date="1998"/>
  <reference refID="VentryKo:1982:AS"
             author="Lord Ventry and Eugene M. Kolesnik"
             title="Airship Saga;  ISBN 0 7137 1001 2"
             date="1982"/>
  <reference refID="WD:1941:AATM"
             author="War Department"
             title="Airship Aerodynamics  Technical Manual; TM 1-310;  ISBN 1-4102-0614-9"
             date="1941"/>
  <reference refID="Whale:1919:BA"
             author="George Whale"
             title="British Airships: Past, Present and Future;  http://www.gutenberg.org"
             date="1919"/>
 </fileheader>

 <!--
 ==== Metrics ===============================================================
 -->

 <metrics>

  <documentation>
   NOTE: The center of the bow of the envelope is the origo of
         the body coordinate system.
         x/y/z = back/right/up

         The nose of the car is at (13.70, 0.0, -6.85) m
  </documentation>

  <documentation>
   According to [Whale:1919:BA] the horizontal and vertical fins of a
   S.S. B.E.2C ship were of the same size and measured 16' by 8'6" and 
   the rudder/elevators measured 4' by 8'6" each.
   The vertical fin of a S.S. Zero might be longer.

   Total gas volume vol = 70000 ft^3 = 1982.2 m^3.

   Performance numbers from [Whale:1919:BA]:
    - Max speed
      - S.S. B.E.2C:            40 - 50.5 mph
      - S.S. Maurice Farman:    "slightly slower"
      - S.S. Zero               45 mph
    - Endurance
      - S.S. Zero:              expected 17 hours. Record: 50:55
  </documentation>

  <documentation>
   From [AHT,
   http://www.aht.ndirect.co.uk/airships/Technical%20Spec/SSZ%20Airship.htm]:

    Envelope capacity       70,000 cubic feet
    Ballonets               Ballonets (two) capacity 19,600 cubic feet
    Length                  143 feet 5 inches
    Diameter                30 feet
    Height                  46 feet
    Plane area (each)       110 square feet
    Car                     boat-shaped, streamlined,length 17 feet 6 inches
    Engine                  75 hp Rolls Royce
    Propeller               Four-bladed 9 feet diameter
    Petrol tanks            (two) capacity ;120 (UK?) gallons total
    Water ballast           capacity 26 gallons
    Gross lift              2.2 tons
    Disposable lift         0.6 tons
    Endurance at full speed 17 hours
    Top speed               53 mph
    Climb                   1,200 feet per minute
    Turning circle          40 seconds minimum, 230 diameter
  </documentation>

  <wingarea unit="M2">  65.0 </wingarea>  <!-- Cross-sectional area.-->
  <wingspan unit="M">    9.1 </wingspan>  <!-- Envelope diameter. -->
  <chord unit="M">      43.7 </chord>     <!-- Envelope length. -->
  <htailarea unit="M2"> 25.27 </htailarea>
  <htailarm unit="M">   12.15 </htailarm> <!-- From CG. -->
  <vtailarea unit="M2"> 12.64 </vtailarea>
  <vtailarm unit="M">   12.15 </vtailarm> <!-- From CG. -->
  <!-- Total gas volume vol = 70000 ft^3 = 1982.2 m^3 -->

  <!-- Aerodynamic Reference Point. -->
  <location name="AERORP" unit="M">
   <x> 20.0 </x>
   <y> 0.0 </y>
   <z> 0.0 </z>
  </location>

  <location name="EYEPOINT" unit="M">
   <x> 16.1 </x>
   <y>  0.0 </y>
   <z> -6.4 </z>
  </location>

  <!-- Visual Reference Point -->
  <location name="VRP" unit="M">
   <x> 13.7  </x>
   <y>  0.0  </y>
   <z> -6.85 </z>
  </location>

 </metrics>

 <!--
 ==== Mass balance =========================================================
 -->

 <mass_balance>

  <documentation>
   According to [VentryKo:1982:AS] the SS Zero class had a useful lift
   of 1344lbs (~610kg). Considering that the lift of 1 m^3 hydrogen
   approximately is 1 kg that leaves 1370kg of lift for the structure
   itself.
  </documentation>

  <emptywt unit="KG"> 733.50 </emptywt>

  <documentation>
   The empty weight is estimated to consist of:
    - The weight of the envelope (70000 ft^3),
      which according to [ADA:1917:Handbook] consists of
      - Fabric
        - Envelope        1143 lb
        - Ballonets        263 lb
      - Envelope fittings   99 lb
      - Rigging            112 lb
   Not included in the empty weight here:
      - Planes (3)         294 lb
   Total with 3 planes    1911 lb
   Total with 4 planes    2009 lb

   The car, engine and planes will be added as point masses.
  </documentation>

  <documentation>
   % Very rough estimation model of the empty weight CG and inertia tensor.
   % Assume the empty weight of is to 100% a cylindrical shell.

   m = 733.5   % KG
   r = 4.5     % meter
   l = 43.7    % meter

   % Very rough estimate of center of gravity (based on the inertial estimate):
   CGx = 0
   CGy = 0
   CGz = 0

   % Steiners theorem:
   % Ixx = \bar{Ixx} + m sqrt(\bar{y}^2 + \bar{z}^2)
   % Ixy = \bar{Ixy} + m \bar{x} \bar{y}

   % Very rough estimation model of Ixx, Iyy and Izz relative the CG.
   Ixx = m * r^2
         
   Iyy = 1/2*m * r^2 + 1/12*m*l^2

   Izz = 1/2*m * r^2 + 1/12*m*l^2

   Ixy = 0
   Ixz = 0
   Iyz = 0

  </documentation>

  <ixx unit="KG*M2">  14853.0 </ixx>
  <iyy unit="KG*M2"> 124160.0 </iyy>
  <izz unit="KG*M2"> 124160.0 </izz>
  <ixy unit="KG*M2">      0.0 </ixy>
  <ixz unit="KG*M2">      0.0 </ixz>
  <iyz unit="KG*M2">      0.0 </iyz>

  <location name="CG" unit="M">
   <x> 21.850 </x>
   <y>  0.000 </y>
   <z>  0.000 </z>
  </location>

  <!--
  === Ballast ================================================
  -->
  <documentation>
   Ballast for a S.S. B.E.2C according to [Whale:1919:BA]:
    - Water ballast tank
      - Located immediatly behind pilot
      - Capacity 14 gallons.
  </documentation>

  <!-- Experimental ballast setup. -->
  <!-- Center ballast -->
  <pointmass name="Ballast_Center">
   <!-- According to the AHT this should be max 100 kg -->
   <location unit="M">
    <x> 17.22 </x>
    <y>  0.0  </y>
    <z> -7.20 </z>
   </location>
   <weight unit="KG"> 100 </weight>
  </pointmass>

  <!--
       Ballast tanks for fine tuning the CG.
       These are controlled by the FCS.
       The weight in these are unaccounted for!
       NOTE: The combined weight is set in the FCS.
  -->
  <!-- Forward ballast -->
  <pointmass name="Ballast_Forward">
   <location unit="M">
    <x> 0.0 </x>
    <y> 0.0 </y>
    <z> -1.0 </z>
   </location>
   <weight unit="KG"> 0.0 </weight>
  </pointmass>
  <!-- Aft ballast -->
  <pointmass type="Ballast_Aft">
   <location unit="M">
    <x> 43.7 </x>
    <y> 0.0 </y>
    <z> -1.0 </z>
   </location>
   <weight unit="KG"> 0.0 </weight>
  </pointmass>

  <!--
  === Armament ===============================================
  -->
  <documentation>
   According to [VentryKo:1982:AS] page 44 these ships normally carried
   two 65lb bombs set as depth charges.

   According to [Mowthrope:1998:Battlebags] the SSZ type could carry either
   two 110lb or one 250lb bomb on each side of the car.

   The inspiration for my bomb model appears to be a 110lb bomb
   from a photo of the car of SSZ.27.
  </documentation>
  <pointmass name="Bomb1">
   <weight unit="LBS"> 110.0 </weight>
   <location name="POINTMASS" unit="M">
    <x> 17.00 </x>
    <y> -0.57 </y>
    <z> -7.35 </z>
   </location>
  </pointmass>
  <pointmass name="Bomb2">
   <weight unit="LBS"> 110.0 </weight>
   <location name="POINTMASS" unit="M">
    <x> 17.00 </x>
    <y>  0.57 </y>
    <z> -7.35 </z>
   </location>
  </pointmass>

  <!--
  === Crew ===================================================
  -->
  <pointmass name="Crew 1 Observer">
   <weight unit="KG"> 85.0 </weight>
   <location name="POINTMASS" unit="M">
    <x> 14.9  </x>
    <y>  0.0  </y>
    <z> -6.85 </z>
   </location>
  </pointmass>
  <pointmass name="Crew 2 Pilot">
   <weight unit="KG"> 85.0 </weight>
   <location name="POINTMASS" unit="M">
    <x> 16.17 </x>
    <y>  0.0  </y>
    <z> -6.85 </z>
   </location>
  </pointmass>
  <pointmass name="Crew 3 Mechanic">
   <weight unit="KG"> 85.0 </weight>
   <location name="POINTMASS" unit="M">
    <x> 17.22 </x>
    <y>  0.0  </y>
    <z> -6.85 </z>
   </location>
  </pointmass>

  <!--
  === Other point masses =====================================
  -->
  <documentation>
   According to [Whale:1919:BA] the engine of a S.S. B.E.2C
   (also 75h.p. but air cooled) weighed 438lb.
  </documentation>
  <documentation>
   According to [ADA:1917:Handbook] the water cooled 100hp Green engine
   of the S.S. Armstrong-Whitworth was 601 lb including carburettor,
   exhaust pipe, radiator and cooling water. 714 lb complete including
   fuel tanks (and propeller a 46 lb?).
  </documentation>

  <pointmass name="Engine">
   <weight unit="LBS"> 601.0 </weight>
   <location name="POINTMASS" unit="M">
    <x> 18.60 </x>
    <y>  0.00 </y>
    <z> -6.10 </z>
   </location>
  </pointmass>

  <documentation>
   According to [ADA:1917:Handbook] the empty weight of a complete car of a
   S.S. Maurice Farman was 1150 lb including engine (516 + 35 lb),
   wireless and bomb racks.
  </documentation>

  <pointmass name="Car">
   <weight unit="LBS"> 599.0 </weight>
   <location name="POINTMASS" unit="M">
    <x> 16.20 </x>
    <y>  0.00 </y>
    <z> -7.85 </z>
   </location>
  </pointmass>

 </mass_balance>

 <!--
 === Gas cell ==============================================================
 -->
 <buoyant_forces>

  <documentation>
   NOTE: The cell is placed based on the envelope centroid location
         as computed by BRLCAD.
         The automatic valves are implemented using a system.
  </documentation>
  <documentation>
   Envelope and gas control for a S.S. B.E.2C according to [Whale:1919:BA]:
    - Ripping panel at the top of the envelope towards the front
      - 14'5" by 8'
    - Nose stiffeners: 24
    - 2 Ballonets
      - capacity 6,375 feet^3 each
      - automatic spring valves set to open at 25 - 28mm water.
    - 2 gas valves one at the bottom and one at the top of the envelope
      - Bottom valve automatically opens at 30 - 35mm water pressure.
      - Top valve is manual only
  </documentation>
  <documentation>
   Envelope and ballonet volumes according to [Mowthrope:1998:Battlebags]:
    - Envelope    70,000 feet^3
    - 2 Ballonets  9,800 feet^3 each
  </documentation>

  <!-- External environment properties -->
  <property> environment/sun-angle-rad </property>
  <property> environment/sun-radiation-norm </property>

  <!-- Forward declaration of ballonet inflow properties -->
  <property value="0.0">ballonets/in-flow-ft3ps[0]</property>
  <property value="0.0">ballonets/in-flow-ft3ps[1]</property>

  <gas_cell type="HYDROGEN">
   <location unit="M">
    <x> 18.8 </x>
    <y> 0.0 </y>
    <z> 0.0 </z>
   </location>
   <x_radius unit="M"> 22.86 </x_radius>
   <y_radius unit="M">  4.55 </y_radius>
   <z_radius unit="M">  4.55 </z_radius>
   <max_overpressure unit="PA"> 395.0 </max_overpressure>
   <valve_coefficient unit="M4*SEC/KG"> 0.0075 </valve_coefficient>
   <fullness>0.90</fullness>

   <!-- heat exchange with the environment. [lb ft / sec] -->
   <!-- Note: The surface area of an ellipsiod is approximately
              4*pi*((a^p*b^p + a^p*c^p + b^p*c^p)/3)^(1/p)
              where p=1.6075
   -->
   <heat>
    <!-- Heat transfer due to temperature difference. -->
    <function name="buoyant_forces/gas-cell/dU_conduction">
     <product>
      <value> 11186.0 </value> <!-- Surface area [ft2] -->
      <value>    0.05 </value> <!-- Conductivity [lb / (K ft sec)] (Guess) -->
      <difference>
       <property> atmosphere/T-R </property>
       <property> buoyant_forces/gas-cell/temp-R </property>
      </difference>
     </product>
    </function>
    <function name="buoyant_forces/gas-cell/dU_radiation">
     <product>
      <value> 0.1714e-8 </value> <!-- Stefan-Boltzmann's constant
                                      [Btu / (h ft^2 R^4)] -->
      <value>       0.2 </value> <!-- Emissivity [0,1] (Guess) -->
      <value>   11186.0 </value> <!-- Surface area [ft2] -->
      <difference>
       <pow>
        <property> atmosphere/T-R </property>
        <value> 4.0 </value>
       </pow>
       <pow>
        <property> buoyant_forces/gas-cell/temp-R </property>
        <value> 4.0 </value>
       </pow>
      </difference>
     </product>
    </function>
    <!-- Heat gain due to solar radiation. Pure guess work. -->
    <function name="buoyant_forces/gas-cell/dU_sun">
     <product>
      <value>   7.2330 </value> <!-- Conversion to [lb ft / s] -->
      <value>   326.80 </value> <!-- Estimated projected surface area [m2] -->
      <value>  1300.00 </value> <!-- Solar energy flow [W/m2] -->
      <value>     0.01 </value> <!-- Guess -->
      <property> environment/sun-radiation-norm </property>
      <max>
       <value> 0.0 </value>
       <difference>
        <value> 1.5708 </value>
        <property> environment/sun-angle-rad </property>
       </difference>
      </max>
     </product>
    </function>

   </heat>

   <!-- Forward ballonet -->
   <ballonet type="AIR">
    <location unit="M">
     <x>  8.8 </x>
     <y>  0.0 </y>
     <z> -3.1 </z>
    </location>
    <x_radius unit="M">  6.45 </x_radius>
    <y_radius unit="M">  3.00 </y_radius>
    <z_radius unit="M">  2.23 </z_radius>
    <max_overpressure unit="PA"> 295.0 </max_overpressure>
    <valve_coefficient unit="M4*SEC/KG"> 0.01 </valve_coefficient>
    <fullness>0.15</fullness>
    <heat>
     <!-- Heat transfer due to temperature difference. -->
     <function name="buoyant_forces/gas-cell/ballonet[0]/dU_conduction">
      <product>
       <value> 5916.06 </value> <!-- Surface area [ft2] -->
       <value>    0.05 </value> <!-- Conductivity [lb / (K ft sec)] (Guess) -->
       <difference>
        <property> buoyant_forces/gas-cell/temp-R </property>
        <property> buoyant_forces/gas-cell/ballonet[0]/temp-R </property>
       </difference>
      </product>
     </function>
     <function name="buoyant_forces/gas-cell/ballonet[0]/dU_radiation">
      <product>
       <value> 0.1714e-8 </value> <!-- Stefan-Boltzmann's constant
                                       [Btu / (h ft^2 R^4)] -->
       <value>       0.2 </value> <!-- Emissivity [0,1] (Guess) -->
       <value>   5916.06 </value> <!-- Surface area [ft2] -->
       <difference>
        <pow>
         <property> buoyant_forces/gas-cell/temp-R </property>
         <value> 4.0 </value>
        </pow>
        <pow>
         <property> buoyant_forces/gas-cell/ballonet[0]/temp-R </property>
         <value> 4.0 </value>
        </pow>
       </difference>
      </product>
     </function>
    </heat>
    <blower_input>
     <function name="buoyant_forces/gas-cell/ballonet[0]/in-flow-ft3ps">
      <property>ballonets/in-flow-ft3ps[0]</property>
     </function>
    </blower_input>
   </ballonet>
   <!-- Aft ballonet -->
   <ballonet type="AIR">
    <location unit="M">
     <x> 34.0 </x>
     <y>  0.0 </y>
     <z> -2.0 </z>
    </location>
    <x_radius unit="M">  7.55 </x_radius>
    <y_radius unit="M">  2.85 </y_radius>
    <z_radius unit="M">  2.00 </z_radius>
    <max_overpressure unit="PA"> 295.0 </max_overpressure>
    <valve_coefficient unit="M4*SEC/KG"> 0.01 </valve_coefficient>
    <fullness>0.15</fullness>
    <heat>
     <!-- Heat transfer due to temperature difference. -->
     <function name="buoyant_forces/gas-cell/ballonet[1]/dU_conduction">
      <product>
       <value> 6557.48 </value> <!-- Surface area [ft2] -->
       <value>    0.05 </value> <!-- Conductivity [lb / (K ft sec)] (Guess) -->
       <difference>
        <property> buoyant_forces/gas-cell/temp-R </property>
        <property> buoyant_forces/gas-cell/ballonet[1]/temp-R </property>
       </difference>
      </product>
     </function>
     <function name="buoyant_forces/gas-cell/ballonet[1]/dU_radiation">
      <product>
       <value> 0.1714e-8 </value> <!-- Stefan-Boltzmann's constant
                                       [Btu / (h ft^2 R^4)] -->
       <value>       0.2 </value> <!-- Emissivity [0,1] (Guess) -->
       <value>   6557.48 </value> <!-- Surface area [ft2] -->
       <difference>
        <pow>
         <property> buoyant_forces/gas-cell/temp-R </property>
         <value> 4.0 </value>
        </pow>
        <pow>
         <property> buoyant_forces/gas-cell/ballonet[1]/temp-R </property>
         <value> 4.0 </value>
        </pow>
       </difference>
      </product>
     </function>
    </heat>
    <blower_input>
     <function name="buoyant_forces/gas-cell/ballonet[1]/in-flow-ft3ps">
      <property>ballonets/in-flow-ft3ps[1]</property>
     </function>
    </blower_input>
   </ballonet>

  </gas_cell>

 </buoyant_forces>

 <!--
 ==== Ground reactions ======================================================
 -->

 <ground_reactions>

  <!--
  === Landing gears ===================================================
  -->
  <contact type="BOGEY" name="CAR_KEEL_FRONT">
   <location unit="M">
    <x>  14.7 </x>
    <y>   0.0 </y>
    <z>  -8.2 </z>
   </location>
   <static_friction>  1.0 </static_friction>
   <dynamic_friction> 2.0 </dynamic_friction>
   <rolling_friction> 0.8 </rolling_friction>
   <spring_coeff unit="N/M"> 50000 </spring_coeff>
   <damping_coeff unit="N/M/SEC"> 10000 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> NONE </brake_group>
   <retractable>0</retractable>
  </contact>

  <contact type="BOGEY" name="CAR_KEEL_MIDDLE">
   <location unit="M">
    <x>  16.6 </x>
    <y>   0.0 </y>
    <z>  -8.2 </z>
   </location>
   <static_friction>  1.0 </static_friction>
   <dynamic_friction> 2.0 </dynamic_friction>
   <rolling_friction> 0.8 </rolling_friction>
   <spring_coeff unit="N/M"> 50000 </spring_coeff>
   <damping_coeff unit="N/M/SEC"> 10000 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> NONE </brake_group>
   <retractable>0</retractable>
  </contact>

  <contact type="BOGEY" name="CAR_KEEL_AFT">
   <location unit="M">
    <x>  18.4 </x>
    <y>   0.0 </y>
    <z>  -8.2 </z>
   </location>
   <static_friction>  1.0 </static_friction>
   <dynamic_friction> 2.0 </dynamic_friction>
   <rolling_friction> 0.8 </rolling_friction>
   <spring_coeff unit="N/M"> 50000 </spring_coeff>
   <damping_coeff unit="N/M/SEC"> 10000 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> NONE </brake_group>
   <retractable>0</retractable>
  </contact>

  <!--
  === Other contact points ============================================
  -->
  <!-- These should induce structural failure. -->
  <contact type="STRUCTURE" name="NOSE">
   <location unit="M">
    <x> 0.0 </x>
    <y> 0.0 </y>
    <z> 0.0 </z>
   </location>
   <static_friction>  0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="N/M">      100000 </spring_coeff>
   <damping_coeff unit="N/M/SEC">  20000 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> NONE </brake_group>
   <retractable>0</retractable>
  </contact>

  <contact type="STRUCTURE" name="TAIL">
   <location unit="M">
    <x> 43.7 </x>
    <y>  0.0 </y>
    <z>  0.0 </z>
   </location>
   <static_friction>  0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="N/M">      100000 </spring_coeff>
   <damping_coeff unit="N/M/SEC">  20000 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> NONE </brake_group>
   <retractable>0</retractable>
  </contact>

  <contact type="STRUCTURE" name="TAIL_FIN">
   <location unit="M">
    <x> 35.6 </x>
    <y>  0.0 </y>
    <z> -5.6 </z>
   </location>
   <static_friction>  0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="N/M">      100000 </spring_coeff>
   <damping_coeff unit="N/M/SEC">  20000 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> NONE </brake_group>
   <retractable>0</retractable>
  </contact>

  <contact type="BOGEY" name="CAR_FRONT">
   <location unit="M">
    <x>  13.7 </x>
    <y>   0.0 </y>
    <z>  -6.85 </z>
   </location>
   <static_friction>  0.5 </static_friction>
   <dynamic_friction> 0.5 </dynamic_friction>
   <rolling_friction> 0.5 </rolling_friction>
   <spring_coeff unit="N/M"> 25000 </spring_coeff>
   <damping_coeff unit="N/M/SEC"> 10000 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> NONE </brake_group>
   <retractable>0</retractable>
  </contact>

 </ground_reactions>

 <!--
 ==== Propulsion ============================================================
 -->

 <propulsion>
 
  <!--
  === Engine ================================================
  -->
  
  <documentation>
   Propulsion installation information for S.S. Zero from [Whale:1919:BA]:
    - Water-cooled Rolls Royce Hawk
      - 75hp
      - 4 bladed pusher propeller.
        - 9 foot diameter according to [AHT, www.aht.ndirect.co.uk].
      - Weight?
    - Petrol tanks.
      - "The petrol is carried in aluminium tanks slung
        on the axis of the envelope, identically with the system in use
        on the S.S.P's."
      What axis? Photos indicate one on each side of the envelope
      roughly above the engine.
      - Capacity? S.S. B.E.2C: 60 (UK?) gallons.
      - 2 tanks. Total capacity 120 (UK?) gallons [AHT, www.aht.ndirect.co.uk].
      - 2, 4 or 6 tanks for S.S.Zero [Mowthrope:1998:Battlebags] (at 60 gal UK?)
  </documentation>

  <!-- Engine -->
  <engine file="eng_RRhawk">
   <feed>0</feed>
   <feed>1</feed>
   <thruster file="prop_SSZ">
    <location unit="M">
     <x> 19.05 </x>
     <y>  0.00 </y>
     <z> -6.10 </z>
    </location>
    <orient unit="DEG">
     <roll>  0.0 </roll>
     <pitch> 0.0 </pitch>
     <yaw>   0.0 </yaw>
    </orient>
   </thruster>
  </engine>

  <!--
  === Fuel tanks ============================================
  -->
  <tank type="FUEL">
   <location unit="M">
    <x> 17.26 </x>
    <y> -4.50 </y>
    <z>  0.0 </z>
   </location>
   <!-- 196.39 kg for 60 UK gallon at AVGAS density 0.72 kg/liter. -->
   <capacity unit="KG"> 196.39 </capacity>
   <contents unit="KG"> 196.39 </contents>
  </tank>
  <tank type="FUEL">
   <location unit="M">
    <x> 17.26 </x>
    <y>  4.50 </y>
    <z>  0.0  </z>
   </location>
   <!-- 196.39 kg for 60 UK gallon at AVGAS density 0.72 kg/liter. -->
   <capacity unit="KG"> 196.39 </capacity>
   <contents unit="KG"> 196.39 </contents>
  </tank>

 </propulsion>

 <!--
 ==== Instrumentation =======================================================
 -->
 <system name="Instrumentation">

  <documentation>
   Instrumentation for a S.S. B.E.2C according to [Whale:1919:BA]:
   Pilot:
    - watch
    - air-speed indicator graduated in knots
    - aneroid (pressure altimeter) reading to 10,000 feet.
    - Elliot revolution counter (tachometer?)
    - Clift inclinometer reading up to 20 degrees depression or elevation.
    - map case
    - oil pressure gauge
    - petrol pressure gauge
    - glass petrol level
    - two concentric glass pressure gauges for gas pressure
    - Compass mounted on a small wooden pedestal in front of the pilot.
    - Controls:
      - rudder pedals
      - elevator wheel mounted in a fore-aft direction across the seat (?)
      - Valve cord to top gas valve
      - Valve cords to the ballonet valves(?)
      - Rip cord

   Observer/Wireless operator:
    - radio w. range up to 50-60 miles.
    - Lewis machine gun 
  </documentation>

  <channel name="Instruments">

   <fcs_function name="instrumentation/gas-pressure-psf[0]">
    <function>
     <difference>
      <property> buoyant_forces/gas-cell/pressure-psf </property>
      <property> atmosphere/P-psf </property>
     </difference>
    </function>
    <!-- output>/instrumentation/gas-manometer[0]/gas-pressure-psf</output -->
   </fcs_function>

   <fcs_function name="instrumentation/gas-pressure-psf[1]">
    <function>
     <difference>
      <property> buoyant_forces/gas-cell/pressure-psf </property>
      <property> atmosphere/P-psf </property>
     </difference>
    </function>
    <!-- output>/instrumentation/gas-manometer[1]/gas-pressure-psf</output -->
   </fcs_function>

  </channel>
 </system>

 <!--
 ==== Animation control =====================================================
 -->
 <!-- system name="Animation">

  <channel name="Envelope">

   <fcs_function name="animation/envelope-shape-norm">
    <function>
     <max>
      <value> 0.0 </value>
      <min>
       <value> 1.0 </value>
       <quotient>
        <property> instrumentation/gas-pressure-psf </property>
        <value> 3.50 </value> --><!-- about 17mm water. --><!--
       </quotient>
      </min>
     </max>
    </function>
    <output> /sim/multiplay/generic/float[0] </output>
   </fcs_function>

  </channel>

 </system -->

 <!--
 ==== Flight control system =================================================
 -->
 <system file="Submarine_Scout_crew_ap">
  <limitation>
   The real aircraft did not have any autopilot. This autopilot is
   mostly intended for scripted flights in JSBSim/standalone.
   The autopilot is only minimally tuned.
  </limitation>
 </system>

 <system file="air-and-gas-control">

  <documentation>
   "Two ballonets are provided, one forward and one aft, the capacity
   of each being 6,375 cubic feet.  The supply of air for filling
   these is taken from the propeller draught by a slanting aluminium
   tube to the underside of the envelope, where it meets a
   longitudinal fabric hose which connects the two ballonet air
   inlets.  Non-return fabric valves known as crab-pots are fitted in
   this fabric hose on either side of their junction with the air
   scoop.  Two automatic air valves are fitted to the underside of the
   envelope, one for each ballonet.  The air pressure tends to open
   the valve instead of keeping it shut and to counteract this the
   spring of the valve is inside the envelope.  The springs are set to
   open at a pressure of 25 to 28 mm."  [Whale:1919:BA] on the S.S.B.E. 2C.

   "The valve cords are led to the pilot's seat through eyes attached
   to the envelope."  [Whale:1919:BA] on the S.S.B.E. 2C.

   "Two gas valves are also fitted, one on the top of the envelope,
   the other at the bottom.  The bottom gas valve spring is set to
   open at 30 to 35 mm. pressure, the top valve is hand controlled
   only."   [Whale:1919:BA] on the S.S.B.E. 2C.

   "One ripping panel is fitted, which is situated on the top of the
   envelope towards the nose.  It has a length of 14 feet 5 inches and
   a breadth of about 8 inches."  [Whale:1919:BA] on the S.S.B.E. 2C.
  </documentation>
  <limitation>
   The slip stream pressure calculation does not take the direction of the
   relative wind into account.
  </limitation>

 </system>

 <flight_control name="Aerodynamic controls">

  <!--
  === Pitch ==================================================
  -->
  <channel name="Pitch">
   <summer name="fcs/pitch-trim-sum">
    <input>fcs/elevator-cmd-norm</input>
    <input>fcs/pitch-trim-cmd-norm</input>
    <input>ap/elevator-cmd-norm</input>
    <clipto>
     <min>-1</min>
     <max>1</max>
    </clipto>
   </summer>

   <aerosurface_scale name="fcs/elevator/pos-rad">
    <input>fcs/pitch-trim-sum</input>
    <gain>0.5236</gain>
    <domain>
     <min>-1</min>
     <max>1</max>
    </domain>
    <range>
     <min>-0.5236</min>
     <max>0.5236</max>
    </range>
    <output>fcs/elevator-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="fcs/elevator/pos-norm">
    <input>fcs/elevator-pos-rad</input>
    <gain>1.9099</gain>
    <domain>
     <min>-0.5236</min>
     <max>0.5236</max>
    </domain>
    <range>
     <min>-1</min>
     <max>1</max>
    </range>
    <output>fcs/elevator-pos-norm</output>
   </aerosurface_scale>
  </channel>

  <!--
  === Yaw ====================================================
  -->
  <channel name="Yaw">

   <summer name="fcs/yaw-trim-sum">
    <input>fcs/rudder-cmd-norm</input>
    <input>-fcs/aileron-cmd-norm</input>
    <input>fcs/yaw-trim-cmd-norm</input>
    <input>ap/rudder-cmd-norm</input>
    <clipto>
     <min>-1</min>
     <max>1</max>
    </clipto>
   </summer>

   <aerosurface_scale name="fcs/rudder/pos-rad">
    <input>fcs/yaw-trim-sum</input>
    <gain>0.5236</gain>
    <domain>
     <min>-1</min>
     <max>1</max>
    </domain>
    <range>
     <min>-0.5236</min>
     <max>0.5236</max>
    </range>
    <output>fcs/rudder-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="fcs/rudder/pos-norm">
    <input>fcs/rudder-pos-rad</input>
    <gain>1.9099</gain>
    <domain>
     <min>-0.5236</min>
     <max>0.5236</max>
    </domain>
    <range>
     <min>-1</min>
     <max>1</max>
    </range>
    <output>fcs/rudder-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <!--
  === Static Trim ============================================
  -->
  <property value="0.50">fcs/static-trim-cmd-norm</property>

  <channel name="StaticTrim">
   <!--
    This is for fine-tuning the CoG.
    In-flight pitch trim was mainly handled by the ballonets.
   -->

   <summer name="fcs/static-trim-norm">
    <input>fcs/static-trim-cmd-norm</input>
    <clipto>
     <min>0</min>
     <max>1</max>
    </clipto>
   </summer>

   <summer name="fcs/inverse-static-trim-norm">
    <input>-fcs/static-trim-norm</input>
    <bias>1</bias>
    <clipto>
     <min>0</min>
     <max>1</max>
    </clipto>
   </summer>

   <aerosurface_scale name="fcs/ballast/trim-norm[0]">
    <input>fcs/inverse-static-trim-norm</input>
     <domain>
     <min>0</min>
     <max>1</max>
    </domain>
    <range>
     <min>0</min>
     <max>50</max>
    </range>
    <output>inertia/pointmass-weight-lbs[1]</output>
   </aerosurface_scale>

   <aerosurface_scale name="fcs/ballast/trim-norm[1]">
    <input>fcs/static-trim-norm</input>
    <domain>
     <min>0</min>
     <max>1</max>
    </domain>
    <range>
     <min>0</min>
     <max>50</max>
    </range>
    <output>inertia/pointmass-weight-lbs[2]</output>
   </aerosurface_scale>

  </channel>

 </flight_control>

 <!--
 ==== Initial static condition weigh-off ====================================
 -->

 <system name="Weigh_Off">
  <channel name="Initial_Static_Weigh_Off">
   <!-- The weight of inertia/pointmass-weight-lbs[0] is set from Nasal. -->

   <summer name="static-condition/net-lift-lbs">
    <input> buoyant_forces/gas-cell[0]/buoyancy-lbs </input>
    <input> -inertia/weight-lbs </input>
   </summer>

  </channel>
 </system>

 <!--
 ==== External reactions ====================================================
 -->

 <external_reactions>

  <!-- Note: these forces are applied to the wrong location. -->
  <force name="guy-wire-north" frame="LOCAL">
   <location unit="M">
    <x> 0.0 </x>
    <y> 0.0 </y>
    <z> 0.0 </z>
   </location>
   <direction>
    <x> 1.0 </x>
    <y> 0.0 </y>
    <z> 0.0 </z>
   </direction>
  </force>
  <force name="guy-wire-east" frame="LOCAL">
   <location unit="M">
    <x> 0.0 </x>
    <y> 0.0 </y>
    <z> 0.0 </z>
   </location>
   <direction>
    <x> 0.0 </x>
    <y> 1.0 </y>
    <z> 0.0 </z>
   </direction>
  </force>
  <force name="guy-wire-down" frame="LOCAL">
   <location unit="M">
    <x> 0.0 </x>
    <y> 0.0 </y>
    <z> 0.0 </z>
   </location>
   <direction>
    <x> 0.0 </x>
    <y> 0.0 </y>
    <z> 1.0 </z>
   </direction>
  </force>

  <force name="added-mass-bx" frame="BODY">
   <location unit="M">
    <x> 20.0 </x>
    <y>  0.0 </y>
    <z>  0.0 </z>
   </location>
   <direction>
    <x> 1.0 </x>
    <y> 0.0 </y>
    <z> 0.0 </z>
   </direction>
  </force>
  <force name="added-mass-by" frame="BODY">
   <location unit="M">
    <x> 20.0 </x>
    <y>  0.0 </y>
    <z>  0.0 </z>
   </location>
   <direction>
    <x> 0.0 </x>
    <y> 1.0 </y>
    <z> 0.0 </z>
   </direction>
  </force>
  <force name="added-mass-bz" frame="BODY">
   <location unit="M">
    <x> 20.0 </x>
    <y>  0.0 </y>
    <z>  0.0 </z>
   </location>
   <direction>
    <x> 0.0 </x>
    <y> 0.0 </y>
    <z> 1.0 </z>
   </direction>
  </force>

  <moment name="added-mass-pitch" frame="BODY" unit="LBSFT">
   <direction>
    <x> 0.0 </x>
    <y> 1.0 </y>
    <z> 0.0 </z>
   </direction>
  </moment>
  <moment name="added-mass-roll" frame="BODY" unit="LBSFT">
   <direction>
    <x> 1.0 </x>
    <y> 0.0 </y>
    <z> 0.0 </z>
   </direction>
  </moment>
  <moment name="added-mass-yaw" frame="BODY" unit="LBSFT">
   <direction>
    <x> 0.0 </x>
    <y> 0.0 </y>
    <z> 1.0 </z>
   </direction>
  </moment>

 </external_reactions>

 <!--
 ==== Mooring force calculation =============================================
 -->

 <system file="visual-reference-point-extensions"/>

 <system name="landing-party">

  <!-- INTERFACE PROPERTIES -->
  <!--  Ground crew actions -->
  <property value="0.0"> landing-party/wire-connected[0] </property>
  <property value="0.0"> landing-party/wire-connected[1] </property>
  <property value="70.0"> landing-party/wire-length-ft </property>
  <!--  Handling party locations -->
  <property value="0.0"> landing-party/latitude-deg[0]  </property>
  <property value="0.0"> landing-party/longitude-deg[0] </property>
  <property value="0.0"> landing-party/altitude-ft[0] </property>
  <property value="0.0"> landing-party/latitude-deg[1]  </property>
  <property value="0.0"> landing-party/longitude-deg[1] </property>
  <property value="0.0"> landing-party/altitude-ft[1] </property>

  <!-- Constants -->
  <!--  Wire -->
  <property value="10.0"> landing-party/wire-spring-coeff </property>
  <property value="500.0"> landing-party/max-wire-force-lbs </property>

  <channel name="Guy Wire Forces">

   <fcs_function name="landing-party/latitude-diff-ft[0]">
    <function>
     <product>
      <value> -364560.0 </value>
      <difference>
       <property> position/vrp-latitude-deg </property>
       <property> landing-party/latitude-deg[0] </property>
      </difference>
     </product>
    </function>
   </fcs_function>
   <fcs_function name="landing-party/latitude-diff-ft[1]">
    <function>
     <product>
      <value> -364560.0 </value>
      <difference>
       <property> position/vrp-latitude-deg </property>
       <property> landing-party/latitude-deg[1] </property>
      </difference>
     </product>
    </function>
   </fcs_function>

   <fcs_function name="landing-party/longitude-diff-ft[0]">
    <function>
     <product>
      <value> -364560.0 </value> <!-- Bougus conversion factor. -->
      <difference>
       <property> position/vrp-longitude-deg </property>
       <property> landing-party/longitude-deg[0] </property>
      </difference>
     </product>
    </function>
   </fcs_function>
   <fcs_function name="landing-party/longitude-diff-ft[1]">
    <function>
     <product>
      <value> -364560.0 </value> <!-- Bougus conversion factor. -->
      <difference>
       <property> position/vrp-longitude-deg </property>
       <property> landing-party/longitude-deg[1] </property>
      </difference>
     </product>
    </function>
   </fcs_function>

   <summer name="landing-party/altitude-diff-ft[0]">
    <input> position/vrp-altitude-ft </input>
    <input> -landing-party/altitude-ft[0] </input>
   </summer>
   <summer name="landing-party/altitude-diff-ft[1]">
    <input> position/vrp-altitude-ft </input>
    <input> -landing-party/altitude-ft[1] </input>
   </summer>

   <fcs_function name="landing-party/total-distance-ft[0]">
    <function>
     <pow>
      <sum>
       <pow>
        <property> landing-party/latitude-diff-ft[0] </property>
        <value> 2.0 </value>
       </pow>
       <pow>
        <property> landing-party/longitude-diff-ft[0] </property>
        <value> 2.0 </value>
       </pow>
       <pow>
        <property> landing-party/altitude-diff-ft[0] </property>
        <value> 2.0 </value>
       </pow>
      </sum>
      <value> 0.5 </value>
     </pow>
    </function>
   </fcs_function>
   <fcs_function name="landing-party/total-distance-ft[1]">
    <function>
     <pow>
      <sum>
       <pow>
        <property> landing-party/latitude-diff-ft[1] </property>
        <value> 2.0 </value>
       </pow>
       <pow>
        <property> landing-party/longitude-diff-ft[1] </property>
        <value> 2.0 </value>
       </pow>
       <pow>
        <property> landing-party/altitude-diff-ft[1] </property>
        <value> 2.0 </value>
       </pow>
      </sum>
      <value> 0.5 </value>
     </pow>
    </function>
   </fcs_function>

   <fcs_function name="landing-party/wire-force-lbs[0]">
    <function>
     <product>
      <property> landing-party/wire-connected[0] </property>
      <property> landing-party/wire-spring-coeff </property>
      <max>
       <difference>
        <property> landing-party/total-distance-ft[0] </property>
        <property> landing-party/wire-length-ft </property>
       </difference>
       <value> 0.0 </value>
      </max>
     </product>
    </function>
    <clipto>
     <min> 0.0 </min>
     <max> landing-party/max-wire-force-lbs </max>
    </clipto>
   </fcs_function>
   <fcs_function name="landing-party/wire-force-lbs[1]">
    <function>
     <product>
      <property> landing-party/wire-connected[1] </property>
      <property> landing-party/wire-spring-coeff </property>
      <max>
       <difference>
        <property> landing-party/total-distance-ft[1] </property>
        <property> landing-party/wire-length-ft </property>
       </difference>
       <value> 0.0 </value>
      </max>
     </product>
    </function>
    <clipto>
     <min> 0.0 </min>
     <max> landing-party/max-wire-force-lbs </max>
    </clipto>
   </fcs_function>

   <fcs_function name="landing-party/force-north-lbs">
    <function>
     <sum>
      <product>
       <property> landing-party/wire-force-lbs[0] </property>
       <quotient>
        <property> landing-party/latitude-diff-ft[0] </property>
        <property> landing-party/total-distance-ft[0] </property>
       </quotient>
      </product>
      <product>
       <property> landing-party/wire-force-lbs[1] </property>
       <quotient>
        <property> landing-party/latitude-diff-ft[1] </property>
        <property> landing-party/total-distance-ft[1] </property>
       </quotient>
      </product>
     </sum>
    </function>
    <output> external_reactions/guy-wire-north/magnitude </output>
   </fcs_function>

   <fcs_function name="landing-party/force-east-lbs">
    <function>
     <sum>
      <product>
       <property> landing-party/wire-force-lbs[0] </property>
       <quotient>
        <property> landing-party/longitude-diff-ft[0] </property>
        <property> landing-party/total-distance-ft[0] </property>
       </quotient>
      </product>
      <product>
       <property> landing-party/wire-force-lbs[1] </property>
       <quotient>
        <property> landing-party/longitude-diff-ft[1] </property>
        <property> landing-party/total-distance-ft[1] </property>
       </quotient>
      </product>
     </sum>
    </function>
    <output> external_reactions/guy-wire-east/magnitude </output>
   </fcs_function>

   <fcs_function name="landing-party/force-down-lbs">
    <function>
     <sum>
      <product>
       <property> landing-party/wire-force-lbs[0] </property>
       <quotient>
        <property> landing-party/altitude-diff-ft[0] </property>
        <property> landing-party/total-distance-ft[0] </property>
       </quotient>
      </product>
      <product>
       <property> landing-party/wire-force-lbs[1] </property>
       <quotient>
        <property> landing-party/altitude-diff-ft[1] </property>
        <property> landing-party/total-distance-ft[1] </property>
       </quotient>
      </product>
     </sum>
    </function>
    <output> external_reactions/guy-wire-down/magnitude </output>
   </fcs_function>

  </channel>

 </system>

 <!--
 ==== Aerodynamics ==========================================================
 -->

 <system name="Aerodynamic constants">
  <!-- Aerodynamic constants. -->
  <property value="143.42">  aero/constants/length-ft </property>
  <property value="30.0">    aero/constants/diameter-ft </property>
  <property value="21469.3"> aero/constants/length-diameter-ft2 </property>
  <property value="70000">   aero/constants/volume-ft3 </property>
  <property value="1698.5">  aero/constants/volume-ft3_2_3 </property>
  <!-- Added mass constants. -->
  <property value="0.06"> aero/constants/added-mass/k-axial </property>
  <property value="0.89"> aero/constants/added-mass/k-transverse </property>
  <property value="0.69"> aero/constants/added-mass/k-rotational </property>
  <property value="0.75">  aero/constants/added-mass/tweak-factor </property>
 </system>
 <system file="airship_added_mass"/>

 <aerodynamics file="Systems/gerris_aero"/>
 <!-- aerodynamics file="Systems/datcom_aero"/ -->

 <!-- output name="datalog.csv" type="CSV" rate="2">

  <rates> ON </rates>
  <velocities> ON </velocities>
  <forces> ON </forces>
  <moments> ON </moments>
  <position> ON </position>
  <propulsion> ON </propulsion>
  <aerosurfaces> ON </aerosurfaces>
  <fcs> ON </fcs>
  <ground_reactions> ON </ground_reactions>
  <property> atmosphere/T-R </property>
  <property> atmosphere/P-psf </property>
  <property> buoyant_forces/gas-cell/max_volume-ft3 </property>
  <property> buoyant_forces/gas-cell/temp-R </property>
  <property> buoyant_forces/gas-cell/pressure-psf </property>
  <property> buoyant_forces/gas-cell/volume-ft3 </property>
  <property> buoyant_forces/gas-cell/buoyancy-lbs </property>
  <property> buoyant_forces/gas-cell/contents-mol </property>
  <property> buoyant_forces/gas-cell/valve_open </property>
  <property> buoyant_forces/gas-cell/ballonet[0]/max_volume-ft3 </property>
  <property> buoyant_forces/gas-cell/ballonet[0]/temp-R </property>
  <property> buoyant_forces/gas-cell/ballonet[0]/pressure-psf </property>
  <property> buoyant_forces/gas-cell/ballonet[0]/volume-ft3 </property>
  <property> buoyant_forces/gas-cell/ballonet[0]/contents-mol </property>
  <property> buoyant_forces/gas-cell/ballonet[0]/valve_open </property>
  <property> buoyant_forces/gas-cell/ballonet[1]/max_volume-ft3 </property>
  <property> buoyant_forces/gas-cell/ballonet[1]/temp-R </property>
  <property> buoyant_forces/gas-cell/ballonet[1]/pressure-psf </property>
  <property> buoyant_forces/gas-cell/ballonet[1]/volume-ft3 </property>
  <property> buoyant_forces/gas-cell/ballonet[1]/contents-mol </property>
  <property> buoyant_forces/gas-cell/ballonet[1]/valve_open </property>
  <property> aero/envelope-aoa-mag-deg </property>

 </output -->

</fdm_config>
