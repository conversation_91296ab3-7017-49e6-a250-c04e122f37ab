<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="paraglider" version="2.0" release="BETA"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

  <fileheader>
    <author> <PERSON>, <PERSON> </author>
    <filecreationdate> 2001-01-01 </filecreationdate>
    <version> 2.1 </version>
    <description> (Powered) Paraglider </description>
<!--
    Reference:
    CFD Calculation of Stability and Control Derivatives
    For Ram-Air Parachutes

    http://www.cobaltcfd.com/pdfs/2016/AIAA-2016-1536.pdf


    http://www.ojovolador.com/eng/read/tests/discus/
     Cells				44
     Wing area (m²)			24	(258.33 ft²)
     Wing area projected (m²)		20.5	(220.66 ft²)
     Wing span (m)			11.2	( 36.75 ft)
     Projected wing span (m)		 8.8	( 28.87 ft)
     Aspect ratio			 5.2
     Projected aspect ratio		 3.7
     Glider weight (kg)			 4.9	( 10.8 lbs)
     Max speed (km/h)			47 ±2	( 25.4 kts ±2)
     Trim speed (km/h)			38 ±1	( 20.5 kts ±½)
-->
  </fileheader>

  <metrics>
    <wingarea unit="FT2"> 220 </wingarea>
    <wingspan unit="FT"> 29 </wingspan>
    <chord unit="FT"> 7 </chord>
    <htailarea unit="FT2"> 1 </htailarea>
    <htailarm unit="FT"> 1 </htailarm>
    <vtailarea unit="FT2"> 1 </vtailarea>
    <vtailarm unit="FT"> 1 </vtailarm>
    <location name="AERORP" unit="IN">
      <x> 0 </x>
      <y> 0 </y>
      <z> 0 </z>
    </location>
    <location name="EYEPOINT" unit="IN">
      <x> 0 </x>
      <y> 0 </y>
      <z> -330 </z>
    </location>
    <location name="VRP" unit="IN">
      <x> 0 </x>
      <y> 0 </y>
      <z> 0 </z>
    </location>
  </metrics>

  <mass_balance>
  <ixx unit="SLUG*FT2">1.703</ixx>
  <iyy unit="SLUG*FT2">1.898</iyy>
  <izz unit="SLUG*FT2">1.360</izz>
  <emptywt unit="LBS"> 10</emptywt>
    <location name="CG" unit="IN">
      <x> 0 </x>
      <y> 0 </y>
      <z> 0 </z>
    </location>
    <pointmass name="Pilot">
      <weight unit="LBS"> 180 </weight>
      <location name="POINTMASS" unit="IN">
        <x>  0 </x>
        <y>  0 </y>
        <z> -330 </z>
      </location>
    </pointmass>
    <pointmass name="Polini Engine">
      <weight unit="LBS"> 25.13 </weight>
      <location name="POINTMASS" unit="IN">
        <x>  4 </x>
        <y>  0 </y>
        <z> -330 </z>
      </location>
    </pointmass>
    <pointmass name="DT Propeller">
      <weight unit="LBS"> 2.425 </weight>
      <location name="POINTMASS" unit="IN">
        <x> 10 </x>
        <y>  0 </y>
        <z> -330 </z>
      </location>
    </pointmass>
  </mass_balance>

  <ground_reactions>
    <contact type="BOGEY" name="LMLG">
      <location unit="IN">
        <x> 0 </x>
        <y> -10 </y>
        <z> -180 </z>
      </location>
      <static_friction> 0.8 </static_friction>
      <dynamic_friction> 0.5 </dynamic_friction>
      <rolling_friction> 0.5 </rolling_friction>
      <spring_coeff unit="LBS/FT"> 200 </spring_coeff>
      <damping_coeff unit="LBS/FT/SEC"> 50 </damping_coeff>
      <max_steer unit="DEG"> 0.0 </max_steer>
      <brake_group> NONE </brake_group>
      <retractable>0</retractable>
    </contact>
    <contact type="BOGEY" name="RMLG">
      <location unit="IN">
        <x> 0 </x>
        <y> 10 </y>
        <z> -180 </z>
      </location>
      <static_friction> 0.8 </static_friction>
      <dynamic_friction> 0.5 </dynamic_friction>
      <rolling_friction> 0.5 </rolling_friction>
      <spring_coeff unit="LBS/FT"> 200 </spring_coeff>
      <damping_coeff unit="LBS/FT/SEC"> 50 </damping_coeff>
      <max_steer unit="DEG"> 0.0 </max_steer>
      <brake_group> NONE </brake_group>
      <retractable>0</retractable>
    </contact>
  </ground_reactions>

  <system file="Controls.xml"/>

  <propulsion>
    <engine file="Polini_THOR80">
    <feed> 0 </feed>

    <thruster file="DT_Propeller">
     <sense> 1 </sense>
     <location unit="IN">
       <x>    10.00 </x>
       <y>     0.00 </y>
       <z>  -330.00 </z>
     </location>
     <orient unit="DEG">
       <pitch>     0.00 </pitch>
        <roll>     0.00 </roll>
         <yaw>     0.00 </yaw>
     </orient>
    </thruster>
  </engine>

  <tank type="FUEL" number="0">
     <location unit="IN">
       <x>     8.00 </x>
       <y>     0.00 </y>
       <z>  -330.00 </z>
     </location>
     <capacity unit="LBS"> 3.5 </capacity>
     <contents unit="LBS"> 1.0 </contents>
  </tank>
  </propulsion>

  <flight_control name="FCS: paraglider_FCS">
  </flight_control>

  <aerodynamics>

    <!-- pilot area (area of a circle with a diameter of 4.1 foot) -->
    <property type="float" value="13.2">metrics/Sp-sqft</property>

    <!-- pilot arm (-330 inches equals to 27.5 foot -->
    <property type="float" value="27.5">metrics/bp-ft</property>

    <axis name="DRAG">
     <function name="aero/coefficient/CDalpha">
      <description>Drag_due_to_alpha</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <table>
        <independentVar lookup="row">aero/alpha-deg</independentVar>
        <independentVar lookup="column">fcs/elevator-pos-norm</independentVar>
        <tableData>
                  0.0     2.0
            0.0   0.065   0.144
            2.5   0.065   0.168
            5.0   0.085   0.206
            7.5   0.109   0.236
            9.0   0.137   0.322
           11.0   0.160   0.386
           12.5   0.199   0.434
           15.0   0.237   0.578
           17.5   0.328   0.706
           19.0   0.339   0.786
           20.0   0.358   0.866
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/coefficient/CDpilot">
      <description>Drag_due_to_pilot</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sp-sqft</property>
       <value>1.8</value>
      </product>
     </function>
    </axis>

    <axis name="SIDE">
     <function name="aero/coefficient/CYDa">
      <description>Side_force_due_to_aileron</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <table>
        <independentVar lookup="row">aero/alpha-deg</independentVar>
        <independentVar lookup="column">fcs/aileron-pos-norm</independentVar>
        <tableData>
                 -1.0     0.0     1.0
            0.0   0.004   0.000  -0.004
            2.5   0.005   0.000  -0.005
            5.0   0.006   0.000  -0.006
            7.5   0.007   0.000  -0.007
            9.0   0.008  -0.000  -0.008
           11.0   0.003  -0.001  -0.003
           12.5   0.002  -0.002  -0.002
           15.0  -0.006   0.002   0.006
           17.5   0.001  -0.001  -0.001
           19.0  -0.009  -0.002   0.009
           20.0  -0.004  -0.002   0.004
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/coefficient/CYb">
      <description>Side_force_due_to_beta</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>aero/beta-rad</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
           0   -0.053
           2   -0.035
           4   -0.020
           6   -0.039
           8   -0.072
          10   -0.056
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/coefficient/CYpilot">
      <description>Side_force_due_to_pilot</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sp-sqft</property>
       <property>aero/beta-rad</property>
       <value>-1.8</value>
      </product>
     </function>
     <function name="aero/coefficient/CYp">
      <description>Side_force_due_to_roll_rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>aero/bi2vel</property>
       <property>velocities/p-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
           0   -0.040
           2   -0.061
           4   -0.086
           6   -0.150
           8   -0.193
          10   -0.179
        </tableData>
       </table>
      </product>
     </function>
    </axis>

    <axis name="LIFT">
     <function name="aero/coefficient/CLalpha">
      <description>Lift_due_to_alpha</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <table>
        <independentVar lookup="row">aero/alpha-deg</independentVar>
        <independentVar lookup="column">fcs/elevator-pos-norm</independentVar>
        <tableData>
                  0.0     2.0
            0.0   0.275   0.642
            2.5   0.395   0.924
            5.0   0.495   1.166
            7.5   0.609   1.410
            9.0   0.628   1.546
           11.0   0.628   1.546
           12.5   0.649   1.450
           15.0   0.614   1.636
           17.5   0.789   1.726
           19.0   0.669   1.902
           20.0   0.705   2.026
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/force/CLadot">
      <description>Lift_due_to_alpha_rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>aero/ci2vel</property>
       <property>aero/alphadot-rad_sec</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
           0.0   8.1
           2.0  11.1
           4.0   1.2
           6.0   0.5
           8.0   2.4
          10.0  10.1
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/force/CLq">
      <description>Lift_due_to_pitch_rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>aero/ci2vel</property>
       <property>velocities/q-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
           0.0  11.9
           2.0  13.6
           4.0   0.8
           6.0   0.9
           8.0   2.4
          10.0  14.9
        </tableData>
       </table>
      </product>
     </function>
    </axis>

    <axis name="ROLL">
     <function name="aero/coefficient/ClDa">
      <description>Roll_moment_due_to_aileron</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/bw-ft</property>
       <value>2.5</value>
       <table>
        <independentVar lookup="row">aero/alpha-deg</independentVar>
        <independentVar lookup="column">fcs/aileron-pos-norm</independentVar>
        <tableData>
                 -1.0     0.0     1.0
            0.0  -0.018   0.000   0.018
            2.5  -0.020   0.000   0.020
            5.0  -0.021   0.000   0.021
            7.5  -0.022   0.000   0.022
            9.0  -0.021   0.002   0.021
           11.0  -0.004   0.002   0.004
           12.5   0.003  -0.003  -0.003
           15.0  -0.008  -0.008   0.008
           17.5  -0.015   0.012   0.015
           19.0  -0.004   0.008   0.004
           20.0   0.000   0.018   0.000
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/moment/Clb">
      <description>Roll moment due to beta</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/bw-ft</property>
       <property>aero/beta-rad</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
          0  -0.080
          2  -0.051
          4  -0.088
          6  -0.107
          8  -0.133 
         10  -0.152
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/moment/Clpilot">
      <description>Roll moment due to pilot</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sp-sqft</property>
       <property>metrics/bp-ft</property>
       <property>aero/beta-rad</property>
       <value>-1.8</value>
      </product>
     </function>
     <function name="aero/coefficient/Clp">
      <description>Roll_moment_due_to_roll_rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/bw-ft</property>
       <property>aero/bi2vel</property>
       <property>velocities/p-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
          0  -0.320
          2  -0.348
          4  -0.200
          6  -0.358
          8  -0.393
         10  -0.367
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/moment/Clr">
      <description>Roll moment due to yaw rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/bw-ft</property>
       <property>aero/bi2vel</property>
       <property>velocities/r-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
          0  -0.050
          2   0.110
          4   0.077
          6   0.080
          8   0.061
         10   0.039
        </tableData>
       </table>
      </product>
     </function>
    </axis>

    <axis name="PITCH">
     <function name="aero/coefficient/CmDe">
      <description>Pitch_moment_due_to_elevator</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/cbarw-ft</property>
       <table>
        <independentVar lookup="row">aero/alpha-deg</independentVar>
        <independentVar lookup="column">fcs/elevator-pos-norm</independentVar>
        <tableData>
                  0.0     2.0
            0.0   0.000  -0.046
            2.5  -0.006  -0.062
            5.0  -0.007  -0.084
            7.5  -0.008  -0.084
            9.0  -0.056  -0.176
           11.0  -0.070  -0.196
           12.5  -0.082  -0.242
           15.0  -0.104  -0.242
           17.5  -0.113  -0.296
           19.0  -0.119  -0.284
           20.0  -0.115  -0.296
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/coefficient/Cmpilot">
      <description>Pitch_moment_due_to_pilot</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sp-sqft</property>
       <property>metrics/bp-ft</property>
       <value>1.8</value>
       </product>
     </function>
     <function name="aero/moment/Cmadot">
      <description>Pitch moment due to alpha rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/cbarw-ft</property>
       <property>aero/ci2vel</property>
       <property>aero/alphadot-rad_sec</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
           0.0  -2.5
           2.0  -0.5
           4.0   2.8
           6.0   0.1
           8.0  -0.4
          10.0   0.0
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/moment/Cmq">
      <description>Pitch moment due to pitch rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/cbarw-ft</property>
       <property>aero/ci2vel</property>
       <property>velocities/q-aero-rad_sec</property>
       <sum>
        <value>-20</value>
        <table>
         <independentVar>aero/alpha-deg</independentVar>
         <tableData>
            0.0  -1.9
            2.0   0.5
            4.0   0.0
            6.0   2.9
            8.0  -0.5
           10.0   0.5
         </tableData>
        </table>
       </sum>
      </product>
     </function>
    </axis>

    <axis name="YAW">
     <function name="aero/coefficient/CnDa">
      <description>Yaw_moment_due_to_aileron</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/bw-ft</property>
       <value>2.5</value>
       <table>
        <independentVar lookup="row">aero/alpha-deg</independentVar>
        <independentVar lookup="column">fcs/aileron-pos-norm</independentVar>
        <tableData>
                 -1.0     0.0     1.0
            0.0   0.007   0.001  -0.007
            2.5   0.007  -0.001  -0.007
            5.0   0.007   0.001  -0.007
            7.5   0.010  -0.001  -0.010
            9.0   0.010   0.001  -0.010
           11.0   0.012   0.002  -0.012
           12.5   0.016   0.000  -0.016
           15.0   0.016   0.000  -0.016
           17.5   0.007  -0.001  -0.007
           19.0   0.011   0.001  -0.011
           20.0   0.014   0.001  -0.014
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/moment/Cnb">
      <description>Yaw moment due to beta</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/bw-ft</property>
       <property>aero/beta-rad</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
          0   0.0009
          2  -0.0176
          4  -0.0040
          6   0.0000
          8   0.0224
         10   0.0176
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/moment/Cnp">
      <description>Yaw_moment_due_to_roll_rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/bw-ft</property>
       <property>aero/bi2vel</property>
       <property>velocities/p-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
          0  -0.320
          2  -0.348
          4  -0.200
          6  -0.358
          8  -0.393
         10  -0.367
        </tableData>
       </table>
      </product>
     </function>
     <function name="aero/moment/Cnr">
      <description>Yaw moment due to yaw rate</description>
      <product>
       <property>aero/qbar-psf</property>
       <property>metrics/Sw-sqft</property>
       <property>metrics/bw-ft</property>
       <property>aero/bi2vel</property>
       <property>velocities/r-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-deg</independentVar>
        <tableData>
          0  -0.070
          2  -0.112
          4  -0.156
          6  -0.036
          8  -0.072
         10  -0.072
        </tableData>
       </table>
      </product>
     </function>
    </axis>
  </aerodynamics>
</fdm_config>
