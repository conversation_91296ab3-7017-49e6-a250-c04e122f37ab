<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="F80C" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
     <author>Aeromatic v 0.8, DPC</author>
     <filecreationdate>2006-01-04</filecreationdate>
     <version>$Revision: 1.6 $</version>
     <description>Models a Lockheed F-80C</description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
 </fileheader>

<!--
  File:     F80C.xml
  Inputs:
    name:          F80C
    type:          single-engine transonic/supersonic fighter
    max weight:    16800.0 lb
    wing span:     38.8 ft
    length:        34.5 ft
    wing area:     unspecified
    gear type:     tricycle
    retractable?:  yes
    # engines:     1
    engine type:   turbine
    engine layout: aft fuselage
    yaw damper?    yes
  Outputs:
    wing loading:  95.00 lb/sq-ft
    CL-alpha:      3.5 per radian
    CL-0:          0.08
    CL-max:        1
    CD-0:          0.021
    K:             0.09

-->

    <metrics>
        <wingarea unit="FT2">  237    </wingarea>
        <wingspan unit="FT">    38.8  </wingspan>
        <wing_incidence>         2.0  </wing_incidence>
        <chord unit="FT">        4.56 </chord>
        <htailarea unit="FT2">  35.37 </htailarea>
        <htailarm unit="FT">    13.80 </htailarm>
        <vtailarea unit="FT2">  21.22 </vtailarea>
        <vtailarm unit="FT">    13.80 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 250 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 82.8 </x>
            <y> 0 </y>
            <z> 36 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 185.0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance>
        <ixx unit="SLUG*FT2"> 21472 </ixx>
        <iyy unit="SLUG*FT2"> 28527 </iyy>
        <izz unit="SLUG*FT2"> 42049 </izz>
        <emptywt unit="LBS">   8240 </emptywt>
        <location name="CG" unit="IN">
            <x> 250.0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 54 </x>
                <y> 0 </y>
                <z> -72 </z>
            </location>
            <static_friction>  0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 5040 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1680 </damping_coeff>
            <max_steer unit="DEG"> 25 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 260.3 </x>
                <y> -41.9 </y>
                <z> -66 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 16800 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3360 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 260.3 </x>
                <y> 41.9 </y>
                <z> -66 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 16800 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3360 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>1</retractable>
        </contact>
	<!-- Structure contact points. -->
        <contact type="STRUCTURE" name="NOSE">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction>  0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.5 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 16800 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3360 </damping_coeff>
            <brake_group> NONE </brake_group>
        </contact>
         <contact type="STRUCTURE" name="LEFT_WINGTIP">
            <location unit="IN">
                <x>  212.60 </x>
                <y> -236.2  </y>
                <z>  -35.4  </z>
            </location>
            <static_friction>  0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.5 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 16800 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3360 </damping_coeff>
            <brake_group> NONE </brake_group>
        </contact>
         <contact type="STRUCTURE" name="RIGHT_WINGTIP">
            <location unit="IN">
                <x> 212.6 </x>
                <y> 236.2 </y>
                <z> -35.4 </z>
            </location>
            <static_friction>  0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.5 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 16800 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3360 </damping_coeff>
            <brake_group> NONE </brake_group>
        </contact>
        <contact type="STRUCTURE" name="TAIL">
            <location unit="IN">
                <x> 425.2 </x>
                <y> 0 </y>
                <z> -9.9 </z>
            </location>
            <static_friction>  0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.5 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 16800 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3360 </damping_coeff>
            <brake_group> NONE </brake_group>
        </contact>
   </ground_reactions>
    <propulsion>
        <engine file="J33-A-35">
            <feed>0</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 354 </x>
                    <y> 0 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.03 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <tank type="FUEL">    <!-- Tank number 0 -->
            <location unit="IN">
                <x> 248.4 </x>
                <y> 0 </y>
                <z> -10.35 </z>
            </location>
            <capacity unit="LBS"> 2848 </capacity>
            <contents unit="LBS"> 2800 </contents>
        </tank>
    </propulsion>
    <flight_control name="FCS: F80C">

        <channel name="Pitch">

            <summer name="Pilot Pitch Sum">
                <input>fcs/elevator-cmd-norm</input>
                <input>fcs/pitch-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max> 1</max>
                </clipto>
            </summer>

            <scheduled_gain name="Pitch Feel">
                <input>fcs/pilot-pitch-sum</input>
                <table>
                  <independentVar>velocities/mach</independentVar>
                    <tableData>
                        0.0000	1.0000
                        0.6000	0.7000
                        1.2000	0.5000
                    </tableData>
                </table>
            </scheduled_gain>

            <aerosurface_scale name="Elevator Final">
                <input>fcs/pitch-feel</input>
                <range>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </range>
                <output>fcs/elevator-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="elevator normalized">
                <input>fcs/elevator-pos-rad</input>
                <domain>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max> 1</max>
                </range>
                <output>fcs/elevator-pos-norm</output>
            </aerosurface_scale>

        </channel>
        <channel name="Roll">
            <summer name="Pilot Roll Sum">
                <input>fcs/aileron-cmd-norm</input>
                <input>fcs/roll-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <scheduled_gain name="Roll Feel">
                <input>fcs/pilot-roll-sum</input>
                <table>
                  <independentVar>velocities/mach</independentVar>
                    <tableData>
                        0.0000	1.0000
                        0.6000	0.7500
                        1.2000	0.5000
                    </tableData>
                </table>
            </scheduled_gain>

            <aerosurface_scale name="Left Aileron Final">
                <input>fcs/roll-feel</input>
                <range>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </range>
                <output>fcs/left-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right Aileron Final">
                <input>-fcs/roll-feel</input>
                <range>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </range>
                <output>fcs/right-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="left aileron normalized">
                <input>fcs/left-aileron-pos-rad</input>
                <domain>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max> 1</max>
                </range>
                <output>fcs/left-aileron-pos-norm</output>
            </aerosurface_scale>

            <aerosurface_scale name="right aileron normalized">
                <input>fcs/right-aileron-pos-rad</input>
                <domain>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max> 1</max>
                </range>
                <output>fcs/right-aileron-pos-norm</output>
            </aerosurface_scale>

        </channel>

        <channel name="Yaw">

            <summer name="Rudder Command Sum">
                <input>fcs/rudder-cmd-norm</input>
                <input>fcs/yaw-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <scheduled_gain name="Yaw Damper Rate">
                <input>velocities/r-aero-rad_sec</input>
                <table>
                  <independentVar>velocities/mach</independentVar>
                    <tableData>
                        0.0000	0.0000
                        0.1000	0.0000
                        0.1100	1.0000
                    </tableData>
                </table>
            </scheduled_gain>

            <summer name="Rudder Sum">
                <input>fcs/rudder-command-sum</input>
                <input>fcs/yaw-damper-rate</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Rudder Control">
                <input>fcs/rudder-sum</input>
                <range>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </range>
                <output>fcs/rudder-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="rudder normalized">
                <input>fcs/rudder-pos-rad</input>
                <domain>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max> 1</max>
                </range>
                <output>fcs/rudder-pos-norm</output>
            </aerosurface_scale>

        </channel>

        <channel name="Flaps">

            <kinematic name="Flaps Control">
                <input>fcs/flap-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>0.5</position>
                        <time>5</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>3</time>
                    </setting>
                </traverse>
                <output>fcs/flap-pos-norm</output>
            </kinematic>
        </channel>

        <channel name="Landing Gear">

            <kinematic name="Gear Control">
                <input>gear/gear-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>3</time>
                    </setting>
                </traverse>
                <output>gear/gear-pos-norm</output>
            </kinematic>

        </channel>

        <channel name="Speedbrake">

            <kinematic name="Speedbrake Control">
                <input>fcs/speedbrake-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>1</time>
                    </setting>
                </traverse>
                <output>fcs/speedbrake-pos-norm</output>
            </kinematic>

        </channel>

    </flight_control>

    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CD0">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                             -1.5700	1.5000
                             -0.2600	0.0420
                              0.0000	0.021
                              0.2600	0.0420
                              1.5700	1.5000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <value>0.11</value>
                </product>
            </function>
            <function name="aero/coefficient/CDmach">
                <description>Drag_due_to_mach</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.00	0.000
                              0.77	0.000
                              1.10	0.024
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDflap">
                <description>Drag_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-norm</property>
                    <value>0.1</value>
                </product>
            </function>
            <function name="aero/coefficient/CDgear">
                <description>Drag_due_to_gear</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>gear/gear-pos-norm</property>
                    <value>0.02</value>
                </product>
            </function>
            <function name="aero/coefficient/CDsb">
                <description>Drag_due_to_speedbrakes</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0250</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                             -1.5700	1.2300
                             -0.2600	0.0500
                              0.0000	0.0000
                              0.2600	0.0500
                              1.5700	1.2300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.23</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-1</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/flap-pos-norm</independentVar>
                          <tableData>
                                        0       1
                             -0.2000   -0.5710	0.1000
                              0.0000	0.0000	0.6000
                              0.2500	0.7160	1.3000
                              0.3300	0.9450	0.3000
                              0.6000	0.1740	0.0000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.2</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value> -0.1 </value>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.4</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.12</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.052</value>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.01</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-0.3</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmde">
                <description>Pitch_moment_due_to_elevator</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>-0.55</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-18.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-9.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmflaps">
                <description>Pitch_moment_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/flap-pos-norm</property>
                    <value>-0.07</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.1</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.15</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.02</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
<!--
    <output name="F80.csv" type="CSV">
        <propulsion> ON </propulsion>
    </output>
-->
</fdm_config>
