<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="T62" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
        <author> Aeromatic / <PERSON> </author>
        <filecreationdate> 2001-01-01 </filecreationdate>
        <version> $Revision: 1.14 $ </version>
        <description> T-6 Texan trainer </description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 176 </wingarea>
        <wingspan unit="FT"> 33.4 </wingspan>
        <chord unit="FT"> 5.27 </chord>
        <htailarea unit="FT2"> 50 </htailarea>
        <htailarm unit="FT"> 16 </htailarm>
        <vtailarea unit="FT2"> 50 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 188.8 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 120.1 </x>
            <y> 0 </y>
            <z> 29.8 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance negated_crossproduct_inertia="true">
        <ixx unit="SLUG*FT2"> 4216.3 </ixx>
        <iyy unit="SLUG*FT2"> 7608 </iyy>
        <izz unit="SLUG*FT2"> 10355 </izz>
        <ixz unit="SLUG*FT2"> -1000 </ixz>
        <emptywt unit="LBS"> 5000 </emptywt>
        <location name="CG" unit="IN">
            <x> 188.8 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 76.8 </x>
                <y> 0 </y>
                <z> -80 </z>
            </location>
            <static_friction> 0.5 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 4000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3000 </damping_coeff>
            <max_steer unit="DEG"> 7 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 217.6 </x>
                <y> -81.4 </y>
                <z> -80 </z>
            </location>
            <static_friction> 0.5 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 5000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 8000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 217.6 </x>
                <y> 81.4 </y>
                <z> -80 </z>
            </location>
            <static_friction> 0.5 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 5000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 8000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>1</retractable>
        </contact>
    </ground_reactions>
    <propulsion>
        <engine file="PT6A-68">
            <feed>0</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 12 </x>
                    <y> 0 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <tank type="FUEL">    <!-- Tank number 0 -->
            <location unit="IN">
                <x> 190 </x>
                <y> -50 </y>
                <z> -12 </z>
            </location>
            <capacity unit="LBS"> 500 </capacity>
            <contents unit="LBS"> 500 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 1 -->
            <location unit="IN">
                <x> 190 </x>
                <y> 50 </y>
                <z> -12 </z>
            </location>
            <capacity unit="LBS"> 500 </capacity>
            <contents unit="LBS"> 500 </contents>
        </tank>
    </propulsion>
    <flight_control name="FCS: t62-fcs">
        <channel name="Pitch">
            <summer name="Pitch Trim Sum">
                <input>fcs/elevator-cmd-norm</input>
                <input>fcs/pitch-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Elevator Control">
                <input>fcs/pitch-trim-sum</input>
                <gain>0.017</gain>
                <range>
                    <min>-30</min>
                    <max>30</max>
                </range>
                <output>fcs/elevator-pos-rad</output>
            </aerosurface_scale>
        </channel>
        <channel name="Roll">
            <summer name="Roll Trim Sum">
                <input>fcs/aileron-cmd-norm</input>
                <input>fcs/roll-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Left Aileron Control">
                <input>fcs/roll-trim-sum</input>
                <gain>0.017</gain>
                <range>
                    <min>-20</min>
                    <max>15</max>
                </range>
                <output>fcs/left-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right Aileron Control">
                <input>-fcs/roll-trim-sum</input>
                <gain>0.017</gain>
                <range>
                    <min>-20</min>
                    <max>15</max>
                </range>
                <output>fcs/right-aileron-pos-rad</output>
            </aerosurface_scale>
        </channel>
        <channel name="Yaw">
            <summer name="Rudder Sum">
                <input>fcs/rudder-cmd-norm</input>
                <input>fcs/yaw-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Rudder Control">
                <input>fcs/rudder-sum</input>
                <gain>0.01745</gain>
                <range>
                    <min>-25</min>
                    <max>25</max>
                </range>
                <output>fcs/rudder-pos-rad</output>
            </aerosurface_scale>
        </channel>
        <channel name="Flaps">
            <kinematic name="Flaps Control">
                <input>fcs/flap-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>15</position>
                        <time>4</time>
                    </setting>
                    <setting>
                        <position>30</position>
                        <time>3</time>
                    </setting>
                </traverse>
                <output>fcs/flap-pos-deg</output>
            </kinematic>
            <aerosurface_scale name="Flap Position Normalizer">
              <input>fcs/flap-pos-deg</input>
              <domain>
                <min>0</min>  <!-- Flaps actual minimum position -->
                <max>30</max>  <!-- Flaps actual maximum position -->
              </domain>
              <range>
                <min>0</min>  <!-- Flaps normalized minimum position -->
                <max>1</max>  <!-- Flaps normalized maximum position -->
              </range>
              <output>fcs/flap-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Landing Gear">
            <kinematic name="Gear Control">
                <input>gear/gear-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>5</time>
                    </setting>
                </traverse>
                <output>gear/gear-pos-norm</output>
            </kinematic>
        </channel>
    </flight_control>
    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CDo">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <value>0.023</value>
                </product>
            </function>
            <function name="aero/coefficient/CDalpha">
                <description>Drag_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                          <tableData>
                                0.0000	30.0000
                              -0.0873	0.0041	0.0014
                              -0.0698	0.0013	0.0041
                              -0.0524	0.0001	0.0084
                              -0.0349	0.0003	0.0141
                              -0.0175	0.0020	0.0212
                              0.0000	0.0052	0.0299
                              0.0175	0.0099	0.0402
                              0.0349	0.0162	0.0521
                              0.0524	0.0240	0.0655
                              0.0698	0.0334	0.0804
                              0.0873	0.0442	0.0968
                              0.1047	0.0566	0.1148
                              0.1222	0.0706	0.1343
                              0.1396	0.0860	0.1554
                              0.1571	0.0962	0.1690
                              0.1745	0.1069	0.1830
                              0.1920	0.1180	0.1975
                              0.2094	0.1298	0.2126
                              0.2269	0.1424	0.2286
                              0.2443	0.1565	0.2464
                              0.2618	0.1727	0.2667
                              0.2793	0.1782	0.2735
                              0.2967	0.1716	0.2653
                              0.3142	0.1618	0.2531
                              0.3316	0.1475	0.2351
                              0.3491	0.1097	0.1866
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.0500</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/mag-beta-rad</property>
                    <value>0.2300</value>
                </product>
            </function>
            <function name="aero/coefficient/CDgear">
                <description>Drag_due_to_landing_gear</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>gear/gear-pos-norm</property>
                    <value>0.0300</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                              -0.3500	0.5000
                              0.0000	0.0000
                              0.3500	-0.5000
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLwbh">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.0900	-0.2200
                              0.0000	0.2500
                              0.0900	0.7300
                              0.1000	0.8300
                              0.1200	0.9200
                              0.1400	1.0200
                              0.1600	1.0800
                              0.1700	1.1300
                              0.1900	1.1900
                              0.2100	1.2500
                              0.2400	1.3500
                              0.2600	1.4400
                              0.2800	1.4700
                              0.3000	1.4300
                              0.3200	1.3800
                              0.3400	1.3000
                              0.3600	1.1500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLDf">
                <description>Delta_lift_due_to_flap_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>fcs/flap-pos-deg</independentVar>
                          <tableData>
                              0.0000	0.0000
                              10.0000	0.2000
                              20.0000	0.3000
                              30.0000	0.3500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLDe">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.1930</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                      <table>
                          <independentVar lookup="row">aero/beta-rad</independentVar>
                          <independentVar lookup="column">aero/alpha-rad</independentVar>
                          <tableData>
                                0.0000	0.1600	0.1700	0.6000
                              -0.3500	0.0300	0.0400	0.1000	0.3000
                              0.0000	0.0000	0.0000	0.0000	0.0000
                              0.3500	-0.0300	-0.0400	-0.1000	-0.3000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate_(roll_damping)</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.5000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.0050</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.2900</value>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0120</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmo">
                <description>Pitching_moment_at_zero_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                      <table>
                          <independentVar>fcs/flap-pos-deg</independentVar>
                          <tableData>
                              0.0000	0.0400
                              30.0000	0.0000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-1.9000</value>
                </product>
            </function>
            <function name="aero/coefficient/CmDe">
                <description>Pitch_moment_due_to_elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>-1.2000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-12.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-9.0000</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.0400</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.0600</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
