<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="F4N" version="2.0" release="ALPHA"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
  <author> Aeromatic v 0.9 </author>
   <author> <PERSON> </author>
  <filecreationdate> 2007-01-16 </filecreationdate>
  <version> 0.0 </version>
  <description> Models a F4N. </description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
 </fileheader>

<!--
  File:     F4N.xml
  Inputs:
    name:          F4N
    type:          two-engine transonic/supersonic fighter
    max weight:    61795.0 lb
    wing span:     38.4 ft
    length:        63 ft
    wing area:     530 sq-ft
    gear type:     tricycle
    retractable?:  yes
    # engines:     2
    engine type:   turbine
    engine layout: aft fuselage
    yaw damper?    yes
  Outputs:
    wing loading:  116.59 lb/sq-ft
    CL-alpha:      3.6 per radian
    CL-0:          0.08
    CL-max:        1
    CD-0:          0.024
    K:             0.1

-->

 <metrics>
   <wingarea  unit="FT2">  530.00 </wingarea>
   <wingspan  unit="FT" >   38.40 </wingspan>
   <wing_incidence>          2.00 </wing_incidence>
   <chord     unit="FT" >   13.80 </chord>
   <htailarea unit="FT2">  106.00 </htailarea>
   <htailarm  unit="FT" >   25.20 </htailarm>
   <vtailarea unit="FT2">   95.40 </vtailarea>
   <vtailarm  unit="FT" >   25.20 </vtailarm>
   <location name="AERORP" unit="IN">
     <x>   0.0 </x>
     <y>   0.0 </y>
     <z> -10.0 </z>
   </location>
   <location name="EYEPOINT" unit="IN">
     <x> 151.20 </x>
     <y>   0.00 </y>
     <z>  38.00 </z>
   </location>
   <location name="VRP" unit="IN">
     <x>0</x>
     <y>0</y>
     <z>0</z>
   </location>
 </metrics>

 <mass_balance>
   <ixx unit="SLUG*FT2">     35698 </ixx>
   <iyy unit="SLUG*FT2">    132077 </iyy>
   <izz unit="SLUG*FT2">    124386 </izz>
   <ixy unit="SLUG*FT2">         0 </ixy>
   <ixz unit="SLUG*FT2">         0 </ixz>
   <iyz unit="SLUG*FT2">         0 </iyz>
   <emptywt unit="LBS" >     28000 </emptywt>
   <location name="CG" unit="IN">
     <x> 0 </x>
     <y> 0 </y>
     <z> 0 </z>
   </location>
 </mass_balance>

 <ground_reactions>

  <contact type="BOGEY" name="NOSE">
   <location unit="IN">
     <x>  -261.59 </x>
     <y>   0.00 </y>
     <z> -62.72 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">      18538.50 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">  5179.50 </damping_coeff>
   <max_steer unit="DEG"> 75.00 </max_steer>
   <brake_group>NONE</brake_group>
   <retractable>1</retractable>
 </contact>

  <contact type="BOGEY" name="LEFT_MAIN">
   <location unit="IN">
     <x> 30.22</x>
     <y> -71.47 </y>
     <z> -63.72 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">      41795.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">  5359.00 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <brake_group>LEFT</brake_group>
   <retractable>1</retractable>
 </contact>

  <contact type="BOGEY" name="RIGHT_MAIN">
   <location unit="IN">
     <x> 30.22 </x>
     <y>  71.47 </y>
     <z> -63.72 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">      41795.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">  5359.00 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <brake_group>RIGHT</brake_group>
   <retractable>1</retractable>
 </contact>

  <contact type="STRUCTURE" name="LEFT_WING">
    <location unit="IN">
     <x> 118</x>
     <y> -19.20 </y>
     <z> -18.90 </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <spring_coeff unit="LBS/FT">      61795.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 12359.00 </damping_coeff>
 </contact>

  <contact type="STRUCTURE" name="RIGHT_WING">
    <location unit="IN">
     <x> 118 </x>
     <y>  19.20 </y>
     <z> -18.90 </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <spring_coeff unit="LBS/FT">      61795.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 12359.00 </damping_coeff>
 </contact>

 </ground_reactions>

  <external_reactions>
    
    <force name="catapult" frame="LOCAL">
      <location unit="IN">
        <x> -260.0 </x>
        <y>    0.0 </y>
        <z>    0.0 </z>
      </location>
      <direction>
        <x>   -0.9612617 </x>
        <y>   -0.2756374 </y>
        <z>    0.0 </z>
      </direction>
    </force>
    
    <force name="holdback" frame="BODY">
      <location unit="IN">
        <x> -250.0 </x>
        <y>    0.0 </y>
        <z>  -30.0 </z>
      </location>
      <direction>
        <x>   -1.0 </x>
        <y>    0.0 </y>
        <z>    0.0 </z>
      </direction>
    </force>
    
    <force name="hook" frame="LOCAL">
      <location unit="IN">
        <x>  230.0 </x>
        <y>    0.0 </y>
        <z>  -30.0 </z>
      </location>
      <direction>
        <x>   0.9902681 </x>
        <y>   0.1391731 </y>
        <z>   0.01 </z>
      </direction>
    </force>
    
  </external_reactions>
  
  <propulsion>

   <engine file="J79-GE-11A">
    <feed>0</feed>
    <thruster file="direct">
     <location unit="IN">
       <x> 190 </x>
       <y> -20.00 </y>
       <z> -18.9 </z>
     </location>
     <orient unit="DEG">
       <pitch> 0.00 </pitch>
       <roll>  0.00 </roll>
       <yaw>   0.00 </yaw>
     </orient>
    </thruster>
  </engine>

   <engine file="J79-GE-11A">
    <feed>0</feed>
    <thruster file="direct">
     <location unit="IN">
       <x> 190 </x>
       <y>  20.00 </y>
       <z> -18.9 </z>
     </location>
     <orient unit="DEG">
       <pitch> 0.00 </pitch>
       <roll>  0.00 </roll>
       <yaw>   0.00 </yaw>
     </orient>
    </thruster>
  </engine>

  <!-- Internal fuel -->
  <tank type="FUEL" number="0">
     <location unit="IN">
       <x> 0 </x>
       <y>   0.00 </y>
       <z> -18.90 </z>
     </location>
     <capacity unit="LBS"> 13160.00 </capacity>
     <contents unit="LBS"> 13000.00 </contents>
  </tank>

 </propulsion>

 <system file="holdback"/>
 <system file="hook"/>
 <system file="catapult"/>
 <system file="BLC"/>
<!-- <system file="refuel"/> -->
 <system file="gear"/> 
 <system file="flaps"/> 
 <system file="speedbrakes"/>
 <system file="FCS-pitch"/>
 <system file="FCS-roll"/>
 <system file="FCS-yaw"/>
 <system file="NWS"/>

<!--
 <flight_control name="FCS">
 </flight_control>
-->

 <aerodynamics>

  <axis name="LIFT">

    <function name="aero/coefficient/CLalpha">
      <description>Lift_due_to_alpha</description>
      <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>
              -0.20 -0.64
               0.00  0.08
               0.26  1.00
               0.40  0.95
               0.60  0.40
               1.30  0.05
            </tableData>
          </table>
      </product>
    </function>

    <function name="aero/coefficient/dCLflap">
       <description>Delta_Lift_due_to_flaps</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/flap-pos-norm</property>
           <value> 0.4 </value>
       </product>
    </function>

    <function name="aero/coefficient/dCLsb">
       <description>Delta_Lift_due_to_speedbrake</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/speedbrake-pos-norm</property>
           <value>-0.05</value>
       </product>
    </function>

    <function name="aero/coefficient/CLde">
       <description>Lift_due_to_Elevator_Deflection</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/elevator-pos-rad</property>
           <value>0.25</value>
       </product>
    </function>

    <function name="aero/coefficient/dCLBLC">
       <description>Delta_Lift_due_to_BLC</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>systems/BLC/active</property>
           <property>fcs/flap-pos-norm</property>
           <value> 0.15 </value>
       </product>
    </function>

  </axis>

  <axis name="DRAG">

    <function name="aero/coefficient/CD0">
       <description>Drag_at_zero_lift</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>
             -1.57    1.500
             -0.26    0.031
              0.00    0.021
              0.26    0.031
              1.57    1.500
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/coefficient/CDi">
       <description>Induced_drag</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/cl-squared</property>
           <value>0.14</value>
         </product>
    </function>

    <function name="aero/coefficient/CDmach">
       <description>Drag_due_to_mach</description>
        <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">velocities/mach</independentVar>
            <tableData>
                0.00      0.000
                0.81      0.000
                1.10      0.018
                1.40      0.009
                2.00      0.004
            </tableData>
          </table>
        </product>
    </function>

    <function name="aero/coefficient/CDflap">
       <description>Drag_due_to_flaps</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/flap-pos-norm</property>
           <value> 0.14</value>
         </product>
    </function>

    <function name="aero/coefficient/CDgear">
       <description>Drag_due_to_gear</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>gear/gear-pos-norm</property>
           <value>0.028</value>
         </product>
    </function>

    <function name="aero/coefficient/CDsb">
       <description>Drag_due_to_speedbrakes</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/speedbrake-pos-norm</property>
           <value>0.03</value>
         </product>
    </function>

    <function name="aero/coefficient/CDbeta">
       <description>Drag_due_to_sideslip</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/beta-rad</independentVar>
            <tableData>
              -1.57    1.230
              -0.26    0.050
               0.00    0.000
               0.26    0.050
               1.57    1.230
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/coefficient/CDde">
       <description>Drag_due_to_Elevator_Deflection</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/mag-elevator-pos-rad</property>
           <value>0.048</value>
       </product>
    </function>

  </axis>

  <axis name="SIDE">

    <function name="aero/coefficient/CYb">
       <description>Side_force_due_to_beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/beta-rad</property>
           <value>-1</value>
       </product>
    </function>

  </axis>

  <axis name="ROLL">

    <function name="aero/coefficient/Clb">
       <description>Roll_moment_due_to_beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <value>-0.05</value>
       </product>
    </function>

    <function name="aero/coefficient/Clp">
       <description>Roll_moment_due_to_roll_rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-aero-rad_sec</property>
           <value>-0.4</value>
       </product>
    </function>

    <function name="aero/coefficient/Clr">
       <description>Roll_moment_due_to_yaw_rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value>0.13</value>
       </product>
    </function>

    <function name="aero/coefficient/Clda">
       <description>Roll_moment_due_to_aileron</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/bw-ft</property>
          <property>fcs/left-aileron-pos-rad</property>
          <table>
            <independentVar lookup="row">velocities/mach</independentVar>
            <tableData>
              0.0    0.120
              2.0    0.040
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/coefficient/Cldr">
       <description>Roll_moment_due_to_rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value>0.005</value>
       </product>
    </function>

  </axis>

  <axis name="PITCH">

    <function name="aero/coefficient/Cmalpha">
       <description>Pitch_moment_due_to_alpha</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/alpha-rad</property>
           <value>-0.3</value>
       </product>
    </function>

    <function name="aero/coefficient/Cmde">
       <description>Pitch_moment_due_to_elevator</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/cbarw-ft</property>
          <property>fcs/elevator-pos-rad</property>
          <table>
            <independentVar lookup="row">velocities/mach</independentVar>
            <tableData>
              0.0     -0.70
              2.0     -0.20
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/coefficient/Cmq">
       <description>Pitch_moment_due_to_pitch_rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>velocities/q-aero-rad_sec</property>
           <value>-18</value>
       </product>
    </function>

    <function name="aero/coefficient/Cmadot">
       <description>Pitch_moment_due_to_alpha_rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>aero/alphadot-rad_sec</property>
           <value>-9</value>
       </product>
    </function>

  </axis>

  <axis name="YAW">

    <function name="aero/coefficient/Cnb">
       <description>Yaw_moment_due_to_beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <value>0.12</value>
       </product>
    </function>

    <function name="aero/coefficient/Cnr">
       <description>Yaw_moment_due_to_yaw_rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value>-0.15</value>
       </product>
    </function>

    <function name="aero/coefficient/Cndr">
       <description>Yaw_moment_due_to_rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value>-0.08</value>
       </product>
    </function>

    <function name="aero/coefficient/Cnda">
       <description>Adverse_yaw</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/left-aileron-pos-rad</property>
           <value>0</value>
       </product>
    </function>

  </axis>

 </aerodynamics>

</fdm_config>
