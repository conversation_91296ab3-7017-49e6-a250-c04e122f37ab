<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="sgstest" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
        <author> Unknown </author>
        <filecreationdate>2003-01-01</filecreationdate>
        <version> $Revision: 1.16 $ </version>
        <description> Model SGS glider </description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 42.23 </wingarea>
        <wingspan unit="FT"> 8 </wingspan>
        <chord unit="FT"> 0.5682 </chord>
        <htailarea unit="FT2"> 0.3949 </htailarea>
        <htailarm unit="FT"> 1.561 </htailarm>
        <vtailarea unit="FT2"> 0.6266 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 2.08 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance negated_crossproduct_inertia="true">
        <ixx unit="SLUG*FT2"> 0.4752 </ixx>
        <iyy unit="SLUG*FT2"> 0.3147 </iyy>
        <izz unit="SLUG*FT2"> 0.7785 </izz>
        <ixz unit="SLUG*FT2"> -0.0255 </ixz>
        <emptywt unit="LBS"> 11.07 </emptywt>
        <location name="CG" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <pointmass name="name">
            <weight unit="LBS"> 0 </weight>
            <location name="POINTMASS" unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
        </pointmass>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1800 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 600 </damping_coeff>
            <max_steer unit="DEG"> 10 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MAIN">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 5400 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1600 </damping_coeff>
            <max_steer unit="DEG"> 360.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MAIN">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 5400 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1600 </damping_coeff>
            <max_steer unit="DEG"> 360.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="TAIL_SKID">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.2 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 20000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_TIP">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.2 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_TIP">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.2 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
    </ground_reactions>
    <propulsion>
    </propulsion>
    <flight_control name="SGS-mini">
        <channel name="Pitch">
          <summer name="Pitch Trim Sum">
              <input>fcs/elevator-cmd-norm</input>
              <input>fcs/pitch-trim-cmd-norm</input>
              <clipto>
                  <min>-1</min>
                  <max>1</max>
              </clipto>
          </summer>

          <aerosurface_scale name="Elevator Control">
              <input>fcs/pitch-trim-sum</input>
              <gain>0.01745</gain>
              <range>
                  <min>-28</min>
                  <max>23</max>
              </range>
              <output>fcs/elevator-pos-rad</output>
          </aerosurface_scale>
        </channel>
        <channel name="Roll">
            <summer name="Roll Trim Sum">
                <input>fcs/aileron-cmd-norm</input>
                <input>fcs/roll-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Aileron Control">
                <input>fcs/aileron-cmd-norm</input>
                <gain>0.01745</gain>
                <range>
                    <min>-20</min>
                    <max>15</max>
                </range>
                <output>fcs/left-aileron-pos-rad</output>
            </aerosurface_scale>
        </channel>
        <channel name="Yaw">
            <summer name="Yaw Trim Sum">
                <input>fcs/rudder-cmd-norm</input>
                <input>fcs/yaw-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Rudder Control">
                <input>fcs/rudder-cmd-norm</input>
                <gain>0.01745</gain>
                <range>
                    <min>-16</min>
                    <max>16</max>
                </range>
                <output>fcs/rudder-pos-rad</output>
            </aerosurface_scale>
        </channel>
        <channel name="Flaps">
            <kinematic name="Flaps Control">
                <input>fcs/flap-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>10</position>
                        <time>2</time>
                    </setting>
                    <setting>
                        <position>20</position>
                        <time>1</time>
                    </setting>
                    <setting>
                        <position>30</position>
                        <time>1</time>
                    </setting>
                </traverse>
                <output>fcs/flap-pos-deg</output>
            </kinematic>
            <aerosurface_scale name="Flap Position Normalizer">
              <input>fcs/flap-pos-deg</input>
              <domain>
                <min>0</min>  <!-- Flaps actual minimum position -->
                <max>30</max>  <!-- Flaps actual maximum position -->
              </domain>
              <range>
                <min>0</min>  <!-- Flaps normalized minimum position -->
                <max>1</max>  <!-- Flaps normalized maximum position -->
              </range>
              <output>fcs/flap-pos-norm</output>
            </aerosurface_scale>
        </channel>
    </flight_control>
    <aerodynamics>

        <function name="aero/function/kCLge">
            <description>Change_in_lift_due_to_ground_effect</description>
            <table>
                <independentVar>aero/h_b-mac-ft</independentVar>
                <tableData>
                    0.0000	1.2030
                    0.1000	1.1270
                    0.1500	1.0900
                    0.2000	1.0730
                    0.3000	1.0460
                    0.4000	1.0550
                    0.5000	1.0190
                    0.6000	1.0130
                    0.7000	1.0080
                    0.8000	1.0060
                    0.9000	1.0030
                    1.0000	1.0020
                    1.1000	1.0000
                </tableData>
            </table>
        </function>

        <axis name="DRAG">
            <function name="aero/coefficient/CDo">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <value>0.0007</value>
                </product>
            </function>
            <function name="aero/coefficient/CDwbh">
                <description>Drag_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <!-- <independentVar lookup="column">fcs/flap-pos-deg</independentVar> -->
                          <tableData>
                              -0.0175	0.0100
                              0.0000	0.0150
                              0.0175	0.0200
                              0.0349	0.0250
                              0.0524	0.0300
                              0.0698	0.0350
                              0.0873	0.0400
                              0.1745	0.1000
                              0.2618	0.2300
                              0.3491	0.3700
                              0.4363	0.5000
                              0.5236	0.6000
                              0.6109	0.7600
                              0.6981	0.8500
                              0.7854	0.9600
                              0.8727	1.0600
                              0.9599	1.1400
                              1.0472	1.2200
                              1.2217	1.3800
                              1.3963	1.5000
                              1.5708	1.4600
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDDe">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>fcs/elevator-pos-norm</independentVar>
                          <tableData>
                              -1.0000	0.1140
                              0.0000	0.0000
                              1.0000	0.1140
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.2850</value>
                </product>
            </function>
            <function name="aero/coefficient/CYda">
                <description>Side_force_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>-0.0456</value>
                </product>
            </function>
            <function name="aero/coefficient/CYdr">
                <description>Side_force_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.1880</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLwbh">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.1571	0.0000
                              -0.1369	0.0600
                              -0.1222	0.1200
                              -0.0900	0.1900
                              -0.0524	0.2600
                              -0.0349	0.3300
                              -0.0175	0.4300
                              0.0000	0.4800
                              0.0175	0.5600
                              0.0349	0.6400
                              0.0524	0.7100
                              0.0698	0.7800
                              0.0873	0.8200
                              0.1047	0.9200
                              0.1222	0.9900
                              0.1369	1.0600
                              0.1571	1.1000
                              0.1745	1.1600
                              0.1920	1.2200
                              0.2094	1.2500
                              0.2269	1.2600
                              0.2444	1.2500
                              0.2618	1.2400
                              0.2793	1.2100
                              0.2967	1.1600
                              0.3142	1.1400
                              0.3316	1.1400
                              0.3491	1.0900
                              0.4363	0.9800
                              0.5236	0.8800
                              0.6109	0.8300
                              0.6981	0.8400
                              0.7854	0.8200
                              0.8727	0.7900
                              0.9599	0.7400
                              1.0472	0.6600
                              1.2217	0.4700
                              1.3963	0.2600
                              1.5708	0.0300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLDe">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.3420</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clo">
                <description>Roll_moment_due_to_vertical_tail_incidence</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <value>-0.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.0513</value>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate_(roll_damping)</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.4700</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.2500</value>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0046</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-0.5730</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-9.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-5.2000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmo">
                <description>Pitching_moment_at_zero_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <value>0.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmde">
                <description>Pitch_moment_due_to_elevator_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>-1.2610</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.0170</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnp">
                <description>Yaw_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.1800</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.0250</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnda">
                <description>Yaw_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.0115</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.0370</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
