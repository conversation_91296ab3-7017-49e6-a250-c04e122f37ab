<system name="steady flight data">

<!--

  Data was obtained with a 'RPM-dictated' ah1s at 8500lbs.
  Atmosphere was left at JSBSim default conditions (as of march 2012).

  Contents:
   
    A kludge for forward/backward speed.
    4 tables giving direct rotor input (col,lon,lat,anti) yielding to level flight.
    2 tables with according trim attitudes (phi,theta) .
     and
    4 derived tables with trim inputs based on given rotor-ctrl system. 

  Note: The property root is set to 'fcs/SFD', relevant outputs go into 'fcs/automatic'.

-->

  <channel name="trim tests">

    <fcs_function name="fcs/SFD/trim/v_dir_kts">
      <description>
        Bad kludge for getting 'plain' forward/backward speed, goes wrong in sideward flight.
      </description>
      <function>
        <product>
          <property> velocities/vg-fps </property>
          <v> 0.5924838 </v>
          <sign>
            <property> velocities/u-fps </property>
          </sign>
        </product>
      </function>
    </fcs_function>


    <!-- propulsion/engine/collective-ctrl-rad -->
    <fcs_function name="fcs/SFD/trim/collective-ctrl-rad"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00     0.232525     0.232809     0.232939     0.233082     0.233366     0.234227     0.235681     0.238668     0.248293     0.269486     0.318934
       -20.00     0.249795     0.251849     0.252310     0.252465     0.252772     0.253698     0.255261     0.258460     0.268674     0.288013     0.337898
         0.00     0.254016     0.263226     0.265372     0.265525     0.265818     0.266701     0.268191     0.271242     0.280999     0.299536     0.347700
        20.00     0.249691     0.251737     0.252197     0.252351     0.252658     0.253585     0.255148     0.258347     0.268560     0.287896     0.337765
        40.00     0.231127     0.231409     0.231539     0.231681     0.231967     0.232830     0.234289     0.237285     0.247736     0.267583     0.317847
        60.00     0.224004     0.224058     0.224140     0.224268     0.224525     0.225304     0.226621     0.229333     0.238119     0.255238     0.301628
        80.00     0.224593     0.224619     0.224690     0.224807     0.225042     0.225751     0.226955     0.229439     0.237552     0.253570     0.297833
       100.00     0.231535     0.231556     0.231619     0.231724     0.231934     0.232570     0.233652     0.235898     0.243328     0.258322     0.300987
       120.00     0.245154     0.245172     0.245225     0.245314     0.245493     0.246036     0.246965     0.248912     0.255504     0.269304     0.310433
       140.00     0.266606     0.266620     0.266661     0.266730     0.266869     0.267291     0.268022     0.269581     0.275088     0.287358     0.326666
          </tableData>
        </table>
      </function> 
      <output> fcs/SFD/ap/afcs/collective-trim-ctrl-rad </output>
    </fcs_function>


    <!-- propulsion/engine/longitudinal-ctrl-rad -->
    <fcs_function name="fcs/SFD/trim/longitudinal-ctrl-rad"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00    -0.028652    -0.028692    -0.028716    -0.028745    -0.028803    -0.028980    -0.029280    -0.029894    -0.031874    -0.036276    -0.046327
       -20.00    -0.019064    -0.019207    -0.019242    -0.019258    -0.019289    -0.019382    -0.019540    -0.019863    -0.020900    -0.022885    -0.028123
         0.00    -0.007809    -0.007841    -0.007848    -0.007849    -0.007849    -0.007850    -0.007852    -0.007856    -0.007870    -0.007904    -0.008025
        20.00     0.007379     0.007585     0.007629     0.007640     0.007663     0.007732     0.007849     0.008092     0.008888     0.010472     0.014876
        40.00     0.016039     0.016113     0.016143     0.016173     0.016233     0.016413     0.016719     0.017346     0.019625     0.023860     0.034419
        60.00     0.021099     0.021112     0.021135     0.021173     0.021248     0.021477     0.021865     0.022666     0.025619     0.031740     0.048096
        80.00     0.031231     0.031241     0.031267     0.031311     0.031399     0.031666     0.032120     0.033064     0.036196     0.042532     0.060535
       100.00     0.044003     0.044012     0.044039     0.044084     0.044174     0.044449     0.044920     0.045912     0.049295     0.056432     0.077715
       120.00     0.060896     0.060904     0.060928     0.060967     0.061047     0.061293     0.061719     0.062635     0.065923     0.073360     0.097232
       140.00     0.083605     0.083611     0.083627     0.083654     0.083711     0.083884     0.084195     0.084899     0.087697     0.094841     0.120438
          </tableData>
        </table>
      </function> 
      <output> fcs/SFD/ap/afcs/longitudinal-trim-ctrl-rad </output>
    </fcs_function>


    <!-- propulsion/engine/lateral-ctrl-rad -->
    <fcs_function name="fcs/SFD/trim/lateral-ctrl-rad"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00     0.001685     0.001677     0.001676     0.001676     0.001677     0.001679     0.001683     0.001687     0.001682     0.001644     0.001250
       -20.00    -0.001234    -0.001309    -0.001323    -0.001324    -0.001326    -0.001330    -0.001339    -0.001357    -0.001427    -0.001591    -0.002109
         0.00    -0.003916    -0.004327    -0.004420    -0.004421    -0.004423    -0.004428    -0.004436    -0.004455    -0.004524    -0.004679    -0.005161
        20.00    -0.006194    -0.006301    -0.006321    -0.006323    -0.006326    -0.006334    -0.006349    -0.006379    -0.006480    -0.006686    -0.007257
        40.00    -0.007465    -0.007480    -0.007484    -0.007485    -0.007488    -0.007497    -0.007513    -0.007545    -0.007716    -0.008019    -0.008673
        60.00    -0.009149    -0.009151    -0.009152    -0.009153    -0.009155    -0.009162    -0.009174    -0.009200    -0.009286    -0.009466    -0.009993
        80.00    -0.011166    -0.011166    -0.011167    -0.011167    -0.011168    -0.011172    -0.011179    -0.011194    -0.011249    -0.011380    -0.011805
       100.00    -0.013563    -0.013563    -0.013562    -0.013562    -0.013561    -0.013557    -0.013552    -0.013545    -0.013537    -0.013579    -0.013860
       120.00    -0.016535    -0.016535    -0.016533    -0.016530    -0.016524    -0.016505    -0.016476    -0.016421    -0.016286    -0.016150    -0.016178
       140.00    -0.020430    -0.020429    -0.020424    -0.020416    -0.020401    -0.020355    -0.020280    -0.020137    -0.019761    -0.019296    -0.018871
          </tableData>
        </table>
      </function> 
      <output> fcs/SFD/ap/afcs/lateral-trim-ctrl-rad </output>
    </fcs_function>


    <!-- propulsion/engine[1]/antitorque-ctrl-rad -->
    <fcs_function name="fcs/SFD/trim/antitorque-ctrl-rad"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00     0.071498     0.071791     0.071907     0.072021     0.072250     0.072946     0.074133     0.076617     0.085038     0.105664     0.163762
       -20.00     0.112443     0.114745     0.115258     0.115425     0.115757     0.116762     0.118468     0.122000     0.133633     0.157060     0.225170
         0.00     0.130936     0.140494     0.142705     0.142879     0.143212     0.144222     0.145937     0.149484     0.161153     0.184638     0.253073
        20.00     0.112474     0.114772     0.115284     0.115451     0.115783     0.116790     0.118499     0.122038     0.133693     0.157171     0.225485
        40.00     0.070552     0.070840     0.070955     0.071068     0.071296     0.071989     0.073172     0.075648     0.084708     0.103910     0.162860
        60.00     0.053266     0.053304     0.053352     0.053423     0.053567     0.054004     0.054752     0.056323     0.061704     0.073449     0.113442
        80.00     0.048178     0.048190     0.048222     0.048273     0.048376     0.048689     0.049224     0.050350     0.054214     0.062692     0.091996
       100.00     0.049119     0.049127     0.049151     0.049191     0.049272     0.049518     0.049939     0.050824     0.053861     0.060533     0.083696
       120.00     0.054116     0.054123     0.054143     0.054177     0.054245     0.054451     0.054804     0.055547     0.058094     0.063687     0.083116
       140.00     0.062504     0.062510     0.062528     0.062558     0.062618     0.062800     0.063112     0.063766     0.066011     0.070932     0.088009
          </tableData>
        </table>
      </function> 
      <output> fcs/SFD/ap/afcs/antitorque-trim-ctrl-rad </output>
    </fcs_function>


    <!-- attitude/phi-rad -->
    <fcs_function name="ap/afcs/automatic/phi-trim-rad"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00    -0.034389    -0.034509    -0.034533    -0.034539    -0.034551    -0.034587    -0.034654    -0.034806    -0.035421    -0.037989    -0.043522
       -20.00    -0.041922    -0.042905    -0.043092    -0.043105    -0.043130    -0.043207    -0.043339    -0.043620    -0.044601    -0.046710    -0.052874
         0.00    -0.043622    -0.048208    -0.049241    -0.049254    -0.049272    -0.049328    -0.049425    -0.049635    -0.050396    -0.052125    -0.057504
        20.00    -0.040868    -0.041920    -0.042119    -0.042131    -0.042155    -0.042226    -0.042349    -0.042612    -0.043538    -0.045552    -0.051533
        40.00    -0.030738    -0.030872    -0.030898    -0.030903    -0.030913    -0.030946    -0.031005    -0.031142    -0.031925    -0.033798    -0.039471
        60.00    -0.026197    -0.026210    -0.026210    -0.026205    -0.026196    -0.026170    -0.026132    -0.026073    -0.026052    -0.026558    -0.029897
        80.00    -0.025660    -0.025659    -0.025651    -0.025637    -0.025608    -0.025524    -0.025389    -0.025139    -0.024543    -0.024090    -0.025450
       100.00    -0.028409    -0.028404    -0.028389    -0.028363    -0.028311    -0.028158    -0.027908    -0.027430    -0.026170    -0.024659    -0.024015
       120.00    -0.034564    -0.034555    -0.034530    -0.034489    -0.034406    -0.034160    -0.033757    -0.032977    -0.030852    -0.028017    -0.025059
       140.00    -0.044788    -0.044776    -0.044737    -0.044674    -0.044545    -0.044168    -0.043547    -0.042342    -0.039017    -0.034403    -0.028530
          </tableData>
        </table>
      </function> 
    </fcs_function>


    <!-- attitude/theta-rad -->
    <fcs_function name="ap/afcs/automatic/theta-trim-rad"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00     0.000560     0.000558     0.000551     0.000538     0.000512     0.000436     0.000311     0.000067    -0.000624    -0.002539    -0.004343
       -20.00    -0.007484    -0.007615    -0.007641    -0.007644    -0.007650    -0.007669    -0.007700    -0.007759    -0.007916    -0.008110    -0.008216
         0.00    -0.006811    -0.006503    -0.006428    -0.006428    -0.006428    -0.006429    -0.006431    -0.006434    -0.006449    -0.006483    -0.006603
        20.00     0.000009     0.000322     0.000377     0.000374     0.000366     0.000343     0.000303     0.000223    -0.000035    -0.000522    -0.001772
        40.00    -0.011455    -0.011419    -0.011406    -0.011395    -0.011373    -0.011307    -0.011198    -0.010984    -0.008614    -0.005574    -0.002553
        60.00    -0.023850    -0.023846    -0.023833    -0.023813    -0.023771    -0.023647    -0.023442    -0.023040    -0.021555    -0.018886    -0.014128
        80.00    -0.035648    -0.035641    -0.035620    -0.035585    -0.035515    -0.035305    -0.034958    -0.034275    -0.032310    -0.029320    -0.024457
       100.00    -0.050129    -0.050119    -0.050089    -0.050038    -0.049936    -0.049632    -0.049129    -0.048135    -0.045263    -0.040838    -0.033416
       120.00    -0.066876    -0.066863    -0.066823    -0.066756    -0.066622    -0.066223    -0.065561    -0.064251    -0.060445    -0.054520    -0.044362
       140.00    -0.085561    -0.085544    -0.085495    -0.085413    -0.085251    -0.084760    -0.083948    -0.082338    -0.077630    -0.070224    -0.057281
          </tableData>
        </table>
      </function> 
    </fcs_function>

    <!-- trim input for use with rotor-ctrl system -->
    <!-- ctrl source: '{JSBSim-installation}/aircraft/ah1s/Systems/rotor_control.xml' -->

    <!-- fcs/collective-cmd-norm -->
    <fcs_function name="fcs/automatic/collective-trim-cmd-norm"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00     0.420570     0.421860     0.422452     0.423098     0.424392     0.428302     0.434913     0.448489     0.492242     0.588574     0.813337
       -20.00     0.499067     0.508403     0.510502     0.511204     0.512599     0.516811     0.523916     0.538457     0.584881     0.672784     0.899537
         0.00     0.518254     0.560120     0.569872     0.570568     0.571898     0.575912     0.582685     0.596555     0.640904     0.725165     0.944093
        20.00     0.498597     0.507894     0.509985     0.510687     0.512082     0.516294     0.523400     0.537941     0.584365     0.672254     0.898932
        40.00     0.414215     0.415493     0.416084     0.416733     0.418031     0.421954     0.428585     0.442204     0.489708     0.579924     0.808393
        60.00     0.381838     0.382081     0.382453     0.383036     0.384207     0.387745     0.393734     0.406060     0.445997     0.523810     0.734671
        80.00     0.384513     0.384633     0.384954     0.385486     0.386552     0.389779     0.395248     0.406540     0.443417     0.516227     0.717422
       100.00     0.416069     0.416165     0.416451     0.416926     0.417881     0.420773     0.425691     0.435898     0.469674     0.537829     0.731757
       120.00     0.477973     0.478054     0.478296     0.478700     0.479513     0.481983     0.486205     0.495053     0.525017     0.587744     0.774695
       140.00     0.575483     0.575545     0.575732     0.576045     0.576678     0.578597     0.581918     0.589006     0.614036     0.669810     0.848482
          </tableData>
        </table>
      </function> 
    </fcs_function>


    <!-- fcs/elevator-cmd-norm -->
    <fcs_function name="fcs/automatic/pitch-trim-cmd-norm"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00    -0.389217    -0.389536    -0.389725    -0.389959    -0.390427    -0.391844    -0.394237    -0.399153    -0.414992    -0.450208    -0.530614
       -20.00    -0.312509    -0.313659    -0.313939    -0.314063    -0.314310    -0.315057    -0.316317    -0.318902    -0.327198    -0.343080    -0.384987
         0.00    -0.222473    -0.222729    -0.222788    -0.222789    -0.222792    -0.222800    -0.222814    -0.222845    -0.222959    -0.223232    -0.224204
        20.00    -0.100969    -0.099323    -0.098971    -0.098879    -0.098696    -0.098144    -0.097206    -0.095266    -0.088897    -0.076228    -0.040995
        40.00    -0.031686    -0.031095    -0.030856    -0.030617    -0.030139    -0.028694    -0.026252    -0.021233    -0.003002     0.030882     0.115349
        60.00     0.008793     0.008893     0.009080     0.009381     0.009986     0.011816     0.014918     0.021324     0.044956     0.093917     0.224770
        80.00     0.089852     0.089928     0.090139     0.090489     0.091194     0.093328     0.096961     0.104512     0.129569     0.180260     0.324279
       100.00     0.192021     0.192093     0.192309     0.192669     0.193392     0.195593     0.199363     0.207295     0.234358     0.291455     0.461718
       120.00     0.327167     0.327231     0.327421     0.327739     0.328379     0.330342     0.333749     0.341080     0.367381     0.426882     0.617856
       140.00     0.508841     0.508884     0.509015     0.509235     0.509686     0.511071     0.513560     0.519196     0.541574     0.598730     0.803502
          </tableData>
        </table>
      </function> 
    </fcs_function>


    <!-- fcs/aileron-cmd-norm -->
    <fcs_function name="fcs/automatic/roll-trim-cmd-norm"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00     0.298744     0.299161     0.299400     0.299692     0.300277     0.302042     0.305013     0.311070     0.330234     0.371852     0.462867
       -20.00     0.274904     0.277514     0.278155     0.278447     0.279030     0.280787     0.283745     0.289771     0.308805     0.344212     0.433622
         0.00     0.229708     0.239905     0.242343     0.242626     0.243179     0.244845     0.247652     0.253378     0.271525     0.305498     0.392178
        20.00     0.175505     0.177453     0.177965     0.178245     0.178803     0.180487     0.183325     0.189118     0.207518     0.242064     0.330382
        40.00     0.112958     0.113216     0.113406     0.113661     0.114173     0.115718     0.118327     0.123674     0.141161     0.174778     0.262242
        60.00     0.065032     0.065098     0.065241     0.065475     0.065944     0.067361     0.069755     0.074673     0.090528     0.121166     0.203404
        80.00     0.025869     0.025915     0.026049     0.026270     0.026715     0.028058     0.030330     0.035003     0.050118     0.079533     0.159562
       100.00    -0.008189    -0.008144    -0.008012    -0.007791    -0.007347    -0.006008    -0.003745     0.000904     0.015909     0.045074     0.124778
       120.00    -0.040397    -0.040350    -0.040206    -0.039966    -0.039484    -0.038034    -0.035590    -0.030600    -0.014707     0.015612     0.097299
       140.00    -0.075396    -0.075338    -0.075161    -0.074867    -0.074279    -0.072509    -0.069547    -0.063568    -0.045043    -0.011205     0.075914
          </tableData>
        </table>
      </function> 
    </fcs_function>


    <!-- fcs/rudder-cmd-norm -->
    <fcs_function name="fcs/automatic/yaw-trim-cmd-norm"> 
      <function>
        <table>
          <independentVar lookup="row"> fcs/SFD/trim/v_dir_kts </independentVar>
          <independentVar lookup="column"> position/h-sl-ft </independentVar>
          <tableData>
                     10.00        20.00        50.00       100.00       200.00       500.00      1000.00      2000.00      5000.00     10000.00     20000.00
       -40.00     0.113092     0.113695     0.113869     0.113990     0.114234     0.114991     0.116335     0.119353     0.131374     0.169434     0.313638
       -20.00     0.278204     0.283577     0.284758     0.285127     0.285863     0.288101     0.291935     0.300006     0.327750     0.388067     0.586312
         0.00     0.365698     0.385539     0.390073     0.390485     0.391282     0.393706     0.397849     0.406538     0.436134     0.499663     0.705931
        20.00     0.278748     0.284128     0.285311     0.285682     0.286421     0.288668     0.292518     0.300625     0.328491     0.389102     0.588544
        40.00     0.112887     0.113469     0.113637     0.113753     0.113989     0.114720     0.116023     0.118960     0.131554     0.166560     0.312555
        60.00     0.042570     0.042594     0.042560     0.042495     0.042364     0.041982     0.041380     0.040316     0.038478     0.041910     0.096578
        80.00     0.012180     0.012155     0.012072     0.011935     0.011660     0.010835     0.009466     0.006749    -0.001083    -0.011827    -0.008863
       100.00    -0.007662    -0.007693    -0.007785    -0.007939    -0.008248    -0.009179    -0.010748    -0.013942    -0.023900    -0.040979    -0.066364
       120.00    -0.029080    -0.029106    -0.029186    -0.029320    -0.029588    -0.030403    -0.031794    -0.034699    -0.044350    -0.063113    -0.103699
       140.00    -0.059943    -0.059959    -0.060009    -0.060091    -0.060259    -0.060774    -0.061681    -0.063676    -0.071092    -0.088062    -0.135132
          </tableData>
        </table>
      </function> 
    </fcs_function>

  </channel>

</system>
