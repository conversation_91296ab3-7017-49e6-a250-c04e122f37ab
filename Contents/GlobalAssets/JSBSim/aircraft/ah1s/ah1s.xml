<?xml version="1.0"?>
<fdm_config name="ah1s-jsbsim" release="ALPHA" version="2.0">
  
  <fileheader>
    <author> <PERSON> </author>
    <email> t.kreitler at web 056 de </email>
    <filecreationdate>2010-10-07</filecreationdate>
    <version>$Revision: 1.5 $</version>
    <description> Models a Bell209/AH1S helicopter. </description>

    <reference refID="BR59" author="Bramwell, A. R. S." title="Longitudinal stability and control of the single rotor helicopter - Aeronautical Research Council R&amp;M 3104" date="1959"/>
    <reference refID="SH79" author="<PERSON><PERSON><PERSON><PERSON><PERSON>, J. <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>" title="Development and Validation of a Piloted Simulation of a Helicopter and External Sling Load - NASA TP-1285" date="1979"/>
    <reference refID="TA77" author="<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>" title="A Mathematical Force and Moment Model of a UH-1H Helicopter for Flight Dynamics Simulations - NASA TM-73254" date="1977"/>
    <reference refID="TA82" author="<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> A., and <PERSON>, <PERSON>. N." title="A Mathematical Model of a Single Main Rotor Helicopter for Piloted Simulation - NASA TM-84281" date="1982"/>

    <note> Aerodynamic helicopter model in wind axes.
           Trigonometric functions are used to cover the full alpha/beta range (See /TA82/). </note>

    <note> Wing aerodynamics are calculated in the same manner as in fixed wing aircraft.
           The other contributions use helicopter specific quantities. </note>

    <note> Additional setup options are in the 'aerodynamics' section </note>

    <note> The model currently flies with electric engines, so tanks are left empty. </note>

    <limitation> The rotor downwash adds an varying 'wind' system, thus some axis transforms
                 occur in the 'aerodynamics' section.
                 Currently this is an experimental (read: 'not so funny') feature. The calculations
                 used are *not* valid for pure sideward flight at high speeds, but should be OK
                 for common forward/backward flight conditions. </limitation>

    <limitation> Horizontal tail incidence variation ('elevator') is experimental. </limitation>

    <limitation> Estimation of fuselage moments is rather fair. </limitation>

    <license>
      <licenseName>GPL</licenseName>
      <licenseURL>http://www.gnu.org/licenses/gpl.html</licenseURL>
      <!--
        DISCLAIMER:
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. It has been validated only to the extent that it may seem
        to "fly right", and possibly to comply to published, publicly known, 
        performance data (maximum speed, endurance, etc.).

        This model is distributed in the hope that it will be useful,
        but WITHOUT ANY WARRANTY; without even the implied warranty of
        MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
        GNU General Public License for more details.
      -->

      <!--
      
        And to avoid unneeded 'friction-losses':
      
                              -= ALTERNATIVE LICENSE =-
        In case the model is used in conjunction with additional program code,
        3D-models, or forms a basis for derived models, and you are publishing
        your work as 'open source', it is also allowed to use and/or distribute
        the model under a CC-BY-NC-SA licence (http://creativecommons.org)

      -->

    </license>

  </fileheader>
  
  <metrics>
    <wingarea unit="FT2">         16.60 </wingarea>         <!-- metrics/Sw-sqft  -->
    <wingspan unit="FT">          10.75 </wingspan>         <!-- metrics/bw-ft    -->
    <wing_incidence unit="DEG">    8.50 </wing_incidence>   <!-- metrics/iw-deg   -->
    <chord unit="FT">              1.54 </chord>            <!-- metrics/cbarw-ft -->
    <htailarea unit="FT2">        12.00 </htailarea>        <!-- metrics/Sh-sqft  -->
    <htailarm unit="FT">          16.50 </htailarm>         <!-- metrics/lh-ft    -->
    <vtailarea unit="FT2">        17.85 </vtailarea>        <!-- metrics/Sv-sqft  -->
    <vtailarm unit="FT">          25.00 </vtailarm>         <!-- metrics/lv-ft    -->
    <!-- more relevant parameters are given in the aerodynamics section -->

    <location name="AERORP" unit="IN">
       <x> 176.0 </x>
       <y>   0.0 </y>
       <z>  55.0 </z>
    </location>
    <location name="EYEPOINT" unit="IN">
       <x> 121.0 </x>
       <y>   0.0 </y>
       <z>  86.0 </z>
    </location>
    <location name="VRP" unit="IN">
      <x>   0.0 </x>
      <y>   0.0 </y>
      <z>   0.0 </z>
    </location>
  </metrics>
  
  <mass_balance>
    <ixx unit="SLUG*FT2">    2593.0 </ixx>
    <iyy unit="SLUG*FT2">   14320.0 </iyy>
    <izz unit="SLUG*FT2">   12330.0 </izz>
    <ixz unit="SLUG*FT2">       0.0 </ixz>
    <!-- grossweight: 6000 to 10000 lbs  -->
    <emptywt unit="LBS">    8500.0 </emptywt>
    <location name="CG" unit="IN">
      <x>   172.0 </x><!-- 'allowed' range ~ 167 - 177 -->
      <y>     0.0 </y>
      <z>    75.0 </z>
    </location>
  </mass_balance>
  
  <ground_reactions>
    <contact type="BOGEY" name="skid_front_left">
      <location unit="IN">
        <x>    115.90 </x>
        <y>   -42.0 </y>
        <z>    -4.0 </z>
      </location>
      <static_friction>    0.85  </static_friction>
      <dynamic_friction>   0.65  </dynamic_friction>
      <rolling_friction>   0.65  </rolling_friction>
      <spring_coeff unit="LBS/FT">        6375.0 </spring_coeff>
      <damping_coeff unit="LBS/FT/SEC">   1275.0 </damping_coeff>
      <brake_group> NONE </brake_group>
      <max_steer> 0 </max_steer>
    </contact>
    <contact type="BOGEY" name="skid_front_right">
      <location unit="IN">
        <x>   115.90 </x>
        <y>    42.0 </y>
        <z>    -4.0 </z>
      </location>
      <static_friction>    0.85  </static_friction>
      <dynamic_friction>   0.65  </dynamic_friction>
      <rolling_friction>   0.65  </rolling_friction>
      <spring_coeff unit="LBS/FT">        6375.0 </spring_coeff>
      <damping_coeff unit="LBS/FT/SEC">   1275.0 </damping_coeff>
      <brake_group> NONE </brake_group>
      <max_steer> 0 </max_steer>
    </contact>
    <contact type="BOGEY" name="skid_rear_left">
      <location unit="IN">
        <x>   235.0 </x>
        <y>   -42.0 </y>
        <z>    -5.0 </z>
      </location>
      <static_friction>    0.85  </static_friction>
      <dynamic_friction>   0.65  </dynamic_friction>
      <rolling_friction>   0.65  </rolling_friction>
      <spring_coeff unit="LBS/FT">        6375.0 </spring_coeff>
      <damping_coeff unit="LBS/FT/SEC">   1275.0 </damping_coeff>
      <brake_group> NONE </brake_group>
      <max_steer> 0 </max_steer>
    </contact>
    <contact type="BOGEY" name="skid_rear_right">
      <location unit="IN">
        <x>   235.0 </x>
        <y>    42.0 </y>
        <z>    -5.0 </z>
      </location>
      <static_friction>    0.85  </static_friction>
      <dynamic_friction>   0.65  </dynamic_friction>
      <rolling_friction>   0.65  </rolling_friction>
      <spring_coeff unit="LBS/FT">        6375.0 </spring_coeff>
      <damping_coeff unit="LBS/FT/SEC">   1275.0 </damping_coeff>
      <brake_group> NONE </brake_group>
      <max_steer> 0 </max_steer>
    </contact>
    <contact type="STRUCTURE" name="bumper_rear">
      <location unit="IN">
        <x>   490.0 </x>
        <y>     0.0 </y>
        <z>    40.0 </z>
      </location>
      <static_friction>    0.85  </static_friction>
      <dynamic_friction>   0.65  </dynamic_friction>
      <rolling_friction>   0.65  </rolling_friction>
      <spring_coeff unit="LBS/FT">         6000.0 </spring_coeff>
      <damping_coeff unit="LBS/FT/SEC">    2000.0 </damping_coeff>
    </contact>
  </ground_reactions>
  
  <propulsion>
    <!-- should become avco_lycoming_t53 turbo-shaft engine -->
    <engine file="electric_1500hp"> 
      <feed>0</feed>
      <feed>1</feed>
      <thruster file="ah1s_rotor">
        <location unit="IN">
          <x>   176.0 </x>
          <y>     0.0 </y>
          <z>   153.0 </z>
        </location>
        <orient unit="DEG">
          <roll>   0.0 </roll>
          <pitch> 90.0 </pitch>
          <yaw>    0.0 </yaw>
        </orient>
        <sense> 1 </sense>
      </thruster>
    </engine>
    <!-- dummy-engine, used for linkage -->
    <engine file="electric_1hp_dummy">
      <feed>0</feed>
      <feed>1</feed>
      <thruster file="ah1s_tail_rotor">
        <location unit="IN">
          <x> 496.67 </x>
          <y>  16.0 </y>
          <z> 119.0 </z>
        </location>
        <orient unit="DEG">
          <roll>  0.0 </roll>
          <pitch> 0.0 </pitch>
          <yaw>  90.0 </yaw>
        </orient>
        <sense> 1 </sense>
      </thruster>
    </engine>
    <!--
        two tanks 260 US galons usable (262 total)
        JP-4: 6.5 lbs/gal JP-5: 6.8 lbs/gal
    -->
    <tank number="0" type="FUEL">
      <location unit="IN">
        <x>   146.0 </x>
        <y>     0.0 </y>
        <z>    38.0 </z>
      </location>
      <capacity unit="LBS">    890.0  </capacity>
      <contents unit="LBS">      0.0  </contents>
      <type>JP-5</type>
    </tank>
    <tank number="1" type="FUEL">
      <location unit="IN">
        <x>   206.0 </x>
        <y>     0.0 </y>
        <z>    38.0 </z>
      </location>
      <capacity unit="LBS">    890.0 </capacity>
      <contents unit="LBS">      0.0 </contents>
      <type>JP-5</type>
    </tank>
  </propulsion>
  
  <system file="rpm_governor"/>
  
  <system file="rotor_control"/>
  
  <system file="afcs"/>
  
  <system file="steady_flight_data"/>
  
  <system file="trim_control"/>
  
  <system name="misc support">
    <channel name="climb/approach angle">

      <fcs_function name="attitude/pitch-deg">
        <function>
          <product>
            <value> 57.2957795 </value>
            <property> attitude/pitch-rad </property>
          </product>
        </function>
      </fcs_function>

      <fcs_function name="attitude/approach-angle-deg">
        <function>
          <difference>
            <property> attitude/pitch-deg </property>
            <property> aero/alpha-deg </property>
          </difference>
        </function>
      </fcs_function>
    </channel>

    <channel name="bell instruments">
      <fcs_function name="propulsion/engine/bell-torque-sensor-psi">
        <!--
           Relation between models' torque and the pressure sensor mounted
           at the gear box. Guessed from models' hover performance at
           various weights. (I fear that the error is about +/- 20%)

           Summary:
             - simple proportional relation,  10_000 lbsft matches approx 35psi -> factor 0.0035
             - linear fit, psi(lbsft) = 0.004158*torque-lbsft - 7.334

           Limits from operator's manual translated back to model torque:
             continuous operation up to 50 'psi' ~ 13800 lbsft
             30 minute limit up to 56 'psi'      ~ 15250 lbsft

        -->
        <function>
          <sum>
            <product>
               <value> 0.00416 </value>
               <property> propulsion/engine/torque-lbsft </property>
            </product>
            <value> -7.33 </value>
          </sum>
        </function>
        <output>/rotors/main/torque-sensor-psi</output> 
      </fcs_function>
    </channel>
  </system>
  
  <flight_control name="ah1s fcs">

    <channel name="tailplane">
      <fcs_function name="fcs/var-incidence-ht-rad">
        <function>
          <description>
            Variation of horizontal stabilizer (aka elevator) incidence with
            longitudinal stick position/control input. Guessed from UH1 data
            in /TA77/. Positve values yield to nose-up moment (mind: forward
            stick gives positive longitudinal-ctrl-rad).
            Note: this is pretty experimental!
          </description>
          <sum>
            <product>
              <value> 1.0 </value>
              <table>
                <independentVar> propulsion/engine/longitudinal-ctrl-rad </independentVar>
                <tableData>
                    -0.04   0.21
                    -0.01   0.0
                     0.00   -0.01
                     0.02   -0.01
                     0.09   0.06
                     0.15   0.12
                </tableData>
              </table>
            </product>
            <value> 0.0 </value>
          </sum>
        </function>
      </fcs_function>
    </channel>

    <channel name="downwash-delay">
      <lag_filter name="aero/theta-downwash-delayed-rad">
        <description>
          Smooth downwash angle value (forward-backward), and
          roughly account for delayed impingement on wings.
        </description>
        <input> propulsion/engine/theta-downwash-rad </input>
        <c1>    10.0 </c1>
      </lag_filter>
      <lag_filter name="aero/phi-downwash-delayed-rad">
        <description> Smooth left-right downwash angle. </description>
        <input> propulsion/engine/phi-downwash-rad </input>
        <c1>    10.0 </c1>
      </lag_filter>
    </channel>

    <channel name="groundeffect-scale">
      <fcs_function name="aero/groundeffect-scale">
        <function>
        <description> gives 1.0 in hover, 0.001 at 40kts (or ~70fps) </description>
        <exp>
          <product>
            <value> -0.056 </value>
            <property> velocities/vt-fps </property>
          </product>
        </exp>
        </function>
        <output> propulsion/engine/groundeffect-scale-norm </output>
      </fcs_function>
    </channel>

  </flight_control>
  
  <aerodynamics>


    <!-- ============================================================
           Vehicle setup
         ============================================================ -->

    <!--
      Limiting the yaw rate:
         0.0  :  pure model (mind: the model isn't complete).
         0.05 :  compatible to the 'tweak'-value used in previous version.
         1.0  :  yields to easier interactive handling when using mouse input
                 (And also helps when exploring backward flight).
     -->
    <property value="0.05"> aero/setup/Nr_limiter </property>

    <!-- enable/disable downwash effects (debug option) -->
    <property value="1.0"> aero/setup/downwash-enable </property>


    <!-- ============================================================
           Rotor properties used below
         ============================================================ -->

    <!-- NOTE: blade-area = num_blades * chord * radius -->
    <property value="22.00">   metrics/rotor/R-ft     </property>
    <property value="1520.53"> metrics/rotor/Area-ft2 </property>
    <property value="0.06511"> metrics/rotor/solidity </property> <!-- blade-area / disk-area -->


    <!-- ============================================================
           Reference properties and non dimensional velocities
         ============================================================ -->

    <property value="99.00">   aero/ref/area-ft2   </property> <!-- solidity * Area -->
    <property value="2178.00"> aero/ref/volume-ft3 </property> <!-- solidity * Area * Radius, currently unused -->
    <property value="746.44">  aero/ref/vtip-fps   </property> <!-- rotor tip speed, Omega_mr * Radius -->

    <function name="aero/HIhat-ps">  <!-- rho*solidity*Area * Omega*R * R -->
      <description> Dynamic momentum, see /BR59/. </description>
      <product>
        <property> atmosphere/rho-slugs_ft3 </property>
        <property> metrics/rotor/solidity   </property>
        <property> metrics/rotor/Area-ft2   </property>
        <property> aero/ref/vtip-fps        </property>
        <property> metrics/rotor/R-ft       </property>
      </product>
    </function>

    <function name="aero/HWhat-psft">  <!-- rho*solidity*Area * Omega*R * R*R -->
      <description> Dynamic angular momentum, see /BR59/. </description>
      <product>
        <property> atmosphere/rho-slugs_ft3 </property>
        <property> metrics/rotor/solidity   </property>
        <property> metrics/rotor/Area-ft2   </property>
        <property> aero/ref/vtip-fps        </property>
        <property> metrics/rotor/R-ft       </property>
        <property> metrics/rotor/R-ft       </property>
      </product>
    </function>

    <function name="aero/mu">
      <description> Common shortcut for advance ratio. </description>
      <property> propulsion/engine/advance-ratio </property>
    </function>

    <!-- ============================================================
           Downwash incidence and angle normalization
         ============================================================ -->

    <!--

       A more or less 'funny' part - this is a memo for the angles used.

          theta-downwash : positive values turn forward (zero is rotor shaft axis)
          phi-downwash   : positive values turn left (cw when viewed from pilots' seat)

          alpha          : positive when wind comes from below the nose
          wing incidence : positive when the chord line points over the nose

    -->

    <function name="aero/raw-alpha-iw-rad">
      <description> Adjust alpha for wing incidence w/o downwash. </description>
      <sum>
        <property> aero/alpha-rad </property>
        <property> metrics/iw-rad </property>
      </sum>
    </function>

    <function name="aero/alpha-iw-rad">
      <description> Normalize to +/- pi. </description>
      <product>
        <value>2.0</value>
        <atan><tan>
          <product>
            <value>0.5</value>
            <property> aero/raw-alpha-iw-rad </property>
          </product>
        </tan></atan>
      </product>
    </function>

    <function name="aero/raw-alpha-iht-rad">
      <description> Adjust alpha for varying horizontal tail incidence w/o downwash. </description>
      <sum>
        <property> aero/alpha-rad </property>
        <property> fcs/var-incidence-ht-rad </property>
      </sum>
    </function>

    <function name="aero/alpha-iht-rad">
      <description> Normalize to +/- pi. </description>
      <product>
        <value>2.0</value>
        <atan><tan>
          <product>
            <value>0.5</value> <property> aero/raw-alpha-iht-rad </property>
          </product>
        </tan></atan>
      </product>
    </function>

    <function name="aero/downwash-window-wing">
      <description> 
         Turns to one if wing is immersed in downwash. (Note, the window is
         currently a simple square, not a circle)
      </description>
      <product>
        <table>
          <independentVar> aero/theta-downwash-delayed-rad </independentVar>
          <tableData>
              -1.30  0
              -1.26  1
               1.26  1
               1.30  0
          </tableData>
        </table>
        <table>
          <independentVar> aero/phi-downwash-delayed-rad </independentVar>
          <tableData>
              -1.37  0.0
              -1.2   0.5
              -1.1   1.0
               0.0   1
               1.1   1.0
               1.2   0.5
               1.37  0.0
          </tableData>
        </table>
      </product>
    </function>

    <function name="aero/downwash-window-wing-value">
      <product>
        <property> aero/setup/downwash-enable </property> <!-- debug switch -->
        <property> aero/downwash-window-wing </property>
      </product>
    </function>

    <function name="aero/downwash-window-wing-compl">
      <description>
         Turns to zero if wing is immersed in downwash.
         (Or stays at one if downwash is disabled)
      </description>
      <difference>
        <value> 1.0 </value>
        <property> aero/downwash-window-wing-value </property>
      </difference>
    </function>

    <function name="aero/downwash-window-ht">
      <description> Turns to one if horizontal tail is immersed in downwash. </description>
      <product>
        <table>
          <independentVar> aero/theta-downwash-delayed-rad </independentVar>
          <tableData>
              -1.42  0
              -1.36  1
               0.63  1
               0.69  0
          </tableData>
        </table>
        <table>
          <independentVar> aero/phi-downwash-delayed-rad </independentVar>
          <tableData>
              -1.37  0.0
              -1.2   0.5
              -1.1   1.0
               0.0   1
               1.1   1.0
               1.2   0.5
               1.37  0.0
          </tableData>
        </table>
      </product>
    </function>

    <function name="aero/downwash-window-ht-value">
      <product>
        <property> aero/setup/downwash-enable </property> <!-- debug switch -->
        <property> aero/downwash-window-ht </property>
      </product>
    </function>

    <function name="aero/downwash-window-ht-compl">
      <description>
         Turns to zero if horizontal tail is immersed in downwash.
         (Or stays at one if downwash is disabled)
      </description>
      <difference>
        <v> 1.0 </v>
        <p> aero/downwash-window-ht-value </p>
      </difference>
    </function>

    <function name="aero/alpha-downwash-rad">
      <description>
         Transform dw-theta to 'alpha' for usage with CL table.
         alpha_dw = - (theta_dw+90). 
         Here the helicopter specific shaft angle is 90 deg.
      </description>
      <difference>
        <v> -1.570796 </v>
        <p> aero/theta-downwash-delayed-rad </p>
      </difference>
    </function>

    <function name="aero/alpha-dw-diff-rad">
      <description>
         Difference between the two wind directions, used when the downwash
         contributions are transformed to the 'main' wind cordinate system.
         (note: this is an approximation for small betas.)
      </description>
      <difference>
        <p> aero/alpha-downwash-rad </p>
        <p> aero/alpha-rad </p>
      </difference>
    </function>


    <!-- repeat incidence adjustments for downwash case  -->

    <function name="aero/raw-alpha-dw-iw-rad">
      <description> Adjust alpha for wing incidence w. downwash. </description>
      <sum>
        <property> aero/alpha-downwash-rad </property>
        <property> metrics/iw-rad </property>
      </sum>
    </function>

    <function name="aero/alpha-dw-iw-rad">
      <description> Normalize to +/- pi. </description>
      <product>
        <value>2.0</value>
        <atan><tan>
          <product>
            <value>0.5</value>
            <property> aero/raw-alpha-dw-iw-rad </property>
          </product>
        </tan></atan>
      </product>
    </function>

    <function name="aero/raw-alpha-dw-iht-rad">
      <description> Adjust alpha for varying horizontal tail incidence w. downwash. </description>
      <sum>
        <property> aero/alpha-downwash-rad </property>
        <property> fcs/var-incidence-ht-rad </property>
      </sum>
    </function>

    <function name="aero/alpha-dw-iht-rad">
      <description> Normalize to +/- pi. </description>
      <product>
        <value>2.0</value>
        <atan><tan>
          <product>
            <value>0.5</value> <property> aero/raw-alpha-dw-iht-rad </property>
          </product>
        </tan></atan>
      </product>
    </function>

    <!-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
           Downwash adds some speed to the regular wind, thus the
           dynamic pressure is a bit larger too.
         - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  -->

    <function name="aero/downwash-qbar-psf">
      <description> Downwash addition to regular qbar-psf. </description>
      <product>
        <value> 0.5 </value>
        <property> atmosphere/rho-slugs_ft3 </property>
        <sum>
          <property> propulsion/engine/vi-fps </property>
          <property> velocities/vt-fps </property>
        </sum>
        <sum>
          <property> propulsion/engine/vi-fps </property>
          <property> velocities/vt-fps </property>
        </sum>
      </product>
    </function>

    <!-- ============================================================
           Coefficients
         ============================================================ -->

    <property value="0.105"> aero/coefficient/CD0_fus   </property>
    <property value="0.600"> aero/coefficient/CDa90_fus </property>
    <property value="1.826"> aero/coefficient/CDb90_fus </property>
    <!-- 
          NOTES: CDx_fus = (projected-area) / (aero/ref/area-ft2) * (0.5 .. 1.0)
                 CD0_fus = 0.1  yield to approx. 170kts at 5000ft and 0.066 to approx. 185kts.
    -->

    <function name="aero/coefficient/CMw_ht">
      <description> Horizontal tail pitch coefficient depending on w (estimated from stability derivative). </description>
      <table>
        <independentVar> aero/mu </independentVar>
        <tableData>
              0.0   0.00
              0.45 -0.07
        </tableData>
      </table>
    </function>

    <function name="aero/coefficient/CMq_ht">
      <description> Horizontal tail pitch coefficient depending on q (estimated from stability derivative). </description>
      <table>
        <independentVar> aero/mu </independentVar>
        <tableData>
            0.0   0.00
            0.45 -0.09
        </tableData>
      </table>
    </function>

    <function name="aero/coefficient/CNv_vt">
      <description> Vertical tail yaw coefficient depending on v (guessed from CMw_ht). </description>
      <table>
        <independentVar> aero/mu </independentVar>
        <tableData>
              0.0   0.00
              0.45  0.07
        </tableData>
      </table>
    </function>

    <function name="aero/coefficient/CNr_vt">
      <description> Vertical tail yaw coefficient depending on r (guessed from CMq_ht). </description>
      <table>
        <independentVar> aero/mu </independentVar>
        <tableData>
            0.0   0.0
            0.45 -0.09
        </tableData>
      </table>
    </function>

    <function name="aero/coefficient/CLa_wing">
      <description> Wing lift coefficient (a=5.5, CL_max=1.92). </description>
      <product>
        <table>
          <independentVar lookup="row"> aero/alpha-iw-rad </independentVar>
          <tableData>
             -3.49  -1.92
             -3.14   0.00
             -2.79   1.92
             -2.74   1.92
             -1.83   0.96
             -1.57   0.00
             -1.31  -0.96
             -0.40  -1.92
             -0.35  -1.92
              0.00   0.00
              0.35   1.92
              0.40   1.92
              1.31   0.96
              1.57   0.00
              1.83  -0.96
              2.74  -1.92
              2.79  -1.92
              3.14   0.00
              3.49   1.92
          </tableData>
        </table>
        <!-- no lift when immersed in downwash -->
        <property> aero/downwash-window-wing-compl </property>
        <!-- diminishing lift with beta -->
        <cos> <property> aero/beta-rad </property> </cos>
        <cos> <property> aero/beta-rad </property> </cos>
      </product>
    </function>

    <function name="aero/coefficient/CLa_ht">
      <description> Horizontal tail lift coefficient (a=3.5, CL_max=1.83). </description>
      <product>
        <table>
          <independentVar lookup="row"> aero/alpha-iht-rad </independentVar>
          <tableData>
             -3.67  -1.83
             -3.14   0.00
             -2.62   1.83
             -2.57   1.83
             -1.83   0.92
             -1.57   0.00
             -1.31  -0.92
             -0.58  -1.83
             -0.52  -1.83
              0.00   0.00
              0.52   1.83
              0.58   1.83
              1.31   0.92
              1.57   0.00
              1.83  -0.92
              2.57  -1.83
              2.62  -1.83
              3.14   0.00
              3.67   1.83
          </tableData>
        </table>
        <!-- no lift when immersed in downwash -->
        <property> aero/downwash-window-ht-compl </property>
        <!-- diminishing lift with beta-->
        <cos> <property> aero/beta-rad </property> </cos>
        <cos> <property> aero/beta-rad </property> </cos>
      </product>
    </function>

    <function name="aero/coefficient/CNb_vt">
      <description> Vertical tail side force coefficient (a=3.5, CL_max=1.83). </description>
      <table>
        <independentVar lookup="row"> aero/beta-rad </independentVar>
        <tableData>
           -1.57   0.00
           -1.31  -0.92
           -0.58  -1.83
           -0.52  -1.83
            0.00   0.00
            0.52   1.83
            0.58   1.83
            1.31   0.92
            1.57   0.00
        </tableData>
      </table>
    </function>

    <function name="aero/coefficient/CD0a_wing">
      <description> Drag coefficient at zero lift, w/o downwash. </description>
      <product>
        <table>
          <independentVar lookup="row"> aero/alpha-iw-rad </independentVar>
          <tableData>
           -3.14    0.03
           -1.57    1.500
           -0.26    0.031
            0.00    0.024
            0.26    0.031
            1.57    1.500
            3.14    0.03
          </tableData>
        </table>
        <property> aero/downwash-window-wing-compl </property>
      </product>
    </function>

    <!-- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
           Tables for downwash contributions
         - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  -->

    <!--   Note: 'tableData' content is the same as above,
                 but lookup-parameters and factors aren't!  -->

    <function name="aero/coefficient/CLa_wing_dw">
      <description> Wing lift coefficient (a=5.5, CL_max=1.92). </description>
      <product>
        <table>
          <independentVar lookup="row"> aero/alpha-dw-iw-rad </independentVar>
          <tableData>
             -3.49  -1.92
             -3.14   0.00
             -2.79   1.92
             -2.74   1.92
             -1.83   0.96
             -1.57   0.00
             -1.31  -0.96
             -0.40  -1.92
             -0.35  -1.92
              0.00   0.00
              0.35   1.92
              0.40   1.92
              1.31   0.96
              1.57   0.00
              1.83  -0.96
              2.74  -1.92
              2.79  -1.92
              3.14   0.00
              3.49   1.92
          </tableData>
        </table>
        <property> aero/downwash-window-wing-value </property>
        <!-- diminishing lift with beta -->
        <cos> <property> aero/beta-rad </property> </cos>
        <cos> <property> aero/beta-rad </property> </cos>
      </product>
    </function>

    <function name="aero/coefficient/CD0a_wing_dw">
      <description> Drag coefficient at zero lift, downwash case. </description>
      <product>
        <table>
          <independentVar lookup="row"> aero/alpha-dw-iw-rad </independentVar>
          <tableData>
           -3.14    0.03
           -1.57    1.500
           -0.26    0.031
            0.00    0.024
            0.26    0.031
            1.57    1.500
            3.14    0.03
          </tableData>
        </table>
        <property> aero/downwash-window-wing-value </property>
      </product>
    </function>

    <function name="aero/coefficient/CLa_ht_dw">
      <description> Horizontal tail lift coefficient (a=3.5, CL_max=1.83).
                    Table data is a copy from aero/coefficient/CLa_ht
      </description>
      <product>
        <table>
          <independentVar lookup="row"> aero/alpha-dw-iht-rad </independentVar>
          <tableData>
             -3.67  -1.83
             -3.14   0.00
             -2.62   1.83
             -2.57   1.83
             -1.83   0.92
             -1.57   0.00
             -1.31  -0.92
             -0.58  -1.83
             -0.52  -1.83
              0.00   0.00
              0.52   1.83
              0.58   1.83
              1.31   0.92
              1.57   0.00
              1.83  -0.92
              2.57  -1.83
              2.62  -1.83
              3.14   0.00
              3.67   1.83
          </tableData>
        </table>
        <property> aero/downwash-window-ht-value </property>
        <!-- diminishing lift with beta-->
        <cos> <property> aero/beta-rad </property> </cos>
        <cos> <property> aero/beta-rad </property> </cos>
      </product>
    </function>

    <!-- ============================================================
           The axes
         ============================================================ -->

    <axis name="LIFT">
      <function name="aero/force/La_wing">
        <description> Wing lift due to alpha, regular air stream. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CLa_wing </property>
        </product>
      </function>
      <function name="aero/force/L0_fus">
        <description> Fuselage lift at zero incidence, w/o downwash. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> aero/ref/area-ft2 </property>
          <value> -0.05 </value>
          <property> aero/coefficient/CDa90_fus </property>
          <cos> <property> aero/alpha-rad </property> </cos>
          <cos> <property> aero/alpha-rad </property> </cos>
        </product>
      </function>
      <function name="aero/force/La_fus">
        <description> Fuselage lift due to alpha, w/o downwash. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> aero/ref/area-ft2 </property>
          <property> aero/coefficient/CDa90_fus </property>
          <sin> <property> aero/alpha-rad </property> </sin>
          <cos> <property> aero/alpha-rad </property> </cos>
          <sum>
            <value> 0.2 </value>
            <product>
              <sin> <property> aero/alpha-rad </property> </sin>
              <sin> <property> aero/alpha-rad </property> </sin>
            </product>
          </sum>
        </product>
      </function>

      <!-- Transform the downwash contributions. -->
      <function name="aero/force/Lc_La_wing_dw">
        <description>
            Wing lift conribution caused by downwash. Projected onto 'regular' lift axis
        </description>
        <product>
          <cos> <property> aero/alpha-dw-diff-rad </property> </cos>
          <property> aero/downwash-qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CLa_wing_dw </property>
        </product>
      </function>
      <function name="aero/force/Lc_D0_wing">
        <description> Drag on wing at zero lift. </description>
        <product>
          <sin> <property> aero/alpha-dw-diff-rad </property> </sin>
          <property> aero/downwash-qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CD0a_wing_dw </property>
        </product>
      </function>
      <function name="aero/force/Lc_Di_wing">
        <description> Induced drag on wing. </description>
        <product>
          <sin> <property> aero/alpha-dw-diff-rad </property> </sin>
          <property> aero/downwash-qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CLa_wing_dw </property>
          <property> aero/coefficient/CLa_wing_dw </property>
          <value> 0.06 </value>
        </product>
      </function>
    </axis>

    <axis name="DRAG">
      <function name="aero/force/D0_wing">
        <description> Drag on wing at zero lift. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CD0a_wing </property>
        </product>
      </function>
      <function name="aero/force/Di_wing">
        <description> Induced drag on wing. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CLa_wing </property>
          <property> aero/coefficient/CLa_wing </property>
          <value> 0.06 </value>
        </product>
      </function>
      <function name="aero/force/D0_fus">
        <description> Fuselage drag. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> aero/ref/area-ft2 </property>
          <property> aero/coefficient/CD0_fus </property>
        </product>
      </function>
      <function name="aero/force/Da_fus">
        <description> Fuselage drag variation due to alpha. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> aero/ref/area-ft2 </property>
          <difference>
            <property> aero/coefficient/CDa90_fus </property>
            <property> aero/coefficient/CD0_fus </property>
          </difference>
          <abs>
            <sin> <property> aero/alpha-rad </property> </sin>
          </abs>
          <sin> <property> aero/alpha-rad </property> </sin>
          <sin> <property> aero/alpha-rad </property> </sin>
        </product>
      </function>
      <function name="aero/force/Db_fus">
        <description> Fuselage drag variation due to beta. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> aero/ref/area-ft2 </property>
          <property> aero/coefficient/CDb90_fus </property>
          <sin> <property> aero/beta-rad </property> </sin>
          <sin> <property> aero/beta-rad </property> </sin>
        </product>
      </function>

      <!-- Transform the downwash contributions. -->
      <function name="aero/force/Dc_La_wing_dw">
        <description>
            Wing lift conribution caused by downwash. Projected onto 'regular' drag axis
        </description>
        <product>
          <value> -1.0 </value>
          <sin> <property> aero/alpha-dw-diff-rad </property> </sin>
          <property> aero/downwash-qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CLa_wing_dw </property>
        </product>
      </function>
      <function name="aero/force/Dc_D0_wing">
        <description> Drag on wing at zero lift. </description>
        <product>
          <cos> <property> aero/alpha-dw-diff-rad </property> </cos>
          <property> aero/downwash-qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CD0a_wing_dw </property>
        </product>
      </function>
      <function name="aero/force/Dc_Di_wing">
        <description> Induced drag on wing. </description>
        <product>
          <cos> <property> aero/alpha-dw-diff-rad </property> </cos>
          <property> aero/downwash-qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/coefficient/CLa_wing_dw </property>
          <property> aero/coefficient/CLa_wing_dw </property>
          <value> 0.06 </value>
        </product>
      </function>
    </axis>

    <axis name="SIDE">
      <function name="aero/force/Yb_wing">
        <description> Side force on wing due to beta. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> metrics/Sw-sqft </property>
          <property> aero/beta-rad </property>
          <value> -1.0 </value>
          <!-- handle large angles -->
          <value> 2.0 </value>
          <sin> <property> aero/beta-rad </property> </sin>
          <cos> <property> aero/beta-rad </property> </cos>
        </product>
      </function>
      <function name="aero/force/Yb_fus">
        <description> Side force on fuselage due to beta. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> aero/ref/area-ft2 </property>
          <property> aero/coefficient/CDb90_fus </property>
          <value> -0.6 </value>
          <sin> <property> aero/beta-rad </property> </sin>
          <cos> <property> aero/beta-rad </property> </cos>
          <sum>
            <value> 1.0 </value>
            <product>
              <sin> <property> aero/beta-rad </property> </sin>
              <sin> <property> aero/beta-rad </property> </sin>
            </product>
          </sum>
        </product>
      </function>
    </axis>
    
    <!-- no ROLL contributions calculated -->

    <axis name="PITCH">
      <function name="aero/moment/Ma_ht">
        <description> Pitching moment due to tailplane lift. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> metrics/Sh-sqft </property>
          <property> metrics/lh-ft </property>
          <value>    -1.0 </value>
          <property> aero/coefficient/CLa_ht </property>
        </product>
      </function>
      <function name="aero/moment/Ma_ht_dw">
        <description> Pitching moment due to tailplane lift, downwash case. </description>
        <product>
          <property> aero/downwash-qbar-psf </property>
          <property> metrics/Sh-sqft </property>
          <property> metrics/lh-ft </property>
          <value>    -1.0 </value>
          <property> aero/coefficient/CLa_ht_dw </property>
        </product>
      </function>
      <function name="aero/moment/Mw_ht">
        <description> Pitching moment due to w. </description>
        <product>
          <property> aero/HIhat-ps </property>
          <property> aero/coefficient/CMw_ht </property>
          <property> velocities/w-aero-fps </property>
        </product>
      </function>
      <function name="aero/moment/Mq_ht">
        <description> Pitching moment due to q. </description>
        <product>
          <property> aero/HWhat-psft </property>
          <property> aero/coefficient/CMq_ht </property>
          <property> velocities/q-aero-rad_sec </property>
        </product>
      </function>
    </axis>

    <axis name="YAW">
      <function name="aero/moment/Nb_vt">
        <description> Yawing moment due to vertical fin. </description>
        <product>
          <property> aero/qbar-psf </property>
          <property> metrics/Sv-sqft </property>
          <property> metrics/lv-ft </property>
          <property> aero/coefficient/CNb_vt </property>
        </product>
      </function>
      <function name="aero/moment/Nv_vt">
        <description> Yawing moment due to v. </description>
        <product>
          <property> aero/HIhat-ps </property>
          <property> aero/coefficient/CNv_vt </property>
          <property> velocities/v-aero-fps </property>
        </product>
      </function>
      <function name="aero/moment/Nr_vt">
        <description> Yawing moment due to r. </description>
        <product>
          <property> aero/HWhat-psft </property>
          <property> aero/coefficient/CNr_vt </property>
          <property> velocities/r-aero-rad_sec </property>
        </product>
      </function>
      <function name="aero/moment/Nr_limiter">
        <description> Limit yaw rate to get more stability in hover. </description>
        <product>
          <value> -0.1 </value>
          <property> aero/HWhat-psft </property>
          <property> velocities/r-aero-rad_sec </property>
          <property> aero/setup/Nr_limiter </property>
          <table>
            <independentVar> aero/mu </independentVar>
            <tableData>
               0.0   1.0
               0.08  0.9
               0.15  0.0
            </tableData>
          </table>
        </product>
      </function>
    </axis>

  </aerodynamics>
  
</fdm_config>
