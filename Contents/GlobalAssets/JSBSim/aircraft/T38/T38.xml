<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="T38" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
     <author>Aeromatic v 0.8, DPC</author>
     <filecreationdate>2004-01-01</filecreationdate>
     <version>$Revision: 1.26 $</version>
     <description>Models a Northrop T-38</description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
 </fileheader>

<!--
  File:     T38.xml
  Inputs:
    name:          T38
    type:          two-engine transonic/supersonic fighter
    max weight:    12500.0 lb
    wing span:     25.25 ft
    length:        46.3 ft
    wing area:     170.0 sq-ft
    gear type:     tricycle
    retractable?:  yes
    # engines:     2
    engine type:   turbine
    engine layout: aft fuselage
    yaw damper?    yes
  Outputs:
    wing loading:  73.53 lb/sq-ft
    CL-alpha:      3.6 per radian
    CL-0:          0.08
    CL-max:        1
    CD-0:          0.024
    K:             0.09

-->

    <metrics>
        <wingarea unit="FT2">  170 </wingarea>
        <wingspan unit="FT">    25.25 </wingspan>
        <chord unit="FT">        6.73 </chord>
        <htailarea unit="FT2">  34 </htailarea>
        <htailarm unit="FT">    13 </htailarm>
        <vtailarea unit="FT2">  47 </vtailarea>
        <vtailarm unit="FT">    13 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 320 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 154 </x>
            <y> 0 </y>
            <z> 36 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 320 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance>
        <ixx unit="SLUG*FT2">  7806 </ixx>
        <iyy unit="SLUG*FT2"> 36075 </iyy>
        <izz unit="SLUG*FT2"> 31319 </izz>
        <emptywt unit="LBS">   7574 </emptywt>
        <location name="CG" unit="IN">
            <x> 314 </x>
            <y> 0 </y>
            <z> -10 </z>
        </location>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 73 </x>
                <y> 0 </y>
                <z> -46 </z>
            </location>
            <static_friction>  0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 5000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 500 </damping_coeff>
            <max_steer unit="DEG"> 5 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 335 </x>
                <y> -65 </y>
                <z> -46 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 37870 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3787 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 335 </x>
                <y> 65 </y>
                <z> -46 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 37870 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 3787 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>1</retractable>
        </contact>
	<contact type="STRUCTURE" name="NOSE">
	    <location unit="IN">
		<x> -22 </x>
		<y> 0 </y>
		<z> -4 </z>
	    </location>
	    <static_friction>  0.80 </static_friction>
	    <dynamic_friction> 0.50 </dynamic_friction>
	    <spring_coeff unit="LBS/FT">      5000.00 </spring_coeff>
	    <damping_coeff unit="LBS/FT/SEC">  500.00 </damping_coeff>
	</contact>
	<contact type="STRUCTURE" name="LEFT_WINGTIP">
	    <location unit="IN">
		<x> 330 </x>
		<y> -157 </y>
		<z> -15 </z>
	    </location>
	    <static_friction>  0.80 </static_friction>
	    <dynamic_friction> 0.50 </dynamic_friction>
	    <spring_coeff unit="LBS/FT">      5000.00 </spring_coeff>
	    <damping_coeff unit="LBS/FT/SEC">  500.00 </damping_coeff>
	</contact>
	<contact type="STRUCTURE" name="RIGHT_WINGTIP">
	    <location unit="IN">
		<x> 330 </x>
		<y> 157 </y>
		<z> -15 </z>
	    </location>
	    <static_friction>  0.80 </static_friction>
	    <dynamic_friction> 0.50 </dynamic_friction>
	    <spring_coeff unit="LBS/FT">      5000.00 </spring_coeff>
	    <damping_coeff unit="LBS/FT/SEC">  500.00 </damping_coeff>
	</contact>
	<contact type="STRUCTURE" name="CANOPY">
	    <location unit="IN">
		<x> 178 </x>
		<y> 0 </y>
		<z> 54 </z>
	    </location>
	    <static_friction>  0.80 </static_friction>
	    <dynamic_friction> 0.50 </dynamic_friction>
	    <spring_coeff unit="LBS/FT">      5000.00 </spring_coeff>
	    <damping_coeff unit="LBS/FT/SEC">  500.00 </damping_coeff>
	</contact>
	<contact type="STRUCTURE" name="VSTAB">
	    <location unit="IN">
		<x> 450 </x>
		<y> 0 </y>
		<z> 114 </z>
	    </location>
	    <static_friction>  0.80 </static_friction>
	    <dynamic_friction> 0.50 </dynamic_friction>
	    <spring_coeff unit="LBS/FT">      5000.00 </spring_coeff>
	    <damping_coeff unit="LBS/FT/SEC">  500.00 </damping_coeff>
	</contact>
	<contact type="STRUCTURE" name="ENGINES">
	    <location unit="IN">
		<x> 525 </x>
		<y> 0 </y>
		<z> 5 </z>
	    </location>
	    <static_friction>  0.80 </static_friction>
	    <dynamic_friction> 0.50 </dynamic_friction>
	    <spring_coeff unit="LBS/FT">      5000.00 </spring_coeff>
	    <damping_coeff unit="LBS/FT/SEC">  500.00 </damping_coeff>
	</contact>
      </ground_reactions>
    <propulsion>
        <engine file="J85-GE-5">
            <feed>0</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 551 </x>
                    <y> -20 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="J85-GE-5">
            <feed>1</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 551 </x>
                    <y> 20 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <tank type="FUEL">    <!-- Tank number 0 -->
            <location unit="IN">
                <x> 298 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <capacity unit="LBS"> 1953 </capacity>
            <contents unit="LBS"> 1900 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 1 -->
            <location unit="IN">
                <x> 370 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <capacity unit="LBS"> 2029 </capacity>
            <contents unit="LBS"> 2000 </contents>
        </tank>
    </propulsion>
    <flight_control name="FCS: T38-FCS">
        <channel name="Pitch">
            <summer name="Pilot Pitch Sum">
                <input>fcs/elevator-cmd-norm</input>
                <input>fcs/pitch-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <scheduled_gain name="Pitch Feel">
                <input>fcs/pilot-pitch-sum</input>
                <table>
                  <independentVar>velocities/mach</independentVar>
                    <tableData>
                        0.0000	1.0000
                        0.6000	1.0000
                        1.2000	0.7000
                    </tableData>
                </table>
            </scheduled_gain>

            <aerosurface_scale name="Elevator Control">
                <input>fcs/pitch-feel</input>
                <range>
                    <min>-1.0</min>
                    <max>0.583</max>
                </range>
                <output>fcs/elevator-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Roll">
            <summer name="Pilot Roll Sum">
                <input>fcs/aileron-cmd-norm</input>
                <input>fcs/roll-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <scheduled_gain name="Roll Feel">
                <input>fcs/pilot-roll-sum</input>
                <table>
                  <independentVar>velocities/mach</independentVar>
                    <tableData>
                        0.0000	1.0000
                        0.6000	1.0000
                        1.2000	0.7000
                    </tableData>
                </table>
            </scheduled_gain>

            <aerosurface_scale name="Left Aileron Control">
                <input>fcs/roll-feel</input>
                <range>
                    <min>-1.00</min>
                    <max>0.75</max>
                </range>
                <output>fcs/left-aileron-pos-norm</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right Aileron Control">
                <input>-fcs/roll-feel</input>
                <range>
                    <min>-1.00</min>
                    <max>0.65</max>
                </range>
                <output>fcs/right-aileron-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Yaw">
            <summer name="Rudder Command Sum">
                <input>fcs/rudder-cmd-norm</input>
                <input>fcs/yaw-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <scheduled_gain name="Yaw SAS Rate">
                <input>velocities/r-aero-rad_sec</input>
                <table>
                  <independentVar>velocities/mach</independentVar>
                    <tableData>
                        0.0000	0.0000
                        0.1000	0.0000
                        0.1100	1.0000
                    </tableData>
                </table>
            </scheduled_gain>

            <scheduled_gain name="Yaw SAS Beta">
                <input>aero/beta-rad</input>
                <table>
                  <independentVar>velocities/mach</independentVar>
                    <tableData>
                        0.0000	0.0000
                        1.2000	0.0000
                    </tableData>
                </table>
            </scheduled_gain>

            <summer name="Yaw SAS Sum">
                <input>fcs/yaw-sas-rate</input>
                <input>fcs/yaw-sas-beta</input>
                <clipto>
                    <min>-0.1</min>
                    <max>0.1</max>
                </clipto>
            </summer>

            <summer name="Rudder Sum">
                <input>fcs/rudder-command-sum</input>
                <input>fcs/yaw-sas-sum</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Rudder Control">
                <input>fcs/rudder-sum</input>
                <range>
                    <min>-1.00</min>
                    <max>1.00</max>
                </range>
                <output>fcs/rudder-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Throttles">
         <pure_gain name="Throttle1">
            <input>fcs/throttle-cmd-norm</input>
            <gain>2</gain>
            <output>fcs/throttle-pos-norm</output>
         </pure_gain>

         <pure_gain name="Throttle2">
            <input>fcs/throttle-cmd-norm[1]</input>
            <gain>2</gain>
            <output>fcs/throttle-pos-norm[1]</output>
         </pure_gain>
        </channel>
        <channel name="Flaps">
            <kinematic name="Flaps Control">
                <input>fcs/flap-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>24</position>
                        <time>5</time>
                    </setting>
                    <setting>
                        <position>40</position>
                        <time>3</time>
                    </setting>
                </traverse>
                <output>fcs/flap-pos-deg</output>
            </kinematic>
            <aerosurface_scale name="Flap Position Normalizer">
              <input>fcs/flap-pos-deg</input>
              <domain>
                <min>0</min>  <!-- Flaps actual minimum position -->
                <max>40</max>  <!-- Flaps actual maximum position -->
              </domain>
              <range>
                <min>0</min>  <!-- Flaps normalized minimum position -->
                <max>1</max>  <!-- Flaps normalized maximum position -->
              </range>
              <output>fcs/flap-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Landing Gear">
            <kinematic name="Gear Control">
                <input>gear/gear-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>3</time>
                    </setting>
                </traverse>
                <output>gear/gear-pos-norm</output>
            </kinematic>
        </channel>
        <channel name="Speedbrake">
            <kinematic name="Speedbrake Control">
                <input>fcs/speedbrake-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>1</time>
                    </setting>
                </traverse>
                <output>fcs/speedbrake-pos-norm</output>
            </kinematic>
        </channel>
    </flight_control>
    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CD0">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                             -1.5700	1.5000
                             -0.2600	0.0320
                              0.0000	0.0160
                              0.2600	0.0320
                              1.5700	1.5000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <value>0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/CDmach">
                <description>Drag_due_to_mach</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.0000
                              0.8100	0.0000
                              1.1000	0.0240
                              1.8000	0.0110
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDflap">
                <description>Drag_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-deg</property>
                    <value>0.00225</value>
                </product>
            </function>
            <function name="aero/coefficient/CDgear">
                <description>Drag_due_to_gear</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>gear/gear-pos-norm</property>
                    <value>0.0150</value>
                </product>
            </function>
            <function name="aero/coefficient/CDsb">
                <description>Drag_due_to_speedbrakes</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0250</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                             -1.5700	1.2300
                             -0.2600	0.0500
                              0.0000	0.0000
                              0.2600	0.0500
                              1.5700	1.2300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.0300</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-1.0000</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                          <tableData>
                                        0.0000 40.0000
                             -0.2000   -0.5710	0.1000
                              0.0000	0.0000	0.6000
                              0.2500	0.7160	1.3000
                              0.3300	0.9450	0.3000
                              0.6000	0.1740	0.0000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-norm</property>
                    <value>0.0600</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.0500
                              0.5200	-0.1200
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1200</value>
                </product>
            </function>
            <function name="aero/coefficient/CldaL">
                <description>Roll_moment_due_to_left_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-norm</property>
                    <value>0.0193</value>
                </product>
            </function>
            <function name="aero/coefficient/CldaR">
                <description>Roll_moment_due_to_right_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/right-aileron-pos-norm</property>
                    <value>-0.0193</value>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-norm</property>
                    <value>0.0021</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-0.3000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmde">
                <description>Pitch_moment_due_to_elevator</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-norm</property>
                    <value>-0.1950</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-18.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-9.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmflaps">
                <description>Pitch_moment_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/flap-pos-deg</property>
                    <value>-0.0025</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-norm</property>
                    <value>-0.0070</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
<!--
    <output name="T38.csv" type="CSV">
        <propulsion> ON </propulsion>
    </output>
-->
</fdm_config>
