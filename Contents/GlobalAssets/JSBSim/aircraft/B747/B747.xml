<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="B747-400" version="2.0" release="ALPHA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
      <author> Unknown </author>
      <filecreationdate> 2002-01-01 </filecreationdate>
      <version> $Id: B747.xml,v 1.25 2012/12/22 15:22:17 jberndt Exp $ </version>
      <description> Boeing 747 </description>
      <license>
        <licenseName>GPL (General Public License)</licenseName>
        <licenseURL>http://www.gnu.org/licenses/gpl.html</licenseURL>
      </license>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 5648 </wingarea>
        <wingspan unit="FT"> 211.5 </wingspan>
        <chord unit="FT"> 27.31 </chord>
        <htailarea unit="FT2"> 1469.6 </htailarea>
        <htailarm unit="FT"> 106.6 </htailarm>
        <vtailarea unit="FT2"> 829.5 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 1377 </x>
            <y> 0 </y>
            <z> -24 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 308 </x>
            <y> -21 </y>
            <z> 138 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 1327 </x>
            <y> 0 </y>
            <z> -24 </z>
        </location>
    </metrics>

    <mass_balance negated_crossproduct_inertia="true">
        <ixx unit="SLUG*FT2"> 1.82e+07 </ixx>
        <iyy unit="SLUG*FT2"> 3.31e+07 </iyy>
        <izz unit="SLUG*FT2"> 4.97e+07 </izz>
        <ixy unit="SLUG*FT2"> -0 </ixy>
        <ixz unit="SLUG*FT2"> -970000 </ixz>
        <iyz unit="SLUG*FT2"> -0 </iyz>
        <emptywt unit="LBS"> 523816 </emptywt>
        <location name="CG" unit="IN">
            <x> 1327 </x>
            <y> 0 </y>
            <z> -24 </z>
        </location>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 396 </x>
                <y> 0 </y>
                <z> -206 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 22000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 87302.6 </damping_coeff>
            <max_steer unit="DEG"> 5 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 1554 </x>
                <y> -216.5 </y>
                <z> -216 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 150000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 174605 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 1554 </x>
                <y> 216.5 </y>
                <z> -216 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 150000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 174605 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>1</retractable>
        </contact>
    </ground_reactions>
    <propulsion>
        <engine file="GE-CF6-80C2-B1F">
            <feed>0</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 1356 </x>
                    <y> -820 </y>
                    <z> -97 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="GE-CF6-80C2-B1F">
            <feed>1</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 996 </x>
                    <y> -460 </y>
                    <z> -121 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="GE-CF6-80C2-B1F">
            <feed>2</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 996 </x>
                    <y> 460 </y>
                    <z> -121 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="GE-CF6-80C2-B1F">
            <feed>3</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 1356 </x>
                    <y> 820 </y>
                    <z> -97 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <tank type="FUEL">    <!-- Tank number 0 -->
            <location unit="IN">
                <x> 1327 </x>
                <y> 0 </y>
                <z> -69.57 </z>
            </location>
            <capacity unit="LBS"> 10912.8 </capacity>
            <contents unit="LBS"> 5456.4 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 1 -->
            <location unit="IN">
                <x> 1327 </x>
                <y> 0 </y>
                <z> -69.57 </z>
            </location>
            <capacity unit="LBS"> 10912.8 </capacity>
            <contents unit="LBS"> 5456.4 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 2 -->
            <location unit="IN">
                <x> 1327 </x>
                <y> 0 </y>
                <z> -69.57 </z>
            </location>
            <capacity unit="LBS"> 10912.8 </capacity>
            <contents unit="LBS"> 5456.4 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 3 -->
            <location unit="IN">
                <x> 1327 </x>
                <y> 0 </y>
                <z> -69.57 </z>
            </location>
            <capacity unit="LBS"> 10912.8 </capacity>
            <contents unit="LBS"> 5456.4 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 4 -->
            <location unit="IN">
                <x> 1327 </x>
                <y> 0 </y>
                <z> -69.57 </z>
            </location>
            <capacity unit="LBS"> 10912.8 </capacity>
            <contents unit="LBS"> 5456.4 </contents>
        </tank>
    </propulsion>
    <flight_control name="FCS: B747">
      <channel name="all">
        <summer name="Pitch Trim Sum">
            <input>fcs/elevator-cmd-norm</input>
            <input>fcs/pitch-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Elevator Control">
            <input>fcs/pitch-trim-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.175</max>
            </range>
            <output>fcs/elevator-pos-rad</output>
        </aerosurface_scale>

        <summer name="Roll Trim Sum">
            <input>fcs/aileron-cmd-norm</input>
            <input>fcs/roll-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Left Aileron Control">
            <input>fcs/roll-trim-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/left-aileron-pos-rad</output>
        </aerosurface_scale>

        <aerosurface_scale name="Right Aileron Control">
            <input>-fcs/roll-trim-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/right-aileron-pos-rad</output>
        </aerosurface_scale>

        <summer name="Rudder Command Sum">
            <input>fcs/rudder-cmd-norm</input>
            <input>fcs/yaw-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <scheduled_gain name="Yaw Damper Rate">
            <input>velocities/r-aero-rad_sec</input>
            <table>
              <independentVar>aero/qbar-psf</independentVar>
               <tableData>
                  3.00        0.00
                 11.00        2.00
               </tableData>
            </table>
        </scheduled_gain>

        <scheduled_gain name="Yaw Damper Beta">
            <input>aero/beta-rad</input>
            <table>
              <independentVar>velocities/ve-kts</independentVar>
              <tableData>
                 30        0.00
                 60        0.00
              </tableData>
            </table>
        </scheduled_gain>

        <summer name="Yaw Damper Sum">
            <input>fcs/yaw-damper-beta</input>
            <input>fcs/yaw-damper-rate</input>
            <clipto>
                <min>-0.2</min>
                <max>0.2</max>
            </clipto>
        </summer>

        <scheduled_gain name="Yaw Damper Final">
            <input>fcs/yaw-damper-sum</input>
            <table>
              <independentVar lookup="row">aero/qbar-psf</independentVar>
              <tableData>
                 2.99         0.0
                 3.00         1.0
              </tableData>
            </table>
        </scheduled_gain>

        <summer name="Rudder Sum">
            <input>fcs/rudder-command-sum</input>
            <input>fcs/yaw-damper-final</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Rudder Control">
            <input>fcs/rudder-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/rudder-pos-rad</output>
        </aerosurface_scale>

        <kinematic name="Flaps Control">
            <input>fcs/flap-cmd-norm</input>
            <traverse>
                <setting>
                    <position>0</position>
                    <time>0</time>
                </setting>
                <setting>
                    <position>15</position>
                    <time>4</time>
                </setting>
                <setting>
                    <position>30</position>
                    <time>3</time>
                </setting>
            </traverse>
            <output>fcs/flap-pos-deg</output>

            <aerosurface_scale name="Flap Position Normalizer">
              <input>fcs/flap-pos-deg</input>
              <domain>
                <min>0</min>  <!-- Flaps actual minimum position -->
                <max>30</max>  <!-- Flaps actual maximum position -->
              </domain>
              <range>
                <min>0</min>  <!-- Flaps normalized minimum position -->
                <max>1</max>  <!-- Flaps normalized maximum position -->
              </range>
              <output>fcs/flap-pos-norm</output>
            </aerosurface_scale>

        </kinematic>

        <kinematic name="Gear Control">
            <input>gear/gear-cmd-norm</input>
            <traverse>
                <setting>
                    <position>0</position>
                    <time>0</time>
                </setting>
                <setting>
                    <position>1</position>
                    <time>5</time>
                </setting>
            </traverse>
            <output>gear/gear-pos-norm</output>
        </kinematic>

        <kinematic name="Speedbrake Control">
            <input>fcs/speedbrake-cmd-norm</input>
            <traverse>
                <setting>
                    <position>0</position>
                    <time>0</time>
                </setting>
                <setting>
                    <position>1</position>
                    <time>1</time>
                </setting>
            </traverse>
            <output>fcs/speedbrake-pos-norm</output>
        </kinematic>
      </channel>
    </flight_control>
    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CD0">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -1.5700	1.5000
                              -0.2600	0.0340
                              0.0000	0.0170
                              0.2600	0.0340
                              1.5700	1.5000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <value>0.0420</value>
                </product>
            </function>
            <function name="aero/coefficient/CDmach">
                <description>Drag_due_to_mach</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.0000
                              0.7900	0.0000
                              1.1000	0.0230
                              1.8000	0.0150
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDflap">
                <description>Drag_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-deg</property>
                    <value>0.001833</value>
                </product>
            </function>
            <function name="aero/coefficient/CDgear">
                <description>Drag_due_to_gear</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>gear/gear-pos-norm</property>
                    <value>0.0110</value>
                </product>
            </function>
            <function name="aero/coefficient/CDsb">
                <description>Drag_due_to_speedbrakes</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0170</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                              -1.5700	1.2300
                              -0.2600	0.0500
                              0.0000	0.0000
                              0.2600	0.0500
                              1.5700	1.2300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.0550</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-1.0000</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.2000	-0.6800
                              0.0000	0.2000
                              0.2300	1.2000
                              0.6000	0.6000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/dCLflap">
                <description>Delta_Lift_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-deg</property>
                    <value>0.05</value>
                </product>
            </function>
            <function name="aero/coefficient/dCLsb">
                <description>Delta_Lift_due_to_speedbrake</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>-0.0800</value>
                </product>
            </function>
            <function name="aero/coefficient/CLde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.2000</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.1000
                              2.0000	0.0330
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0100</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-0.7000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmde">
                <description>Pitch_moment_due_to_elevator</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	-1.3000
                              2.0000	-0.3250
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-21.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-4.0000</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.1200</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnda">
                <description>Adverse_yaw</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.0000</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
