###################################
# Boeing 787-8 Dreamliner Authors #
###################################

Current maintainer: <PERSON> (radi)

> Omega95: <PERSON>ckpit, FDM, Nasal, Instruments, Ground, Database, Sounds, Systems
> Redneck: FDM, Nasal, Instruments, Database, Systems
> Jentron: FDM, Systems
> Scotth1: Terminal Procedures Parser
> Bicyus: Nasal Code Simplification and Organization
> Awexome: Nasal Code Simplification and Organization
> Hooray: Nasal, Instruments, Systems
> Zan: CameraView Instrument (still on the drawing board)
> Nicky: 787-8 Maintainer till Jan 26, 2012 (start of the re-devel project)
> Thorsten: WXRadar Storm Scripts
> Oliver T: Ground Service Vehicles Models, De-icing Animations
> Joshua W: Model, (original 787-8 developer - during FG v1.0)
> <PERSON><PERSON>: misc bug fixes
> <PERSON> (radi): Wingflexer, ALS lights+shadows

# Systems include dialogs, autopilot, vnav, holding, pushback and extras
