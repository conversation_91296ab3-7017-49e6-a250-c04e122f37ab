<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>

<fdm_config name="PC-7" version="2.0" release="ALPHA"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
  <author> Aeromatic v 3.3.0 </author>
  <filecreationdate> 06 Jun 2016 </filecreationdate>
  <version>$Revision: 1.18 $</version>
  <description> Models a PC-7. </description>
  <note>
    This model was created using publicly available data, publicly available
    technical reports, textbooks, and guesses. It contains no proprietary or
    restricted data. If this model has been validated at all, it would be
    only to the extent that it seems to "fly right", and that it possibly
    complies with published, publicly known, performance data (maximum speed,
    endurance, etc.). Thus, this model is meant for educational and
    entertainment purposes only.

    This simulation model is not endorsed by the manufacturer.
  </note>
 </fileheader>

<!--
  File:     PC-7.xml
  Inputs:
    name:          PC-7
    type:          WWII fighter, subsonic sport, aerobatic
    stall speed:   75.05kts
    max weight:    5953.50 lb
    length:        32.09 ft
    wing: 
     span:         34.12 ft
     area:         178.68 sq-ft
     mean chord:   5.25 ft
     aspect ratio: 6.52:1
     taper ratio:  0.47:1
     incidence:    0.00 degrees
     dihedral:     7.00 degrees
     sweep:        1.00 degrees

    no. engines:   1
    engine type:   Turboprop Engine
    engine layout: forward fuselage

    gear type:     tricycle
    steering type: steering
    retractable?:  yes

  Outputs:
    wing loading:  33.32 lb/sq-ft
    payload:       2294.52 lbs
    CL-alpha:      4.96 per radian
    CL-0:          0.39
    CL-max:        1.75
    CD-0:          0.02
    K:             0.06
    Mcrit:         0.75
-->

 <metrics>
   <wingarea  unit="FT2">   178.68 </wingarea>
   <wingspan  unit="FT" >    34.12 </wingspan>
   <wing_incidence>           0.00 </wing_incidence>
   <chord     unit="FT" >     5.25 </chord>
   <htailarea unit="FT2">    30.38 </htailarea>
   <htailarm  unit="FT" >    19.25 </htailarm>
   <vtailarea  unit="FT2">   17.87 </vtailarea>
   <vtailarm  unit="FT" >    19.25 </vtailarm>
   <location name="AERORP" unit="IN">
     <x>   154.02 </x>
     <y>     0.00 </y>
     <z>     0.00 </z>
   </location>
   <location name="EYEPOINT" unit="IN">
     <x>   107.81 </x>
     <y>     0.00 </y>
     <z>    40.00 </z>
   </location>
   <location name="VRP" unit="IN">
     <x>     0.0 </x>
     <y>     0.0 </y>
     <z>     0.0 </z>
   </location>
 </metrics>

 <mass_balance>
   <ixx unit="SLUG*FT2">   1932.45 </ixx>
   <iyy unit="SLUG*FT2">   3038.07 </iyy>
   <izz unit="SLUG*FT2">   4401.45 </izz>
   <emptywt unit="LBS" >   2932.65 </emptywt>
   <location name="CG" unit="IN">
     <x>   154.02 </x>
     <y>     0.00 </y>
     <z>    -9.63 </z>
   </location>
   <pointmass name="Payload">
    <description> 2294.52 LBS should bring model up to entered max weight </description>
    <weight unit="LBS"> 1147.26 </weight>
    <location name="POINTMASS" unit="IN">
     <x>   154.02 </x>
     <y>     0.00 </y>
     <z>    -9.63 </z>
   </location>
  </pointmass>
 </mass_balance>

 <propulsion>

   <engine file="PT6A">
    <feed> 0 </feed>

    <thruster file="Propeller">
     <sense> 1 </sense>
     <location unit="IN">
       <x>    36.00 </x>
       <y>     0.00 </y>
       <z>     0.00 </z>
     </location>
     <orient unit="DEG">
       <pitch>     0.00 </pitch>
	<roll>     0.00 </roll>
	 <yaw>     0.00 </yaw>
     </orient>
    </thruster>
  </engine>

  <tank type="FUEL" number="0">
     <location unit="IN">
       <x>   154.02 </x>
       <y>     0.00 </y>
       <z>    -9.63 </z>
     </location>
     <capacity unit="LBS"> 363.16 </capacity>
     <contents unit="LBS"> 181.58 </contents>
  </tank>
  <tank type="FUEL" number="1">
     <location unit="IN">
       <x>   154.02 </x>
       <y>     0.00 </y>
       <z>    -9.63 </z>
     </location>
     <capacity unit="LBS"> 363.16 </capacity>
     <contents unit="LBS"> 181.58 </contents>
  </tank>

 </propulsion>

 <ground_reactions>

  <contact type="BOGEY" name="NOSE">
    <location unit="IN">
      <x>    50.06 </x>
      <y>     0.00 </y>
      <z>   -46.20 </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <rolling_friction> 0.02 </rolling_friction>
    <spring_coeff  unit="LBS/FT">     1786.05 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 893.03 </damping_coeff>
    <max_steer unit="DEG"> 5.00 </max_steer>
    <brake_group> NONE </brake_group>
    <retractable> 1 </retractable>
  </contact>

  <contact type="BOGEY" name="LEFT_MAIN">
    <location unit="IN">
      <x>   160.18 </x>
      <y>   -61.42 </y>
      <z>   -46.20 </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <rolling_friction> 0.02 </rolling_friction>
    <spring_coeff  unit="LBS/FT">     5953.50 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 2976.75 </damping_coeff>
    <max_steer unit="DEG">0</max_steer>
    <brake_group> LEFT </brake_group>
    <retractable> 1 </retractable>
  </contact>

  <contact type="BOGEY" name="RIGHT_MAIN">
    <location unit="IN">
      <x>   160.18 </x>
      <y>    61.42 </y>
      <z>   -46.20 </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <rolling_friction> 0.02 </rolling_friction>
    <spring_coeff  unit="LBS/FT">     5953.50 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 2976.75 </damping_coeff>
    <max_steer unit="DEG">0</max_steer>
    <brake_group> RIGHT </brake_group>
    <retractable> 1 </retractable>
  </contact>

  <contact type="STRUCTURE" name="LEFT_WING">
    <location unit="IN">
     <x>   154.02 </x>
     <y>  -204.72 </y>
     <z>    -9.63 </z>
    </location>
   <static_friction>  1 </static_friction>
   <dynamic_friction> 1 </dynamic_friction>
   <spring_coeff unit="LBS/FT">      5953.50 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 5953.50 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="RIGHT_WING">
    <location unit="IN">
     <x>   154.02 </x>
     <y>   204.72 </y>
     <z>    -9.63 </z>
    </location>
   <static_friction>  1 </static_friction>
   <dynamic_friction> 1 </dynamic_friction>
   <spring_coeff unit="LBS/FT">      5953.50 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 5953.50 </damping_coeff>
  </contact>

 </ground_reactions>

 <system file="Conventional Controls.xml"/>
 <system file="Landing Gear.xml"/>
 <system file="Flaps.xml"/>

 <flight_control name="FCS: PC-7">
   <channel name="Landing Gear">
    <switch name="fcs/gear-no-wow">
      <default value="1"/>
      <test logic="AND" value="0">
          gear/unit[1]/WOW eq 1
          gear/unit[2]/WOW eq 1
      </test>
    </switch>
  </channel>
 </flight_control>

 <aerodynamics>

    <function name="aero/function/kCDge">
      <description>Change_in_drag_due_to_ground_effect</description>
      <table>
          <independentVar>aero/h_b-mac-ft</independentVar>
          <tableData>
              0.0000        0.4800
              0.1000        0.5150
              0.1500        0.6290
              0.2000        0.7090
              0.3000        0.8150
              0.4000        0.8820
              0.5000        0.9280
              0.6000        0.9620
              0.7000        0.9880
              0.8000        1.0000
              0.9000        1.0000
              1.0000        1.0000
              1.1000        1.0000
          </tableData>
      </table>
  </function>

  <function name="aero/function/kCLge">
      <description>Change_in_lift_due_to_ground_effect</description>
      <table>
          <independentVar>aero/h_b-mac-ft</independentVar>
          <tableData>
              0.0000        1.2030
              0.1000        1.1270
              0.1500        1.0900
              0.2000        1.0730
              0.3000        1.0460
              0.4000        1.0550
              0.5000        1.0190
              0.6000        1.0130
              0.7000        1.0080
              0.8000        1.0060
              0.9000        1.0030
              1.0000        1.0020
              1.1000        1.0000
          </tableData>
      </table>
  </function>

   <function name="aero/function/velocity-induced-fps">
    <description> velocity including the propulsion induced velocity.</description>
    <sum>
      <property>velocities/u-aero-fps</property>
      <property>propulsion/engine/prop-induced-velocity_fps</property>
      <property>propulsion/engine/prop-induced-velocity_fps</property>
    </sum>
  </function>

  <function name="aero/function/qbar-induced-psf">
    <description> q bar including the propulsion induced velocity.</description>
    <product>
      <property>aero/function/velocity-induced-fps</property>
      <property>aero/function/velocity-induced-fps</property>
      <property>atmosphere/rho-slugs_ft3</property>
      <value>0.5</value>
    </product>
  </function>

  <axis name="LIFT">

    <function name="aero/force/Lift_propwash">
      <description>Delta lift due to propeller induced velocity</description>
      <product>
         <property>propulsion/engine[0]/thrust-coefficient</property>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
            <tableData>
                     0.0     60.0
              -0.29  0.000   0.000
               0.00  0.087   0.232
               0.30  0.389   1.038
               0.59  0.000   0.000
            </tableData>
          </table>
      </product>
    </function>

    <function name="aero/force/Lift_alpha">
      <description>Lift due to alpha</description>
      <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>aero/function/kCLge</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>
              -0.20 -0.6018
               0.00  0.3900
               0.27  1.7471
               0.60  0.9328
            </tableData>
          </table>
      </product>
    </function>

    <function name="aero/force/Lift_pitch_rate">
        <description>Lift due to pitch rate</description>
        <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>velocities/q-aero-rad_sec</property>
          <property>aero/ci2vel</property>
          <value> 5.2675 </value>
        </product>
      </function>

      <function name="aero/force/Lift_alpha_rate">
        <description>Lift due to alpha rate</description>
        <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/alphadot-rad_sec</property>
           <property>aero/ci2vel</property>
           <value> 2.4730 </value>
        </product>
      </function>

    <function name="aero/force/Lift_elevator">
       <description>Lift due to Elevator Deflection</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/elevator-pos-rad</property>
           <value> 0.2903 </value>
       </product>
    </function>

    <function name="aero/force/Lift_flap">
       <description>Delta Lift due to flaps</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/flap-pos-deg</property>
           <value> 0.0092 </value>
       </product>
    </function>


  </axis>

  <axis name="DRAG">

    <function name="aero/force/Drag_basic">
       <description>Drag at zero lift</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>
             -1.57    1.4093
             -0.27    0.0749
              0.00    0.0197
              0.27    0.0749
              1.57    1.4093
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/force/Drag_induced">
       <description>Induced drag</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/cl-squared</property>
           <property>aero/function/kCDge</property>
           <value> 0.0522 </value>
         </product>
    </function>

    <function name="aero/force/Drag_mach">
       <description>Drag due to mach</description>
        <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">velocities/mach</independentVar>
            <tableData>
                0.00    0.0000
                0.75    0.0000
                1.10    0.0230
                1.80    0.0150
            </tableData>
          </table>
        </product>
    </function>

    <function name="aero/force/Drag_beta">
       <description>Drag due to sideslip</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/beta-rad</independentVar>
            <tableData>
              -1.57    1.2300
              -0.26    0.0500
               0.00    0.0000
               0.26    0.0500
               1.57    1.2300
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/force/Drag_elevator">
       <description>Drag due to Elevator Deflection</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <abs><property>fcs/elevator-pos-rad</property></abs>
           <value> 0.0400 </value>
       </product>
    </function>

    <function name="aero/force/Drag_gear">
       <description>Drag due to gear</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>gear/gear-pos-norm</property>
           <value> 0.0300 </value>
         </product>
    </function>

    <function name="aero/force/Drag_flap">
       <description>Drag due to flaps</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/flap-pos-deg</property>
           <value> 0.0012 </value>
         </product>
    </function>


  </axis>

  <axis name="SIDE">

    <function name="aero/force/Side_beta">
       <description>Side force due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/beta-rad</property>
           <value> -0.2310 </value>
       </product>
    </function>
    <function name="aero/force/Side_roll_rate">
       <description>Side force due to roll rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-aero-rad_sec</property>
           <table>
             <independentVar lookup="row">aero/Re</independentVar>
             <tableData>
                 4237447   0.2180
                 4661192   0.0191
                 6356170   0.0085
                 8404342   0.0049
             </tableData>
           </table>
       </product>
    </function>

    <function name="aero/force/Side_yaw_rate">
       <description>Side force due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value> 0.2606 </value>
       </product>
    </function>

    <function name="aero/force/Side_rudder">
       <description>Side force due to rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/rudder-pos-rad</property>
           <value> 0.1407 </value>
       </product>
    </function>

  </axis>

  <axis name="PITCH">

    <function name="aero/moment/Pitch_propwash">
      <description>Pitch moment due to propeller induced velocity</description>
      <product>
          <property>propulsion/engine[0]/thrust-coefficient</property>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/bw-ft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
            <tableData>
                     0.0     60.0
              -0.02  0.000   0.000
               0.00 -0.054  -0.144
               0.30 -0.243  -0.647
               0.39  0.000   0.000
            </tableData>
          </table>
      </product>
    </function>

    <function name="aero/moment/Pitch_alpha">
       <description>Pitch moment due to alpha</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/alpha-rad</property>
           <value> -1.4070 </value>
       </product>
    </function>

    <function name="aero/moment/Pitch_elevator">
       <description>Pitch moment due to elevator</description>
       <product>
          <property>aero/function/qbar-induced-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/cbarw-ft</property>
          <property>fcs/elevator-pos-rad</property>
          <table>
            <independentVar lookup="row">velocities/mach</independentVar>
            <tableData>
              0.0    -1.6414
              2.0    -0.4104
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/moment/Pitch_damp">
       <description>Pitch moment due to pitch rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>velocities/q-aero-rad_sec</property>
           <value> -19.3187 </value>
       </product>
    </function>

    <function name="aero/moment/Pitch_alphadot">
       <description>Pitch moment due to alpha rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>aero/alphadot-rad_sec</property>
           <value> -9.0698 </value>
       </product>
    </function>

  </axis>

  <axis name="ROLL">

    <function name="aero/moment/Roll_differential_propwash">
       <description>Roll moment due to differential propwash</description>
       <product>
           <property>propulsion/engine[0]/thrust-coefficient</property>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/alpha-rad</property>
           <value> 0.1607 </value>
       </product>
    </function>

    <function name="aero/moment/Roll_beta">
       <description>Roll moment due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <table>
             <independentVar lookup="row">aero/alpha-rad</independentVar>
             <independentVar lookup="column">aero/Re</independentVar>
             <tableData>
                          4237447  4661192  6356170  8404342
                 -0.0349  -0.5540  -0.1534  -0.1320  -0.1247
                  0.3491  -0.6227  -0.1610  -0.1399  -0.1332
             </tableData>
           </table>
       </product>
    </function>

    <function name="aero/moment/Roll_damp">
       <description>Roll moment due to roll rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-aero-rad_sec</property>
           <value> -0.4643 </value>
       </product>
    </function>

    <function name="aero/moment/Roll_yaw">
       <description>Roll moment due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <table>
             <independentVar lookup="row">aero/alpha-rad</independentVar>
             <independentVar lookup="column">aero/Re</independentVar>
             <tableData>
                          4237447  4661192  6356170  8404342
                 -0.0349   0.5608   0.0881   0.0629   0.0542
                  0.3491   4.8743   0.5642   0.5389   0.5302
             </tableData>
           </table>
       </product>
    </function>

    <function name="aero/moment/Roll_aileron">
       <description>Roll moment due to aileron</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/bw-ft</property>
          <property>fcs/left-aileron-pos-rad</property>
           <table>
            <independentVar lookup="row">velocities/mach</independentVar>
            <tableData>
              0.0    0.1800
              2.0    0.0450
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/moment/Roll_rudder">
       <description>Roll moment due to rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value> 0.0100 </value>
       </product>
    </function>

  </axis>

  <axis name="YAW">

    <!-- Stall initiator -->
    <function name="aero/moment/Yaw_alpha">
       <description>Yaw moment due to alpha</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/alpha-rad</property>
           <property>fcs/gear-no-wow</property>
           <table>
             <independentVar lookup="row">aero/beta-rad</independentVar>
             <independentVar lookup="column">aero/Re</independentVar>
             <tableData>
                          4237447  4261192
                 -0.3491  -1.0000   0.0000
                  0.3491   1.0000   0.0000
             </tableData>
           </table>
       </product>
    </function>

    <function name="aero/moment/Yaw_beta">
       <description>Yaw moment due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <value> 0.1303 </value>
       </product>
    </function>

    <function name="aero/moment/Yaw_roll_rate">
       <description>Yaw moment due to roll rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-rad_sec</property>
           <table>
             <independentVar lookup="row">aero/Re</independentVar>
             <tableData>
                 4237447  -2.1839
                 4661192  -0.1917
                 6356170  -0.0852
                 8404342  -0.0487
             </tableData>
           </table>
       </product>
    </function>

    <function name="aero/moment/Yaw_damp">
       <description>Yaw moment due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value> -0.1585 </value>
       </product>
    </function>

    <function name="aero/moment/Yaw_rudder">
       <description>Yaw moment due to rudder</description>
       <product>
           <property>aero/function/qbar-induced-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value> -0.0794 </value>
       </product>
    </function>

    <function name="aero/moment/Yaw_aileron">
       <description>Adverse yaw</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/left-aileron-pos-rad</property>
           <value> -0.0030 </value>
       </product>
    </function>

  </axis>

 </aerodynamics>

 <external_reactions>
 </external_reactions>

</fdm_config>
