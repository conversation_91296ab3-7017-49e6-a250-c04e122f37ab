<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="Fokker-100" version="2.0" release="ALPHA"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
  <author> <PERSON> </author>
  <filecreationdate> 2010-08-08 </filecreationdate>
  <version>$Revision: 1.3 $</version>
  <description> Fokker-100 commercial airliner </description>
  <reference title="Butterworth-Heinemann - Civil Jet Aircraft Design (BH)
        http://www.elsevierdirect.com/companions/9780340741528/appendices/data-a/table-8/table.htm"/>
  <reference title="Lissys - PIANO aircraft analysis software
        http://www.lissys.demon.co.uk/f7tx2.html"/>
  <reference title="Aerodynamic Design of Transport Aircraft
        http://www.scribd.com/doc/23956816/Aerodynamic-Design-of-Transport-Aircraft"/>
 </fileheader>

<!--
  File:     xf100.xml
  Inputs:
    name:          NG-Aircraft XF-100 (Fokker 100NG)
    type:          two-engine transonic transport
    max weight:    95013.45 lb
    wing span:     92.13048 ft
    length:        106.6325 ft
    wing area:     1025.9045 sq-ft
    gear type:     tricycle
    retractable?:  yes
    # engines:     2
    engine type:   turbine
    engine layout: aft fuselage
    yaw damper?    yes

  Outputs:
    wing loading:  83.35 lb/sq-ft
    CL-alpha:      4.4 per radian
    CL-0:          0.2
    CL-max:        1.72
    CD-0:          0.02
    K:             0.043

-->

 <metrics>
   <wingarea  unit="FT2"> 1025.90 </wingarea>
   <wingspan  unit="FT" >   92.13 </wingspan>
   <wing_incidence>          2.00 </wing_incidence>
   <chord     unit="FT" >   12.4671 </chord>
   <htailarea unit="FT2">  233.792 </htailarea>
   <htailarm  unit="FT" >   52.4934 </htailarm>
   <vtailarea unit="FT2">  132.396 </vtailarea>
   <vtailarm  unit="FT" >   44.6194 </vtailarm>
   <location name="AERORP" unit="IN">
     <x> 699.48 </x> <!-- 668.48 -->
     <y>   0.00 </y>
     <z>  38.00 </z>
   </location>
   <location name="EYEPOINT" unit="IN">
     <x> 122.04 </x>
     <y> -30.00 </y>
     <z>  88.94 </z>
   </location>
   <location name="VRP" unit="IN">
     <x> 101.65 </x>
     <y>   0.0  </y>
     <z>   0.0  </z>
   </location>
 </metrics>

 <mass_balance>
   <ixx unit="SLUG*FT2">    218368 </ixx>
   <iyy unit="SLUG*FT2">    675849 </iyy>
   <izz unit="SLUG*FT2">    860261 </izz>
   <emptywt unit="LBS" >     51307 </emptywt>
   <location name="CG" unit="IN">
     <x> 719.81 </x> <!-- 693.81 -->
     <y>   0.00 </y>
     <z>  38.12 </z>
   </location>
   <pointmass name="pilot">
     <weight unit="LBS"> 230 </weight>
     <location name="POINTMASS" unit="IN">
       <x> 122.04 </x>
       <y> -30.0 </y>
       <z>  66.0 </z>
     </location>
   </pointmass>
   <pointmass name="copilot">
     <weight unit="LBS"> 230 </weight>
     <location name="POINTMASS" unit="IN">
       <x> 122.04 </x>
       <y>  30.0 </y>
       <z>  66.0 </z>
     </location>
   </pointmass>
   <pointmass name="steward">
     <weight unit="LBS"> 400 </weight>
     <location name="POINTMASS" unit="IN">
       <x>  218 </x>
       <y>  32.0 </y>
       <z>  66.0 </z>
     </location>
   </pointmass>
 </mass_balance>

 <ground_reactions>

  <contact type="BOGEY" name="NOSE">
   <location unit="IN">
     <x> 172.87 </x>
     <y>   0.00 </y>
     <z> -55.73 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">       7750.03 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">  1600.34 </damping_coeff>
   <max_steer unit="DEG"> 5.00 </max_steer>
   <brake_group>NONE</brake_group>
   <retractable>1</retractable>
 </contact>

  <contact type="BOGEY" name="LEFT_MAIN">
   <location unit="IN">
     <x> 731.93 </x>
     <y>-101.65 </y>
     <z> -63.53 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">      95013.45 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 19002.69 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <brake_group>LEFT</brake_group>
   <retractable>1</retractable>
 </contact>

  <contact type="BOGEY" name="RIGHT_MAIN">
   <location unit="IN">
     <x> 731.93 </x>
     <y> 101.65 </y>
     <z> -63.53 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">      95013.45 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 19002.69 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <brake_group>RIGHT</brake_group>
   <retractable>1</retractable>
 </contact>

  <contact type="STRUCTURE" name="LEFT_WING">
    <location unit="IN">
     <x> 753.77 </x>
     <y>-1105.4 </y>
     <z>   0.0 </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <spring_coeff unit="LBS/FT">      95013.45 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 19002.69 </damping_coeff>
 </contact>

  <contact type="STRUCTURE" name="RIGHT_WING">
    <location unit="IN">
     <x> 753.77 </x>
     <y> 1105.4 </y>
     <z>   0.0  </z>
    </location>
    <static_friction>  0.80 </static_friction>
    <dynamic_friction> 0.50 </dynamic_friction>
    <spring_coeff unit="LBS/FT">      95013.45 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 19002.69 </damping_coeff>
 </contact>

 </ground_reactions>

 <external_reactions>
    <force name="pushback" frame="BODY">
       <location unit="IN">
          <x>-139</x>
          <y>0</y>
          <z>-71</z>
       </location>
       <direction>
          <x>1</x>
          <y>0</y>
          <z>0</z>
       </direction>
    </force>
 </external_reactions>

 <propulsion>

   <engine file="BR-725">
    <feed>0</feed>
    <thruster file="direct">
     <location unit="IN">
       <x>  922.52 </x>
       <y> -101.65 </y>
       <z>   43.53 </z>
     </location>
     <orient unit="DEG">
       <pitch> 0.00 </pitch>
       <roll>  0.00 </roll>
       <yaw>   0.00 </yaw>
     </orient>
    </thruster>
  </engine>

   <engine file="BR-725">
    <feed>1</feed>
    <thruster file="direct">
     <location unit="IN">
       <x>  922.52 </x>
       <y>  101.65 </y>
       <z>   43.53 </z>
     </location>
     <orient unit="DEG">
       <pitch> 0.00 </pitch>
       <roll>  0.00 </roll>
       <yaw>   0.00 </yaw>
     </orient>
    </thruster>
  </engine>

  <tank type="FUEL" number="0">
     <location unit="IN">
       <x> 713.81 </x>
       <y>-190.59 </y>
       <z>   0.0  </z>
     </location>
     <capacity unit="LBS"> 13360.01 </capacity>
     <contents unit="LBS"> 688.50 </contents>
  </tank>

  <tank type="FUEL" number="1">
     <location unit="IN">
       <x> 713.81 </x>
       <y> 190.59 </y>
       <z>   0.0  </z>
     </location>
     <capacity unit="LBS"> 13360.01 </capacity>
     <contents unit="LBS"> 688.50 </contents>
  </tank>
  <tank type="FUEL" number="2">
     <location unit="IN">
       <x> 703.77 </x>
       <y>   0.00 </y>
       <z> -31.99 </z>
     </location>
     <capacity unit="LBS"> 1377.01 </capacity>
     <contents unit="LBS"> 688.50 </contents>
  </tank>
 </propulsion>

 <system file="pushback"/>

 <flight_control name="FCS: Fokker-100">

  <channel name="Pitch">
   <summer name="fcs/pitch-trim-sum">
      <input>fcs/elevator-cmd-norm</input>
      <input>fcs/pitch-trim-cmd-norm</input>
      <clipto>
        <min> -1 </min>
        <max>  1 </max>
      </clipto>
   </summer>

   <aerosurface_scale name="fcs/elevator-control">
      <input>fcs/pitch-trim-sum</input>
      <range>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </range>
      <output>fcs/elevator-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="fcs/elevator-normalization">
      <input>fcs/elevator-pos-rad</input>
      <domain>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </domain>
      <range>
        <min> -1 </min>
        <max>  1 </max>
      </range>
      <output>fcs/elevator-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <channel name="Roll">
   <summer name="fcs/roll-trim-sum">
      <input>fcs/aileron-cmd-norm</input>
      <input>fcs/roll-trim-cmd-norm</input>
      <clipto>
        <min> -1 </min>
        <max>  1 </max>
      </clipto>
   </summer>

   <aerosurface_scale name="fcs/left-aileron-control">
      <input>fcs/roll-trim-sum</input>
      <range>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </range>
      <output>fcs/left-aileron-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="fcs/right-aileron-control">
      <input>fcs/roll-trim-sum</input>
      <range>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </range>
      <output>fcs/right-aileron-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="fcs/left-aileron-normalization">
      <input>fcs/left-aileron-pos-rad</input>
      <domain>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </domain>
      <range>
        <min> -1 </min>
        <max>  1 </max>
      </range>
      <output>fcs/left-aileron-pos-norm</output>
   </aerosurface_scale>

   <aerosurface_scale name="fcs/right-aileron-normalization">
      <input>fcs/right-aileron-pos-rad</input>
      <domain>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </domain>
      <range>
        <min> -1 </min>
        <max>  1 </max>
      </range>
      <output>fcs/right-aileron-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <channel name="Yaw">
   <summer name="fcs/rudder-command-sum">
      <input>fcs/rudder-cmd-norm</input>
      <input>fcs/yaw-trim-cmd-norm</input>
      <clipto>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </clipto>
   </summer>

   <scheduled_gain name="fcs/yaw-damper-rate">
      <input>velocities/r-aero-rad_sec</input>
      <table>
        <independentVar lookup="row">velocities/ve-kts</independentVar>
         <tableData>
            30     0.00
            60     2.00
         </tableData>
      </table>
   </scheduled_gain>

   <scheduled_gain name="fcs/yaw-damper-beta">
      <input>aero/beta-rad</input>
      <table>
        <independentVar lookup="row">velocities/ve-kts</independentVar>
        <tableData>
           30     0.00
           60     0.00
        </tableData>
      </table>
   </scheduled_gain>

   <summer name="fcs/yaw-damper-sum">
      <input>fcs/yaw-damper-beta</input>
      <input>fcs/yaw-damper-rate</input>
      <clipto>
        <min> -0.1 </min>
        <max>  0.1 </max>
      </clipto>
   </summer>

   <scheduled_gain name="fcs/yaw-damper-final">
      <input>fcs/yaw-damper-sum</input>
      <table>
        <independentVar lookup="row">velocities/ve-kts</independentVar>
        <tableData>
           30         0.0
           31         1.0
        </tableData>
      </table>
   </scheduled_gain>

   <summer name="fcs/rudder-sum">
      <input>fcs/rudder-command-sum</input>
      <input>fcs/yaw-damper-final</input>
      <clipto>
        <min> -1 </min>
        <max>  1 </max>
      </clipto>
   </summer>

   <aerosurface_scale name="fcs/rudder-control">
      <input>fcs/rudder-sum</input>
      <range>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </range>
      <output>fcs/rudder-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="rcs/rudder-normalization">
      <input>fcs/rudder-pos-rad</input>
      <domain>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </domain>
      <range>
        <min> -1 </min>
        <max>  1 </max>
      </range>
      <output>fcs/rudder-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <channel name="Flaps">
   <kinematic name="fcs/flaps-control">
     <input>fcs/flap-cmd-norm</input>
     <traverse>
       <setting>
         <position>0</position>
         <time>0</time>
       </setting>
       <setting>
         <position>8</position>
         <time>3</time>
       </setting>
       <setting>
         <position>15</position>
         <time>3</time>
       </setting>
       <setting>
         <position>25</position>
         <time>4</time>
       </setting>
       <setting>
         <position>42</position>
         <time>4</time>
       </setting>
     </traverse>
     <output>fcs/flap-pos-deg</output>
   </kinematic>

   <aerosurface_scale name="fcs/flap-normalization">
      <input>fcs/flap-pos-deg</input>
      <domain>
        <min>0</min>
        <max>42</max>
      </domain>
      <range>
        <min>0</min>
        <max>1</max>
      </range>
      <output>fcs/flap-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <channel name="Landing Gear">
   <kinematic name="fcs/gear-control">
     <input>gear/gear-cmd-norm</input>
     <traverse>
       <setting>
          <position> 0 </position>
          <time>     0 </time>
       </setting>
       <setting>
          <position> 1 </position>
          <time>     5 </time>
       </setting>
     </traverse>
     <output>gear/gear-pos-norm</output>
   </kinematic>

  </channel>

  <channel name="Speedbrake">
   <kinematic name="fcs/speedbrake-control">
     <input>fcs/speedbrake-cmd-norm</input>
     <traverse>
       <setting>
          <position> 0 </position>
          <time>     0 </time>
       </setting>
       <setting>
          <position> 1 </position>
          <time>     1 </time>
       </setting>
     </traverse>
     <output>fcs/speedbrake-pos-norm</output>
   </kinematic>
  </channel>


  <channel name="Spoilers">
   <kinematic name="fcs/spoiler-control">
     <input>fcs/spoiler-cmd-norm</input>
     <traverse>
        <setting>
          <position>0</position>
          <time>0</time>
        </setting>
        <setting>
          <position>1</position>
          <time>1</time>
        </setting>
     </traverse>
     <output>fcs/spoiler-pos-norm</output>
   </kinematic>
  </channel>
 </flight_control>

    <aerodynamics>

        <function name="aero/function/kCDge">
            <description>Change_in_drag_due_to_ground_effect</description>
            <product>
                  <table>
                      <independentVar>aero/h_b-mac-ft</independentVar>
                      <tableData>
                          0.0000	0.0350
                          0.1000	0.4170
                          0.1500	0.5390
                          0.2000	0.6240
                          0.3000	0.7510
                          0.4000	0.8000
                          0.5000	0.8960
                          0.6000	0.9390
                          0.7000	0.9740
                          0.8000	0.9810
                          0.9000	0.9900
                          1.0000	0.9940
                          1.1000	1.0000
                      </tableData>
                  </table>
            </product>
        </function>

        <function name="aero/function/kCLge">
            <description>Change_in_lift_due_to_ground_effect</description>
            <product>
                  <table>
                      <independentVar>aero/h_b-mac-ft</independentVar>
                      <tableData>
                          0.0000	1.3910
                          0.1000	1.2350
                          0.1500	1.1670
                          0.2000	1.1350
                          0.3000	1.0850
                          0.4000	1.1020
                          0.5000	1.0350
                          0.6000	1.0240
                          0.7000	1.0150
                          0.8000	1.0110
                          0.9000	1.0060
                          1.0000	1.0040
                          1.1000	1.0000
                      </tableData>
                  </table>
            </product>
        </function>

        <axis name="DRAG">
            <function name="aero/coefficient/CD0">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCDge</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -1.5700	1.5000
                              -0.2600	0.0392
                              0.0000	0.01884
                              0.2600	0.0392
                              1.5700	1.4700
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <property>aero/function/kCDge</property>
                    <value>0.00717</value>
                </product>
            </function>
            <function name="aero/coefficient/CDflap">
                <description>Drag_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-norm</property>
                    <property>aero/function/kCDge</property>
                    <value>0.0830</value>
                </product>
            </function>
            <function name="aero/coefficient/CDmach">
                <description>Drag_due_to_mach</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.0000
                              0.6500	0.0000
                              0.7700	0.0015
                              0.8600	0.0200
                              1.1000	0.0390
                              1.8000	0.0260
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDgear">
                <description>Drag_due_to_gear</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>gear/gear-pos-norm</property>
                    <value>0.0120</value>
                </product>
            </function>
            <function name="aero/coefficient/CDsb">
                <description>Drag_due_to_speedbrakes</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.1200</value>
                </product>
            </function>
            <function name="aero/coefficient/CDsp">
                <description>Drag_due_to_spoilers</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/spoiler-pos-norm</property>
                    <value>0.0280</value>
                </product>
            </function>
            <function name="aero/coefficient/CDrv">
                <description>Drag_due_to_reverse</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>propulsion/engine[0]/reverser-angle-rad</property>
                    <property>propulsion/engine[1]/reverser-angle-rad</property>
                    <value>0.0187</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                              -1.5700	1.2300
                              -0.2600	0.0500
                              0.0000	0.0000
                              0.2600	0.0500
                              1.5700	1.2300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.0590</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-1.0000</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.20 -0.680
                               0.00  0.221
                               0.23  1.300
                               0.60  0.612
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/dCLflap">
                <description>Delta_Lift_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                      <table>
                          <independentVar>fcs/flap-pos-norm</independentVar>
                          <tableData>
                              0.0000	0.0000
                              0.3570	0.4900
                              1.0000	1.1800
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/dCLsp">
                <description>Delta_Lift_due_to_spoiler</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/spoiler-pos-norm</property>
                    <property>aero/function/kCLge</property>
                    <value>-0.1200</value>
                </product>
            </function>
            <function name="aero/coefficient/CLde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.2000</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.1000
                              2.0000	0.0330
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0100</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/function/kCmge">
                <description>Change_in_lift_due_to_ground_effect</description>
                <product>
                      <table>
                          <independentVar>aero/h_b-mac-ft</independentVar>
                          <tableData>
                              0.0000	1.2030
                              0.1000	1.1270
                              0.1500	1.0900
                              0.2000	1.0730
                              0.3000	1.0460
                              0.4000	1.0550
                              0.5000	1.0190
                              0.6000	1.0130
                              0.7000	1.0080
                              0.8000	1.0060
                              0.9000	1.0030
                              1.0000	1.0020
                              1.1000	1.0000
                          </tableData>
                      </table>
                </product>
            </function>

            <function name="aero/coefficient/Cmo">
                <description>Pitching_moment_at_zero_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/function/kCmge</property>
                    <value>-0.000737</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-0.6000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmde">
                <description>Pitch_moment_due_to_elevator</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	-1.2000
                              2.0000	-0.3000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-31.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-16.0000</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.1200</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnda">
                <description>Adverse_yaw</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.0000</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
