<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="wrightFlyer1903" version="2.0" release="ALPHA"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
  <author> <PERSON>, <PERSON> </author>
  <filecreationdate>2015-09-19</filecreationdate>
  <version>$Revision: 1.1 $</version>
  <description> Models the 1903 Wright Flyer. </description>
 </fileheader>

<!--
  Wright Flyer data:
  (http://www.wright-brothers.org/Information_Desk/Help_with_Homework/Help_with_Homework_Intro/Flyer_Specifications_11x17.pdf)
  +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
  Dimensions: 
    40.ft. 4 in (12.29 m) overall width 
    21 ft. (6.40 m) overall length 
    8 ft. 1 in. (2.46 m) height over wings 
    8 ft. 4 in. (2.54 m) height over sweep of propellers
    6 ft. 6 in. (1.98 m) wing chord 
    1:20 wing camber 
    3.25 degrees angle of incidence
    10 in. (0.25 m) wing anhedral (droop) 

    6.2 ft (189 cm) (wing) separation
    510 sq ft (47.4 sq. m) wing area
    48 sq ft (4.6 sq m) double horizontal front rudder
    21 sq ft (1.9 sq m) twin movable vertical rear rudders

  Weights: 
    605 lbs. (274.42 kg) Total weight without pilot 
    16 lbs. (7.26 kg) fluids (water, gas, oil) 
    145 lbs. (65.77 kg) average weight of pilots 

  Engine: 
    4-cycle gasoline, 4 cylinders 
    4 in. bore x 4 in. stroke (10.16 cm x 10.16 cm) 
    Aluminum-copper alloy crankcase 
    12 hp at 1020 rpm 
    152 lbs (68.95 kg) weight of engine 
    18 lbs (8.16 kg) weight of magneto 

  Wing Loading: 
    1.47 lbs. per sq. ft. (7.18 kg per m2)
    62.5 lbs. (28.35 kg) per engine horsepower 

  Propellers 
    Twin contra-rotating propellers 
    Pusher configuration 
    Driven by roller chain, 1-in. (2.54 cm) pitch 
    8-tooth sprockets on crankshaft 
    23-tooth sprockets on propeller shafts 
    2-7/8:1 Engine to propeller rpm ratio 
    980 rpm approx. engine speed in flight 
    340 rpm approx. propeller speed in fligh

  Sources:
  * https://www.liv.ac.uk/media/livacuk/flightscience/projects/fs/fshistory/wright/publications/FHQ1905_v2.pdf

  * http://www.wright-brothers.org/Information_Desk/Just_the_Facts/Airplanes/Flyer_I.htm

  * http://www.wrightsfly.info/Page_27.html

  * UIUC FDM Data:
     - Culick, F.E.C. and Jex, H.R., "Aerodynamics, Stability and
       Control of the 1903 Wright Flyer," Proceedings of The Wright
       Flyer: An Engineering Perspective, National Air and Space Museum,
       Smithsonian Institution, 1985.

     - Jex, H.R. and Culick, F.E.C., "Flight Control Dynamics of the
       1903 Wright Flyer," 12th AIAA Atmospheric Flight Mechanics
       Conference, AIAA Paper 85-1804-CP, 1985.

     - Jex, H, Grimm, R., Latz, J.P., and Hange, C., "Full-Scale 1903
       Wright Flyer Wind Tunnel Test Results From the NASA Ames Research
       Center", AIAA 38th Aerospace Sciences Meeting, AIAA Paper
       2000-0512, 2000.

     - Various other sources including 
       http://www.wrightflyer.org/Papers/papers.html
       http://www.aae.uiuc.edu/m-selig/uiuc_lsat.html (low Re data)


  Descriptions:
    UIUC - University of Illinois Urbana-Champaign windtunnel data.
    Flyer - Flyer specifications from the wright-brothers.org website.

    **************************************************************
    Unless otherwise specified the data is generated by Aeromatic2 
    **************************************************************
-->

 <metrics>
   <wingarea  unit="FT2">  503.00 </wingarea>		<!-- Flyer III -->
   <wingspan  unit="FT" >   40.50 </wingspan>		<!-- Flyer III -->
   <wing_incidence>          3.25 </wing_incidence>	<!-- Flyer -->
   <chord     unit="FT" >    6.50 </chord>		<!-- Flyer III -->
   <htailarea unit="FT2">   83.00 </htailarea>		<!-- Flyer III -->
   <htailarm  unit="FT" >   12.00 </htailarm>		<!-- Flyer III -->
   <vtailarea unit="FT2">   34.80 </vtailarea>		<!-- Flyer III -->
   <vtailarm  unit="FT" >    6.50 </vtailarm>		<!-- UIUC -->
   <location name="AERORP" unit="IN">
     <x>  -4.3 </x>
     <y>   0.0 </y>
     <z> -17.0 </z>
   </location>
   <location name="EYEPOINT" unit="IN">			<!-- Model -->
     <x> -30.5 </x>
     <y>  18.1 </y>
     <z> -14.2 </z>
   </location>
   <location name="VRP" unit="IN">
     <x>   0.0 </x>
     <y>   0.0 </y>
     <z>   0.0 </z>
   </location>
 </metrics>

 <mass_balance>
   <ixx unit="SLUG*FT2">      1378 </ixx>		<!-- UIUC -->
   <iyy unit="SLUG*FT2">       271 </iyy>		<!-- UIUC -->
   <izz unit="SLUG*FT2">      1343 </izz>		<!-- UIUC -->
   <emptywt unit="LBS" >       565 </emptywt>		<!-- Flyer -->
   <location name="CG" unit="IN">			<!-- Flyer III -->
     <x>   0.0 </x>
     <y>   0.0 </y>
     <z> -23.0 </z>
   </location>
   <pointmass name="Pilot">
    <description> 145 lbs pilot </description>
    <weight unit="LBS">    145.0 </weight>		<!-- Model -->
    <location name="POINTMASS" unit="IN">
      <x>  -2.3 </x>
      <y>  18.1 </y>
      <z> -30.9 </z>
    </location>
  </pointmass> 
  <pointmass name="Engine">
    <description> 152 lbs engine + 18 lbs magneto </description>
    <weight unit="LBS">    170.0 </weight>		<!-- Model -->
    <location name="POINTMASS" unit="IN">
      <x>  -4.9 </x>
      <y> -19.5 </y>
      <z> -27.6 </z>
    </location>
  </pointmass>
  <pointmass name="Canard_mass">
  <description> 70 lbs to move C.G. forward </description>
    <weight unit="LBS">    70.0 </weight>
    <location name="POINTMASS" unit="IN">
      <x> -120.2 </x>
      <y>    0.0 </y>
      <z>  -55.9 </z>
    </location>
  </pointmass>

 </mass_balance>

 <ground_reactions>

  <contact type="BOGEY" name="LEFT_FRONT_MAIN">
   <location unit="IN">
     <x> -25.0 </x>
     <y> -23.0 </y>
     <z> -60.0 </z>
   </location>
   <static_friction>  0.35 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.002 </rolling_friction>
   <spring_coeff unit="LBS/FT">        600.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   300.00 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <retractable>1</retractable>
  </contact>

  <contact type="BOGEY" name="RIGHT_FRONT_MAIN">
   <location unit="IN">
     <x> -25.0 </x>
     <y>  23.0 </y>
     <z> -60.0 </z>
   </location>
   <static_friction>  0.35 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.002 </rolling_friction>
   <spring_coeff unit="LBS/FT">        600.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   300.00 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <retractable>1</retractable>
  </contact>

  <contact type="BOGEY" name="LEFT_REAR_MAIN">
   <location unit="IN">
     <x>  -5.0 </x>
     <y> -23.0 </y>
     <z> -60.0 </z>
   </location>
   <static_friction>  0.35 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.002 </rolling_friction>
   <spring_coeff unit="LBS/FT">        600.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   300.00 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <retractable>1</retractable>
  </contact>

  <contact type="BOGEY" name="RIGHT_REAR_MAIN">
   <location unit="IN">
     <x>  -5.0 </x>
     <y>  23.0 </y>
     <z> -60.0 </z>
   </location>
   <static_friction>  0.35 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.002 </rolling_friction>
   <spring_coeff unit="LBS/FT">        600.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   300.00 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <retractable>1</retractable>
  </contact>

  <contact type="STRUCTURE" name="LEFT_SKID">		<!-- Model -->
    <location unit="IN">
      <x> -15.0 </x>
      <y> -23.0 </y>
      <z> -57.6 </z>
    </location>
    <static_friction>  0.800 </static_friction>
    <dynamic_friction> 0.500 </dynamic_friction>
    <spring_coeff  unit="LBS/FT">       600.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC">   300.00 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="RIGHT_SKID">		<!-- Model -->
    <location unit="IN">
     <x> -15.0 </x>
     <y>  23.0 </y>
     <z> -57.6 </z>
   </location>
   <static_friction>  0.800 </static_friction>
   <dynamic_friction> 0.500 </dynamic_friction>
   <spring_coeff unit="LBS/FT">        600.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   300.00 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="NOSE">		<!-- Model -->
    <location unit="IN">
     <x> -129.2 </x>
     <y>    0.0 </y>
     <z>  -55.9 </z>
   </location>
   <static_friction>  0.800 </static_friction>
   <dynamic_friction> 0.500 </dynamic_friction>
   <spring_coeff unit="LBS/FT">        600.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   300.00 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="TAIL">		<!-- Model -->
    <location unit="IN">
     <x>  117.1 </x>
     <y>    0.0 </y>
     <z>  -51.8 </z>
   </location>
   <static_friction>  0.800 </static_friction>
   <dynamic_friction> 0.500 </dynamic_friction>
   <spring_coeff unit="LBS/FT">        600.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   300.00 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="LEFT_UPPER_WING">	<!-- Model -->
    <location unit="IN">
     <x>   -4.3 </x>
     <y>  245.8 </y>
     <z>   39.0 </z>
   </location>
   <static_friction>  1.00 </static_friction>
   <dynamic_friction> 1.00 </dynamic_friction>
   <spring_coeff unit="LBS/FT">        800.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   400.00 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="RIGHT_UPPER_WING">	<!-- Model -->
    <location unit="IN">
     <x>   -4.3 </x>
     <y> -245.8 </y>
     <z>   39.0 </z>
   </location>
   <static_friction>  1.00 </static_friction>
   <dynamic_friction> 1.00 </dynamic_friction>
   <spring_coeff unit="LBS/FT">        800.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   400.00 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="LEFT_LOWER_WING">	<!-- Model -->
    <location unit="IN">
     <x>   -4.3 </x>
     <y>  245.8 </y>
     <z>  -39.0 </z>
   </location>
   <static_friction>  1.00 </static_friction>
   <dynamic_friction> 1.00 </dynamic_friction>
   <spring_coeff unit="LBS/FT">        800.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   400.00 </damping_coeff>
  </contact>

  <contact type="STRUCTURE" name="RIGHT_LOWER_WING">	<!-- Model -->
    <location unit="IN">
     <x>   -4.3 </x>
     <y> -245.8 </y>
     <z>  -39.0 </z>
   </location>
   <static_friction>  1.00 </static_friction>
   <dynamic_friction> 1.00 </dynamic_friction>
   <spring_coeff unit="LBS/FT">        800.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC">   400.00 </damping_coeff>
  </contact>

 </ground_reactions>

 <propulsion>

   <engine file="wright1905_engine">
    <feed>0</feed>
    <thruster file="wright1903_propellers">
     <sense> 1 </sense>
     <location unit="IN">
       <x>  64.35 </x>
       <y>   0.00 </y>
       <z>   9.10 </z>
     </location>
     <orient unit="DEG">
       <pitch> 0.00 </pitch>
       <roll>  0.00 </roll>
       <yaw>   0.00 </yaw>
     </orient>
    </thruster>
  </engine>

  <tank type="FUEL" number="0">
     <location unit="IN">
       <x> -26.1 </x>
       <y>  37.2 </y>
       <z>  20.2 </z>
     </location>
     <capacity unit="LBS">  22.00 </capacity>		<!-- Flyer III -->
     <contents unit="LBS">  20.00 </contents>
  </tank>

 </propulsion>

 <flight_control name="FCS: unnamed">

  <channel name="Pitch">

   <aerosurface_scale name="Elevator Control">
      <input>fcs/elevator-cmd-norm</input>
      <range>
        <min> -0.35 </min>			<!-- UIUC -->
        <max>  0.35 </max>			<!-- UIUC -->
      </range>
      <output>fcs/elevator-pos-rad</output>
   </aerosurface_scale>

  </channel>

  <channel name="Roll">

   <aerosurface_scale name="Left Aileron Control">
      <input>fcs/aileron-cmd-norm</input>
      <range>
        <min> -0.148 </min>			<!-- UIUC -->
        <max>  0.148 </max>			<!-- UIUC -->
      </range>
      <output>fcs/left-aileron-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="Right Aileron Control">
      <input>-fcs/aileron-cmd-norm</input>
      <range>
        <min> -0.148 </min>			<!-- UIUC -->
        <max>  0.148 </max>			<!-- UIUC -->
      </range>
      <output>fcs/right-aileron-pos-rad</output>
   </aerosurface_scale>

  </channel>

  <channel name="Yaw">

   <aerosurface_scale name="Rudder Control">
      <input>fcs/rudder-cmd-norm</input>
      <range>
        <min> -0.74 </min>			<!-- UIUC -->
        <max>  0.74 </max>			<!-- UIUC -->
      </range>
      <output>fcs/rudder-pos-rad</output>
   </aerosurface_scale>

  </channel>

  <channel name="Flaps">
  </channel>

  <channel name="Landing Gear">
  </channel>

  <channel name="Speedbrake">
  </channel>

 </flight_control>

 <aerodynamics>

  <function name="aero/function/kCDge">
    <description>Change_in_drag_due_to_ground_effect</description>
    <product>
          <table>
            <independentVar>aero/h_b-mac-ft</independentVar>
            <tableData>
                0.0000	0.0350
                0.1000	0.4170
                0.1500	0.5390
                0.2000	0.6240
                0.3000	0.7510
                0.4000	0.8000
                0.5000	0.8960
                0.6000	0.9390
                0.7000	0.9740
                0.8000	0.9810
                0.9000	0.9900
                1.0000	0.9940
                1.1000	1.0000
            </tableData>
          </table>
    </product>
  </function> 

  <function name="aero/function/kCLge">
    <description>Change_in_lift_due_to_ground_effect</description>
    <product>
          <table>
            <independentVar>aero/h_b-mac-ft</independentVar>
            <tableData>
                0.0000	1.3910
                0.1000	1.2350
                0.1500	1.1670
                0.2000	1.1350
                0.3000	1.0850
                0.4000	1.1020
                0.5000	1.0350
                0.6000	1.0240
                0.7000	1.0150
                0.8000	1.0110
                0.9000	1.0060
                1.0000	1.0040
                1.1000	1.0000
            </tableData>
          </table>
    </product>
  </function>

  <axis name="LIFT">

    <function name="aero/force/Lift_alpha">
      <description>Lift due to alpha and elevator deflection</description>
      <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">fcs/elevator-pos-rad</independentVar>
            <tableData>			<!-- UIUC: CLfade.dat -->
               		-0.080		-0.002		0.089
                -1.571	0.0		0.0		0.0
                -0.672	-0.61639	-0.61639	-0.61639
                -0.612	-0.65079	-0.65079	-0.65079
                -0.502	-0.67564	-0.67564	-0.67564
                -0.377	-0.70041	-0.70041	-0.70041
                -0.340	-0.73031	-0.73031	-0.73031
                -0.329	-0.75803	-0.75803	-0.75803
                -0.317	-0.76491	-0.76491	-0.76491
                -0.298	-0.74396	-0.74396	-0.74396
                -0.273	-0.67202	-0.67202	-0.67202
                -0.120	-0.089846	-0.089846	-0.089846
                -0.102	0.028372	0.028372	0.028372
                -0.085	0.20911		0.22067		0.23455
                -0.072	0.27635		0.29254		0.34349
                -0.065	0.31941		0.33634		0.38992
                -0.026	0.47699		0.51083		0.54184
                0.014	0.6402		0.66557		0.69661
                0.052	0.78932		0.8147		0.8288
                0.091	0.92714		0.93562		0.95252
                0.129	1.0932		1.0932		1.0932
                0.148	1.1669		1.1669		1.1669
                0.157	1.1948		1.1948		1.1948
                0.170	1.1948		1.1948		1.1948
                0.179	1.1764		1.1764		1.1764
                0.193	1.1093		1.1093		1.1093
                0.214	1.026		1.026		1.026
                0.229	1.0053		1.0053		1.0053
                0.262	0.99156		0.99156		0.99156
                0.420	0.94846		0.94846		0.94846
                0.537	0.87501		0.87501		0.87501
                0.633	0.77134		0.77134		0.77134
                1.571	0.0		0.0		0.0
            </tableData>
          </table>
      </product>
    </function>

  </axis>

  <axis name="DRAG">

    <function name="aero/force/Drag_basic">
       <description>Drag at zero lift</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>			<!-- UIUC: CDfa.dat -->
                -1.571  1.1
                -0.722	0.75232
                -0.307	0.45656
                -0.227	0.39696
                -0.185	0.35873
                -0.158	0.30025
                -0.131	0.20354
                -0.109	0.13832
                -0.091	0.1147
                -0.065	0.10368
                -0.033	0.10708
                0.008	0.11898
                0.043	0.13711
                0.088	0.16317
                0.117	0.1915
                0.136	0.22041
                0.159	0.26314
                0.182	0.316
                0.207	0.36323
                0.254	0.41721
                0.304	0.45994
                0.658	0.6826
                1.571   1.1
            </tableData>
          </table>
       </product>
    </function>

  </axis>

  <axis name="SIDE">

    <function name="aero/force/Side_beta">
       <description>Side force due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/beta-rad</property>
           <value>-0.321</value>		<!-- UIUC -->
       </product>
    </function>

    <function name="aero/coefficient/Side_aileron">
       <description>Side force due to aileron</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/right-aileron-pos-rad</property>
           <value>-0.636</value>		<!-- UIUC -->
       </product>
   </function>

   <function name="aero/coefficient/Side_rudder">
       <description>Side force due to rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/rudder-pos-rad</property>
           <value>0.241</value>			<!-- UIUC -->
       </product>
   </function>

   <function name="aero/coefficient/Side_yaw_rate">
       <description>Side force due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value>0.3</value>			<!-- UIUC -->
       </product>
   </function>

  </axis>

  <axis name="ROLL">

    <function name="aero/moment/Roll_beta">
       <description>Roll moment due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <value>0.0212</value>		<!-- UIUC -->
       </product>
    </function>

    <function name="aero/moment/Roll_aileron">
       <description>Roll moment due to aileron</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/bw-ft</property>
          <property>fcs/right-aileron-pos-rad</property>
          <value>-0.1317</value>		<!-- UIUC -->
       </product>
    </function>

    <function name="aero/moment/Roll_rudder">
       <description>Roll moment due to rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value>0.007</value>			<!-- UIUC -->
       </product>
    </function>

    <function name="aero/moment/Roll_damp">
       <description>Roll moment due to roll rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-aero-rad_sec</property>
           <value>-0.5</value>			<!-- UIUC -->
       </product>
    </function>

    <function name="aero/moment/Roll_yaw">
       <description>Roll moment due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value>0.0</value>			<!-- UIUC -->
       </product>
    </function>

  </axis>

  <axis name="PITCH">

    <function name="aero/moment/Pitch_alpha">
       <description>Pitch moment due to alpha</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <table>				<!-- UIUC: Cmfa2.dat (*2.5) -->
               <independentVar lookup="row">aero/alpha-rad</independentVar>
               <tableData>
                  -1.571	0.425
                  -0.61		0.468925
                  -0.114	-0.15011
                  -0.096	-0.204495
                  -0.084	-0.2943
                  -0.063	-0.3296
                  0.182		-0.3288
                  0.205		-0.35865
                  0.222		-0.445725
                  0.249		-0.49735
                  0.618		-0.85275
                  1.571		-0.75
               </tableData>
           </table>
       </product>
    </function>

    <function name="aero/moment/Pitch_elevator">
       <description>Pitch moment due to elevator</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/cbarw-ft</property>
          <table>			<!-- UIUC: Cmfade6.dat (*2.5) -->
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <independentVar lookup="column">fcs/elevator-cmd-norm</independentVar>
            <independentVar lookup="table">fcs/elevator-pos-rad</independentVar>
            <tableData  breakPoint="-0.524">
                         -1.0		1.0
                -1.338	-0.225		-0.225
                -0.978	-0.34845	-0.34845
                -0.808	-0.3318		-0.3318
                -0.627	-0.279825	-0.279825
                -0.572	-0.222025	-0.222025
                -0.532	-0.1363		-0.1363
                -0.499	-0.088475	-0.088475
                -0.48	-0.096175	-0.096175
                -0.458	-0.106425	-0.106425
                -0.44	-0.083775	-0.083775
                -0.395	0.03225		0.03225
                -0.343	0.168475	0.168475
                -0.306	0.25925		0.25925
                -0.267	0.339925	0.339925
                -0.232	0.385225	0.385225
                -0.193	0.41025		0.41025
                -0.172	0.410125	0.410125
                -0.15	0.3923		0.3923
                -0.129	0.36675		0.36675
                -0.113	0.3738		0.3738
                -0.094	0.3661		0.3661
                0.148	0.349425	0.349425
                0.376	0.3202		0.3202
                0.756	0.275		0.275
            </tableData>
            <tableData  breakPoint="-0.436">
                         -1.0		1.0
                -1.28	-0.225		-0.225
                -0.92	-0.34845	-0.34845
                -0.75	-0.3318		-0.3318
                -0.569	-0.279825	-0.279825
                -0.514	-0.222025	-0.222025
                -0.474	-0.1363		-0.1363
                -0.441	-0.088475	-0.088475
                -0.422	-0.096175	-0.096175
                -0.4	-0.106425	-0.106425
                -0.382	-0.083775	-0.083775
                -0.337	0.03225		0.03225
                -0.285	0.168475	0.168475
                -0.248	0.25925		0.25925
                -0.208	0.339925	0.339925
                -0.174	0.385225	0.385225
                -0.134	0.41025		0.41025
                -0.114	0.410125	0.410125
                -0.092	0.3923		0.3923
                -0.071	0.36675		0.36675
                -0.055	0.3738		0.3738
                -0.035	0.3661		0.3661
                0.206	0.349425	0.349425
                0.434	0.3202		0.3202
            </tableData>
            <tableData  breakPoint="-0.349">
                         -1.0		1.0
                -1.222	-0.225		-0.225
                -0.862	-0.34845	-0.34845
                -0.692	-0.3318		-0.3318
                -0.51	-0.279825	-0.279825
                -0.456	-0.222025	-0.222025
                -0.416	-0.1363		-0.1363
                -0.383	-0.088475	-0.088475
                -0.364	-0.096175	-0.096175
                -0.342	-0.106425	-0.106425
                -0.324	-0.083775	-0.083775
                -0.279	0.03225		0.03225
                -0.227	0.168475	0.168475
                -0.19	0.25925		0.25925
                -0.15	0.339925	0.339925
                -0.116	0.385225	0.385225
                -0.076	0.41025		0.41025
                -0.056	0.410125	0.410125
                -0.034	0.3923		0.3923
                -0.013	0.36675		0.36675
                0.003	0.3738		0.3738
                0.023	0.3661		0.3661
                0.264	0.349425	0.349425
                0.492	0.3202		0.3202

            </tableData>
            <tableData  breakPoint="-0.262">
                         -1.0		1.0
                -1.164	-0.225		-0.225
                -0.804	-0.34845	-0.34845
                -0.633	-0.3318		-0.3318
                -0.452	-0.279825	-0.279825
                -0.397	-0.222025	-0.222025
                -0.358	-0.1363		-0.1363
                -0.325	-0.088475	-0.088475
                -0.306	-0.096175	-0.096175
                -0.284	-0.106425	-0.106425
                -0.266	-0.083775	-0.083775
                -0.221	0.03225		0.03225
                -0.169	0.168475	0.168475
                -0.132	0.25925		0.25925
                -0.092	0.339925	0.339925
                -0.058	0.385225	0.385225
                -0.018	0.41025		0.41025
                0.003	0.410125	0.410125
                0.025	0.3923		0.3923
                0.045	0.36675		0.36675
                0.062	0.3738		0.3738
                0.081	0.3661		0.3661
                0.323	0.349425	0.349425
                0.55	0.3202		0.3202
            </tableData>
            <tableData  breakPoint="-0.175">
                         -1.0		1.0
                -1.105	-0.225		-0.225
                -0.745	-0.34845	-0.34845
                -0.575	-0.3318		-0.3318
                -0.394	-0.279825	-0.279825
                -0.339	-0.222025	-0.222025
                -0.3	-0.1363		-0.1363
                -0.267	-0.088475	-0.088475
                -0.247	-0.096175	-0.096175
                -0.226	-0.106425	-0.106425
                -0.208	-0.083775	-0.083775
                -0.163	0.03225		0.03225
                -0.111	0.168475	0.168475
                -0.074	0.25925		0.25925
                -0.034	0.339925	0.339925
                0	0.385225	0.385225
                0.04	0.41025		0.41025
                0.061	0.410125	0.410125
                0.083	0.3923		0.3923
                0.103	0.36675		0.36675
                0.12	0.3738		0.3738
                0.139	0.3661		0.3661
                0.381	0.349425	0.349425
                0.609	0.3202		0.3202
            </tableData>
            <tableData  breakPoint="-0.087">
                         -1.0		1.0
                -1.047	-0.225		-0.225
                -0.687	-0.34845	-0.34845
                -0.517	-0.3318		-0.3318
                -0.336	-0.279825	-0.279825
                -0.281	-0.222025	-0.222025
                -0.241	-0.1363		-0.1363
                -0.209	-0.088475	-0.088475
                -0.189	-0.096175	-0.096175
                -0.167	-0.106425	-0.106425
                -0.15	-0.083775	-0.083775
                -0.104	0.03225		0.03225
                -0.052	0.168475	0.168475
                -0.015	0.25925		0.25925
                0.024	0.339925	0.339925
                0.059	0.385225	0.385225
                0.098	0.41025		0.41025
                0.119	0.410125	0.410125
                0.141	0.3923		0.3923
                0.162	0.36675		0.36675
                0.178	0.3738		0.3738
                0.197	0.3661		0.3661
                0.439	0.349425	0.349425
                0.667	0.3202		0.3202
            </tableData>
            <tableData  breakPoint="0">	
                         -1.0		1.0
               -0.989	-0.225		-0.225
               -0.629	-0.34845	-0.34845
               -0.459	-0.3318		-0.3318
               -0.278	-0.279825	-0.279825
               -0.223	-0.222025	-0.222025
               -0.183	-0.1363		-0.1363
               -0.15	-0.088475	-0.088475
               -0.131	-0.096175	-0.096175
               -0.109	-0.106425	-0.106425
               -0.091	-0.083775	-0.083775
               -0.046	0.03225		0.03225
               0.006	0.168475	0.168475
               0.043	0.25925		0.25925
               0.082	0.339925	0.339925
               0.117	0.385225	0.385225
               0.156	0.41025		0.41025
               0.177	0.410125	0.410125
               0.199	0.3923		0.3923
               0.22	0.36675		0.36675
               0.236	0.3738		0.3738
               0.255	0.3661		0.3661
               0.497	0.349425	0.349425
               0.725	0.3202		0.3202
            </tableData>
            <tableData  breakPoint="0.087">
                         -1.0		1.0
               -0.931	-0.225		-0.225
               -0.571	-0.34845	-0.34845
               -0.401	-0.3318		-0.3318
               -0.22	-0.279825	-0.279825
               -0.165	-0.222025	-0.222025
               -0.125	-0.1363		-0.1363
               -0.092	-0.088475	-0.088475
               -0.073	-0.096175	-0.096175
               -0.051	-0.106425	-0.106425
               -0.033	-0.083775	-0.083775
               0.012	0.03225		0.03225
               0.064	0.168475	0.168475
               0.101	0.25925		0.25925
               0.141	0.339925	0.339925
               0.175	0.385225	0.385225
               0.215	0.41025		0.41025
               0.235	0.410125	0.410125
               0.257	0.3923		0.3923
               0.278	0.36675		0.36675
               0.294	0.3738		0.3738
               0.314	0.3661		0.3661
               0.555	0.349425	0.349425
               0.783	0.3202		0.3202
            </tableData>
            <tableData  breakPoint="0.175">
                         -1.0		1.0
               -0.873	-0.225		-0.225
               -0.513	-0.34845	-0.34845
               -0.343	-0.3318		-0.3318
               -0.161	-0.279825	-0.279825
               -0.107	-0.222025	-0.222025
               -0.067	-0.1363		-0.1363
               -0.034	-0.088475	-0.088475
               -0.015	-0.096175	-0.096175
               0.007	-0.106425	-0.106425
               0.025	-0.083775	-0.083775
               0.07	0.03225		0.03225
               0.122	0.168475	0.168475
               0.159	0.25925		0.25925
               0.199	0.339925	0.339925
               0.233	0.385225	0.385225
               0.273	0.41025		0.41025
               0.293	0.410125	0.410125
               0.315	0.3923		0.3923
               0.336	0.36675		0.36675
               0.353	0.3738		0.3738
               0.372	0.3661		0.3661
               0.613	0.349425	0.349425
               0.841	0.3202		0.3202
            </tableData>
            <tableData  breakPoint="0.262">
                         -1.0		1.0
               -0.814	-0.225		-0.225
               -0.455	-0.34845	-0.34845
               -0.284	-0.3318		-0.3318
               -0.103	-0.279825	-0.279825
               -0.048	-0.222025	-0.222025
               -0.009	-0.1363		-0.1363
               0.024	-0.088475	-0.088475
               0.043	-0.096175	-0.096175
               0.065	-0.106425	-0.106425
               0.083	-0.083775	-0.083775
               0.128	0.03225		0.03225
               0.18	0.168475	0.168475
               0.217	0.25925		0.25925
               0.257	0.339925	0.339925
               0.291	0.385225	0.385225
               0.331	0.41025		0.41025
               0.352	0.410125	0.410125
               0.374	0.3923		0.3923
               0.394	0.36675		0.36675
               0.411	0.3738		0.3738
               0.43	0.3661		0.3661
               0.672	0.349425	0.349425
               0.899	0.3202		0.3202
            </tableData>
            <tableData  breakPoint="0.349">
                         -1.0		1.0
               -0.756	-0.225		-0.225
               -0.396	-0.34845	-0.34845
               -0.226	-0.3318		-0.3318
               -0.045	-0.279825	-0.279825
               0.01	-0.222025	-0.222025
               0.049	-0.1363		-0.1363
               0.082	-0.088475	-0.088475
               0.102	-0.096175	-0.096175
               0.124	-0.106425	-0.106425
               0.141	-0.083775	-0.083775
               0.187	0.03225		0.03225
               0.238	0.168475	0.168475
               0.275	0.25925		0.25925
               0.315	0.339925	0.339925
               0.349	0.385225	0.385225
               0.389	0.41025		0.41025
               0.41	0.410125	0.410125
               0.432	0.3923		0.3923
               0.452	0.36675		0.36675
               0.469	0.3738		0.3738
               0.488	0.3661		0.3661
               0.73	0.349425	0.349425
               0.958	0.3202		0.3202
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/moment/Pitch_damp">
       <description>Pitch moment due to pitch rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>velocities/q-aero-rad_sec</property>
           <value>-3.5</value>			<!-- UIUC -->
       </product>
    </function>

  </axis>

  <axis name="YAW">

    <function name="aero/moment/Yaw_beta">
       <description>Yaw moment due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <value>0.0335</value>		<!-- UIUC -->
       </product>
    </function>

    <function name="aero/moment/Yaw_rudder">
       <description>Yaw moment due to rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value>-0.039</value>		<!-- UIUC -->
       </product>
    </function>

    <function name="aero/moment/Yaw_aileron">
       <description>Adverse yaw</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/right-aileron-pos-rad</property>
           <value>0.024</value>			<!-- UIUC -->
       </product>
    </function>

    <function name="aero/coefficient/Yaw_roll_rate">
       <description>Yaw moment due to roll rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-aero-rad_sec</property>
           <value>-0.06</value>			<!-- UIUC -->
       </product>
   </function>

    <function name="aero/moment/Yaw_damp">
       <description>Yaw moment due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value>-0.15</value>			<!-- UIUC -->
       </product>
    </function>

  </axis>

 </aerodynamics>

 <external_reactions>
 </external_reactions>

</fdm_config>

