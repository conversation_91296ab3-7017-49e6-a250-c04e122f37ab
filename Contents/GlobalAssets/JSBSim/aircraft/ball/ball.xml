<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="BALL" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
      <author> <PERSON> </author>
      <filecreationdate> 2004-06-01 </filecreationdate>
      <version> Version 1.0 </version>
      <description> Test file </description>
      <note>
        This model of a ball was created using publicly available data, publicly available
        technical reports, textbooks, testing, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be only to the
        extent that it seems to fly, bounce, roll, or be retrieved by a dog, in a plausible
        way, and that it possibly complies with published, publicly known, performance data
        (maximum bounce, endurance, etc.). Thus, this model is meant for educational and
        entertainment purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 1 </wingarea>
        <wingspan unit="FT"> 1 </wingspan>
        <chord unit="FT"> 1 </chord>
        <htailarea unit="FT2"> 0 </htailarea>
        <htailarm unit="FT"> 0 </htailarm>
        <vtailarea unit="FT2"> 0 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance>
        <ixx unit="SLUG*FT2"> 20 </ixx>
        <iyy unit="SLUG*FT2"> 10 </iyy>
        <izz unit="SLUG*FT2"> 10 </izz>
        <ixy unit="SLUG*FT2"> -0 </ixy>
        <ixz unit="SLUG*FT2"> -0 </ixz>
        <iyz unit="SLUG*FT2"> -0 </iyz>
        <emptywt unit="LBS"> 20000 </emptywt>
        <location name="CG" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <pointmass name="M0">
            <weight unit="LBS"> 10 </weight>
            <location name="POINTMASS" unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
        </pointmass>
        <pointmass name="M1">
            <weight unit="LBS"> 0 </weight>
            <location name="POINTMASS" unit="FT">
                <x> 0 </x>
                <y> -5 </y>
                <z> 0 </z>
            </location>
        </pointmass>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="CONTACT">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0 </static_friction>
            <dynamic_friction> 0 </dynamic_friction>
            <rolling_friction> 0 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 200000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
    </ground_reactions>

    <external_reactions>
      <!-- "Declare" the reefing term -->
      <property>fcs/parachute_reef_pos_norm</property>
      <property>propulsion/rocket_thrust</property>
      
      <force name="parachute" frame="WIND">
        <function>
          <product>
            <property>aero/qbar-psf</property>
            <property>fcs/parachute_reef_pos_norm</property>
            <value> 1.0 </value>  <!-- Full drag coefficient -->
            <value> 10000.0 </value> <!-- Full parachute area -->
          </product>
        </function>
        <!-- The location below is in structural frame (x positive
             aft), so this location describes a point 1 foot aft
             of the origin. In this case, the origin is the center. -->
        <location unit="FT">
          <x>1</x>
          <y>0</y>
          <z>0</z>
        </location>
        <!-- The direction describes a unit vector. In this case, since
             the selected frame is the WIND frame, the "-1" x component
             describes a direction exactly opposite of the direction
             into the WIND vector. That is, the direction specified below
             is the direction that the drag force acts in. -->
        <direction>
          <x> -1 </x>
          <y>  0 </y>
          <z>  0 </z>
        </direction>
      </force>

      <force name="rocket" frame="INERTIAL">
        <function>
          <property> propulsion/rocket_thrust </property>
        </function>
        <location unit="FT">
          <x>0</x>
          <y>0</y>
          <z>0</z>
        </location>
        <!-- The direction that the thrust acts in is defined below in the Pointing
             channel of the flight_control section, through the creation of a unit 
             pointing vector. That is, the direction specified below
             is the direction that the drag force acts in. -->
      </force>

    </external_reactions>

    <propulsion/>

    <flight_control name="FGFCS">
    
      <channel name="Pointing">
      
        <fcs_function name="propulsion/tvc_inertial_x">
          <function>
            <quotient>
              <property>  velocities/eci-x-fps            </property>
              <property>  velocities/eci-velocity-mag-fps </property>
            </quotient>
          </function>
          <output> external_reactions/rocket/x </output>
        </fcs_function>
      
        <fcs_function name="propulsion/tvc_inertial_y">
          <function>
            <quotient>
              <property>  velocities/eci-y-fps            </property>
              <property>  velocities/eci-velocity-mag-fps </property>
            </quotient>
          </function>
          <output> external_reactions/rocket/y </output>
        </fcs_function>

        <fcs_function name="propulsion/tvc_inertial_z">
          <function>
            <quotient>
              <property>  velocities/eci-z-fps            </property>
              <property>  velocities/eci-velocity-mag-fps </property>
            </quotient>
          </function>
          <output> external_reactions/rocket/z </output>
        </fcs_function>

      </channel>
    
    </flight_control>

    <aerodynamics>
        <axis name="DRAG">
            <function name="aero/coefficient/CD">
                <description>Drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <value>0.000</value>
                </product>
            </function>
        </axis>
    </aerodynamics>

    <output name="BallOut.csv" type="CSV" rate="1">
        <rates> ON </rates>
        <velocities> ON </velocities>
        <forces> ON </forces>
        <moments> ON </moments>
        <position> ON </position>
        <massprops> ON </massprops>
        <property> position/vrp-gc-latitude_deg </property>
        <property> position/vrp-longitude_deg </property>
        <property> position/vrp-radius-ft </property>

    </output>
</fdm_config>
