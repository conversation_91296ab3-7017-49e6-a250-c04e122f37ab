<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="MK-82" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
        <author> Unknown </author>
        <filecreationdate> 2001-01-01 </filecreationdate>
        <version> $Revision: 1.13 $ </version>
        <description> Mk-82 </description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Or, it could be a gross approximation, with the only
        similarity to an actual object being the name. Thus, this model is meant
        for educational and entertainment purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 2.54 </wingarea>
        <wingspan unit="FT"> 0.9 </wingspan>
        <chord unit="FT"> 0.9 </chord>
        <htailarea unit="FT2"> 0 </htailarea>
        <htailarm unit="FT"> 0 </htailarm>
        <vtailarea unit="FT2"> 0 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 33.08 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 31 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance>
        <ixx unit="SLUG*FT2"> 0.66 </ixx>
        <iyy unit="SLUG*FT2"> 633 </iyy>
        <izz unit="SLUG*FT2"> 633 </izz>
        <emptywt unit="LBS"> 500 </emptywt>
        <location name="CG" unit="IN">
            <x> 31 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </mass_balance>

    <ground_reactions>
        <contact type="STRUCTURE" name="NOSE_CONTACT">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0 </static_friction>
            <dynamic_friction> 0 </dynamic_friction>
            <rolling_friction> 0 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 200000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="STRUCTURE" name="TAIL_CONTACT">
            <location unit="IN">
                <x> 66 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0 </static_friction>
            <dynamic_friction> 0 </dynamic_friction>
            <rolling_friction> 0 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 200000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
    </ground_reactions>
    <propulsion>
    </propulsion>
    <flight_control name="FCS: MK-82 FCS">
    </flight_control>
    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CDmin">
                <description>Drag_minimum</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.2000	0.1400	
                              0.4000	0.1390	
                              0.8000	0.1440	
                              0.9000	0.1490	
                              0.9500	0.2110	
                              0.9800	0.2520	
                              1.0000	0.2810	
                              1.0200	0.3050	
                              1.0500	0.3270	
                              1.1000	0.3590	
                              1.2000	0.3730	
                              1.4000	0.4180	
                              1.6000	0.2750	
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Drag_induced</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">velocities/mach</independentVar>
                          <tableData>
                              	0.2000	0.4000	0.8000	0.9000	0.9500	0.9800	1.0000	1.0200	1.0500	1.1000	1.2000	1.4000	1.6000	
                              0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	
                              0.1750	0.1560	0.1500	0.1070	0.1050	0.1030	0.1020	0.1020	0.1010	0.0990	0.0980	0.1110	0.1600	0.1190	
                              0.3490	0.6730	0.5730	0.4500	0.4510	0.4440	0.4430	0.4450	0.4430	0.4450	0.4470	0.6230	0.8560	0.7290	
                              0.5240	1.4760	1.3320	1.1990	1.4870	1.5370	1.6100	1.6740	1.7090	1.7460	1.7740	1.9140	2.3430	1.9960	
                              0.6980	2.1210	2.0970	2.8570	2.9140	2.9880	3.1060	3.2080	3.2780	3.3460	3.3770	3.5290	4.2260	3.5760	
                              0.8730	2.5020	2.7550	4.5630	4.5610	4.6780	4.9210	5.1210	5.2740	5.4180	5.4270	5.5120	6.2080	5.3600	
                              1.0470	2.9490	3.3400	6.2000	6.2250	6.3620	6.6550	6.8950	7.0660	7.2180	7.2770	7.4390	8.0250	7.1240	
                              1.2220	3.2720	4.0200	7.6130	7.5170	7.6200	8.0050	8.3230	8.5590	8.7720	8.8950	9.0890	9.4860	8.6470	
                              1.3960	3.4140	4.4490	8.5890	8.3450	8.5060	8.9530	9.3140	9.5900	9.8810	10.0510	10.1820	10.4330	9.7400	
                              1.5530	3.3830	4.5250	8.9570	8.6620	8.8520	9.3210	9.6990	10.0120	10.3210	10.5040	10.5930	10.7020	10.1650	
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">velocities/mach</independentVar>
                          <tableData>
                              	0.2000	0.4000	0.8000	0.9000	0.9500	0.9800	1.0000	1.0200	1.0500	1.1000	1.2000	1.4000	1.6000	
                              0.0000	-4.5720	-4.7780	-2.8390	-2.7830	-2.7820	-2.7820	-2.8020	-2.7880	-2.7260	-2.6240	-2.1270	-3.7510	-1.8680	
                              0.1750	-3.4650	-3.4620	-2.5390	-2.5700	-2.5920	-2.6220	-2.6620	-2.6740	-2.6870	-2.6990	-3.2400	-5.0870	-3.5300	
                              0.3490	-4.0330	-3.1760	-2.3060	-2.4370	-2.4890	-2.5570	-2.6210	-2.6490	-2.7270	-2.8330	-4.4100	-6.4600	-5.2600	
                              0.5240	-3.7880	-3.2320	-2.9470	-4.1930	-4.5100	-4.8660	-5.1520	-5.3400	-5.5510	-5.7630	-6.4010	-7.7820	-6.4780	
                              0.6980	-3.1380	-3.0860	-4.9400	-5.1690	-5.4310	-5.7500	-6.0080	-6.2040	-6.4040	-6.5570	-6.9630	-7.8090	-6.5080	
                              0.8730	-2.9750	-3.2000	-5.2580	-5.3000	-5.5000	-5.8190	-6.0770	-6.2740	-6.4640	-6.5150	-6.6640	-7.2910	-6.2470	
                              1.0470	-2.7910	-3.0190	-4.9470	-4.9880	-5.1250	-5.3530	-5.5420	-5.6700	-5.7860	-5.8440	-6.0050	-6.4240	-5.6280	
                              1.2220	-2.5860	-2.8680	-4.3030	-4.2850	-4.3540	-4.5250	-4.6730	-4.7700	-4.8570	-4.9100	-5.0550	-5.3130	-4.7050	
                              1.3960	-2.3880	-2.5850	-3.4120	-3.3890	-3.4360	-3.5320	-3.6200	-3.6690	-3.7200	-3.7470	-3.8640	-4.0610	-3.5780	
                              1.5530	-2.2370	-2.2840	-2.4930	-2.5160	-2.5300	-2.5510	-2.5830	-2.5840	-2.5840	-2.5790	-2.7030	-2.9020	-2.4700	
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLwbh">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/alpha-rad</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">velocities/mach</independentVar>
                          <tableData>
                              	0.2000	0.4000	0.8000	0.9000	0.9500	0.9800	1.0000	1.0200	1.0500	1.1000	1.2000	1.4000	1.6000	
                              0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	
                              0.1750	0.8920	0.8530	0.6050	0.5960	0.5830	0.5770	0.5780	0.5720	0.5640	0.5510	0.6290	0.0000	0.0000	
                              0.3490	1.8910	1.5980	1.2390	1.2460	1.2240	1.2220	1.2310	1.2240	1.2300	1.2340	1.7500	0.9180	0.6530	
                              0.5240	2.6800	2.3890	2.1310	2.7150	2.8200	2.9670	3.0950	3.1670	3.2450	3.3060	3.5860	2.3710	1.9530	
                              0.6980	2.7130	2.6710	3.8650	3.9660	4.0880	4.2740	4.4320	4.5460	4.6580	4.7190	4.9500	4.0820	3.3810	
                              0.8730	2.2200	2.4250	4.1130	4.1320	4.2580	4.4950	4.6880	4.8340	4.9760	5.0020	5.1060	5.0580	4.1590	
                              1.0470	1.7770	1.9990	3.7420	3.7690	3.8700	4.0620	4.2180	4.3260	4.4280	4.4760	4.5890	5.2690	4.4650	
                              1.2220	1.2300	1.5000	2.8490	2.8190	2.8740	3.0280	3.1540	3.2430	3.3290	3.3840	3.4650	4.7080	4.1210	
                              1.3960	0.6120	0.7940	1.5350	1.4940	1.5310	1.6170	1.6850	1.7320	1.7870	1.8230	1.8500	3.5210	3.1700	
                              1.5530	0.0470	0.0670	0.1450	0.1400	0.1440	0.1520	0.1590	0.1600	0.1650	0.1690	0.1720	1.8770	1.7320	
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clbeta">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">velocities/mach</independentVar>
                          <tableData>
                              	0.2000	0.4000	0.8000	0.9000	0.9500	0.9800	1.0000	1.0200	1.0500	1.1000	1.2000	1.4000	1.6000	
                              0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	0.0000	
                              0.1750	0.1500	0.0890	0.0050	0.0110	0.0140	0.0160	0.0170	0.0170	0.0180	0.0190	0.0210	0.0370	0.0400	
                              0.3490	-0.0550	-0.0550	-0.0370	-0.0290	-0.0270	-0.0240	-0.0220	-0.0200	-0.0160	-0.0100	0.0020	0.0350	0.0420	
                              0.5240	-0.0280	-0.0200	0.0110	0.0160	0.0180	0.0190	0.0200	0.0210	0.0220	0.0180	0.0260	0.0500	0.0740	
                              0.6980	0.2960	0.2760	0.2300	0.2210	0.2130	0.2090	0.2080	0.2040	0.1980	0.1880	0.1790	0.1560	0.1660	
                              0.8730	0.3000	0.2920	0.2750	0.2730	0.2700	0.2690	0.2700	0.2680	0.2660	0.2610	0.2660	0.2690	0.2570	
                              1.0470	0.2960	0.2980	0.3070	0.3110	0.3100	0.3110	0.3130	0.3120	0.3110	0.3080	0.3210	0.3390	0.3230	
                              1.2220	0.3100	0.3120	0.3240	0.3290	0.3290	0.3300	0.3330	0.3320	0.3300	0.3280	0.3430	0.3650	0.3520	
                              1.3960	0.3160	0.3170	0.3300	0.3360	0.3370	0.3380	0.3410	0.3400	0.3390	0.3380	0.3540	0.3780	0.3660	
                              1.5530	0.3120	0.3130	0.3270	0.3320	0.3330	0.3340	0.3380	0.3370	0.3360	0.3340	0.3500	0.3740	0.3630	
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">velocities/mach</independentVar>
                          <tableData>
                              	0.2000	0.4000	0.8000	0.9000	0.9500	0.9800	1.0000	1.0200	1.0500	1.1000	1.2000	1.4000	1.6000	
                              0.0000	-1.3720	-1.7830	-1.3250	-0.2610	-0.2670	-0.0530	-0.2280	-0.1110	0.3980	-0.0010	-1.6930	-3.6220	9.0240	
                              0.1750	-3.6990	-3.7670	-2.3840	-1.2630	-1.1680	-0.9210	-1.0390	-0.8730	-0.3300	-0.5070	-1.5380	-2.5500	8.7340	
                              0.3490	-6.2840	-6.2120	-4.5940	-3.4430	-3.2580	-2.9880	-3.0420	-2.8720	-2.3760	-2.4010	-3.1630	-3.7190	5.6670	
                              0.5240	-6.6830	-6.6450	-5.4900	-4.8850	-4.7620	-4.5880	-4.6300	-4.5470	-4.2660	-4.3030	-5.0530	-5.7670	0.4580	
                              0.6980	-1.3220	-1.7460	-2.7300	-3.1210	-3.2820	-3.3450	-3.4360	-3.5030	-3.5630	-3.7900	-4.5880	-6.0190	-3.9490	
                              0.8730	0.4270	-0.0950	-2.1340	-2.9000	-3.1370	-3.3200	-3.3910	-3.5370	-3.8170	-3.9870	-4.4190	-5.6210	-7.8500	
                              1.0470	-3.0160	-3.0290	-4.1670	-4.9730	-5.0950	-5.2690	-5.2630	-5.4040	-5.7450	-5.6550	-5.4110	-5.5440	-11.3800	
                              1.2220	-4.2180	-4.1060	-5.2920	-6.2680	-6.3560	-6.5490	-6.5100	-6.6640	-7.0630	-6.8850	-6.3960	-6.1490	-14.1200	
                              1.3960	-4.5480	-4.4880	-5.7950	-6.8090	-6.8780	-7.0910	-7.0530	-7.2180	-7.6500	-7.4550	-6.9020	-6.5500	-15.1500	
                              1.5530	-4.5480	-4.5460	-5.8850	-6.8540	-6.9290	-7.1500	-7.1170	-7.2760	-7.7250	-7.5390	-6.9700	-6.6060	-15.1600	
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-50.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-3.4600</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnbeta">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">velocities/mach</independentVar>
                          <tableData>
                              	0.2000	0.4000	0.8000	0.9000	0.9500	0.9800	1.0000	1.0200	1.0500	1.1000	1.2000	1.4000	1.6000	
                              0.0000	1.3720	1.7830	1.3250	0.2610	0.2670	0.0530	0.2280	0.1110	-0.3980	0.0010	1.6930	3.6220	-9.0240	
                              0.1750	3.6990	3.7670	2.3840	1.2630	1.1680	0.9210	1.0390	0.8730	0.3300	0.5070	1.5380	2.5500	-8.7340	
                              0.3490	6.2840	6.2120	4.5940	3.4430	3.2580	2.9880	3.0420	2.8720	2.3760	2.4010	3.1630	3.7190	-5.6670	
                              0.5240	6.6830	6.6450	5.4900	4.8850	4.7620	4.5880	4.6300	4.5470	4.2660	4.3030	5.0530	5.7670	-0.4580	
                              0.6980	1.3220	1.7460	2.7300	3.1210	3.2820	3.3450	3.4360	3.5030	3.5630	3.7900	4.5880	6.0190	3.9490	
                              0.8730	-0.4270	0.0950	2.1340	2.9000	3.1370	3.3200	3.3910	3.5370	3.8170	3.9870	4.4190	5.6210	7.8500	
                              1.0470	3.0160	3.0290	4.1670	4.9730	5.0950	5.2690	5.2630	5.4040	5.7450	5.6550	5.4110	5.5440	11.3800	
                              1.2220	4.2180	4.1060	5.2920	6.2680	6.3560	6.5490	6.5100	6.6640	7.0630	6.8850	6.3960	6.1490	14.1200	
                              1.3960	4.5480	4.4880	5.7950	6.8090	6.8780	7.0910	7.0530	7.2180	7.6500	7.4550	6.9020	6.5500	15.1500	
                              1.5530	4.5480	4.5460	5.8850	6.8540	6.9290	7.1500	7.1170	7.2760	7.7250	7.5390	6.9700	6.6060	15.1600	
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-60.0000</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
