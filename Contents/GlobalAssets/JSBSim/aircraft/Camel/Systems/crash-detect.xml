<?xml version="1.0"?>

<system name="crash-detect">

  <property value="0">systems/crash-detect/crashed</property>
  <property value="1">terrain-info/terrain</property>

  <channel name="Over-G">

     <switch name="systems/crash-detect/over-g">
          <test value="1">                            
                accelerations/Nz gt 20.0
          </test>
          <output>systems/crash-detect/crashed</output>
     </switch>

  </channel>

  <channel name="impact-ground">

     <switch name="systems/crash-detect/impact">
          <test logic="AND" value="1">
                position/h-agl-ft lt 1.0
                simulation/sim-time-sec gt 1.0
                terrain-info/terrain gt 0.0
          </test>
          <output>systems/crash-detect/crashed</output>
     </switch>

  </channel>

  <channel name="impact-water">

     <switch name="systems/crash-detect/impact-water">
          <test logic="AND" value="1">
                position/h-agl-ft lt 4.5
                terrain-info/terrain eq 0.0
                simulation/sim-time-sec gt 1.0
                velocities/vg-fps lt 10
          </test>
          <output>systems/crash-detect/crashed</output>
     </switch>

  </channel>
  

  <!-- instead fo this 'freeze' thing we're using a nasal script to freeze/unfreeze -->
  <!-- <channel name="freeze">

    <switch name="systems/crash-detect/switch1">
          <default value="3"/>          
          <test value="0">
                systems/crash-detect/crashed eq 1
          </test>
          <output>simulation/integrator/position/rotational</output>
    </switch>
    <switch name="systems/crash-detect/switch2">
          <default value="3"/>          
          <test value="0">
                systems/crash-detect/crashed eq 1
          </test>
          <output>simulation/integrator/position/translational</output>
    </switch>
    <switch name="systems/crash-detect/switch3">
          <default value="3"/>          
          <test value="0">
                systems/crash-detect/crashed eq 1
          </test>
          <output>simulation/integrator/rate/rotational</output>
    </switch>
    <switch name="systems/crash-detect/switch4">
          <default value="3"/>          
          <test value="0">
                systems/crash-detect/crashed eq 1
          </test>
          <output>simulation/integrator/rate/translational</output>
    </switch>

  </channel>   -->

</system>
