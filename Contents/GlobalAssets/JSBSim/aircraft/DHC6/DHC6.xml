<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="DHC-6" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

<!--
  File:     dhc6.xml
  Inputs:
    name:          dhc6
    type:          Multi-engine Glider, Small Commuter
    stall speed:   74.00kts
    max weight:    12500.00 lb
    length:        51.75 ft
    wing: 
     span:         65.00 ft
     area:         422.50 sq-ft
     mean chord:   6.47 ft
     aspect ratio: 10.10:1
     taper ratio:  0.99:1
     incidence:    2.50 degrees
     dihedral:     3.00 degrees
     sweep:        0.00 degrees

    no. engines:   2
    engine type:   Turboprop Engine
    engine layout: wings

    gear type:     tricycle
    steering type: steering
    retractable?:  no

  Outputs:
    wing loading:       29.59 lb/sq-ft
     - thickness ratio: 17.25%
    payload:            3331.50 lbs
    CL-alpha:           5.81 per radian
    CL-0:               0.33
    CL-max:             1.60
    CD-0:               0.02
    K:                  0.04
    Mcrit:              0.72
-->

    <fileheader>
        <author> <PERSON> Hofman </author>
        <filecreationdate> 2003-01-01 </filecreationdate>
        <version> $Id: DHC6.xml,v 1.24 2016/11/29 17:38:48 ehofman Exp $ </version>
        <description> DeHavilland DHC-6 </description>
      <reference title="http://www.aoc.noaa.gov/aircraft_otter.htm"/>
      <reference title="Smart Icing Systems Aerodynamics group UIUC
           https://gitorious.org/fg/fgdata/source/73c7e296676c55a4d1db43b49c3fcb938e991177:Aircraft-uiuc/TwinOtter"/>
      <reference title="Current Methods for Modeling and Simulating Icing Effects on Aircraft Performance, Stability and Control
           http://ntrs.nasa.gov/archive/nasa/casi.ntrs.nasa.gov/20090005992_2009004916.pdf"/>
      <reference title="Simulation Model Development for Icing Effects Flight Training
        http://www.bihrle.aero/pdfs/aiaa2002-01-1527.pdf"/>
      <reference title="performance data of the DHC-6-300
        http://www.caamsllc.com/Performance%20Data/DHC-6-300.pdf"/>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 422.5 </wingarea>
        <wingspan unit="FT"> 65 </wingspan>
        <chord unit="FT"> 6.5 </chord>
        <htailarea unit="FT2"> 98.18 </htailarea>
        <htailarm unit="FT"> 24.75 </htailarm>
        <vtailarea unit="FT2"> 76.05 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 211.8 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 93.6 </x>
            <y> -18.2 </y>
            <z> 31.2 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance negated_crossproduct_inertia="true">
        <ixx unit="SLUG*FT2"> 19317 </ixx>
        <iyy unit="SLUG*FT2"> 24679 </iyy>
        <izz unit="SLUG*FT2"> 35344 </izz>
        <ixz unit="SLUG*FT2"> -1099 </ixz>
        <emptywt unit="LBS"> 8100 </emptywt>
        <location name="CG" unit="IN">
            <x> 211.8 </x>
            <y> 0 </y>
            <z> -14.9 </z>
        </location>
        <pointmass name="name">
            <weight unit="LBS"> 230 </weight>
            <location name="POINTMASS" unit="IN">
                <x> 93.6 </x>
                <y> -18.2 </y>
                <z> 8.4 </z>
            </location>
        </pointmass>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 75.5 </x>
                <y> 0 </y>
                <z> -118.2 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 2982.6 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 994.2 </damping_coeff>
            <max_steer unit="DEG"> 15 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 245.9 </x>
                <y> -77.3 </y>
                <z> -117.5 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 9942 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1988.4 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 245.9 </x>
                <y> 77.3 </y>
                <z> -117.5 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 9942 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1988.4 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>0</retractable>
        </contact>
    </ground_reactions>
    <propulsion>
        <engine file="PT6A-27">
            <feed>0</feed>
            <feed>1</feed>
            <thruster file="Propeller">
                <location unit="IN">
                    <x> 142.4 </x>
                    <y> -109.3 </y>
                    <z> -2.88 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="PT6A-27">
            <feed>1</feed>
            <feed>0</feed>
            <thruster file="Propeller">
                <location unit="IN">
                    <x> 142.4 </x>
                    <y> 109.3 </y>
                    <z> -2.88 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <tank type="FUEL">    <!-- Tank number 0 -->
            <location unit="IN">
                <x> 211.8 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <capacity unit="LBS"> 1290.5 </capacity>
            <contents unit="LBS"> 892.1 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 1 -->
            <location unit="IN">
                <x> 211.8 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <capacity unit="LBS"> 1290.5 </capacity>
            <contents unit="LBS"> 892.1 </contents>
        </tank>
    </propulsion>

    <system file="Propulsion.xml"/>
    <system file="Conventional Controls.xml"/>
    <system file="Landing Gear.xml"/>
    <system file="Flaps.xml"/>

    <flight_control name="FCS: DHC-6 Twin Otter">
    </flight_control>
 
    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CD0">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -1.5700	1.6590
                              -0.2600	0.0580
                              0.0000	0.0488
                              0.2600	0.0580
                              1.5700	1.6590
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <value>0.0390</value>
                </product>
            </function>
            <function name="aero/coefficient/CDmach">
                <description>Drag_due_to_mach</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.0000
                              0.7000	0.0000
                              1.1000	0.0230
                              1.8000	0.0150
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDflap">
                <description>Drag_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-norm</property>
                    <value>0.0350</value>
                </product>
            </function>
            <function name="aero/coefficient/CDsb">
                <description>Drag_due_to_speedbrakes</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0280</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/mag-beta-rad</independentVar>
                          <tableData>
                              0.0000	0.0000
                              0.2600	0.0500
                              1.5700	1.2300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.0350</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.6000</value>
                </product>
            </function>
            <function name="aero/coefficient/CYdr">
                <description>Side_force_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/CYp">
                <description>Side_force_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.2000</value>
                </product>
            </function>
            <function name="aero/coefficient/CYr">
                <description>Side_force_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.4000</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.3491	-0.5800
                              -0.2618	-0.5200
                              -0.1745	-0.4500
                              -0.1396	-0.2800
                              -0.1047	-0.2000
                              -0.0698	0.1600
                              -0.0349	0.3200
                              0.0000	0.4400
                              0.0349	0.5800
                              0.0698	0.7500
                              0.1047	0.8100
                              0.1396	0.9500
                              0.1745	1.1000
                              0.2094	1.1250
                              0.2443	1.1400
                              0.2793	1.1600
                              0.3142	1.1200
                              0.3491	1.1300
                              0.4363	1.1700
                              0.5236	1.2099
                              0.6109	1.2699
                              0.6981	1.3999
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/dCLflap">
                <description>Delta_Lift_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>fcs/flap-pos-deg</independentVar>
                          <tableData>
                              0.0000	0.0000
                              10.0000	0.9250
                              20.0000	1.1890
                              30.0000	1.4750
                              40.0000	1.6330
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/dCLsb">
                <description>Delta_Lift_due_to_speedbrake</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/CLde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.6080</value>
                </product>
            </function>
            <function name="aero/coefficient/CLq">
                <description>Lift_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <property>aero/ci2vel</property>
                    <value>19.9700</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                      <table>
                          <independentVar lookup="row">aero/beta-rad</independentVar>
                          <independentVar lookup="column">aero/alpha-rad</independentVar>
                          <tableData>
                                      0.0698  0.1745  0.2443  0.3491
                              -0.3491 0.1250  0.0450  0.0240 -0.0270
                              -0.2618 0.0900  0.0220  0.0030 -0.0150
                              -0.1745 0.0770  0.0200 -0.0100  0.0000
                              -0.0873 0.0360  0.0270 -0.0220 -0.0080
                              0.0000  0.0000 -0.0100  0.0200 -0.0060
                              0.0873  0.0360 -0.0170  0.0200 -0.0100
                              0.1745  0.0770 -0.0100  0.0050 -0.0100
                              0.2618  0.0900 -0.0250 -0.0180  0.0000
                              0.3491  0.1250 -0.0450 -0.0350  0.0200

                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate_(roll_damping)</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.5000</value>
<!--
                    <table>
                        <independentVar lookup="row">aero/alpha-rad</independentVar>
                        <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                        <tableData>
                                       0.0     20.0     40.0
                              0.0000  -0.4078  0.2897   0.4123
                              0.1745  -0.1537  0.1054   0.0251
                              0.2618  -0.0055  0.0029   0.0057
                              0.3491   0.0657 -0.0476  -0.0669
                        </tableData>
                      </table>
-->
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.0600</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.1500
                              2.0000	0.0500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0150</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cma">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <tableData>
                             -0.3491   0.391
                             -0.2618   0.423
                             -0.1745   0.288
                             -0.0873   0.189
                              0.0000   0.093
                              0.0873  -0.048
                              0.1745  -0.200
                              0.2618  -0.318
                              0.3491  -0.384
                              0.4363  -0.397
                              0.5236  -0.403
                              0.6109  -0.469
                              0.6981  -0.532
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CmDe">
                <description>Pitch_moment_due_to_elevator_and_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/elevator-pos-rad</independentVar>
                          <independentVar lookup="table">fcs/flap-pos-deg</independentVar>
                          <tableData breakPoint="0.0">
                                     -0.4538	-0.2269	0.0000	0.1222	0.2443
                              -0.3491	0.2800	0.1700	0.0000	-0.1000	-0.2200
                              -0.2618	0.2000	0.0900	0.0000	-0.0900	-0.2800
                              -0.1745	0.4300	0.3100	0.0000	-0.1700	-0.3700
                              -0.1396	0.5100	0.3700	0.0000	-0.1900	-0.3800
                              -0.1047	0.5300	0.3900	0.0000	-0.2000	-0.3800
                              -0.0698	0.5600	0.4000	0.0000	-0.2000	-0.3900
                              -0.0349	0.5900	0.4100	0.0000	-0.2000	-0.3800
                              0.0000	0.6300	0.4000	0.0000	-0.1900	-0.3800
                              0.0349	0.6400	0.3900	0.0000	-0.2000	-0.3600
                              0.0698	0.6500	0.4000	0.0000	-0.1900	-0.3400
                              0.1047	0.6400	0.3800	0.0000	-0.1800	-0.3200
                              0.1396	0.5800	0.3600	0.0000	-0.1600	-0.3200
                              0.1745	0.5900	0.3700	0.0000	-0.2100	-0.3200
                              0.2094	0.5600	0.3500	0.0000	-0.2100	-0.3400
                              0.2443	0.5400	0.3400	0.0000	-0.1900	-0.2900
                              0.2793	0.4800	0.3300	0.0000	-0.1700	-0.2600
                              0.3142	0.4300	0.3100	0.0000	-0.1500	-0.2300
                              0.3491	0.4600	0.3000	0.0000	-0.2100	-0.2000
                              0.4363	0.2600	0.2100	0.0000	-0.0800	-0.1400
                              0.5236	0.2900	0.1900	0.0000	-0.0900	-0.1900
                              0.6109	0.3800	0.2400	0.0000	-0.0800	-0.1600
                              0.6981	0.4200	0.2300	0.0000	-0.0900	-0.1800
                          </tableData>
                          <tableData breakPoint="20.0">
                                     -0.4538	-0.2269	0.0000	0.1222	0.2443
                              -0.3491	0.3300	0.1800	0.0000	-0.4000	-0.1900
                              -0.2618	0.3000	0.2100	0.0000	-0.2000	-0.2200
                              -0.1745	0.4400	0.2400	0.0000	-0.1500	-0.3800
                              -0.1396	0.4800	0.2800	0.0000	-0.1700	-0.3600
                              -0.1047	0.4700	0.3000	0.0000	-0.1700	-0.3500
                              -0.0698	0.4600	0.3200	0.0000	-0.1900	-0.3700
                              -0.0349	0.4500	0.3500	0.0000	-0.2000	-0.3900
                              0.0000	0.5300	0.3600	0.0000	-0.2000	-0.4000
                              0.0349	0.5700	0.3800	0.0000	-0.2000	-0.3800
                              0.0698	0.5900	0.3900	0.0000	-0.2000	-0.3600
                              0.1047	0.6200	0.3800	0.0000	-0.2000	-0.3500
                              0.1396	0.5700	0.3500	0.0000	-0.1500	-0.2900
                              0.1745	0.4900	0.2900	0.0000	-0.1300	-0.2800
                              0.2094	0.4700	0.2700	0.0000	-0.1200	-0.2700
                              0.2443	0.4500	0.2500	0.0000	-0.1300	-0.2700
                              0.2793	0.4100	0.2300	0.0000	-0.1400	-0.2800
                              0.3142	0.4200	0.2500	0.0000	-0.1200	-0.3100
                              0.3491	0.4100	0.2300	0.0000	-0.1500	-0.2700
                              0.4363	0.3200	0.1900	0.0000	-0.0900	-0.1300
                              0.5236	0.2400	0.2000	0.0000	-0.0600	-0.1300
                              0.6109	0.2700	0.1600	0.0000	-0.0800	-0.1700
                              0.6981	0.3600	0.2100	0.0000	-0.0700	-0.1500
                          </tableData>
                          <tableData breakPoint="40.0">
                                     -0.4538	-0.2269	0.0000	0.1222	0.2443
                              -0.3491	0.2900	0.1700	0.0000	-0.0700	-0.2200
                              -0.2618	0.2000	0.0900	0.0000	-0.0700	-0.2600
                              -0.1745	0.1800	0.2100	0.0000	-0.1600	-0.3200
                              -0.1396	0.3600	0.2900	0.0000	-0.1800	-0.3600
                              -0.1047	0.4800	0.3300	0.0000	-0.2000	-0.3800
                              -0.0698	0.5000	0.3600	0.0000	-0.2000	-0.3800
                              -0.0349	0.5300	0.3700	0.0000	-0.2000	-0.3600
                              0.0000	0.5600	0.3800	0.0000	-0.1900	-0.3400
                              0.0349	0.6000	0.3900	0.0000	-0.1700	-0.2900
                              0.0698	0.6400	0.2600	0.0000	-0.1500	-0.2600
                              0.1047	0.5900	0.2400	0.0000	-0.1300	-0.2400
                              0.1396	0.5300	0.2400	0.0000	-0.1100	-0.2000
                              0.1745	0.4200	0.2200	0.0000	-0.1000	-0.2100
                              0.2094	0.4100	0.2100	0.0000	-0.1100	-0.2400
                              0.2443	0.4400	0.2200	0.0000	-0.1300	-0.2300
                              0.2793	0.4200	0.2500	0.0000	-0.1100	-0.2000
                              0.3142	0.4800	0.2800	0.0000	-0.1000	-0.2000
                              0.3491	0.1800	0.2100	0.0000	-0.0800	-0.1900
                              0.4363	0.2400	0.1900	0.0000	-0.1200	-0.1600
                              0.5236	0.2800	0.1800	0.0000	-0.0700	-0.1400
                              0.6109	0.3600	0.1800	0.0000	-0.1000	-0.1700
                              0.6981	0.3900	0.2100	0.0000	-0.0900	-0.1300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CmT">
                <description>Pitch_moment_due_to_propulsion</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>systems/propulsion/thrust-coefficient</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                          <tableData>
                                       0.00    40.00
                            -0.3491    0.0000   0.0000
                            -0.2618    0.0025   0.0941
                            -0.1745    0.0187   0.1523
                            -0.0873    0.0739   0.0898
                             0.0000    0.0791   0.1691
                             0.0873    0.1150   0.5529
                             0.1745    0.2268   0.6156
                             0.2618    0.3468   0.0000
                             0.3491    0.2793   0.0000
                             0.4363    0.0000   0.0000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-34.2000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-8.0000</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb"> 
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                          <tableData>
                                       0.0      20.0     40.0
                             -0.3491  -0.6000  -0.6000  -0.6000
                             -0.2618  -0.5150  -0.5150  -0.5150
                             -0.1745  -0.4750   0.0000   0.5923
                             -0.0873   0.0010   0.5897   1.4987
                              0.0000   0.5000   1.2032   2.4329
                              0.0873   0.8670   1.0046   2.8111
                              0.1745   1.1575   2.2389   3.1892
                              0.2618   1.1768   2.4895   2.8321
                              0.3491   1.0080   2.1187   2.5586
                              0.4363   1.1973   1.9986   2.1203
                              0.5236   1.1249   1.7488   1.9943
                              0.6109   1.2786   1.7401   1.8735
                              0.6981   1.4377   1.7387   1.7387
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.1800</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnp">
                <description>Yaw_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.0600</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.1250</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnda">
                <description>Adverse_yaw</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>-0.0010</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
