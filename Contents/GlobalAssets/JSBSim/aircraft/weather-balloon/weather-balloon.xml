<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="Weather Balloon" version="2.0" release="ALPHA"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
  <author> <PERSON> </author>
  <filecreationdate>2011-12-11</filecreationdate>
  <version>$Revision: 1.3 $</version>
  <description> Models a 6-foot weather balloon. </description>
  <license
     licenseName="GPL (General Public License)"
     licenseURL="http://www.gnu.org/licenses/gpl.html"/>
  <note>
   This model was created using publicly available data, publicly available
   technical reports, textbooks, and guesses. It contains no proprietary or
   restricted data. It has been validated only to the extent that it seems
   to "fly right", and possibly to comply to published, publicly known,
   performance data (maximum speed, endurance, etc.). Thus, this model
   is meant for educational and entertainment purposes only.

   This simulation model is not endorsed by the manufacturer.
   This model is not to be sold.
  </note> 
  <note>
    References
    [1] BT-1039.12 http://library.gsfc.nasa.gov/Databases/Balloon/Data/BT1039.12.pdf
       Some Theoretical and Practical Aspects of Sounding Balloon Velocity
       Dewey and Alrny
       Andrew J. Kelly
       Jun 1968

    [2]  http://www.novalynx.com/400-balloons.html
      Model      Weight   Uninflated Diameter   Std Inflated Diameter   Burst Diameter
                  gm        in (mm)                   ft (cm)              ft (cm)
     400-8242    800     42.5 (108)                 6.1 (185)           22.3 (680)

     Balloon Weight   Free Lift   Payload   Gross Lift   Inflation Volume   Inflation Diameter   Ascent Rate   Altitude at Burst
           gm             gm         gm         gm               ft3 (m3)           ft (m)            m/min           mi (km)
          800           2000       1150       3950             127.4 (3.6)         6.2 (1.9)           400          17.4 (28)

    [3] http://ntrs.nasa.gov/search.jsp?R=19640016055    Aerodynamics of spherical balloon wind sensors
   </note>
 </fileheader>

 <metrics>
   <wingarea  unit="FT2">   9.8 </wingarea>
   <wingspan  unit="FT" >   6.2 </wingspan>
   <wing_incidence>         0.0 </wing_incidence>
   <chord     unit="FT" >   6.2 </chord>
   <htailarea unit="FT2">   0 </htailarea>
   <htailarm  unit="FT" >   0 </htailarm>
   <vtailarea unit="FT2">   0 </vtailarea>
   <vtailarm  unit="FT" >   0 </vtailarm>
   <location name="AERORP" unit="IN">
     <x> 0.0 </x>
     <y> 0.0 </y>
     <z> 0.0 </z>
   </location>
   <location name="EYEPOINT" unit="IN">
     <x> 0.0 </x>
     <y> 0.0 </y>
     <z> 0.0 </z>
   </location>
   <location name="VRP" unit="IN">
     <x> 0.0 </x>
     <y> 0.0 </y>
     <z> 0.0 </z>
   </location>
 </metrics>

 <mass_balance>
   <description> Inertia varies between 0.35 and 4.5 slug*ft2 as radius increases from 3.1 ft to 11.1 ft </description>
   <ixx unit="SLUG*FT2">     1.0 </ixx>
   <iyy unit="SLUG*FT2">     1.0 </iyy>
   <izz unit="SLUG*FT2">     1.0 </izz>
   <emptywt unit="KG" >      0.8 </emptywt>
   <location name="CG" unit="IN">
     <x> 0.0 </x>
     <y> 0.0 </y>
     <z> 0.0 </z>
   </location>
   <pointmass name="Radiosonde">
    <weight unit="KG">    1.15 </weight>
    <location name="POINTMASS" unit="IN">
     <x>   0.0 </x>
     <y>   0.0 </y>
     <z> -25.1 </z>
    </location>
  </pointmass> 
 </mass_balance>

 <ground_reactions>
  <contact type="STRUCTURE" name="Default">
    <location unit="IN">
     <x> 0.0 </x>
     <y> 0.0 </y>
     <z> 0.0 </z>
    </location>
    <static_friction>  1.00 </static_friction>
    <dynamic_friction> 1.00 </dynamic_friction>
    <spring_coeff unit="LBS/FT">      10.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 10.00 </damping_coeff>
  </contact>
 </ground_reactions>

 <propulsion>
 </propulsion>

 <flight_control name="FCS: Sounding Balloon">
  <channel name="Burst">
   <switch name="buoyant_forces/gas-cell/burst">
    <default value="0.0"/>
    <test logic="OR" value="1.0">
      buoyant_forces/gas-cell/volume-ft3 ge buoyant_forces/gas-cell/max_volume-ft3
      buoyant_forces/gas-cell/burst ge 0.5
    </test>
    <output>buoyant_forces/gas-cell/valve_open</output>
   </switch>
  </channel>

  <channel name="chute">
   <fcs_function name="fcs/parachute_reef_pos_norm">
    <function>
     <sum>
      <property>inertia/weight-lbs</property>
      <property>forces/fbz-buoyancy-lbs</property>
     </sum>
    </function>
    <clipto>
     <min>0.</min>
     <max>1.</max>
    </clipto>
   </fcs_function>
  <pure_gain name="metrics/Schute-sqft">
    <input>fcs/parachute_reef_pos_norm</input>
    <gain> 8.0 </gain> <!-- Area of braking chute -->
   </pure_gain>
  </channel>

  <channel name="radiosonde">
   <fcs_function name="fcs/radiosonde-in">
    <description>Radiosonde is tied to the bottom of the balloon</description>
    <function>
     <product>
     <sum>
       <value>1.0</value> <!-- Radiosonde CG in feet below skin of balloon -->
       <property>metrics/radius-ft</property>
      </sum>
      <value>-12</value><!-- ft to inches -->
     </product>
    </function>
    <output>inertia/pointmass-location-Z-inches</output>
   </fcs_function>
  </channel>
 </flight_control>

 <aerodynamics>

  <function name="metrics/radius-ft">
   <description>Radius of balloon</description>
   <pow>
    <product>
     <value>0.23873241</value>
     <property>buoyant_forces/gas-cell/volume-ft3</property>
    </product>
    <value>0.33333</value>
    </pow>
  </function>

  <function name="metrics/diameter-ft">
   <description>Diameter of balloon</description>
   <product>
    <value>2.0</value>
    <property>metrics/radius-ft</property>
   </product>
  </function>

  <function name="metrics/Sref-sqft">
   <description>Profile area of balloon</description>
   <product>
    <property>metrics/radius-ft</property>
    <property>metrics/radius-ft</property>
    <value>3.14159</value>
   </product>
  </function>

   <function name="metrics/surface-sqft">
   <description>surface area of balloon</description>
   <product>
    <property>metrics/radius-ft</property>
    <property>metrics/radius-ft</property>
    <value>3.14159</value>
    <value>4.0</value>
   </product>
  </function>

  <function name="aero/Re-cor">
   <description>Reynold Number Corrected to balloon Diameter</description>
   <product>
    <property>aero/Re</property>
    <quotient>
      <property>metrics/diameter-ft</property>
      <property>metrics/cbarw-ft</property>
    </quotient>
   </product>
  </function>


  <axis name="LIFT">

  </axis>

  <axis name="DRAG">

   <function name="aero/force/BasicDrag">
     <description>Drag. Table from [1]</description>
      <product>
        <value name="Tweak"> 1.22 </value>
        <property>aero/qbar-psf</property>
        <property>metrics/Sref-sqft</property>
        <table>
          <independentVar>aero/Re-cor</independentVar>
          <tableData>
              8766  0.39
             28687  0.44
             81039  0.50
            110575  0.49
            232685  0.42
            460735  0.31
            613333  0.24
            690250  0.21
            883256  0.20
          </tableData>
        </table>
      </product>
    </function>

  </axis>

  <axis name="SIDE">

  </axis>

  <axis name="ROLL">
   <function name="aero/coefficient/Clp">
    <description>Roll_moment_due_to_roll_rate_(roll_damping)</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sref-sqft</property>
     <property>aero/bi2vel</property>
     <property>velocities/p-aero-rad_sec</property>
     <value>-0.09</value>
    </product>
   </function> 

   <function name="aero/coefficient/Clpc">
    <description>Roll_moment_due_to_roll_rate_chute_(roll_damping)</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Schute-sqft</property>
     <property>aero/bi2vel</property>
     <property>velocities/p-aero-rad_sec</property>
     <value>-0.4</value>
    </product>
   </function> 
   </axis>

  <axis name="PITCH">
   <function name="aero/coefficient/Cmq">
    <description>Pitch_moment_due_to_pitch_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sref-sqft</property>
     <property>aero/ci2vel</property>
     <property>velocities/q-aero-rad_sec</property>
     <value>-0.09</value>
    </product>
   </function>

   <function name="aero/coefficient/Cmqc">
    <description>Pitch_moment_due_to_pitch_rate_chute</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Schute-sqft</property>
     <property>aero/ci2vel</property>
     <property>velocities/q-aero-rad_sec</property>
     <value>-0.4</value>
    </product>
   </function> 
  </axis>

  <axis name="YAW">
    <function name="aero/coefficient/Cnr">
     <description>Yaw_moment_due_to_yaw_rate</description>
     <product>
      <property>aero/qbar-psf</property>
      <property>metrics/Sref-sqft</property>
      <property>aero/bi2vel</property>
      <property>velocities/r-aero-rad_sec</property>
      <value>-0.05</value>
    </product>
   </function> 
 
  <function name="aero/coefficient/Cnrc">
     <description>Yaw_moment_due_to_yaw_rate_chute</description>
     <product>
      <property>aero/qbar-psf</property>
      <property>metrics/Schute-sqft</property>
      <property>aero/bi2vel</property>
      <property>velocities/r-aero-rad_sec</property>
      <value>-0.05</value>
    </product>
   </function> 
  </axis>

 </aerodynamics>

 <external_reactions>
  <force name="parachute" frame="WIND">
    <function>
      <product>
        <p> aero/qbar-psf </p>
        <p> metrics/Schute-sqft </p>
        <v> 1.4 </v> <!-- Full drag coefficient -->
      </product>
    </function>
    <location unit="FT">
      <x> 0</x>
      <y> 0</y>
      <z>-6.0</z>
    </location>
    <!-- The direction describes a unit vector. In this case, since
         the selected frame is the WIND frame, the "-1" x component
         describes a direction exactly opposite of the direction
         into the wind vector. That is, the direction specified below
         is the direction that the drag force acts in. -->
    <direction>
      <x>-1</x>
      <y>0</y>
      <z>0</z>
    </direction>
  </force>
 </external_reactions>

<buoyant_forces>
  <gas_cell type="HYDROGEN">
    <location unit="IN">
      <x> 0.0 </x>
      <y> 0.0 </y>
      <z> 0.0 </z>
    </location>
    <x_radius unit="IN"> 133.0 </x_radius>
    <y_radius unit="IN"> 133.0 </y_radius>
    <z_radius unit="IN"> 133.0 </z_radius>
    <fullness> 0.02 </fullness>
    <max_overpressure unit="LBS/FT2"> 3000.0 </max_overpressure><!-- WAG -->
    <heat>
    <!-- Heat transfer due to temperature difference. -->
    <function name="buoyant_forces/gas-cell/dU_conduction">
     <product>
      <property> metrics/surface-sqft </property> <!-- Surface area [ft2] -->
      <value>     0.02 </value> <!-- Conductivity [lb / (K ft sec)] (Guess) -->
      <difference>
       <property> atmosphere/T-R </property>
       <property> buoyant_forces/gas-cell/temp-R </property>
      </difference>
     </product>
    </function>
    <function name="buoyant_forces/gas-cell/dU_radiation">
     <product>
      <value> 0.1714e-8 </value> <!-- Stefan-Boltzmann's constant
                                      [Btu / (h ft^2 R^4)] -->
      <value>       0.05 </value> <!-- Emissivity [0,1] (Guess) -->
      <property> metrics/surface-sqft</property><!-- Surface area [ft2] -->
      <difference>
       <pow>
        <property> atmosphere/T-R </property>
        <value> 4.0 </value>
       </pow>
       <pow>
        <property> buoyant_forces/gas-cell/temp-R </property>
        <value> 4.0 </value>
       </pow>
      </difference>
     </product>
    </function> 
    </heat>
    <valve_coefficient unit="FT4*SEC/SLUG"> 100000 </valve_coefficient> <!-- just a big number so the hydrogen disappears when the balloon bursts -->
  </gas_cell>
</buoyant_forces>

</fdm_config>

