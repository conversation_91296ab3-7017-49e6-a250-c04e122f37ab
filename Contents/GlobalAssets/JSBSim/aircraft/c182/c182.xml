<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="c182" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
        <author> Unknown </author>
        <filecreationdate> 2002-01-01 </filecreationdate>
        <version> $Id: c182.xml,v 1.24 2012/12/22 15:22:21 jberndt Exp $ </version>
        <description> Cessna C-182 </description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 174 </wingarea>
        <wingspan unit="FT"> 35.8 </wingspan>
        <chord unit="FT"> 4.9 </chord>
        <htailarea unit="FT2"> 21.9 </htailarea>
        <htailarm unit="FT"> 15.7 </htailarm>
        <vtailarea unit="FT2"> 16.5 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 43.2 </x>
            <y> 0 </y>
            <z> 59.4 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 37 </x>
            <y> 0 </y>
            <z> 48 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 43.9 </x>
            <y> 0 </y>
            <z> 40.6 </z>
        </location>
    </metrics>

    <mass_balance>
        <ixx unit="SLUG*FT2"> 948 </ixx>
        <iyy unit="SLUG*FT2"> 1346 </iyy>
        <izz unit="SLUG*FT2"> 1967 </izz>
        <emptywt unit="LBS"> 1700 </emptywt>
        <location name="CG" unit="IN">
            <x> 41 </x>
            <y> 0 </y>
            <z> 36.5 </z>
        </location>
        <pointmass name="name">
            <weight unit="LBS"> 180 </weight>
            <location name="POINTMASS" unit="IN">
                <x> 36 </x>
                <y> -14 </y>
                <z> 24 </z>
            </location>
        </pointmass>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE">
            <location unit="IN">
                <x> -6.8 </x>
                <y> 0 </y>
                <z> -20 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1800 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 600 </damping_coeff>
            <max_steer unit="DEG"> 10 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MAIN">
            <location unit="IN">
                <x> 58.2 </x>
                <y> -43 </y>
                <z> -17.9 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 5400 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1600 </damping_coeff>
            <max_steer unit="DEG"> 360.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MAIN">
            <location unit="IN">
                <x> 58.2 </x>
                <y> 43 </y>
                <z> -17.9 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 5400 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1600 </damping_coeff>
            <max_steer unit="DEG"> 360.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="TAIL_SKID">
            <location unit="IN">
                <x> 188 </x>
                <y> 0 </y>
                <z> 8 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.2 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 20000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_TIP">
            <location unit="IN">
                <x> 43.2 </x>
                <y> -214.8 </y>
                <z> 59.4 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.2 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_TIP">
            <location unit="IN">
                <x> 43.2 </x>
                <y> 214.8 </y>
                <z> 59.4 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.2 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
    </ground_reactions>
    <propulsion>
        <engine file="engIO540AB1A5">
            <feed>0</feed>
            <feed>1</feed>
            <thruster file="prop_81in2v">
                <location unit="IN">
                    <x> -37.7 </x>
                    <y> 0 </y>
                    <z> 26.6 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
                <sense> 1 </sense>
                <p_factor> 10 </p_factor>
            </thruster>
        </engine>
        <tank type="FUEL">    <!-- Tank number 0 -->
            <location unit="IN">
                <x> 56 </x>
                <y> -112 </y>
                <z> 59.4 </z>
            </location>
            <capacity unit="LBS"> 300 </capacity>
            <contents unit="LBS"> 200 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 1 -->
            <location unit="IN">
                <x> 56 </x>
                <y> 112 </y>
                <z> 59.4 </z>
            </location>
            <capacity unit="LBS"> 300 </capacity>
            <contents unit="LBS"> 200 </contents>
        </tank>
    </propulsion>
    <flight_control name="FCS: c182">
        <channel name="Pitch">
            <summer name="Pitch Trim Sum">
                <input>fcs/elevator-cmd-norm</input>
                <input>fcs/pitch-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Elevator Control">
                <input>fcs/pitch-trim-sum</input>
                <gain>0.01745</gain>
                <range>
                    <min>-28</min>
                    <max>23</max>
                </range>
                <output>fcs/elevator-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Elevator position normalized">
  	         <input>fcs/elevator-pos-deg</input>
  	         <domain>
  	             <min>-25</min>
  	             <max>35</max>
  	         </domain>
  	         <range>
  	              <min>-1</min>
  	              <max>1</max>
  	         </range>
  	         <output>fcs/elevator-pos-norm</output>
  	     </aerosurface_scale>
        </channel>
        <channel name="Roll">
            <summer name="Roll Trim Sum">
                <input>fcs/aileron-cmd-norm</input>
                <input>fcs/roll-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Aileron Control">
                <input>fcs/roll-trim-sum</input>
                <gain>0.01745</gain>
                <range>
                    <min>-20</min>
                    <max>15</max>
                </range>
                <output>fcs/left-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Left Aileron position normalized">
               <input>fcs/left-aileron-pos-deg</input>
               <domain>
                  <min>-20</min>
                  <max>15</max>
               </domain>
               <range>
                   <min>-0.75</min>
                   <max>1</max>
               </range>
               <output>fcs/left-aileron-pos-norm</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right Aileron position normalized">
               <input>fcs/right-aileron-pos-deg</input>
               <domain>
                  <min>-20</min>
                  <max>15</max>
               </domain>
               <range>
                   <min>-0.75</min>
                   <max>1</max>
               </range>
               <output>fcs/right-aileron-pos-norm</output>
            </aerosurface_scale>

        </channel>
        <channel name="Yaw">
            <summer name="Yaw Trim Sum">
                <input>fcs/rudder-cmd-norm</input>
                <input>fcs/yaw-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Rudder Control">
                <input>fcs/yaw-trim-sum</input>
                <gain>0.01745</gain>
                <range>
                    <min>-16</min>
                    <max>16</max>
                </range>
                <output>fcs/rudder-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Rudder position normalized">
                 <input>fcs/rudder-pos-deg</input>
                 <domain>
                      <min>-25</min>
                      <max>25</max>
                 </domain>
                 <range>
                      <min>-1</min>
                      <max>1</max>
                 </range>
                 <output>fcs/rudder-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Flaps">
            <kinematic name="Flaps Control">
                <input>fcs/flap-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>10</position>
                        <time>2</time>
                    </setting>
                    <setting>
                        <position>20</position>
                        <time>1</time>
                    </setting>
                    <setting>
                        <position>30</position>
                        <time>1</time>
                    </setting>
                </traverse>
                <output>fcs/flap-pos-deg</output>
            </kinematic>

            <aerosurface_scale name="Flap position normalized">
                 <input>fcs/flap-pos-deg</input>
                 <domain>
                    <min>0</min>
                    <max>30</max>
                 </domain>
                 <range>
                     <min>0</min>
                     <max>1</max>
                 </range>
                 <output>fcs/flap-pos-norm</output>
            </aerosurface_scale>
        </channel>
    </flight_control>
    <aerodynamics>

        <function name="aero/function/kCLge">
            <description>Change_in_lift_due_to_ground_effect</description>
            <table>
                <independentVar>aero/h_b-mac-ft</independentVar>
                <tableData>
                    0.0000	1.2030
                    0.1000	1.1270
                    0.1500	1.0900
                    0.2000	1.0730
                    0.3000	1.0460
                    0.4000	1.0550
                    0.5000	1.0190
                    0.6000	1.0130
                    0.7000	1.0080
                    0.8000	1.0060
                    0.9000	1.0030
                    1.0000	1.0020
                    1.1000	1.0000
                </tableData>
            </table>
        </function>

        <axis name="DRAG">
            <function name="aero/coefficient/CDo">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <value>0.027</value>
                </product>
            </function>
            <function name="aero/coefficient/CDalpha">
                <description>Drag_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.0873	0.0041
                              -0.0698	0.0013
                              -0.0524	0.0001
                              -0.0349	0.0003
                              -0.0175	0.0020
                              0.0000	0.0052
                              0.0175	0.0099
                              0.0349	0.0162
                              0.0524	0.0240
                              0.0698	0.0334
                              0.0873	0.0442
                              0.1047	0.0566
                              0.1222	0.0706
                              0.1396	0.0860
                              0.1571	0.0962
                              0.1745	0.1069
                              0.1920	0.1180
                              0.2094	0.1298
                              0.2269	0.1424
                              0.2443	0.1565
                              0.2618	0.1727
                              0.2793	0.1782
                              0.2967	0.1716
                              0.3142	0.1618
                              0.3316	0.1475
                              0.3491	0.1097
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/mag-beta-rad</property>
                    <value>0.1700</value>
                </product>
            </function>
            <function name="aero/coefficient/CDDf">
                <description>Drag_due_to_flap_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                          <tableData>
                                0.0000	10.0000	20.0000	30.0000
                              -0.0873	0.0000	0.0030	0.0084	0.0153
                              -0.0698	0.0000	0.0061	0.0131	0.0208
                              -0.0524	0.0000	0.0092	0.0178	0.0263
                              -0.0349	0.0000	0.0124	0.0225	0.0317
                              -0.0175	0.0000	0.0155	0.0272	0.0372
                              0.0000	0.0000	0.0186	0.0319	0.0427
                              0.0175	0.0000	0.0218	0.0367	0.0483
                              0.0349	0.0000	0.0250	0.0415	0.0539
                              0.0524	0.0000	0.0282	0.0463	0.0594
                              0.0698	0.0000	0.0314	0.0511	0.0650
                              0.0873	0.0000	0.0346	0.0558	0.0706
                              0.1047	0.0000	0.0378	0.0606	0.0762
                              0.1222	0.0000	0.0409	0.0654	0.0818
                              0.1396	0.0000	0.0441	0.0702	0.0873
                              0.1571	0.0000	0.0461	0.0731	0.0907
                              0.1745	0.0000	0.0480	0.0760	0.0941
                              0.1920	0.0000	0.0499	0.0789	0.0975
                              0.2094	0.0000	0.0518	0.0818	0.1008
                              0.2269	0.0000	0.0538	0.0847	0.1043
                              0.2443	0.0000	0.0559	0.0879	0.1080
                              0.2618	0.0000	0.0582	0.0913	0.1120
                              0.2793	0.0000	0.0590	0.0925	0.1133
                              0.2967	0.0000	0.0581	0.0911	0.1117
                              0.3142	0.0000	0.0567	0.0890	0.1093
                              0.3316	0.0000	0.0546	0.0859	0.1056
                              0.3491	0.0000	0.0485	0.0767	0.0950
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDDe">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.06</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.3930
                              0.0943	-0.4040
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CYp">
                <description>Side_force_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.0750
                              0.0943	-0.1450
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CYr">
                <description>Side_force_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	0.2140
                              0.0943	0.2670
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CYDa">
                <description>Side_force_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/CYDr">
                <description>Side_force_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.1870</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLo">
                <description>Lift_at_zero_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                    <value>0.3070</value>
                </product>
            </function>
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.0900	-0.5270
                              0.0000	-0.0570
                              0.1400	0.7130
                              0.2100	0.9450
                              0.2400	1.0470
                              0.2600	1.1330
                              0.2800	1.1590
                              0.3000	1.1250
                              0.3200	1.0740
                              0.3400	0.9910
                              0.3600	0.6630
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLDf">
                <description>Lift_due_to_flap_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                      <table>
                          <independentVar>fcs/flap-pos-deg</independentVar>
                          <tableData>
                              0.0000	0.0000
                              10.0000	0.2000
                              20.0000	0.3000
                              30.0000	0.3500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLadot">
                <description>Lift_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/alphadot-rad_sec</property>
                    <property>aero/ci2vel</property>
                    <value>1.7000</value>
                </product>
            </function>
            <function name="aero/coefficient/CLq">
                <description>Lift_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <property>aero/ci2vel</property>
                    <value>3.9000</value>
                </product>
            </function>
            <function name="aero/coefficient/CLDe">
                <description>Lift_due_to_elevator_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.4300</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                      <table>
                          <independentVar lookup="row">aero/beta-rad</independentVar>
                          <independentVar lookup="column">aero/alpha-rad</independentVar>
                          <tableData>
                                0.0000	0.0943
                              -0.3490	0.0322	0.0312
                              0.0000	0.0000	0.0000
                              0.3490	-0.0322	-0.0312
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate_(roll_damping)</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.4840
                              0.0943	-0.4870
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	0.0798
                              0.0943	0.1869
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/ClDa">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.15</value>
                </product>
            </function>
            <function name="aero/coefficient/ClDr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.0147</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmo">
                <description>Pitching_moment_at_zero_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <value>0.0400</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.6130
                              0.0943	-0.6500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-7.2700
                              0.0943	-5.5700
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-12.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/CmDf">
                <description>Pitch_moment_due_to_flap_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                      <table>
                          <independentVar>fcs/flap-pos-deg</independentVar>
                          <tableData>
                              0.0000	0.0000
                              10.0000	-0.0654
                              20.0000	-0.0981
                              30.0000	-0.1140
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CmDe">
                <description>Pitch_moment_due_to_elevator_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.6220
                              0.0943	-0.8690
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	0.0587
                              0.0943	0.0907
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cnp">
                <description>Yaw_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.0278
                              0.0943	-0.0649
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.0937
                              0.0943	-0.1199
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CnDa">
                <description>Yaw_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.0216
                              0.0943	-0.0504
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CnDr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              0.0000	-0.0645
                              0.0943	-0.0805
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
