<fdm_config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="global5000" release="ALPHA" version="2.0" xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
  <author> Aeromatic v 0.96 and fixed bugs <PERSON><PERSON><PERSON><PERSON> </author>
  <filecreationdate>2021-05-17</filecreationdate>
  <version>$Revision: 1.20 $</version>
  <description>


    File created with Aeromatic

    Based on <PERSON><PERSON><PERSON>, M., Jafer, S., Towhidnejad, M. "Flight Dynamics Model of Bombardier Global 5000 Aircraft", AIAA Modeling and Simulation Technologies Conference, 13-17 June 2016, Washington D.C., AIAA 2016-3525, DOI: 10.2514/6.2016-3525

    Models a global5000

    Mods:
    included feed=2 in the engine to use tank 2
    included important outputs
    adjusted CL and CD tables to match theoretical Polar Drag curve
    by considering e=0.83 and k=0.03453 and FAA TC 18-7

    modified by <PERSON><PERSON><PERSON><PERSON>
    ATS aerohermalsolutions.co
    <EMAIL>
    May 2021

    File:     global5000.xml
    Inputs:
    name:          global5000
    type:          two-engine transonic transport
    max weight:    87700 lb
    wing span:     93 ft
    length:        96.9 ft
    wing area:     1022 sq-ft
    gear type:     tricycle
    castering:
    retractable?:  yes
    # engines:     2
    engine type:   turbine
    engine layout: aft fuselage
    yaw damper?    yes
    Outputs:
    wing loading:  85.81 lb/sq-ft
    payload:       15172.1 lbs
    CL-alpha:      4.4 per radian
    CL-0:          0.2
    CL-max:        1.2
    CD-0:          0.02
    K:             0.043

  </description>
 </fileheader>



 <metrics>
   <wingarea unit="FT2"> 1022.00 </wingarea>
   <wingspan unit="FT">   93.00 </wingspan>
   <wing_incidence>          2.00 </wing_incidence>
   <chord unit="FT">   10.99 </chord>
   <htailarea unit="FT2">  245.00 </htailarea>
   <htailarm unit="FT">   31.00 </htailarm>
   <vtailarea unit="FT2">  186.00 </vtailarea>
   <vtailarm unit="FT">   13.00 </vtailarm>
   <location name="AERORP" unit="IN">
     <x> 790.80 </x>
     <y>   0.00 </y>
     <z>   0.00 </z>
   </location>
   <location name="EYEPOINT" unit="IN">
     <x>  81.40 </x>
     <y> -30.00 </y>
     <z>  70.00 </z>
   </location>
   <location name="VRP" unit="IN">
     <x>0</x>
     <y>0</y>
     <z>0</z>
   </location>
 </metrics>

 <mass_balance>
   <ixx unit="SLUG*FT2">    238070 </ixx>
   <iyy unit="SLUG*FT2">    589404 </iyy>
   <izz unit="SLUG*FT2">    834676 </izz>
   <emptywt unit="LBS">     48235 </emptywt>
   <location name="CG" unit="IN">
     <x> 790.80 </x>
     <y>   0.00 </y>
     <z> -29.07 </z>
   </location>
   <pointmass name="Payload">
    <description> 15172 LBS + full (24293 LBS) fuel should bring model up to entered max weight</description>
    <weight unit="LBS">   7586.0 </weight>
    <location name="POINTMASS" unit="IN">
      <x> 790.80 </x>
      <y>   0.00 </y>
      <z> -29.07 </z>
    </location>
  </pointmass> 
 </mass_balance>

 <ground_reactions>

  <contact name="NOSE" type="BOGEY">
   <location unit="IN">
     <x> 151.16 </x>
     <y>   0.00 </y>
     <z> -139.54 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">      26310.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 13155.00 </damping_coeff>
   <max_steer unit="DEG"> 5.00 </max_steer>
   <brake_group>NONE</brake_group>
   <retractable>1</retractable>
  </contact>

  <contact name="LEFT_MAIN" type="BOGEY">
   <location unit="IN">
     <x> 822.43 </x>
     <y> -100.44 </y>
     <z> -139.54 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">      87700.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 43850.00 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <brake_group>LEFT</brake_group>
   <retractable>1</retractable>
  </contact>

  <contact name="RIGHT_MAIN" type="BOGEY">
   <location unit="IN">
     <x> 822.43 </x>
     <y> 100.44 </y>
     <z> -139.54 </z>
   </location>
   <static_friction>  0.80 </static_friction>
   <dynamic_friction> 0.50 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT">      87700.00 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 43850.00 </damping_coeff>
   <max_steer unit="DEG">0</max_steer>
   <brake_group>RIGHT</brake_group>
   <retractable>1</retractable>
  </contact>

  <contact name="LEFT_WING" type="STRUCTURE">
    <location unit="IN">
     <x> 790.80 </x>
     <y> -46.50 </y>
     <z> -29.07 </z>
    </location>
    <static_friction>  1.00 </static_friction>
    <dynamic_friction> 1.00 </dynamic_friction>
    <spring_coeff unit="LBS/FT">      87700.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 87700.00 </damping_coeff>
  </contact>

  <contact name="RIGHT_WING" type="STRUCTURE">
    <location unit="IN">
     <x> 790.80 </x>
     <y>  46.50 </y>
     <z> -29.07 </z>
    </location>
    <static_friction>  1.00 </static_friction>
    <dynamic_friction> 1.00 </dynamic_friction>
    <spring_coeff unit="LBS/FT">      87700.00 </spring_coeff>
    <damping_coeff unit="LBS/FT/SEC"> 87700.00 </damping_coeff>
  </contact>

 </ground_reactions>

 <propulsion>

   <engine file="BR710">
    <feed>0</feed>
    <feed>2</feed>
    <thruster file="direct">
     <location unit="IN">
       <x> 1102.80 </x>
       <y> -20.00 </y>
       <z>   0.00 </z>
     </location>
     <orient unit="DEG">
       <pitch> 0.00 </pitch>
       <roll>  0.00 </roll>
       <yaw>   0.00 </yaw>
     </orient>
    </thruster>
  </engine>

   <engine file="BR710">
    <feed>1</feed>
    <feed>2</feed>
    <thruster file="direct">
     <location unit="IN">
       <x> 1102.80 </x>
       <y>  20.00 </y>
       <z>   0.00 </z>
     </location>
     <orient unit="DEG">
       <pitch> 0.00 </pitch>
       <roll>  0.00 </roll>
       <yaw>   0.00 </yaw>
     </orient>
    </thruster>
  </engine>

  <tank number="0" type="FUEL">
     <location unit="IN">
       <x> 790.80 </x>
       <y>   0.00 </y>
       <z> -29.07 </z>
     </location>
     <capacity unit="LBS"> 8097.63 </capacity>
     <contents unit="LBS"> 8097.63 </contents>
  </tank>

  <tank number="1" type="FUEL">
     <location unit="IN">
       <x> 790.80 </x>
       <y>   0.00 </y>
       <z> -29.07 </z>
     </location>
     <capacity unit="LBS"> 8097.63 </capacity>
     <contents unit="LBS"> 8097.63 </contents>
  </tank>

  <tank number="2" type="FUEL">
     <location unit="IN">
       <x> 790.80 </x>
       <y>   0.00 </y>
       <z> -29.07 </z>
     </location>
     <capacity unit="LBS"> 8097.63 </capacity>
     <contents unit="LBS"> 8097.63 </contents>
  </tank>

 </propulsion>

 
 <system file="GNCUtilities" />

 <system file="Autopilot">
      <property value="0.523"> guidance/roll-angle-limit </property>
      <property value="0.174"> guidance/roll-rate-limit </property>
 </system>
 
 <autopilot file="global5000ap" />


 <flight_control name="FCS: global5000">

  <channel name="Pitch">

   <summer name="Pitch Trim Sum">
      <input>ap/elevator_cmd</input> 
      <input>fcs/elevator-cmd-norm</input>
      <input>fcs/pitch-trim-cmd-norm</input>
      <clipto>
        <min> -1 </min>
        <max>  1 </max>
      </clipto>
   </summer>

   <aerosurface_scale name="Elevator Control">
      <input>fcs/pitch-trim-sum</input>
      <range>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </range>
      <output>fcs/elevator-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="elevator normalization">
      <input>fcs/elevator-pos-rad</input>
      <domain>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </domain>
      <range>
        <min> -1 </min>
        <max>  1 </max>
      </range>
      <output>fcs/elevator-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <channel name="Roll">

   <summer name="Roll Trim Sum">
      <input>fcs/aileron-cmd-norm</input>
      <input>fcs/roll-trim-cmd-norm</input>
      <clipto>
        <min> -1 </min>
        <max>  1 </max>
      </clipto>
   </summer>

   <aerosurface_scale name="Left Aileron Control">
      <input>fcs/roll-trim-sum</input>
      <range>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </range>
      <output>fcs/left-aileron-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="Right Aileron Control">
      <input>fcs/roll-trim-sum</input>
      <range>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </range>
      <output>fcs/right-aileron-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="left aileron normalization">
      <input>fcs/left-aileron-pos-rad</input>
      <domain>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </domain>
      <range>
        <min> -1 </min>
        <max>  1 </max>
      </range>
      <output>fcs/left-aileron-pos-norm</output>
   </aerosurface_scale>

   <aerosurface_scale name="right aileron normalization">
      <input>fcs/right-aileron-pos-rad</input>
      <domain>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </domain>
      <range>
        <min> -1 </min>
        <max>  1 </max>
      </range>
      <output>fcs/right-aileron-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <property value="1">fcs/yaw-damper-enable</property>
  <channel name="Yaw">

   <summer name="Rudder Command Sum">
      <input>fcs/rudder-cmd-norm</input>
      <input>fcs/yaw-trim-cmd-norm</input>
      <clipto>
        <min> -1 </min>
        <max>  1 </max>
      </clipto>
   </summer>

   <scheduled_gain name="Yaw Damper Rate">
      <input>velocities/r-aero-rad_sec</input>
      <table>
        <independentVar lookup="row">velocities/ve-kts</independentVar>
         <tableData>
            30     0.00
            60     2.00
         </tableData>
      </table>
      <gain>fcs/yaw-damper-enable</gain>
   </scheduled_gain>

   <summer name="Rudder Sum">
      <input>fcs/rudder-command-sum</input>
      <input>fcs/yaw-damper-rate</input>
      <clipto>
        <min> -1.1 </min>
        <max>  1.1 </max>
      </clipto>
   </summer>

   <aerosurface_scale name="Rudder Control">
      <input>fcs/rudder-sum</input>
      <domain>
        <min> -1.1 </min>
        <max>  1.1 </max>
      </domain>
      <range>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </range>
      <output>fcs/rudder-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="rudder normalization">
      <input>fcs/rudder-pos-rad</input>
      <domain>
        <min> -0.35 </min>
        <max>  0.35 </max>
      </domain>
      <range>
        <min> -1 </min>
        <max>  1 </max>
      </range>
      <output>fcs/rudder-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <channel name="Flaps">
   <kinematic name="Flaps Control">
     <input>fcs/flap-cmd-norm</input>
     <traverse>
       <setting>
          <position>  0 </position>
          <time>      0 </time>
       </setting>
       <setting>
          <position> 15 </position>
          <time>      4 </time>
       </setting>
       <setting>
          <position> 30 </position>
          <time>      3 </time>
       </setting>
     </traverse>
     <output>fcs/flap-pos-deg</output>
   </kinematic>

   <aerosurface_scale name="flap normalization">
      <input>fcs/flap-pos-deg</input>
      <domain>
        <min>  0 </min>
        <max> 30 </max>
      </domain>
      <range>
        <min> 0 </min>
        <max> 1 </max>
      </range>
      <output>fcs/flap-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <channel name="Landing Gear">
   <kinematic name="Gear Control">
     <input>gear/gear-cmd-norm</input>
     <traverse>
       <setting>
          <position> 0 </position>
          <time>     0 </time>
       </setting>
       <setting>
          <position> 1 </position>
          <time>     5 </time>
       </setting>
     </traverse>
     <output>gear/gear-pos-norm</output>
   </kinematic>

  </channel>

  <channel name="Speedbrake">
   <kinematic name="Speedbrake Control">
     <input>fcs/speedbrake-cmd-norm</input>
     <traverse>
       <setting>
          <position> 0 </position>
          <time>     0 </time>
       </setting>
       <setting>
          <position> 1 </position>
          <time>     1 </time>
       </setting>
     </traverse>
     <output>fcs/speedbrake-pos-norm</output>
   </kinematic>

  </channel>

 </flight_control>

 <aerodynamics>

        <function name="aero/function/kCDge">
            <description>Change_in_drag_due_to_ground_effect</description>
            <table>
                <independentVar>aero/h_b-mac-ft</independentVar>
                <tableData>
                    0.0000	0.0480
                    0.1000	0.5150
                    0.1500	0.6290
                    0.2000	0.7090
                    0.3000	0.8150
                    0.4000	0.8820
                    0.5000	0.9280
                    0.6000	0.9620
                    0.7000	0.9880
                    0.8000	1.0000
                </tableData>
            </table>
        </function>
 
	 <function name="aero/function/kCLge">
	    <description>Change_in_lift_due_to_ground_effect</description>
	    <table>
		<independentVar>aero/h_b-mac-ft</independentVar>
		<tableData>
		    0.0000	1.2030
		    0.1000	1.1270
		    0.1500	1.0900
		    0.2000	1.0730
		    0.3000	1.0460
		    0.4000	1.0280
		    0.5000	1.0190
		    0.6000	1.0130
		    0.7000	1.0080
		    0.8000	1.0060
		    0.9000	1.0030
		    1.0000	1.0020
		    1.1000	1.0000
		</tableData>
	    </table>
	</function>

        <function name="aero/function/kCLsb">
            <description>Change_in_lift_due_to_speed_brake</description>
            <table>
                <independentVar>fcs/speedbrake-pos-norm</independentVar>
                <tableData>
                    0.0000	1.0
                    0.1000	0.85
                </tableData>
            </table>
        </function>

        <function name="aero/function/kCLsp">
            <description>Change_in_lift_due_to_spoilers</description>
            <table>
                <independentVar>fcs/spoiler-pos-norm</independentVar>
                <tableData>
                    0.0000	1.0
                    0.1000	0.6
                </tableData>
            </table>
        </function>


  <axis name="LIFT">

    <function name="aero/force/Lift_alpha">
      <description>Lift due to alpha</description>
      <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>
              -0.20 -0.880
               0.00  0.000
               0.23  1.000
               0.60  0.880
            </tableData>
          </table>
      </product>
    </function>

    <function name="aero/force/Lift_flap">
       <description>Delta Lift due to flaps</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/flap-pos-deg</property>
           <value> 0.05000 </value>
       </product>
    </function>

    <function name="aero/force/Lift_speedbrake">
       <description>Delta Lift due to speedbrake</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/speedbrake-pos-norm</property>
           <value>-0.1</value>
       </product>
    </function>

    <function name="aero/force/Lift_elevator">
       <description>Lift due to Elevator Deflection</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/elevator-pos-rad</property>
           <value>0.2</value>
       </product>
    </function>

  </axis>

  <axis name="DRAG">

    <function name="aero/force/Drag_basic">
       <description>Drag at zero lift</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/alpha-rad</independentVar>
            <tableData>
             -1.57    1.504
             -0.26    0.030
              0.00    0.024
              0.26    0.030
              1.57    1.504
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/force/Drag_induced">
       <description>Induced drag</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/cl-squared</property>
           <value>0.043</value>
         </product>
    </function>

    <function name="aero/force/Drag_mach">
       <description>Drag due to mach</description>
        <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">velocities/mach</independentVar>
            <tableData>
                0.00      0.000
                0.79      0.000
                1.10      0.023
                1.80      0.015
            </tableData>
          </table>
        </product>
    </function>

    <function name="aero/force/Drag_flap">
       <description>Drag due to flaps</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/flap-pos-deg</property>
           <value> 0.00197 </value>
         </product>
    </function>

    <function name="aero/force/Drag_gear">
       <description>Drag due to gear</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>gear/gear-pos-norm</property>
           <value>0.015</value>
         </product>
    </function>

    <function name="aero/force/Drag_speedbrake">
       <description>Drag due to speedbrakes</description>
         <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>fcs/speedbrake-pos-norm</property>
           <value>0.02</value>
         </product>
    </function>

    <function name="aero/force/Drag_beta">
       <description>Drag due to sideslip</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <table>
            <independentVar lookup="row">aero/beta-rad</independentVar>
            <tableData>
              -1.57    1.230
              -0.26    0.050
               0.00    0.000
               0.26    0.050
               1.57    1.230
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/force/Drag_elevator">
       <description>Drag due to Elevator Deflection</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <abs><property>fcs/elevator-pos-norm</property></abs>
           <value>0.04</value>
       </product>
    </function>

  </axis>

  <axis name="SIDE">

    <function name="aero/force/Side_beta">
       <description>Side force due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>aero/beta-rad</property>
           <value>-1</value>
       </product>
    </function>

  </axis>

  <axis name="ROLL">

    <function name="aero/moment/Roll_beta">
       <description>Roll moment due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <value>-0.1</value>
       </product>
    </function>

    <function name="aero/moment/Roll_damp">
       <description>Roll moment due to roll rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/p-aero-rad_sec</property>
           <value>-0.4</value>
       </product>
    </function>

    <function name="aero/moment/Roll_yaw">
       <description>Roll moment due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value>0.15</value>
       </product>
    </function>

    <function name="aero/moment/Roll_aileron">
       <description>Roll moment due to aileron</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/bw-ft</property>
          <property>fcs/left-aileron-pos-rad</property>
          <value>0.1</value>
       </product>
    </function>

    <function name="aero/moment/Roll_rudder">
       <description>Roll moment due to rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value>0.01</value>
       </product>
    </function>

  </axis>

  <axis name="PITCH">

    <function name="aero/moment/Pitch_alpha">
       <description>Pitch moment due to alpha</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/alpha-rad</property>
           <value>-0.6</value>
       </product>
    </function>

    <function name="aero/moment/Pitch_elevator">
       <description>Pitch moment due to elevator</description>
       <product>
          <property>aero/qbar-psf</property>
          <property>metrics/Sw-sqft</property>
          <property>metrics/cbarw-ft</property>
          <property>fcs/elevator-pos-rad</property>
          <table>
            <independentVar lookup="row">velocities/mach</independentVar>
            <tableData>
              0.0     -1.200
              2.0     -0.300
            </tableData>
          </table>
       </product>
    </function>

    <function name="aero/moment/Pitch_damp">
       <description>Pitch moment due to pitch rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>velocities/q-aero-rad_sec</property>
           <value>-17</value>
       </product>
    </function>

    <function name="aero/moment/Pitch_alphadot">
       <description>Pitch moment due to alpha rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/cbarw-ft</property>
           <property>aero/ci2vel</property>
           <property>aero/alphadot-rad_sec</property>
           <value>-6</value>
       </product>
    </function>

  </axis>

  <axis name="YAW">

    <function name="aero/moment/Yaw_beta">
       <description>Yaw moment due to beta</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/beta-rad</property>
           <value>0.12</value>
       </product>
    </function>

    <function name="aero/moment/Yaw_damp">
       <description>Yaw moment due to yaw rate</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>aero/bi2vel</property>
           <property>velocities/r-aero-rad_sec</property>
           <value>-0.15</value>
       </product>
    </function>

    <function name="aero/moment/Yaw_rudder">
       <description>Yaw moment due to rudder</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/rudder-pos-rad</property>
           <value>-0.1</value>
       </product>
    </function>

    <function name="aero/moment/Yaw_aileron">
       <description>Adverse yaw</description>
       <product>
           <property>aero/qbar-psf</property>
           <property>metrics/Sw-sqft</property>
           <property>metrics/bw-ft</property>
           <property>fcs/left-aileron-pos-rad</property>
           <value>0</value>
       </product>
    </function>

  </axis>

 </aerodynamics>

    <output name="global5000.csv" rate="60" type="CSV">
	<property> aero/alphadot-deg_sec </property>
	<property> aero/betadot-deg_sec  </property>
	<property> velocities/ve-kts </property>
	<property> velocities/vc-kts </property>
	<property> velocities/mach </property>
	<property> aero/beta-deg </property>
	<property> aero/alpha-deg </property>
	<property> velocities/vtrue-kts </property>
	<property> gear/gear-cmd-norm </property>
	<property> aero/coefficient/CLalpha </property>
	<property> position/h-agl-ft </property>


	<simulation>       OFF </simulation>
	<atmosphere>       OFF </atmosphere>
	<massprops>        OFF </massprops>
	<aerosurfaces>     OFF  </aerosurfaces>
	<rates>            OFF  </rates>
	<velocities>       OFF </velocities>
	<forces>           OFF </forces>
	<moments>          OFF </moments>
	<position>         OFF  </position>
	<coefficients>     OFF </coefficients>
	<ground_reactions> OFF </ground_reactions>
	<fcs>              OFF  </fcs>
	<propulsion>       ON </propulsion>
    </output>
</fdm_config>