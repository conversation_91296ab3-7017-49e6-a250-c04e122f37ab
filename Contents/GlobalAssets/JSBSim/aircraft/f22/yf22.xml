<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="F22" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
        <PERSON><author>  </author>
        <filecreationdate> 2004-01-01 </filecreationdate>
        <version> $Revision: 1.1 $ </version>
        <description> Models an F-22 Raptor </description>
        <license>
         <licenseName>GPL (General Public License)</licenseName>
         <licenseURL>http://www.gnu.org/licenses/gpl.html</licenseURL>
        </license>
        <note>
         This model was created using data that is, or has been, publically
         available by means of technical reports, textbooks, image graphs or
         published code. This aircraft description file is in no way related
         to the manufacturer of the real aircraft.
         Neither the name of (any of) the authors nor the names of (any of) the
         manufacturers may be used to endorse or promote products derived from
         this file.

         The data is provided ''as is'' and any express or implied
         warranties, including, but not limitted to the implied warranties of
         merchantability and fitness for a particular purpose are disclaimed.
        </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 840.0 </wingarea>
        <wingspan unit="FT"> 44.49 </wingspan>
        <chord unit="FT"> 23.06 </chord>
        <htailarea unit="FT2"> 238.9 </htailarea>
        <htailarm unit="FT"> 18.66 </htailarm>
        <vtailarea unit="FT2"> 158.2 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 446.5 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 148.8 </x>
            <y> 0 </y>
            <z> 38 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 100 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance negated_crossproduct_inertia="true">
        <ixx unit="SLUG*FT2"> 40843 </ixx>
        <iyy unit="SLUG*FT2"> 204751 </iyy>
        <izz unit="SLUG*FT2"> 258201 </izz>
        <ixz unit="SLUG*FT2"> 520 </ixz>
        <emptywt unit="LBS"> 43430 </emptywt>
        <location name="CG" unit="IN">
            <x> 446.5 </x>
            <y> 0 </y>
            <z> -18.6 </z>
        </location>
        <pointmass name="Pilot">
            <weight unit="LBS"> 230 </weight>
            <location name="POINTMASS" unit="IN">
                <x> 148.8 </x>
                <y> 0 </y>
                <z> 20 </z>
            </location>
        </pointmass>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 96.7 </x>
                <y> 0 </y>
                <z> -89.3 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 18603.4 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 6201.1 </damping_coeff>
            <max_steer unit="DEG"> 5 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 468.8 </x>
                <y> -48 </y>
                <z> -89.3 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 62011.2 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 12402.2 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 468.8 </x>
                <y> 48 </y>
                <z> -89.3 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 62011.2 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 12402.2 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>1</retractable>
        </contact>
    </ground_reactions>
    <propulsion>
        <engine file="F119-PW-1">
            <feed>0</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 684.131 </x>
                    <y> -20 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="F119-PW-1">
            <feed>1</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 684.131 </x>
                    <y> 20 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <tank type="FUEL">
            <location unit="IN">
                <x> 446.478 </x>
                <y> 0 </y>
                <z> -18.6033 </z>
            </location>
            <capacity unit="LBS"> 7000 </capacity>
            <contents unit="LBS"> 3500 </contents>
        </tank>
        <tank type="FUEL">
            <location unit="IN">
                <x> 446.478 </x>
                <y> 0 </y>
                <z> -18.6033 </z>
            </location>
            <capacity unit="LBS"> 7000 </capacity>
            <contents unit="LBS"> 3500 </contents>
        </tank>
    </propulsion>
    <flight_control name="FCS: F22">
        <channel name="Throttle">
            <pure_gain name="Throttle1">
                <input>fcs/throttle-cmd-norm</input>
                <gain>2</gain>
                <output>fcs/throttle-pos-norm</output>
            </pure_gain>
        </channel>

        <channel name="Pitch">
            <summer name="Pitch Trim Sum">
                <input>fcs/elevator-cmd-norm</input>
                <input>fcs/pitch-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Elevator Control">
                <input>fcs/pitch-trim-sum</input>
                <range>
                    <min>-0.35</min>
                    <max>0.3</max>
                </range>
                <output>fcs/elevator-pos-rad</output>
            </aerosurface_scale>
        </channel>
        <channel name="Roll">
            <summer name="Roll Trim Sum">
                <input>fcs/aileron-cmd-norm</input>
                <input>fcs/roll-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Left Aileron Control">
                <input>fcs/roll-trim-sum</input>
                <range>
                    <min>-0.35</min>
                    <max>0.35</max>
                </range>
                <output>fcs/left-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right Aileron Control">
                <input>-fcs/roll-trim-sum</input>
                <range>
                    <min>-0.35</min>
                    <max>0.35</max>
                </range>
                <output>fcs/right-aileron-pos-rad</output>
            </aerosurface_scale>
        </channel>
        <channel name="Yaw">
            <summer name="Rudder Command Sum">
                <input>fcs/rudder-cmd-norm</input>
                <input>fcs/yaw-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <scheduled_gain name="Yaw Damper Rate">
                <input>velocities/r-aero-rad_sec</input>
                <table>
                  <independentVar>aero/qbar-psf</independentVar>
                  <tableData>
                     3.00  0.00
                    11.00  2.00
                  </tableData>
                </table>
            </scheduled_gain>
            <scheduled_gain name="Yaw Damper Beta">
                <input>aero/beta-rad</input>
                <table>
                  <independentVar>aero/qbar-psf</independentVar>
                  <tableData>
                     3.00  0.00
                    11.00  0.00
                  </tableData>
                </table>
            </scheduled_gain>

            <summer name="Yaw Damper Sum">
                <input>fcs/yaw-damper-beta</input>
                <input>fcs/yaw-damper-rate</input>
                <clipto>
                    <min>-0.2</min>
                    <max>0.2</max>
                </clipto>
            </summer>

            <scheduled_gain name="Yaw Damper Final">
                <input>fcs/yaw-damper-sum</input>
                <table>
                  <independentVar>aero/qbar-psf</independentVar>
                  <tableData>
                    2.99  0.0
                    3.00  1.0
                  </tableData>
                </table>
            </scheduled_gain>

            <summer name="Rudder Sum">
                <input>fcs/rudder-command-sum</input>
                <input>fcs/yaw-damper-final</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Rudder Control">
                <input>fcs/rudder-sum</input>
                <range>
                    <min>-0.35</min>
                    <max>0.35</max>
                </range>
                <output>fcs/rudder-pos-rad</output>
            </aerosurface_scale>
        </channel>
        <channel name="Flaps">
            <switch name="Flap Command">
                <default value="0"/>
                <test logic="OR" value="1">
                    velocities/vc-kts lt 250
                </test>
            </switch>
            <kinematic name="Flaps Control">
                <input>fcs/flap-command</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>15</position>
                        <time>4</time>
                    </setting>
                    <setting>
                        <position>30</position>
                        <time>3</time>
                    </setting>
                </traverse>
                <output>fcs/flap-pos-deg</output>
            </kinematic>
            <aerosurface_scale name="Flap Position Normalizer">
              <input>fcs/flap-pos-deg</input>
              <domain>
                <min>0</min>
                <max>30</max>
              </domain>
              <range>
                <min>0</min>
                <max>1</max>
              </range>
              <output>fcs/flap-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Landing Gear">
            <kinematic name="Gear Control">
                <input>gear/gear-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>5</time>
                    </setting>
                </traverse>
                <output>gear/gear-pos-norm</output>
            </kinematic>
        </channel>
        <channel name="Speedbrake">
            <kinematic name="Speedbrake Control">
                <input>fcs/speedbrake-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>1</time>
                    </setting>
                </traverse>
                <output>fcs/speedbrake-pos-norm</output>
            </kinematic>
        </channel>
    </flight_control>
    <aerodynamics>
        <function name="aero/function/kCLge">
            <description>Change_in_lift_due_to_ground_effect</description>
            <product>
                  <table>
                      <independentVar>aero/h_b-mac-ft</independentVar>
                      <tableData>
                        0.0000  1.3893
                        0.1000  1.2108
                        0.1500  1.1972
                        0.2000  1.2108
                        0.3000  1.1785
                        0.4000  1.0697
                        0.5000  1.0578
                        0.6000  1.0323
                        0.7000  1.0136
                        0.8000  1.0051
                        0.9000  1.0017
                        1.0000  1.0000
                        1.1000  1.0000
                      </tableData>
                  </table>
            </product>
        </function>
        <function name="aero/function/kCDge">
            <description>Change_in_drag_due_to_ground_effect</description>
            <product>
                  <table>
                      <independentVar>aero/h_b-mac-ft</independentVar>
                      <tableData>
                        0.0000  0.5235
                        0.1000  0.4759
                        0.1500  0.5250
                        0.2000  0.5388
                        0.3000  0.5924
                        0.4000  0.6471
                        0.5000  0.6941
                        0.6000  0.7624
                        0.7000  0.8253
                        0.8000  0.8812
                        0.9000  0.9412
                        1.0000  1.0000
                        1.1000  1.0000
                      </tableData>
                  </table>
            </product>
        </function>

        <axis name="DRAG">
            <function name="aero/coefficient/CD0">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCDge</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -1.5700    1.5000
                              -0.2600    0.0480
                              0.0000    0.0240
                              0.2600    0.0480
                              1.5700    1.5000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <property>aero/function/kCDge</property>
                    <value>0.0900</value>
                </product>
            </function>
            <function name="aero/coefficient/CDmach">
                <description>Drag_due_to_mach</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000    0.0000
                              0.8100    0.0000
                              1.1000    0.0230
                              1.8000    0.0150
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDflap">
                <description>Drag_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-norm</property>
                    <property>aero/function/kCDge</property>
                    <value>0.0750</value>
                </product>
            </function>
            <function name="aero/coefficient/CDgear">
                <description>Drag_due_to_gear</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>gear/gear-pos-norm</property>
                    <value>0.0200</value>
                </product>
            </function>
            <function name="aero/coefficient/CDsb">
                <description>Drag_due_to_speedbrakes</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0240</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCDge</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                              -1.5700    1.2300
                              -0.2600    0.0500
                              0.0000    0.0000
                              0.2600    0.0500
                              1.5700    1.2300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <property>aero/function/kCDge</property>
                    <value>0.0750</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.2875</value>
                </product>
            </function>
            <function name="aero/coefficient/CYbdot">
                <description>Side_force_due_to_beta_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/betadot-rad_sec</property>
                    <value>-0.0458</value>
                </product>
            </function>
            <function name="aero/coefficient/CYDr">
                <description>Side_force_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0619</value>
                </product>
            </function>
            <function name="aero/coefficient/CYp">
                <description>Side_force_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>0.0399</value>
                </product>
            </function>
            <function name="aero/coefficient/CYr">
                <description>Side_force_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1218</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.1745    -0.4279
                              0.7854    2.0000
                              1.3963    1.0726
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLadot">
                <description>Lift_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/alphadot-rad_sec</property>
                    <property>aero/ci2vel</property>
                    <value>2.5602</value>
                </product>
            </function>
            <function name="aero/coefficient/CLq">
                <description>Lift_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <property>aero/function/kCLge</property>
                    <property>aero/ci2vel</property>
                    <value>2.7711</value>
                </product>
            </function>
            <function name="aero/coefficient/dCLflap">
                <description>Delta_Lift_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-norm</property>
                    <value>0.3500</value>
                </product>
            </function>
            <function name="aero/coefficient/dCLsb">
                <description>Delta_Lift_due_to_speedbrake</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/CLde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/function/kCLge</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.2000</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.0873    0.0189
                              0.0000    0.0176
                              0.0873    0.0119
                              0.1745    0.0050
                              0.2618    0.0049
                              0.3491    0.0028
                              0.4363    0.0084
                              0.5236    0.0171
                              0.6109    0.0044
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Clbdot">
                <description>Roll_moment_due_to_beta_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/betadot-rad_sec</property>
                    <value>-0.0037</value>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <table>
                        <independentVar>aero/alpha-rad</independentVar>
                        <tableData>
                            0.0000  -0.4000
                            0.3490  -0.3100
                        </tableData>
                    </table>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000    0.0489
                              2.0000    0.0400
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/ClDr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.1745    0.0078
                              -0.0873    0.0070
                              0.0000    0.0059
                              0.0873    0.0043
                              0.1745    0.0023
                              0.2618    0.0007
                              0.3491    -0.0008
                              0.4363    0.0004
                              0.5236    0.0019
                              0.6109    0.0019
                              0.6981    0.0017
                              0.7854    0.0009
                              0.8727    0.0003
                              1.0472    0.0000
                              1.2217    0.0000
                              1.3963    0.0000
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/CmDh">
                <description>Pitch_moment_due_to_horizontal_tail_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                      <table>
                        <independentVar lookup="row">aero/alpha-rad</independentVar>
                        <independentVar lookup="column">fcs/elevator-pos-rad</independentVar>
                        <tableData>
                                     -0.5236    0.0       0.4363
                           -0.0873    0.3222 -0.0546 -0.4911
                            0.0000    0.3482  0.0000 -0.4729
                            0.0873    0.3897  0.0286 -0.4782
                            0.1745    0.3689  0.0156 -0.4988
                            0.2618    0.3975  0.0546 -0.4287
                            0.3491    0.3819  0.0442 -0.2722
                            0.4363    0.4131  0.0831 -0.1637
                            0.5236    0.4599  0.1377 -0.0546
                            0.6109    0.4443  0.1325 -0.0530
                            0.6981    0.3819  0.1013 -0.0650
                            0.7854    0.3585  0.0909 -0.0624
                            0.8727    0.3430  0.1299 -0.0364
                            1.0472    0.0208 -0.1117 -0.1975
                            1.2217    0.3248 -0.4521 -0.5144                          
                        </tableData>
                      </table>
                </product>
            </function>
<!--
            <function name="aero/coefficient/CmDtv">
                <description>Pitch_moment_due_to_thrust_vectoring</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/throttle-pos-norm</property>
                      <table>
                        <independentVar lookup="row">aero/alpha-rad</independentVar>
                        <independentVar lookup="column">fcs/thrust-control-pos-rad</independentVar>
                        <tableData>
                                     -0.3491  0.0  0.3491
                           -0.0873    0.4417  0.0 -0.7924
                            0.0000    0.4650  0.0 -0.8366
                            0.0873    0.4448  0.0 -0.8808
                            0.1745    0.4677  0.0 -0.8886
                            0.2618    0.4755  0.0 -0.8600
                            0.3491    0.4625  0.0 -0.6834
                            0.4363    0.4963  0.0 -0.6001
                            0.5236    0.4936  0.0 -0.5508
                            0.6109    0.4781  0.0 -0.5300
                            0.6981    0.5025  0.0 -0.4754
                            0.7854    0.5015  0.0 -0.4443
                            0.8727    0.4209  0.0 -0.4884
                            1.0472    0.4417  0.0 -0.4547
                            1.2217   -0.2988  0.0 -0.4183
                        </tableData>
                      </table>
                </product>
            </function>
-->
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-17.830</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.2570    -1.5611
                              1.8000    -2.0500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CmadotDh">
                <description>Pitch_moment_due_to_horizontal_tail_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>-1.5611</value>
                </product>
            </function>
            <function name="aero/coefficient/CmqDh">
                <description>Pitch_moment_due_to_pitch_rate_and_horizontal_tail_deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>-1.1726</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.1200</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.0873    0.0016
                              0.0000    0.0016
                              0.0873    0.0018
                              0.1745    0.0026
                              0.2618    0.0028
                              0.3491    0.0026
                              0.4363    0.0021
                              0.5236    0.0033
                              0.6109    0.0015
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cnbdot">
                <description>Yaw_moment_due_to_beta_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/betadot-rad_sec</property>
                    <value>-0.0115</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnp">
                <description>Yaw_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.0035</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/CnDr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.1745    -0.0082
                              -0.0873    -0.0128
                              0.0000    -0.0131
                              0.0873    -0.0140
                              0.1745    -0.0154
                              0.2618    -0.0128
                              0.3491    -0.0100
                              0.4363    -0.0077
                              0.5236    -0.0065
                              0.6109    -0.0051
                              0.6981    -0.0041
                              0.7854    -0.0025
                              0.8727    0.0000
                              1.0472    0.0000
                              1.2217    0.0000
                              1.3963    0.0000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cnda">
                <description>Adverse_yaw</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.0000</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
