<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="OV10 Bronco" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
        <author> <PERSON>, based on Aero-Matic </author>
        <filecreationdate> 2001-01-01 </filecreationdate>
        <version> $Revision: 1.25 $ </version>
        <description> OV-10 Bronco </description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 291 </wingarea>
        <wingspan unit="FT"> 40 </wingspan>
        <chord unit="FT"> 7.28 </chord>
        <htailarea unit="FT2"> 46.56 </htailarea>
        <htailarm unit="FT"> 20.8 </htailarm>
        <vtailarea unit="FT2"> 52.38 </vtailarea>
        <vtailarm unit="FT"> 18.5 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 200 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 84.9 </x>
            <y> -18 </y>
            <z> 45 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance>
        <ixx unit="SLUG*FT2"> 19561 </ixx>
        <iyy unit="SLUG*FT2"> 35552 </iyy>
        <izz unit="SLUG*FT2"> 56531 </izz>
        <emptywt unit="LBS">   8640 </emptywt>
        <location name="CG" unit="IN">
            <x> 200.0 </x>
            <y> 0 </y>
            <z> -12.5 </z>
        </location>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 64.9 </x>
                <y> 0 </y>
                <z> -59.9 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 20160 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 1440 </damping_coeff>
            <max_steer unit="DEG"> 35 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 221.0 </x>
                <y> -43.2 </y>
                <z> -58.0 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 72000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 2880 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 221.0 </x>
                <y> 43.2 </y>
                <z> -58.0 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 72000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 2880 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>1</retractable>
        </contact>
    </ground_reactions>
    <propulsion>
        <engine file="T76">
            <feed>0</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 190 </x>
                    <y> -80 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="T76">
            <feed>0</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 190 </x>
                    <y> 80 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
	<!-- Internal Tank -->
        <tank type="FUEL">    
            <location unit="IN">
                <x> 200 </x>
                <y> 0 </y>
                <z> 12 </z>
            </location>
            <capacity unit="LBS"> 1554 </capacity> <!-- must account for the external tank (3004) -->
            <contents unit="LBS"> 1500 </contents>
        </tank>
	<!-- External Tank -->
        <tank type="FUEL">    
            <location unit="IN">
                <x> 200 </x>
                <y> 0 </y>
                <z> -40 </z>
            </location>
            <capacity unit="LBS"> 1554 </capacity>
            <contents unit="LBS"> 1500 </contents>
        </tank>
	<!-- Mk 82 weight -->
        <tank type="FUEL">    
            <location unit="IN">
                <x> 200 </x>
                <y> 21.5 </y>
                <z> -35 </z>
            </location>
            <capacity unit="LBS"> 500 </capacity>
            <contents unit="LBS"> 500 </contents>
        </tank>
        <tank type="FUEL">    
            <location unit="IN">
                <x> 200 </x>
                <y> -21.5 </y>
                <z> -35 </z>
            </location>
            <capacity unit="LBS"> 500 </capacity>
            <contents unit="LBS"> 500 </contents>
        </tank>
	<!-- LAU 68 weight -->
        <tank type="FUEL">    
            <location unit="IN">
                <x> 200 </x>
                <y> 35.8 </y>
                <z> -30 </z>
            </location>
            <capacity unit="LBS"> 190 </capacity>
            <contents unit="LBS"> 190 </contents>
        </tank>
        <tank type="FUEL">    
            <location unit="IN">
                <x> 200 </x>
                <y> -35.8 </y>
                <z> -30 </z>
            </location>
            <capacity unit="LBS"> 190 </capacity>
            <contents unit="LBS"> 190 </contents>
        </tank>
	<!-- Pilots weight -->
        <tank type="FUEL">    
            <location unit="IN">
                <x> 90 </x>
                <y> 0 </y>
                <z> 10 </z>
            </location>
            <capacity unit="LBS"> 191 </capacity>
            <contents unit="LBS"> 191 </contents>
        </tank>
        <tank type="FUEL">    
            <location unit="IN">
                <x> 150 </x>
                <y> 0 </y>
                <z> 10 </z>
            </location>
            <capacity unit="LBS"> 192 </capacity>
            <contents unit="LBS"> 192 </contents>
        </tank>
	<!-- Rear payload weight -->
        <tank type="FUEL">    
            <location unit="IN">
                <x> 249.6 </x>
                <y> 0 </y>
                <z> 20 </z>
            </location>
            <capacity unit="LBS"> 200 </capacity>
            <contents unit="LBS"> 200 </contents>
        </tank>
	<!-- Paratroopers weight -->
        <tank type="FUEL">    
            <location unit="IN">
                <x> 249.6 </x>
                <y> 0 </y>
                <z> 20 </z>
            </location>
            <capacity unit="LBS"> 1100 </capacity>
            <contents unit="LBS"> 1100 </contents>
        </tank>
    </propulsion>
    <flight_control name="FCS: OV10">

        <channel name="Pitch">
            <summer name="Pitch Trim Sum">
                <input>fcs/elevator-cmd-norm</input>
                <input>fcs/pitch-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max> 1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Elevator Control">
                <input>fcs/pitch-trim-sum</input>
                <range>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </range>
                <output>fcs/elevator-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Elevator Normalized">
                <input>fcs/elevator-pos-rad</input>
                <domain>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max> 1</max>
                </range>
                <output>fcs/elevator-pos-norm</output>
            </aerosurface_scale>
        </channel>

        <channel name="Roll">
            <summer name="Roll Trim Sum">
                <input>fcs/aileron-cmd-norm</input>
                <input>fcs/roll-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max> 1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Left Aileron Control">
                <input>fcs/roll-trim-sum</input>
                <range>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </range>
                <output>fcs/left-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right Aileron Control">
                <input>-fcs/roll-trim-sum</input>
                <range>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </range>
                <output>fcs/right-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Left aileron Normalized">
                <input>fcs/left-aileron-pos-rad</input>
                <domain>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max> 1</max>
                </range>
                <output>fcs/left-aileron-pos-norm</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right aileron Normalized">
                <input>fcs/right-aileron-pos-rad</input>
                <domain>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max> 1</max>
                </range>
                <output>fcs/right-aileron-pos-norm</output>
            </aerosurface_scale>
        </channel>

        <channel name="Yaw">
            <summer name="Rudder Command Sum">
                <input>fcs/rudder-cmd-norm</input>
                <input>fcs/yaw-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Rudder Control">
                <input>fcs/rudder-command-sum</input>
                <range>
                    <min>-0.35</min>
                    <max>0.35</max>
                </range>
                <output>fcs/rudder-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Rudder Normalized">
                <input>fcs/rudder-pos-rad</input>
                <domain>
                    <min>-0.35</min>
                    <max> 0.35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max> 1</max>
                </range>
                <output>fcs/rudder-pos-norm</output>
            </aerosurface_scale>
        </channel>

        <channel name="Flaps">
            <kinematic name="Flaps Control">
                <input>fcs/flap-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>0.5</position>
                        <time>4</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>3</time>
                    </setting>
                </traverse>
                <output>fcs/flap-pos-norm</output>
            </kinematic>
        </channel>

        <channel name="Landing Gear">
            <kinematic name="Gear Control">
                <input>gear/gear-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>5</time>
                    </setting>
                </traverse>
                <output>gear/gear-pos-norm</output>
            </kinematic>
        </channel>

    </flight_control>

    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CD0">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -1.5700	1.5000
                              -0.2600	0.0700
                              0.0000	0.0350
                              0.2600	0.0700
                              1.5700	1.5000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <value>0.0410</value>
                </product>
            </function>
            <function name="aero/coefficient/CDmach">
                <description>Drag_due_to_mach</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.0000
                              0.7200	0.0000
                              1.1000	0.0230
                              1.8000	0.0150
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDflap">
                <description>Drag_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-deg</property>
                    <value>0.0013</value>
                </product>
            </function>
            <function name="aero/coefficient/CDgear">
                <description>Drag_due_to_gear</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>gear/gear-pos-norm</property>
                    <value>0.0500</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                              -1.5700	1.2300
                              -0.2600	0.0700
                              0.0000	0.0000
                              0.2600	0.0700
                              1.5700	1.2300
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.0390</value>
                </product>
            </function>
            <function name="aero/coefficient/dCDprop">
                <description>Drag_due_to_prop_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>fcs/throttle-pos-norm</independentVar>
                          <tableData>
                              0.0000	0.0700
                              0.0500	0.0000
                              1.0000	0.0000
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-1.0000</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/Lalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                             -0.2000   -0.7200
                              0.0000	0.2400
                              0.2200	1.3000
                              0.6000	0.6640
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/dLflap">
                <description>Delta_Lift_due_to_flaps</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/flap-pos-norm</property>
                    <value>0.66</value>
                </product>
            </function>
            <function name="aero/coefficient/Lde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>fcs/elevator-pos-norm</property> 
                    <value>0.2000</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.1700
                              2.0000	0.0570
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0100</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmde">
                <description>Pitch_moment_due_to_elevator</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	-1.0000
                              2.0000	-0.2500
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-22.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-8.0000</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.2500</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnda">
                <description>Adverse_yaw</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>-0.0050</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
