<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="A320-200" version="2.0" release="BETA"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
      <author> Unknown </author>
      <filecreationdate> 2003-01-01 </filecreationdate>
      <version> Version </version>
      <description> Airbus A320 </description>
      <note>
        This model was created using publicly available data, publicly available
        technical reports, textbooks, and guesses. It contains no proprietary or
        restricted data. If this model has been validated at all, it would be
        only to the extent that it seems to "fly right", and that it possibly
        complies with published, publicly known, performance data (maximum speed,
        endurance, etc.). Thus, this model is meant for educational and entertainment
        purposes only.

        This simulation model is not endorsed by the manufacturer. This model is not
        to be sold.
      </note>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 1317 </wingarea>
        <wingspan unit="FT"> 111.3 </wingspan>
        <chord unit="FT"> 14.1 </chord>
        <htailarea unit="FT2"> 333.6 </htailarea>
        <htailarm unit="FT"> 44.4 </htailarm>
        <vtailarea unit="FT2"> 231.3 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 672 </x>
            <y> 0 </y>
            <z> 20 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 80 </x>
            <y> -30 </y>
            <z> 70 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 661.1 </x>
            <y> 0 </y>
            <z> -37 </z>
        </location>
    </metrics>

    <mass_balance negated_crossproduct_inertia="true">
        <ixx unit="SLUG*FT2"> 942877 </ixx>
        <iyy unit="SLUG*FT2"> 2.78892e+06 </iyy>
        <izz unit="SLUG*FT2"> 3.59757e+06 </izz>
        <ixy unit="SLUG*FT2"> -0 </ixy>
        <ixz unit="SLUG*FT2"> -10000 </ixz>
        <iyz unit="SLUG*FT2"> -0 </iyz>
        <emptywt unit="LBS"> 111000 </emptywt>
        <location name="CG" unit="IN">
            <x> 672 </x>
            <y> 0 </y>
            <z> -40 </z>
        </location>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x> 196.1 </x>
                <y> 0 </y>
                <z> -138.2 </z>
            </location>
            <static_friction> 0.5 </static_friction>
            <dynamic_friction> 0.8 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 100000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 5000 </damping_coeff>
            <max_steer unit="DEG"> 60 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 688.7 </x>
                <y> -144.4 </y>
                <z> -142 </z>
            </location>
            <static_friction> 0.5 </static_friction>
            <dynamic_friction> 0.8 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 150000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 12000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>1</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 688.7 </x>
                <y> 144.4 </y>
                <z> -142 </z>
            </location>
            <static_friction> 0.5 </static_friction>
            <dynamic_friction> 0.8 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 150000 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 12000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>1</retractable>
        </contact>
        <!-- <contact type="STRUCTURE" name="TAIL_1">
            <location unit="IN">
                <x> 987 </x>
                <y> 0 </y>
                <z> -55.7 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact> -->
        <!-- <contact type="STRUCTURE" name="ENG_1">
            <location unit="IN">
                <x> 515.4 </x>
                <y> 226.2 </y>
                <z> -104.3 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact> -->
        <!-- <contact type="STRUCTURE" name="ENG_2">
            <location unit="IN">
                <x> 515.4 </x>
                <y> -226.2 </y>
                <z> -104.3 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact> -->
        <contact type="STRUCTURE" name="WING_TIP_1">
            <location unit="IN">
                <x> 846.1 </x>
                <y> -667.6 </y>
                <z> -37.4 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="STRUCTURE" name="WING_TIP_2">
            <location unit="IN">
                <x> 846.1 </x>
                <y> 667.6 </y>
                <z> -37.4 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="STRUCTURE" name="NOSE_TIP">
            <location unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <!-- <contact type="STRUCTURE" name="NOSE_TOP">
            <location unit="IN">
                <x> 131.6 </x>
                <y> 0 </y>
                <z> 92.6 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact> -->
        <!-- <contact type="STRUCTURE" name="TAIL_TOP">
            <location unit="IN">
                <x> 1382.6 </x>
                <y> 0 </y>
                <z> 340 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact> -->
        <!-- <contact type="STRUCTURE" name="TAIL_TIP">
            <location unit="IN">
                <x> 1476.7 </x>
                <y> 0 </y>
                <z> 58.9 </z>
            </location>
            <static_friction> 0.2 </static_friction>
            <dynamic_friction> 0.2 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 1.5e+06 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 60000 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact> -->
    </ground_reactions>
    <propulsion>
        <engine file="CFM56_5">
            <feed>0</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 670 </x>
                    <y> -200 </y>
                    <z> -45 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <engine file="CFM56_5">
            <feed>1</feed>
            <thruster file="direct">
                <location unit="IN">
                    <x> 670 </x>
                    <y> 200 </y>
                    <z> -45 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
            </thruster>
        </engine>
        <tank type="FUEL">    <!-- Tank number 0 -->
            <location unit="IN">
                <x> 600 </x>
                <y> -90 </y>
                <z> -20 </z>
            </location>
            <capacity unit="LBS"> 23940 </capacity>
            <contents unit="LBS"> 15000 </contents>
        </tank>
        <tank type="FUEL">    <!-- Tank number 1 -->
            <location unit="IN">
                <x> 600 </x>
                <y> 90 </y>
                <z> -20 </z>
            </location>
            <capacity unit="LBS"> 23940 </capacity>
            <contents unit="LBS"> 15000 </contents>
        </tank>
    </propulsion>
    <flight_control name="FCS: A320">
        <channel name="Pitch">
            <summer name="Pitch Trim Sum">
                <input>fcs/elevator-cmd-norm</input>
                <input>fcs/pitch-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Elevator Control">
                <input>fcs/pitch-trim-sum</input>
                <gain>0.018</gain>
                <range>
                    <min>-25</min>
                    <max>35</max>
                </range>
                <output>fcs/elevator-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Elevator position normalized">
                <input>fcs/elevator-pos-deg</input>
                <domain>
                    <min>-25</min>
                    <max>35</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max>1</max>
                </range>
                <output>fcs/elevator-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Roll">
            <summer name="Roll Trim Sum">
                <input>fcs/aileron-cmd-norm</input>
                <input>fcs/roll-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Left Aileron Control">
                <input>fcs/roll-trim-sum</input>
                <gain>0.02</gain>
                <range>
                    <min>-20</min>
                    <max>15</max>
                </range>
                <output>fcs/left-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right Aileron Control">
                <input>-fcs/roll-trim-sum</input>
                <gain>0.02</gain>
                <range>
                    <min>-20</min>
                    <max>15</max>
                </range>
                <output>fcs/right-aileron-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Left Aileron position normalized">
                <input>fcs/left-aileron-pos-deg</input>
                <domain>
                    <min>-20</min>
                    <max>15</max>
                </domain>
                <range>
                    <min>-0.75</min>
                    <max>1</max>
                </range>
                <output>fcs/left-aileron-pos-norm</output>
            </aerosurface_scale>

            <aerosurface_scale name="Right Aileron position normalized">
                <input>fcs/right-aileron-pos-deg</input>
                <domain>
                    <min>-20</min>
                    <max>15</max>
                </domain>
                <range>
                    <min>-0.75</min>
                    <max>1</max>
                </range>
                <output>fcs/right-aileron-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Yaw">
            <pure_gain name="Yaw Damper Rate">
                <input>velocities/r-rad_sec</input>
                <gain>2</gain>
            </pure_gain>

            <pure_gain name="Yaw Damper Beta">
                <input>aero/beta-rad</input>
                <gain>-5</gain>
            </pure_gain>

            <summer name="Yaw Trim Sum">
                <input>fcs/rudder-cmd-norm</input>
                <input>fcs/yaw-trim-cmd-norm</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <summer name="Rudder Sum">
                <input>fcs/yaw-trim-sum</input>
                <input>fcs/yaw-damper-rate</input>
                <input>fcs/yaw-damper-beta</input>
                <clipto>
                    <min>-1</min>
                    <max>1</max>
                </clipto>
            </summer>

            <aerosurface_scale name="Rudder Control">
                <input>fcs/rudder-sum</input>
                <gain>0.01745</gain>
                <range>
                    <min>-25</min>
                    <max>25</max>
                </range>
                <output>fcs/rudder-pos-rad</output>
            </aerosurface_scale>

            <aerosurface_scale name="Rudder position normalized">
                <input>fcs/rudder-pos-deg</input>
                <domain>
                    <min>-25</min>
                    <max>25</max>
                </domain>
                <range>
                    <min>-1</min>
                    <max>1</max>
                </range>
                <output>fcs/rudder-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Flaps">
            <kinematic name="Flaps Control">
                <input>fcs/flap-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>4</time>
                    </setting>
                    <setting>
                        <position>2</position>
                        <time>4</time>
                    </setting>
                    <setting>
                        <position>5</position>
                        <time>3</time>
                    </setting>
                    <setting>
                        <position>10</position>
                        <time>3</time>
                    </setting>
                    <setting>
                        <position>15</position>
                        <time>3</time>
                    </setting>
                    <setting>
                        <position>25</position>
                        <time>3</time>
                    </setting>
                    <setting>
                        <position>30</position>
                        <time>2</time>
                    </setting>
                    <setting>
                        <position>40</position>
                        <time>2</time>
                    </setting>
                </traverse>
                <output>fcs/flap-pos-deg</output>
            </kinematic>

            <aerosurface_scale name="Flap position normalized">
                <input>fcs/flap-pos-deg</input>
                <domain>
                    <min>0</min>
                    <max>40</max>
                </domain>
                <range>
                    <min>0</min>
                    <max>1</max>
                </range>
                <output>fcs/flap-pos-norm</output>
            </aerosurface_scale>
        </channel>
        <channel name="Landing Gear">
            <kinematic name="Gear Control">
                <input>gear/gear-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>5</time>
                    </setting>
                </traverse>
                <output>gear/gear-pos-norm</output>
            </kinematic>
        </channel>
        <channel name="Speedbrake">
            <kinematic name="Speedbrake">
                <input>fcs/speedbrake-cmd-norm</input>
                <traverse>
                    <setting>
                        <position>0</position>
                        <time>0</time>
                    </setting>
                    <setting>
                        <position>1</position>
                        <time>2</time>
                    </setting>
                </traverse>
                <output>fcs/speedbrake-pos-norm</output>
            </kinematic>
        </channel>
    </flight_control>
    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CDo">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <value>0.016</value>
                </product>
            </function>
            <function name="aero/coefficient/CDalpha">
                <description>Drag_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                          <tableData>
                                0.0000	1.0000	25.0000	40.0000
                              -0.0873	0.0041	0.0000	0.0005	0.0014
                              -0.0698	0.0013	0.0004	0.0025	0.0041
                              -0.0524	0.0001	0.0023	0.0059	0.0084
                              -0.0349	0.0003	0.0057	0.0108	0.0141
                              -0.0175	0.0020	0.0105	0.0172	0.0212
                              0.0000	0.0052	0.0168	0.0251	0.0399
                              0.0175	0.0099	0.0248	0.0346	0.0502
                              0.0349	0.0162	0.0342	0.0457	0.0621
                              0.0524	0.0240	0.0452	0.0583	0.0755
                              0.0698	0.0334	0.0577	0.0724	0.0904
                              0.0873	0.0442	0.0718	0.0881	0.1068
                              0.1047	0.0566	0.0874	0.1053	0.1248
                              0.1222	0.0706	0.1045	0.1240	0.1443
                              0.1396	0.0860	0.1232	0.1442	0.1654
                              0.1571	0.0962	0.1353	0.1573	0.1790
                              0.1745	0.1069	0.1479	0.1708	0.1930
                              0.1920	0.1180	0.1610	0.1849	0.2075
                              0.2094	0.1298	0.1746	0.1995	0.2226
                              0.2269	0.1424	0.1892	0.2151	0.2386
                              0.2443	0.1565	0.2054	0.2323	0.2564
                              0.2618	0.1727	0.2240	0.2521	0.2767
                              0.2793	0.1782	0.2302	0.2587	0.2835
                              0.2967	0.1716	0.2227	0.2507	0.2753
                              0.3142	0.1618	0.2115	0.2388	0.2631
                              0.3316	0.1475	0.1951	0.2214	0.2451
                              0.3491	0.1097	0.1512	0.1744	0.1966
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/mag-elevator-pos-rad</property>
                    <value>0.0500</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/mag-beta-rad</property>
                    <value>0.2000</value>
                </product>
            </function>
            <function name="aero/coefficient/CDgear">
                <description>Drag_due_to_landing_gear</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>gear/gear-pos-norm</property>
                    <value>0.0400</value>
                </product>
            </function>
            <function name="aero/coefficient/CDspeedbrake">
                <description>Drag_due_to_speedbrake</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/speedbrake-pos-norm</property>
                    <value>0.0400</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                              -0.3500	0.5000
                              0.0000	0.0000
                              0.3500	-0.5000
                          </tableData>
                      </table>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar lookup="row">aero/alpha-rad</independentVar>
                          <independentVar lookup="column">fcs/flap-pos-deg</independentVar>
                          <tableData>
                                0.0000	1.0000	9.0000	10.0000	40.0000
                              -0.0900	-0.2200	-0.2200	-0.1200	-0.1200	0.3200
                              0.0000	0.2500	0.2500	0.3500	0.3500	0.7500
                              0.0900	0.7300	0.7300	0.8300	0.8300	1.2300
                              0.1000	0.8300	0.8300	0.9300	0.9300	1.3300
                              0.1200	0.9200	0.9200	1.0200	1.0200	1.4200
                              0.1400	1.0200	1.0200	1.1200	1.1200	1.5200
                              0.1600	1.0800	1.0800	1.1800	1.1800	1.5800
                              0.1700	1.1300	1.1300	1.2300	1.2300	1.6300
                              0.1900	1.1900	1.1900	1.2900	1.2900	1.6900
                              0.2100	1.2500	1.2500	1.3500	1.3500	1.7700
                              0.2400	1.3500	1.3700	1.4700	1.4800	1.9300
                              0.2600	1.4400	1.4700	1.5700	1.6200	2.1200
                              0.2800	1.4700	1.5100	1.6100	1.7800	2.4000
                              0.3000	1.5000	1.5600	1.6600	1.9000	2.3000
                              0.3200	1.4700	1.6100	1.6000	1.7000	2.0300
                              0.3400	1.3500	1.5000	1.4100	1.5000	1.5300
                              0.3600	1.1500	1.2000	1.2000	1.2000	1.2000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLDe">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.1930</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                              -0.3500	0.0100
                              0.0000	0.0000
                              0.3500	-0.0100
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate_(roll_damping)</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.5000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.0050</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>0.2000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0050</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmo">
                <description>Pitching_moment_at_zero_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                      <table>
                          <independentVar>fcs/flap-pos-deg</independentVar>
                          <tableData>
                              0.0000	0.0400
                              40.0000	-0.1000
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-4.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/CmDe">
                <description>Pitch_moment_due_to_elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>-1.5000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-10.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-12.0000</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.0400</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.2000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.5000</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
