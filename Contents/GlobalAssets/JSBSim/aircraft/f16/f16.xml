<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sourceforge.net/JSBSim.xsl"?>
<fdm_config name="General Dynamics F-16A" version="2.0" release="PRODUCTION"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

 <fileheader>
  <author> <PERSON> </author>
  <filecreationdate> 2001-12-28 </filecreationdate>
  <version> $Revision: 1.95 $ </version>
  <license>
   <licenseName>GPL (General Public License)</licenseName>
   <licenseURL>http://www.gnu.org/licenses/gpl.html</licenseURL>
  </license>
  <description> Models an F-16A Block-32 (Basic US configuration) </description>
  <note>
   This model was created using data that is, or has been, publically
   available by means of technical reports, textbooks, image graphs or
   published code. This aircraft description file is in no way related 
   to the manufacturer of the real aircraft.
   Neither the name of (any of) the authors nor the names of (any of) the
   manufacturers may be used to endorse or promote products derived from
   this file.
 
   The data is provided ''as is'' and any express or implied
   warranties, including, but not limitted to the implied warranties of
   merchantability and fitness for a particular purpose are disclaimed.
  </note>
  <reference refID="ISBN 0-7232-3458-2" author="William Green" title="General Dynamics F-16 Dash-1" date="1987"/>
  <reference refID="NASA TP-1538" author="" title="wind tunnel data" date="12/1979"/>
  <reference refID="None" author="Richard Murray" title="http://www.cds.caltech.edu/~murray/projects/afosr95-vehicles/models/f16/" date="n/a"/>
  <reference refID="H-1999" author="NASA" title="Dynamic ground effects flight test of an F-15 aircraft" date="n/a"/>
  <reference refID="H-2177" author="NASA" title="Dynamic ground effect for a Cranked Arrow Wing Airplane" date="n/a"/>
  <reference refID="None" author="n/a" title="http://www.codeonemagazine.com/archives/1991/articles/jul_91/july2a_91.html" date="n/a"/>
 </fileheader>

 <metrics>
  <wingarea unit="FT2"> 300 </wingarea>
  <wingspan unit="FT"> 30 </wingspan>
  <chord unit="FT"> 11.32 </chord>
  <htailarea unit="FT2"> 63.7 </htailarea>
  <htailarm unit="FT"> 16.46 </htailarm>
  <vtailarea unit="FT2"> 54.75 </vtailarea>
  <vtailarm unit="FT"> 0 </vtailarm>
  <location name="AERORP" unit="IN">
   <x> -189.5 </x>
   <y> 0 </y>
   <z> 3.9 </z>
  </location>
  <location name="EYEPOINT" unit="IN">
   <x> -336.2 </x>
   <y> 0 </y>
   <z> 29.5 </z>
  </location>
  <location name="VRP" unit="IN">
   <x> -180 </x>
   <y> 0 </y>
   <z> 0 </z>
  </location>
 </metrics>

 <mass_balance negated_crossproduct_inertia="true">
  <ixx unit="SLUG*FT2"> 9496 </ixx>
  <iyy unit="SLUG*FT2"> 55814 </iyy>
  <izz unit="SLUG*FT2"> 63100 </izz>
  <ixy unit="SLUG*FT2"> 0 </ixy>
  <ixz unit="SLUG*FT2"> -982 </ixz>
  <iyz unit="SLUG*FT2"> 0 </iyz>
  <emptywt unit="LBS"> 17400 </emptywt>
  <location name="CG" unit="IN">
   <x> -193 </x>
   <y> 0 </y>
   <z> -5.1 </z>
  </location>
  <pointmass name="Pilot">
   <weight unit="LBS"> 230 </weight>
   <location name="POINTMASS" unit="IN">
    <x> -336.2 </x>
    <y> 0 </y>
    <z> 0 </z>
   </location>
  </pointmass>
 </mass_balance>

 <ground_reactions>
  <contact type="BOGEY" name="NOSE_LG">
   <location unit="IN">
    <x> -299.6 </x>
    <y> 0 </y>
    <z> -72 </z>
   </location>
   <static_friction> 0.8 </static_friction>
   <dynamic_friction> 0.5 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 17250 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 4250 </damping_coeff>
   <max_steer unit="DEG"> 80 </max_steer>
   <brake_group> NOSE </brake_group>
   <retractable>1</retractable>
  </contact>
  <contact type="BOGEY" name="LEFT_MLG">
   <location unit="IN">
    <x> -158.6 </x>
    <y> -48 </y>
    <z> -71.6 </z>
   </location>
   <static_friction> 0.8 </static_friction>
   <dynamic_friction> 0.5 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 37500 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 7500 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> LEFT </brake_group>
   <retractable>1</retractable>
  </contact>
  <contact type="BOGEY" name="RIGHT_MLG">
   <location unit="IN">
    <x> -158.6 </x>
    <y> 48 </y>
    <z> -71.6 </z>
   </location>
   <static_friction> 0.8 </static_friction>
   <dynamic_friction> 0.5 </dynamic_friction>
   <rolling_friction> 0.02 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 37500 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 7500 </damping_coeff>
   <max_steer unit="DEG"> 0.0 </max_steer>
   <brake_group> RIGHT </brake_group>
   <retractable>1</retractable>
  </contact>
  <contact type="STRUCTURE" name="LEFT_WT">
   <location unit="IN">
    <x> -121.3 </x>
    <y> -189 </y>
    <z> 0 </z>
   </location>
   <static_friction> 0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
  </contact>
  <contact type="STRUCTURE" name="RIGHT_WT">
   <location unit="IN">
    <x> -121.3 </x>
    <y> 189 </y>
    <z> 0 </z>
   </location>
   <static_friction> 0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
  </contact>
  <contact type="STRUCTURE" name="TOP_VS">
   <location unit="IN">
    <x> -27.2 </x>
    <y> 0 </y>
    <z> 123.2 </z>
   </location>
   <static_friction> 0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
  </contact>
  <contact type="STRUCTURE" name="LEFT_VFT">
   <location unit="IN">
    <x> -97.6 </x>
    <y> -24.8 </y>
    <z> -52 </z>
   </location>
   <static_friction> 0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 200 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 5000 </damping_coeff>
  </contact>
  <contact type="STRUCTURE" name="RIGHT_VFT">
   <location unit="IN">
    <x> -97.6 </x>
    <y> 24.8 </y>
    <z> -52 </z>
   </location>
   <static_friction> 0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 200 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 5000 </damping_coeff>
  </contact>
  <contact type="STRUCTURE" name="INTAKE">
   <location unit="IN">
    <x> -322.4 </x>
    <y> 0 </y>
    <z> -36.6 </z>
   </location>
   <static_friction> 0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
  </contact>
  <contact type="STRUCTURE" name="RADOME">
   <location unit="IN">
    <x> -486.6 </x>
    <y> 0 </y>
    <z> -8.7 </z>
   </location>
   <static_friction> 0.2 </static_friction>
   <dynamic_friction> 0.2 </dynamic_friction>
   <rolling_friction> 0.2 </rolling_friction>
   <spring_coeff unit="LBS/FT"> 10000 </spring_coeff>
   <damping_coeff unit="LBS/FT/SEC"> 2000 </damping_coeff>
  </contact>
 </ground_reactions>

 <external_reactions>
   <force name="pushback" frame="BODY">
  <location unit="IN">
    <x>  -2.98081 </x>
    <y> 0.0 </y>
    <z>  -1.9683 </z>
  </location>
  <direction>
    <x>1</x>
    <y>0</y>
    <z>0</z>
  </direction>
   </force>

   <force name="hook" frame="BODY">
  <location unit="IN">
    <x>  100.669 </x>
    <y> 0.0 </y>
    <z>  -28.818 </z>
  </location>
  <direction>
    <x>  -0.9995 </x>
    <y>   0.0 </y>
    <z>   0.01 </z>
  </direction>
   </force>
 </external_reactions>

 <propulsion>
  <engine file="F100-PW-229">
   <feed>0</feed>
   <feed>1</feed>
   <feed>2</feed>
   <feed>3</feed>
   <thruster file="direct">
    <location unit="IN">
     <x> 0 </x>
     <y> 0 </y>
     <z> 0 </z>
    </location>
    <orient unit="DEG">
     <roll> 0.0 </roll>
     <pitch> 0.0 </pitch>
     <yaw> 0.0 </yaw>
    </orient>
   </thruster>
  </engine>
  <tank type="FUEL"> <!-- Tank number 0 -->
   <location unit="IN">
    <x> -174.4 </x>
    <y> 65.0 </y>
    <z> 5.0 </z>
   </location>
   <capacity unit="LBS"> 3486 </capacity>
   <contents unit="LBS"> 1500 </contents>
  </tank>
  <tank type="FUEL"> <!-- Tank number 1 -->
   <location unit="IN">
    <x> -174.4 </x>
    <y> -65.0 </y>
    <z> 5.0 </z>
   </location>
   <capacity unit="LBS"> 3486 </capacity>
   <contents unit="LBS"> 1500 </contents>
  </tank>
  <tank type="FUEL"> <!-- External Tank number 0  (station 4) -->
   <location unit="IN">
    <x> -174.4 </x>
    <y> 65.0 </y>
    <z> -15.0 </z>
   </location>
   <capacity unit="LBS"> 2991 </capacity>
   <contents unit="LBS"> 0 </contents>
  </tank>
  <tank type="FUEL"> <!-- External Tank number 1 (station 6) -->
   <location unit="IN">
    <x> -174.4 </x>
    <y> -65.0 </y>
    <z> -15.0 </z>
   </location>
   <capacity unit="LBS"> 2991 </capacity>
   <contents unit="LBS"> 0 </contents>
  </tank>
 </propulsion>

 <system file="pushback">
   <property>/sim/model/pushback/kp</property>
   <property>/sim/model/pushback/ki</property>
   <property>/sim/model/pushback/kd</property>
 </system>
 <system file="hook"/>

 <flight_control name="F-16 FC">

   <!-- Declare some interface properties -->
  <property>fcs/alpha-norm</property>
  <property>fcs/hook-engage</property>
  <property>fcs/canopy-engage</property>
  <property>fcs/fbw-override</property>

  <channel name="Flaps">

   <switch name="fcs/tef-pos-rad">
    <default value="0.0"/>
    <test logic="AND" value="0.349">
     velocities/vc-kts lt 250
    </test>
    <test logic="AND" value="-0.0349">
     velocities/mach gt 0.9
    </test>
   </switch>

   <pure_gain name="fcs/tef-pos-norm">
     <input>fcs/tef-pos-rad</input>
     <gain>2.864789</gain>
   </pure_gain>

   <kinematic name="fcs/tef-control">
    <input>fcs/tef-pos-norm</input>
    <traverse>
     <setting>
      <position>-1.0</position>
      <time>3.0</time>
     </setting>
     <setting>
      <position>0</position>
      <time>0</time>
     </setting>
     <setting>
      <position>1.0</position>
      <time>3.0</time>
     </setting>
    </traverse>
   </kinematic>

  </channel>

  <channel name="Roll">

   <!-- Calculate the normalized roll-rate -->
   <pure_gain name="fcs/roll-rate-norm">
    <input>velocities/p-aero-rad_sec</input>
    <gain>0.31821</gain>
   </pure_gain>

   <!--
     - Calculate the difference between actual roll-rate and
     - commanded roll-rate.
     -->
   <summer name="fcs/roll-trim-error">
    <input>fcs/aileron-cmd-norm</input>
    <input>-fcs/roll-rate-norm</input>
   </summer>

   <!--
     - Make sure the PID controller is only active when the aircraft
     - has gained some speed. This will prevent bad behaviour when the
     - aircraft has been parked for a while.
     -->
   <switch name="fcs/aileron-pid-trigger">
    <default value="1"/>
    <test value="0">
     velocities/vc-kts lt 20.0
    </test>
   </switch>

   <pid name="fcs/roll-rate-pid">
     <trigger>fcs/aileron-pid-trigger</trigger>
     <input>fcs/roll-trim-error</input>
     <kp> 3.00000 </kp>
     <ki> 0.00050 </ki>
     <kd> -0.00125 </kd>
   </pid>

   <summer name="fcs/roll-rate-command">
    <input>fcs/roll-rate-pid</input>
    <input>fcs/aileron-cmd-norm</input>
    <clipto>
     <min>-1</min>
     <max>1</max>
    </clipto>
   </summer>

   <aerosurface_scale name="fcs/aileron-control">
    <input>fcs/roll-rate-command</input>
    <range>
     <min>-0.375</min>
     <max>0.375</max>
    </range>
    <output>fcs/aileron-pos-rad</output>
   </aerosurface_scale>
   
   <!--
     - FBW override swich
     -->
   <switch name="fcs/roll-rate-command-switch">
    <default value="fcs/roll-rate-command"/>
    <test value="fcs/aileron-cmd-norm">
     fcs/fbw-override == 1
    </test>
   </switch>

   <kinematic name="fcs/aileron-position">
    <input>fcs/roll-rate-command-switch</input>
    <traverse>
     <setting>
      <position>-1</position>
      <time>0.3</time>
     </setting>
     <setting>
      <position>1</position>
      <time>0.3</time>
     </setting>
    </traverse>
    <output>fcs/left-aileron-pos-norm</output>
   </kinematic>

   <scheduled_gain name="fcs/aileron-speed-compensated">
    <input>fcs/left-aileron-pos-norm</input>
    <table>
     <independentVar>velocities/mach</independentVar>
     <tableData>
      0.0  1.0
      1.0  0.15
     </tableData>
    </table>
   </scheduled_gain>

   <summer name="fcs/left-flaperon-norm">
    <input>-fcs/tef-control</input>
    <input>-fcs/aileron-speed-compensated</input>
    <clipto>
     <min>-1.0</min>
     <max>1.0</max>
    </clipto>
   </summer>

   <summer name="fcs/right-flaperon-norm">
    <input>fcs/tef-control</input>
    <input>-fcs/aileron-speed-compensated</input>
    <clipto>
     <min>-1.0</min>
     <max>1.0</max>
    </clipto>
   </summer>

   <summer name="fcs/flaperon-summer">
    <input>fcs/left-flaperon-norm</input>
    <input>fcs/right-flaperon-norm</input>
   </summer>
   
   <pure_gain name="fcs/flaperon-mix-rad">
    <input>fcs/flaperon-summer</input>
    <gain>1.4324</gain>
   </pure_gain>

   <aerosurface_scale name="fcs/left-aileron-control">
    <input>fcs/aileron-speed-compensated</input>
    <range>
     <min>-0.375</min>
     <max>0.375</max>
    </range>
    <output>fcs/left-aileron-pos-rad</output>
   </aerosurface_scale>

   <aerosurface_scale name="fcs/right-aileron-control">
    <input>-fcs/aileron-speed-compensated</input>
    <range>
     <min>-0.375</min>
     <max>0.375</max>
    </range>
    <output>fcs/right-aileron-pos-rad</output>
   </aerosurface_scale>

  </channel>

  <channel name="Pitch">
   <!--
     - accelerations/n-pilot-z-norm has the disadvantage that it
     - has an offset of 1G when positioned horizontal (and -1G
     - when upside down). To calculate the difference between the
     - commanded pitch-rate and actual pitch-rate this offset has
     - to be eliminated. The following function calculates an offset
     - to compensate for the earth's gravity.
     -->
   <fcs_function name="fcs/n-pilot-z-correction">
    <function>
    <product>
     <cos><property>attitude/pitch-rad</property></cos>
     <cos><property>attitude/roll-rad</property></cos>
    </product>
    </function>
   </fcs_function>

   <summer name="fcs/g-load-corrected">
     <input>accelerations/n-pilot-z-norm</input>
     <input>-fcs/n-pilot-z-correction</input>
   </summer>

   <!--
     - The F-16 has a G limit of 9G positive and 4G negative (44.44% 
     - of 9G). This section limits the stick output to 100% up and 44%
     - down.
     -->
   <summer name="fcs/elevator-cmd-limiter">
     <input>fcs/elevator-cmd-norm</input>
     <input>fcs/pitch-trim-cmd-norm</input>
     <clipto>
    <min>-1</min>
    <max>0.44</max>
     </clipto>
   </summer>

   <!--
      - If alpha (the difference between the pitch vector and the
      - thrust vector) approaches 30 degrees the Flight Computer will
      - command full down elevator deflection to prevent stalling.
      - This section reduces pilot command to zero when alpha
      - exceeds 28 degrees and approaches 30 degrees.
      - (note: JSBSim doesn't extrapolate, so no need to specify
      - beyond 30 degrees)
      -->
   <scheduled_gain name="fcs/elevator-scheduler">
    <input>fcs/elevator-cmd-limiter</input>
    <table>
     <independentVar>aero/alpha-rad</independentVar>
     <tableData>
     -0.5236    0.0
     -0.5       0.11
      0.0       1.0
      0.5       0.11
      0.5236    0.0
     </tableData>
    </table>
   </scheduled_gain>

   <!-- Command full pitch down when approaching 30 degrees alpha -->
   <pure_gain name="fcs/alpha-limiter-norm">
     <input>aero/alpha-rad</input>
     <gain>1.0472</gain>
   </pure_gain>

   <!-- Calculate the normalized current pitch-rate -->
   <pure_gain name="fcs/pitch-rate-norm">
    <input>velocities/q-aero-rad_sec</input>
    <gain>6.2</gain>
   </pure_gain>

   <!-- Calculate the normalized current pilot g-load -->
   <pure_gain name="fcs/g-load-norm">
     <input>fcs/g-load-corrected</input>
     <gain>0.020</gain>
   </pure_gain>

   <!--
     - Calculated the difference between the commanded pitch-rate
     - (elevator-scheduler) and the current pitch-rate and g-load.
     - The difference has to be compensated for straight flight.
     -->
   <summer name="fcs/pitch-trim-error">
     <input>fcs/elevator-scheduler</input>
     <input>fcs/pitch-rate-norm</input>
     <input>-fcs/g-load-norm</input>
   </summer>

   <!--
     - Make sure the PID controller is only active when the aircraft
     - has gained some speed. This will prevent bad behaviour when the
     - aircraft has been parked for a while.
     -->
   <switch name="fcs/elevator-pid-trigger">
    <default value="1"/>
    <test value="0">
     velocities/vc-kts lt 5.0
    </test>
   </switch>

   <pid name="fcs/g-load-pid">
     <trigger>fcs/elevator-pid-trigger</trigger>
     <input>fcs/pitch-trim-error</input>
     <kp> 0.3000 </kp>
     <ki> 0.0250 </ki>
     <kd> 0.0000 </kd>
     <clipto>
    <min>-1</min>
    <max>1</max>
     </clipto>
   </pid>

   <!--
     - Calculate the difference between the current pitch-rate and
     - the one requested for. Compensate for too high alpha values.
     -->
   <summer name="fcs/pitch-scheduler">
     <input>fcs/elevator-scheduler</input>
     <input>fcs/alpha-limiter-norm</input>
     <input>fcs/g-load-pid</input>
     <clipto>
    <min>-1</min>
    <max>1</max>
     </clipto>
   </summer>
   
   <!--
     - FBW override swich
     -->
    <switch name="fcs/pitch-scheduler-switch">
    <default value="fcs/pitch-scheduler"/>
    <test value="fcs/elevator-cmd-limiter">
     fcs/fbw-override == 1
    </test>
   </switch>

   <kinematic name="fcs/elevator-position-normalized">
    <input>fcs/pitch-scheduler-switch</input>
    <traverse>
     <setting>
      <position>-1</position>
      <time>0.3</time>
     </setting>
     <setting>
      <position>1</position>
      <time>0.3</time>
     </setting>
    </traverse>
    <output>fcs/elevator-pos-norm</output>
   </kinematic>

   <aerosurface_scale name="fcs/elevator-position">
     <input>fcs/elevator-pos-norm</input>
     <range>
    <min>-0.436</min>
    <max>0.436</max>
     </range>
     <output>fcs/elevator-pos-rad</output>
   </aerosurface_scale>

   <summer name="fcs/dht-left-pos-rad">
     <input>-fcs/elevator-pos-rad</input>
     <input>-fcs/left-aileron-pos-rad</input>
     <clipto>
    <min>-0.436</min>
    <max>0.436</max>
     </clipto>
    </summer>

    <summer name="fcs/dht-right-pos-rad">
     <input>fcs/elevator-pos-rad</input>
     <input>fcs/right-aileron-pos-rad</input>
     <clipto>
    <min>-0.436</min>
    <max>0.436</max>
     </clipto>
    </summer>

  </channel>

  <channel name="Yaw">
   <!-- Calculate the normalized yaw-rate -->
   <scheduled_gain name="fcs/yaw-rate-norm">
    <input>velocities/r-aero-rad_sec</input>
    <table>
     <independentVar>velocities/vg-fps</independentVar>
     <tableData>
      80.0  0.0
      100.0    15.0
      150.0    100.0
     </tableData>
    </table>
   </scheduled_gain>

   <!-- Calculate the normalized yaw-load -->
   <pure_gain name="fcs/yaw-load-norm">
    <input>accelerations/n-pilot-y-norm</input>
    <gain>0.25</gain>
   </pure_gain>

   <!--
     - Calculate the difference between the current yaw-rate
     - and the one requiested for.
     -->
   <summer name="fcs/yaw-trim-error">
    <input>fcs/rudder-cmd-norm</input>
    <input>fcs/yaw-rate-norm</input>
    <input>fcs/yaw-load-norm</input>
   </summer>

   <!--
     - Make sure the PID controller is only active when the aircraft
     - has gained some speed. This will prevent bad behaviour when the
     - aircraft has been parked for a while.
     -->
   <switch name="fcs/rudder-pid-trigger">
    <default value="1"/>
    <test value="0">
     velocities/vc-kts lt 10.0
    </test>
   </switch>

   <pid name="fcs/yaw-load-pid">
     <trigger>fcs/rudder-pid-trigger</trigger>
     <input>fcs/yaw-trim-error</input>
     <kp> 0.105500 </kp>
     <ki> 0.000010 </ki>
     <kd> 0.00005 </kd>
     <clipto>
    <min>-1</min>
    <max>1</max>
     </clipto>
     <output>fcs/rudder-pos-norm</output>
   </pid>

   <summer name="fcs/yaw-scheduler">
     <input>fcs/rudder-cmd-norm</input>
     <input>fcs/yaw-trim-cmd-norm</input>
     <input>fcs/yaw-load-pid</input>
     <clipto>
    <min>-1</min>
    <max>1</max>
     </clipto>
   </summer>

   <kinematic name="fcs/rudder-position">
    <input>fcs/yaw-scheduler</input>
    <traverse>
     <setting>
      <position>-1</position>
      <time>0.4</time>
     </setting>
     <setting>
      <position>1</position>
      <time>0.4</time>
     </setting>
    </traverse>
    <output>fcs/rudder-pos-norm</output>
   </kinematic>

   <aerosurface_scale name="fcs/rudder-control">
    <input>fcs/rudder-pos-norm</input>
    <range>
     <min>-0.524</min>
     <max>0.524</max>
    </range>
    <output>fcs/rudder-pos-rad</output>
   </aerosurface_scale>

  </channel>

  <channel name="Landing Gear">

   <switch name="fcs/gear-wow">
    <default value="0"/>
    <test logic="AND" value="1">
     gear/unit[1]/WOW eq 1
     gear/unit[2]/WOW eq 1
    </test>
   </switch>

   <kinematic name="fcs/gear-control">
    <input>gear/gear-cmd-norm</input>
    <traverse>
     <setting>
      <position>0</position>
      <time>0</time>
     </setting>
     <setting>
      <position>1</position>
      <time>5</time>
     </setting>
    </traverse>
    <output>gear/gear-pos-norm</output>
   </kinematic>

   <scheduled_gain name="fcs/scheduled-steer-pos-deg">
    <input>fcs/steer-cmd-norm</input>
    <table>
     <independentVar>velocities/vg-fps</independentVar>
     <tableData>
      10.0  80.0
      50.0  15.0
      150.0    2.0
     </tableData>
    </table>
    <output>fcs/steer-pos-deg</output>
   </scheduled_gain>

  </channel>
   
  <channel name="Leading Edge Flap">

   <switch name="fcs/lef-pos-rad">
    <default value="0.0"/>
    <test logic="AND" value="-0.0349">
     fcs/gear-wow eq 1
     gear/gear-pos-norm gt 0
    </test>
    <test logic="AND" value="0.436">
     gear/gear-pos-norm eq 0
     aero/alpha-rad gt 0.2618
    </test>
    <test logic="AND" value="0.262">
     fcs/gear-wow eq 0
     aero/alpha-rad gt 0.0873
    </test>
    <test logic="AND" value="-0.0349">
     velocities/mach gt 0.9
    </test>
   </switch>

   <pure_gain name="fcs/lef-pos-norm">
     <input>fcs/lef-pos-rad</input>
     <gain>2.293578</gain>
   </pure_gain>

   <kinematic name="fcs/lef-control">
    <input>fcs/lef-pos-norm</input>
    <traverse>
     <setting>
      <position>-1.0</position>
      <time>3.0</time>
     </setting>
     <setting>
      <position>1.0</position>
      <time>3.0</time>
     </setting>
    </traverse>
   </kinematic>

   <aerosurface_scale name="fcs/lef-pos-deg">
     <input>fcs/lef-control</input>
     <domain>
    <min>-1.0</min>
    <max>1.0</max>
     </domain>
     <range>
    <min>-25</min>
    <max>25</max>
     </range>
   </aerosurface_scale>

  </channel>

  <channel name="Throttle">

   <pure_gain name="fcs/throttle1">
    <input>fcs/throttle-cmd-norm</input>
    <gain>2</gain>
    <output>fcs/throttle-pos-norm</output>
   </pure_gain>

  </channel>

  <channel name="Speedbrake">
   <!--
     - To prevent deep stall the Flight Computer commands speedbrake
     - deflection at high angle of attack (alpha) and low speeds. This
     - will provide just enough pitch down moment to keep the aircraft
     - under control.
     -->
   <switch name="fcs/speedbrake-alpha-limiter">
    <default value="0"/>
    <test logic="AND" value="1">
     aero/alpha-deg ge 53 
     velocities/v-fps le 18
    </test>
   </switch>

   <switch name="fcs/speedbrake-initiate">
    <default value="0"/>
    <test logic="OR" value="1">
     fcs/speedbrake-alpha-limiter eq 1
     fcs/speedbrake-cmd-norm eq 1
    </test>
   </switch>

   <!--
     - Speedbrake deflection is limited to 43 degrees (instead of 60
     - degrees) when the gear is extended to prevent physical
     - speedbrake damage on touchdown.
     -->
   <scheduled_gain name="fcs/speedbrake-scheduler">
    <input>fcs/speedbrake-initiate</input>
    <table>
     <independentVar>gear/gear-cmd-norm</independentVar>
     <tableData>
      0 1.0
      1 0.71667
     </tableData>
    </table>
    <!-- <output>fcs/speedbrake-pos-norm</output> -->
   </scheduled_gain>

   <kinematic name="fcs/speedbrake-control">
    <input>fcs/speedbrake-scheduler</input>
    <traverse>
     <setting>
      <position>0</position>
      <time>0</time>
     </setting>
     <setting>
      <position>60</position>
      <time>1</time>
     </setting>
    </traverse>
    <output>fcs/speedbrake-pos-deg</output>
   </kinematic>

     <aerosurface_scale name="fcs/speedbrake-position-normalizer">
     <input>fcs/speedbrake-control</input>
     <domain>
    <min>0</min>
    <max>60</max>
     </domain>
     <range>
    <min>0</min>
    <max>1</max>
     </range>
     <output>fcs/speedbrake-pos-norm</output>
   </aerosurface_scale>

  </channel>

  <channel name="Hook">

   <kinematic name="fcs/hook-control">
    <input>fcs/hook-engage</input>
    <traverse>
     <setting>
      <position>0</position>
      <time>0</time>
     </setting>
     <setting>
      <position>1</position>
      <time>2.5</time>
     </setting>
    </traverse>
    <output>fcs/hook-pos-norm</output>
   </kinematic>
   
  </channel>

  <channel name="Canopy">

   <switch name="fcs/canopy-trigger">
    <default value="fcs/canopy-engage"/>
    <test value="0">
     velocities/u-fps gt 1.0
    </test>
   </switch>

   <kinematic name="fcs/canopy-control">
    <input>fcs/canopy-trigger</input>
    <traverse>
     <setting>
      <position>0</position>
      <time>0</time>
     </setting>
     <setting>
      <position>1</position>
      <time>10</time>
     </setting>
    </traverse>
    <output>fcs/canopy-pos-norm</output>
   </kinematic>

  </channel>

 </flight_control>

 <aerodynamics>

  <function name="aero/function/kCLge">
   <description>Change_in_lift_due_to_ground_effect</description>
   <table>
     <independentVar>aero/h_b-mac-ft</independentVar>
     <tableData>
       0.0000  1.2290
       0.1000  1.1240
       0.1500  1.1160
       0.2000  1.1240
       0.3000  1.1050
       0.4000  1.0410
       0.5000  1.0340
       0.6000  1.0190
       0.7000  1.0080
       0.8000  1.0030
       0.9000  1.0010
       1.0000  1.0000
       1.1000  1.0000
     </tableData>
   </table>
  </function>

  <axis name="DRAG">
   <function name="aero/coefficient/CDDh">
    <description>Drag_due_to_horizontal_tail_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
       <table name="CDdHT">
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">fcs/elevator-pos-rad</independentVar>
        <tableData>
                 -0.4360 -0.2180  0.0000  0.2180  0.4360
         -0.1750  0.2170  0.1740  0.1560  0.1810  0.2300
         -0.0870  0.0940  0.0550  0.0410  0.0620  0.1010
          0.0000  0.0810  0.0400  0.0210  0.0390  0.0760
          0.0870  0.1060  0.0610  0.0400  0.0570  0.1010
          0.1750  0.1660  0.1190  0.0960  0.1140  0.1580
          0.2620  0.2520  0.2030  0.1820  0.2020  0.2400
          0.3490  0.4040  0.3620  0.3470  0.3710  0.4160
          0.4360  0.6280  0.5880  0.5770  0.6010  0.6370
          0.5240  0.8750  0.8400  0.8260  0.8520  0.8800
          0.6110  1.1270  1.0950  1.0840  1.1020  1.1250
          0.6980  1.3650  1.3340  1.3260  1.3380  1.3560
          0.7850  1.5170  1.4870  1.4780  1.4820  1.4890
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CDmach">
    <description>Drag_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
         0.0000  0.0000
         0.8100  0.0000
         1.1000  0.0230
         1.8000  0.0150
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CDDlef">
    <description>Drag_due_to_leading_edge_flap_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>fcs/lef-pos-rad</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750  0.0030
         -0.0870  0.0010
          0.0000  0.0000
          0.0870  0.0010
          0.1750  0.0020
          0.2620  0.0040
          0.3490  0.0070
          0.4360  0.0110
          0.5240  0.0150
          0.6110  0.0190
          0.6980  0.0230
          0.7850  0.0240
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CDDflaps">
    <description>Drag_due_to_trailing_edge_flaps</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>fcs/flaperon-mix-rad</property>
     <value>0.0800</value>
    </product>
   </function>
   <function name="aero/coefficient/CDgear">
    <description>Drag_due_to_gear</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>gear/gear-pos-norm</property>
     <value>0.0270</value>
    </product>
   </function>
   <function name="aero/coefficient/CDDsb">
    <description>Drag_due_to_speedbrake_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>fcs/speedbrake-pos-rad</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750 -0.0545
         -0.0870 -0.0225
          0.0000  0.0096
          0.0870  0.0590
          0.1750  0.1244
          0.2620  0.2182
          0.3490  0.2324
          0.4360  0.2029
          0.5240  0.1435
          0.6110  0.1046
          0.6980  0.1363
          0.7850  0.1196
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CDq">
    <description>Drag_due_to_pitch_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>velocities/q-aero-rad_sec</property>
     <property>aero/ci2vel</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750  -1.2650
         -0.0870  -2.1390
          0.0000  -0.3080
          0.0870   1.4020
          0.1750   3.3690
          0.2620   5.1350
          0.3490   6.8800
          0.4360  10.0600
          0.5240  13.2010
          0.6110  15.8720
          0.6980  23.2170
          0.7850  24.1050
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CDq_Dlef">
    <description>Drag_due_to_pitch_rate_and_leading_edge_flap_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>velocities/q-aero-rad_sec</property>
     <property>aero/ci2vel</property>
     <property>fcs/lef-pos-rad</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750  0.0670
         -0.0870  0.0340
          0.0000  0.0280
          0.0870  0.0290
          0.1750  0.0330
          0.2620  0.0590
          0.3490  0.0610
          0.4360  0.0270
          0.5240  0.0360
          0.6110  0.0470
          0.6980  0.0290
          0.7850  0.0150
        </tableData>
       </table>
    </product>
   </function>
  </axis>

  <axis name="SIDE">
   <function name="aero/coefficient/CYb">
    <description>Side_force_due_to_beta</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>aero/beta-rad</property>
     <value>-1.1460</value>
    </product>
   </function>
   <function name="aero/coefficient/CYb_M">
    <description>Change_in_Side_force_due_to_beta_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>aero/beta-rad</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
          0.4000  0.0000
          1.2000  0.0573
          1.6000 -0.1719
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CYDa">
    <description>Side_force_due_to_aileron</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>fcs/aileron-pos-rad</property>
     <value>-0.0226</value>
    </product>
   </function>
   <function name="aero/coefficient/CYdr">
    <description>Side_force_due_to_rudder</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>fcs/rudder-pos-rad</property>
     <value>0.0860</value>
    </product>
   </function>
   <function name="aero/coefficient/CYp">
    <description>Side_force_due_to_roll_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>aero/bi2vel</property>
     <property>velocities/p-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750 -0.1080
         -0.0870 -0.1080
          0.0000 -0.1880
          0.0870  0.1100
          0.1750  0.2580
          0.2620  0.2260
          0.3490  0.3440
          0.4360  0.3620
          0.5240  0.6110
          0.6110  0.5290
          0.6980  0.2980
          0.7850 -0.2270
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CYr">
    <description>Side_force_due_to_yaw_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>aero/bi2vel</property>
     <property>velocities/r-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750  0.8820
         -0.0870  0.8520
          0.0000  0.8760
          0.0870  0.9580
          0.1750  0.9620
          0.2620  0.9740
          0.3490  0.8190
          0.4360  0.4830
          0.5240  0.5900
          0.6110  1.2100
          0.6980 -0.4930
          0.7850 -1.0400
        </tableData>
       </table>
    </product>
   </function>
  </axis>

  <axis name="LIFT">
   <function name="aero/coefficient/CLDh">
    <description>Lift_due_to_horizontal_tail_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>aero/function/kCLge</property>
       <table>
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">fcs/elevator-pos-rad</independentVar>
        <tableData>
                 -0.4360 -0.2180  0.0000  0.2180  0.4360
         -0.1750 -0.6590 -0.7090 -0.7540 -0.7920 -0.8250
         -0.0870 -0.1510 -0.1960 -0.2380 -0.2780 -0.3160
          0.0000  0.1830  0.1410  0.1000  0.0590  0.0170
          0.0870  0.4910  0.4540  0.4140  0.3710  0.3260
          0.1750  0.7970  0.7630  0.7250  0.6800  0.6300
          0.2620  1.1080  1.0790  1.0410  0.9930  0.9400
          0.3490  1.3950  1.3660  1.3270  1.2740  1.2140
          0.4360  1.6150  1.5870  1.5470  1.4900  1.4270
          0.5240  1.8040  1.7770  1.7370  1.6740  1.6100
          0.6110  1.9000  1.8720  1.8290  1.7660  1.6990
          0.6980  1.8980  1.8690  1.8220  1.7570  1.6890
          0.7850  1.7530  1.7240  1.6740  1.6120  1.5460
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CLDlef">
    <description>Lift_due_to_leading_edge_flap_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>fcs/lef-pos-rad</property>
     <property>aero/function/kCLge</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750 -0.0120
         -0.0870 -0.0040
          0.0000  0.0020
          0.0870  0.0070
          0.1750  0.0120
          0.2620  0.0180
          0.3490  0.0220
          0.4360  0.0250
          0.5240  0.0260
          0.6110  0.0280
          0.6980  0.0280
          0.7850  0.0250
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CLDflaps">
    <description>Delta_Lift_due_to_trailing_edge_flaps</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>fcs/flaperon-mix-rad</property>
     <property>aero/function/kCLge</property>
     <value>0.3500</value>
    </product>
   </function>
   <function name="aero/coefficient/CLDsb">
    <description>Lift_due_to_speedbrake_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>aero/function/kCLge</property>
     <property>fcs/speedbrake-pos-rad</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750  0.3645
         -0.0870  0.3678 
          0.0000  0.3684
          0.0870  0.2522
          0.1750  0.2710
          0.2620  0.3615
          0.3490  0.1283
          0.4360  0.0075
          0.5240 -0.1311
          0.6110 -0.1836
          0.6980 -0.1161
          0.7850 -0.1064
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CLq">
    <description>Lift_due_to_pitch_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>velocities/q-aero-rad_sec</property>
     <property>aero/function/kCLge</property>
     <property>aero/ci2vel</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750   8.7127
         -0.0870  25.7114
          0.0000  28.9000
          0.0870  31.3973
          0.1750  31.0872
          0.2620  30.4071
          0.3490  26.9735
          0.4360  26.4242
          0.5240  25.8647
          0.6110  25.2654
          0.6980  30.5158
          0.7850  25.8165
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CLq_Dsb">
    <description>Lift_due_to_pitch_rate_and_speedbrake_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>velocities/q-aero-rad_sec</property>
     <property>aero/ci2vel</property>
     <property>fcs/speedbrake-pos-rad</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750 -0.2560
         -0.0870 -0.0620
          0.0000 -0.0100
          0.0870  0.0200
          0.1750 -0.0110
          0.2620  0.0530
          0.3490  0.0630
          0.4360 -0.0090
          0.5240  0.0340
          0.6110  0.0420
          0.6980  0.0050
          0.7850  0.0010
        </tableData>
       </table>
    </product>
   </function>
  </axis>

  <axis name="ROLL">
   <function name="aero/coefficient/Clb">
    <description>Roll_moment_due_to_beta</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
       <table>
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">aero/beta-rad</independentVar>
        <tableData>
                 -0.5240 -0.4360 -0.3490 -0.2620 -0.1750 -0.0870  0.0000  0.0870  0.1750  0.2620  0.3490  0.4360  0.5240
         -0.1750 -0.0090 -0.0070 -0.0000  0.0010  0.0030  0.0010  0.0000 -0.0010 -0.0030 -0.0010  0.0000  0.0070  0.0090
         -0.0870  0.0110  0.0100  0.0100  0.0100  0.0090  0.0040  0.0000 -0.0040 -0.0090 -0.0100 -0.0100 -0.0100 -0.0110
          0.0000  0.0230  0.0230  0.0220  0.0200  0.0170  0.0080  0.0000 -0.0080 -0.0170 -0.0200 -0.0220 -0.0230 -0.0230
          0.0870  0.0370  0.0340  0.0340  0.0300  0.0240  0.0120  0.0000 -0.0120 -0.0240 -0.0300 -0.0340 -0.0340 -0.0370
          0.1750  0.0500  0.0490  0.0470  0.0390  0.0300  0.0160  0.0000 -0.0160 -0.0300 -0.0390 -0.0470 -0.0490 -0.0500
          0.2620  0.0680  0.0630  0.0600  0.0540  0.0410  0.0220  0.0000 -0.0220 -0.0410 -0.0540 -0.0600 -0.0630 -0.0680
          0.3490  0.0890  0.0810  0.0690  0.0570  0.0450  0.0220  0.0000 -0.0220 -0.0450 -0.0570 -0.0690 -0.0810 -0.0890
          0.4360  0.0880  0.0790  0.0670  0.0540  0.0400  0.0210  0.0000 -0.0210 -0.0400 -0.0540 -0.0670 -0.0790 -0.0880
          0.5240  0.0910  0.0600  0.0330  0.0230  0.0160  0.0150  0.0000 -0.0150 -0.0160 -0.0230 -0.0330 -0.0600 -0.0910
          0.6110  0.0760  0.0580  0.0360  0.0060  0.0020  0.0080  0.0000 -0.0080 -0.0020 -0.0060 -0.0360 -0.0580 -0.0760
          0.6980  0.0770  0.0620  0.0350  0.0140  0.0100  0.0130  0.0000 -0.0130 -0.0100 -0.0140 -0.0350 -0.0620 -0.0770
          0.7850  0.0760  0.0590  0.0350  0.0270  0.0190  0.0150  0.0000 -0.0150 -0.0190 -0.0270 -0.0350 -0.0590 -0.0760
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Clb_M">
    <description>Change_in_Roll_moment_due_to_beta_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/beta-rad</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
         0.6000  0.0000
         0.8000  0.1891
         1.0000  0.1776
         1.2000  0.1375
         1.4000  0.1203
         1.6000  0.0859
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Clp">
    <description>Roll_moment_due_to_roll_rate_(roll_damping)</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/bi2vel</property>
     <property>velocities/p-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750  -0.3600
         -0.0870  -0.3590
         0.0000  -0.4430
         0.0870  -0.4200
         0.1750  -0.3830
         0.2620  -0.3750
         0.3490  -0.3290
         0.4360  -0.2940
         0.5240  -0.2300
         0.6110  -0.2100
         0.6980  -0.1200
         0.7850  -0.1000
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Clr">
    <description>Roll_moment_due_to_yaw_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/bi2vel</property>
     <property>velocities/r-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750  -0.1260
         -0.0870  -0.0260
         0.0000  0.0630
         0.0870  0.1130
         0.1750  0.2080
         0.2620  0.2300
         0.3490  0.3190
         0.4360  0.4370
         0.5240  0.6800
         0.6110  0.1000
         0.6980  0.4470
         0.7850  -0.3300
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Clda">
    <description>Roll_moment_due_to_aileron</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>fcs/aileron-pos-rad</property>
       <table>
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">aero/beta-rad</independentVar>
        <tableData>
           -0.5240 -0.3490 -0.1750  0.0000  0.1750  0.3490  0.5240
         -0.1750  0.0410  0.0410  0.0420  0.0400  0.0430  0.0440  0.0430
         -0.0870  0.0520  0.0530  0.0530  0.0520  0.0490  0.0480  0.0490
         0.0000   0.0530  0.0530  0.0520  0.0510  0.0480  0.0480  0.0470
         0.0870   0.0560  0.0530  0.0510  0.0520  0.0490  0.0470  0.0450
         0.1750   0.0500  0.0500  0.0490  0.0480  0.0430  0.0420  0.0420
         0.2620   0.0560  0.0510  0.0490  0.0480  0.0420  0.0410  0.0370
         0.3490   0.0820  0.0660  0.0430  0.0420  0.0420  0.0200  0.0030
         0.4360   0.0590  0.0430  0.0350  0.0370  0.0360  0.0280  0.0130
         0.5240   0.0420  0.0380  0.0260  0.0310  0.0250  0.0130  0.0100
         0.6110   0.0380  0.0270  0.0160  0.0260  0.0210  0.0140  0.0030
         0.6980   0.0270  0.0230  0.0180  0.0170  0.0160  0.0110  0.0070
         0.7850   0.0170  0.0160  0.0140  0.0120  0.0110 -0.0100  0.0080
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Clda_M">
    <description>Change_in_Roll_moment_due_to_aileron_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/alpha-rad</property>
     <property>fcs/aileron-pos-rad</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
         0.6000  0.0000
         1.2000  -0.0630
         1.6000  -0.0630
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cldr_M">
    <description>Change_in_Roll_moment_due_to_rudder_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/alpha-rad</property>
     <property>fcs/rudder-pos-rad</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
         0.6000  0.0000
         1.6000  -0.0201
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cldr">
    <description>Roll_moment_due_to_rudder</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>fcs/rudder-pos-rad</property>
       <table>
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">aero/beta-rad</independentVar>
        <tableData>
        -0.5240  -0.3490  -0.1750  0.0000  0.1750  0.3490  0.5240
         -0.1750  0.0050  0.0070  0.0130  0.0180  0.0150  0.0210  0.0230
         -0.0870  0.0170  0.0160  0.0130  0.0150  0.0140  0.0110  0.0100
         0.0000  0.0140  0.0140  0.0110  0.0150  0.0130  0.0100  0.0110
         0.0870  0.0100  0.0140  0.0120  0.0140  0.0130  0.0110  0.0110
         0.1750  -0.0050  0.0130  0.0110  0.0140  0.0120  0.0100  0.0110
         0.2620  0.0090  0.0090  0.0090  0.0140  0.0110  0.0090  0.0100
         0.3490  0.0190  0.0120  0.0080  0.0140  0.0110  0.0080  0.0080
         0.4360  0.0050  0.0050  0.0050  0.0150  0.0100  0.0100  0.0100
         0.5240  0.0000  0.0000  0.0000  0.0130  0.0080  0.0060  0.0060
         0.6110  -0.0050  0.0040  0.0050  0.0110  0.0080  0.0050  0.0140
         0.6980  -0.0110  0.0090  0.0030  0.0060  0.0070  0.0000  0.0200
         0.7850  0.0080  0.0070  0.0050  0.0010  0.0030  0.0010  0.0000
        </tableData>
       </table>
    </product>
   </function>
  </axis>

  <axis name="PITCH">
   <function name="aero/coefficient/CmDh">
    <description>Pitch_moment_due_to_horizontal_tail_deflection</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/cbarw-ft</property>
       <table>
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">fcs/elevator-pos-rad</independentVar>
        <tableData>
                 -0.4360 -0.2180  0.0000  0.2180  0.4360
         -0.1750  0.2050  0.0810 -0.0460 -0.1740 -0.2590
         -0.0870  0.1680  0.0770 -0.0200 -0.1450 -0.2020
          0.0000  0.1860  0.1070 -0.0090 -0.1210 -0.1840
          0.0870  0.1960  0.1100 -0.0050 -0.1270 -0.1930
          0.1750  0.2130  0.1100 -0.0060 -0.1290 -0.1990
          0.2620  0.2510  0.1410  0.0100 -0.1020 -0.1500
          0.3490  0.2450  0.1270  0.0060 -0.0970 -0.1600
          0.4360  0.2380  0.1190 -0.0010 -0.1130 -0.1670
          0.5240  0.2520  0.1330  0.0140 -0.0870 -0.1040
          0.6110  0.2310  0.1080  0.0000 -0.0840 -0.0760
          0.6980  0.1980  0.0810 -0.0130 -0.0690 -0.0410
          0.7850  0.1920  0.0930  0.0320 -0.0060 -0.0050
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cma_M">
    <description>Change_in_Pitch_moment_due_to_alpha_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/cbarw-ft</property>
     <property>aero/alpha-rad</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
          0.6000  0.0000
          0.8000  0.0974
          0.9000 -0.3323
          1.0000 -0.9626
          1.1000 -0.8480
          1.2000 -0.7907
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/CmDsb">
    <description>Change_in_Pitch_moment_due_to_speedbrake</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/cbarw-ft</property>
     <property>fcs/speedbrake-pos-rad</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
          -0.1750 -0.0036
          -0.0870 -0.0036
           0.0000 -0.0036
           0.0870  0.0303
           0.1750  0.0225
           0.2620  0.0128
           0.3490  0.0252
           0.4360  0.0275
           0.5240 -0.0171
           0.6110 -0.0448
           0.6980 -0.0737
           0.7850 -0.0884
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cmq">
    <description>Pitch_moment_due_to_pitch_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/cbarw-ft</property>
     <property>aero/ci2vel</property>
     <property>velocities/q-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750 -7.2100
         -0.0870 -5.4000
          0.0000 -5.2300
          0.0870 -5.2600
          0.1750 -6.1100
          0.2620 -6.6400
          0.3490 -5.6900
          0.4360 -6.0000
          0.5240 -6.2000
          0.6110 -6.4000
          0.6980 -6.6000
          0.7850 -6.0000
        </tableData>
       </table>
    </product>
   </function>
  </axis>

  <axis name="YAW">
   <function name="aero/coefficient/Cnb">
    <description>Yaw_moment_due_to_beta</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
       <table>
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">aero/beta-rad</independentVar>
        <tableData>
                 -0.5240 -0.4360 -0.3490 -0.2620 -0.1750 -0.0870  0.0000  0.0870  0.1750  0.2620  0.3490  0.4360  0.5240
         -0.1750 -0.0790 -0.0740 -0.0640 -0.0560 -0.0380 -0.0180  0.0000  0.0180  0.0380  0.0560  0.0640  0.0740  0.0790
         -0.0870 -0.0900 -0.0860 -0.0770 -0.0570 -0.0420 -0.0190  0.0000  0.0190  0.0420  0.0570  0.0770  0.0860  0.0900
          0.0000 -0.1060 -0.0930 -0.0760 -0.0590 -0.0420 -0.0180  0.0000  0.0180  0.0420  0.0590  0.0760  0.0930  0.1060
          0.0870 -0.1060 -0.0890 -0.0740 -0.0580 -0.0420 -0.0190  0.0000  0.0190  0.0420  0.0580  0.0740  0.0890  0.1060
          0.1750 -0.0960 -0.0800 -0.0730 -0.0580 -0.0430 -0.0190  0.0000  0.0190  0.0430  0.0580  0.0730  0.0800  0.0960
          0.2620 -0.0800 -0.0620 -0.0570 -0.0530 -0.0390 -0.0180  0.0000  0.0180  0.0390  0.0530  0.0570  0.0620  0.0800
          0.3490 -0.0680 -0.0490 -0.0290 -0.0320 -0.0300 -0.0130  0.0000  0.0130  0.0300  0.0320  0.0290  0.0490  0.0680
          0.4360 -0.0300 -0.0220 -0.0070 -0.0120 -0.0170 -0.0070  0.0000  0.0070  0.0170  0.0120  0.0070  0.0220  0.0300
          0.5240 -0.0640 -0.0280 -0.0120 -0.0020 -0.0040 -0.0040  0.0000  0.0040  0.0040  0.0020  0.0120  0.0280  0.0640
          0.6110 -0.0150  0.0120  0.0340  0.0460  0.0350  0.0140  0.0000 -0.0140 -0.0350 -0.0460 -0.0340 -0.0120  0.0150
          0.6980 -0.0110  0.0020  0.0650  0.0710  0.0470  0.0170  0.0000 -0.0170 -0.0470 -0.0710 -0.0650 -0.0020  0.0110
          0.7850  0.0010  0.0130  0.0410  0.0730  0.0570  0.0330  0.0000 -0.0330 -0.0570 -0.0730 -0.0410 -0.0130 -0.0010
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cnb_M">
    <description>Change_in_Yaw_moment_due_to_beta_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/beta-rad</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
          0.6000  0.0000
          0.7000 -0.0688
          0.8000 -0.0172
          0.9000  0.0229
          1.0000  0.0000
          1.2000 -0.0688
          1.4000  0.0057
          1.6000  0.0688
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cnp">
    <description>Yaw_moment_due_to_roll_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/bi2vel</property>
     <property>velocities/p-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750 -0.0610
         -0.0870 -0.0520
          0.0000 -0.0520
          0.0870  0.0120
          0.1750  0.0130
          0.2620  0.0240
          0.3490 -0.0500
          0.4360 -0.1500
          0.5240 -0.1300
          0.6110 -0.1580
          0.6980 -0.2400
          0.7850 -0.1500
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cnr">
    <description>Yaw_moment_due_to_yaw_rate</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/bi2vel</property>
     <property>velocities/r-aero-rad_sec</property>
       <table>
        <independentVar>aero/alpha-rad</independentVar>
        <tableData>
         -0.1750 -0.3800
         -0.0870 -0.3630
          0.0000 -0.3780
          0.0870 -0.3860
          0.1750 -0.3700
          0.2620 -0.4530
          0.3490 -0.5500
          0.4360 -0.5820
          0.5240 -0.5950
          0.6110 -0.6370
          0.6980 -1.0200
          0.7850 -0.8400
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cnda_M">
    <description>Change_in_Yaw_moment_due_to_aileron_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>fcs/aileron-pos-rad</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
          0.6000  0.0000
          0.8000  0.0149
          1.0000 -0.0011
          1.2000  0.0097
          1.4000  0.0126
          1.6000 -0.0040
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cnda">
    <description>Yaw_moment_due_to_aileron</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>fcs/aileron-pos-rad</property>
       <table>
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">aero/beta-rad</independentVar>
        <tableData>
                 -0.5240 -0.3490 -0.1750  0.0000  0.1750  0.3490  0.5240
         -0.1750  0.0010  0.0020  0.0060  0.0110  0.0150  0.0240 -0.0220
         -0.0870  0.0270  0.0140  0.0080  0.0110  0.0150  0.0100 -0.0020
          0.0000  0.0170  0.0160  0.0060  0.0100  0.0140  0.0040  0.0030
          0.0870  0.0130  0.0160  0.0060  0.0090  0.0120  0.0020  0.0050
          0.1750  0.0120  0.0140  0.0050  0.0080  0.0110  0.0010  0.0030
          0.2620  0.0160  0.0190  0.0080  0.0060  0.0080 -0.0030  0.0010
          0.3490 -0.0010  0.0210  0.0050 -0.0000  0.0020 -0.0140  0.0090
          0.4360 -0.0170 -0.0020 -0.0070 -0.0040 -0.0020 -0.0060  0.0090
          0.5240 -0.0110 -0.0120 -0.0040 -0.0070 -0.0060  0.0010  0.0010
          0.6110 -0.0170 -0.0160 -0.0070 -0.0100 -0.0120 -0.0040 -0.0030
          0.6980 -0.0080 -0.0150 -0.0060 -0.0040 -0.0110 -0.0040  0.0020
          0.7850 -0.0160 -0.0110 -0.0060 -0.0100 -0.0110 -0.0060 -0.0010
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cndr">
    <description>Yaw_moment_due_to_rudder</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>fcs/rudder-pos-rad</property>
       <table>
        <independentVar lookup="row">aero/alpha-rad</independentVar>
        <independentVar lookup="column">aero/beta-rad</independentVar>
        <tableData>
                 -0.5240 -0.3490 -0.1750  0.0000  0.1750  0.3490  0.5240
         -0.1750 -0.0180 -0.0280 -0.0370 -0.0480 -0.0430 -0.0520 -0.0620
         -0.0870 -0.0520 -0.0510 -0.0410 -0.0450 -0.0440 -0.0340 -0.0340
          0.0000 -0.0520 -0.0430 -0.0380 -0.0450 -0.0410 -0.0360 -0.0270
          0.0870 -0.0520 -0.0460 -0.0400 -0.0450 -0.0410 -0.0360 -0.0280
          0.1750 -0.0540 -0.0450 -0.0400 -0.0440 -0.0400 -0.0350 -0.0270
          0.2620 -0.0490 -0.0490 -0.0380 -0.0450 -0.0380 -0.0280 -0.0270
          0.3490 -0.0590 -0.0570 -0.0370 -0.0470 -0.0340 -0.0240 -0.0230
          0.4360 -0.0510 -0.0520 -0.0300 -0.0480 -0.0350 -0.0230 -0.0230
          0.5240 -0.0300 -0.0300 -0.0270 -0.0490 -0.0350 -0.0200 -0.0190
          0.6110 -0.0370 -0.0330 -0.0240 -0.0450 -0.0290 -0.0160 -0.0090
          0.6980 -0.0260 -0.0300 -0.0190 -0.0330 -0.0220 -0.0100 -0.0250
          0.7850 -0.0130 -0.0080 -0.0130 -0.0160 -0.0090 -0.0140 -0.0100
        </tableData>
       </table>
    </product>
   </function>
   <function name="aero/coefficient/Cndr_M">
    <description>Change_in_Yaw_moment_due_to_rudder_due_to_mach</description>
    <product>
     <property>aero/qbar-psf</property>
     <property>metrics/Sw-sqft</property>
     <property>metrics/bw-ft</property>
     <property>aero/alpha-rad</property>
     <property>fcs/rudder-pos-rad</property>
       <table>
        <independentVar>velocities/mach</independentVar>
        <tableData>
          0.6000  0.0000
          1.2000  0.0401
          1.6000  0.0716
        </tableData>
       </table>
    </product>
   </function>
  </axis>
 </aerodynamics>
<!--
  <output name="f16_datalog.csv" type="CSV" rate="1">
 <property> aero/qbar-psf </property>
 <property> attitude/phi-rad </property>
 <property> position/h-sl-ft </property>
 <property> velocities/vc-kts </property>
 <property> fcs/throttle-cmd-norm </property>
 <property> fcs/elevator-cmd-norm </property>
 <property> fcs/pitch-trim-cmd-norm </property>
 <property> propulsion/total-fuel-lbs </property>
 <property> flight-path/gamma-rad </property>
 <property> accelerations/n-pilot-z-norm </property>
 <rates> ON </rates>
 <velocities> ON </velocities>
 <forces> ON </forces>
 <moments> ON </moments>
 <position> ON </position>
 <fcs> ON </fcs>
 <propulsion> OFF </propulsion>
 <aerosurfaces> ON </aerosurfaces>
 <fcs> ON </fcs>
 <ground_reactions> ON </ground_reactions>
  </output>
-->
</fdm_config>

