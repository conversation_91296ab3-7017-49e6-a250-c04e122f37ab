<?xml version="1.0" ?>
<flight_control name="FCS: quad-copter">
  <channel name="Pilot_Inputs">
    <summer name="pilotRoll_norm">
      <input>fcs/aileron-cmd-norm</input>
      <input>fcs/roll-trim-cmd-norm</input>
      <clipto>
        <min>-1.0</min>
        <max>1.0</max>
      </clipto>
      <output>fcs/pilotRoll_norm</output>
    </summer>
    <summer name="pilotPitch_norm">
      <input>fcs/elevator-cmd-norm</input>
      <input>fcs/pitch-trim-cmd-norm</input>
      <clipto>
        <min>-1.0</min>
        <max>1.0</max>
      </clipto>
      <output>fcs/pilotPitch_norm</output>
    </summer>
    <summer name="pilotYaw_norm">
      <input>fcs/rudder-cmd-norm</input>
      <input>fcs/yaw-trim-cmd-norm</input>
      <clipto>
        <min>-1.0</min>
        <max>1.0</max>
      </clipto>
      <output>fcs/pilotYaw_norm</output>
    </summer>

    <pure_gain name="refPhi_rad">
      <input>fcs/pilotRoll_norm</input>
      <gain>0.75</gain>
      <!-- <output>fcs/refPhi_rad</output> -->
      <output>fcs/refRoll_rps</output>
    </pure_gain>
    <pure_gain name="refTheta_rad">
      <input>fcs/pilotPitch_norm</input>
      <gain>0.75</gain>
      <!-- <output>fcs/refTheta_rad</output> -->
      <output>fcs/refPitch_rps</output>
    </pure_gain>
    <pure_gain name="refYaw_rps">
      <input>fcs/pilotYaw_norm</input>
      <gain>0.75</gain>
      <output>fcs/refYaw_rps</output>
    </pure_gain>
  </channel>

  <property value="0.0"> fcs/ScasEngage </property>
  <property>fcs/cmdHeave_nd </property>
  <property>fcs/refPhi_rad</property>
  <property>fcs/refTheta_rad</property>

  <!-- <channel name="InnerLoop-Attitude">
    <summer>
      <input>fcs/refPhi_rad</input>
      <input>-attitude/phi-rad</input>
      <output>fcs/errPhi_rad</output>
    </summer>

    <pure_gain>
      <input>fcs/errPhi_rad</input>
      <gain>fcs/ScasEngage</gain>
      <output>fcs/errPhi_rad</output>
    </pure_gain>

    <pid name="fcs/refRoll_rps">
      <input>fcs/errPhi_rad</input>
      <kp>0.500</kp>
      <ki>0.000</ki>
      <kd>0.000</kd>
    </pid>

    <summer>
      <input>fcs/refTheta_rad</input>
      <input>-attitude/theta-rad</input>
      <output>fcs/errTheta_rad</output>
    </summer>

    <pure_gain>
      <input>fcs/errTheta_rad</input>
      <gain>fcs/ScasEngage</gain>
      <output>fcs/errTheta_rad</output>
    </pure_gain>

    <pid name="fcs/refPitch_rps">
      <input>fcs/errTheta_rad</input>
      <kp>0.500</kp>
      <ki>0.000</ki>
      <kd>0.000</kd>
    </pid>
  </channel> -->

  <channel name="InnerLoop-Rates">
    <summer>
      <input>fcs/refRoll_rps</input>
      <input>-velocities/pi-rad_sec</input>
      <!-- <input>-sensor/imu/gyroX_rps</input> -->
      <output>fcs/errRoll_rps</output>
    </summer>

    <pure_gain>
      <input>fcs/errRoll_rps</input>
      <gain>fcs/ScasEngage</gain>
      <output>fcs/errRoll_rps</output>
    </pure_gain>

    <pid name="fcs/cmdRoll_rps">
      <input>fcs/errRoll_rps</input>
      <kp>0.150</kp>
      <ki>0.050</ki>
      <kd>0.003</kd>
    </pid>

    <summer>
      <input>fcs/refPitch_rps</input>
      <input>-velocities/qi-rad_sec</input>
      <!-- <input>-sensor/imu/gyroY_rps</input> -->
      <output>fcs/errPitch_rps</output>
    </summer>

    <pure_gain>
      <input>fcs/errPitch_rps</input>
      <gain>fcs/ScasEngage</gain>
      <output>fcs/errPitch_rps</output>
    </pure_gain>

    <pid name="fcs/cmdPitch_rps">
      <input>fcs/errPitch_rps</input>
      <kp>0.150</kp>
      <ki>0.050</ki>
      <kd>0.003</kd>
    </pid>

    <summer>
      <input>fcs/refYaw_rps</input>
      <!-- <input>-velocities/ri-rad_sec</input> -->
      <input>-sensor/imu/gyroZ_rps</input>
      <output>fcs/errYaw_rps</output>
    </summer>

    <pure_gain>
      <input>fcs/errYaw_rps</input>
      <gain>fcs/ScasEngage</gain>
      <output>fcs/errYaw_rps</output>
    </pure_gain>

    <pid name="fcs/cmdYaw_rps">
      <input>fcs/errYaw_rps</input>
      <kp>0.300</kp>
      <ki>0.100</ki>
      <kd>0.000</kd>
    </pid>
  </channel>

  <channel name="Control Mixer">
    <pure_gain name="cmdHeave_mps_2_FR">
      <input>fcs/cmdHeave_nd</input>
      <gain>1.0</gain>
      <output>fcs/cmdHeave_nd_2_FR</output>
    </pure_gain>
    <pure_gain name="cmdRoll_rps_2_FR">
      <input>fcs/cmdRoll_rps</input>
      <gain>-1.0</gain>
      <output>fcs/cmdRoll_rps_2_FR</output>
    </pure_gain>
    <pure_gain name="cmdPitch_rps_2_FR">
      <input>fcs/cmdPitch_rps</input>
      <gain>1.0</gain>
      <output>fcs/cmdPitch_rps_2_FR</output>
    </pure_gain>
    <pure_gain name="cmdYaw_rps_2_FR">
      <input>fcs/cmdYaw_rps</input>
      <gain>1.0</gain>
      <output>fcs/cmdYaw_rps_2_FR</output>
    </pure_gain>
    <summer name="cmdFR_nd">
      <input>fcs/cmdHeave_nd_2_FR</input>
      <input>fcs/cmdRoll_rps_2_FR</input>
      <input>fcs/cmdPitch_rps_2_FR</input>
      <input>fcs/cmdYaw_rps_2_FR</input>
      <output>fcs/cmdFR_nd</output>
    </summer>

    <pure_gain name="cmdHeave_mps_2_AL">
      <input>fcs/cmdHeave_nd</input>
      <gain>1.0</gain>
      <output>fcs/cmdHeave_nd_2_AL</output>
    </pure_gain>
    <pure_gain name="cmdRoll_rps_2_AL">
      <input>fcs/cmdRoll_rps</input>
      <gain>1.0</gain>
      <output>fcs/cmdRoll_rps_2_AL</output>
    </pure_gain>
    <pure_gain name="cmdPitch_rps_2_AL">
      <input>fcs/cmdPitch_rps</input>
      <gain>-1.0</gain>
      <output>fcs/cmdPitch_rps_2_AL</output>
    </pure_gain>
    <pure_gain name="cmdYaw_rps_2_AL">
      <input>fcs/cmdYaw_rps</input>
      <gain>1.0</gain>
      <output>fcs/cmdYaw_rps_2_AL</output>
    </pure_gain>
    <summer name="cmdAL_nd">
      <input>fcs/cmdHeave_nd_2_AL</input>
      <input>fcs/cmdRoll_rps_2_AL</input>
      <input>fcs/cmdPitch_rps_2_AL</input>
      <input>fcs/cmdYaw_rps_2_AL</input>
      <output>fcs/cmdAL_nd</output>
    </summer>

    <pure_gain name="cmdHeave_mps_2_FL">
      <input>fcs/cmdHeave_nd</input>
      <gain>1.0</gain>
      <output>fcs/cmdHeave_nd_2_FL</output>
    </pure_gain>
    <pure_gain name="cmdRoll_rps_2_FL">
      <input>fcs/cmdRoll_rps</input>
      <gain>1.0</gain>
      <output>fcs/cmdRoll_rps_2_FL</output>
    </pure_gain>
    <pure_gain name="cmdPitch_rps_2_FL">
      <input>fcs/cmdPitch_rps</input>
      <gain>1.0</gain>
      <output>fcs/cmdPitch_rps_2_FL</output>
    </pure_gain>
    <pure_gain name="cmdYaw_rps_2_FL">
      <input>fcs/cmdYaw_rps</input>
      <gain>-1.0</gain>
      <output>fcs/cmdYaw_rps_2_FL</output>
    </pure_gain>
    <summer name="cmdFL_nd">
      <input>fcs/cmdHeave_nd_2_FL</input>
      <input>fcs/cmdRoll_rps_2_FL</input>
      <input>fcs/cmdPitch_rps_2_FL</input>
      <input>fcs/cmdYaw_rps_2_FL</input>
      <output>fcs/cmdFL_nd</output>
    </summer>

    <pure_gain name="cmdHeave_mps_2_AR">
      <input>fcs/cmdHeave_nd</input>
      <gain>1.0</gain>
      <output>fcs/cmdHeave_nd_2_AR</output>
    </pure_gain>
    <pure_gain name="cmdRoll_rps_2_AR">
      <input>fcs/cmdRoll_rps</input>
      <gain>-1.0</gain>
      <output>fcs/cmdRoll_rps_2_AR</output>
    </pure_gain>
    <pure_gain name="cmdPitch_rps_2_AR">
      <input>fcs/cmdPitch_rps</input>
      <gain>-1.0</gain>
      <output>fcs/cmdPitch_rps_2_AR</output>
    </pure_gain>
    <pure_gain name="cmdYaw_rps_2_AR">
      <input>fcs/cmdYaw_rps</input>
      <gain>-1.0</gain>
      <output>fcs/cmdYaw_rps_2_AR</output>
    </pure_gain>
    <summer name="cmdAR_nd">
      <input>fcs/cmdHeave_nd_2_AR</input>
      <input>fcs/cmdRoll_rps_2_AR</input>
      <input>fcs/cmdPitch_rps_2_AR</input>
      <input>fcs/cmdYaw_rps_2_AR</input>
      <output>fcs/cmdAR_nd</output>
    </summer>
  </channel>

  <property value="0.0">fcs/cmdFR_ext_nd</property>
  <property value="0.0">fcs/cmdAL_ext_nd</property>
  <property value="0.0">fcs/cmdFL_ext_nd</property>
  <property value="0.0">fcs/cmdAR_ext_nd</property>

  <channel name="External Input Summations">
    <summer>
      <input>fcs/cmdFR_nd</input>
      <input>fcs/cmdFR_ext_nd</input>
      <output>fcs/cmdEscFR_nd</output>
    </summer>
    <summer>
      <input>fcs/cmdAL_nd</input>
      <input>fcs/cmdAL_ext_nd</input>
      <output>fcs/cmdEscAL_nd</output>
    </summer>
    <summer>
      <input>fcs/cmdFL_nd</input>
      <input>fcs/cmdFL_ext_nd</input>
      <output>fcs/cmdEscFL_nd</output>
    </summer>
    <summer>
      <input>fcs/cmdAR_nd</input>
      <input>fcs/cmdAR_ext_nd</input>
      <output>fcs/cmdEscAR_nd</output>
    </summer>
  </channel>
</flight_control>
