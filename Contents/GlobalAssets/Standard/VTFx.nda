adnj
{
    "Guid": "3919ea634457c47e59629cc49fc472a1",
    "Version": 5,
    "ClassID": 24,
    "DataSize": 0,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "e090e2c5eeb804be5b129b529f6daead",
        "PipelineResource/FFSRP/Shader/Material/Lit/LitShadow.shader.nda"
    ]
}
{
  "version": {
    "version": [
      1
    ]
  },
  "property": {
    "Default_Group": {
      "CLUSTER_RENDERING": {
        "Value": false,
        "Visible": true
      },
      "LIGHT_MAP_UV_CHANNEL_0": {
        "Value": false,
        "Visible": true
      },
      "CE_USE_DOUBLE_TRANSFORM": {
        "Value": true,
        "Visible": true
      },
      "_BaseColor": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_DoubleSidedConstants": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_AO_Intensity": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_ReflectionProbeIntensity": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_LightSpecIntensity": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_VLMReflectionProbeIntensity": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_VLMReflectionProbeAOIntensity": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_TextureSampleBias": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_TexSampleBiasCurveParam": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_TexSampleBiasCriticleDistance": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_TextureEnableVT": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_VTPageTableUniform0": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_VTPageTableUniform1": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_VTPackedUniform": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_CustomBaseColor": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_CustomRoughness": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_SSSColor": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_SSSIndencity": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "VISULIZE_VT": {
        "Value": false,
        "Visible": true
      },
      "USE_VT_NORMAL": {
        "Value": false,
        "Visible": true
      },
      "_VTPageTable": {
        "Value": "EngineResource/Texture/DefaultTexture.nda",
        "Visible": true
      },
      "VISUALIZE": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "MATERIAL_TYPE": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "DOUBLE_SIDED": {
        "Value": false,
        "Visible": true
      },
      "NO_INVERTUV": {
        "Value": false,
        "Visible": true
      },
      "_CustomSpecular": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_CustomMetalic": {
        "Value": [
          0.0
        ],
        "Visible": true
      }
    },
    "Only_in_shader": {
      "VOXELIZE_PASS": {
        "Value": false,
        "Visible": true
      },
      "QTANGENT": {
        "Value": false,
        "Visible": true
      },
      "INSTANCING": {
        "Value": false,
        "Visible": true
      },
      "TEXTURE_ARRAY_ENABLE": {
        "Value": false,
        "Visible": true
      },
      "PUNCTUAL_LIGHT": {
        "Value": false,
        "Visible": true
      },
      "_Metallic": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_Smoothness": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_NormalScale": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_SmoothnessRemapMin": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_SmoothnessRemapMax": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_MinAdjustClipDistance": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_MaxAdjustClipDistance": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_HashedAlphaDistance": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_Specular": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_EmissiveColor": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_SubsurfaceColor": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_AlphaClip": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_TuneColor_DiffuseBrightness": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_TuneColor_DiffuseContrast": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "_TuneColor_DesaturationFraction": {
        "Value": [
          0.0
        ],
        "Visible": true
      },
      "ALPHA_CLIPPING": {
        "Value": false,
        "Visible": true
      },
      "_EnableMapArrayFlags": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "USE_VT_EMISSIVE": {
        "Value": true,
        "Visible": true
      },
      "_NormalMap": {
        "Value": "EngineResource/Texture/DefaultTexture.nda",
        "Visible": true
      },
      "_EmissiveMap_VT_1": {
        "Value": "EngineResource/Texture/DefaultTexture.nda",
        "Visible": true
      },
      "_VTLayerRemap": {
        "Value": [
          0.0,
          0.0,
          0.0,
          0.0
        ],
        "Visible": true
      },
      "_BaseMap_VT_0": {
        "Value": "EngineResource/Texture/DefaultTexture.nda",
        "Visible": true
      },
      "_BaseMap": {
        "Value": "EngineResource/Texture/DefaultTexture.nda",
        "Visible": true
      },
      "ANISO_SAMPLE": {
        "Value": false,
        "Visible": true
      }
    }
  },
  "pass": [
    {
      "name": "gpass",
      "shader": "e090e2c5eeb804be5b129b529f6daead",
      "render_group": 2000,
      "state": {
        "depth_enable": true,
        "depth_write": true,
        "depth_cmp": "greater_equal",
        "cull": "back",
        "rasterization_mode": "defaultraster",
        "raster_overestimation_size": 1,
        "fill_mode": "solid",
        "blend_targets": "opacity"
      }
    },
    {
      "name": "VoxelizePass",
      "shader": "e090e2c5eeb804be5b129b529f6daead",
      "render_group": 2000,
      "state": {
        "depth_enable": false,
        "depth_write": false,
        "depth_cmp": "greater_equal",
        "cull": "none",
        "rasterization_mode": "defaultraster",
        "raster_overestimation_size": 1,
        "fill_mode": "solid",
        "blend_targets": "opacity"
      }
    },
    {
      "name": "shadow",
      "shader": "PipelineResource/FFSRP/Shader/Material/Lit/LitShadow.shader.nda",
      "render_group": 2000,
      "state": {
        "depth_enable": true,
        "depth_write": true,
        "depth_cmp": "less",
        "cull": "back",
        "rasterization_mode": "defaultraster",
        "raster_overestimation_size": 1,
        "fill_mode": "solid",
        "blend_targets": "opacity"
      }
    },
    {
      "name": "shadow_all",
      "shader": "PipelineResource/FFSRP/Shader/Material/Lit/LitShadow.shader.nda",
      "render_group": 2000,
      "state": {
        "depth_enable": true,
        "depth_write": true,
        "depth_cmp": "less",
        "cull": "back",
        "rasterization_mode": "defaultraster",
        "raster_overestimation_size": 1,
        "fill_mode": "solid",
        "blend_targets": "opacity"
      }
    }
  ]
}