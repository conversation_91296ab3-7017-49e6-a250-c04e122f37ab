
#pragma vertex VSMain
#pragma pixel PSMain


#pragma keyword CLUSTER_RENDERING
//#pragma keyword LIGHT_MAP_ENABLE
//#pragma keyword LIGHT_MAP_UV_CHANNEL_0
#pragma keyword VOXELIZE_PASS
#pragma keyword CE_USE_DOUBLE_TRANSFORM
#define NUM_MATERIAL_TEXCOORDS 2


#define FULL_CUSTOM
#define OPEN_VT

#define ADDITIONAL_MTL_PARAM \
float4 _CustomBaseColor; \
float _CustomRoughness; \
float _CustomSpecular;\
float _CustomMetalic;\
float4 _SSSColor; \
float4 _EmissiveColor;\
float _SSSIndencity;\
bool VISULIZE_VT;\
bool USE_VT_NORMAL; \
bool USE_VT_EMISSIVE;



#include "/Material/Lit/SurfaceShaderIncludes.hlsl"
#include "/ShaderLibrary/Packing.hlsl"

SHADER_CONST(bool, ALPHA_CLIP, false);

DECLARVT(Texture2D<float4>, _BaseMap, 0);
DECLARVT(Texture2D<float4>, _EmissiveMap, 1);


SurfaceData GetSurfaceData(SurfaceInput input)
{
	SurfaceData surfaceData = (SurfaceData)0; 

    
	float4 color = float4(0.0, 0.0 ,0.0, 1.0);
	uint vtLayerIndex = 0;
	FVirtualTextureFeedbackParams feedback;

	color = SampleVT(input, VT_PARA(_BaseMap, 0), ce_Sampler_Anisotropic, feedback);
	

	float3 debugResult = float3(0.0, 0.0, 0.0);
    float4 channels = _NormalMap.Sample(ce_Sampler_Anisotropic, input.uv);

    float3 normalTS = UnpackNormalmapRGorAG(channels, 1.0);
	//normalTS.y = 1.0 - normalTS.y;
    //float4 maskColor = _MaskTex.Sample(ce_Sampler_Repeat, psInput.uv);

	float4 emissive = float4(0, 0 ,0, 0);
	// if(_TextureEnableVT == 0)
	// {
	// 	emissive = _EmissiveMap.SampleBias(ce_Sampler_Anisotropic, input.uv, 0.0);
	// }
	// else
	// {
	// 	if(USE_VT_EMISSIVE)
	// 	{
	// 		emissive = SampleVT(input, _EmissiveMap, ce_Sampler_Anisotropic, vtLayerIndex, feedback);
	// 		//surfaceData.virtualTextureFeedbackRequest = feedback.Request;
	// 		vtLayerIndex++;
	// 	}
	// 	else
	// 	{
	// 		emissive = float4(0, 0 ,0, 0);//_EmissiveMap.SampleBias(ce_Sampler_Anisotropic, input.uv, 0.0);
	// 	}
	// }
	if(USE_VT_EMISSIVE)
	{
		emissive = SampleVT(input, VT_PARA(_EmissiveMap, 1) , ce_Sampler_Anisotropic, feedback) * _EmissiveColor;
	}

	surfaceData.virtualTextureFeedbackRequest = feedback.Request;
	surfaceData.baseColor = color.rgb ;
	// surfaceData.alpha = maskColor.r;



    
	// surfaceData.geomNormalWS = psInput.normalWS;
    surfaceData.normalTS = normalTS;
	surfaceData.ambientOcclusion = 1;
	surfaceData.roughness = _CustomRoughness;
	surfaceData.emissiveColor = emissive.rgb;
    surfaceData.subsurfaceColor =  _SSSColor * color.rgb * _SSSIndencity;
	surfaceData.materialType = MATERIAL_TYPE;
    surfaceData.specular = _CustomSpecular;
	surfaceData.debugColor = emissive;
	surfaceData.metallic = _CustomMetalic;

	return surfaceData;
}



#include "/Material/Lit/Lit.hlsl"
#if !defined(VOXELIZE_PASS) || !VOXELIZE_PASS
#include "/RenderPass/ShaderPassGBuffer.hlsl"
#else
#include "/RenderPass/ShaderPassVoxelizer.hlsl"
#endif

