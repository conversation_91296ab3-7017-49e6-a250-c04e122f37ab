adnj
{
    "Guid": "f3a6ae433b2ce445883529bf94784940",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 15318,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "3f2152e918e3c45acaa7858190260a93",
        "8997cb7e0e99a4573af09b40c12c099d",
        "8c96d49d42c424cd98467acc44d0f678"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "a6f184749598544c29ad5e7e21bbdd99"
        },
        "entities": {
            "a6f184749598544c29ad5e7e21bbdd99": {
                "prefabId": "8997cb7e0e99a4573af09b40c12c099d",
                "prefabEuid": "142320403843c4d349ea8a9116fc565c",
                "euid": "a6f184749598544c29ad5e7e21bbdd99",
                "name": "Towbar",
                "prototype": 113965963340369851,
                "+floder": false,
                "+expand": true,
                "+hide": false,
                "+selectable": true,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTRSFlag": 35,
                            "mScale": {
                                "x": 0.8,
                                "y": 0.8,
                                "z": 0.8
                            },
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "mModels": [
                            {
                                "i": "0",
                                "v": {
                                    "mSubModelProperties": [
                                        {
                                            "i": "0",
                                            "v": {
                                                "mMaterial": "8c96d49d42c424cd98467acc44d0f678"
                                            }
                                        }
                                    ],
                                    "+mReceiveDecals": true
                                }
                            }
                        ],
                        "+mCacheableForDrawing": false,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        }
                    },
                    "+cross::AnimatorComponentG": {
                        "Path": "3f2152e918e3c45acaa7858190260a93",
                        "componentHash": 3346675932
                    },
                    "cross::RenderPropertyComponentG": {
                        "+mLayerIndex": 0,
                        "+mNeedVoxelized": true,
                        "-mLightMask": null,
                        "-mCameraMask": null,
                        "-mRenderStage": null
                    },
                    "+cross::ControllableUnitComponentG": {
                        "Type": 1003,
                        "Controller": "{\n    \"Entity\": \"a6f184749598544c29ad5e7e21bbdd99\",\n    \"RotationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 1.0\n    },\n    \"TranslationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"SpawnSpot\": \"\",\n    \"InsideWorldBounds\": {\n        \"position\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        },\n        \"rotate\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0,\n            \"w\": 1.0\n        },\n        \"halfExtents\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        }\n    },\n    \"CollisionType\": 0,\n    \"BlockMask\": 65535,\n    \"bUseControllerRotationPitch\": true,\n    \"bUseControllerRotationYaw\": true,\n    \"bUseControllerRotationRoll\": true,\n    \"bUseControllerTranslate\": true\n}",
                        "componentHash": **********
                    }
                },
                "children": [
                    "22bfdf26c99044c21ab0a5eb7b121ded",
                    "50e98a2c6ddbb469e999b6705f55bf62",
                    "0ba6e575b982b491d903aa10ad5b76b4",
                    "8d265d4e4a38246a6bc35f51e55fbbb3"
                ]
            },
            "22bfdf26c99044c21ab0a5eb7b121ded": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "22bfdf26c99044c21ab0a5eb7b121ded",
                "name": "TractorSocket",
                "prototype": 16064513550957060465,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 17.607999801635747,
                                "z": 211.0023956298828
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 0.9999998807907105,
                                "y": 1.0000007152557374,
                                "z": 0.9999998807907105
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.7071043848991394,
                                "y": 0.00002439207855786663,
                                "z": 0.001856512506492436,
                                "w": 0.7071068286895752
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::SkeltSocketComponentG": {
                        "Bone": "front_mount",
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1990270841
                    }
                },
                "children": []
            },
            "50e98a2c6ddbb469e999b6705f55bf62": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "50e98a2c6ddbb469e999b6705f55bf62",
                "name": "AircraftSocket",
                "prototype": 16064513550957060465,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -0.0000010728834922701936,
                                "y": 43.115325927734378,
                                "z": -220.3797149658203
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.000000238418579,
                                "z": 1.000000238418579
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.7066209316253662,
                                "y": 5.820766091346741e-10,
                                "z": 5.384208634495734e-10,
                                "w": -0.7075924277305603
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::SkeltSocketComponentG": {
                        "Bone": "rear_mount",
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1990270841
                    }
                },
                "children": []
            },
            "0ba6e575b982b491d903aa10ad5b76b4": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "0ba6e575b982b491d903aa10ad5b76b4",
                "name": "RightWheel",
                "prototype": 11701872955039122557,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 26.98460388183594,
                                "y": 11.616741180419922,
                                "z": -68.32810974121094
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.375746488571167,
                                "y": 0.23843374848365785,
                                "z": 0.10068093985319138,
                                "w": 0.889846920967102
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::VehicleWheelComponentG": {
                        "mBoneName": "bone_wheel_r",
                        "mWheelRadius": 12.0,
                        "mMass": 10.0,
                        "mAxleType": 0,
                        "bSteeringEnabled": false,
                        "mMaxSteeringAngle": 45.0,
                        "mTranslation": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0
                        },
                        "mRotation": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 1.0
                        },
                        "componentHash": 667007736
                    }
                },
                "children": []
            },
            "8d265d4e4a38246a6bc35f51e55fbbb3": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "8d265d4e4a38246a6bc35f51e55fbbb3",
                "name": "LeftWheel",
                "prototype": 11701872955039122557,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -26.984607696533204,
                                "y": 11.61674213409424,
                                "z": -68.32810974121094
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0000001192092896,
                                "z": 1.0000001192092896
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.3763977885246277,
                                "y": 0.2374043166637421,
                                "z": 0.09824485331773758,
                                "w": 0.8901191353797913
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::VehicleWheelComponentG": {
                        "mBoneName": "bone_wheel_l",
                        "mWheelRadius": 10.0,
                        "mMass": 1.0,
                        "mAxleType": 0,
                        "bSteeringEnabled": false,
                        "mMaxSteeringAngle": 45.0,
                        "mTranslation": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0
                        },
                        "mRotation": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 1.0
                        },
                        "componentHash": 667007736
                    }
                },
                "children": []
            }
        }
    }
}