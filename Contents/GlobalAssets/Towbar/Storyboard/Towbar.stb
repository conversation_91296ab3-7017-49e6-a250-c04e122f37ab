<NodeGraph
  Name = "Towbar"
  NodeID = "10659"
  ConnectionID = "21284"
  HashName = "15975407362226838409">
  <Camera MaxZoom = "5" MinZoom = "-10" ZoomCount = "-1">
    <Viewport
      _WorldX = "-1129.371"
      _WorldY = "-770.2042"
      _Height = "1618.7517"
      _AspectRatio = "1.7312741"/>
    <Viewport
      _WorldX = "750"
      _WorldY = "80"
      _Height = "1295"
      _AspectRatio = "1.7312741"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "720"
    Y = "-110"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21239"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_BlendByLayeredFilterNode
    ID = "10366"
    X = "90"
    Y = "-110"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21148",
    "21135",
    "21112"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "bone_piston_l",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "bone_piston_r",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10366",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "bone_wheel_r"
    ID = "10495"
    X = "-550"
    Y = "0"
    NodeJsonData = ##{
  "BoneName": "bone_wheel_r",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21283",
    ""
  ],
  "Name": "10495",
  "Type": "TransformBoneNode"
}##/>
  <Anim_TransformBoneNode
    BoneName = "bone_wheel_l"
    ID = "10528"
    X = "-550"
    Y = "-170"
    NodeJsonData = ##{
  "BoneName": "bone_wheel_l",
  "Translation": {
    "Space": 1,
    "Mode": 0
  },
  "Rotation": {
    "Space": 1,
    "Mode": 2
  },
  "Scale": {
    "Space": 1,
    "Mode": 0
  },
  "InPoseLinks": [
    ""
  ],
  "InParamLinks": [
    "",
    "21272",
    ""
  ],
  "Name": "10528",
  "Type": "TransformBoneNode"
}##/>
  <Anim_SlotNode
    SlotName = "DefaultSlot"
    GroupName = "DefaultGroup"
    ID = "10591"
    X = "-580"
    Y = "-410"
    NodeJsonData = ##{
  "InPoseLinks": [],
  "SlotName": "DefaultSlot",
  "Name": "10591",
  "Type": "SlotNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "WheelRot"
    ParamType = "Vector3"
    ID = "10658"
    X = "-930"
    Y = "-40"
    NodeJsonData = ##{
  "InParams": [
    "WheelRot"
  ],
  "ReturnType": "Vector3",
  "Name": "10658",
  "Type": "ParamImplNode"
}##/>
  <Connection
    ID = "21112"
    OutSlotNodeID = "10495"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "2"/>
  <Connection
    ID = "21135"
    OutSlotNodeID = "10528"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "1"/>
  <Connection
    ID = "21148"
    OutSlotNodeID = "10591"
    OutSlotIndex = "0"
    InSlotNodeID = "10366"
    InSlotIndex = "0"/>
  <Connection
    ID = "21239"
    OutSlotNodeID = "10366"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
  <Connection
    ID = "21272"
    OutSlotNodeID = "10658"
    OutSlotIndex = "0"
    InSlotNodeID = "10528"
    InSlotIndex = "2"/>
  <Connection
    ID = "21283"
    OutSlotNodeID = "10658"
    OutSlotIndex = "0"
    InSlotNodeID = "10495"
    InSlotIndex = "2"/>
</NodeGraph>
<StbProperty ApplyMode = "0" ExtractionMode = "1">
  <Param ParamType = "CrossEditor.AnimParameter_Vector3" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0,
    "Z": 0.0
  },
  "Type": "Vector3",
  "Name": "WheelRot"
}##/>
</StbProperty>
