adnj
{
    "Guid": "90018a994391c4dea9a17ad3281449eb",
    "Version": 5,
    "ClassID": 24,
    "DataSize": 10955,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "39edfad5f429246ad90ec9bf5428c051",
        "d05b13f446ff8482a8ac6b48a55d4be5"
    ]
}
{
    "version": 1,
    "property": {
        "Please_Give_Me_A_Name ": {
            "_EnableMapArrayFlags": {
                "Value": [
                    -1.0,
                    -1.0,
                    -1.0,
                    -1.0
                ],
                "Visible": true,
                "IsColor": false
            },
            "MATERIAL_TYPE": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_BaseMap": {
                "Value": "39edfad5f429246ad90ec9bf5428c051",
                "Visible": true
            },
            "_BaseColor": {
                "Value": [
                    1.0,
                    1.0,
                    1.0,
                    1.0
                ],
                "Visible": true,
                "IsColor": true
            },
            "_AlphaClip": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "ALPHA_CLIPPING": {
                "Value": true,
                "Visible": true
            },
            "_MinAdjustClipDistance": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_MaxAdjustClipDistance": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_HashedAlphaDistance": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_NormalMap": {
                "Value": "39edfad5f429246ad90ec9bf5428c051",
                "Visible": true
            },
            "_NormalScale": {
                "Value": [
                    1.0
                ],
                "Visible": true
            },
            "_MaskMap": {
                "Value": "39edfad5f429246ad90ec9bf5428c051",
                "Visible": true
            },
            "_Metallic": {
                "Value": [
                    1.0
                ],
                "Visible": true
            },
            "_Smoothness": {
                "Value": [
                    1.0
                ],
                "Visible": true
            },
            "_SmoothnessRemapMin": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_SmoothnessRemapMax": {
                "Value": [
                    1.0
                ],
                "Visible": true
            },
            "_Specular": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_EmissiveColor": {
                "Value": [
                    0.0,
                    0.0,
                    0.0,
                    0.0
                ],
                "Visible": true,
                "IsColor": true
            },
            "_SubsurfaceColor": {
                "Value": [
                    1.0,
                    1.0,
                    1.0,
                    1.0
                ],
                "Visible": true,
                "IsColor": true
            },
            "DOUBLE_SIDED": {
                "Value": false,
                "Visible": true
            },
            "_DoubleSidedConstants": {
                "Value": [
                    0.0,
                    0.0,
                    0.0,
                    0.0
                ],
                "Visible": true,
                "IsColor": false
            },
            "NO_INVERTUV": {
                "Value": true,
                "Visible": true
            },
            "DPR_MAP": {
                "Value": false,
                "Visible": true
            },
            "INSTANCING": {
                "Value": false,
                "Visible": true
            },
            "LIGHT_MAP_UV_CHANNEL_0": {
                "Value": false,
                "Visible": true
            },
            "_AO_Intensity": {
                "Value": [
                    1.0
                ],
                "Visible": true
            },
            "_ReflectionProbeIntensity": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_LightSpecIntensity": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "QTANGENT": {
                "Value": false,
                "Visible": true
            },
            "_VLMReflectionProbeIntensity": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_VLMReflectionProbeAOIntensity": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_TextureSampleBias": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "_TexSampleBiasCurveParam": {
                "Value": [
                    10.0
                ],
                "Visible": true
            },
            "_TexSampleBiasCriticleDistance": {
                "Value": [
                    100000.0
                ],
                "Visible": true
            },
            "TUNE_COLOR": {
                "Value": false,
                "Visible": true
            },
            "_TuneColor_DiffuseBrightness": {
                "Value": [
                    1.0
                ],
                "Visible": true
            },
            "_TuneColor_DiffuseContrast": {
                "Value": [
                    1.0
                ],
                "Visible": true
            },
            "_TuneColor_DesaturationFraction": {
                "Value": [
                    0.5
                ],
                "Visible": true
            },
            "CHANNEL_PACKING": {
                "Value": false,
                "Visible": true
            },
            "ENABLE_LIGHTMAP_PRT_API": {
                "Value": false,
                "Visible": true
            },
            "PUNCTUAL_LIGHT": {
                "Value": false,
                "Visible": true
            },
            "CE_USE_DOUBLE_TRANSFORM": {
                "Value": true,
                "Visible": true
            },
            "VISUALIZE": {
                "Value": [
                    0.0
                ],
                "Visible": true
            },
            "CLUSTER_RENDERING": {
                "Value": false,
                "Visible": true
            }
        },
        "Only_in_shader": {
            "TEXTURE_ARRAY_ENABLE": {
                "Value": false,
                "Visible": true
            },
            "VOXELIZE_PASS": {
                "Value": false,
                "Visible": true
            },
            "ANISO_SAMPLE": {
                "Value": true,
                "Visible": true
            },
            "USE_CLIPMAP_WRAP": {
                "Value": false,
                "Visible": true
            },
            "_EnvBRDFLutMap": {
                "Value": "39edfad5f429246ad90ec9bf5428c051",
                "Visible": true
            },
            "SPLIT_REFLECTION_INDIRECT": {
                "Value": false,
                "Visible": true
            },
            "FORWARD_DEAL": {
                "Value": false,
                "Visible": true
            },
            "FORWARD_SSR": {
                "Value": false,
                "Visible": true
            },
            "ENABLE_LOCAL_LM_PRT": {
                "Value": false,
                "Visible": true
            },
            "ENABLE_TILE_BASED_LIGHTING": {
                "Value": false,
                "Visible": true
            },
            "ENABLE_MONTE_CARLO_SKY_LIGHTING": {
                "Value": false,
                "Visible": true
            },
            "CE_INSTANCING": {
                "Value": false,
                "Visible": true
            }
        },
        "Default Group": {
            "LIGHT_MAP_ENABLE": {
                "Value": false,
                "Visible": true
            },
            "ENABLE_LIGHT_PROBE": {
                "Value": false,
                "Visible": true
            },
            "ENABLE_VOLUMETRIC_LIGHT_MAP": {
                "Value": false,
                "Visible": true
            },
            "USE_SKY_OCCLUSION": {
                "Value": false,
                "Visible": true
            },
            "USE_LM_DIRECTIONALITY": {
                "Value": false,
                "Visible": true
            },
            "DISABLE_BAKE_NORMAL": {
                "Value": false,
                "Visible": true
            },
            "DISABLE_BAKE_SHADOW": {
                "Value": false,
                "Visible": true
            },
            "IS_STATIC": {
                "Value": true,
                "Visible": true
            },
            "WireFrameColor": {
                "Value": [
                    0.0,
                    0.0,
                    0.0,
                    0.0
                ],
                "Visible": true,
                "IsColor": false
            },
            "color_texture": {
                "Value": "39edfad5f429246ad90ec9bf5428c051",
                "Visible": true
            }
        }
    },
    "pass": [
        {
            "name": "gpass",
            "shader": "d05b13f446ff8482a8ac6b48a55d4be5",
            "render_group": 2000,
            "state": {
                "blend_targets": "opacity",
                "depth_enable": true,
                "depth_write": true,
                "depth_cmp": "GreaterEqual",
                "stencil_enable": false,
                "stencil_read_mask": 255,
                "stencil_write_mask": 255,
                "front_face_stencil_op": {
                    "fail_op": "Keep",
                    "depth_fail_op": "Keep",
                    "pass_op": "Keep",
                    "compare_op": "Always"
                },
                "back_face_stencil_op": {
                    "fail_op": "Keep",
                    "depth_fail_op": "Keep",
                    "pass_op": "Keep",
                    "compare_op": "Always"
                },
                "fill_mode": "Solid",
                "cull": "Back",
                "depth_clip": true,
                "enable_depth_bias": false,
                "depth_bias": 0,
                "slope_scaled_depth_bias": 0.0,
                "depth_bias_clamp": 0.0,
                "rasterization_mode": "DefaultRaster",
                "raster_overestimation_size": 1,
                "stencil_reference": 0
            }
        }
    ]
}