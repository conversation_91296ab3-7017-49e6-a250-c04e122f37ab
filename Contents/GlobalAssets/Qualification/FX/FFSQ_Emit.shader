#pragma vertex VS
#pragma pixel PS

#pragma keyword LIGHT_MAP_ENABLE


#include "ShaderLibrary/GlobalModelVariables.hlsl"
#include "Material/Lit/LitCommonStruct.hlsl"
#include "Material/MaterialCommon.hlsl"

#include"../Shader/ShaderLibrary/Common.hlsl"
SHADER_CONST(int, MATERIAL_TYPE, MaterialType_Unlit);

#include "/Material/Lit/GbufferEncoderDecoder.hlsl"


Texture2D<float4> color_texture : register(space1);

struct VertexIn
{
    float4 posL : POSITION;
	float3 Normal: NORMAL;
    float2 texcoord : TEXCOORD0;
    float4 color : COLOR; 
};

struct VertexOut
{
    float4 posHS : SV_POSITION;
    float3 viewDir: TEXCOORD0;
    float2 uv:TEXCOORD1;
    float4 VertexColor:TEXCOORD2;
    
};

VertexOut VS(VertexIn vin)
{
    VertexOut vout = (VertexOut)0;
	float3 pos = vin.posL.xyz;
    pos = mul(ce_World, float4(pos, 1.0));
    float4x4 MVP = mul(ce_Projection, ce_View);
    vout.posHS = mul(MVP, float4(pos, 1.0));
    vout.viewDir = normalize(mul(ce_World, pos));
    vout.uv = vin.texcoord;
    vout.VertexColor = vin.color;
    return vout;
}

struct PSOutput
{
	float4 gBuffer0 : SV_Target0;
	float4 gBuffer1 : SV_Target1;
	float4 gBuffer2 : SV_Target2;
	float4 gBuffer3 : SV_Target3;
	float4 gBuffer4 : SV_Target4;
    float4 gBuffer5 : SV_Target5;
};



PSOutput PS(VertexOut pin)
{
    float4 tex = color_texture.Sample(ce_Sampler_Clamp, pin.uv);

    PSOutput output = (PSOutput)0;
    SurfaceData surfacedata = GetDefaultSurfaceData();
    surfacedata.baseColor = float3(0.0, 0.0, 0.0);
    surfacedata.materialType = 1;
    surfacedata.emissiveColor = tex * pin.VertexColor;
	surfacedata.temporalReactive = float2(0.0, 0.0);
    BuiltinData builtinData = (BuiltinData)0;
    EncodeIntoGBuffer(surfacedata, builtinData, output.gBuffer0, output.gBuffer1, output.gBuffer2, output.gBuffer3, output.gBuffer4, output.gBuffer5);
    return output;
}