adnj
{
    "Guid": "2e961267714dd468e9db9f9881752cb7",
    "Version": 5,
    "ClassID": 50,
    "DataSize": 73545,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "EngineResource/Model/Cube.nda",
        "PipelineResource/FFSRP/Material/EmitterDefault.mpc",
        "PipelineResource/FFSRP/Material/ParticleMeshDefault.nda",
        "d1541558154284ef89c4f98e8bc1cc01",
        "d8432090585a649ca96d36cbf734f423"
    ]
}
{
    "Emitters": [
        {
            "ASSET_MAGIC_NUMBER": 778986593,
            "Path": "d8432090585a649ca96d36cbf734f423"
        }
    ],
    "EmitterInfos": [
        {
            "EmitterName": "Emitter_1",
            "EmitterState": {
                "Enabled": true,
                "AutoRandomSeed": true,
                "RandomSeed": 0,
                "LocalSpace": true,
                "LoopBehavior": 1,
                "LoopDuration": 5.0,
                "LoopCount": 0,
                "StartDelay": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "ApplyLighting": false
            },
            "ParticleSpawn": {
                "Enabled": true,
                "ParticleCountLimit": 100,
                "SpawnRateCount": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 20.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "Bursts": null
            },
            "LocationShape": {
                "Enabled": true,
                "PositionOffset": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": -0.0
                },
                "ShapeType": 0,
                "EmitFrom": 0,
                "EmitType": 0,
                "Box": {
                    "x": 40000.0,
                    "y": 1.0,
                    "z": 40000.0
                },
                "Radius": 10.0,
                "RadiusMode": 0,
                "RadiusSpread": 0.0,
                "RadiusSpeed": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "Angle": 45.0,
                "Arc": 360.0,
                "Length": 1000.0,
                "ModelPath": "",
                "LodIndex": 0
            },
            "ParticleInit": {
                "Enabled": true,
                "StartSpeed": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "OriginVelocity": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 1000.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                },
                "Offset": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                },
                "Lifetime": {
                    "Mode": 0,
                    "FloatMin": 1.0,
                    "FloatMax": 15.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "Scaler": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "SeparateAxis": false,
                "SpriteSizeX": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "SpriteSizeY": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "SpriteRotation": {
                    "Mode": 0,
                    "FloatMin": -1.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "MeshSizeX": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "MeshSizeY": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "MeshSizeZ": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "MeshRotationX": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "MeshRotationY": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "MeshRotationZ": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "UVScale": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": 1.0,
                    "w": 1.0
                },
                "Color": {
                    "Mode": 0,
                    "ColorMin": {
                        "r": 0.9230769276618958,
                        "g": 1.0,
                        "b": 0.8391608595848084,
                        "a": 1.0
                    },
                    "ColorMax": {
                        "r": 0.4545454680919647,
                        "g": 0.4517335891723633,
                        "b": 0.32104259729385378,
                        "a": 1.0
                    },
                    "GradientMin": {
                        "RCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "GCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "BCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "ACurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0,
                            "w": 1.0
                        }
                    },
                    "GradientMax": {
                        "RCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "GCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "BCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "ACurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0,
                            "w": 1.0
                        }
                    }
                }
            },
            "SizeScale": {
                "Enabled": true,
                "SizeScale": {
                    "Mode": 5,
                    "FloatMin": 1.5,
                    "FloatMax": 2.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": [
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": -0.0,
                                "Value": 0.0,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 0.0,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": 6.0,
                                "EventString": ""
                            },
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 0.20000000298023225,
                                "Value": 1.2000000476837159,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 0.5,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": 0.5,
                                "EventString": ""
                            },
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 0.4000000059604645,
                                "Value": 0.20000000298023225,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 0.0,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": 0.0,
                                "EventString": ""
                            },
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 0.800000011920929,
                                "Value": 1.2000000476837159,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 0.3333333134651184,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": 0.3333333134651184,
                                "EventString": ""
                            },
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 1.0,
                                "Value": 0.4000000059604645,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": -4.000000476837158,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": -4.000000476837158,
                                "EventString": ""
                            }
                        ],
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 10.0
                },
                "SeparateAxis": false,
                "SizeScaleX": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "SizeScaleY": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "SizeScaleZ": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "ScaleWithCamera": false,
                "CameraRemapScale": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": 0.0,
                    "w": 0.0
                }
            },
            "ColorScale": {
                "Enabled": true,
                "ColorScale": {
                    "Mode": 1,
                    "ColorMin": {
                        "r": 1.0,
                        "g": 1.0,
                        "b": 1.0,
                        "a": 1.0
                    },
                    "ColorMax": {
                        "r": 1.0,
                        "g": 1.0,
                        "b": 1.0,
                        "a": 1.0
                    },
                    "GradientMin": {
                        "RCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "GCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "BCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "ACurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0,
                            "w": 1.0
                        }
                    },
                    "GradientMax": {
                        "RCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": [
                                {
                                    "SmoothType": 0,
                                    "InterpType": 2,
                                    "Time": 0.0010000000474974514,
                                    "Value": 1.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": 0.0,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": 0.0,
                                    "EventString": ""
                                },
                                {
                                    "SmoothType": 0,
                                    "InterpType": 2,
                                    "Time": 0.20000000298023225,
                                    "Value": 1.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": 0.0,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": 0.0,
                                    "EventString": ""
                                }
                            ],
                            "Name": "R",
                            "UseType": 0
                        },
                        "GCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": [
                                {
                                    "SmoothType": 0,
                                    "InterpType": 2,
                                    "Time": 0.0010000000474974514,
                                    "Value": 1.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": 0.0,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": 0.0,
                                    "EventString": ""
                                },
                                {
                                    "SmoothType": 0,
                                    "InterpType": 2,
                                    "Time": 0.20000000298023225,
                                    "Value": 1.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": 0.0,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": 0.0,
                                    "EventString": ""
                                }
                            ],
                            "Name": "G",
                            "UseType": 0
                        },
                        "BCurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": [
                                {
                                    "SmoothType": 0,
                                    "InterpType": 2,
                                    "Time": 0.0010000000474974514,
                                    "Value": 1.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": 0.0,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": 0.0,
                                    "EventString": ""
                                },
                                {
                                    "SmoothType": 0,
                                    "InterpType": 2,
                                    "Time": 0.20000000298023225,
                                    "Value": 1.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": 0.0,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": 0.0,
                                    "EventString": ""
                                }
                            ],
                            "Name": "B",
                            "UseType": 0
                        },
                        "ACurve": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": [
                                {
                                    "SmoothType": 0,
                                    "InterpType": 2,
                                    "Time": 0.0,
                                    "Value": 0.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": 0.0,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": 5.0,
                                    "EventString": ""
                                },
                                {
                                    "SmoothType": 1,
                                    "InterpType": 2,
                                    "Time": 0.20000000298023225,
                                    "Value": 1.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": 0.30377480387687685,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": 0.30377480387687685,
                                    "EventString": ""
                                },
                                {
                                    "SmoothType": 1,
                                    "InterpType": 2,
                                    "Time": 0.800000011920929,
                                    "Value": 1.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": -0.013086937367916108,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": -0.013086937367916108,
                                    "EventString": ""
                                },
                                {
                                    "SmoothType": 0,
                                    "InterpType": 2,
                                    "Time": 1.0,
                                    "Value": 0.0,
                                    "AutoWeighted": true,
                                    "ArriveWeight": 0.3333333432674408,
                                    "ArriveTangent": -5.000000476837158,
                                    "LeaveWeight": 0.3333333432674408,
                                    "LeaveTangent": -5.000000476837158,
                                    "EventString": ""
                                }
                            ],
                            "Name": "ACurve",
                            "UseType": 0
                        },
                        "Scaler": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0,
                            "w": 1.0
                        }
                    }
                },
                "ScaleWithCamera": false,
                "ScaleChannel": 1040896,
                "CameraRemapScale": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": 0.0,
                    "w": 0.0
                }
            },
            "SpriteRotationRate": {
                "Enabled": false,
                "SpriteRotationRate": {
                    "Mode": 0,
                    "FloatMin": -100.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": [
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 0.20000000298023225,
                                "Value": 0.0,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 0.0,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": -0.0,
                                "EventString": ""
                            },
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 0.30000001192092898,
                                "Value": -0.0,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": -0.0,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": -0.0,
                                "EventString": ""
                            }
                        ],
                        "Name": "MinCurve",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": [
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 0.0,
                                "Value": 0.10000000149011612,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 0.0,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": 0.5,
                                "EventString": ""
                            },
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 1.0,
                                "Value": 0.6000000238418579,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 0.5,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": 0.5,
                                "EventString": ""
                            }
                        ],
                        "Name": "MaxCurve",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                }
            },
            "Velocity": {
                "Enabled": false,
                "Vector": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                }
            },
            "VectorNoise": {
                "Enabled": false,
                "Frequency": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.009999999776482582,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.009999999776482582,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.009999999776482582,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                },
                "Scaler": 1.0,
                "Strength": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 100.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 100.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 100.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                },
                "ScrollOffset": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "PositionOffset": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                },
                "SeparateAxis": false,
                "Damping": true,
                "OctaveCount": 0,
                "OctaveScale": 0.5,
                "OctaveMultiplier": 1.0
            },
            "GravityForce": {
                "Enabled": false,
                "GravityVector": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": -980.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                }
            },
            "Force": {
                "Enabled": false,
                "ForceVector": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                },
                "Space": 0,
                "Scaler": 1.0
            },
            "VortexForce": {
                "Enabled": false,
                "VortexAxis": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                },
                "VortexOriginOffset": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                },
                "VortexForceAmount": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "OriginPullAmount": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "Space": 0
            },
            "PointAttractionForce": {
                "Enabled": false,
                "AttractionStrength": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "AttractionRadius": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "UseFalloff": false,
                "FalloffExponent": 0.0,
                "KillRadius": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 0.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "KillRadiusOvershootCorrection": 1.0,
                "AttractorPosition": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": 0.0
                },
                "AttractorOffset": {
                    "X": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Y": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    },
                    "Z": {
                        "Mode": 0,
                        "FloatMin": 0.0,
                        "FloatMax": 0.0,
                        "CurveMin": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "CurveMax": {
                            "EnterType": 0,
                            "LeaveType": 0,
                            "Keys": null,
                            "Name": "",
                            "UseType": 0
                        },
                        "Scaler": 1.0
                    }
                }
            },
            "SolveForceVelocity": {
                "Enabled": true,
                "SpeedScale": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "SpeedLimit": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 10000.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "DragEnable": false,
                "Drag": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 1.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                }
            },
            "SubUV": {
                "Enabled": true,
                "TileX": 8,
                "TileY": 8,
                "Animation": 0,
                "StartFrame": {
                    "Mode": 0,
                    "FloatMin": 0.0,
                    "FloatMax": 60.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "Frame": {
                    "Mode": 2,
                    "FloatMin": 0.0,
                    "FloatMax": 64.0,
                    "CurveMin": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": null,
                        "Name": "",
                        "UseType": 0
                    },
                    "CurveMax": {
                        "EnterType": 0,
                        "LeaveType": 0,
                        "Keys": [
                            {
                                "SmoothType": 0,
                                "InterpType": 0,
                                "Time": 0.0,
                                "Value": -0.0,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 0.0,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": 0.0,
                                "EventString": ""
                            },
                            {
                                "SmoothType": 0,
                                "InterpType": 2,
                                "Time": 1.0,
                                "Value": 63.0,
                                "AutoWeighted": true,
                                "ArriveWeight": 0.3333333432674408,
                                "ArriveTangent": 63.0,
                                "LeaveWeight": 0.3333333432674408,
                                "LeaveTangent": 63.0,
                                "EventString": ""
                            }
                        ],
                        "Name": "",
                        "UseType": 0
                    },
                    "Scaler": 1.0
                },
                "Cycles": 1,
                "PlayRate": 20.0,
                "RowMode": 1,
                "RowIndex": 0
            },
            "ParticleState": {
                "Enabled": true,
                "KillTrigger": 0,
                "Volume": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": 0.0
                },
                "VolumePosition": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": 0.0
                },
                "VolumeRotation": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": 0.0
                },
                "Space": 205917231
            },
            "SpriteRenderer": {
                "EnableCameraDistanceCull": false,
                "MinCameraDistance": 0.0,
                "MaxCameraDistance": 10000.0,
                "EmitterCustomProperty": null,
                "ParticleCustomProperty": null,
                "EmitterMPCPath": "PipelineResource/FFSRP/Material/EmitterDefault.mpc",
                "Enabled": true,
                "Alignment": 0,
                "AlignDirection": {
                    "x": 0.0,
                    "y": 1.0,
                    "z": 0.0
                },
                "Facing": 1,
                "FacingDirection": {
                    "x": 1.0,
                    "y": 0.0,
                    "z": 0.0
                },
                "MinBlendDistance": 0.0,
                "MaxBlendDistance": 0.0,
                "EnableLargeCoordinate": true,
                "DefaultPivot": {
                    "x": 0.0,
                    "y": 0.0
                },
                "MaterialPath": "d1541558154284ef89c4f98e8bc1cc01"
            },
            "MeshRenderer": {
                "EnableCameraDistanceCull": false,
                "MinCameraDistance": 0.0,
                "MaxCameraDistance": 0.0,
                "EmitterCustomProperty": null,
                "ParticleCustomProperty": null,
                "EmitterMPCPath": "PipelineResource/FFSRP/Material/EmitterDefault.mpc",
                "Enabled": false,
                "Alignment": 0,
                "AlignDirection": {
                    "x": 0.0,
                    "y": 0.0,
                    "z": 0.0
                },
                "ModelPath": "EngineResource/Model/Cube.nda",
                "MaterialSlots": null,
                "MaterialPath": "PipelineResource/FFSRP/Material/ParticleMeshDefault.nda"
            },
            "Simulation": 0,
            "WorldX": 7,
            "WorldY": 86,
            "RendererType": 0
        }
    ],
    "RandomSeed": 0,
    "AutoPlay": false
}