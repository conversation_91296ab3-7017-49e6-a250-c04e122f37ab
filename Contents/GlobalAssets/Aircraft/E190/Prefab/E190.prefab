adnj
{
    "Guid": "2ed29791a3ef34918347e83b98f51aa1",
    "Version": 5,
    "ClassID": 25,
    "DataSize": 143097,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "015ba05752f724fc4a0293de95d83501",
        "1524518e7ba8645d5a8cab52d729dd65",
        "20bf06fd5f2f41a40740762167add725",
        "27111fd6caefe4cb39c116d8ffd4f432",
        "663ae23904c58fb9874e6cc56815d0a8",
        "83211ebbc6452fb6664a0a394c1e1332",
        "89d643a8f0fc6bb03a41d9773618b22b",
        "Contents/TypeScript/aircraft/E190Controller.mts",
        "Contents/TypeScript/light/AircraftStrobeLightControl.mts",
        "abbd5caae208f4b7cb60949e90da0e4c",
        "afa7df8d278942a8ff4de7e1ce1e6fe0",
        "c345ca26800020925d42ec041ddfc38e",
        "ca67386b48b3341aea210ee85041924d",
        "d5faa5abcc27a7b8e04cb30086741fed",
        "dde8279f78d372af1f41aa8c260e1b43",
        "ee87c1c92685dcb6204571977cbed725"
    ]
}
{
    "ecs": {
        "RootNode": {
            "euid": "5fc58c5dcb21acb91f447ffb36db1f0d"
        },
        "entities": {
            "5fc58c5dcb21acb91f447ffb36db1f0d": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "5fc58c5dcb21acb91f447ffb36db1f0d",
                "name": "E190",
                "prototype": 834057844181673936,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "d5faa5abcc27a7b8e04cb30086741fed",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "dde8279f78d372af1f41aa8c260e1b43",
                                            "mVisible": true
                                        },
                                        {
                                            "mMaterialPath": "c345ca26800020925d42ec041ddfc38e",
                                            "mVisible": true
                                        },
                                        {
                                            "mMaterialPath": "20bf06fd5f2f41a40740762167add725",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": false,
                            "mUseLod0Bbox": false
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::SkeletonComponentG": {
                        "Path": "015ba05752f724fc4a0293de95d83501",
                        "PhysicsPath": "",
                        "componentHash": 2294777672
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::AnimatorComponentG": {
                        "Path": "abbd5caae208f4b7cb60949e90da0e4c",
                        "componentHash": 3346675932
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": false,
                        "mIsTrigger": true,
                        "mIsKinematic": true,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 9,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": -500.0,
                                        "y": -100.0,
                                        "z": -1330.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 80.0,
                                        "y": 80.0,
                                        "z": 160.0
                                    }
                                },
                                {
                                    "position": {
                                        "x": 500.0,
                                        "y": -100.0,
                                        "z": -1330.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 80.0,
                                        "y": 80.0,
                                        "z": 160.0
                                    }
                                },
                                {
                                    "position": {
                                        "x": -940.0,
                                        "y": 70.0,
                                        "z": -1770.0
                                    },
                                    "rotate": {
                                        "x": 0.011289527639746666,
                                        "y": -0.25857269763946535,
                                        "z": -0.04213309288024902,
                                        "w": 0.9650064706802368
                                    },
                                    "halfExtents": {
                                        "x": 850.0,
                                        "y": 30.0,
                                        "z": 40.0
                                    }
                                },
                                {
                                    "position": {
                                        "x": 940.0,
                                        "y": 70.0,
                                        "z": -1770.0
                                    },
                                    "rotate": {
                                        "x": 0.011289527639746666,
                                        "y": 0.25857269763946535,
                                        "z": 0.04213309288024902,
                                        "w": 0.9650064706802368
                                    },
                                    "halfExtents": {
                                        "x": 850.0,
                                        "y": 30.0,
                                        "z": 40.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 50.0,
                                        "z": -1800.0
                                    },
                                    "rotate": {
                                        "x": 0.7071067690849304,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 0.7071067094802856
                                    },
                                    "radius": 150.0,
                                    "halfHeight": 1600.0
                                }
                            ],
                            "ConvexGeometry": []
                        },
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 524288
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "E190",
                            "ScriptPath": "Contents/TypeScript/aircraft/E190Controller.mts",
                            "ScriptEditorFields": {
                                "Fields": []
                            },
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::AnimationComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "c35d92c5d1528f963f4c281fbd6b6fe2",
                    "835c0e524e945f80844e45c8dad85081",
                    "f1f3905cefed2094364ecab0f56d090e",
                    "1029f868a551659ee2466b5834ea1cbd",
                    "abf2d7acd63bfa9101434095bbc9fefe",
                    "027b7ac510037f86904f7e2c8550538f",
                    "c8524c9627dd0fa7cc493e7031f107e5",
                    "2ec8c66a924378913e4f4abd9a51508b",
                    "6b84552e064403a70340c341c56988ce",
                    "3dde4a2c1a5d7e86c0476f6a1854d701",
                    "657cd45aed2892ba394e01f366f7a6ff",
                    "d986cc2404da2b82324b04721eb80de9",
                    "a5ac169400cb2cba8842f1c19ba9db7e",
                    "0d1b70d6ca8c89b78d467c02b6f39ea3",
                    "c854a8174dfdab8b1449b6649c37a29a",
                    "f33616aa2fab5b8f054749a7d0fcabc4",
                    "ba9752b4fd871eb3574ebc86b0c0e9ca",
                    "19a9154059f9768b5e4865aacdf6641d"
                ]
            },
            "c35d92c5d1528f963f4c281fbd6b6fe2": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "c35d92c5d1528f963f4c281fbd6b6fe2",
                "name": "NoseLandingLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 2.973552227020264,
                                "y": -95.5221710205078,
                                "z": -389.55963134765627
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0000004768371585,
                                "z": 0.9999995231628418
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 4.9016865233397769e-8,
                                "y": 0.03063130378723145,
                                "z": -0.9995306134223938,
                                "w": -5.38004542249837e-7
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 3.0,
                        "mPrtIntensity": 0.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 20000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 15.000000953674317,
                        "mOuterConeAngle": 20.000001907348634,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 10.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 7.0,
                        "mShadowSlopeBias": 0.5,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 505,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "NoseLandingLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "835c0e524e945f80844e45c8dad85081": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "835c0e524e945f80844e45c8dad85081",
                "name": "LeftLandingLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -207.5620269775391,
                                "y": 3.7601318359375,
                                "z": -1315.23828125
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0000004768371585,
                                "y": 0.9999999403953552,
                                "z": 0.9999999403953552
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.048703864216804507,
                                "y": -0.028016172349452977,
                                "z": 0.9393097758293152,
                                "w": -0.3384378254413605
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 3.0,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 16000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 26.000001907348634,
                        "mOuterConeAngle": 36.0,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 10.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 7.0,
                        "mShadowSlopeBias": 0.5,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 503,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LeftLandingLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "f1f3905cefed2094364ecab0f56d090e": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "f1f3905cefed2094364ecab0f56d090e",
                "name": "RightLandingLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 207.5620269775391,
                                "y": 3.7601318359375,
                                "z": -1315.23828125
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0,
                                "y": 0.999999701976776,
                                "z": 0.999999701976776
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.019133534282445909,
                                "y": 0.05282749980688095,
                                "z": -0.49640747904777529,
                                "w": 0.8662695288658142
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 3.0,
                        "mPrtIntensity": 6.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 16000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 26.0,
                        "mOuterConeAngle": 36.0,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 10.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 7.0,
                        "mShadowSlopeBias": 0.5,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 504,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "RightLandingLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "1029f868a551659ee2466b5834ea1cbd": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "1029f868a551659ee2466b5834ea1cbd",
                "name": "NoseTaxiLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -1.8265005350112916,
                                "y": -127.9162139892578,
                                "z": -419.5836791992188
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0000001192092896,
                                "z": 1.0000001192092896
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 1.9251675809073284e-8,
                                "y": 0.06326141953468323,
                                "z": -0.9979969263076782,
                                "w": -5.297593475006579e-7
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 2.0,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 20000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 18.0,
                        "mOuterConeAngle": 30.000001907348634,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 10.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 7.0,
                        "mShadowSlopeBias": 0.5,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 500,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "NoseTaxiLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "abf2d7acd63bfa9101434095bbc9fefe": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "abf2d7acd63bfa9101434095bbc9fefe",
                "name": "LeftTaxiLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -234.29603576660157,
                                "y": 3.7601318359375,
                                "z": -1322.46728515625
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0000004768371585,
                                "y": 0.9999999403953552,
                                "z": 0.9999998807907105
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.08238711953163147,
                                "y": 0.2779121696949005,
                                "z": -0.6143261790275574,
                                "w": -0.7338804006576538
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 2.0,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 16000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 18.0,
                        "mOuterConeAngle": 32.0,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 10.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 7.0,
                        "mShadowSlopeBias": 0.5,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 501,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LeftTaxiLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "027b7ac510037f86904f7e2c8550538f": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "027b7ac510037f86904f7e2c8550538f",
                "name": "RightTaxiLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 234.29623413085936,
                                "y": 3.7601318359375,
                                "z": -1322.46728515625
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0000001192092896,
                                "y": 0.9999996423721314,
                                "z": 0.9999996423721314
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.2878502905368805,
                                "y": -0.03413297235965729,
                                "z": -0.8273153305053711,
                                "w": -0.4811719059944153
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 2.0,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 16000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 18.0,
                        "mOuterConeAngle": 32.0,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 10.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 7.0,
                        "mShadowSlopeBias": 0.5,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 502,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "RightTaxiLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "c8524c9627dd0fa7cc493e7031f107e5": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "c8524c9627dd0fa7cc493e7031f107e5",
                "name": "LeftLogoLight",
                "prototype": 8038777479644361505,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -208.5430145263672,
                                "y": 179.************,
                                "z": -3129.************
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0000003576278689,
                                "y": 1.0000004768371585,
                                "z": 1.0000003576278689
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.923065721988678,
                                "y": 0.1969875395298004,
                                "z": -0.19698747992515565,
                                "w": -0.26521986722946169
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 511,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LeftLogoLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "2ec8c66a924378913e4f4abd9a51508b": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "2ec8c66a924378913e4f4abd9a51508b",
                "name": "RightLogoLight",
                "prototype": 8038777479644361505,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 208.54342651367188,
                                "y": 179.************,
                                "z": -3129.************
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 0.9999995827674866,
                                "y": 0.9999998807907105,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.9230663180351256,
                                "y": -0.1969866454601288,
                                "z": 0.19698631763458253,
                                "w": -0.265219509601593
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 511,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "RightLogoLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "6b84552e064403a70340c341c56988ce": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "6b84552e064403a70340c341c56988ce",
                "name": "LeftRedNavigationLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -1587.5502929687528,
                                "y": 149.6093139648436,
                                "z": -2154.************
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.00000011920929,
                                "y": 0.999999940395355,
                                "z": 0.999999940395355
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.34071865342161009,
                                "y": -0.08189960831908937,
                                "z": -0.9361168066628592,
                                "w": -0.0298090196262091
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 0.0,
                            "z": 0.0
                        },
                        "mIntensity": 1.0,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 2000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 45.0,
                        "mOuterConeAngle": 55.0,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 506,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LeftRedNavigationLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "3dde4a2c1a5d7e86c0476f6a1854d701": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "3dde4a2c1a5d7e86c0476f6a1854d701",
                "name": "LeftNavigationLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -1624.6903076171876,
                                "y": 230.104736328125,
                                "z": -2275.66552734375
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 0.999999225139618,
                                "y": 1.0000001192092896,
                                "z": 0.9999999403953552
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.4636361002922058,
                                "y": -0.8619742393493652,
                                "z": -0.08295837044715882,
                                "w": 0.1875106990337372
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 0.20000000298023225,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 5000.00048828125,
                        "mVersion": 1,
                        "mInnerConeAngle": 40.000003814697269,
                        "mOuterConeAngle": 40.000003814697269,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 506,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LeftNavigationLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "657cd45aed2892ba394e01f366f7a6ff": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "657cd45aed2892ba394e01f366f7a6ff",
                "name": "RightGreenNavigationLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 1587.************,
                                "y": 149.6090698242188,
                                "z": -2154.************
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.000000476837159,
                                "y": 1.0,
                                "z": 0.999999821186066
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.34071865342161009,
                                "y": 0.08189960831908937,
                                "z": 0.9361168066628592,
                                "w": -0.0298090196262091
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 0.042868971824645999,
                            "y": 1.0,
                            "z": 0.3207482099533081
                        },
                        "mIntensity": 1.0,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 2000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 45.0,
                        "mOuterConeAngle": 55.0,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 507,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "RightGreenNavigationLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "d986cc2404da2b82324b04721eb80de9": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "d986cc2404da2b82324b04721eb80de9",
                "name": "RightNavigationLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 1624.6903076171876,
                                "y": 230.104736328125,
                                "z": -2275.66552734375
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 0.9999995231628418,
                                "y": 0.9999995827674866,
                                "z": 0.9999995231628418
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.6508464813232422,
                                "y": -0.7468940019607544,
                                "z": -0.0908803790807724,
                                "w": -0.10143549740314484
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 2,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 0.20000000298023225,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 4999.998046875,
                        "mVersion": 1,
                        "mInnerConeAngle": 40.000003814697269,
                        "mOuterConeAngle": 40.000003814697269,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 507,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "RightNavigationLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "a5ac169400cb2cba8842f1c19ba9db7e": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "a5ac169400cb2cba8842f1c19ba9db7e",
                "name": "LightPoint_Right_Green",
                "prototype": 9257903982826669269,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 1565.0899867506738,
                                "y": 134.5989460124127,
                                "z": -2121.************
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.22583573910177105,
                                "y": -0.0250295464561169,
                                "z": -0.15804237042828185,
                                "w": 0.9609341027864332
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightMapComponentG": {
                        "mLMColorScale": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 0.0
                        },
                        "mLMColorAdd": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 0.0
                        },
                        "mLMDirectionScale": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 0.0
                        },
                        "mLMDirectionAdd": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 0.0
                        },
                        "mLMUVBias": {
                            "x": 0.0,
                            "y": 0.0
                        },
                        "mLMUVScale": {
                            "x": 0.0,
                            "y": 0.0
                        },
                        "mPRTLMUVBias": {
                            "x": 0.0,
                            "y": 0.0
                        },
                        "mPRTLMUVScale": {
                            "x": 0.0,
                            "y": 0.0
                        },
                        "mLMTransferScale": {
                            "m00": 1.0,
                            "m01": 0.0,
                            "m02": 0.0,
                            "m03": 0.0,
                            "m10": 0.0,
                            "m11": 1.0,
                            "m12": 0.0,
                            "m13": 0.0,
                            "m20": 0.0,
                            "m21": 0.0,
                            "m22": 1.0,
                            "m23": 0.0,
                            "m30": 0.0,
                            "m31": 0.0,
                            "m32": 0.0,
                            "m33": 1.0
                        },
                        "mLMTransferAdd": {
                            "m00": 1.0,
                            "m01": 0.0,
                            "m02": 0.0,
                            "m03": 0.0,
                            "m10": 0.0,
                            "m11": 1.0,
                            "m12": 0.0,
                            "m13": 0.0,
                            "m20": 0.0,
                            "m21": 0.0,
                            "m22": 1.0,
                            "m23": 0.0,
                            "m30": 0.0,
                            "m31": 0.0,
                            "m32": 0.0,
                            "m33": 1.0
                        },
                        "mLocalGroupEUID": [
                            "",
                            "",
                            "",
                            ""
                        ],
                        "mLocalLMUVBias": [
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            }
                        ],
                        "mLocalLMUVScale": [
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            }
                        ],
                        "mLocalLMTransferScale": {
                            "m00": 1.0,
                            "m01": 0.0,
                            "m02": 0.0,
                            "m03": 0.0,
                            "m10": 0.0,
                            "m11": 1.0,
                            "m12": 0.0,
                            "m13": 0.0,
                            "m20": 0.0,
                            "m21": 0.0,
                            "m22": 1.0,
                            "m23": 0.0,
                            "m30": 0.0,
                            "m31": 0.0,
                            "m32": 0.0,
                            "m33": 1.0
                        },
                        "mLocalLMTransferAdd": {
                            "m00": 1.0,
                            "m01": 0.0,
                            "m02": 0.0,
                            "m03": 0.0,
                            "m10": 0.0,
                            "m11": 1.0,
                            "m12": 0.0,
                            "m13": 0.0,
                            "m20": 0.0,
                            "m21": 0.0,
                            "m22": 1.0,
                            "m23": 0.0,
                            "m30": 0.0,
                            "m31": 0.0,
                            "m32": 0.0,
                            "m33": 1.0
                        },
                        "mInvPenumbraSize": {
                            "x": 20.0,
                            "y": 20.0,
                            "z": 20.0,
                            "w": 20.0
                        },
                        "mLMDiffuseTex": "",
                        "mLMShadowMapTex": "",
                        "mSkyOcclusionTex": "",
                        "mAOMaterialMaskTex": "",
                        "mLMTransferTex0": "",
                        "mLMTransferTex1": "",
                        "mLocalLMTransferTex": "",
                        "mAOEnable": true,
                        "mEnable": true,
                        "mUseDirectionality": true,
                        "mApplyLightMap": true,
                        "mApplyShadowMap": true,
                        "mShadowEnable": true,
                        "mGIEnable": true,
                        "componentHash": 1298647526
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "1524518e7ba8645d5a8cab52d729dd65",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "27111fd6caefe4cb39c116d8ffd4f432",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": false,
                            "mUseLod0Bbox": false
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": false,
                        "componentHash": 2559651570
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 507,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LightPoint_Right_Green",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    },
                    "cross::LightMapEditorComponentG": {
                        "mResolution": {
                            "x": 64.0,
                            "y": 64.0
                        },
                        "mDiffuseBoost": 1.0,
                        "mEnable": true,
                        "mCastShadow": true,
                        "mForceBakeLightMap": false,
                        "editorOnly": true,
                        "componentHash": 2198276411
                    }
                },
                "children": []
            },
            "0d1b70d6ca8c89b78d467c02b6f39ea3": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "0d1b70d6ca8c89b78d467c02b6f39ea3",
                "name": "LightPoint_Left_Red",
                "prototype": 9257903982826669269,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -1580.121959299112,
                                "y": 144.5512470264865,
                                "z": -2136.************
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.3019289613741813,
                                "y": -0.1756525270950944,
                                "z": 0.7192917376315845,
                                "w": 0.6005035288685245
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightMapComponentG": {
                        "mLMColorScale": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 0.0
                        },
                        "mLMColorAdd": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 0.0
                        },
                        "mLMDirectionScale": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 0.0
                        },
                        "mLMDirectionAdd": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 0.0
                        },
                        "mLMUVBias": {
                            "x": 0.0,
                            "y": 0.0
                        },
                        "mLMUVScale": {
                            "x": 0.0,
                            "y": 0.0
                        },
                        "mPRTLMUVBias": {
                            "x": 0.0,
                            "y": 0.0
                        },
                        "mPRTLMUVScale": {
                            "x": 0.0,
                            "y": 0.0
                        },
                        "mLMTransferScale": {
                            "m00": 1.0,
                            "m01": 0.0,
                            "m02": 0.0,
                            "m03": 0.0,
                            "m10": 0.0,
                            "m11": 1.0,
                            "m12": 0.0,
                            "m13": 0.0,
                            "m20": 0.0,
                            "m21": 0.0,
                            "m22": 1.0,
                            "m23": 0.0,
                            "m30": 0.0,
                            "m31": 0.0,
                            "m32": 0.0,
                            "m33": 1.0
                        },
                        "mLMTransferAdd": {
                            "m00": 1.0,
                            "m01": 0.0,
                            "m02": 0.0,
                            "m03": 0.0,
                            "m10": 0.0,
                            "m11": 1.0,
                            "m12": 0.0,
                            "m13": 0.0,
                            "m20": 0.0,
                            "m21": 0.0,
                            "m22": 1.0,
                            "m23": 0.0,
                            "m30": 0.0,
                            "m31": 0.0,
                            "m32": 0.0,
                            "m33": 1.0
                        },
                        "mLocalGroupEUID": [
                            "",
                            "",
                            "",
                            ""
                        ],
                        "mLocalLMUVBias": [
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            }
                        ],
                        "mLocalLMUVScale": [
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            },
                            {
                                "x": 0.0,
                                "y": 0.0
                            }
                        ],
                        "mLocalLMTransferScale": {
                            "m00": 1.0,
                            "m01": 0.0,
                            "m02": 0.0,
                            "m03": 0.0,
                            "m10": 0.0,
                            "m11": 1.0,
                            "m12": 0.0,
                            "m13": 0.0,
                            "m20": 0.0,
                            "m21": 0.0,
                            "m22": 1.0,
                            "m23": 0.0,
                            "m30": 0.0,
                            "m31": 0.0,
                            "m32": 0.0,
                            "m33": 1.0
                        },
                        "mLocalLMTransferAdd": {
                            "m00": 1.0,
                            "m01": 0.0,
                            "m02": 0.0,
                            "m03": 0.0,
                            "m10": 0.0,
                            "m11": 1.0,
                            "m12": 0.0,
                            "m13": 0.0,
                            "m20": 0.0,
                            "m21": 0.0,
                            "m22": 1.0,
                            "m23": 0.0,
                            "m30": 0.0,
                            "m31": 0.0,
                            "m32": 0.0,
                            "m33": 1.0
                        },
                        "mInvPenumbraSize": {
                            "x": 20.0,
                            "y": 20.0,
                            "z": 20.0,
                            "w": 20.0
                        },
                        "mLMDiffuseTex": "",
                        "mLMShadowMapTex": "",
                        "mSkyOcclusionTex": "",
                        "mAOMaterialMaskTex": "",
                        "mLMTransferTex0": "",
                        "mLMTransferTex1": "",
                        "mLocalLMTransferTex": "",
                        "mAOEnable": true,
                        "mEnable": true,
                        "mUseDirectionality": true,
                        "mApplyLightMap": true,
                        "mApplyShadowMap": true,
                        "mShadowEnable": true,
                        "mGIEnable": true,
                        "componentHash": 1298647526
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "1524518e7ba8645d5a8cab52d729dd65",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "ca67386b48b3341aea210ee85041924d",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": false,
                            "mUseLod0Bbox": false
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": false,
                        "componentHash": 2559651570
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 506,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LightPoint_Left_Red",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    },
                    "cross::LightMapEditorComponentG": {
                        "mResolution": {
                            "x": 64.0,
                            "y": 64.0
                        },
                        "mDiffuseBoost": 1.0,
                        "mEnable": true,
                        "mCastShadow": true,
                        "mForceBakeLightMap": false,
                        "editorOnly": true,
                        "componentHash": 2198276411
                    }
                },
                "children": []
            },
            "c854a8174dfdab8b1449b6649c37a29a": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "c854a8174dfdab8b1449b6649c37a29a",
                "name": "Strobe",
                "prototype": 4034668323159335804,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Strobe",
                            "ScriptPath": "Contents/TypeScript/light/AircraftStrobeLightControl.mts",
                            "ScriptEditorFields": {
                                "Fields": []
                            },
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "39c08ad3d53be594444ad6105519c457",
                    "79fe8d5724c63db2654cd895a7c8f3f2",
                    "f50e1c0d43f7038a664c48b67fdcd162",
                    "b1695828984e8fa9fb448e24b3ccd647"
                ]
            },
            "39c08ad3d53be594444ad6105519c457": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "39c08ad3d53be594444ad6105519c457",
                "name": "UpperBeaconLight",
                "prototype": 13842447287503567054,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 3.388131789017202e-21,
                                "y": 303.8125915527344,
                                "z": -810.9144287109375
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 4.329780963457767e-17,
                                "y": -0.7071068286895752,
                                "z": 0.7071068286895752,
                                "w": 4.329780963457767e-17
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 1,
                        "mColor": {
                            "x": 1.0,
                            "y": 0.0,
                            "z": 0.0
                        },
                        "mIntensity": 0.30000001192092898,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 1000.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 60.000003814697269,
                        "mOuterConeAngle": 60.000003814697269,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "UpperBeaconLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "79fe8d5724c63db2654cd895a7c8f3f2": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "79fe8d5724c63db2654cd895a7c8f3f2",
                "name": "LowerBeaconLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 6.776263578034403e-21,
                                "y": -104.29681396484377,
                                "z": -1264.25341796875
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 4.329780963457767e-17,
                                "y": -0.7071068286895752,
                                "z": 0.7071068286895752,
                                "w": 4.329780963457767e-17
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 1,
                        "mColor": {
                            "x": 1.0,
                            "y": 0.0,
                            "z": 0.0
                        },
                        "mIntensity": 0.30000001192092898,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 800.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 60.000003814697269,
                        "mOuterConeAngle": 79.99980926513672,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 510,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LowerBeaconLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "f50e1c0d43f7038a664c48b67fdcd162": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "f50e1c0d43f7038a664c48b67fdcd162",
                "name": "LeftStrobeLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -1602.86474609375,
                                "y": 163.62890625,
                                "z": -2173.************
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0000001192092896,
                                "y": 0.9999999403953552,
                                "z": 0.9999999403953552
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.3023417592048645,
                                "y": -0.60331130027771,
                                "z": 0.6541517972946167,
                                "w": -0.3415999412536621
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 1,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 0.30000001192092898,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 2800.000244140625,
                        "mVersion": 1,
                        "mInnerConeAngle": 60.000003814697269,
                        "mOuterConeAngle": 60.000003814697269,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 509,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LeftStrobeLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "b1695828984e8fa9fb448e24b3ccd647": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "b1695828984e8fa9fb448e24b3ccd647",
                "name": "RightStrobeLight",
                "prototype": 17846556440636776827,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 1602.8603515625,
                                "y": 163.62890625,
                                "z": -2173.************
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 0.9999996423721314,
                                "y": 0.9999999403953552,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.3023417294025421,
                                "y": -0.6033109426498413,
                                "z": 0.6541517972946167,
                                "w": 0.3416005671024323
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::LightComponentG": {
                        "mType": 1,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 0.30000001192092898,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngleOrRadius": 0.5357000231742859,
                        "mSoftSourceAngleOrRadius": 0.0,
                        "mSourceLength": 0.0,
                        "mRange": 2800.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 60.000003814697269,
                        "mOuterConeAngle": 79.99980926513672,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": false,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::FFSLightPointComponentG": {
                        "mEnable": true,
                        "mLightsType": 509,
                        "mVasisIndex": 0,
                        "mRunWayNumber": "",
                        "mLightsIntensity": 0,
                        "mHz": 0.0,
                        "componentHash": *********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "RightStrobeLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "f33616aa2fab5b8f054749a7d0fcabc4": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "f33616aa2fab5b8f054749a7d0fcabc4",
                "name": "LeftDecal",
                "prototype": 13334232788742207780,
                "floder": false,
                "expand": false,
                "hide": true,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -100.0,
                                "y": 215.0,
                                "z": -1069.9999999999994
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.7606690630486562,
                                "y": 1.4007241973985634,
                                "z": 1.000000000000003
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.7071067811865476,
                                "z": 0.0,
                                "w": -0.7071067811865475
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::DecalComponentG": {
                        "config": {
                            "DecalMaterial": "83211ebbc6452fb6664a0a394c1e1332",
                            "SortOrder": 0,
                            "FadeScreenSize": 0.009999999776482582,
                            "FadeStartDelay": 0.0,
                            "FadeDuration": 0.0,
                            "FadeInDuration": 0.0,
                            "FadeInStartDelay": 0.0,
                            "DecalSize": {
                                "x": 256.0,
                                "y": 256.0,
                                "z": 128.0
                            }
                        },
                        "componentHash": 3216588107
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "LeftDecal",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::DecalComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "ba9752b4fd871eb3574ebc86b0c0e9ca": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "ba9752b4fd871eb3574ebc86b0c0e9ca",
                "name": "RightDecal",
                "prototype": 13334232788742207780,
                "floder": false,
                "expand": false,
                "hide": true,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 100.0,
                                "y": 215.0,
                                "z": -1069.9999999999994
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.7606690630486566,
                                "y": 1.4007241973985634,
                                "z": 1.0000000000000034
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.7071067811865476,
                                "z": 0.0,
                                "w": 0.7071067811865476
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::DecalComponentG": {
                        "config": {
                            "DecalMaterial": "89d643a8f0fc6bb03a41d9773618b22b",
                            "SortOrder": 0,
                            "FadeScreenSize": 0.009999999776482582,
                            "FadeStartDelay": 0.0,
                            "FadeDuration": 0.0,
                            "FadeInDuration": 0.0,
                            "FadeInStartDelay": 0.0,
                            "DecalSize": {
                                "x": 256.0,
                                "y": 256.0,
                                "z": 128.0
                            }
                        },
                        "componentHash": 3216588107
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "RightDecal",
                            "ScriptPath": "",
                            "ScriptEditorFields": null,
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::DecalComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "19a9154059f9768b5e4865aacdf6641d": {
                "prefabId": "ee87c1c92685dcb6204571977cbed725",
                "prefabEuid": "01b72523e79efc8825461a8f77985309",
                "euid": "19a9154059f9768b5e4865aacdf6641d",
                "name": "Characters",
                "prototype": 5867300607375780488,
                "+floder": false,
                "+expand": false,
                "+hide": false,
                "+selectable": true,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "afa7df8d278942a8ff4de7e1ce1e6fe0",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "663ae23904c58fb9874e6cc56815d0a8",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true,
                            "mUseLod0Bbox": false
                        },
                        "+mEnabledIntersection": true,
                        "+mCacheableForDrawing": false,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null
                    },
                    "+cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": false,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": null,
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "+mLayerIndex": 0,
                        "+mNeedVoxelized": true,
                        "-mLightMask": null,
                        "-mCameraMask": null,
                        "-mRenderStage": null
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "ObjectName": "Characters",
                            "ScriptEditorFields": null,
                            "-ScriptDefaultValue": null
                        }
                    }
                },
                "children": []
            }
        }
    }
}