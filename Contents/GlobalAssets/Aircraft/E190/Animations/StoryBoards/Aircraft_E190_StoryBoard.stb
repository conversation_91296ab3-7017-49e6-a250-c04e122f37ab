<NodeGraph
  Name = "Aircraft_E190"
  NodeID = "10888"
  ConnectionID = "21172"
  HashName = "11628096001240411236">
  <Camera MaxZoom = "5" MinZoom = "-10" ZoomCount = "-1">
    <Viewport
      _WorldX = "81.257324"
      _WorldY = "-424.00168"
      _Height = "1465.0011"
      _AspectRatio = "1.6211604"/>
    <Viewport
      _WorldX = "968"
      _WorldY = "80"
      _Height = "1172"
      _AspectRatio = "1.6211604"/>
  </Camera>
  <Anim_RootNode
    ID = "10000"
    X = "4420"
    Y = "30"
    NodeJsonData = ##{
  "InPoseLinks": [
    "21099"
  ],
  "Name": "10000",
  "Type": "RootNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "RightEngine"
    ParamType = "Float"
    ID = "10012"
    X = "-182"
    Y = "100"
    NodeJsonData = ##{
  "InParams": [
    "RightEngine"
  ],
  "ReturnType": "Float",
  "Name": "10012",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "LeftEngine"
    ParamType = "Float"
    ID = "10029"
    X = "-175"
    Y = "197"
    NodeJsonData = ##{
  "InParams": [
    "LeftEngine"
  ],
  "ReturnType": "Float",
  "Name": "10029",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "FrontWheelSpin"
    ParamType = "Float"
    ID = "10045"
    X = "-203"
    Y = "294"
    NodeJsonData = ##{
  "InParams": [
    "FrontWheelSpin"
  ],
  "ReturnType": "Float",
  "Name": "10045",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "RightWheelSpin"
    ParamType = "Float"
    ID = "10048"
    X = "-203"
    Y = "391"
    NodeJsonData = ##{
  "InParams": [
    "RightWheelSpin"
  ],
  "ReturnType": "Float",
  "Name": "10048",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "LeftWheelSpin"
    ParamType = "Float"
    ID = "10051"
    X = "-196"
    Y = "490"
    NodeJsonData = ##{
  "InParams": [
    "LeftWheelSpin"
  ],
  "ReturnType": "Float",
  "Name": "10051",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Sequences/Aircraft_E190_anims_IDLE_ANIM.nda"
    Loop = "true"
    ID = "10053"
    X = "46"
    Y = "30"
    NodeJsonData = ##{
  "CompositePath": "065fdd9703cf64f4fadac38209e791b0",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    ""
  ],
  "Name": "10053",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Sequences/Aircraft_E190_anims_enginespin_ANIM.nda"
    Loop = "true"
    ID = "10063"
    X = "46"
    Y = "109"
    NodeJsonData = ##{
  "CompositePath": "be80dcbe6e4084eb7a433727cb297f7b",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20000"
  ],
  "Name": "10063",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Sequences/Aircraft_E190_anims_enginespin_ANIM.nda"
    Loop = "true"
    ID = "10078"
    X = "46"
    Y = "188"
    NodeJsonData = ##{
  "CompositePath": "be80dcbe6e4084eb7a433727cb297f7b",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20010"
  ],
  "Name": "10078",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Sequences/Aircraft_E190_front_wheelspin_ANIM.nda"
    Loop = "true"
    ID = "10084"
    X = "46"
    Y = "267"
    NodeJsonData = ##{
  "CompositePath": "5471e9ab5f7654565a4e2c0bf90ec835",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20028"
  ],
  "Name": "10084",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Sequences/Aircraft_E190_MainWheelsSpin_ANIM.nda"
    Loop = "true"
    ID = "10103"
    X = "46"
    Y = "346"
    NodeJsonData = ##{
  "CompositePath": "f1212b8f0754542dc8685c96bac2edb8",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20041"
  ],
  "Name": "10103",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_PlaySequenceNode
    SequencePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Sequences/Aircraft_E190_MainWheelsSpin_ANIM.nda"
    Loop = "true"
    ID = "10135"
    X = "46"
    Y = "426"
    NodeJsonData = ##{
  "CompositePath": "f1212b8f0754542dc8685c96bac2edb8",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "Loop": true,
  "InParamLinks": [
    "20049"
  ],
  "Name": "10135",
  "Type": "PlayCompositeNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlaySequenceNode>
  <Anim_BlendByLayeredFilterNode
    ID = "10151"
    X = "580"
    Y = "30"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20063",
    "20078",
    "20093",
    "20106",
    "20119",
    "20123"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "right_engine",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "left_engine",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "front_landing_gear_wheel",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_gear_wheel",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "left_gear_wheel",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10151",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "FrontGearRetract"
    ParamType = "Vector2"
    ID = "10170"
    X = "560"
    Y = "220"
    NodeJsonData = ##{
  "InParams": [
    "FrontGearRetract"
  ],
  "ReturnType": "Vector2",
  "Name": "10170",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "FrontWheelTurn"
    ParamType = "Vector2"
    ID = "10185"
    X = "560"
    Y = "412"
    NodeJsonData = ##{
  "InParams": [
    "FrontWheelTurn"
  ],
  "ReturnType": "Vector2",
  "Name": "10185",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "FrontWheelShocks"
    ParamType = "Vector2"
    ID = "10202"
    X = "560"
    Y = "316"
    NodeJsonData = ##{
  "InParams": [
    "FrontWheelShocks"
  ],
  "ReturnType": "Vector2",
  "Name": "10202",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "FrontGearDoor"
    ParamType = "Vector2"
    ID = "10214"
    X = "560"
    Y = "510"
    NodeJsonData = ##{
  "InParams": [
    "FrontGearDoor"
  ],
  "ReturnType": "Vector2",
  "Name": "10214",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_frontGear_UpDown_BlendSpace.nda"
    ID = "10216"
    X = "800"
    Y = "220"
    NodeJsonData = ##{
  "BlendSpacePath": "de3cf99cfb24f4cefaa092548ea79800",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20138"
  ],
  "Name": "10216",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_front_WheelTurn_BlendSpace.nda"
    ID = "10232"
    X = "800"
    Y = "412"
    NodeJsonData = ##{
  "BlendSpacePath": "c9cbe7922618b4e01843040576f3c7b6",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "21126"
  ],
  "Name": "10232",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_frontWheelShocks_UpDown_BlendSpace.nda"
    ID = "10242"
    X = "800"
    Y = "316"
    NodeJsonData = ##{
  "BlendSpacePath": "********************************",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "21117"
  ],
  "Name": "10242",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_frontGear_Doors_OpenClose_BlendSpace.nda"
    ID = "10258"
    X = "800"
    Y = "510"
    NodeJsonData = ##{
  "BlendSpacePath": "25119ea2a16cb4b78a24318caf20e2e6",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20179"
  ],
  "Name": "10258",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_BlendByLayeredFilterNode
    ID = "10263"
    X = "1410"
    Y = "30"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20903",
    "20904",
    "21143",
    "21158",
    "20961"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "root_front_gear",
          "Depth": 1
        },
        {
          "BoneName": "front_landing_gear_shock",
          "Depth": -1
        },
        {
          "BoneName": "front_shutter-3",
          "Depth": 1
        },
        {
          "BoneName": "front_shutter-4",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "front_landing_gear_shock",
          "Depth": -1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "front_landing_gear_piston",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "front_shutter-1",
          "Depth": 1
        },
        {
          "BoneName": "front_shutter-2",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10263",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "MainGearRetract"
    ParamType = "Vector2"
    ID = "10271"
    X = "1450"
    Y = "220"
    NodeJsonData = ##{
  "InParams": [
    "MainGearRetract"
  ],
  "ReturnType": "Vector2",
  "Name": "10271",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "MainWheelShocks"
    ParamType = "Vector2"
    ID = "10281"
    X = "1450"
    Y = "325"
    NodeJsonData = ##{
  "InParams": [
    "MainWheelShocks"
  ],
  "ReturnType": "Vector2",
  "Name": "10281",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "MainGearDoorBay"
    ParamType = "Vector2"
    ID = "10297"
    X = "1450"
    Y = "430"
    NodeJsonData = ##{
  "InParams": [
    "MainGearDoorBay"
  ],
  "ReturnType": "Vector2",
  "Name": "10297",
  "Type": "ParamImplNode"
}##/>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_MainGear_UpDown_BlendSpace.nda"
    ID = "10306"
    X = "1700"
    Y = "220"
    NodeJsonData = ##{
  "BlendSpacePath": "6ec963479c6874b429922b823536d170",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20211"
  ],
  "Name": "10306",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_MainGearShocks_UpDown_BlendSpace.nda"
    ID = "10324"
    X = "1700"
    Y = "325"
    NodeJsonData = ##{
  "BlendSpacePath": "49f66b80e57dc45598098880f8d49e80",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20218"
  ],
  "Name": "10324",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_MainGear_BayDoors_Open_BlendSpace.nda"
    ID = "10342"
    X = "1700"
    Y = "430"
    NodeJsonData = ##{
  "BlendSpacePath": "fa92e936847d74ef8a136eab4e31dc20",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20227"
  ],
  "Name": "10342",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_BlendByLayeredFilterNode
    ID = "10347"
    X = "2310"
    Y = "30"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20977",
    "20253",
    "20268",
    "20286"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "main_right_gear",
          "Depth": 1
        },
        {
          "BoneName": "right_coverpanel-2",
          "Depth": 1
        },
        {
          "BoneName": "right_coverpanel-3",
          "Depth": 1
        },
        {
          "BoneName": "main_right_gear_shock",
          "Depth": -1
        },
        {
          "BoneName": "main_left_gear",
          "Depth": 1
        },
        {
          "BoneName": "left_coverpanel-2",
          "Depth": 1
        },
        {
          "BoneName": "left_coverpanel-3",
          "Depth": 1
        },
        {
          "BoneName": "main_left_gear_shock",
          "Depth": -1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "main_right_gear_shock",
          "Depth": 1
        },
        {
          "BoneName": "right_gear_wheel",
          "Depth": -1
        },
        {
          "BoneName": "main_right_gear_piston",
          "Depth": 1
        },
        {
          "BoneName": "main_left_gear_shock",
          "Depth": 1
        },
        {
          "BoneName": "left_gear_wheel",
          "Depth": -1
        },
        {
          "BoneName": "main_left_gear_piston",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_coverpanel-1",
          "Depth": 1
        },
        {
          "BoneName": "left_coverpanel-1",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10347",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_SmallFlaps_UpDown_BlendSpace.nda"
    ID = "10387"
    X = "2330"
    Y = "570"
    NodeJsonData = ##{
  "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20667"
  ],
  "Name": "10387",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_SmallFlaps_UpDown_BlendSpace.nda"
    ID = "10389"
    X = "2330"
    Y = "674"
    NodeJsonData = ##{
  "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20672"
  ],
  "Name": "10389",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_SmallFlaps_UpDown_BlendSpace.nda"
    ID = "10401"
    X = "2330"
    Y = "778"
    NodeJsonData = ##{
  "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20688"
  ],
  "Name": "10401",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_SmallFlaps_UpDown_BlendSpace.nda"
    ID = "10419"
    X = "2330"
    Y = "882"
    NodeJsonData = ##{
  "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20704"
  ],
  "Name": "10419",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_SmallFlaps_UpDown_BlendSpace.nda"
    ID = "10421"
    X = "2330"
    Y = "986"
    NodeJsonData = ##{
  "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20713"
  ],
  "Name": "10421",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_BlendByLayeredFilterNode
    ID = "10476"
    X = "3110"
    Y = "30"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20383",
    "20390",
    "20405",
    "20412",
    "20428",
    "20441"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "right_spoiler_01",
          "Depth": 1
        },
        {
          "BoneName": "left_spoiler_01",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_spoiler_02",
          "Depth": 1
        },
        {
          "BoneName": "left_spoiler_02",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_spoiler_03",
          "Depth": 1
        },
        {
          "BoneName": "left_spoiler_03",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_spoiler_04",
          "Depth": 1
        },
        {
          "BoneName": "left_spoiler_04",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_spoiler_05",
          "Depth": 1
        },
        {
          "BoneName": "left_spoiler_05",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10476",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_Large_Flaps_Extend_BlendSpace.nda"
    ID = "10585"
    X = "3320"
    Y = "370"
    NodeJsonData = ##{
  "BlendSpacePath": "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20721"
  ],
  "Name": "10585",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_Large_Flaps_Extend_BlendSpace.nda"
    ID = "10589"
    X = "3320"
    Y = "478"
    NodeJsonData = ##{
  "BlendSpacePath": "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20729"
  ],
  "Name": "10589",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_OuterFlaps_UpDown_BlendSpace.nda"
    ID = "10600"
    X = "3320"
    Y = "586"
    NodeJsonData = ##{
  "BlendSpacePath": "ef56ade3d455e4c668704f1ae1b2ca6e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20746"
  ],
  "Name": "10600",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_TailFlap_BlendSpace.nda"
    ID = "10617"
    X = "3320"
    Y = "694"
    NodeJsonData = ##{
  "BlendSpacePath": "bd6e1be5113364b64927696fb68978f3",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20757"
  ],
  "Name": "10617",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_TopTailFlap_BlendSpace.nda"
    ID = "10626"
    X = "3320"
    Y = "802"
    NodeJsonData = ##{
  "BlendSpacePath": "e8982b8e09a684186b4494a8e44c703d",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20773"
  ],
  "Name": "10626",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_OuterFlaps_UpDown_BlendSpace.nda"
    ID = "10641"
    X = "3320"
    Y = "910"
    NodeJsonData = ##{
  "BlendSpacePath": "ef56ade3d455e4c668704f1ae1b2ca6e",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20781"
  ],
  "Name": "10641",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_Large_Flaps_Extend_BlendSpace.nda"
    ID = "10651"
    X = "3320"
    Y = "1018"
    NodeJsonData = ##{
  "BlendSpacePath": "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20800"
  ],
  "Name": "10651",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_PlayBlendSpaceNode
    BlendSpacePath = "Contents/GlobalAssets/Aircraft/E190/Animations/Blendspaces/Aircraft_E190_Large_Flaps_Extend_BlendSpace.nda"
    ID = "10660"
    X = "3320"
    Y = "1130"
    NodeJsonData = ##{
  "BlendSpacePath": "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
  "SyncParams": {
    "SyncMethod": "DoNotSync",
    "GroupName": "",
    "GroupRole": "CanBeLeader"
  },
  "InParamLinks": [
    "",
    "20805"
  ],
  "Name": "10660",
  "Type": "PlayBlendSpaceNode"
}##>
    <SyncParams GroupName = "" AnimSyncMethod = "0" AnimSyncGroupRole = "0"/>
  </Anim_PlayBlendSpaceNode>
  <Anim_BlendByLayeredFilterNode
    ID = "10675"
    X = "4100"
    Y = "30"
    NodeJsonData = ##{
  "InPoseLinks": [
    "20533",
    "20549",
    "20561",
    "20580",
    "20586",
    "20604",
    "20611",
    "20628",
    "20637"
  ],
  "Layers": [
    {
      "Filters": [
        {
          "BoneName": "right_inboardflap",
          "Depth": 1
        },
        {
          "BoneName": "left_inboardflap",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_midspanflap",
          "Depth": 1
        },
        {
          "BoneName": "left_midspanflap",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "left_outboard_flap",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_tailflap",
          "Depth": -1
        },
        {
          "BoneName": "left_tailflap",
          "Depth": -1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "tailflap",
          "Depth": -1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_outboard_flap",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_slats_inner",
          "Depth": 1
        },
        {
          "BoneName": "left_slats_inner",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    },
    {
      "Filters": [
        {
          "BoneName": "right_slats_outer",
          "Depth": 1
        },
        {
          "BoneName": "left_slats_outer",
          "Depth": 1
        }
      ],
      "Weight": 1.0
    }
  ],
  "Name": "10675",
  "Type": "BlendByLayeredFilterNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "Spoiler1"
    ParamType = "Vector2"
    ID = "10679"
    X = "2080"
    Y = "570"
    NodeJsonData = ##{
  "InParams": [
    "Spoiler1"
  ],
  "ReturnType": "Vector2",
  "Name": "10679",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "Spoiler2"
    ParamType = "Vector2"
    ID = "10686"
    X = "2080"
    Y = "660"
    NodeJsonData = ##{
  "InParams": [
    "Spoiler2"
  ],
  "ReturnType": "Vector2",
  "Name": "10686",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "Spoiler3"
    ParamType = "Vector2"
    ID = "10692"
    X = "2080"
    Y = "750"
    NodeJsonData = ##{
  "InParams": [
    "Spoiler3"
  ],
  "ReturnType": "Vector2",
  "Name": "10692",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "Spoiler4"
    ParamType = "Vector2"
    ID = "10697"
    X = "2080"
    Y = "840"
    NodeJsonData = ##{
  "InParams": [
    "Spoiler4"
  ],
  "ReturnType": "Vector2",
  "Name": "10697",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "Spoiler5"
    ParamType = "Vector2"
    ID = "10713"
    X = "2080"
    Y = "930"
    NodeJsonData = ##{
  "InParams": [
    "Spoiler5"
  ],
  "ReturnType": "Vector2",
  "Name": "10713",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "InboardFlaps"
    ParamType = "Vector2"
    ID = "10720"
    X = "3050"
    Y = "370"
    NodeJsonData = ##{
  "InParams": [
    "InboardFlaps"
  ],
  "ReturnType": "Vector2",
  "Name": "10720",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "MidspanFlaps"
    ParamType = "Vector2"
    ID = "10729"
    X = "3050"
    Y = "478"
    NodeJsonData = ##{
  "InParams": [
    "MidspanFlaps"
  ],
  "ReturnType": "Vector2",
  "Name": "10729",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "LeftOutboardFlap"
    ParamType = "Vector2"
    ID = "10730"
    X = "3050"
    Y = "586"
    NodeJsonData = ##{
  "InParams": [
    "LeftOutboardFlap"
  ],
  "ReturnType": "Vector2",
  "Name": "10730",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "TailFlaps"
    ParamType = "Vector2"
    ID = "10738"
    X = "3050"
    Y = "694"
    NodeJsonData = ##{
  "InParams": [
    "TailFlaps"
  ],
  "ReturnType": "Vector2",
  "Name": "10738",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "TopTailFlap"
    ParamType = "Vector2"
    ID = "10742"
    X = "3050"
    Y = "802"
    NodeJsonData = ##{
  "InParams": [
    "TopTailFlap"
  ],
  "ReturnType": "Vector2",
  "Name": "10742",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "RightOutboardFlap"
    ParamType = "Vector2"
    ID = "10759"
    X = "3050"
    Y = "910"
    NodeJsonData = ##{
  "InParams": [
    "RightOutboardFlap"
  ],
  "ReturnType": "Vector2",
  "Name": "10759",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "SlatsDeployInner"
    ParamType = "Vector2"
    ID = "10773"
    X = "3050"
    Y = "1018"
    NodeJsonData = ##{
  "InParams": [
    "SlatsDeployInner"
  ],
  "ReturnType": "Vector2",
  "Name": "10773",
  "Type": "ParamImplNode"
}##/>
  <Anim_ParamImplNode
    ParamName = "SlatsDeployOuter"
    ParamType = "Vector2"
    ID = "10775"
    X = "3050"
    Y = "1130"
    NodeJsonData = ##{
  "InParams": [
    "SlatsDeployOuter"
  ],
  "ReturnType": "Vector2",
  "Name": "10775",
  "Type": "ParamImplNode"
}##/>
  <Connection
    ID = "20000"
    OutSlotNodeID = "10012"
    OutSlotIndex = "0"
    InSlotNodeID = "10063"
    InSlotIndex = "0"/>
  <Connection
    ID = "20010"
    OutSlotNodeID = "10029"
    OutSlotIndex = "0"
    InSlotNodeID = "10078"
    InSlotIndex = "0"/>
  <Connection
    ID = "20028"
    OutSlotNodeID = "10045"
    OutSlotIndex = "0"
    InSlotNodeID = "10084"
    InSlotIndex = "0"/>
  <Connection
    ID = "20041"
    OutSlotNodeID = "10048"
    OutSlotIndex = "0"
    InSlotNodeID = "10103"
    InSlotIndex = "0"/>
  <Connection
    ID = "20049"
    OutSlotNodeID = "10051"
    OutSlotIndex = "0"
    InSlotNodeID = "10135"
    InSlotIndex = "0"/>
  <Connection
    ID = "20063"
    OutSlotNodeID = "10053"
    OutSlotIndex = "0"
    InSlotNodeID = "10151"
    InSlotIndex = "0"/>
  <Connection
    ID = "20078"
    OutSlotNodeID = "10063"
    OutSlotIndex = "0"
    InSlotNodeID = "10151"
    InSlotIndex = "1"/>
  <Connection
    ID = "20093"
    OutSlotNodeID = "10078"
    OutSlotIndex = "0"
    InSlotNodeID = "10151"
    InSlotIndex = "2"/>
  <Connection
    ID = "20106"
    OutSlotNodeID = "10084"
    OutSlotIndex = "0"
    InSlotNodeID = "10151"
    InSlotIndex = "3"/>
  <Connection
    ID = "20119"
    OutSlotNodeID = "10103"
    OutSlotIndex = "0"
    InSlotNodeID = "10151"
    InSlotIndex = "4"/>
  <Connection
    ID = "20123"
    OutSlotNodeID = "10135"
    OutSlotIndex = "0"
    InSlotNodeID = "10151"
    InSlotIndex = "5"/>
  <Connection
    ID = "20138"
    OutSlotNodeID = "10170"
    OutSlotIndex = "0"
    InSlotNodeID = "10216"
    InSlotIndex = "1"/>
  <Connection
    ID = "20179"
    OutSlotNodeID = "10214"
    OutSlotIndex = "0"
    InSlotNodeID = "10258"
    InSlotIndex = "1"/>
  <Connection
    ID = "20211"
    OutSlotNodeID = "10271"
    OutSlotIndex = "0"
    InSlotNodeID = "10306"
    InSlotIndex = "1"/>
  <Connection
    ID = "20218"
    OutSlotNodeID = "10281"
    OutSlotIndex = "0"
    InSlotNodeID = "10324"
    InSlotIndex = "1"/>
  <Connection
    ID = "20227"
    OutSlotNodeID = "10297"
    OutSlotIndex = "0"
    InSlotNodeID = "10342"
    InSlotIndex = "1"/>
  <Connection
    ID = "20253"
    OutSlotNodeID = "10306"
    OutSlotIndex = "0"
    InSlotNodeID = "10347"
    InSlotIndex = "1"/>
  <Connection
    ID = "20268"
    OutSlotNodeID = "10324"
    OutSlotIndex = "0"
    InSlotNodeID = "10347"
    InSlotIndex = "2"/>
  <Connection
    ID = "20286"
    OutSlotNodeID = "10342"
    OutSlotIndex = "0"
    InSlotNodeID = "10347"
    InSlotIndex = "3"/>
  <Connection
    ID = "20383"
    OutSlotNodeID = "10347"
    OutSlotIndex = "0"
    InSlotNodeID = "10476"
    InSlotIndex = "0"/>
  <Connection
    ID = "20390"
    OutSlotNodeID = "10387"
    OutSlotIndex = "0"
    InSlotNodeID = "10476"
    InSlotIndex = "1"/>
  <Connection
    ID = "20405"
    OutSlotNodeID = "10389"
    OutSlotIndex = "0"
    InSlotNodeID = "10476"
    InSlotIndex = "2"/>
  <Connection
    ID = "20412"
    OutSlotNodeID = "10401"
    OutSlotIndex = "0"
    InSlotNodeID = "10476"
    InSlotIndex = "3"/>
  <Connection
    ID = "20428"
    OutSlotNodeID = "10419"
    OutSlotIndex = "0"
    InSlotNodeID = "10476"
    InSlotIndex = "4"/>
  <Connection
    ID = "20441"
    OutSlotNodeID = "10421"
    OutSlotIndex = "0"
    InSlotNodeID = "10476"
    InSlotIndex = "5"/>
  <Connection
    ID = "20533"
    OutSlotNodeID = "10476"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "0"/>
  <Connection
    ID = "20549"
    OutSlotNodeID = "10585"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "1"/>
  <Connection
    ID = "20561"
    OutSlotNodeID = "10589"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "2"/>
  <Connection
    ID = "20580"
    OutSlotNodeID = "10600"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "3"/>
  <Connection
    ID = "20586"
    OutSlotNodeID = "10617"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "4"/>
  <Connection
    ID = "20604"
    OutSlotNodeID = "10626"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "5"/>
  <Connection
    ID = "20611"
    OutSlotNodeID = "10641"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "6"/>
  <Connection
    ID = "20628"
    OutSlotNodeID = "10651"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "7"/>
  <Connection
    ID = "20637"
    OutSlotNodeID = "10660"
    OutSlotIndex = "0"
    InSlotNodeID = "10675"
    InSlotIndex = "8"/>
  <Connection
    ID = "20667"
    OutSlotNodeID = "10679"
    OutSlotIndex = "0"
    InSlotNodeID = "10387"
    InSlotIndex = "1"/>
  <Connection
    ID = "20672"
    OutSlotNodeID = "10686"
    OutSlotIndex = "0"
    InSlotNodeID = "10389"
    InSlotIndex = "1"/>
  <Connection
    ID = "20688"
    OutSlotNodeID = "10692"
    OutSlotIndex = "0"
    InSlotNodeID = "10401"
    InSlotIndex = "1"/>
  <Connection
    ID = "20704"
    OutSlotNodeID = "10697"
    OutSlotIndex = "0"
    InSlotNodeID = "10419"
    InSlotIndex = "1"/>
  <Connection
    ID = "20713"
    OutSlotNodeID = "10713"
    OutSlotIndex = "0"
    InSlotNodeID = "10421"
    InSlotIndex = "1"/>
  <Connection
    ID = "20721"
    OutSlotNodeID = "10720"
    OutSlotIndex = "0"
    InSlotNodeID = "10585"
    InSlotIndex = "1"/>
  <Connection
    ID = "20729"
    OutSlotNodeID = "10729"
    OutSlotIndex = "0"
    InSlotNodeID = "10589"
    InSlotIndex = "1"/>
  <Connection
    ID = "20746"
    OutSlotNodeID = "10730"
    OutSlotIndex = "0"
    InSlotNodeID = "10600"
    InSlotIndex = "1"/>
  <Connection
    ID = "20757"
    OutSlotNodeID = "10738"
    OutSlotIndex = "0"
    InSlotNodeID = "10617"
    InSlotIndex = "1"/>
  <Connection
    ID = "20773"
    OutSlotNodeID = "10742"
    OutSlotIndex = "0"
    InSlotNodeID = "10626"
    InSlotIndex = "1"/>
  <Connection
    ID = "20781"
    OutSlotNodeID = "10759"
    OutSlotIndex = "0"
    InSlotNodeID = "10641"
    InSlotIndex = "1"/>
  <Connection
    ID = "20800"
    OutSlotNodeID = "10773"
    OutSlotIndex = "0"
    InSlotNodeID = "10651"
    InSlotIndex = "1"/>
  <Connection
    ID = "20805"
    OutSlotNodeID = "10775"
    OutSlotIndex = "0"
    InSlotNodeID = "10660"
    InSlotIndex = "1"/>
  <Connection
    ID = "20903"
    OutSlotNodeID = "10151"
    OutSlotIndex = "0"
    InSlotNodeID = "10263"
    InSlotIndex = "0"/>
  <Connection
    ID = "20904"
    OutSlotNodeID = "10216"
    OutSlotIndex = "0"
    InSlotNodeID = "10263"
    InSlotIndex = "1"/>
  <Connection
    ID = "20961"
    OutSlotNodeID = "10258"
    OutSlotIndex = "0"
    InSlotNodeID = "10263"
    InSlotIndex = "4"/>
  <Connection
    ID = "20977"
    OutSlotNodeID = "10263"
    OutSlotIndex = "0"
    InSlotNodeID = "10347"
    InSlotIndex = "0"/>
  <Connection
    ID = "21099"
    OutSlotNodeID = "10675"
    OutSlotIndex = "0"
    InSlotNodeID = "10000"
    InSlotIndex = "0"/>
  <Connection
    ID = "21117"
    OutSlotNodeID = "10202"
    OutSlotIndex = "0"
    InSlotNodeID = "10242"
    InSlotIndex = "1"/>
  <Connection
    ID = "21126"
    OutSlotNodeID = "10185"
    OutSlotIndex = "0"
    InSlotNodeID = "10232"
    InSlotIndex = "1"/>
  <Connection
    ID = "21143"
    OutSlotNodeID = "10242"
    OutSlotIndex = "0"
    InSlotNodeID = "10263"
    InSlotIndex = "2"/>
  <Connection
    ID = "21158"
    OutSlotNodeID = "10232"
    OutSlotIndex = "0"
    InSlotNodeID = "10263"
    InSlotIndex = "3"/>
</NodeGraph>
<StbProperty ApplyMode = "0" ExtractionMode = "1">
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 0.0,
  "Type": "Float",
  "Name": "RightEngine"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 0.0,
  "Type": "Float",
  "Name": "LeftEngine"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 0.0,
  "Type": "Float",
  "Name": "FrontWheelSpin"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 0.0,
  "Type": "Float",
  "Name": "RightWheelSpin"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Float" ParamJson = ##{
  "Value": 0.0,
  "Type": "Float",
  "Name": "LeftWheelSpin"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "FrontWheelTurn"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 100.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "FrontGearDoor"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "FrontGearRetract"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 100.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "FrontWheelShocks"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "MainGearRetract"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 100.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "MainWheelShocks"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 100.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "MainGearDoorBay"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "Spoiler1"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "Spoiler2"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "Spoiler3"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "Spoiler4"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "Spoiler5"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "InboardFlaps"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "LeftOutboardFlap"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "RightOutboardFlap"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "TailFlaps"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "TopTailFlap"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "MidspanFlaps"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "SlatsDeployInner"
}##/>
  <Param ParamType = "CrossEditor.AnimParameter_Vector2" ParamJson = ##{
  "Value": {
    "X": 0.0,
    "Y": 0.0
  },
  "Type": "Vector2",
  "Name": "SlatsDeployOuter"
}##/>
</StbProperty>
