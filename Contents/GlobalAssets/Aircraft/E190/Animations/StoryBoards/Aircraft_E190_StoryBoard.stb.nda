adnj
{
    "Guid": "abbd5caae208f4b7cb60949e90da0e4c",
    "Version": 5,
    "ClassID": 36,
    "DataSize": 47715,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "065fdd9703cf64f4fadac38209e791b0",
        "25119ea2a16cb4b78a24318caf20e2e6",
        "********************************",
        "49f66b80e57dc45598098880f8d49e80",
        "5471e9ab5f7654565a4e2c0bf90ec835",
        "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
        "6ec963479c6874b429922b823536d170",
        "bd6e1be5113364b64927696fb68978f3",
        "be80dcbe6e4084eb7a433727cb297f7b",
        "c9cbe7922618b4e01843040576f3c7b6",
        "de3cf99cfb24f4cefaa092548ea79800",
        "e8982b8e09a684186b4494a8e44c703d",
        "ef56ade3d455e4c668704f1ae1b2ca6e",
        "f1212b8f0754542dc8685c96bac2edb8",
        "f17d414298e5b40af94cbd2990183e5a",
        "fa92e936847d74ef8a136eab4e31dc20"
    ]
}
{
    "StoryBoard": {
        "RootMotionMode": {
            "ExtractMode": "ExtractFromEverything",
            "ApplyMode": "Apply"
        },
        "Parameters": [
            {
                "Value": 0.0,
                "Type": "Float",
                "Name": "RightEngine"
            },
            {
                "Value": 0.0,
                "Type": "Float",
                "Name": "LeftEngine"
            },
            {
                "Value": 0.0,
                "Type": "Float",
                "Name": "FrontWheelSpin"
            },
            {
                "Value": 0.0,
                "Type": "Float",
                "Name": "RightWheelSpin"
            },
            {
                "Value": 0.0,
                "Type": "Float",
                "Name": "LeftWheelSpin"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "FrontWheelTurn"
            },
            {
                "Value": {
                    "X": 100.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "FrontGearDoor"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "FrontGearRetract"
            },
            {
                "Value": {
                    "X": 100.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "FrontWheelShocks"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "MainGearRetract"
            },
            {
                "Value": {
                    "X": 100.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "MainWheelShocks"
            },
            {
                "Value": {
                    "X": 100.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "MainGearDoorBay"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "Spoiler1"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "Spoiler2"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "Spoiler3"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "Spoiler4"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "Spoiler5"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "InboardFlaps"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "LeftOutboardFlap"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "RightOutboardFlap"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "TailFlaps"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "TopTailFlap"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "MidspanFlaps"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "SlatsDeployInner"
            },
            {
                "Value": {
                    "X": 0.0,
                    "Y": 0.0
                },
                "Type": "Vector2",
                "Name": "SlatsDeployOuter"
            }
        ],
        "Name": "11628096001240411236",
        "Nodes": [
            {
                "InPoseLinks": [
                    "21099"
                ],
                "Name": "10000",
                "Type": "RootNode"
            },
            {
                "InParams": [
                    "RightEngine"
                ],
                "ReturnType": "Float",
                "Name": "10012",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "LeftEngine"
                ],
                "ReturnType": "Float",
                "Name": "10029",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "FrontWheelSpin"
                ],
                "ReturnType": "Float",
                "Name": "10045",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "RightWheelSpin"
                ],
                "ReturnType": "Float",
                "Name": "10048",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "LeftWheelSpin"
                ],
                "ReturnType": "Float",
                "Name": "10051",
                "Type": "ParamImplNode"
            },
            {
                "CompositePath": "065fdd9703cf64f4fadac38209e791b0",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    ""
                ],
                "Name": "10053",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "be80dcbe6e4084eb7a433727cb297f7b",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20000"
                ],
                "Name": "10063",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "be80dcbe6e4084eb7a433727cb297f7b",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20010"
                ],
                "Name": "10078",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "5471e9ab5f7654565a4e2c0bf90ec835",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20028"
                ],
                "Name": "10084",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "f1212b8f0754542dc8685c96bac2edb8",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20041"
                ],
                "Name": "10103",
                "Type": "PlayCompositeNode"
            },
            {
                "CompositePath": "f1212b8f0754542dc8685c96bac2edb8",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "Loop": true,
                "InParamLinks": [
                    "20049"
                ],
                "Name": "10135",
                "Type": "PlayCompositeNode"
            },
            {
                "InPoseLinks": [
                    "20063",
                    "20078",
                    "20093",
                    "20106",
                    "20119",
                    "20123"
                ],
                "Layers": [
                    {
                        "Filters": [
                            {
                                "BoneName": "right_engine",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "left_engine",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "front_landing_gear_wheel",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_gear_wheel",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "left_gear_wheel",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    }
                ],
                "Name": "10151",
                "Type": "BlendByLayeredFilterNode"
            },
            {
                "InParams": [
                    "FrontGearRetract"
                ],
                "ReturnType": "Vector2",
                "Name": "10170",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "FrontWheelTurn"
                ],
                "ReturnType": "Vector2",
                "Name": "10185",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "FrontWheelShocks"
                ],
                "ReturnType": "Vector2",
                "Name": "10202",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "FrontGearDoor"
                ],
                "ReturnType": "Vector2",
                "Name": "10214",
                "Type": "ParamImplNode"
            },
            {
                "BlendSpacePath": "de3cf99cfb24f4cefaa092548ea79800",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20138"
                ],
                "Name": "10216",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "c9cbe7922618b4e01843040576f3c7b6",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "21126"
                ],
                "Name": "10232",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "********************************",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "21117"
                ],
                "Name": "10242",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "25119ea2a16cb4b78a24318caf20e2e6",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20179"
                ],
                "Name": "10258",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "InPoseLinks": [
                    "20903",
                    "20904",
                    "21143",
                    "21158",
                    "20961"
                ],
                "Layers": [
                    {
                        "Filters": [
                            {
                                "BoneName": "root_front_gear",
                                "Depth": 1
                            },
                            {
                                "BoneName": "front_landing_gear_shock",
                                "Depth": -1
                            },
                            {
                                "BoneName": "front_shutter-3",
                                "Depth": 1
                            },
                            {
                                "BoneName": "front_shutter-4",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "front_landing_gear_shock",
                                "Depth": -1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "front_landing_gear_piston",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "front_shutter-1",
                                "Depth": 1
                            },
                            {
                                "BoneName": "front_shutter-2",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    }
                ],
                "Name": "10263",
                "Type": "BlendByLayeredFilterNode"
            },
            {
                "InParams": [
                    "MainGearRetract"
                ],
                "ReturnType": "Vector2",
                "Name": "10271",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "MainWheelShocks"
                ],
                "ReturnType": "Vector2",
                "Name": "10281",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "MainGearDoorBay"
                ],
                "ReturnType": "Vector2",
                "Name": "10297",
                "Type": "ParamImplNode"
            },
            {
                "BlendSpacePath": "6ec963479c6874b429922b823536d170",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20211"
                ],
                "Name": "10306",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "49f66b80e57dc45598098880f8d49e80",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20218"
                ],
                "Name": "10324",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "fa92e936847d74ef8a136eab4e31dc20",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20227"
                ],
                "Name": "10342",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "InPoseLinks": [
                    "20977",
                    "20253",
                    "20268",
                    "20286"
                ],
                "Layers": [
                    {
                        "Filters": [
                            {
                                "BoneName": "main_right_gear",
                                "Depth": 1
                            },
                            {
                                "BoneName": "right_coverpanel-2",
                                "Depth": 1
                            },
                            {
                                "BoneName": "right_coverpanel-3",
                                "Depth": 1
                            },
                            {
                                "BoneName": "main_right_gear_shock",
                                "Depth": -1
                            },
                            {
                                "BoneName": "main_left_gear",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_coverpanel-2",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_coverpanel-3",
                                "Depth": 1
                            },
                            {
                                "BoneName": "main_left_gear_shock",
                                "Depth": -1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "main_right_gear_shock",
                                "Depth": 1
                            },
                            {
                                "BoneName": "right_gear_wheel",
                                "Depth": -1
                            },
                            {
                                "BoneName": "main_right_gear_piston",
                                "Depth": 1
                            },
                            {
                                "BoneName": "main_left_gear_shock",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_gear_wheel",
                                "Depth": -1
                            },
                            {
                                "BoneName": "main_left_gear_piston",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_coverpanel-1",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_coverpanel-1",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    }
                ],
                "Name": "10347",
                "Type": "BlendByLayeredFilterNode"
            },
            {
                "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20667"
                ],
                "Name": "10387",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20672"
                ],
                "Name": "10389",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20688"
                ],
                "Name": "10401",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20704"
                ],
                "Name": "10419",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "f17d414298e5b40af94cbd2990183e5a",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20713"
                ],
                "Name": "10421",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "InPoseLinks": [
                    "20383",
                    "20390",
                    "20405",
                    "20412",
                    "20428",
                    "20441"
                ],
                "Layers": [
                    {
                        "Filters": [
                            {
                                "BoneName": "right_spoiler_01",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_spoiler_01",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_spoiler_02",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_spoiler_02",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_spoiler_03",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_spoiler_03",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_spoiler_04",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_spoiler_04",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_spoiler_05",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_spoiler_05",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    }
                ],
                "Name": "10476",
                "Type": "BlendByLayeredFilterNode"
            },
            {
                "BlendSpacePath": "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20721"
                ],
                "Name": "10585",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20729"
                ],
                "Name": "10589",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "ef56ade3d455e4c668704f1ae1b2ca6e",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20746"
                ],
                "Name": "10600",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "bd6e1be5113364b64927696fb68978f3",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20757"
                ],
                "Name": "10617",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "e8982b8e09a684186b4494a8e44c703d",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20773"
                ],
                "Name": "10626",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "ef56ade3d455e4c668704f1ae1b2ca6e",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20781"
                ],
                "Name": "10641",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20800"
                ],
                "Name": "10651",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "BlendSpacePath": "5b70d70a1a0d1490ebde7ec5ac8ceaa8",
                "SyncParams": {
                    "SyncMethod": "DoNotSync",
                    "GroupName": "",
                    "GroupRole": "CanBeLeader"
                },
                "InParamLinks": [
                    "",
                    "20805"
                ],
                "Name": "10660",
                "Type": "PlayBlendSpaceNode"
            },
            {
                "InPoseLinks": [
                    "20533",
                    "20549",
                    "20561",
                    "20580",
                    "20586",
                    "20604",
                    "20611",
                    "20628",
                    "20637"
                ],
                "Layers": [
                    {
                        "Filters": [
                            {
                                "BoneName": "right_inboardflap",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_inboardflap",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_midspanflap",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_midspanflap",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "left_outboard_flap",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_tailflap",
                                "Depth": -1
                            },
                            {
                                "BoneName": "left_tailflap",
                                "Depth": -1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "tailflap",
                                "Depth": -1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_outboard_flap",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_slats_inner",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_slats_inner",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    },
                    {
                        "Filters": [
                            {
                                "BoneName": "right_slats_outer",
                                "Depth": 1
                            },
                            {
                                "BoneName": "left_slats_outer",
                                "Depth": 1
                            }
                        ],
                        "Weight": 1.0
                    }
                ],
                "Name": "10675",
                "Type": "BlendByLayeredFilterNode"
            },
            {
                "InParams": [
                    "Spoiler1"
                ],
                "ReturnType": "Vector2",
                "Name": "10679",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "Spoiler2"
                ],
                "ReturnType": "Vector2",
                "Name": "10686",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "Spoiler3"
                ],
                "ReturnType": "Vector2",
                "Name": "10692",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "Spoiler4"
                ],
                "ReturnType": "Vector2",
                "Name": "10697",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "Spoiler5"
                ],
                "ReturnType": "Vector2",
                "Name": "10713",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "InboardFlaps"
                ],
                "ReturnType": "Vector2",
                "Name": "10720",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "MidspanFlaps"
                ],
                "ReturnType": "Vector2",
                "Name": "10729",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "LeftOutboardFlap"
                ],
                "ReturnType": "Vector2",
                "Name": "10730",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "TailFlaps"
                ],
                "ReturnType": "Vector2",
                "Name": "10738",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "TopTailFlap"
                ],
                "ReturnType": "Vector2",
                "Name": "10742",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "RightOutboardFlap"
                ],
                "ReturnType": "Vector2",
                "Name": "10759",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "SlatsDeployInner"
                ],
                "ReturnType": "Vector2",
                "Name": "10773",
                "Type": "ParamImplNode"
            },
            {
                "InParams": [
                    "SlatsDeployOuter"
                ],
                "ReturnType": "Vector2",
                "Name": "10775",
                "Type": "ParamImplNode"
            }
        ],
        "Links": [
            {
                "Name": "20000",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10012",
                "SourceNode": "10063"
            },
            {
                "Name": "20010",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10029",
                "SourceNode": "10078"
            },
            {
                "Name": "20028",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10045",
                "SourceNode": "10084"
            },
            {
                "Name": "20041",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10048",
                "SourceNode": "10103"
            },
            {
                "Name": "20049",
                "Type": "ParamImplLink<float>",
                "TargetNode": "10051",
                "SourceNode": "10135"
            },
            {
                "Name": "20063",
                "Type": "LocalPoseLink",
                "TargetNode": "10053",
                "SourceNode": "10151"
            },
            {
                "Name": "20078",
                "Type": "LocalPoseLink",
                "TargetNode": "10063",
                "SourceNode": "10151"
            },
            {
                "Name": "20093",
                "Type": "LocalPoseLink",
                "TargetNode": "10078",
                "SourceNode": "10151"
            },
            {
                "Name": "20106",
                "Type": "LocalPoseLink",
                "TargetNode": "10084",
                "SourceNode": "10151"
            },
            {
                "Name": "20119",
                "Type": "LocalPoseLink",
                "TargetNode": "10103",
                "SourceNode": "10151"
            },
            {
                "Name": "20123",
                "Type": "LocalPoseLink",
                "TargetNode": "10135",
                "SourceNode": "10151"
            },
            {
                "Name": "20138",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10170",
                "SourceNode": "10216"
            },
            {
                "Name": "20179",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10214",
                "SourceNode": "10258"
            },
            {
                "Name": "20211",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10271",
                "SourceNode": "10306"
            },
            {
                "Name": "20218",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10281",
                "SourceNode": "10324"
            },
            {
                "Name": "20227",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10297",
                "SourceNode": "10342"
            },
            {
                "Name": "20253",
                "Type": "LocalPoseLink",
                "TargetNode": "10306",
                "SourceNode": "10347"
            },
            {
                "Name": "20268",
                "Type": "LocalPoseLink",
                "TargetNode": "10324",
                "SourceNode": "10347"
            },
            {
                "Name": "20286",
                "Type": "LocalPoseLink",
                "TargetNode": "10342",
                "SourceNode": "10347"
            },
            {
                "Name": "20383",
                "Type": "LocalPoseLink",
                "TargetNode": "10347",
                "SourceNode": "10476"
            },
            {
                "Name": "20390",
                "Type": "LocalPoseLink",
                "TargetNode": "10387",
                "SourceNode": "10476"
            },
            {
                "Name": "20405",
                "Type": "LocalPoseLink",
                "TargetNode": "10389",
                "SourceNode": "10476"
            },
            {
                "Name": "20412",
                "Type": "LocalPoseLink",
                "TargetNode": "10401",
                "SourceNode": "10476"
            },
            {
                "Name": "20428",
                "Type": "LocalPoseLink",
                "TargetNode": "10419",
                "SourceNode": "10476"
            },
            {
                "Name": "20441",
                "Type": "LocalPoseLink",
                "TargetNode": "10421",
                "SourceNode": "10476"
            },
            {
                "Name": "20533",
                "Type": "LocalPoseLink",
                "TargetNode": "10476",
                "SourceNode": "10675"
            },
            {
                "Name": "20549",
                "Type": "LocalPoseLink",
                "TargetNode": "10585",
                "SourceNode": "10675"
            },
            {
                "Name": "20561",
                "Type": "LocalPoseLink",
                "TargetNode": "10589",
                "SourceNode": "10675"
            },
            {
                "Name": "20580",
                "Type": "LocalPoseLink",
                "TargetNode": "10600",
                "SourceNode": "10675"
            },
            {
                "Name": "20586",
                "Type": "LocalPoseLink",
                "TargetNode": "10617",
                "SourceNode": "10675"
            },
            {
                "Name": "20604",
                "Type": "LocalPoseLink",
                "TargetNode": "10626",
                "SourceNode": "10675"
            },
            {
                "Name": "20611",
                "Type": "LocalPoseLink",
                "TargetNode": "10641",
                "SourceNode": "10675"
            },
            {
                "Name": "20628",
                "Type": "LocalPoseLink",
                "TargetNode": "10651",
                "SourceNode": "10675"
            },
            {
                "Name": "20637",
                "Type": "LocalPoseLink",
                "TargetNode": "10660",
                "SourceNode": "10675"
            },
            {
                "Name": "20667",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10679",
                "SourceNode": "10387"
            },
            {
                "Name": "20672",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10686",
                "SourceNode": "10389"
            },
            {
                "Name": "20688",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10692",
                "SourceNode": "10401"
            },
            {
                "Name": "20704",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10697",
                "SourceNode": "10419"
            },
            {
                "Name": "20713",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10713",
                "SourceNode": "10421"
            },
            {
                "Name": "20721",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10720",
                "SourceNode": "10585"
            },
            {
                "Name": "20729",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10729",
                "SourceNode": "10589"
            },
            {
                "Name": "20746",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10730",
                "SourceNode": "10600"
            },
            {
                "Name": "20757",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10738",
                "SourceNode": "10617"
            },
            {
                "Name": "20773",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10742",
                "SourceNode": "10626"
            },
            {
                "Name": "20781",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10759",
                "SourceNode": "10641"
            },
            {
                "Name": "20800",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10773",
                "SourceNode": "10651"
            },
            {
                "Name": "20805",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10775",
                "SourceNode": "10660"
            },
            {
                "Name": "20903",
                "Type": "LocalPoseLink",
                "TargetNode": "10151",
                "SourceNode": "10263"
            },
            {
                "Name": "20904",
                "Type": "LocalPoseLink",
                "TargetNode": "10216",
                "SourceNode": "10263"
            },
            {
                "Name": "20961",
                "Type": "LocalPoseLink",
                "TargetNode": "10258",
                "SourceNode": "10263"
            },
            {
                "Name": "20977",
                "Type": "LocalPoseLink",
                "TargetNode": "10263",
                "SourceNode": "10347"
            },
            {
                "Name": "21099",
                "Type": "LocalPoseLink",
                "TargetNode": "10675",
                "SourceNode": "10000"
            },
            {
                "Name": "21117",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10202",
                "SourceNode": "10242"
            },
            {
                "Name": "21126",
                "Type": "ParamImplLink<vector2>",
                "TargetNode": "10185",
                "SourceNode": "10232"
            },
            {
                "Name": "21143",
                "Type": "LocalPoseLink",
                "TargetNode": "10242",
                "SourceNode": "10263"
            },
            {
                "Name": "21158",
                "Type": "LocalPoseLink",
                "TargetNode": "10232",
                "SourceNode": "10263"
            }
        ]
    }
}