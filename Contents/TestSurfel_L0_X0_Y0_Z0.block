adnj
{
    "Guid": "447acde6c5a67487a248274ef031c934",
    "Version": 5,
    "ClassID": 26,
    "DataSize": 329848,
    "ContentType": 2,
    "IsStreamFile": false,
    "Dependency": [
        "08bf2626b515b41d3878329fccadadb5",
        "1052b67f612354955bd32c498c756367",
        "1137201b846e64b9bb4dd627eb50a8b7",
        "1164fefdfd2b14e70bcc89ff6c905a09",
        "149206cd8aee74d728a9ea7d46155fed",
        "14feedbabb0d34de5b5f4b9338bb8692",
        "16879475f050344a08ec92c3449e00f6",
        "1898d267a26de41efa2841b45db5b0de",
        "1c7f0ceb5971545c6972cc9c80093d74",
        "1c82a27f6f1504e01afcb7e8c2e64117",
        "21075dfc9965648f4aaa14c8a59d9959",
        "311c0b95ed39747edb927cfd9578e482",
        "36bee4d32c71045e1bfc05c07683f933",
        "37a83bccf3a9f41ea8e9ffa2c91dd337",
        "3815658f29f5143509d41cbb71e0107d",
        "38da7ae674d8248548ec5cccaf5ae06a",
        "4a01d9d7939d74065952fb94b70cb158",
        "4d358c3456bea492082a81de46c4e8f7",
        "4e029b08dd5d84c7e9ebe3d6f69773cc",
        "4ff7a315d9fe44edbb6c199fa6c55c42",
        "5159c91b6514a4f238630da6520dff66",
        "5ce8f63a04a9a40dfb08b9fb34bf36b2",
        "60cded566dafa4687a0e2f5928ee4ea6",
        "646e14ced137442609c5f508accd2a76",
        "64d511ef6cf254d308b0971a9548f509",
        "67a5ccb0fc61a4fb2b7b351e4f44cdde",
        "683062091af06418ab71a59de92fd89c",
        "6876f36fc5fa44f359e72ea334c05fac",
        "6cc33f76e71334530b76ce453ec50475",
        "71818c39cdc224fc5a3aca0f8940c514",
        "752b14e1c2bbe4b3daca99b220097a2a",
        "77f6c7d64138f4e8a99f0c321ad5ac98",
        "7a4a8d29daceb4c14ab45ead06379db6",
        "7a6b847d472f34e04b5d439bed60d3b7",
        "7e44733a3307b0b80a41c661792facc4",
        "7fb139db9c1bb4b48a86a79c874ad3dc",
        "81375b2984621416ea2ff11bdd29c155",
        "823a96d223a1e483fa928e65bed3d7ef",
        "89da77ea5d01e4a389b3315234e840c0",
        "8f36519c8b19c4ba2b7df0e9d8b31613",
        "90e1309bd9a7a45729465fe186a516bf",
        "93f7aee102a7c4ad287f1acf64faaadc",
        "94614fc616afe427ba4bbcc2bb9ac626",
        "951561effb0d244209c176543f3b20d3",
        "96f309a3ce597443182396dd9f667b6c",
        "9b0219907dbd546f085e7b954d9bd351",
        "9b085785e8e7f4db386b36629052a008",
        "9bd1ed5fe331840a39af5c27cc8f9b95",
        "D:/UGit/CrossEngine/bin/Visual_Studio_17_2022_Win64_md/RelWithDebInfo",
        "a6df954d48cda4d0a81bf6ee83468ff6",
        "a95f0c0b257264bb29815ac06e157646",
        "a978747a510f3ca6114e2b69e192600b",
        "aa577600d4f7c4919908ccf5181b8c34",
        "b32755536ef12471187fa155dd45f28c",
        "b397cf4fdef974e278ce33614207483d",
        "be5072a03d7764b86b2d3f8d252190bc",
        "bf39e997488944d378f42f28fb2175a0",
        "c4bb1a6ab12ec49fda4c8a3cd8bd6443",
        "c4f411872a54940c29a5a896c6054f17",
        "c5c127efd30f749d6a4006758c7f9bd3",
        "caffa1cbda63e4a0385a625e544ae69b",
        "d38789cffc2551804f4a80099768faae",
        "dc3059147e4ca423cbf15a58537313a6",
        "ea9c13c42a8cf47db96319a5117c0e8c",
        "ec2ebaed666654694b1618cd7ed8e294",
        "ecc7e81963d7a46229fe22307a12ac80",
        "eebdf646a5d1b4de29bf2c0f2be282ab",
        "f8bec310b4c7b4b38b5c67af578c8d51",
        "fb12d4f8501cd44c184eacb49577b1a9"
    ]
}
{
    "ecs": {
        "entities": {
            "75af4e9878894449da3a1b0ca5862627": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "75af4e9878894449da3a1b0ca5862627",
                "name": "Scene",
                "prototype": 4034668323159335804,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Scene",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "a0c03346489444c8ba9a11b4a6665f94",
                    "a8724195be7cb4b48a921f26c1abde17",
                    "edf53fa699a2a47e9af115cfb8a9c6ea"
                ],
                "rootParent": "e372af75000000001823c72200000000"
            },
            "a0c03346489444c8ba9a11b4a6665f94": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "a0c03346489444c8ba9a11b4a6665f94",
                "name": "GroundCube",
                "prototype": 5867300607375780488,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 3500.0
                            },
                            "mTRSFlag": 35,
                            "mScale": {
                                "x": 39.99999999999999,
                                "y": 0.10000000149011602,
                                "z": 39.99999999999999
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "9b0219907dbd546f085e7b954d9bd351",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "a978747a510f3ca6114e2b69e192600b",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": true,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 1,
                        "mCollisionMask": 1,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": null,
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 524288
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "GroundCube",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "a8724195be7cb4b48a921f26c1abde17": {
                "prefabId": "bf39e997488944d378f42f28fb2175a0",
                "prefabEuid": "16b850ad4319040fab6bcd1728cff4a4",
                "euid": "a8724195be7cb4b48a921f26c1abde17",
                "expand": false,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    }
                },
                "children": [
                    "1890032397e09585b64db2f5af26bb59",
                    "030cb43ea41884b6cd4774b9d007f145"
                ]
            },
            "1890032397e09585b64db2f5af26bb59": {
                "prefabId": "bf39e997488944d378f42f28fb2175a0",
                "prefabEuid": "f94b9a4e8ec574e079dd32dd7835b974",
                "euid": "1890032397e09585b64db2f5af26bb59",
                "prototype": 17145047431506164927,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "77f6c7d64138f4e8a99f0c321ad5ac98",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "ecc7e81963d7a46229fe22307a12ac80",
                                            "mVisible": true
                                        },
                                        {
                                            "mMaterialPath": "ea9c13c42a8cf47db96319a5117c0e8c",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    }
                },
                "children": [
                    "7ec11704df49a881e74d97119d99ac4e",
                    "a78308b8ca78459a004596dba26310cf",
                    "5975582691d6d896d8413381850d1201",
                    "8bf8db10b55aa389e5467a02632e7d04"
                ]
            },
            "7ec11704df49a881e74d97119d99ac4e": {
                "prefabId": "bf39e997488944d378f42f28fb2175a0",
                "prefabEuid": "43993974a36ea4314b32e35070f316ab",
                "euid": "7ec11704df49a881e74d97119d99ac4e",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "6876f36fc5fa44f359e72ea334c05fac",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "a78308b8ca78459a004596dba26310cf": {
                "prefabId": "bf39e997488944d378f42f28fb2175a0",
                "prefabEuid": "33b826ad176ac406f8d5170e09e5ff53",
                "euid": "a78308b8ca78459a004596dba26310cf",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "93f7aee102a7c4ad287f1acf64faaadc",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "5975582691d6d896d8413381850d1201": {
                "prefabId": "bf39e997488944d378f42f28fb2175a0",
                "prefabEuid": "7b4363601fccd42aaa0028f53de0012d",
                "euid": "5975582691d6d896d8413381850d1201",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "f8bec310b4c7b4b38b5c67af578c8d51",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "8bf8db10b55aa389e5467a02632e7d04": {
                "prefabId": "bf39e997488944d378f42f28fb2175a0",
                "prefabEuid": "6b80d9162651f4f01a21d631b38c3adc",
                "euid": "8bf8db10b55aa389e5467a02632e7d04",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "3815658f29f5143509d41cbb71e0107d",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "030cb43ea41884b6cd4774b9d007f145": {
                "prefabId": "bf39e997488944d378f42f28fb2175a0",
                "prefabEuid": "6f6613d248dce41fb8fc365cbb37415f",
                "euid": "030cb43ea41884b6cd4774b9d007f145",
                "prototype": 17145047431506164927,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "7a6b847d472f34e04b5d439bed60d3b7",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "ec2ebaed666654694b1618cd7ed8e294",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    }
                },
                "children": []
            },
            "edf53fa699a2a47e9af115cfb8a9c6ea": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "776d514a5a29e4ad6b457bf23541aacb",
                "euid": "edf53fa699a2a47e9af115cfb8a9c6ea",
                "hide": true,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 83.66796875,
                                "y": 5.0,
                                "z": 3500.0
                            },
                            "-mUnesedPadding": null
                        }
                    }
                },
                "children": [
                    "07d2dae795b9fda4c04916917c0f50e3",
                    "69816837d2df4b87774422bb702ca06e",
                    "0695fd01f6788ebd1a4ed98a4e320a4a",
                    "32a18c9c05d33d8b7647ec24a8d06d15",
                    "65c99bdaf5c5069f3b451229d466245f",
                    "ee14ec298bd1359dd24eb5a48b87617b",
                    "b871269344d7ef9cb34e356d91ded799",
                    "eee6f4278a92dbbe6b47ce31d0c461c5",
                    "98ff543504708f90c64481707d8df96e",
                    "e0da65b7a98faeae23409982475a02b0",
                    "aeeed31643b948abfc41a3de0d92a266",
                    "c40df2a5dcfb4386024d47bc9502ad9d",
                    "7b11aa25003e29a96f41cf646e9faac1",
                    "334efef82130e6a33249a42b5fc24e1a",
                    "e26238c4d5314682994c9b6318608502",
                    "0ab2f3647c3c6584d141b7b96bab0542",
                    "2fccf5d5ead4d88f69492cb41a0ec886",
                    "2aeede4d02b2aead3246255a292ac2a1",
                    "a8b9ae3176e021ba8048172a8a83d3d9",
                    "7c8613b63cbfd985474f8becc1e2c482",
                    "3b0781e9c3e48780154bbe174fbc781d",
                    "fc96838216a3d2b04c4c2ea83a05fce6",
                    "e1895510c6c6a4936943793fede0ff74",
                    "9c875128ad8af398cc45ff0585561af1",
                    "51483630f8dc7c8c1e48f26c2fdacc47",
                    "378e3d796cace5b7e9482ded0299c54e",
                    "d6b10e22bbbaacb0e245dfccaa82a833",
                    "11349a299f277688c44329b91c661737",
                    "f8dc658aa6b8deadb54e3b2fe0ad7756",
                    "a57548b730fb8486904776a4ad6a782d",
                    "206623036d0463ba304bf76ca76702ce",
                    "a9a6de609bb318931f491513ddcd5d48",
                    "7ef48f51e1dc1bb07642298f3415968f",
                    "05721a94628d78b9ca4ebb50ff304f1b",
                    "ac687f9df8b0e49df44ff94b6d6db2c8",
                    "574f85db7e36f8a0524703f452d4223c",
                    "72fa7c3d50c71cacdc4f4a2ae4a5f707",
                    "f28360eac054d9b6b84b927552c154ba",
                    "362e231942c4038b084d1eb83b9fccf3",
                    "5b07036f70e50d8c9742acf4d0908d57",
                    "ec4223bc50378191df4f2b57dc336edd",
                    "3e0c2d870fdf1e9fd24f3403e1bfb95e",
                    "9f70046fba67fd832644befa54df14a2",
                    "056f000e02efaf99844149e740e78dd6",
                    "3879c953be486380e14c008833564faf",
                    "b9182dc36be01dab94410f233679d608",
                    "b222cbe24cef25bcc8419df8ad6c6aa6",
                    "b3518dd69e73779161429cf745984757",
                    "2a845ab46790b3822646dd0cee4c1ff1",
                    "4f0e356f69c3c08f7e4653262c271ea6"
                ]
            },
            "07d2dae795b9fda4c04916917c0f50e3": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "8d835db2684bf40feb8fff62d79bfb91",
                "euid": "07d2dae795b9fda4c04916917c0f50e3",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "a95f0c0b257264bb29815ac06e157646",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "69816837d2df4b87774422bb702ca06e": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "5c28cf28cd0214e2e85405b55d689151",
                "euid": "69816837d2df4b87774422bb702ca06e",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "b397cf4fdef974e278ce33614207483d",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "0695fd01f6788ebd1a4ed98a4e320a4a": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "2f89124f359cc478ca32952e9da9eda9",
                "euid": "0695fd01f6788ebd1a4ed98a4e320a4a",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "37a83bccf3a9f41ea8e9ffa2c91dd337",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "32a18c9c05d33d8b7647ec24a8d06d15": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "2837c404044a4491499b5016034af6b0",
                "euid": "32a18c9c05d33d8b7647ec24a8d06d15",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "1c82a27f6f1504e01afcb7e8c2e64117",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "65c99bdaf5c5069f3b451229d466245f": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "2b496850febf34ed5acc8871e9181ee5",
                "euid": "65c99bdaf5c5069f3b451229d466245f",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "6cc33f76e71334530b76ce453ec50475",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "ee14ec298bd1359dd24eb5a48b87617b": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "58ac3fef26e024e9e82e70909af53504",
                "euid": "ee14ec298bd1359dd24eb5a48b87617b",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "5159c91b6514a4f238630da6520dff66",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "b871269344d7ef9cb34e356d91ded799": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "e1349f9a1e9764135a010a496c499617",
                "euid": "b871269344d7ef9cb34e356d91ded799",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "4ff7a315d9fe44edbb6c199fa6c55c42",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "eee6f4278a92dbbe6b47ce31d0c461c5": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "b9dd72f2734da4ed6a1c7c44c97614cd",
                "euid": "eee6f4278a92dbbe6b47ce31d0c461c5",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "16879475f050344a08ec92c3449e00f6",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "98ff543504708f90c64481707d8df96e": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "5b30944d16ae94637a42d0436f6d1b52",
                "euid": "98ff543504708f90c64481707d8df96e",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "1c7f0ceb5971545c6972cc9c80093d74",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "e0da65b7a98faeae23409982475a02b0": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "6df227192128743379545b2d628f50a3",
                "euid": "e0da65b7a98faeae23409982475a02b0",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "21075dfc9965648f4aaa14c8a59d9959",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "aeeed31643b948abfc41a3de0d92a266": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "4fd6d09a611564047a97bba026ce5cf7",
                "euid": "aeeed31643b948abfc41a3de0d92a266",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "823a96d223a1e483fa928e65bed3d7ef",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "c40df2a5dcfb4386024d47bc9502ad9d": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "ce1572085cd4a44d89e6172e240aa9bd",
                "euid": "c40df2a5dcfb4386024d47bc9502ad9d",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "be5072a03d7764b86b2d3f8d252190bc",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "7b11aa25003e29a96f41cf646e9faac1": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "098ce9c633b5f40dcb5a218168ee51c3",
                "euid": "7b11aa25003e29a96f41cf646e9faac1",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "4d358c3456bea492082a81de46c4e8f7",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "334efef82130e6a33249a42b5fc24e1a": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "24ca4008012854be8b8c4c6a83ac1225",
                "euid": "334efef82130e6a33249a42b5fc24e1a",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "683062091af06418ab71a59de92fd89c",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "e26238c4d5314682994c9b6318608502": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "6d3b79be7a7e24e6cac42f15ea0a78a4",
                "euid": "e26238c4d5314682994c9b6318608502",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "38da7ae674d8248548ec5cccaf5ae06a",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "0ab2f3647c3c6584d141b7b96bab0542": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "5f1fe6d0a3026436ebeb3a03c3def70d",
                "euid": "0ab2f3647c3c6584d141b7b96bab0542",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "67a5ccb0fc61a4fb2b7b351e4f44cdde",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "2fccf5d5ead4d88f69492cb41a0ec886": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "b889b4a3c8076459e8cf7e7906b5b5ce",
                "euid": "2fccf5d5ead4d88f69492cb41a0ec886",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "c4bb1a6ab12ec49fda4c8a3cd8bd6443",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "2aeede4d02b2aead3246255a292ac2a1": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "c34bac4c2462b426dac5aaa96f0c523c",
                "euid": "2aeede4d02b2aead3246255a292ac2a1",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "7a4a8d29daceb4c14ab45ead06379db6",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "a8b9ae3176e021ba8048172a8a83d3d9": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "573129f7c8ef249598019075a3c08f7e",
                "euid": "a8b9ae3176e021ba8048172a8a83d3d9",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "7fb139db9c1bb4b48a86a79c874ad3dc",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "7c8613b63cbfd985474f8becc1e2c482": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "9fb037e1ff05345029db97acd2ff8c4b",
                "euid": "7c8613b63cbfd985474f8becc1e2c482",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "1164fefdfd2b14e70bcc89ff6c905a09",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "3b0781e9c3e48780154bbe174fbc781d": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "c61ce64e77bca44f987bd8291f9b1e42",
                "euid": "3b0781e9c3e48780154bbe174fbc781d",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "64d511ef6cf254d308b0971a9548f509",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "fc96838216a3d2b04c4c2ea83a05fce6": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "98a826d6f036f4d4dbaf1c50d11f348c",
                "euid": "fc96838216a3d2b04c4c2ea83a05fce6",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "1137201b846e64b9bb4dd627eb50a8b7",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "e1895510c6c6a4936943793fede0ff74": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "33220b626d17f4e62a0ef1e3045b5e35",
                "euid": "e1895510c6c6a4936943793fede0ff74",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "b32755536ef12471187fa155dd45f28c",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "9c875128ad8af398cc45ff0585561af1": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "1c8adda6a33654af3a14400607c5b87a",
                "euid": "9c875128ad8af398cc45ff0585561af1",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "90e1309bd9a7a45729465fe186a516bf",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "51483630f8dc7c8c1e48f26c2fdacc47": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "39b8243d1520344fc9a01419fc12afa4",
                "euid": "51483630f8dc7c8c1e48f26c2fdacc47",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "89da77ea5d01e4a389b3315234e840c0",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "378e3d796cace5b7e9482ded0299c54e": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "b67091d24c6e941abb50fdf8f78fac15",
                "euid": "378e3d796cace5b7e9482ded0299c54e",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "4a01d9d7939d74065952fb94b70cb158",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "d6b10e22bbbaacb0e245dfccaa82a833": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "31694ed73fe4a4d8e8a824306ea77952",
                "euid": "d6b10e22bbbaacb0e245dfccaa82a833",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "14feedbabb0d34de5b5f4b9338bb8692",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "11349a299f277688c44329b91c661737": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "dc47637429d6d46158ff621c04f2b917",
                "euid": "11349a299f277688c44329b91c661737",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "c5c127efd30f749d6a4006758c7f9bd3",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "f8dc658aa6b8deadb54e3b2fe0ad7756": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "c97bd3198415042c4ac9c4469e86000a",
                "euid": "f8dc658aa6b8deadb54e3b2fe0ad7756",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "9bd1ed5fe331840a39af5c27cc8f9b95",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "a57548b730fb8486904776a4ad6a782d": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "34c9e4bc589674afb8a7958f390f8b09",
                "euid": "a57548b730fb8486904776a4ad6a782d",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "1052b67f612354955bd32c498c756367",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "206623036d0463ba304bf76ca76702ce": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "4c7a5b318eb624031b87b101d1c02bc5",
                "euid": "206623036d0463ba304bf76ca76702ce",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "1898d267a26de41efa2841b45db5b0de",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "a9a6de609bb318931f491513ddcd5d48": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "81a145906a08d495cb4963d429878f35",
                "euid": "a9a6de609bb318931f491513ddcd5d48",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "dc3059147e4ca423cbf15a58537313a6",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "7ef48f51e1dc1bb07642298f3415968f": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "4d1093be0417c4d8fb5ddc33a8d5cb78",
                "euid": "7ef48f51e1dc1bb07642298f3415968f",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "951561effb0d244209c176543f3b20d3",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "05721a94628d78b9ca4ebb50ff304f1b": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "b72365c7ab4b247b19363d75a7fe7fe2",
                "euid": "05721a94628d78b9ca4ebb50ff304f1b",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "eebdf646a5d1b4de29bf2c0f2be282ab",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "ac687f9df8b0e49df44ff94b6d6db2c8": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "b2ebc868b5a74464b82ba844c50b3d5f",
                "euid": "ac687f9df8b0e49df44ff94b6d6db2c8",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "08bf2626b515b41d3878329fccadadb5",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "574f85db7e36f8a0524703f452d4223c": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "fc56480b6fb694496a7b0c0355c5080b",
                "euid": "574f85db7e36f8a0524703f452d4223c",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "c4f411872a54940c29a5a896c6054f17",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "72fa7c3d50c71cacdc4f4a2ae4a5f707": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "33c45aa0ee7d84faa8c5e222cab7a687",
                "euid": "72fa7c3d50c71cacdc4f4a2ae4a5f707",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "646e14ced137442609c5f508accd2a76",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "f28360eac054d9b6b84b927552c154ba": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "7df87ce8c92cd4db08329b5259db3e32",
                "euid": "f28360eac054d9b6b84b927552c154ba",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "4e029b08dd5d84c7e9ebe3d6f69773cc",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "362e231942c4038b084d1eb83b9fccf3": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "f195ae21a0cda4ff8b0fbde5aa1917a1",
                "euid": "362e231942c4038b084d1eb83b9fccf3",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "aa577600d4f7c4919908ccf5181b8c34",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "5b07036f70e50d8c9742acf4d0908d57": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "845d964589eb14d1ab193fa5c3784236",
                "euid": "5b07036f70e50d8c9742acf4d0908d57",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "fb12d4f8501cd44c184eacb49577b1a9",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "ec4223bc50378191df4f2b57dc336edd": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "43b081be38a904e9a908c5e2162ef783",
                "euid": "ec4223bc50378191df4f2b57dc336edd",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "caffa1cbda63e4a0385a625e544ae69b",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "3e0c2d870fdf1e9fd24f3403e1bfb95e": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "0b77b3ca36a04409a8c338e8b1f98c4f",
                "euid": "3e0c2d870fdf1e9fd24f3403e1bfb95e",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "311c0b95ed39747edb927cfd9578e482",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "9f70046fba67fd832644befa54df14a2": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "0de99a77756ff486088be85c2783ed60",
                "euid": "9f70046fba67fd832644befa54df14a2",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "36bee4d32c71045e1bfc05c07683f933",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "056f000e02efaf99844149e740e78dd6": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "a622d5b7a02b4418491f64a668a79480",
                "euid": "056f000e02efaf99844149e740e78dd6",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "71818c39cdc224fc5a3aca0f8940c514",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "3879c953be486380e14c008833564faf": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "df414157b5cc64f14b1b21c028bd9ceb",
                "euid": "3879c953be486380e14c008833564faf",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "752b14e1c2bbe4b3daca99b220097a2a",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "b9182dc36be01dab94410f233679d608": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "af7e1056e890949e9b22bafefcefef73",
                "euid": "b9182dc36be01dab94410f233679d608",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "96f309a3ce597443182396dd9f667b6c",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "b222cbe24cef25bcc8419df8ad6c6aa6": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "d98d61011383c403da9be0e5dd04baa0",
                "euid": "b222cbe24cef25bcc8419df8ad6c6aa6",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "a6df954d48cda4d0a81bf6ee83468ff6",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "b3518dd69e73779161429cf745984757": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "f2354f0a912f94c718af54dc58560faf",
                "euid": "b3518dd69e73779161429cf745984757",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "94614fc616afe427ba4bbcc2bb9ac626",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "2a845ab46790b3822646dd0cee4c1ff1": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "4dcc15b39472b4714bd77ed956d0b79a",
                "euid": "2a845ab46790b3822646dd0cee4c1ff1",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "149206cd8aee74d728a9ea7d46155fed",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "4f0e356f69c3c08f7e4653262c271ea6": {
                "prefabId": "9b085785e8e7f4db386b36629052a008",
                "prefabEuid": "2ed2fb64807434d48b2fc930ace4b17d",
                "euid": "4f0e356f69c3c08f7e4653262c271ea6",
                "prototype": 17521096633800154225,
                "components": {
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "-mUnesedPadding": null
                        }
                    },
                    "cross::ModelComponentG": {
                        "+mChangeableModels": [],
                        "+mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "5ce8f63a04a9a40dfb08b9fb34bf36b2",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "+mEnabledIntersection": true,
                        "+mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "-mModels": null,
                        "-mEnableSubModelCulling": null
                    },
                    "cross::PhysicsComponentG": {
                        "+mIsDynamic": false,
                        "+mUseMeshCollision": false,
                        "+mCollisionMask": 0,
                        "+mEnableCollisionEvent": false,
                        "mExtraCollision": {
                            "mBoxGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "halfExtents": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    }
                                }
                            ],
                            "mSphereGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "radius": 0.0
                                }
                            ],
                            "mCapsuleGeometry": [
                                {
                                    "position": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0
                                    },
                                    "rotate": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 1.0
                                    },
                                    "radius": 0.0,
                                    "halfHeight": 0.0
                                }
                            ],
                            "+ConvexGeometry": []
                        },
                        "-mEnableSimulate": null,
                        "-mOnlyUseExtraCollision": null,
                        "-mCollisionChannel": null
                    }
                },
                "children": []
            },
            "c1ce8b3e70b5845f6a909a328d113977": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "c1ce8b3e70b5845f6a909a328d113977",
                "name": "Env_Light",
                "prototype": 4034668323159335804,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 3,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Env_Light",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [
                    "8ff51a832ad354c0cbd910bec363f8f9",
                    "b2742605b6f57458987b176f524be1d3",
                    "14c966ca17d0b442eb356524a776cdd0",
                    "11c65ee36e0e04c0dae3f4ceb17fdf12",
                    "bc37da904f20a47259bfca4648379abf",
                    "7e57cc0a4958d4372b08d43fd500dcb8",
                    "21ceb9487311b441188d475e5dfbd548",
                    "60c9b1d2f9ebd48d1a75b4afda82d969"
                ],
                "rootParent": "e372af75000000001823c72200000000"
            },
            "8ff51a832ad354c0cbd910bec363f8f9": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "8ff51a832ad354c0cbd910bec363f8f9",
                "name": "PostProcessVolume",
                "prototype": 13135681827418360850,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 3,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::PostProcessVolumeComponentG": {
                        "PostProcessVolumeSettings": {
                            "mType": 0,
                            "BlendSettings": {
                                "Priority": 0,
                                "Weight": 1.0,
                                "Radius": 100.0
                            },
                            "ChromaticAberration": {
                                "enable": false,
                                "Intensity": 0.10000000149011612,
                                "Range": 1.0
                            },
                            "Vignette": {
                                "enable": false,
                                "VignetteIntensity": 0.4000000059604645
                            },
                            "mTonemapSettings": {
                                "enable": true,
                                "WhiteBalanceSetting": {
                                    "EnableWhiteBalance": false,
                                    "WhiteTemperature": 6500.0,
                                    "WhiteTint": 0.0
                                },
                                "LinearColorAdajustment": {
                                    "EnableLinearColorAdjustment": false,
                                    "Global": {
                                        "Saturation": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Contrast": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Gamma": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Gain": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Offset": {
                                            "x": 0.0,
                                            "y": 0.0,
                                            "z": 0.0,
                                            "w": 0.0
                                        }
                                    },
                                    "Shadows": {
                                        "Saturation": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Contrast": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Gamma": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Gain": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Offset": {
                                            "x": 0.0,
                                            "y": 0.0,
                                            "z": 0.0,
                                            "w": 0.0
                                        }
                                    },
                                    "Midtones": {
                                        "Saturation": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Contrast": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Gamma": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Gain": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Offset": {
                                            "x": 0.0,
                                            "y": 0.0,
                                            "z": 0.0,
                                            "w": 0.0
                                        }
                                    },
                                    "Highlights": {
                                        "Saturation": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Contrast": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Gamma": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Gain": {
                                            "x": 1.0,
                                            "y": 1.0,
                                            "z": 1.0,
                                            "w": 1.0
                                        },
                                        "Offset": {
                                            "x": 0.0,
                                            "y": 0.0,
                                            "z": 0.0,
                                            "w": 0.0
                                        }
                                    },
                                    "ColorAdjustShadowsMax": 0.09000000357627869,
                                    "ColorAdjustHighlightsMin": 0.5,
                                    "ColorAdjustHighlightsMax": 1.0
                                },
                                "ACECureveSetting": {
                                    "EnableToneMapping": true,
                                    "FilmSlope": 0.8799999952316284,
                                    "FilmToe": 0.550000011920929,
                                    "FilmShoulder": 0.25999999046325686,
                                    "FilmBlackClip": 0.0,
                                    "FilmWhiteClip": 0.03999999910593033,
                                    "ToneCurveAmount": 1.0,
                                    "OverlayColor": {
                                        "x": 0.0,
                                        "y": 0.0,
                                        "z": 0.0,
                                        "w": 0.0
                                    },
                                    "OverlayScale": {
                                        "x": 1.0,
                                        "y": 1.0,
                                        "z": 1.0
                                    }
                                },
                                "HSLSetting": {
                                    "EnableSRGBAdjustment": false,
                                    "GlobalLuminance": 0.5,
                                    "GlobalHueShift": 0.5,
                                    "GlobalSaturation": 0.5,
                                    "GlobalVibrance": 0.5,
                                    "sigma": 15.0,
                                    "HueSensitivity": 120.0,
                                    "Hue": {
                                        "Enable": false,
                                        "Red": 0.5,
                                        "Orange": 0.5,
                                        "Yellow": 0.5,
                                        "Green": 0.5,
                                        "Aqua": 0.5,
                                        "Blue": 0.5,
                                        "Purple": 0.5,
                                        "Magenta": 0.5
                                    },
                                    "Saturation": {
                                        "Enable": false,
                                        "Red": 0.5,
                                        "Orange": 0.5,
                                        "Yellow": 0.5,
                                        "Green": 0.5,
                                        "Aqua": 0.5,
                                        "Blue": 0.5,
                                        "Purple": 0.5,
                                        "Magenta": 0.5
                                    },
                                    "Luminance": {
                                        "Enable": false,
                                        "Red": 0.5,
                                        "Orange": 0.5,
                                        "Yellow": 0.5,
                                        "Green": 0.5,
                                        "Aqua": 0.5,
                                        "Blue": 0.5,
                                        "Purple": 0.5,
                                        "Magenta": 0.5
                                    }
                                },
                                "ManuelLUTSetting": {
                                    "EnableLUT": false,
                                    "LUT": "Texture/PostProcess/none.nda"
                                },
                                "UseSimpleTonemapping": false
                            },
                            "mPostProcessExposureSetting": {
                                "enable": true,
                                "mExposureType": 1,
                                "mHistogramExposureSettings": {
                                    "enable": false,
                                    "MinBrightness": 0.029999999329447748,
                                    "MaxBrightness": 8.0,
                                    "SpeedUp": 3.0,
                                    "SpeedDown": 1.0,
                                    "AutoExposureBias": 1.0,
                                    "HistogramLogMin": -8.0,
                                    "HistogramLogMax": 4.0,
                                    "HighPercent": 0.8999999761581421,
                                    "LowPercent": 0.09999999403953552,
                                    "ExposureCompensationCurve": {
                                        "EnterType": 0,
                                        "LeaveType": 0,
                                        "Keys": [],
                                        "Name": "",
                                        "UseType": 0
                                    }
                                },
                                "mManualExposureSettings": {
                                    "enable": true,
                                    "Exposure": 1.0
                                }
                            },
                            "mPostProcessLocalExposureSetting": {
                                "enable": false,
                                "HighlightContrast": 1.0,
                                "ShadowContrast": 1.0,
                                "HighlightThreshold": 0.0,
                                "ShadowThreshold": 0.0,
                                "DetailStrength": 1.0,
                                "BlurredLuminanceBlend": 0.6000000238418579,
                                "MiddleGreyBias": 0.0
                            },
                            "mPostProcessLensFlareSetting": {
                                "enable": false,
                                "Size": 1.0,
                                "Intesnity": 1.0,
                                "Threshold": 100.0
                            },
                            "mPostProcessBloomSetting": {
                                "enable": false,
                                "BloomThreshold": 3.0,
                                "BloomIntensity": 5.0,
                                "BloomTint": {
                                    "x": 1.0,
                                    "y": 1.0,
                                    "z": 1.0
                                },
                                "BloomScale": 1.0,
                                "BloomLuminanceClamp": 10.0
                            },
                            "mDepthOfFieldSetting": {
                                "enable": false,
                                "BlurRadius": 4.0,
                                "FocalLength": 80.0,
                                "Aperture": 1.2000000476837159,
                                "FocusDistance": 10.0,
                                "DOFAutoFocus": false,
                                "FixedFocusDistance": 10.0,
                                "AutoInfluenceType": 1,
                                "DepthOfFieldAutoFocusComputeShader": "Shader/Features/PostProcess/DepthOfFieldAutoFocus.compute.nda",
                                "DepthOfFieldComputeShader": "Shader/Features/PostProcess/DepthOfField.compute.nda"
                            },
                            "mMotionBlurSetting": {
                                "enable": false,
                                "Intensity": 0.5,
                                "MaxDistortion": 5.0,
                                "TargetFPS": 30,
                                "ComputeShader": true
                            },
                            "mScreenBlurSetting": {
                                "enable": false,
                                "BlurRadius": 1.0,
                                "BlurStrength": 6.0,
                                "BlurClipThreshold": -1.0
                            },
                            "mPostProcessWindSetting": {
                                "enable": false,
                                "Intensity": 1.0,
                                "Direction": {
                                    "x": -0.9279999732971191,
                                    "y": 0.0,
                                    "z": 0.5908470153808594
                                },
                                "Speed": 0.5
                            },
                            "mPostProcessRainSetting": {
                                "enable": false,
                                "Intensity": 1.0,
                                "Speed": 0.5
                            },
                            "mPostProcessSnowSetting": {
                                "enable": false
                            }
                        },
                        "componentHash": 3272036147
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "PostProcessVolume",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::PostProcessVolumeComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "b2742605b6f57458987b176f524be1d3": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "b2742605b6f57458987b176f524be1d3",
                "name": "SkyLight",
                "prototype": 10823887098909128729,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 3,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::SkyLightComponentG": {
                        "Enable": true,
                        "RealTimeCapture": true,
                        "RealTimeCaptureSliceCount": 10,
                        "LightColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "SkyLightIntensity": 1.0,
                        "EnableSkyLightBlend": false,
                        "ForceUpdateSkyLight": false,
                        "LightMapIntensityDebug": 0.0,
                        "mDiffuseProbe": [
                            {
                                "x": -0.003044300014153123,
                                "y": -0.19617418944835664,
                                "z": 0.08162236958742142,
                                "w": 0.3903099000453949
                            },
                            {
                                "x": -0.0013626383151859046,
                                "y": -0.18576791882514954,
                                "z": 0.0951475203037262,
                                "w": 0.5076125264167786
                            },
                            {
                                "x": 0.002345460932701826,
                                "y": -0.1728866845369339,
                                "z": 0.13147644698619843,
                                "w": 0.7257214188575745
                            },
                            {
                                "x": 0.005995072424411774,
                                "y": -0.1167006567120552,
                                "z": -0.04524802416563034,
                                "w": -0.0023408227134495975
                            },
                            {
                                "x": 0.004320001229643822,
                                "y": -0.11656857281923294,
                                "z": -0.06405702233314514,
                                "w": -0.0018469634233042598
                            },
                            {
                                "x": 0.0011339911725372077,
                                "y": -0.11691846698522568,
                                "z": -0.09014725685119629,
                                "w": -0.000046341407141881066
                            },
                            {
                                "x": -0.04115375876426697,
                                "y": -0.039200957864522937,
                                "z": -0.03698145970702171,
                                "w": 1.0
                            }
                        ],
                        "SpecularProbe": "46d68ad3dea8d43beabd7284579f6ccf",
                        "IsLowerHemisphereColor": true,
                        "TODLowerHemisphereColor": true,
                        "LowerHemisphereColor": {
                            "x": 0.0,
                            "y": 0.0,
                            "z": 0.0,
                            "w": 1.0
                        },
                        "componentHash": 3852407668
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "SkyLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::SkyLightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "14c966ca17d0b442eb356524a776cdd0": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "14c966ca17d0b442eb356524a776cdd0",
                "name": "TOD_Env_ReflectionProbe",
                "prototype": 5774972612602404256,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -5.8871961611223297e-30,
                                "y": 276.5548223958522,
                                "z": 4.4646791470950999e-14
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 99.99999999999996,
                                "y": 100.0000298023204,
                                "z": 99.9999940395357
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.7071067811865475,
                                "y": -8.659560562354933e-17,
                                "z": -8.659560562354933e-17,
                                "w": 0.7071067811865476
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ReflectionProbeComponentG": {
                        "mRefleProbeType": 2,
                        "mReflectionTexturePath": "46d68ad3dea8d43beabd7284579f6ccf",
                        "mRefleProbeShapeType": 1,
                        "mSphereRadius": 1500.0,
                        "mBoxSize": {
                            "x": 5.0,
                            "y": 5.0,
                            "z": 5.0
                        },
                        "mBlendDistance": 1.0,
                        "mIntensity": 1.0,
                        "mNearPlane": 10.0,
                        "mBakedReflectionTexturePath": "EngineResource/Texture/DefaultTextureCube.nda",
                        "mRefreshMode": 2,
                        "mTimeSlicing": 1,
                        "mBoxProjection": false,
                        "mEnable": true,
                        "mProbeId": 0,
                        "componentHash": 2168790100
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "TOD_Env_ReflectionProbe",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::ReflectionProbeComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "11c65ee36e0e04c0dae3f4ceb17fdf12": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "11c65ee36e0e04c0dae3f4ceb17fdf12",
                "name": "TOD_Controller",
                "prototype": 12042886633079726197,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -409799430.47871378,
                                "y": 296722325.588841,
                                "z": -387847714.49803897
                            },
                            "mTRSFlag": 34,
                            "mScale": {
                                "x": 1.0000000000000029,
                                "y": 1.0000000000000014,
                                "z": 1.0000000000000014
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.5440569366476596,
                                "y": -0.15944149098373257,
                                "z": -0.1067461605607118,
                                "w": 0.8168143717173417
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ControllableUnitComponentG": {
                        "Type": 9,
                        "Controller": "{\n    \"Entity\": \"11c65ee36e0e04c0dae3f4ceb17fdf12\",\n    \"RotationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 1.0\n    },\n    \"TranslationOffset\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"SpawnSpot\": \"\",\n    \"InsideWorldBounds\": {\n        \"position\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        },\n        \"rotate\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0,\n            \"w\": 1.0\n        },\n        \"halfExtents\": {\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"z\": 0.0\n        }\n    },\n    \"CollisionType\": 0,\n    \"BlockMask\": 65535,\n    \"bUseControllerRotationPitch\": true,\n    \"bUseControllerRotationYaw\": true,\n    \"bUseControllerRotationRoll\": true,\n    \"bUseControllerTranslate\": true,\n    \"CurveCtrResPath\": \"e8ed3711e07784e16ac26c1789817b27\",\n    \"mPlayOnStart\": false,\n    \"mPlaySpeed\": 1.0,\n    \"mRetargetCurveBindings\": false\n}",
                        "componentHash": **********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "TOD_Controller",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::ControllableUnitComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "bc37da904f20a47259bfca4648379abf": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "bc37da904f20a47259bfca4648379abf",
                "name": "DirectionalLight",
                "prototype": 13180312176993274609,
                "floder": false,
                "expand": true,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -41.30840757572323,
                                "y": 530.5092560182251,
                                "z": -91.78252611699084
                            },
                            "mTRSFlag": 35,
                            "mScale": {
                                "x": 1.0000000000000005,
                                "y": 0.9999999999999998,
                                "z": 0.9999999999999998
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.08226710525346189,
                                "y": 0.5545817449938821,
                                "z": -0.28766362114501517,
                                "w": -0.7764797824686059
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 2,
                        "componentHash": 495869105
                    },
                    "cross::TODLightComponentG": {
                        "config": {
                            "Year": 2023,
                            "Month": 1,
                            "Day": 10,
                            "Hour": 7,
                            "Minute": 52,
                            "Second": 46
                        },
                        "componentHash": 3512910319
                    },
                    "cross::LightComponentG": {
                        "mType": 0,
                        "mColor": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mIntensity": 6.0,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngle": 0.7357000112533569,
                        "mRange": 1.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 196968.390625,
                        "mOuterConeAngle": 196968.390625,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 1.0,
                        "mSourceHeight": 1.0,
                        "mBarnDoorAngle": 1.0,
                        "mBarnDoorLength": 1.0,
                        "mCastShadow": true,
                        "mCastScreenSpaceShadow": true,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 0,
                        "mShadowType": 3,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.5,
                        "mShadowSlopeBias": 0.5,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": true,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": true,
                            "AtmosphereSunLightIndex": 0,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::ScriptComponentG": {
                        "ScriptPath": "",
                        "EnableUpdate": true,
                        "ScriptProperties": null,
                        "componentHash": 1186449325
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "DirectionalLight",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                },
                                {
                                    "ComponentType": "cegf::TODLightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "7e57cc0a4958d4372b08d43fd500dcb8": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "7e57cc0a4958d4372b08d43fd500dcb8",
                "name": "Moon",
                "prototype": 17540517759633003746,
                "floder": false,
                "expand": false,
                "hide": true,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.5441355592760817,
                                "y": -0.5496638773145648,
                                "z": -0.24864064589251137,
                                "w": -0.5830642711716948
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::TODLightComponentG": {
                        "config": {
                            "Year": 2023,
                            "Month": 1,
                            "Day": 10,
                            "Hour": 7,
                            "Minute": 52,
                            "Second": 46
                        },
                        "componentHash": 3512910319
                    },
                    "cross::LightComponentG": {
                        "mType": 0,
                        "mColor": {
                            "x": 0.5671116709709168,
                            "y": 0.6141843795776367,
                            "z": 1.0
                        },
                        "mIntensity": 0.5,
                        "mPrtIntensity": 1.0,
                        "mSpecularIntensity": 1.0,
                        "mVolumetricFactor": 1.0,
                        "mSourceAngle": 1.0,
                        "mRange": 500.0,
                        "mVersion": 1,
                        "mInnerConeAngle": 196968.390625,
                        "mOuterConeAngle": 196968.390625,
                        "mConeFadeIntensity": 1.0,
                        "mConeOverFlowLength": 0.0,
                        "mSpotDistanceExp": 1.0,
                        "mSourceWidth": 64.0,
                        "mSourceHeight": 64.0,
                        "mBarnDoorAngle": 88.0,
                        "mBarnDoorLength": 20.0,
                        "mCastShadow": false,
                        "mCastScreenSpaceShadow": false,
                        "mShadowStrength": 1.0,
                        "mMode": 1,
                        "mPriority": 10,
                        "mShadowType": 2,
                        "mShadowAmount": 1.0,
                        "mShadowBias": 0.6000000238418579,
                        "mShadowSlopeBias": 0.6000000238418579,
                        "mVarianceBiasVSM": 0.009999999776482582,
                        "mLightLeakBiasVSM": 0.009999999776482582,
                        "mFilterSizePCF": 2.0,
                        "mSoftnessPCSS": 0.004999999888241291,
                        "mSampleCountPCSS": 32.0,
                        "mEnable": false,
                        "mAtmosphereLightConfig": {
                            "AtmosphereSunLight": true,
                            "AtmosphereSunLightIndex": 1,
                            "AtmosphereSunDiscColorScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "AtmosphereSunDiscIntensityScale": 1.0,
                            "AtmosphereDirectionReversed": false,
                            "ReversedLightRadius": 0
                        },
                        "mRenderingLayerMask": 1,
                        "mEnableTransmittance": true,
                        "componentHash": 594056575
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Moon",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::LightComponent"
                                },
                                {
                                    "ComponentType": "cegf::TODLightComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "21ceb9487311b441188d475e5dfbd548": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "21ceb9487311b441188d475e5dfbd548",
                "name": "Cloud_SkySphere",
                "prototype": 9139264133816859606,
                "floder": false,
                "expand": false,
                "hide": true,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 640000000.0,
                                "y": 640000000.0,
                                "z": 640000000.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "81375b2984621416ea2ff11bdd29c155",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::ScriptComponentG": {
                        "ScriptPath": "",
                        "EnableUpdate": true,
                        "ScriptProperties": null,
                        "componentHash": 1186449325
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": false,
                        "componentHash": 2559651570
                    },
                    "cross::ControllableUnitComponentG": {
                        "Type": 0,
                        "Controller": "{}",
                        "componentHash": **********
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Cloud_SkySphere",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::ControllableUnitComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "60c9b1d2f9ebd48d1a75b4afda82d969": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "60c9b1d2f9ebd48d1a75b4afda82d969",
                "name": "Atmosphere",
                "prototype": 5788600200797953615,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": 0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::SkyAtmosphereComponentG": {
                        "config": {
                            "MiePhase": 0.00039999998989515007,
                            "MieScattCoeff": {
                                "x": 0.5764706134796143,
                                "y": 0.5764706134796143,
                                "z": 0.5764706134796143
                            },
                            "MieScattScale": 0.003996000159531832,
                            "MieAbsorCoeff": {
                                "x": 0.5764706134796143,
                                "y": 0.5764706134796143,
                                "z": 0.5764706134796143
                            },
                            "MieAbsorScale": 0.004999999888241291,
                            "RayScattCoeff": {
                                "x": 0.16078431904315949,
                                "y": 0.37254902720451357,
                                "z": 0.9137254953384399
                            },
                            "RayScattScale": 0.03310000151395798,
                            "AbsorptiCoeff": {
                                "x": 0.32549020648002627,
                                "y": 0.9450980424880981,
                                "z": 0.04313725605607033
                            },
                            "AbsorptiScale": 0.0017999999690800906,
                            "PlanetRadius": 6375.0,
                            "AtmosHeight": 60.0,
                            "MieScaleHeight": 1.2000000476837159,
                            "RayScaleHeight": 8.0,
                            "GroundAlbedo3": {
                                "x": 0.0010000000474974514,
                                "y": 0.0010000000474974514,
                                "z": 0.0010000000474974514
                            },
                            "SFogMieScattScale": 0.05000000074505806,
                            "SkyLuminanceFactor": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "MultiScatteringFactor": 1.0,
                            "HeightFogContribution": 1.0
                        },
                        "outerParam": {
                            "PlanetTopAtWorldOrigin": true,
                            "RenderSunDisk": true,
                            "RenderMoonDisk": true,
                            "MoonPhase": false,
                            "AerialPerspStartDepthKM": 1.0,
                            "Exposure": 1.0,
                            "APScale": 1.0,
                            "HighQualityAP": false,
                            "ColorTransmittance": 1.0
                        },
                        "componentHash": 2589265039
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Atmosphere",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::SkyAtmosphereComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": []
            },
            "8d6fb3a9330cc79b514e1571b4f378aa": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "8d6fb3a9330cc79b514e1571b4f378aa",
                "name": "CameraObject_7",
                "prototype": 10797187789158273370,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 0.0,
                                "y": 282.6196236237446,
                                "z": -369.1025455726622
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 1.0,
                                "y": 1.0,
                                "z": 1.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::CameraComponentG": {
                        "mCameraInfo": {
                            "mAspectRatio": 2.1666667461395265,
                            "mFov": 1.5700000524520875,
                            "mFocalLength": 35.0,
                            "mMinFocalLength": 4.0,
                            "mMaxFocalLength": 1000.0,
                            "mSensorWidth": 52.0,
                            "mSensorHeight": 24.0,
                            "mFarPlane": 10000.0,
                            "mNearPlane": 10.0,
                            "mWidth": 1080.0,
                            "mHeight": 720.0,
                            "mOrthNearPlane": 10.0,
                            "mOrthFarPlane": 10000.0,
                            "mNormalBias": 0.0,
                            "mDepthBias": 0.0,
                            "mJitterIntensity": 1.0
                        },
                        "mCameraMask": 4294967295,
                        "mEnable": false,
                        "mProjectionMode": 0,
                        "mTargetWidth": 800,
                        "mTargetHeight": 800,
                        "mRenderTexturePath": "",
                        "componentHash": 2143649290
                    },
                    "cross::EditorIconComponentG": {
                        "Scale": 1.0,
                        "componentHash": 2703027760
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::CameraObject",
                            "ObjectName": "CameraObject_7",
                            "ScriptPath": "EngineResource/Puerts/TypeScript/CameraControl.mts",
                            "ScriptEditorFields": "{\"Fields\":[]}",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::InputComponent"
                                },
                                {
                                    "ComponentType": "cegf::CameraComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [],
                "rootParent": "e372af75000000001823c72200000000"
            },
            "dfc47ec5ba4395881840ddd4b2f57e27": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "dfc47ec5ba4395881840ddd4b2f57e27",
                "name": "Sphere",
                "prototype": 5867300607375780488,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": -21.87024591036206,
                                "y": 288.3757402455266,
                                "z": 2945.************
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 5.0,
                                "y": 5.0,
                                "z": 5.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "60cded566dafa4687a0e2f5928ee4ea6",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "d38789cffc2551804f4a80099768faae",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": false,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": null,
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Sphere",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [],
                "rootParent": "e372af75000000001823c72200000000"
            },
            "4ce48e5117a5fa8d4d44384472ae8b3b": {
                "prefabId": "",
                "prefabEuid": "",
                "euid": "4ce48e5117a5fa8d4d44384472ae8b3b",
                "name": "Cube",
                "prototype": 5867300607375780488,
                "floder": false,
                "expand": false,
                "hide": false,
                "selectable": true,
                "components": {
                    "cross::ecs::EntityIDComponent": {
                        "componentHash": 4167152417
                    },
                    "cross::ecs::EntityMetaComponentG": {
                        "componentHash": 4243265654
                    },
                    "cross::LocalTransformComponentG": {
                        "TRS": {
                            "mTranslation": {
                                "x": 151.5269644072406,
                                "y": 384.47031892867849,
                                "z": 3808.133502457299
                            },
                            "mTRSFlag": 2,
                            "mScale": {
                                "x": 5.0,
                                "y": 5.0,
                                "z": 5.0
                            },
                            "mVersion": 0,
                            "mRotation": {
                                "x": -0.0,
                                "y": 0.0,
                                "z": 0.0,
                                "w": 1.0
                            }
                        },
                        "componentHash": 1549327113
                    },
                    "cross::WorldTransformComponentG": {
                        "mHashWay": 3,
                        "componentHash": 495869105
                    },
                    "cross::ModelComponentG": {
                        "mChangeableModels": [],
                        "mMainModel": {
                            "mAssetPath": "9b0219907dbd546f085e7b954d9bd351",
                            "mSingleLODModelProperties": [
                                {
                                    "mSubModelProperties": [
                                        {
                                            "mMaterialPath": "7e44733a3307b0b80a41c661792facc4",
                                            "mVisible": true
                                        }
                                    ]
                                }
                            ],
                            "mVisible": true,
                            "mReceiveDecals": true
                        },
                        "mEnableGPUSkin": false,
                        "mEnabledIntersection": true,
                        "mCacheableForDrawing": false,
                        "mDistanceCulling": {
                            "minCullingDistance": 0.0,
                            "maxCullingDistance": 0.0
                        },
                        "componentHash": 1128797582
                    },
                    "cross::AABBComponentG": {
                        "componentHash": 947675368
                    },
                    "cross::PhysicsComponentG": {
                        "mEnable": true,
                        "mIsDynamic": false,
                        "mEnableGravity": false,
                        "mIsTrigger": false,
                        "mIsKinematic": false,
                        "mUseMeshCollision": false,
                        "mStartAsleep": false,
                        "mLinearDamping": 0.009999999776482582,
                        "mMass": 0.0,
                        "mMaxDepenetrationVelocity": 0.0,
                        "mMassSpaceInertiaTensorMultiplier": {
                            "x": 1.0,
                            "y": 1.0,
                            "z": 1.0
                        },
                        "mCollisionType": 0,
                        "mCollisionMask": 0,
                        "mMaterialType": 0,
                        "mEnableCollisionEvent": false,
                        "mExtraCollision": null,
                        "componentHash": 1482413578
                    },
                    "cross::RenderPropertyComponentG": {
                        "mCullingProperty": 1,
                        "mLayerIndex": 0,
                        "RenderEffect": {
                            "RuntimeEffectMask": 1572864
                        },
                        "mNeedVoxelized": true,
                        "componentHash": 2559651570
                    },
                    "cross::GOSerializerComponentG": {
                        "GameObject": {
                            "MetaClassType": "cegf::GameObject",
                            "ObjectName": "Cube",
                            "ScriptPath": "",
                            "ScriptEditorFields": "",
                            "GameComponents": [
                                {
                                    "ComponentType": "cegf::GameObjectComponent"
                                },
                                {
                                    "ComponentType": "cegf::BasicComponent"
                                },
                                {
                                    "ComponentType": "cegf::RenderPropertyComponent"
                                },
                                {
                                    "ComponentType": "cegf::PhysicsComponent"
                                },
                                {
                                    "ComponentType": "cegf::ModelComponent"
                                }
                            ]
                        },
                        "componentHash": 2339196393
                    }
                },
                "children": [],
                "rootParent": "e372af75000000001823c72200000000"
            }
        }
    }
}