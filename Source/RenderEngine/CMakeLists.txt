message(STATUS "Configuration RenderEngine...")
project(RenderEngine)


list(APPEND EXTERMAL_FILES ${CROSSENGINE_SRC_DIR}/Source/External/NlohmannJSON/single_include/nlohmann/json.hpp)
generate_source_files_from_base_dir(${CROSSENGINE_SRC_DIR}/Source ${EXTERMAL_FILES})

file(GLOB_RECURSE  RENDER_ENGINE_H *.h *.hpp *.inl)
file(GLOB_RECURSE  RENDER_ENGINE_S  *.c *.cpp)
generate_source_files(${RENDER_ENGINE_H} ${RENDER_ENGINE_S})

file(GLOB_RECURSE Shader_S ${CROSSENGINE_SRC_DIR}/Resource/PipelineResource/*.shader)
file(GLOB_RECURSE Shader_H ${CROSSENGINE_SRC_DIR}/Resource/PipelineResource/*.hlsl)
file(GLOB_RECURSE Compute_S ${CROSSENGINE_SRC_DIR}/Resource/PipelineResource/*.compute)
file(GLOB_RECURSE RayTracing_S ${CROSSENGINE_SRC_DIR}/Resource/PipelineResource/*.raytracing)

function (GroupShader source_list)
	foreach(source IN LISTS source_list)
		get_filename_component(source_path "${source}" DIRECTORY)
		string(REPLACE "/" "\\" source_path_msvc "${source_path}")
		file(RELATIVE_PATH  rel "${CROSSENGINE_SRC_DIR}/Resource/PipelineResource" ${source_path_msvc})
		source_group("RenderEngine/Shaders/${rel}" FILES "${source}")
	endforeach()
endfunction()

if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	GroupShader("${Shader_S}")
	GroupShader("${Shader_H}")
	GroupShader("${Compute_S}")
	GroupShader("${RayTracing_S}")
endif()
get_generated_code(${PROJECT_NAME} GENERATED_CODE)

list(APPEND RenderEngine_Files
	${EXTERMAL_FILES}
	${RENDER_ENGINE_H}
	${RENDER_ENGINE_S}
	${GENERATED_CODE}
)

set_source_files_properties(${Shader_S} PROPERTIES VS_TOOL_OVERRIDE "None")
set_source_files_properties(${Shader_H} PROPERTIES VS_TOOL_OVERRIDE "None")
set_source_files_properties(${Compute_S} PROPERTIES VS_TOOL_OVERRIDE "None")
set_source_files_properties(${RayTracing_S} PROPERTIES VS_TOOL_OVERRIDE "None")
if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_ANDROID} OR ${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_IOS})
	add_library(${PROJECT_NAME} STATIC ${RenderEngine_Files}  ${Shader_S} ${Shader_H} ${Compute_S} ${RayTracing_S})
else()
    add_library(${PROJECT_NAME} SHARED ${RenderEngine_Files}  ${Shader_S} ${Shader_H} ${Compute_S} ${RayTracing_S})
endif()

# Like UE, Shader/Shared can be accessed by CPU and GPU
target_include_directories(${PROJECT_NAME} PRIVATE 
    "${CROSSENGINE_SRC_DIR}/Resource/PipelineResource/FFSRP/Shader/Shared"
)

set(dependItems CrossBase CECommon ECS NativeGraphicsInterface CEResource FileSystem CrossUI CrossFX)
include_directories(${CROSSENGINE_SRC_DIR}/Source/GameplayBaseFramework/third_party/imgui-ws/include)
include_directories(${CROSSENGINE_SRC_DIR}/Source/GameplayBaseFramework/third_party/IMGUINodeEditor)
add_dependencies(${PROJECT_NAME} imguinodeeditor)
target_link_libraries(${PROJECT_NAME} PUBLIC imguinodeeditor)

foreach(item ${dependItems})
	include_directories("${CROSSENGINE_SRC_DIR}/Source/${item}")
	add_dependencies(${PROJECT_NAME} ${item})
	target_link_libraries(${PROJECT_NAME} PUBLIC ${item})
endforeach()
if (WIN32) 
target_link_libraries(${PROJECT_NAME} PUBLIC streamline)
target_link_libraries(${PROJECT_NAME} PUBLIC ffx)
target_link_libraries(${PROJECT_NAME} PUBLIC dxgi.lib)
endif()

if (MSVC)
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_RUNTIME_DLLS:${PROJECT_NAME}> $<TARGET_FILE_DIR:${PROJECT_NAME}>
    COMMAND_EXPAND_LISTS
    )
endif()
set_output_dir(${PROJECT_NAME})  
SET_PROPERTY(TARGET RenderEngine PROPERTY FOLDER "CECore")
SET_PROPERTY(TARGET ${PROJECT_NAME} PROPERTY MODULE_API_STR "RENDER_ENGINE_API")
