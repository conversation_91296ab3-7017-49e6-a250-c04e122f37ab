#pragma once

#include "RenderEngine/RenderPipeline/Effects/PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "HybridGIVoxel.h"
#include <RenderEngine/PrimitiveGenerator.h>
#include "RenderEngine/SkyLightSystemR.h"
#include "HybridCommon.h"
#include "Surfel/HybridSurfelCommon.h"
#include "Surfel/HybridSurfelLighting.h"
#include "AO/HybridShortRangeAO.h"
#include "Sharc/HybridSharc.h"

namespace cross {
class TextureCreateHelper
{
public:
    static NGITextureViewDesc GetTexture2DViewDesc(NGITextureUsage usage, GraphicsFormat format, NGITextureAspect aspect = NGITextureAspect::Color);

    static NGITextureDesc GetTexture2DDesc(GraphicsFormat format, UInt32 width, UInt32 height, NGITextureUsage usage);

    static REDTextureView* GetHistoryTextureView(RenderingExecutionDescriptor* RED, REDTextureRef historyTex, REDTextureView* defaultTexView, NGITextureAspect aspect = NGITextureAspect::Color);

    static REDTextureRef AllocateNewTemporalTexture(RenderingExecutionDescriptor* RED, std::string_view name, NGITextureUsage usage, GraphicsFormat format, UInt32 width, UInt32 height);
};

enum class CEMeta(Reflect, Editor) FoliageTwoSidedLightingMode
{
    DISABLE_INDIRECT_LIGHTING_3S = 0,
    INDIRECT_LIGHTING_3S_USE_GI,
    INDIRECT_LIGHTING_3S_USE_SKYLIGHT,
};

enum class CEMeta(Reflect, Editor) BoolOverrideType
{
    USE_GLOBAL_CONFIG = -1,
    FALSE_OVERRIDE,
    TRUE_OVERRIDE,
};

class CEMeta(Editor, Reflect) RENDER_ENGINE_API HybridGIPostProcessSetting : public PassSetting
{
public:
    HybridGIPostProcessSetting()
    {
        enable = false;
    }

    CE_Virtual_Serialize_Deserialize;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelGI"))
    BoolOverrideType mEnableVoxelGI = BoolOverrideType::USE_GLOBAL_CONFIG;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "FoliageGIIntensity_Tricked")) float mFoliageGIIntensity_Tricked = 1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Lighting Intensity"))
    float mIndirectLightingIntensity = -1.0f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Specular Lighting Intensity"))
    float mIndirectSpecularLightingIntensity = -1.0f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Diffuse Lighting Intensity"))
    float mIndirectDiffuseLightingIntensity = -1.0f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableGISpecularCombine"))
    BoolOverrideType mEnableGISpecularCombine = BoolOverrideType::USE_GLOBAL_CONFIG;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Reflection use GI probe when roughness greater than this value", ValueMin = "0", ValueMax = "2"))
    float mReflectionMaxRoughnessToTrace = -1.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "fade range of goughness", ValueMin = "0.000001", ValueMax = "2"))
    float mReflectionRoughnessFadeLength = -1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Uniform Downsample Factor", ValueMin = "1", ValueMax = "32"))
    int mDownsampleFactor = -1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "AdaptiveProbe MinDownsampleFactor", ValueMin = "1", ValueMax = "32"))
    int mAdaptiveProbeMinDownsampleFactor = -1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ProbeOctResolution"))
    BoolOverrideType mProbeOctHalfResolution = BoolOverrideType::USE_GLOBAL_CONFIG;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelRadiosity Enable or not"))
    BoolOverrideType mVoxelRadiosityEnable = BoolOverrideType::USE_GLOBAL_CONFIG;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "RadiosityDecayRate"))
    float mRadiosityDecayRate = -1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "HybridSceneFirstClipmapWorldExtent"))
    float mHybridSceneFirstClipmapWorldExtent = -1;

    virtual void Initialize() override;
};

class HybridGIPassSettings : public PassSetting
{
public:

    CE_Virtual_Serialize_Deserialize

    // TODO will move voxel setting outside

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Hybrid Surfel Settings", ToolTips = "HybridGI"))
    HybridSurfelSettings mHybridSurfelSetting;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Hybrid Short Range AO Settings", ToolTips = "HybridGI"))
    HybridShortRangeAOSettings mHybridShortRangeAOSetting;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Hybrid SHaRC Settings", ToolTips = "NVIDIA SHaRC Radiance Cache for accelerated path tracing"))
    HybridSharcSettings mHybridSharcSetting;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelGI")) bool mEnableVoxelGI = false;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Lighting Intensity")) float mIndirectLightingIntensity = 1.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Specular Lighting Intensity")) float mIndirectSpecularLightingIntensity = 0.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Indirect Diffuse Lighting Intensity")) float mIndirectDiffuseLightingIntensity = 1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Emissive Object's Indirect Lighting Intensity in HybridGI, 0 means disabled, > 1 means to boost"))
    float mEmissiveGIIntensity = 1.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Last Frame SceneColor Exposure Scale, Also Affect Emissive Color"))
    float mSceneColorWithEmissiveExposureScale = 1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableGISpecularCombine")) bool mEnableGISpecularCombine = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Reflection use GI probe when roughness greater than this value", ValueMin = "0", ValueMax = "2"))
    float mReflectionMaxRoughnessToTrace = 1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "fade range of goughness", ValueMin = "0.000001", ValueMax = "2"))
    float mReflectionRoughnessFadeLength = 0.5f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Uniform Downsample Factor", ValueMin = "1", ValueMax = "32")) int mDownsampleFactor = 16;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "AdaptiveProbe MinDownsampleFactor", ValueMin = "1", ValueMax = "32"))
    float mAdaptiveProbeMinDownsampleFactor = 4;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ProbeOctResolution")) bool mProbeOctHalfResolution = false;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelRadiosity Enable or not")) bool mVoxelRadiosityEnable = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Radiosity Rays Per Voxel", ValueMin = "1", ValueMax = "64")) UInt32 mRadiosityRaysPerVoxel = 64;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "RadiosityDecayRate")) float mRadiosityDecayRate = 1.0f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "UseHiZRayTracing")) bool mUseHiZRayTracing = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "NumRayCastSteps")) UInt32 mNumRayCastSteps = 64;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MaxScreenTraceDistance")) float mMaxScreenTraceDistance = 5000.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MaxScreenTraceFraction")) float mMaxScreenTraceFraction = 0.3f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "HybridSceneFirstClipmapWorldExtent")) float mHybridSceneFirstClipmapWorldExtent = 2500;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MinVoxelTraceDistance")) float mMinVoxelTraceDistance = 100.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MaxVoxelTraceDistance")) float mMaxVoxelTraceDistance = 100000.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelTraceSurfaceBias")) float mVoxelTraceSurfaceBias = 0.2f;
    /*
     * NOTE: Be careful when turn on this setting, may cause severe light leaking problem, especially for geometries that occupy two voxels
     *  Influences all cone trace effects(DirectLightingInjection, ConeTraceShadow, ConeTrace)
     */
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableFirstVoxelUnHit")) bool mEnableFirstVoxelUnHit = false;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableIsolatedPixelFlickingSuppression"))
    bool mIsolatedPixelFlickingSuppression = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugShowIsolatedPixel"))
    bool mDebugShowIsolatedPixel = false;
    CEMeta(Serialize, Reflect, Editor, EdtiorPropertyInfo(PropertyType = "Auto", ToolTips = "Number of zero-depth neighbors required to classify a pixel as isolated. Range: 0-8. Lower values mean more pixels will be considered isolated."))
    UInt32 mIsolatedPixelThreshold = 2;
    
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Foliage Twosided LightingMode"))
    FoliageTwoSidedLightingMode mFoliageTwosidedLightingMode = FoliageTwoSidedLightingMode::DISABLE_INDIRECT_LIGHTING_3S;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Foliage Twosided SkyLight")) float mFoliageSkyLightLerpDistance = 10000;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "LongDistanceUseSkyLight")) bool mLongDistanceUseSkyLight = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "LongDistanceUseSkyLight")) float mLongDistanceUseSkyLightDistStart = 5000;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "LongDistanceUseSkyLightDistLerp")) float mLongDistanceUseSkyLightDistLerp = 20000;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Diffuse Boost, but not recommended"))
    float mDiffuseBoost = 1.f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "IrradianceFormatUseSH3")) bool mIrradianceFormatUseSH3 = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "BentNormalAODirectSkyLightMethod")) bool mBentNormalAODirectSkyLightMethod = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableCompositeTraces")) bool mEnableCompositeTraces = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableProbeTemporalFilter")) bool mEnableProbeTemporalFilter = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ProbeTemporalFilterWithHitDistance")) bool mProbeTemporalFilterWithHitDistance = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableProbeSpatialFilter")) bool mEnableProbeSpatialFilter = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "SpatialFilterNumPasses")) int mSpatialFilterNumPasses = 3;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ProbeInterpolationWithNormal")) bool mProbeInterpolationWithNormal = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableTemporalFilter")) bool mEnableTemporalFilter = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "MaxFramesAccumulated")) float mMaxFramesAccumulated = 14.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "NumHistoryAccumulateThres")) float mNumHistoryAccumulateThres = 0.9f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "FastUpdateScale")) float mFastUpdateScale = 10.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "HistoryDistanceThreshold")) float mHistoryDistanceThreshold = 0.05f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DisocclusionDistanceThreshold")) float mDisocclusionDistanceThreshold = 100000.f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "FixFrameIndex")) int mFixFrameIndex = -1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "UseTaaRT")) bool mUseTaaRT = false;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enable PreVoxelize")) bool mEnablePreVoxelize = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Voxelize New Size")) bool mVoxelizeNewSize = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Voxelize New Size Scale")) float mVoxelizeNewSizeScale = 8.0f;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Voxelize Mesh Lod Index")) int mVoxelizeMeshLodIndex = 100;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelsForceFullUpdate")) bool mVoxelsForceFullVoxelize = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelsForceUpdateLighting")) bool mVoxelsForceUpdateLighting = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Inject Lighting from Previous Frame")) bool mInjectLightingFromPrevFrame = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelLightCulling")) bool mEnableVoxelLightCulling = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelChunkSize")) uint32_t mVoxelChunkSize = 8;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "UseLocalLightShadowCache")) bool mUseLocalLightShadowCache = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "VoxelsKeepNotClear")) bool mVoxelsKeepNotClear = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "ClearTextureUseCS")) bool mClearTextureUseCS = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelOpacityTex")) bool mEnableVoxelOpacityTex = true;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableVoxelOpacityCompactTex")) bool mEnableVoxelOpacityCompactTex = true;
    //CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "EnableScreenTrace")) bool mEnableTraceHZB = true;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enable DebugShowVoxels")) bool mDebugShowVoxels = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugShowVoxelType")) float mDebugShowVoxelType = 2;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugShowClipmap")) int mDebugShowClipmap = -1;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugTraceSkyLightingOnly")) bool mDebugTraceSkyLightingOnly = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugTraceSkyLightSH")) bool mDebugTraceSkyLightSH = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugShowSH3")) bool mDebugShowSH3 = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugScreenPosition")) Float2 mDebugScreenPosition{0.5f, 0.5f};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugScreenPosition2")) Float2 mDebugScreenPosition2{0.8f, 0.8f};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "LockShowSH3")) bool mLockShowSH3 = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugSHOffset")) Float3 mDebugSHOffset{0, 0, 0};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugSHOffset2")) Float3 mDebugSHOffset2{0, 0, 0};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugSHScale")) Float3 mDebugSHScale{10, 10, 10};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRay")) bool mEnableDebugRay = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRayFreeze")) bool mEnableDebugRayFreeze = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRayPosition")) Float2 mDebugRayPosition{0.5f, 0.5f};
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRayIndex")) int mDebugRayIndex = -1;


    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, HybridVisualizeSceneMtl, "Shader/HybridGI/HybridVisualizeSceneMtl.nda", "HybridVisualizeMaterial", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, HybridVisualizeSHCoefsMtl, "Shader/HybridGI/HybridVisualizeSHDebugMtl.nda", "HybridVisualizeMaterial", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, HybridVisualizeCubeMtl, "Shader/HybridGI/HybridVisualizeCubeDebugMtl.nda", "HybridVisualizeMaterial", "", "General Settings");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Texture, EnvBRDFTexture, "Texture/envBRDFlut.nda", "EnvBRDF Texture", "", "");

    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, UpdateVoxelizeTextureShader, "Shader/HybridGI/UpdateVoxelizeTextureShader.compute.nda", "UpdateVoxelizeTextureShader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, ClearReplacementShader, "Shader/HybridGI/ClearReplacementShader.compute.nda", "ClearReplacementShader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridSceneRadiosityShader, "Shader/HybridGI/HybridSceneRadiosity.compute.nda", "HybridSceneRadiosity", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridSceneLightingCompositeShader, "Shader/HybridGI/HybridSceneLightingComposite.compute.nda", "HybridSceneLightingComposite", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridVisualizeVoxelsShader, "Shader/HybridGI/HybridVisualizeVoxels.compute.nda", "Debug Visualize Voxels Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridVoxelizerUpdateShader, "Shader/HybridGI/HybridVoxelizerUpdate.compute.nda", "Voxel Texture Clear Shader", "", "")
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridCompactTracesShader, "Shader/HybridGI/HybridFinalGatherTracing.compute.nda", "Compact Traces Shader", "", "")
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFinalGatherTraceVoxelsShader, "Shader/HybridGI/HybridFinalGatherTraceVoxels.compute.nda", "Hybrid Final Gather Trace Voxels Shader", "", "")

    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HiZGeneratorShader, "Shader/HybridGI/HZB.compute.nda", "HiZ Generator Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFGTraceHZBShader, "Shader/HybridGI/HybridFinalGatherTraceHZB.compute.nda", "Hybrid Final Gather Trace HZB Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridImportanceSamplingShader, "Shader/HybridGI/HybridImportanceSampling.compute.nda", "Hybrid Importance Sampling Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridImportanceSamplingHalfResShader, "Shader/HybridGI/HybridImportanceSamplingHalfRes.compute.nda", "Hybrid Importance Sampling Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFinalGatherFilteringShader, "Shader/HybridGI/HybridFinalGatherFiltering.compute.nda", "Hybrid Final Gather Filtering Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFinalGatherFilteringHalfResShader, "Shader/HybridGI/HybridFinalGatherFilteringHalfRes.compute.nda", "Hybrid Final Gather Filtering Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFinalGatherFilteringProbeShader, "Shader/HybridGI/HybridFinalGatherFilteringProbe.compute.nda", "Hybrid Final Gather Filtering Probe Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFinalGatherIntegrateShader, "Shader/HybridGI/HybridFinalGatherIntegrate.compute.nda", "Hybrid Final Gather Integrate Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFinalGatherTemporalShader, "Shader/HybridGI/HybridFinalGatherTemporal.compute.nda", "Hybrid Final Gather Temporal Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFinalGatherScreenProbeComputeShader, "Shader/HybridGI/FinalGatherScreenProbe.compute.nda", "Hybrid Final Gather Screen Probe Compute Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridFinalGatherRayDebugShader, "Shader/HybridGI/HybridFinalGatherRayDebug.compute.nda", "Hybrid Hybrid Final Gather Ray Debug Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridVoxelizerCompactComputeShader, "Shader/HybridGI/HybridVoxelizerCompact.compute.nda", "Hybrid Voxelizer Compact Compute Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridVoxelizerDirectLightingComputeShader, "Shader/HybridGI/HybridSceneDirectLighting.compute.nda", "Hybrid Scene Direct Lighting Compute Shader", "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridVoxelLightCullingComputeShader, "Shader/HybridGI/HybridVoxelLightCulling.compute.nda", "Hybrid Voxel Light Culling Compute Shader", "", "")

    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, HybridVisualizeSHComputeShader, "Shader/HybridGI/HybridVisualizeSH.compute.nda", "Compute Shader", "", "");

    virtual void Initialize() override;

    ComputeShaderR* GetHybridFinalGatherRayDebugShader() const
    {
        return HybridFinalGatherRayDebugShaderR;
    }

    ComputeShaderR* GetHybridFinalGatherFilteringShader(const GameContext& gameContext) const
    {
        return IsProbeOctHalfResolution(gameContext) ? HybridFinalGatherFilteringHalfResShaderR : HybridFinalGatherFilteringShaderR;
    }

    ComputeShaderR* GetImportanceSamplingShader(const GameContext& gameContext) const
    {
        return IsProbeOctHalfResolution(gameContext) ? HybridImportanceSamplingHalfResShaderR : HybridImportanceSamplingShaderR;
    }

    ComputeShaderR* GetHybridFinalGatherIntegrateShader() const
    {
        return HybridFinalGatherIntegrateShaderR;
    }

    int GetProbeIrradianceFormat() const
    {
        return mIrradianceFormatUseSH3 ? 0 : 1;
    }

    static const HybridGIPostProcessSetting& GetGIPostProcess(const GameContext& gameContext);

    // Enable HybridGI means that ScreenSpace is default enabled
    bool IsEnableGI(const GameContext& gameContext) const
    {
        return enable; //GetGIPostProcess(gameContext).enable ? true : enable;
    }

    bool IsEnableVoxelGI(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mEnableVoxelGI == BoolOverrideType::USE_GLOBAL_CONFIG ? mEnableVoxelGI : static_cast<bool>(GetGIPostProcess(gameContext).mEnableVoxelGI);
    }

    float GetVoxelClipmapWorldExtent(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mHybridSceneFirstClipmapWorldExtent > 0 ? GetGIPostProcess(gameContext).mHybridSceneFirstClipmapWorldExtent : mHybridSceneFirstClipmapWorldExtent;
    }

    float GetIndirectLightingIntensity(const GameContext& gameContext) const
    {
        if (IsEnableGI(gameContext))
        {
            return GetGIPostProcess(gameContext).mIndirectLightingIntensity >= 0 ? GetGIPostProcess(gameContext).mIndirectLightingIntensity : mIndirectLightingIntensity;
        }
        else
        {
            return 0.f;
        }
    }

    float GetFoliageGIIntensity_Tricked(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mFoliageGIIntensity_Tricked;
    }

    float GetIndirectSpecularLightingIntensity(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mIndirectSpecularLightingIntensity >= 0 ? GetGIPostProcess(gameContext).mIndirectSpecularLightingIntensity : mIndirectSpecularLightingIntensity;
    }

    float GetIndirectDiffuseLightingIntensity(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mIndirectDiffuseLightingIntensity >= 0 ? GetGIPostProcess(gameContext).mIndirectDiffuseLightingIntensity : mIndirectDiffuseLightingIntensity;
    }

    bool IsEnableGISpecularCombine(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mEnableGISpecularCombine == BoolOverrideType::USE_GLOBAL_CONFIG ? mEnableGISpecularCombine : static_cast<bool>(GetGIPostProcess(gameContext).mEnableGISpecularCombine);
    }

    float GetMaxRoughnessToTrace(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mReflectionMaxRoughnessToTrace >= 0 ? GetGIPostProcess(gameContext).mReflectionMaxRoughnessToTrace : mReflectionMaxRoughnessToTrace;
    }

    float GetInvRoughnessFadeLength(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mReflectionRoughnessFadeLength > 0 ? (1.0f / GetGIPostProcess(gameContext).mReflectionRoughnessFadeLength) : (1.0f / mReflectionRoughnessFadeLength);
    }

    int GetDownsampleFactor(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mDownsampleFactor > 0 ? GetGIPostProcess(gameContext).mDownsampleFactor : mDownsampleFactor;
    }

    int GetAdaptiveProbeMinDownsampleFactor(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mAdaptiveProbeMinDownsampleFactor > 0 ? GetGIPostProcess(gameContext).mAdaptiveProbeMinDownsampleFactor : static_cast<int>(mAdaptiveProbeMinDownsampleFactor);
    }

    bool IsProbeOctHalfResolution(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mProbeOctHalfResolution == BoolOverrideType::USE_GLOBAL_CONFIG ? mProbeOctHalfResolution : static_cast<bool>(GetGIPostProcess(gameContext).mProbeOctHalfResolution);
    }

    bool IsVoxelRadiosityEnable(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mVoxelRadiosityEnable == BoolOverrideType::USE_GLOBAL_CONFIG ? mVoxelRadiosityEnable : static_cast<bool>(GetGIPostProcess(gameContext).mVoxelRadiosityEnable);
    }

    float GetRadiosityDecayRate(const GameContext& gameContext) const
    {
        return GetGIPostProcess(gameContext).mRadiosityDecayRate > 0 ? GetGIPostProcess(gameContext).mRadiosityDecayRate : mRadiosityDecayRate;
    }

    uint32_t GetVoxelChunkSize() const
    {
        return std::clamp(mVoxelChunkSize, 2u, HybridGI::GetClipmapResolutionXY() / 2);
    }
};


class RENDER_ENGINE_API HybridGIPass : public PassBase<HybridGIPassSettings, HybridGIPass>
{
public:
    HybridGIPass(IRenderPipeline* renderPipeline)
        : mRenderPipeline(renderPipeline) {}

    ~HybridGIPass();

    static PassDesc GetPassDesc();

public:
    enum class StageStatus
    {
        NONE_STAGE,
        Hybrid_GI_STAGE,
        Hybrid_GI_COMPLETED_STAGE,
    };

    StageStatus mStageStatus{StageStatus::NONE_STAGE};

    ViewModeVisualizeType mVisualize = ViewModeVisualizeType::Lit;

    REDTextureView* input_depthView = nullptr;
    REDTextureView* input_targetView = nullptr;

    REDTextureView* input_sceneColorView = nullptr;
    GBufferTextures input_gBufferViews{nullptr, nullptr, nullptr, nullptr};
    REDTextureView* input_objCullingGUIDView{nullptr};
    REDTextureView* input_aoView = nullptr;
    REDTextureView* input_gtaoBentNormalView = nullptr;
    REDTextureView* input_bentNormalAO = nullptr;
    REDTextureView* input_HiZView = nullptr;

    const ShadowProperties* input_shadowProperties = nullptr;

    REDTextureView* output_ClosestHiZView = nullptr;
    REDTextureView* output_diffuseIndirectView = nullptr;
    REDTextureView* output_specularIndirectView = nullptr;
    REDTextureView* output_compositeIndirectView = nullptr;

private:
    void SwapEmissiveColorRT();

public:


    bool NeedClosestHiZDepth() const
    {
        return mSetting.mUseHiZRayTracing;
    }

    REDTextureView* GetIndirectLighitingView() const
    {
        if (mSetting.mDebugShowVoxels && mSetting.mEnableVoxelGI)
        {
            return mDebugState.mDebugSceneColor;
        }
        else
        {
            return output_compositeIndirectView;
        }
    }

    std::tuple<REDTextureView*, REDTextureView*> GetIndirectLightingViews() const
    {
        return std::make_tuple(output_diffuseIndirectView, output_specularIndirectView);
    }

    auto GetShortRangeAOViw() -> REDTextureView*
    {
        if (mShortRangeAORenderer)
            return mShortRangeAORenderer->GetShortRangeAOTex();
        return nullptr;
    }

    bool GetEnableVoxelGI(const GameContext& gameContext)
    {
        return mSetting.IsEnableVoxelGI(gameContext);
    }

    void SetupSkyLightingGameContext(const GameContext& gameContext, REDPass* pass);

protected:
    bool ExecuteImp(const GameContext& gameContext);

    friend PassBase<HybridGIPassSettings, HybridGIPass>;

private:
    void InitializeOctahedralSolidAngleTexture(const GameContext& gameContext);

    void AssembleHybridGIScreenProbe(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView);

    cross::REDTextureView* AssembleHybridGenerateRays(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView);

    void AssembleHybridFGTraceHZB(const GameContext& gameContext, RenderingExecutionDescriptor* RED, GBufferTextures& gBufferViews, REDTextureView* depthView, REDTextureView* depthPyramid, REDTextureView* sceneColorView,
                                 REDTextureView* rayInfosForTracing);

    void AssembleHybridFilteringProbe(const GameContext& gameContext, RenderingExecutionDescriptor* RED);

    void AssembleHybridConvertToIrradiance(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* rayInfosForTracing);

    void AssembleHybridIntegrate(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView);

    void AssembleHybridScreenTemporalFilter(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView*& diffuseIndirect, REDTextureView*& specularIndirect);

    void AssembleDebugSHReadPass(const GameContext& gameContext, RenderingExecutionDescriptor* RED, int probeIndex, Float2 debugScreenPos, REDTextureView* sceneColorView);

    void UpdateReadBackBuffer(const GameContext& gameContext, RenderingExecutionDescriptor* RED);

    void AssembleDebugSHDrawPass(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* outputRT, REDTextureRef depthView);

    void AssembleRayDebug(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* depthView, REDTextureView* rayInfosForTracing);

private:
    FinalGatherCommonCB mHybridFGCommon;
    HybridCbViewParams mHybridViewParams;
    bool mInitialized{false};
    void InitializeHybridCommon(const GameContext& gameContext);
    void InitializeViewParams(const GameContext& gameContext);
    void InitializeParams(const GameContext& gameContext);

    void UpdateContextHybridCommon(const GameContext& gameContext);
    void UpdateContextViewParams(const GameContext& gameContext);

    void ExtendPreSceneColorRT(REDTextureRef sceneColorRT);

    struct FinalGatherTemporalState
    {
        REDTextureRef mPreSceneColorRT;

        REDTextureRef mDiffuseIndirectHistoryRT;
        REDTextureRef mRoughSpecularIndirectHistoryRT;

        REDTextureRef mNumFramesAccumulatedRT;
        REDTextureRef mFastUpdateModeHistoryRT;
        REDTextureRef mDefaultR8RT;
        REDTextureView* mDefaultR8View{nullptr};

        REDTextureRef mNormalHistoryRT;
        REDTextureRef mDepthHistoryRT;

        REDTextureRef mScreenProbeHitDistanceHistoryRT;
        REDTextureRef mScreenProbeRadianceHistoryRT;
        REDTextureRef mScreenProbeSceneDepthHistoryRT;
        REDTextureRef mScreenProbeTranslatedWorldPositionHistoryRT;
    };

    FinalGatherTemporalState mTemporalState;

private:
    void UpdateSkyLightContext(const GameContext& gameContext);
    void UpdateHybridGIRenderContext(const GameContext& gameContext);

    constexpr static int cDebugProbeNum = 2;

    struct cbDebugState
    {
        REDTextureView* mDebugSceneColor{nullptr};
        int mDebugSHCoefsSize{30 * 4 + 3 * 4};
        REDBufferView* mDebugSHCoefs[cDebugProbeNum]{nullptr, nullptr};
        std::shared_ptr<float> mReadBackSHData[cDebugProbeNum];
        std::queue<std::tuple<UInt32, std::unique_ptr<NGIStagingBuffer>, void*, SizeType>> mPendingCopyBackTasks;
    };

    cbDebugState mDebugState;
    void InitializeDebugState();

private:
    IRenderPipeline* mRenderPipeline;
    std::unique_ptr<HybridVoxelRenderer> mVoxelRenderer;
    std::unique_ptr<HybridSurfelRenderer> mSurfelRenderer;
    std::unique_ptr<HybridShortRangeAORenderer> mShortRangeAORenderer;
    std::unique_ptr<HybridSharc> mSharcRenderer;

#define RAY_DEBUG_MAX_FLYING_FRAME 3
    NGIStagingBuffer* mRayDebugFeedbackBuffer[RAY_DEBUG_MAX_FLYING_FRAME]{nullptr};
    PrimitiveData primitive[RAY_DEBUG_MAX_FLYING_FRAME];
    bool mRayDebugCurrentFreezeState{false};
    float* mRayDebugFreezeDatas{nullptr};
    UInt32 mRayDebugRayDataSize{0};
};

}
