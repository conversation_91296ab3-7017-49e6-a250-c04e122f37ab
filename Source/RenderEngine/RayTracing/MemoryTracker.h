#pragma once

#include "NativeGraphicsInterface/NGI.h"

namespace cross {

enum class TrackedMemoryUsage : uint8_t
{
    StaticBLAS,
    DynamicBLAS,
    ISMBLAS,
    HISMBlAS
};

class MemoryTracker
{
public:
    MemoryTracker() = default;
    
    ~MemoryTracker() = default;
    
    void Add(TrackedMemoryUsage usage, UInt32 size)
    {
        std::scoped_lock lock(mMutex);
        mMemoryUsages[usage] += size;
        mTotalMemory += size;
    }

    void Remove(TrackedMemoryUsage usage, UInt32 size)
    {
        std::scoped_lock lock(mMutex);
        mMemoryUsages[usage] -= size;
        mTotalMemory -= size;
    }

    UInt32 GetMemoryUsage(TrackedMemoryUsage usage) const
    {
        auto it = mMemoryUsages.find(usage);
        return it != mMemoryUsages.end() ? it->second : 0;
    }

    UInt32 GetTotalMemoryUsage() const
    {
        return mTotalMemory;
    }

protected:
    std::mutex mMutex;
    std::unordered_map<TrackedMemoryUsage, UInt32> mMemoryUsages;
    UInt32 mTotalMemory = 0;
};

}
