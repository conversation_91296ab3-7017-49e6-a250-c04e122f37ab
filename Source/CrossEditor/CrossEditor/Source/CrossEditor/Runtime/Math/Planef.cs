namespace CrossEditor
{
    public struct Planef
    {
        public Vector3f Normal;
        public float Distance;

        public Planef(Vector3f v1, Vector3f v2, Vector3f v3)
        {
            Vector3f Temp1 = v2 - v1;
            Vector3f Temp2 = v3 - v1;
            Normal = Vector3f.CrossProduct(ref Temp1, ref Temp2);
            Normal.Normalize();
            Distance = -Vector3f.DotProduct(ref Normal, ref v1);
        }
    }
}
