using System;

namespace CrossEditor
{
    internal class Line
    {
        // Given three colinear points pt1, pt2, pt, the function checks if
        // point pt lies on line segment 'pt1_pt2'
        public static bool OnSegment(Vector2f pt1, Vector2f pt2, Vector2f pt, double epsilon = 0.001)
        {
            if (pt.X - Math.Max(pt1.X, pt2.X) > epsilon ||
                Math.Min(pt1.X, pt2.X) - pt.X > epsilon ||
                pt.Y - Math.Max(pt1.Y, pt2.Y) > epsilon ||
                Math.Min(pt1.Y, pt2.Y) - pt.Y > epsilon)
                return false;

            if (Math.Abs(pt2.X - pt1.X) < epsilon)
                return Math.Abs(pt1.X - pt.X) < epsilon || Math.Abs(pt2.X - pt.X) < epsilon;
            if (Math.Abs(pt2.Y - pt1.Y) < epsilon)
                return Math.Abs(pt1.Y - pt.Y) < epsilon || Math.Abs(pt2.Y - pt.Y) < epsilon;

            double x = pt1.X + (pt.Y - pt1.Y) * (pt2.X - pt1.X) / (pt2.Y - pt1.Y);
            double y = pt1.Y + (pt.X - pt1.X) * (pt2.Y - pt1.Y) / (pt2.X - pt1.X);

            return Math.Abs(pt.X - x) < epsilon || Math.Abs(pt.Y - y) < epsilon;
        }

        internal enum Orientation
        {
            Colinear,
            Clockwise,
            Counterclockwise
        }

        /// To find orientation of ordered triplet (p, q, r)
        /// -------------> x_axis
        /// | 
        /// |
        /// |
        /// |
        /// v
        /// y_axis
        static public Orientation Orient(Vector2f p, Vector2f q, Vector2f r)
        {
            float val = (q.Y - p.Y) * (r.X - q.X) -
                (q.X - p.X) * (r.Y - q.Y);

            if (val == 0) return Orientation.Colinear;  // colinear

            return (val < 0) ? Orientation.Counterclockwise : Orientation.Clockwise; // clock or counterclock wise
        }
    }
}
