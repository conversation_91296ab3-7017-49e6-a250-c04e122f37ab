using CEngine;
using System;

namespace CrossEditor
{
    public struct Matrix4x4d
    {
        public double M11, M12, M13, M14;
        public double M21, M22, M23, M24;
        public double M31, M32, M33, M34;
        public double M41, M42, M43, M44;

        public Matrix4x4d(Float4x4 float4x4)
        {
            M11 = float4x4.m00;
            M12 = float4x4.m01;
            M13 = float4x4.m02;
            M14 = float4x4.m03;

            M21 = float4x4.m10;
            M22 = float4x4.m11;
            M23 = float4x4.m12;
            M24 = float4x4.m13;

            M31 = float4x4.m20;
            M32 = float4x4.m21;
            M33 = float4x4.m22;
            M34 = float4x4.m23;

            M41 = float4x4.m30;
            M42 = float4x4.m31;
            M43 = float4x4.m32;
            M44 = float4x4.m33;
        }

        public Matrix4x4d(Double4x4 double4x4)
        {
            M11 = double4x4.m00;
            M12 = double4x4.m01;
            M13 = double4x4.m02;
            M14 = double4x4.m03;

            M21 = double4x4.m10;
            M22 = double4x4.m11;
            M23 = double4x4.m12;
            M24 = double4x4.m13;

            M31 = double4x4.m20;
            M32 = double4x4.m21;
            M33 = double4x4.m22;
            M34 = double4x4.m23;

            M41 = double4x4.m30;
            M42 = double4x4.m31;
            M43 = double4x4.m32;
            M44 = double4x4.m33;
        }

        public Matrix4x4d(Clicross.Double4x4 double4x4)
        {
            M11 = double4x4.m00;
            M12 = double4x4.m01;
            M13 = double4x4.m02;
            M14 = double4x4.m03;

            M21 = double4x4.m10;
            M22 = double4x4.m11;
            M23 = double4x4.m12;
            M24 = double4x4.m13;

            M31 = double4x4.m20;
            M32 = double4x4.m21;
            M33 = double4x4.m22;
            M34 = double4x4.m23;

            M41 = double4x4.m30;
            M42 = double4x4.m31;
            M43 = double4x4.m32;
            M44 = double4x4.m33;
        }
        public Matrix4x4d(Matrix4x4f Matrix)
        {
            M11 = Matrix.M11;
            M12 = Matrix.M12;
            M13 = Matrix.M13;
            M14 = Matrix.M14;

            M21 = Matrix.M21;
            M22 = Matrix.M22;
            M23 = Matrix.M23;
            M24 = Matrix.M24;

            M31 = Matrix.M31;
            M32 = Matrix.M32;
            M33 = Matrix.M33;
            M34 = Matrix.M34;

            M41 = Matrix.M41;
            M42 = Matrix.M42;
            M43 = Matrix.M43;
            M44 = Matrix.M44;
        }

        public Float4x4 ToFloat4x4()
        {
            Float4x4 float4x4 = new Float4x4();
            float4x4.m00 = (float)M11;
            float4x4.m01 = (float)M12;
            float4x4.m02 = (float)M13;
            float4x4.m03 = (float)M14;

            float4x4.m10 = (float)M21;
            float4x4.m11 = (float)M22;
            float4x4.m12 = (float)M23;
            float4x4.m13 = (float)M24;

            float4x4.m20 = (float)M31;
            float4x4.m21 = (float)M32;
            float4x4.m22 = (float)M33;
            float4x4.m23 = (float)M34;

            float4x4.m30 = (float)M41;
            float4x4.m31 = (float)M42;
            float4x4.m32 = (float)M43;
            float4x4.m33 = (float)M44;
            return float4x4;
        }
        public Clicross.Float4x4 ToCliFloat4x4()
        {
            Clicross.Float4x4 float4x4 = new Clicross.Float4x4();
            float4x4.m00 = (float)M11;
            float4x4.m01 = (float)M12;
            float4x4.m02 = (float)M13;
            float4x4.m03 = (float)M14;

            float4x4.m10 = (float)M21;
            float4x4.m11 = (float)M22;
            float4x4.m12 = (float)M23;
            float4x4.m13 = (float)M24;

            float4x4.m20 = (float)M31;
            float4x4.m21 = (float)M32;
            float4x4.m22 = (float)M33;
            float4x4.m23 = (float)M34;

            float4x4.m30 = (float)M41;
            float4x4.m31 = (float)M42;
            float4x4.m32 = (float)M43;
            float4x4.m33 = (float)M44;
            return float4x4;
        }
        public Double4x4 ToDouble4x4()
        {
            Double4x4 double4x4 = new Double4x4();
            double4x4.m00 = M11;
            double4x4.m01 = M12;
            double4x4.m02 = M13;
            double4x4.m03 = M14;

            double4x4.m10 = M21;
            double4x4.m11 = M22;
            double4x4.m12 = M23;
            double4x4.m13 = M24;

            double4x4.m20 = M31;
            double4x4.m21 = M32;
            double4x4.m22 = M33;
            double4x4.m23 = M34;

            double4x4.m30 = M41;
            double4x4.m31 = M42;
            double4x4.m32 = M43;
            double4x4.m33 = M44;
            return double4x4;
        }
        public Clicross.Double4x4 ToClicrossDouble4x4()
        {
            Clicross.Double4x4 double4x4 = new Clicross.Double4x4();
            double4x4.m00 = M11;
            double4x4.m01 = M12;
            double4x4.m02 = M13;
            double4x4.m03 = M14;

            double4x4.m10 = M21;
            double4x4.m11 = M22;
            double4x4.m12 = M23;
            double4x4.m13 = M24;

            double4x4.m20 = M31;
            double4x4.m21 = M32;
            double4x4.m22 = M33;
            double4x4.m23 = M34;

            double4x4.m30 = M41;
            double4x4.m31 = M42;
            double4x4.m32 = M43;
            double4x4.m33 = M44;
            return double4x4;
        }

        public Matrix4x4f ToMatrix4x4f()
        {
            Matrix4x4f Matrix = new Matrix4x4f();
            Matrix.M11 = (float)M11;
            Matrix.M12 = (float)M12;
            Matrix.M13 = (float)M13;
            Matrix.M14 = (float)M14;

            Matrix.M21 = (float)M21;
            Matrix.M22 = (float)M22;
            Matrix.M23 = (float)M23;
            Matrix.M24 = (float)M24;

            Matrix.M31 = (float)M31;
            Matrix.M32 = (float)M32;
            Matrix.M33 = (float)M33;
            Matrix.M34 = (float)M34;

            Matrix.M41 = (float)M41;
            Matrix.M42 = (float)M42;
            Matrix.M43 = (float)M43;
            Matrix.M44 = (float)M44;
            return Matrix;
        }

        public void LoadIdentity()
        {
            M11 = 1.0f;
            M12 = 0.0f;
            M13 = 0.0f;
            M14 = 0.0f;

            M21 = 0.0f;
            M22 = 1.0f;
            M23 = 0.0f;
            M24 = 0.0f;

            M31 = 0.0f;
            M32 = 0.0f;
            M33 = 1.0f;
            M34 = 0.0f;

            M41 = 0.0f;
            M42 = 0.0f;
            M43 = 0.0f;
            M44 = 1.0f;
        }

        public void GetPosition(out Vector3d Position)
        {
            Position = new Vector3d(M41, M42, M43);
        }

        public void SetPosition(ref Vector3d Position)
        {
            M41 = Position.X;
            M42 = Position.Y;
            M43 = Position.Z;
        }

        public void SetPosition(double X, double Y, double Z)
        {
            M41 = X;
            M42 = Y;
            M43 = Z;
        }

        public void GetScale(out Vector3d Scale)
        {
            Scale.X = new Vector4d(M11, M12, M13, M14).Magnitude();
            Scale.Y = new Vector4d(M21, M22, M23, M24).Magnitude();
            Scale.Z = new Vector4d(M31, M32, M33, M34).Magnitude();
        }

        public void SetScaling(double X, double Y, double Z)
        {
            M11 = X;
            M22 = Y;
            M33 = Z;
        }

        public void GetRotation(out Vector3d Rotation)
        {

            var trs = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(this.ToClicrossDouble4x4());
            Vector3d translation = new Vector3d(trs.translation.x, trs.translation.y, trs.translation.z);
            Vector3d sacle = new Vector3d(trs.scaling.x, trs.scaling.y, trs.scaling.z);
            Quaterniond quaternion = new Quaterniond(trs.rotation.x, trs.rotation.y, trs.rotation.z, trs.rotation.w);
            Rotation = Quaterniond.ToEuler(quaternion);
        }

        public void SetScale(Vector3d Scale)
        {
            var trs = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(this.ToClicrossDouble4x4());
            var result = Clicross.math.MatrixUtil.Math_MatrixComposeD(new Clicross.Double3(Scale.X, Scale.Y, Scale.Z), new Clicross.Quaternion64(trs.rotation.x, trs.rotation.y, trs.rotation.z, trs.rotation.w), trs.translation);
            this = new Matrix4x4d(result);
        }

        public void SetRotationX(double X)
        {
            double CosX = (double)Math.Cos(X);
            double SinX = (double)Math.Sin(X);

            M11 = 1.0f;
            M12 = 0.0f;
            M13 = 0.0f;

            M21 = 0.0f;
            M22 = CosX;
            M23 = SinX;

            M31 = 0.0f;
            M32 = -SinX;
            M33 = CosX;
        }

        public void SetRotationY(double Y)
        {
            double CosY = (double)Math.Cos(Y);
            double SinY = (double)Math.Sin(Y);

            M11 = CosY;
            M12 = 0.0f;
            M13 = -SinY;

            M21 = 0.0f;
            M22 = 1.0f;
            M23 = 0.0f;

            M31 = SinY;
            M32 = 0.0f;
            M33 = CosY;
        }

        public void SetRotationZ(double Z)
        {
            double CosZ = Math.Cos(Z);
            double SinZ = Math.Sin(Z);

            M11 = CosZ;
            M12 = SinZ;
            M13 = 0.0f;

            M21 = -SinZ;
            M22 = CosZ;
            M23 = 0.0f;

            M31 = 0.0f;
            M32 = 0.0f;
            M33 = 1.0f;
        }

        public void Mul(ref Matrix4x4d m1, ref Matrix4x4d m2)
        {
            Matrix4x4d m = new Matrix4x4d();

            m.M11 = m1.M11 * m2.M11 + m1.M12 * m2.M21 + m1.M13 * m2.M31 + m1.M14 * m2.M41;
            m.M12 = m1.M11 * m2.M12 + m1.M12 * m2.M22 + m1.M13 * m2.M32 + m1.M14 * m2.M42;
            m.M13 = m1.M11 * m2.M13 + m1.M12 * m2.M23 + m1.M13 * m2.M33 + m1.M14 * m2.M43;
            m.M14 = m1.M11 * m2.M14 + m1.M12 * m2.M24 + m1.M13 * m2.M34 + m1.M14 * m2.M44;

            m.M21 = m1.M21 * m2.M11 + m1.M22 * m2.M21 + m1.M23 * m2.M31 + m1.M24 * m2.M41;
            m.M22 = m1.M21 * m2.M12 + m1.M22 * m2.M22 + m1.M23 * m2.M32 + m1.M24 * m2.M42;
            m.M23 = m1.M21 * m2.M13 + m1.M22 * m2.M23 + m1.M23 * m2.M33 + m1.M24 * m2.M43;
            m.M24 = m1.M21 * m2.M14 + m1.M22 * m2.M24 + m1.M23 * m2.M34 + m1.M24 * m2.M44;

            m.M31 = m1.M31 * m2.M11 + m1.M32 * m2.M21 + m1.M33 * m2.M31 + m1.M34 * m2.M41;
            m.M32 = m1.M31 * m2.M12 + m1.M32 * m2.M22 + m1.M33 * m2.M32 + m1.M34 * m2.M42;
            m.M33 = m1.M31 * m2.M13 + m1.M32 * m2.M23 + m1.M33 * m2.M33 + m1.M34 * m2.M43;
            m.M34 = m1.M31 * m2.M14 + m1.M32 * m2.M24 + m1.M33 * m2.M34 + m1.M34 * m2.M44;

            m.M41 = m1.M41 * m2.M11 + m1.M42 * m2.M21 + m1.M43 * m2.M31 + m1.M44 * m2.M41;
            m.M42 = m1.M41 * m2.M12 + m1.M42 * m2.M22 + m1.M43 * m2.M32 + m1.M44 * m2.M42;
            m.M43 = m1.M41 * m2.M13 + m1.M42 * m2.M23 + m1.M43 * m2.M33 + m1.M44 * m2.M43;
            m.M44 = m1.M41 * m2.M14 + m1.M42 * m2.M24 + m1.M43 * m2.M34 + m1.M44 * m2.M44;

            this = m;
        }

        public Matrix4x4d Inverse()
        {
            var result = Clicross.math.MatrixUtil.Math_MatrixInverse_d(this.ToClicrossDouble4x4());

            return new Matrix4x4d(result);
        }

        public void Transform(ref Vector3d v)
        {
            double x = M11 * v.X + M21 * v.Y + M31 * v.Z + M41;
            double y = M12 * v.X + M22 * v.Y + M32 * v.Z + M42;
            double z = M13 * v.X + M23 * v.Y + M33 * v.Z + M43;
            v.X = x;
            v.Y = y;
            v.Z = z;
        }

        public void Transform(ref Vector4d v)
        {
            double x = M11 * v.X + M21 * v.Y + M31 * v.Z + M41 * v.W;
            double y = M12 * v.X + M22 * v.Y + M32 * v.Z + M42 * v.W;
            double z = M13 * v.X + M23 * v.Y + M33 * v.Z + M43 * v.W;
            double w = M14 * v.X + M24 * v.Y + M34 * v.Z + M44 * v.W;
            v.X = x;
            v.Y = y;
            v.Z = z;
            v.W = w;
        }

        public void TransformNormal(ref Vector3d v)
        {
            double x = M11 * v.X + M21 * v.Y + M31 * v.Z;
            double y = M12 * v.X + M22 * v.Y + M32 * v.Z;
            double z = M13 * v.X + M23 * v.Y + M33 * v.Z;
            v.X = x;
            v.Y = y;
            v.Z = z;
        }

        public void TransformNormal(ref Double3 v)
        {
            double x = M11 * v.x + M21 * v.y + M31 * v.z;
            double y = M12 * v.x + M22 * v.y + M32 * v.z;
            double z = M13 * v.x + M23 * v.y + M33 * v.z;
            v.x = x;
            v.y = y;
            v.z = z;
        }

        public override string ToString()
        {
            return string.Format("({0}, {1}, {2}, {3},\n {4}, {5}, {6}, {7},\n {8}, {9}, {10}, {11}\n {12}, {13}, {14}, {15})",
                M11, M12, M13, M14, M21, M22, M23, M24, M31, M32, M33, M34, M41, M42, M43, M44);
        }
    }
}
