namespace CrossEditor
{
    public struct Planed
    {
        public Vector3d Normal;
        public double Distance;

        public Planed(Vector3d v1, Vector3d v2, Vector3d v3)
        {
            Vector3d Temp1 = v2 - v1;
            Vector3d Temp2 = v3 - v1;
            Normal = Vector3d.CrossProduct(ref Temp1, ref Temp2);
            Normal.Normalize();
            Distance = -Vector3d.DotProduct(ref Normal, ref v1);
        }

        public Planef ToPlanef()
        {
            Planef Planef = new Planef();
            Planef.Normal = this.Normal.ToVector3f();
            Planef.Distance = (float)this.Distance;
            return Planef;
        }
        public Clicross.Plane2 ToCliStruct()
        {
            return new Clicross.Plane2((float)Normal.X, (float)Normal.Y, (float)Normal.Z, (float)Distance);
        }
    }
}
