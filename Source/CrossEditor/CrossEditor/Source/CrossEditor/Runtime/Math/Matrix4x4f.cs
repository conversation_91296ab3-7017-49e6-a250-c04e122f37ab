using CEngine;
using System;

namespace CrossEditor
{
    public struct Matrix4x4f
    {
        public float M11, M12, M13, M14;
        public float M21, M22, M23, M24;
        public float M31, M32, M33, M34;
        public float M41, M42, M43, M44;

        public Matrix4x4f(Float4x4 float4x4)
        {
            M11 = float4x4.m00;
            M12 = float4x4.m01;
            M13 = float4x4.m02;
            M14 = float4x4.m03;

            M21 = float4x4.m10;
            M22 = float4x4.m11;
            M23 = float4x4.m12;
            M24 = float4x4.m13;

            M31 = float4x4.m20;
            M32 = float4x4.m21;
            M33 = float4x4.m22;
            M34 = float4x4.m23;

            M41 = float4x4.m30;
            M42 = float4x4.m31;
            M43 = float4x4.m32;
            M44 = float4x4.m33;
        }
        public Matrix4x4f(Clicross.Float4x4 float4x4)
        {
            M11 = float4x4.m00;
            M12 = float4x4.m01;
            M13 = float4x4.m02;
            M14 = float4x4.m03;

            M21 = float4x4.m10;
            M22 = float4x4.m11;
            M23 = float4x4.m12;
            M24 = float4x4.m13;

            M31 = float4x4.m20;
            M32 = float4x4.m21;
            M33 = float4x4.m22;
            M34 = float4x4.m23;

            M41 = float4x4.m30;
            M42 = float4x4.m31;
            M43 = float4x4.m32;
            M44 = float4x4.m33;
        }

        public Float4x4 ToFloat4x4()
        {
            Float4x4 float4x4 = new Float4x4();
            float4x4.m00 = M11;
            float4x4.m01 = M12;
            float4x4.m02 = M13;
            float4x4.m03 = M14;

            float4x4.m10 = M21;
            float4x4.m11 = M22;
            float4x4.m12 = M23;
            float4x4.m13 = M24;

            float4x4.m20 = M31;
            float4x4.m21 = M32;
            float4x4.m22 = M33;
            float4x4.m23 = M34;

            float4x4.m30 = M41;
            float4x4.m31 = M42;
            float4x4.m32 = M43;
            float4x4.m33 = M44;
            return float4x4;
        }
        public Clicross.Float4x4 ToClicrossFloat4x4()
        {
            Clicross.Float4x4 float4x4 = new Clicross.Float4x4();
            float4x4.m00 = M11;
            float4x4.m01 = M12;
            float4x4.m02 = M13;
            float4x4.m03 = M14;

            float4x4.m10 = M21;
            float4x4.m11 = M22;
            float4x4.m12 = M23;
            float4x4.m13 = M24;

            float4x4.m20 = M31;
            float4x4.m21 = M32;
            float4x4.m22 = M33;
            float4x4.m23 = M34;

            float4x4.m30 = M41;
            float4x4.m31 = M42;
            float4x4.m32 = M43;
            float4x4.m33 = M44;
            return float4x4;
        }

        public void LoadIdentity()
        {
            M11 = 1.0f;
            M12 = 0.0f;
            M13 = 0.0f;
            M14 = 0.0f;

            M21 = 0.0f;
            M22 = 1.0f;
            M23 = 0.0f;
            M24 = 0.0f;

            M31 = 0.0f;
            M32 = 0.0f;
            M33 = 1.0f;
            M34 = 0.0f;

            M41 = 0.0f;
            M42 = 0.0f;
            M43 = 0.0f;
            M44 = 1.0f;
        }

        public void GetPosition(out Vector3f Position)
        {
            Position = new Vector3f(M41, M42, M43);
        }

        public void SetPosition(ref Vector3f Position)
        {
            M41 = Position.X;
            M42 = Position.Y;
            M43 = Position.Z;
        }

        public void SetPosition(float X, float Y, float Z)
        {
            M41 = X;
            M42 = Y;
            M43 = Z;
        }

        public void GetScale(out Vector3f Scale)
        {
            Scale.X = new Vector4f(M11, M12, M13, M14).Magnitude();
            Scale.Y = new Vector4f(M21, M22, M23, M24).Magnitude();
            Scale.Z = new Vector4f(M31, M32, M33, M34).Magnitude();
        }

        public void SetScaling(float X, float Y, float Z)
        {
            M11 = X;
            M22 = Y;
            M33 = Z;
        }

        public void GetRotation(out Vector3f Rotation)
        {
            var trs = Clicross.math.MatrixUtil.Math_MatrixDecompose(this.ToClicrossFloat4x4());
            Vector3f translation = new Vector3f(trs.translation.x, trs.translation.y, trs.translation.z);
            Vector3f sacle = new Vector3f(trs.scaling.x, trs.scaling.y, trs.scaling.z);
            Quaternionf quaternion = new Quaternionf(trs.rotation.x, trs.rotation.y, trs.rotation.z, trs.rotation.w);
            Rotation = Quaternionf.ToEuler(quaternion);
        }

        public void SetScale(Vector3f Scale)
        {
            var trs = Clicross.math.MatrixUtil.Math_MatrixDecompose(this.ToClicrossFloat4x4());
            var result = Clicross.math.MatrixUtil.Math_MatrixCompose(new Clicross.Float3(Scale.X, Scale.Y, Scale.Z), new Clicross.Quaternion(trs.rotation.x, trs.rotation.y, trs.rotation.z, trs.rotation.w), trs.translation);
            this = new Matrix4x4f(result);
        }

        public void SetRotationX(float X)
        {
            float CosX = (float)Math.Cos(X);
            float SinX = (float)Math.Sin(X);

            M11 = 1.0f;
            M12 = 0.0f;
            M13 = 0.0f;

            M21 = 0.0f;
            M22 = CosX;
            M23 = SinX;

            M31 = 0.0f;
            M32 = -SinX;
            M33 = CosX;
        }

        public void SetRotationY(float Y)
        {
            float CosY = (float)Math.Cos(Y);
            float SinY = (float)Math.Sin(Y);

            M11 = CosY;
            M12 = 0.0f;
            M13 = -SinY;

            M21 = 0.0f;
            M22 = 1.0f;
            M23 = 0.0f;

            M31 = SinY;
            M32 = 0.0f;
            M33 = CosY;
        }

        public void SetRotationZ(float Z)
        {
            float CosZ = (float)Math.Cos(Z);
            float SinZ = (float)Math.Sin(Z);

            M11 = CosZ;
            M12 = SinZ;
            M13 = 0.0f;

            M21 = -SinZ;
            M22 = CosZ;
            M23 = 0.0f;

            M31 = 0.0f;
            M32 = 0.0f;
            M33 = 1.0f;
        }

        public void Mul(ref Matrix4x4f m1, ref Matrix4x4f m2)
        {
            Matrix4x4f m = new Matrix4x4f();

            m.M11 = m1.M11 * m2.M11 + m1.M12 * m2.M21 + m1.M13 * m2.M31 + m1.M14 * m2.M41;
            m.M12 = m1.M11 * m2.M12 + m1.M12 * m2.M22 + m1.M13 * m2.M32 + m1.M14 * m2.M42;
            m.M13 = m1.M11 * m2.M13 + m1.M12 * m2.M23 + m1.M13 * m2.M33 + m1.M14 * m2.M43;
            m.M14 = m1.M11 * m2.M14 + m1.M12 * m2.M24 + m1.M13 * m2.M34 + m1.M14 * m2.M44;

            m.M21 = m1.M21 * m2.M11 + m1.M22 * m2.M21 + m1.M23 * m2.M31 + m1.M24 * m2.M41;
            m.M22 = m1.M21 * m2.M12 + m1.M22 * m2.M22 + m1.M23 * m2.M32 + m1.M24 * m2.M42;
            m.M23 = m1.M21 * m2.M13 + m1.M22 * m2.M23 + m1.M23 * m2.M33 + m1.M24 * m2.M43;
            m.M24 = m1.M21 * m2.M14 + m1.M22 * m2.M24 + m1.M23 * m2.M34 + m1.M24 * m2.M44;

            m.M31 = m1.M31 * m2.M11 + m1.M32 * m2.M21 + m1.M33 * m2.M31 + m1.M34 * m2.M41;
            m.M32 = m1.M31 * m2.M12 + m1.M32 * m2.M22 + m1.M33 * m2.M32 + m1.M34 * m2.M42;
            m.M33 = m1.M31 * m2.M13 + m1.M32 * m2.M23 + m1.M33 * m2.M33 + m1.M34 * m2.M43;
            m.M34 = m1.M31 * m2.M14 + m1.M32 * m2.M24 + m1.M33 * m2.M34 + m1.M34 * m2.M44;

            m.M41 = m1.M41 * m2.M11 + m1.M42 * m2.M21 + m1.M43 * m2.M31 + m1.M44 * m2.M41;
            m.M42 = m1.M41 * m2.M12 + m1.M42 * m2.M22 + m1.M43 * m2.M32 + m1.M44 * m2.M42;
            m.M43 = m1.M41 * m2.M13 + m1.M42 * m2.M23 + m1.M43 * m2.M33 + m1.M44 * m2.M43;
            m.M44 = m1.M41 * m2.M14 + m1.M42 * m2.M24 + m1.M43 * m2.M34 + m1.M44 * m2.M44;

            this = m;
        }

        public void MulD(ref Matrix4x4f m1, ref Matrix4x4f m2)
        {
            Matrix4x4d m1d = new Matrix4x4d(m1);
            Matrix4x4d m2d = new Matrix4x4d(m2);
            Matrix4x4d m = new Matrix4x4d();
            m.Mul(ref m1d, ref m2d);
            this = m.ToMatrix4x4f();
        }

        public Matrix4x4f Inverse()
        {
            var res = Clicross.math.MatrixUtil.Math_MatrixInverse(this.ToClicrossFloat4x4());
            return new Matrix4x4f(res);
        }

        public Matrix4x4f InverseD()
        {
            Matrix4x4d This = new Matrix4x4d(this);
            return This.Inverse().ToMatrix4x4f();
        }

        public void Transform(ref Vector3f v)
        {
            float x = M11 * v.X + M21 * v.Y + M31 * v.Z + M41;
            float y = M12 * v.X + M22 * v.Y + M32 * v.Z + M42;
            float z = M13 * v.X + M23 * v.Y + M33 * v.Z + M43;
            v.X = x;
            v.Y = y;
            v.Z = z;
        }

        public void Transform(ref Vector4f v)
        {
            float x = M11 * v.X + M21 * v.Y + M31 * v.Z + M41 * v.W;
            float y = M12 * v.X + M22 * v.Y + M32 * v.Z + M42 * v.W;
            float z = M13 * v.X + M23 * v.Y + M33 * v.Z + M43 * v.W;
            float w = M14 * v.X + M24 * v.Y + M34 * v.Z + M44 * v.W;
            v.X = x;
            v.Y = y;
            v.Z = z;
            v.W = w;
        }

        public void TransformNormal(ref Vector3f v)
        {
            float x = M11 * v.X + M21 * v.Y + M31 * v.Z;
            float y = M12 * v.X + M22 * v.Y + M32 * v.Z;
            float z = M13 * v.X + M23 * v.Y + M33 * v.Z;
            v.X = x;
            v.Y = y;
            v.Z = z;
        }

        public override string ToString()
        {
            return string.Format("({0}, {1}, {2}, {3},\n {4}, {5}, {6}, {7},\n {8}, {9}, {10}, {11}\n {12}, {13}, {14}, {15})",
                M11, M12, M13, M14, M21, M22, M23, M24, M31, M32, M33, M34, M41, M42, M43, M44);
        }
    }
}
