using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    public class CanvasWatcher
    {
        private static CanvasWatcher _Instance = new CanvasWatcher();

        public FileSystemWatcher _ProjectWatcher;
        public FileSystemWatcher _EngineResourceWatcher;
        public List<string> _ChangedFiles = new List<string>();
        public List<string> _RenamedFiles = new List<string>();

        public static CanvasWatcher GetInstance()
        {
            return _Instance;
        }

        public void Initialize(string ProjectDirectory, string EngineResourceDirectory)
        {
            _ProjectWatcher = new FileSystemWatcher();
            _ProjectWatcher.Filters.Add("*.html");
            _ProjectWatcher.Filters.Add("*.rml");
            _ProjectWatcher.Path = ProjectDirectory;
            _ProjectWatcher.Changed += OnChanged;
            _ProjectWatcher.Renamed += OnRenamed;
            _ProjectWatcher.EnableRaisingEvents = true;
            _ProjectWatcher.IncludeSubdirectories = true;
            _ProjectWatcher.NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.DirectoryName | NotifyFilters.FileName;

            _EngineResourceWatcher = new FileSystemWatcher();
            _EngineResourceWatcher.Filters.Add("*.html");
            _EngineResourceWatcher.Filters.Add("*.rml");
            _EngineResourceWatcher.Path = EngineResourceDirectory;
            _EngineResourceWatcher.Changed += OnChanged;
            _EngineResourceWatcher.Renamed += OnRenamed;
            _EngineResourceWatcher.EnableRaisingEvents = true;
            _EngineResourceWatcher.IncludeSubdirectories = true;
            _EngineResourceWatcher.NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.DirectoryName | NotifyFilters.FileName;
        }

        private void OnChanged(object source, FileSystemEventArgs e)
        {
            if (e.ChangeType == WatcherChangeTypes.Changed)
            {
                _ChangedFiles.Add(e.Name);
            }
        }

        private void OnRenamed(object sender, RenamedEventArgs e)
        {
            if (e.ChangeType == WatcherChangeTypes.Renamed)
            {
                _ChangedFiles.Add(e.OldName);
                _RenamedFiles.Add(e.Name);
            }
        }

        public void Update()
        {
            if (_ChangedFiles.Count == 0)
                return;

            bool bRenamed = _RenamedFiles.Count != 0;

            Entity Root = EditorScene.GetInstance().GetRoot();
            if (Root != null)
            {
                Queue<Entity> Queue = new Queue<Entity>();
                Queue.Enqueue(Root);
                while (Queue.Count != 0)
                {
                    Entity Head = Queue.Dequeue();
                    if (Head.HasComponent(typeof(CanvasComponent)))
                    {
                        CanvasComponent Canvas = Head.GetComponent(typeof(CanvasComponent)) as CanvasComponent;
                        string CanvasPath = Canvas.Path;
                        for (int i = 0; i < _ChangedFiles.Count; i++)
                        {
                            string ChangedPath = PathHelper.ToStandardForm(_ChangedFiles[i]);
                            if (string.Compare(ChangedPath, CanvasPath, StringComparison.OrdinalIgnoreCase) == 0)
                            {
                                if (bRenamed)
                                    Canvas.Path = PathHelper.ToStandardForm(_RenamedFiles[i]);
                                else
                                    Canvas.Reload();
                                break;
                            }
                        }
                    }

                    foreach (Entity Child in Head.Children)
                    {
                        Queue.Enqueue(Child);
                    }
                }
            }

            _ChangedFiles.Clear();
            _RenamedFiles.Clear();
        }
    }
}
