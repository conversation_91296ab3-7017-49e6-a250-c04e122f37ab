using CEngine;
using System.IO;
using System.Reflection;
using System.Xml;
using System.Xml.Serialization;

namespace CrossEditor
{
    [XmlRoot]
    public class ShaderConfig
    {
        [XmlAttribute]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Compile shader on-demand")]
        public bool useOnDemandCompilation { set; get; }

        //[XmlAttribute]
        //[PropertyInfo(PropertyType = "Auto", ToolTips = "Skip shader cache")]
        public bool IgnoreCache { set; get; }

        [XmlAttribute]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Auto import new shader file")]
        public bool AutoImportNewFile { set; get; }

        [XmlAttribute]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Auto import dirty shader when editor activate")]
        public bool AutoImportDirtyShaderWhenEditorActivate { set; get; }

        [XmlAttribute]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Generate all variants for current shader format in used")]
        public bool GenAllVariants { set; get; }

        [XmlAttribute]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to build shader maps when startup")]
        public bool BuildShaderMapsAtStartup { set; get; }

        [XmlAttribute]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to check shader modules for auto import shaders")]
        public bool CheckShaderModules { set; get; }

        [XmlAttribute]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to generate shader maps for shader modules")]
        public bool GenShaderMaps { set; get; }

        [XmlAttribute]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to generate shader maps debug info")]
        public bool GenShaderMapsDebugInfo { set; get; }

        public const string CONFIG_FILENAME = EditorConfig.EDITOR_CONFIG_RELATIVE_PATH + "ShaderConfig.config";

        public ShaderConfig()
        {
            useOnDemandCompilation = true;
            IgnoreCache = false;
            AutoImportNewFile = false;
            GenAllVariants = true;
            AutoImportDirtyShaderWhenEditorActivate = true;
            BuildShaderMapsAtStartup = true;
            CheckShaderModules = true;
            GenShaderMaps = true;
            GenShaderMapsDebugInfo = false;
        }

        public void Load()
        {
            if (File.Exists(CONFIG_FILENAME))
            {
                XmlSerializer Ser = new XmlSerializer(typeof(ShaderConfig));
                using (StreamReader Reader = new StreamReader(CONFIG_FILENAME))
                {
                    ShaderConfig config = (ShaderConfig)Ser.Deserialize(Reader);
                    useOnDemandCompilation = config.useOnDemandCompilation;
                    //IgnoreCache = config.IgnoreCache;
                    AutoImportNewFile = config.AutoImportNewFile;
                    GenAllVariants = config.GenAllVariants;
                    AutoImportDirtyShaderWhenEditorActivate = config.AutoImportDirtyShaderWhenEditorActivate;
                    BuildShaderMapsAtStartup = config.BuildShaderMapsAtStartup;
                    CheckShaderModules = config.CheckShaderModules;
                    GenShaderMaps = config.GenShaderMaps;
                    GenShaderMapsDebugInfo = config.GenShaderMapsDebugInfo;
                    Reader.Close();
                }
            }
        }

        public void Dump()
        {
            XmlSerializer Ser = new XmlSerializer(typeof(ShaderConfig));
            using (StreamWriter Writer = new StreamWriter(CONFIG_FILENAME))
            {
                Ser.Serialize(Writer, this);
                Writer.Close();
            }
        }
    }
}