using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;

namespace CrossEditor
{
    public class ScriptWatcher
    {
        private static ScriptWatcher _Instance = new ScriptWatcher();

        public FileSystemWatcher _ProjectWatcher;
        public FileSystemWatcher _EngineResourceWatcher;
        public List<string> _ChangedFiles = new List<string>();
        public List<string> _ChangedTsFiles = new List<string>();
        public List<string> _RenamedFilesOld = new List<string>();
        public List<string> _RenamedFiles = new List<string>();

        public static ScriptWatcher GetInstance()
        {
            return _Instance;
        }
        bool IsTsFile(string path)
        {
            bool bIndexFile = path.EndsWith(".d.ts") || path.EndsWith(".d.mts");
            bool bTsFile = path.EndsWith(".ts") || path.EndsWith(".mts");

            return bTsFile && !bIndexFile;
        }
        public void Initialize(string ProjectDirectory, string EngineResourceDirectory)
        {
            _ProjectWatcher = new FileSystemWatcher();
            _ProjectWatcher.Filters.Add("*.lua");
            _ProjectWatcher.Filters.Add("*.js");
            _ProjectWatcher.Filters.Add("*.mjs");
            _ProjectWatcher.Filters.Add("*.mts");
            _ProjectWatcher.Filters.Add("*.ts");
            _ProjectWatcher.Path = ProjectDirectory;
            _ProjectWatcher.Changed += OnChanged;
            _ProjectWatcher.Renamed += OnRenamed;
            _ProjectWatcher.EnableRaisingEvents = true;
            _ProjectWatcher.IncludeSubdirectories = true;
            _ProjectWatcher.NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.DirectoryName | NotifyFilters.FileName;

            _EngineResourceWatcher = new FileSystemWatcher();
            _EngineResourceWatcher.Filters.Add("*.lua");
            _ProjectWatcher.Filters.Add("*.js");
            _ProjectWatcher.Filters.Add("*.mjs");
            _ProjectWatcher.Filters.Add("*.mts");
            _ProjectWatcher.Filters.Add("*.ts");
            _EngineResourceWatcher.Path = EngineResourceDirectory;
            _EngineResourceWatcher.Changed += OnChanged;
            _EngineResourceWatcher.Renamed += OnRenamed;
            _EngineResourceWatcher.EnableRaisingEvents = true;
            _EngineResourceWatcher.IncludeSubdirectories = true;
            _EngineResourceWatcher.NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime | NotifyFilters.DirectoryName | NotifyFilters.FileName;
        }

        private void OnChanged(object Source, FileSystemEventArgs e)
        {
            if (e.ChangeType == WatcherChangeTypes.Changed)
            {
                if (_ChangedFiles.IndexOf(e.Name) >= 0)
                    return;
                if (IsTsFile(e.Name))
                {
                    _ChangedTsFiles.Add(e.Name);
                    _ChangedFiles.Add(e.Name);
                }
                else
                {
                    _ChangedFiles.Add(e.Name);
                }
            }
        }

        private void OnRenamed(object Source, RenamedEventArgs e)
        {
            if (e.ChangeType == WatcherChangeTypes.Renamed)
            {
                if (_RenamedFiles.IndexOf(e.Name) >= 0)
                    return;
                _RenamedFilesOld.Add(e.OldName);
                _RenamedFiles.Add(e.Name);
            }
        }
        async System.Threading.Tasks.Task CompileTypeScriptCompileAndReload(List<string> ChangedFiles)
        {
            await MainUI.GetInstance().TypeScriptCompile();

            foreach (var filename in ChangedFiles)
            {
                string ChangedPath = PathHelper.ToStandardForm(filename);
                if (ChangedPath.StartsWith("Build"))
                    continue;
                ResourceManager.Instance().TryReloadResource(ChangedPath);

                Entity Root = EditorScene.GetInstance().GetRoot();
                if (Root != null)
                {
                    Queue<Entity> Queue = new Queue<Entity>();
                    Queue.Enqueue(Root);
                    while (Queue.Count != 0)
                    {
                        Entity Head = Queue.Dequeue();
                        if (Head.HasComponent(typeof(Script)))
                        {
                            Script Script = Head.GetComponent(typeof(Script)) as Script;
                            string ScriptPath = Script.Path;
                            ChangedPath = PathHelper.ToStandardForm(ChangedPath);
                            if (string.Compare(ChangedPath, ScriptPath, StringComparison.OrdinalIgnoreCase) == 0)
                            {
                                Script.Path = ScriptPath; // reset
                            }
                        }

                        foreach (Entity Child in Head.Children)
                        {
                            Queue.Enqueue(Child);
                        }
                    }
                }
            }
        }
        public void Update()
        {
            bool bRenamed = _RenamedFiles.Count != 0;
            if (bRenamed)
            {
                Entity Root = EditorScene.GetInstance().GetRoot();
                if (Root != null)
                {
                    Queue<Entity> Queue = new Queue<Entity>();
                    Queue.Enqueue(Root);
                    while (Queue.Count != 0)
                    {
                        Entity Head = Queue.Dequeue();
                        if (Head.HasComponent(typeof(Script)))
                        {
                            Script Script = Head.GetComponent(typeof(Script)) as Script;
                            string ScriptPath = Script.Path;
                            for (int i = 0; i < _RenamedFilesOld.Count; i++)
                            {
                                string ChangedPath = PathHelper.ToStandardForm(_RenamedFilesOld[i]);
                                if (string.Compare(ChangedPath, ScriptPath, StringComparison.OrdinalIgnoreCase) == 0)
                                {
                                    Script.Path = PathHelper.ToStandardForm(_RenamedFiles[i]);
                                }
                            }
                        }

                        foreach (Entity Child in Head.Children)
                        {
                            Queue.Enqueue(Child);
                        }
                    }
                }
            }

            _RenamedFiles.Clear();
            _RenamedFilesOld.Clear();

            var ChangedFiles = _ChangedFiles.Distinct().ToList();
            var ChangedTsFiles = _ChangedTsFiles.Distinct().ToList();

            _ChangedFiles.Clear();
            _ChangedTsFiles.Clear();

            if (ChangedTsFiles.Count() > 0)
            {
                _ = CompileTypeScriptCompileAndReload(ChangedFiles);
            }
        }
    }
}