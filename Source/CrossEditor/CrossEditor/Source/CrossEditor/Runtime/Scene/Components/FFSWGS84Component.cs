using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "FFS/FFSWGS84Component", InspectorType = typeof(Inspector_FFSWGS84Component))]
    public class FFSWGS84Component : Component
    {
        static string[] _NativeNames = { "cross::FFSWGS84ComponentG" };

        public FFSWGS84ComponentG ComponentData = new FFSWGS84ComponentG();
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public FFSWGS84Component()
        { }

        static int _ComponentOrder = 100;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 400;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "ffs model elevetion")]
        public double mElevevation
        {
            get
            {
                return WGS84SystemG.GetElevation(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                WGS84SystemG.SetElevation(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
        [PropertyInfo(PropertyType = "Auto", ToolTips = "ffs Latitude")]
        public Float3 mLatitude
        {
            get
            {
                return WGS84SystemG.GetLatitude(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                WGS84SystemG.SetLatitude(Entity.World.GetNativePointer(), Entity.EntityID, value);
                Entity.SetDirty();
            }
        }
        [PropertyInfo(PropertyType = "Auto", ToolTips = "ffs Longitude")]
        public Float3 mLongitude
        {
            get
            {
                return WGS84SystemG.GetLongitude(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                WGS84SystemG.SetLongitude(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "ffs mHeading")]
        public double mHeading
        {
            get
            {
                return WGS84SystemG.GetHeading(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                WGS84SystemG.SetHeading(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
        [PropertyInfo(PropertyType = "Auto", ToolTips = "ffs mPitch")]
        public double mPitch
        {
            get
            {
                return WGS84SystemG.GetPitch(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                WGS84SystemG.SetPitch(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
        [PropertyInfo(PropertyType = "Auto", ToolTips = "ffs mRoll")]
        public double mRoll
        {
            get
            {
                return WGS84SystemG.GetRoll(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                WGS84SystemG.SetRoll(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "ffs Zfight")]
        public double mZfight
        {
            get
            {
                return WGS84SystemG.GetZFight(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                WGS84SystemG.SetZFight(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
    }
}
