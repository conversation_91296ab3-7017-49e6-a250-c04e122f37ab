using CEngine;
using Newtonsoft.Json;
using System;
using System.Reflection;

namespace CrossEditor
{

    public struct ControllableUnitData
    {
        public ControllableUnitType Type;
        public string Json;
    }

    [ComponentAttribute(DisplayUINames = "ControllableUnitComponent", InspectorType = typeof(Inspector_ControllableUnitComponent))]
    public class ControllableUnitComponent : Component
    {
        static string[] _NativeNames = { "cross::ControllableUnitComponentG" };

        static public readonly string NameSpacePrefix = "CEngine.";


        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public ControllableUnitComponent()
            : base()
        {

        }
        public override void OnComponentAddToEntity()
        {
        }

        public override void SyncDataFromEngine()
        {
            _ControllerType = ControllableUnitSystemG.GetControllableUnitType(Entity.World.GetNativePointer(), Entity.EntityID);
            if (_ControllerType != ControllableUnitType.None)
            {
                string outJson = ControllableUnitSystemG.GetControllableUnitJson(GetWorldPtr(), Entity.EntityID);
                Type type = CrossEngine.GetType(GetControllableTypeName(_ControllerType));
                ControllableUnit = JsonConvert.DeserializeObject(outJson, type) as ControllableUnit;
            }
        }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        private ControllableUnitType _ControllerType = ControllableUnitType.None;
        [PropertyInfo(PropertyType = "Auto", ToolTips = "ControllerType")]
        public ControllableUnitType ControllerType
        {
            get
            {
                _ControllerType = ControllableUnitSystemG.GetControllableUnitType(Entity.World.GetNativePointer(), Entity.EntityID);
                return _ControllerType;
            }
            set
            {
                if (_ControllerType != value)
                {
                    _ControllerType = value;
                    ControllableUnitSystemG.SetControllableUnitType(Entity.World.GetNativePointer(), Entity.EntityID, _ControllerType);
                    ControllableUnit = CreateInstance(_ControllerType);
                }
            }
        }

        public ControllableUnit ControllableUnit;

        [PropertyInfo(bHide = true)]
        public ControllableUnitData ControllableUnitData
        {
            get
            {
                return new ControllableUnitData()
                {
                    Type = ControllerType,
                    Json = JsonConvert.SerializeObject(ControllableUnit)
                };
            }
            set
            {
                if (value.Type == ControllableUnitType.None)
                    return;

                if (ControllerType != value.Type)
                    ControllableUnitSystemG.SetControllableUnitType(Entity.World.GetNativePointer(), Entity.EntityID, value.Type);

                Type type = CrossEngine.GetType(GetControllableTypeName(value.Type));
                ControllableUnit = JsonConvert.DeserializeObject(value.Json, type) as ControllableUnit;
                ControllableUnit.Entity = Entity;
                string json = JsonConvert.SerializeObject(ControllableUnit);
                ControllableUnitSystemG.SetControllableUnitJson(GetWorldPtr(), Entity.EntityID, json);
            }
        }

        static protected string GetControllableTypeName(ControllableUnitType type)
        {
            return NameSpacePrefix + type.ToString();
        }

        static protected ControllableUnitType GetControllableTypeValue(string typeName)
        {
            if (typeName.StartsWith(NameSpacePrefix))
            {
                var name = typeName.Remove(NameSpacePrefix.Length);

                if (Enum.TryParse(name, false, out ControllableUnitType result))
                    return result;
            }
            return ControllableUnitType.None;
        }

        protected ControllableUnit CreateInstance(ControllableUnitType type)
        {
            Type typeInst = CrossEngine.GetType(GetControllableTypeName(type));
            return typeInst != null ? Activator.CreateInstance(typeInst) as ControllableUnit : null;
        }
    }
}
