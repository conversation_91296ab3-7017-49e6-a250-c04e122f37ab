using CEngine;

namespace CrossEditor
{
    class VRViewComponent : Component
    {
        static string[] _NativeNames = { "cross::VRViewComponentG" };
        static int _ComponentOrder = 3;
        static int _GroupOrder = 2;

        public VRViewComponentG ComponentData = new VRViewComponentG();

        public override void SyncDataFromEngine()
        {
            ComponentData.mViewID = VRViewSystemG.GetViewID(Entity.World.GetNativePointer(), Entity.EntityID);
        }


        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "ViewID")]
        public uint ViewID
        {
            get
            {
                return ComponentData.mViewID;
            }
            set
            {
                ComponentData.mViewID = value;
                VRViewSystemG.SetViewID(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData.mViewID);
            }
        }
    }
}
