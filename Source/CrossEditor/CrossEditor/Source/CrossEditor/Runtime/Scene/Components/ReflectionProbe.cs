using CEngine;

namespace CrossEditor
{
    class ReflectionProbeComponent : Component
    {
        static string[] _NativeNames = { "cross::ReflectionProbeComponentG" };

        ReflectionProbeComponentG mReflectionProbe = new ReflectionProbeComponentG();
        EditorBakedReflectionProbePathCallBack _FetchBakedReflectionProbePathCallBack;

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public ReflectionProbeComponent()
        {
            _FetchBakedReflectionProbePathCallBack = (string path) =>
            {
                mReflectionProbe.mBakedReflectionTexturePath = path;
            };
        }

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 8;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void Reset()
        {
            base.Reset();
        }
        public override void SyncDataFromEngine()
        {
            CrossEngineApi.SetBakeRPPathCallback(Entity.World.GetNativePointer(), Entity.EntityID, _FetchBakedReflectionProbePathCallBack);
            CrossEngineApi.GetReflectionProbe(Entity.World.GetNativePointer(), Entity.EntityID, mReflectionProbe);
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "ReflectionProbeStruct")]
        public ReflectionProbeComponentG ReflectionProbe
        {
            get
            {
                CrossEngineApi.SetBakeRPPathCallback(Entity.World.GetNativePointer(), Entity.EntityID, _FetchBakedReflectionProbePathCallBack);
                return mReflectionProbe;
            }
            set
            {
                mReflectionProbe = value;
                CrossEngineApi.SetReflectionProbe(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this component.", bGenerateCode = false)]
        public override bool Enable
        {
            get => ReflectionProbe.mEnable;
            set
            {
                mReflectionProbe.mEnable = value;
                ReflectionProbe = mReflectionProbe;
            }
        }

    }
}