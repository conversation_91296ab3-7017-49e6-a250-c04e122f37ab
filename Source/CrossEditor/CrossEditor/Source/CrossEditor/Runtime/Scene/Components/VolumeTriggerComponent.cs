using CEngine;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class VolumeTriggerComponent : Component
    {
        static string[] _NativeNames = { "cross::VolumeTriggerComponentG" };

        static new List<Type> _AssociateComponents = new List<Type> { typeof(Physics) };

        VolumeTriggerComponentG _VolumeTriggerComponentG = new VolumeTriggerComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public VolumeTriggerComponent() { }

        static int _ComponentOrder = 1;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "Volume Trigger Component")]
        public VolumeTriggerComponentG VolumeTrigger
        {
            get
            {
                VolumeTriggerSystemG.GetVolumeTriggerComponent(Entity.World.GetNativePointer(), Entity.EntityID, _VolumeTriggerComponentG);
                return _VolumeTriggerComponentG;
            }
            set
            {
                _VolumeTriggerComponentG = value;
                VolumeTriggerSystemG.SetVolumeTriggerComponent(Entity.World.GetNativePointer(), Entity.EntityID, _VolumeTriggerComponentG);
            }
        }
    }
}
