using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(NeedRenderProperty = true)]
    public class TerrainComponent : Component
    {
        static string[] _NativeNames = { "cross::TerrainComponentG" };

        string _TerrainPath;
        string _MaterialOverridePath;

        public TerrainComponent()
        {
            _TerrainPath = "";
        }

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        static int _ComponentOrder = 4;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void Reset()
        {
            base.Reset();
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Terrain enable")]
        public override bool Enable
        {
            get
            {
                _Enable = TerrainSystemG.GetTerrainEnable(Entity.World.GetNativePointer(), Entity.EntityID);
                return _Enable;
            }
            set
            {
                _Enable = value;
                TerrainSystemG.SetTerrainEnable(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Terrain path", FileTypeDescriptor = "Terrain Assets#nda", ObjectClassID1 = ClassIDType.CLASS_TerrainResource)]
        public string TerrainPath
        {
            get
            {
                string Path = TerrainSystemG.GetTerrainPath(Entity.World.GetNativePointer(), Entity.EntityID);
                if (Path != "")
                {
                    return Path;
                }
                return _TerrainPath;
            }
            set
            {
                _TerrainPath = value;
                TerrainSystemG.SetTerrainPath(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Material override path", FileTypeDescriptor = "Material Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Material)]
        public string MaterialOverridePath
        {
            get
            {
                string Path = TerrainSystemG.GetMaterialOverridePath(Entity.World.GetNativePointer(), Entity.EntityID);
                if (Path != "")
                {
                    return Path;
                }
                return _MaterialOverridePath;
            }
            set
            {
                _MaterialOverridePath = value;
                TerrainSystemG.SetMaterialOverridePath(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        public void TerrainBVHGenerate()
        {
            TerrainSystemG.GenerateTerrainTileAABBTree(Entity.World.GetNativePointer(), Entity.EntityID, 16);
        }
    }
}
