using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{

    public class SubModelProperty
    {
        bool _Visible;
        string _MaterialPath;
        public bool _NeedSetLODMaterials = false;
        public bool _NeedGetLODMaterials = true;

        public SubModelProperty()
        {
            Visible = true;
            MaterialPath = CrossEngineApi.GetSettingManager().GetRenderPipelineSettingForEditor().DefaultMaterial;
        }

        public SubModelProperty(SubModelProperty copyProperty)
        {
            Visible = copyProperty.Visible;
            MaterialPath = copyProperty.MaterialPath;
        }

        public SubModelProperty(string InMaterialPath)
        {
            Visible = true;
            MaterialPath = InMaterialPath;
        }

        public bool IsMaterialValid()
        {
            return _MaterialPath.Length > 0;
        }

        public static string MaterialDefault()
        {
            return CrossEngineApi.GetSettingManager().GetRenderPipelineSettingForEditor().DefaultMaterial;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Is SubModel Visible", DefaultValue = true)]
        public bool Visible
        {
            get => _Visible; set => _Visible = value;
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Material to use when rendering.",
            FileTypeDescriptor = "Material Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Material, ObjectClassID2 = ClassIDType.CLASS_Fx)]
        public string MaterialPath
        {
            get => _MaterialPath;
            set
            {
                _MaterialPath = value;
                _NeedGetLODMaterials = true;
            }
        }
    }
    public class LODProperty
    {
        List<SubModelProperty> _SubModels = new List<SubModelProperty>();

        public LODProperty()
        {
             //_SubModels.Add(new SubModelProperty());
        }
        public LODProperty(LODProperty copyProperty)
        {
            _SubModels = copyProperty.SubModels;
        }
        public LODProperty(string InMaterialPath)
        {
            _SubModels.Add(new SubModelProperty(InMaterialPath));
        }
        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "SubModelProperty Info of this LOD.", bFixedItems = true)]
        public List<SubModelProperty> SubModels
        {
            get => _SubModels;
            set
            {
                _SubModels = value;
            }
        }
    }
    public class Model
    {
        const string _StaticMeshDefault = "EngineResource/Model/Cube.nda";
        const string _SkeltMeshDefault = "EngineResource/Model/SimpleSKMesh.nda";

        bool _Visible;
        string _ModelPath;
        bool _ReceiveDecals = true;
        bool _UseLod0Bbox = false;
        public bool _ModelAssetDirty = true;
        public bool _NeedLoadChannels = true;
        List<LODProperty> _SubModelProperties = new List<LODProperty>();

        public bool _bHasBlendShape = false;
        public Dictionary<string, float> _BlendShapeChannels = new Dictionary<string, float>();

        public Model()
        {
            Visible = true;
            _ReceiveDecals = true;
            _UseLod0Bbox = false;
            ModelPath = "";
            List<LODProperty> SubModelPropertiesDefault = new List<LODProperty>();
            SubModelPropertiesDefault.Add(new LODProperty());
            LODProperties = SubModelPropertiesDefault;
        }
        public Model(Model copyModel)
        {
            Visible = copyModel.Visible;
            _ReceiveDecals = copyModel._ReceiveDecals;
            _UseLod0Bbox = copyModel._UseLod0Bbox;
            ModelPath = copyModel.ModelPath;

            List<LODProperty> SubModelPropertiesDefault = new List<LODProperty>();
            foreach (var subP in copyModel.LODProperties)
            {
                SubModelPropertiesDefault.Add(new LODProperty(subP));
            }
            LODProperties = SubModelPropertiesDefault;
        }

        public Model(string InModelPath)
        {
            Visible = true;
            _ReceiveDecals = true;
            _UseLod0Bbox = false;
            ModelPath = InModelPath;
            List<LODProperty> SubModelPropertiesDefault = new List<LODProperty>();
            SubModelPropertiesDefault.Add(new LODProperty());
            LODProperties = SubModelPropertiesDefault;
        }

        public Model(string InModelPath, string MatPath)
        {
            Visible = true;
            _ReceiveDecals = true;
            _UseLod0Bbox = false;
            ModelPath = InModelPath;
            List<LODProperty> SubModelPropertiesDefault = new List<LODProperty>();
            SubModelPropertiesDefault.Add(new LODProperty(MatPath));
            LODProperties = SubModelPropertiesDefault;
        }

        public bool IsModelValid()
        {
            return ModelPath.Length > 0;
        }

        public static string StaticMeshDefault()
        {
            return _StaticMeshDefault;
        }

        public static string SkeltMeshDefault()
        {
            return _SkeltMeshDefault;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Is Model Visible", DefaultValue = true)]
        public bool Visible
        {
            get => _Visible; set => _Visible = value;
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Mesh filename that represent the shape of the mesh.",
            FileTypeDescriptor = "Mesh Assets#nda", ObjectClassID1 = ClassIDType.CLASS_MeshAssetDataResource, DefaultValue = _StaticMeshDefault)]
        public string ModelPath
        {
            get => _ModelPath;
            set
            {
                _ModelPath = value;
                _ModelAssetDirty = true;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether Model Receives Decals", DefaultValue = true)]
        public bool ReceiveDecals
        {
            get => _ReceiveDecals; set => _ReceiveDecals = value;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether Model Use Lod0 Bounding Box", DefaultValue = false)]
        public bool UseLod0Bbox
        {
            get => _UseLod0Bbox; set => _UseLod0Bbox = value;
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "SubModelProperty Info of this model.", bFixedItems = true)]
        public List<LODProperty> LODProperties
        {
            get => _SubModelProperties; set => _SubModelProperties = value;
        }

        [PropertyInfo(PropertyType = "Dictionary", ChildPropertyType = "FloatWithTrack", ToolTips = "Blend shape.", bFixedItems = true)]
        public Dictionary<string, float> BlendShapeChannels
        {
            get => _BlendShapeChannels; set => _BlendShapeChannels = value;
        }
    }

    [ComponentAttribute(DisplayUINames = "Mesh/Model Component", NeedRenderProperty = true)]
    public class ModelComponent : Component
    {
        static string[] _NativeNames = { "cross::ModelComponentG", "cross::AABBComponentG" };

        List<Model> _ModelList = new List<Model>();

        bool _IsStreamable = false;

        protected EntityDistanceCulling mEntityDistanceCulling = new EntityDistanceCulling();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public ModelComponent()
        {
        }
        static int _ComponentOrder = 2;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            base.Reset();
            List<Model> ModelsDefault = new List<Model>();
            ModelsDefault.Add(new Model());
            Models = ModelsDefault;
        }

        public override void SyncDataFromEngine()
        {
            SetModelList(GetAllModelsFromRuntime());
        }

        public List<Model> GetModels()
        {
            return _ModelList;
        }

        public void SetAllModelsAccordingToList()
        {
            int ModelIndex = 0;
            uint CurrentModelCount = 0;
            CurrentModelCount = ModelSystemG.GetModelCount(Entity.World.GetNativePointer(), Entity.EntityID);
            // set model info for runtime when model already exists
            for (; ModelIndex < CurrentModelCount; ModelIndex++)
            {
                // when model path is empty, set to default static model path
                if (!_ModelList[ModelIndex].IsModelValid())
                {
                    // if skeletal model
                    if (ModelSystemG.IsSkeletalModel(Entity.World.GetNativePointer(), Entity.EntityID))
                    {
                        _ModelList[ModelIndex].ModelPath = Model.SkeltMeshDefault();
                    }
                    // if static model
                    else
                    {
                        _ModelList[ModelIndex].ModelPath = Model.StaticMeshDefault();
                    }
                }
                ModelSystemG.SetModelAssetPath(Entity.World.GetNativePointer(), Entity.EntityID, _ModelList[ModelIndex].ModelPath, (uint)ModelIndex);
                ModelSystemG.SetModelVisibility(Entity.World.GetNativePointer(), Entity.EntityID, _ModelList[ModelIndex].Visible, (uint)ModelIndex, true);
                ModelSystemG.SetModelReceiveDecals(Entity.World.GetNativePointer(), Entity.EntityID, _ModelList[ModelIndex].ReceiveDecals, (uint)ModelIndex);
                ModelSystemG.SetModelUseLod0Bbox(Entity.World.GetNativePointer(), Entity.EntityID, _ModelList[ModelIndex].UseLod0Bbox, (uint)ModelIndex);
                SetSubModelPropertiesForModel(ModelIndex);
                SetBlendShapeForModel(ModelIndex);
                _ModelList[ModelIndex]._NeedLoadChannels = false;
                _ModelList[ModelIndex]._ModelAssetDirty = false;
            }

            // set model info for runtime when model is newly added
            for (; ModelIndex < _ModelList.Count; ModelIndex++)
            {
                if (!_ModelList[ModelIndex].IsModelValid())
                {
                    // if skeletal model
                    if (ModelSystemG.IsSkeletalModel(Entity.World.GetNativePointer(), Entity.EntityID))
                    {
                        _ModelList[ModelIndex].ModelPath = Model.SkeltMeshDefault();
                    }
                    // if static model
                    else
                    {
                        _ModelList[ModelIndex].ModelPath = Model.StaticMeshDefault();
                    }
                }
                ModelSystemG.AddModelByAssetPath(Entity.World.GetNativePointer(), Entity.EntityID, _ModelList[ModelIndex].ModelPath);
                ModelSystemG.SetModelVisibility(Entity.World.GetNativePointer(), Entity.EntityID, _ModelList[ModelIndex].Visible, (uint)ModelIndex, true);
                ModelSystemG.SetModelReceiveDecals(Entity.World.GetNativePointer(), Entity.EntityID, _ModelList[ModelIndex].ReceiveDecals, (uint)ModelIndex);
                ModelSystemG.SetModelUseLod0Bbox(Entity.World.GetNativePointer(), Entity.EntityID, _ModelList[ModelIndex].UseLod0Bbox, (uint)ModelIndex);

                SetSubModelPropertiesForModel(ModelIndex);
                SetBlendShapeForModel(ModelIndex);
                _ModelList[ModelIndex]._NeedLoadChannels = false;
                _ModelList[ModelIndex]._ModelAssetDirty = false;
            }
        }

        void SetSubModelPropertiesForModel(int modelIndex)
        {

            uint lodcount = ModelSystemG.GetLODCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)modelIndex);
            for (uint i = 0; i < lodcount; i++)
            {
                int SubModelPropertyIndex = 0;
                uint SubMeshCount = ModelSystemG.GetSubModelCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)modelIndex, i);
                List<SubModelProperty> CurSubModelProperties = new List<SubModelProperty>();
                while (_ModelList[modelIndex].LODProperties.Count < lodcount)
                {
                    _ModelList[modelIndex].LODProperties.Add(new LODProperty());
                }
                // set sub mesh info when exist
                for (; SubModelPropertyIndex < SubMeshCount && SubModelPropertyIndex < _ModelList[modelIndex].LODProperties[(int)i].SubModels.Count; ++SubModelPropertyIndex)
                {
                    CurSubModelProperties.Add(_ModelList[modelIndex].LODProperties[(int)i].SubModels[SubModelPropertyIndex]);
                    ModelSystemG.SetModelLodMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID,
                        CurSubModelProperties[SubModelPropertyIndex].MaterialPath, (uint)SubModelPropertyIndex, i, (uint)modelIndex);
                    ModelSystemG.SetSubModelVisibility(Entity.World.GetNativePointer(), Entity.EntityID,
                        CurSubModelProperties[SubModelPropertyIndex].Visible, SubModelPropertyIndex, i, (uint)modelIndex);
                }

                // set sub mesh info new come
                // this will happen when setting new model and new model's sub mesh number is more than old one
                for (; SubModelPropertyIndex < SubMeshCount; ++SubModelPropertyIndex)
                {
                    CurSubModelProperties.Add(new SubModelProperty());
                    ModelSystemG.SetModelLodMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID,
                        CurSubModelProperties[SubModelPropertyIndex].MaterialPath, (uint)SubModelPropertyIndex, i, (uint)modelIndex);
                    ModelSystemG.SetSubModelVisibility(Entity.World.GetNativePointer(), Entity.EntityID,
                        CurSubModelProperties[SubModelPropertyIndex].Visible, SubModelPropertyIndex, i,(uint)modelIndex);
                }

                for (int SubModelIndex = 0; SubModelIndex < SubMeshCount; ++SubModelIndex)
                {
                    if (!_ModelList[modelIndex]._ModelAssetDirty && CurSubModelProperties[SubModelIndex]._NeedSetLODMaterials)
                    {
                        ModelSystemG.SetModelLodMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID,
                                    CurSubModelProperties[SubModelPropertyIndex].MaterialPath, (uint)SubModelPropertyIndex, i, (uint)modelIndex);
                        CurSubModelProperties[SubModelIndex]._NeedSetLODMaterials = false;
                    }
                    else if (_ModelList[modelIndex]._ModelAssetDirty || CurSubModelProperties[SubModelIndex]._NeedGetLODMaterials)
                    {
                        CurSubModelProperties[SubModelIndex]._NeedGetLODMaterials = false;
                    }
                }
                _ModelList[modelIndex].LODProperties[(int)i].SubModels = CurSubModelProperties;
            }
            // refresh sub mesh list

        }

        void SetBlendShapeForModel(int modelIndex)
        {
            Model Model = _ModelList[modelIndex];
            // Set or refresh blend shape channels
            if (Model._NeedLoadChannels)
            {
                // Set
                bool bHasBlendShape = ModelSystemG.IsModelHasBlendShape(Entity.World.GetNativePointer(), Entity.EntityID, (uint)modelIndex);
                Model._bHasBlendShape = bHasBlendShape;
                Model._BlendShapeChannels.Clear();

                if (bHasBlendShape)
                {
                    uint ChannelCount = ModelSystemG.GetModelBlendShapeChannelCount(Entity.World.GetNativePointer(), Entity.EntityID,
                        (uint)modelIndex);

                    // Write names into single string and record name length
                    int[] NameLength = new int[ChannelCount];
                    vector_string VS = new vector_string();
                    ModelSystemG.GetModelBlendShapeChannelNames(Entity.World.GetNativePointer(), Entity.EntityID, (uint)modelIndex, VS);
                    // Split names by name length
                    int StartIndex = 0;
                    for (int i = 0; i < ChannelCount; ++i)
                    {
                        Model.BlendShapeChannels.Add(VS[i], 0.0f);
                        StartIndex += NameLength[i];
                    }
                }
            }
            else
            {
                // Refresh
                if (Model._bHasBlendShape)
                {
                    foreach (string Key in Model._BlendShapeChannels.Keys)
                    {
                        HashString KeyUni = new HashString(Key);
                        ModelSystemG.SetModelBlendShapeChannelWeight(Entity.World.GetNativePointer(), Entity.EntityID,
                            (uint)modelIndex, KeyUni, Model._BlendShapeChannels[Key], 0);
                    }
                }
            }
        }

        public void SetDefaultMaterial(int modelIdx)
        {
            uint lod = ModelSystemG.GetLODCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)modelIdx);
            for (uint i = 0; i < lod; i++)
            {
                uint SubMeshCount = ModelSystemG.GetSubModelCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)modelIdx, i);
                for (int j = 0; j < SubMeshCount; j++)
                {
                    ModelSystemG.SetModelLodMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID, SubModelProperty.MaterialDefault(), (uint)j, i, (uint)modelIdx);
                }
            }
        }

        public void SetDefaultMaterial()
        {
            uint CurrentModelCount = 0;
            CurrentModelCount = ModelSystemG.GetModelCount(Entity.World.GetNativePointer(), Entity.EntityID);
            for (int modelIdx = 0; modelIdx < CurrentModelCount; modelIdx++)
            {
                if (ModelSystemG.IsSkeletalModel(Entity.World.GetNativePointer(), Entity.EntityID))
                {
                    ModelSystemG.SetModelLodMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID,
    "EngineResource/Material/SkeletonDefaultMaterial.nda", 0, 0, (uint)modelIdx);
                }
                else
                {
                    SetDefaultMaterial(modelIdx);
                }
            }
        }

        public void SetUVUnwrapMaterial(int index = 1)
        {
            uint CurrentModelCount = 0;
            CurrentModelCount = ModelSystemG.GetModelCount(Entity.World.GetNativePointer(), Entity.EntityID);
            for (int modelIdx = 0; modelIdx < CurrentModelCount; modelIdx++)
            {
                uint lodcount = ModelSystemG.GetLODCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)modelIdx);
                for (uint i = 0; i < lodcount; i++)
                {
                    uint SubMeshCount = ModelSystemG.GetSubModelCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)modelIdx, i);
                    for (int j = 0; j < SubMeshCount; j++)
                    {
                        ModelSystemG.SetModelLodMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID,
                            "EngineResource/Material/UVUnwrapMaterial" + index + ".nda", (uint)j, i, (uint)modelIdx);
                        //"Contents/Test/NewMaterial.nda", i, (uint)modelIdx);
                    }
                }
            }
        }

        List<Model> GetAllModelsFromRuntime()
        {
            uint ModelCount = 0;
            ModelCount = ModelSystemG.GetModelCount(Entity.World.GetNativePointer(), Entity.EntityID);
            List<Model> ModelProperties = new List<Model>();
            for (int ModelIdx = 0; ModelIdx < ModelCount; ModelIdx++)
            {
                // model path and visibility
                string ModelPath;
                ModelSystemG.GetModelAssetPath(Entity.World.GetNativePointer(), Entity.EntityID, (uint)ModelIdx, out ModelPath);
                bool ModelVisible = ModelSystemG.IsModelVisible(Entity.World.GetNativePointer(), Entity.EntityID, (uint)ModelIdx);
                bool ModelReceiveDecals = ModelSystemG.GetModelReceiveDecals(Entity.World.GetNativePointer(), Entity.EntityID, (uint)ModelIdx);
                bool ModelUseLod0Bbox = ModelSystemG.GetModelUseLod0Bbox(Entity.World.GetNativePointer(), Entity.EntityID, (uint)ModelIdx);

                ModelProperties.Add(new Model(ModelPath.ToString()));
                ModelProperties[ModelProperties.Count - 1].Visible = ModelVisible;
                ModelProperties[ModelProperties.Count - 1].ReceiveDecals = ModelReceiveDecals;
                ModelProperties[ModelProperties.Count - 1].UseLod0Bbox = ModelUseLod0Bbox;

                // sub mesh material path and visibility
                uint modellodcount = ModelSystemG.GetLODCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)ModelIdx);
                List<LODProperty> SubModelProperties = new List<LODProperty>();
                for (uint lodindex = 0; lodindex < modellodcount; lodindex++)
                {
                    SubModelProperties.Add(new LODProperty());
                    uint SubMeshCount = ModelSystemG.GetSubModelCount(Entity.World.GetNativePointer(), Entity.EntityID, (uint)ModelIdx, lodindex);
                    for (int SubModelPropertyIndex = 0; SubModelPropertyIndex < SubMeshCount; SubModelPropertyIndex++)
                    {
                        string MaterialPath;
                        MaterialPath = ModelSystemG.GetModelMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID, (uint)ModelIdx, (uint)SubModelPropertyIndex, lodindex);
                        if (MaterialPath.ToString().Length == 0)
                        {
                            SubModelProperties[(int)lodindex].SubModels.Add(new SubModelProperty());
                        }
                        else
                        {
                            SubModelProperties[(int)lodindex].SubModels.Add(new SubModelProperty(MaterialPath.ToString()));
                        }

                        bool SubMeshVisible = ModelSystemG.IsSubModelVisible(Entity.World.GetNativePointer(), Entity.EntityID, SubModelPropertyIndex,(uint)ModelIdx, lodindex);

                        SubModelProperties[(int)lodindex].SubModels[SubModelPropertyIndex].MaterialPath = MaterialPath;
                        SubModelProperties[(int)lodindex].SubModels[SubModelPropertyIndex].Visible = SubMeshVisible;
                    }
                    ModelProperties[ModelIdx].LODProperties = SubModelProperties;
                }
            }

            return ModelProperties;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this mesh renderer.")]
        public override bool Enable
        {
            get { return _Enable; }
            set
            {
                _Enable = value;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this mesh renderer.")]
        public bool GPUSkin
        {
            get
            {
                bool enable = false;
                enable = ModelSystemG.GetSkeltModelGPUSkin(Entity.World.GetNativePointer(), Entity.EntityID);
                return enable;
            }
            set
            {
                ModelSystemG.SetSkeltModelGPUSkin(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable to intersect.")]
        public bool Intersection
        {
            get
            {
                return ModelSystemG.GetIntersection(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                ModelSystemG.SetIntersection(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Center of models", bReadOnly = true, bAdvanced = true)]
        public Float3 Center
        {
            get
            {
                return CrossEngineApi.GetModelCenter(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {

            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Extents of models", bReadOnly = true, bAdvanced = true)]
        public Float3 Extents
        {
            get
            {
                return CrossEngineApi.GetModelExtents(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {

            }
        }

        void SetModelList(List<Model> models)
        {
            _ModelList = models;
            if (_ModelList.Count == 0)
            {
                _ModelList.Add(new Model());
            }

            // remove model for runtime
            uint CurrentModelCount = 0;
            CurrentModelCount = ModelSystemG.GetModelCount(Entity.World.GetNativePointer(), Entity.EntityID);
            if (_ModelList.Count < CurrentModelCount)
            {
                // should remove from back to front 
                for (uint RemoveIdx = CurrentModelCount - 1; RemoveIdx >= _ModelList.Count; --RemoveIdx)
                {
                    ModelSystemG.RemoveModel(Entity.World.GetNativePointer(), Entity.EntityID, RemoveIdx);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Mark Model as Streamable for Mesh Streaming")]
        public bool IsStreamable
        {
            get
            {
                _IsStreamable = ModelSystemG.IsModelAssetStreamable(Entity.World.GetNativePointer(), Entity.EntityID, 0);
                return _IsStreamable;
            }
            set
            {
                _IsStreamable = value;
                ModelSystemG.SetModelAssetStreamable(Entity.World.GetNativePointer(), Entity.EntityID, 0, _IsStreamable);
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Models that this mesh renderer uses.")]
        public List<Model> Models
        {
            get
            {
                return _ModelList;
            }
            set
            {
                SetModelList(value);

                // set model info for runtime
                SetAllModelsAccordingToList();
                Entity.SetDirty();
            }
        }


        [PropertyInfo(PropertyType = "Struct", ToolTips = "Entity distance culling")]
        public EntityDistanceCulling DistanceCulling
        {
            get
            {
                mEntityDistanceCulling = ModelSystemG.GetModelEnityDistanceCulling(Entity.World.GetNativePointer(), Entity.EntityID);
                return mEntityDistanceCulling;
            }
            set
            {
                mEntityDistanceCulling = value;
                ModelSystemG.SetModelEnityDistanceCulling(Entity.World.GetNativePointer(), Entity.EntityID, mEntityDistanceCulling);
            }
        }
    }
}
