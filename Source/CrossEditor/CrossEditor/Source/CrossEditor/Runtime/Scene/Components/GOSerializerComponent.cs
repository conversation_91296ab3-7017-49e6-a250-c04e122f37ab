using Clicegf;
using System;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Game Object Specifier", InspectorType = typeof(Inspector_GameObject))]
    class GOSerializerComponent : Component
    {
        static string[] _NativeNames = { "cross::GOSerializerComponentG" };
        private GameObject mGO;
        private string mNativeClassName;
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        public override int ComponentOrder()
        {
            return Int32.MaxValue;
        }
        public override int GroupOrder()
        {
            return Int32.MaxValue;
        }


        public GOSerializerComponent()
        {
            mGO = new GameObject();
        }

        public GameObject GameObject
        {
            get
            {
                return mGO;
            }
            set
            {
            }
        }
        public override void SyncDataFromEngine()
        {
            mGO.mEntity = Entity;
            mGO.RefreshGameObjectComponents();
            mNativeClassName = mGO.GetRuntimeGameObject() == null ? "cegf::GameObject" : mGO.GetRuntimeGameObject().GetCxxTypeName();
        }

        public override void ApplyPropertiesTo(ComponentBase otherComp)
        {
            base.ApplyPropertiesTo(otherComp);
            var otherGOComp = otherComp as GOSerializerComponent;
            /* 1. re-build an object on the entity
             */
            otherGOComp.RuntimeTransferEntityToGameObject(otherGOComp.Entity, mNativeClassName);

            GOContext GoContext = new GOContext(Entity.World._WorldInterface);
            GoContext.DuplicateGameObject(Entity.EntityID, otherGOComp.Entity.EntityID);

            // 2. SyncDataFromEngine
            otherGOComp.SyncDataFromEngine();

            // 3. update the pointer in the ecs component
            var RuntimeGO = otherGOComp.mGO.GetRuntimeGameObject();
            RuntimeGO.SyncGOSerializer();
        }


        public override void RuntimeReapplyProperties()
        {
            /*]1. re-build an object on the entity
             */
            RuntimeTransferEntityToGameObject(Entity, mNativeClassName);

            // 2. reset entity
            mGO.mEntity = Entity;

            // 3. update the pointer in the ecs component
            var RuntimeGO = mGO.GetRuntimeGameObject();
            RuntimeGO.SyncGOSerializer();

            // 4.rebuild all components
            mGO.RuntimeAddComponentsAndReapplyProperties();
        }

        /*
         * Transfer an entity to a game object with className.
         * Note that this function will not create new GameComponent.
         * For example, if the className is "GameObject", no game component will be added to the object, event if the entity has modelComponent or physics comp
         */
        private void RuntimeTransferEntityToGameObject(Entity entity, string ClassName)
        {
            GOContext GoContext = new GOContext(entity.World._WorldInterface);
            GOHandle handle = new GOHandle();

            // do create task
            GoContext.CreateGameObject(ClassName, new Clicross.Double3(), entity.EntityID, handle);
        }
        //public List<GameComponents>
    }
}