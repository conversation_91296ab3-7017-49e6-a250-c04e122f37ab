using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Animation/SkeltSocket")]
    public class SkeltSocket : Component
    {
        Float3 _Translation = new Float3(0, 0, 0);
        Quaternion _Rotation = new Quaternion(0, 0, 0, 1);
        Float3 _Scale = new Float3(1, 1, 1);

        string _BoneName = "";

        static string[] _NativeNames = { "cross::SkeltSocketComponentG" };

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public SkeltSocket()
        { }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            _Translation = new Float3(0, 0, 0);
            _Rotation = new Quaternion(0, 0, 0, 1);
            _Scale = new Float3(1, 1, 1);
            base.Reset();
        }
        public override void SyncDataFromEngine()
        {
            _BoneName = SkeltSocketSystemG.GetBoneName(Entity.World.GetNativePointer(), Entity.EntityID);
            _Translation = SkeltSocketSystemG.GetReltvTranslation(Entity.World.GetNativePointer(), Entity.EntityID);
            _Rotation = SkeltSocketSystemG.GetReltvRotation(Entity.World.GetNativePointer(), Entity.EntityID);
            _Scale = SkeltSocketSystemG.GetReltvScale(Entity.World.GetNativePointer(), Entity.EntityID);
            Reattch();
        }
        [PropertyInfo(PropertyType = "Auto", ToolTips = "BoneName")]
        public string BoneName
        {
            get { return _BoneName; }
            set
            {
                if (_BoneName != null && !value.Equals(_BoneName))
                {
                    _BoneName = value;
                    Reattch();
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The translation of the socket.")]
        public Float3 Translation
        {
            get { return _Translation; }
            set { _Translation = value; Reattch(); }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The rotation of the socket.")]
        public Quaternion Rotation
        {
            get
            {
                return _Rotation;
            }
            set { _Rotation = value; Reattch(); }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The scale of the socket.")]
        public Float3 Scale
        {
            get { return _Scale; }
            set { _Scale = value; Reattch(); }
        }

        protected void Reattch()
        {
            SkeltSocketSystemG.SetSocket(
                Entity.World.GetNativePointer(), Entity.EntityID,
                _BoneName, _Translation, _Rotation, _Scale
            );
        }
    }
}
