using CEngine;

namespace CrossEditor
{
    class VehicleWheelComponent : Component
    {
        static string[] _NativeNames = { "cross::VehicleWheelComponentG" };

        protected VehicleWheelComponentG mVehicleWheelComponent = new VehicleWheelComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public VehicleWheelComponent()
        { }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true)]
        public VehicleWheelComponentG VehicleWheel
        {
            get
            {
                VehicleWheelSystemG.GetVehicleWheelComponent(GetWorldPtr(), Entity.EntityID, mVehicleWheelComponent);
                return mVehicleWheelComponent;
            }
            set
            {
                VehicleWheelSystemG.SetVehicleWheelComponent(GetWorldPtr(), Entity.EntityID, value);
            }
        }
    }
}
