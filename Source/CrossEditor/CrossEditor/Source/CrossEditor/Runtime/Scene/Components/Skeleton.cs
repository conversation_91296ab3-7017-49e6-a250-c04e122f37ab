using CEngine;
using EditorUI;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Animation/SkeletonComponent")]
    public class Skeleton : Component
    {
        string _SkeletonPath;
        string _SkeletonPhysicsPath;

        static string[] _NativeNames = { "cross::SkeletonComponentG" };

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public Skeleton()
        {
            _SkeletonPath = "";
            _SkeletonPhysicsPath = "";
        }
        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            Clicross.AnimationEditorUtil.Skeleton_ResetRuntimeSkeleton(Entity.World._WorldInterface, Entity.EntityID);
            Clicross.AnimationEditorUtil.Skeleton_ResetSkeletonPhysics(Entity.World._WorldInterface, Entity.EntityID);
        }

        public override void Initialize(string initialization)
        {
            base.Initialize(initialization);
            SkeletonPath = initialization;
        }

        public static new bool OnPathDroped(Inspector_Entity entity_inspector, string path, string Extension)
        {
            if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
            {
                AssetType AssetType = AssetImporterManager.Instance().GetAssetType(path);

                if (AssetType == AssetType.Default && Resource.GetResourceTypeStatic(path) == ClassIDType.CLASS_SkeletonResource)
                {
                    entity_inspector.AddComponentClicked<Skeleton>(path);
                    return true;
                }
            }
            return false;
        }

        public override void SyncDataFromEngine()
        {
            _SkeletonPath = SkeletonSystemG.GetSkeletonAssetPath(Entity.World.GetNativePointer(), Entity.EntityID);
            _SkeletonPhysicsPath = SkeletonSystemG.GetSkeletonPhysics(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable/Disable Skeleton Component.")]
        public override bool Enable
        {
            get { return _Enable; }
            set
            {
                _Enable = value;
                Clicross.AnimationEditorUtil.Skeleton_SetSkeletonCompEnable(Entity.World._WorldInterface, Entity.EntityID, _Enable);
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Skeleton",
            FileTypeDescriptor = "The skeleton resource#nda", ObjectClassID1 = ClassIDType.CLASS_SkeletonResource)]
        public string SkeletonPath
        {
            get
            {
                return _SkeletonPath;
            }
            set
            {
                if (value.Length > 0)
                {
                    _SkeletonPath = value;
                    SkeletonSystemG.SetSkeleton(Entity.World.GetNativePointer(), Entity.EntityID, _SkeletonPath);
                }
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "SkeletonPhysics",
            FileTypeDescriptor = "The SkeletonPhysics resource#nda", ObjectClassID1 = ClassIDType.CLASS_SkeletonPhysicsResource)]
        public string SkeletonPhysicsPath
        {
            get
            {
                return _SkeletonPhysicsPath;
            }
            set
            {
                if (value.Length > 0)
                {
                    _SkeletonPhysicsPath = value;
                    SkeletonSystemG.SetSkeletonPhysics(Entity.World.GetNativePointer(), Entity.EntityID, _SkeletonPhysicsPath);
                }
            }
        }
    }
}
