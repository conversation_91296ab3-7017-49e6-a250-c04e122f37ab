using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Decal Component", NeedRenderProperty = true)]
    class DecalComponent : Component
    {

        static string[] _NativeNames = { "cross::DecalComponentG", "cross::AABBComponentG" };

        DecalConfig _Config;
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public DecalComponent()
        {
            _Config = new DecalConfig();
        }

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 51;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            base.Reset();
            Config = new DecalConfig();
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "Decal configuration")]
        public DecalConfig Config
        {
            get
            {
                return DecalSystemG.GetDecalConfig(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _Config = value;
                DecalSystemG.SetDecalConfig(Entity.World.GetNativePointer(), Entity.EntityID, _Config);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this decal.")]
        public override bool Enable
        {
            get
            {
                return _Enable;
            }
            set
            {
                _Enable = value;
                DecalSystemG.SetDecalEnable(Entity.World.GetNativePointer(), Entity.EntityID, _Enable);
            }
        }
        public override void OnComponentAddToEntity()
        {
            base.OnComponentAddToEntity();
            Config = _Config;
            DecalSystemG.OnComponentAddToEntity(Entity.World.GetNativePointer(), Entity.EntityID);
        }
    }
}