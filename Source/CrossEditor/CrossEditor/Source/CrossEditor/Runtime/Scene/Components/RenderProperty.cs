using CEngine;

namespace CrossEditor
{
    class RenderProperty : Component
    {
        static string[] _NativeNames = { "cross::RenderPropertyComponentG" };

        RenderEffectTag _RenderEffect = RenderEffectTag.CastShadow | RenderEffectTag.IsStatic;
        CullingProperty _CullingProperty;
        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public RenderProperty()
        {
        }

        public override void Reset()
        {
            base.Reset();
            CullingProperty = CullingProperty.CULLING_PROPERTY_CULLABLE;
        }

        public override void SyncDataFromEngine()
        {
            _RenderEffect = RenderPropertySystemG.GetRenderEffectTag(Entity.World.GetNativePointer(), Entity.EntityID);
            _CullingProperty = RenderPropertySystemG.GetCullingProperty(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        [PropertyInfo(PropertyType = "Layer", ToolTips = "Visibility on different kinds of cameras.")]
        public uint LayerIndex
        {
            get
            {
                return RenderPropertySystemG.GetLayerIndex(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                RenderPropertySystemG.SetLayerIndex(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Cast shadow or not")]
        public bool CastShadow
        {
            get { return _RenderEffect.HasFlag(RenderEffectTag.CastShadow); }
            set
            {
                if (value)
                {
                    _RenderEffect |= RenderEffectTag.CastShadow;
                    RenderPropertySystemG.AddRuntimeRenderEffect(Entity.World.GetNativePointer(), Entity.EntityID, RenderEffectTag.CastShadow);
                }
                else
                {
                    _RenderEffect &= (~RenderEffectTag.CastShadow);
                    RenderPropertySystemG.RemoveRuntimeRenderEffect(Entity.World.GetNativePointer(), Entity.EntityID, RenderEffectTag.CastShadow);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "(For local light shadow) Is static or not")]
        public bool IsStatic
        {
            get { return _RenderEffect.HasFlag(RenderEffectTag.IsStatic); }
            set
            {
                if (value)
                {
                    _RenderEffect |= RenderEffectTag.IsStatic;
                    RenderPropertySystemG.AddRuntimeRenderEffect(Entity.World.GetNativePointer(), Entity.EntityID, RenderEffectTag.IsStatic);
                }
                else
                {
                    _RenderEffect &= (~RenderEffectTag.IsStatic);
                    RenderPropertySystemG.RemoveRuntimeRenderEffect(Entity.World.GetNativePointer(), Entity.EntityID, RenderEffectTag.IsStatic);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Need Voxelized or Not")]
        public bool NeedVoxelized
        {
            get
            {
                return RenderPropertySystemG.IsNeedVoxelized(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                RenderPropertySystemG.SetNeedVoxelized(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        public CullingProperty CullingProperty
        {
            get { return _CullingProperty; }
            set
            {
                _CullingProperty = value;
                RenderPropertySystemG.SetCullingProperty(Entity.World.GetNativePointer(), Entity.EntityID, _CullingProperty);
            }
        }
    }
}