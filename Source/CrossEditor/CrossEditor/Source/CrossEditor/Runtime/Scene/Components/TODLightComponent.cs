using CEngine;

namespace CrossEditor
{
    public class TODLightComponent : Component
    {

        static string[] _NativeNames = { "cross::TODLightComponentG" };

        static string _NativeSystemName = "cross::TODLightSystemG";
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        public override string NativeSystemName()
        {
            return _NativeSystemName;
        }

        public TODLightComponent()
        {

        }
        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 57;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            base.Reset();
        }
        public static void UpdateCinematicUI(float SolarEleAngle)
        {
            CinematicUI cine = CinematicUI.GetInstance();
            if (cine == null || !cine.IsDockingCardActive())
            {
                return;
            }

            Entity previewEntity = cine.GetLevelSequenceEntity();
            if (previewEntity == null || previewEntity.World == null)
            {
                return;
            }

            bool IsSolarEleAngleCurve = ControllableUnitSystemG.GetIsSolarElevationAngleCurveController(previewEntity.World.GetNativePointer(), previewEntity.EntityID);
            if (!IsSolarEleAngleCurve)
            {
                return;
            }

            //
            if (SolarEleAngle < -90.0f || SolarEleAngle > 90.0f)
            {
                return;
            }

            cine.ForceSetHeadLocation((decimal)SolarEleAngle);
            cine.SetStartLocation((decimal)-90);
            cine.SetEndLocation((decimal)90);
        }

        public void OnTODChanged(TODLightConfig value)
        {
            Clicross.GameWorldInterface.World_TriggerRealTimeProbeCaptureByScript(Entity.World._WorldInterface);
            float SolarEleAngle = TODLightSystemG.ComputeSolarElevationAngleAtCameraPosition(Entity.World.GetNativePointer(), Entity.EntityID);
            UpdateCinematicUI(SolarEleAngle);
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "TODLight configuration", bKeyFrame = false)]
        public TODLightConfig Config
        {
            get
            {
                return TODLightSystemG.GetTODLightConfig(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                var cur = TODLightSystemG.GetTODLightConfig(Entity.World.GetNativePointer(), Entity.EntityID);
                if (value.Equal(cur, TimeZone, TimeZone))
                {
                    return;
                }

                TODLightSystemG.SetTODLightConfig(Entity.World.GetNativePointer(), Entity.EntityID, value);
                TODLightSystemG.OnChangeConfig(Entity.World.GetNativePointer(), Entity.EntityID, false);
                TODLightSystemG.SyncAllTODLightComponents(Entity.World.GetNativePointer(), Entity.EntityID);
                OnTODChanged(value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "TODLight TimeZone setting")]
        public int TimeZone
        {
            get
            {
                return TODLightSystemG.GetTODLightTimeZone(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                var cur = TODLightSystemG.GetTODLightTimeZone(Entity.World.GetNativePointer(), Entity.EntityID);
                if (cur == value)
                {
                    return;
                }
                TODLightSystemG.SetTODLightTimeZone(Entity.World.GetNativePointer(), Entity.EntityID, value);
                TODLightSystemG.OnChangeConfig(Entity.World.GetNativePointer(), Entity.EntityID, false);
                TODLightSystemG.SyncAllTODLightComponents(Entity.World.GetNativePointer(), Entity.EntityID);
                OnTODChanged(Config);
            }
        }

        public override void OnComponentAddToEntity()
        {
            base.OnComponentAddToEntity();
        }
    }
}