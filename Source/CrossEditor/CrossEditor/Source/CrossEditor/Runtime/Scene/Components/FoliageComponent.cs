using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    static class Constants
    {
        public const string _DefaultMeshPath = "EngineResource/Model/Cube.nda";
        public const string _DefaultMaterialPath = "EngineResource/Material/DefaultMaterial.nda";
    }

    public class PartitionData
    {
        public Float3 childMin;
        public Float3 childMax;
        public Entity childEntity;
        public List<RTSData> childInstanceData = new List<RTSData>();
    }

    public class LoDSection
    {
        string _DefaultMaterial = Constants._DefaultMaterialPath;
        List<string> _SubSectionMaterials = new List<string>();

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Default material for this LoD.",
            FileTypeDescriptor = "Material Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Material)]
        public string DefaultMaterial
        {
            get => _DefaultMaterial;
            set => _DefaultMaterial = value;
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "StringAsResource", ToolTips = "Materials for each sub-section.",
            FileTypeDescriptor = "Material Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Material)]
        public List<string> SubSectionMaterials
        {
            get => _SubSectionMaterials;
            set => _SubSectionMaterials = value;
        }
    }

    public class ESubmeshProperty
    {
        bool _visible = true;

        [PropertyInfo(PropertyType = "Auto", ToolTips = "visible for each submesh.")]
        public bool Visible
        {
            get => _visible;
            set => _visible = value;
        }
    }

    [ComponentAttribute(NeedRenderProperty = true)]
    public class FoliageComponent : Component
    {
        public FoliageComponent()
        {
        }

        string _PrimaryMeshAsset = Constants._DefaultMeshPath;
        string _PrimaryMaterial = Constants._DefaultMaterialPath;
        string _InstanceResource = "";
        string _InstanceLightResource = "";
        string _Prefab;
        List<LoDSection> _LoDSections = new List<LoDSection>();
        List<ESubmeshProperty> _SubmeshProperties = new List<ESubmeshProperty>();
        List<RTSData> _InstanceData = new List<RTSData>();
        bool _EnabledIntersection = true;
        float _GlobalScale = 1.0f;
        float _GlobalRangeScale = 1.0f;
        float _MaxRandomCulling = 1.0f;
        float _Density = 1.0f;
        bool _LightCastShadow = true;
        int _PartitionBlockSize = 20000;
        FoliageGenerationType _GenerationType = FoliageGenerationType.FOLIAGE_GENERATION_TYPE_INSTANCE;
        static bool _InstanceDataVisible = false;
        static string[] _NativeNames = { "cross::FoliageComponentG", "cross::AABBComponentG" };
        //static string _DefaultMaterial = "EngineResource/Material/DefaultMaterial.nda";
        static string _DefaultMaterial = "Material/DefaultFoliage.nda";
        vector_int _InstanceIndexVec = new vector_int();
        bool _IsFoliageBVHGenerate = false;

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void Reset()
        {
            base.Reset();
            PrimaryMeshAsset = "EngineResource/Model/Cube.nda";
            PrimaryMaterial = _DefaultMaterial;
            LoDSection LoDSection = new LoDSection();
            LoDSection.DefaultMaterial = _DefaultMaterial;
            LoDSection.SubSectionMaterials.Add(_DefaultMaterial);

            List<LoDSection> LoDSections1 = new List<LoDSection>();
            LoDSections1.Add(LoDSection);
            this.LoDSections = LoDSections1;
        }

        public void Reset(string path)
        {
            base.Reset();
            PrimaryMeshAsset = path;
            PrimaryMaterial = _DefaultMaterial;
            LoDSection LoDSection = new LoDSection();
            LoDSection.DefaultMaterial = _DefaultMaterial;
            LoDSection.SubSectionMaterials.Add(_DefaultMaterial);
            LoDSection.SubSectionMaterials.Add(_DefaultMaterial);

            List<LoDSection> LoDSections1 = new List<LoDSection>();
            LoDSections1.Add(LoDSection);
            this.LoDSections = LoDSections1;
        }

        public void SetPropertyVisible(string propName, bool visible)
        {
            PropertyInfo[] ThisTypeProperties = this.GetType().GetProperties();
            PropertyInfo info = this.GetType().GetProperty(propName);
            AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(info);
            if (AttributeList != null)
            {
                AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();
                AttributeData.NamedAttributes["bHide"] = !visible;
            }
        }

        public void SetMethodVisible(string propName, bool visible)
        {
            MethodInfo info = this.GetType().GetMethod(propName);
            AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(info);
            if (AttributeList != null)
            {
                AttributeData AttributeData = AttributeList.GetAttributeData("MethodInfo");
                AttributeData.NamedAttributes["bHide"] = !visible;
            }
        }

        public void RefreshProperyVisibility()
        {
            bool visible = !(_GenerationType == FoliageGenerationType.FOLIAGE_GENERATION_TYPE_LIGHT);
            //SetPropertyVisible("PrefabResource", true);
            SetPropertyVisible("PrimaryMeshAsset", visible);
            SetPropertyVisible("PrimaryMaterial", visible);
            SetPropertyVisible("SubmeshProperties", visible);
            SetPropertyVisible("LoDSections", visible);
            SetPropertyVisible("PartitionBlockSize", visible);
            SetMethodVisible("FoliagePartition", visible);
            SetMethodVisible("FoliageBVHGenerate", true);

            SetPropertyVisible("LightCastShadow", !visible);
            SetPropertyVisible("GlobalRangeScale", !visible);

            SetPropertyVisible("InstanceData", _InstanceDataVisible);

            //OperationQueue.GetInstance().AddOperation(() => { InspectorUI.GetInstance().InspectObject(); });
        }

        public override void BeforeInspectObject()
        {
            RefreshProperyVisibility();
        }

        public void SyncInstanceData()
        {
            _InstanceData.Clear();
            FoliageComponentInstanceData instanceData = FoliageSystemG.GetEditorInstanceData(Entity.World.GetNativePointer(), Entity.EntityID);
            int i = 0;
            foreach (FoliageComponentTransform rts in instanceData.mInstanceData)
            {
                RTSData transform = new RTSData();
                try
                {
                    // HACK: it could be crash on release if instance data count great than 8w
                    transform.Translation = new Vector3d(rts.mTranslation.x, rts.mTranslation.y, rts.mTranslation.z);
                    transform.Scale = new Vector3d(rts.mScale.x, rts.mScale.y, rts.mScale.z);
                    transform.Rotation = Quaterniond.ToEuler(new Quaterniond(rts.mRotation.x, rts.mRotation.y, rts.mRotation.z, rts.mRotation.w));
                    transform.Delete = rts.mDelete;
                }
                catch
                {
                    System.Console.WriteLine($"index: {i}, count: {instanceData.mInstanceData.Capacity}");
                }
                _InstanceData.Add(transform);
                i++;
            }
        }

        public override void SyncDataFromEngine()
        {
            FoliageComponentResources resources = FoliageSystemG.GetEditorResources(Entity.World.GetNativePointer(), Entity.EntityID);
            _PrimaryMeshAsset = resources.mPrimaryMeshAssetPath;
            _PrimaryMaterial = resources.mPrimaryMaterialPath;
            _InstanceResource = resources.mInstanceResource;
            _InstanceLightResource = resources.mInstanceLightResource;
            _SubmeshProperties.Clear();
            _LoDSections.Clear();
            foreach (SubmeshProperty prop in resources.mSubmeshProperty)
            {
                ESubmeshProperty item = new ESubmeshProperty();
                item.Visible = prop.visible;
                _SubmeshProperties.Add(item);
            }
            foreach (FoliageComponentLoDSection LoD in resources.mLoDSections)
            {
                LoDSection item = new LoDSection();
                item.DefaultMaterial = LoD.mDefaultMaterialPath;
                foreach (string Sub in LoD.mSubSectionMaterialPaths)
                {
                    item.SubSectionMaterials.Add(Sub);
                }
                _LoDSections.Add(item);
            }

            _InstanceData.Clear();
            _EnabledIntersection = resources.mEnabledIntersection;
            _GlobalScale = resources.mGlobalScale;
            _GlobalRangeScale = FoliageSystemG.GetGlobalRangeScale(Entity.World.GetNativePointer(), Entity.EntityID);
            _MaxRandomCulling = FoliageSystemG.GetMaxRandomCulling(Entity.World.GetNativePointer(), Entity.EntityID);
            _Density = FoliageSystemG.GetDensity(Entity.World.GetNativePointer(), Entity.EntityID);
            _Prefab = resources.mPrefabResource;
            _LightCastShadow = FoliageSystemG.GetLightCastShadow(Entity.World.GetNativePointer(), Entity.EntityID);
            _Enable = FoliageSystemG.GetEnable(Entity.World.GetNativePointer(), Entity.EntityID);
            _GenerationType = FoliageSystemG.GetFoliageGenerationType(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this light.")]
        public override bool Enable
        {
            get
            {
                return _Enable;
            }
            set
            {
                _Enable = value;
                FoliageSystemG.SetEnable(Entity.World.GetNativePointer(), Entity.EntityID, _Enable);
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Place foliage prefab here.",
            FileTypeDescriptor = "Prefab Assets#prefab", ObjectClassID1 = ClassIDType.CLASS_PrefabResource)]
        public string PrefabResource
        {
            get => _Prefab;
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    _Prefab = value;
                    FoliageSystemG.SetEditorPrefabResource(Entity.World.GetNativePointer(), Entity.EntityID, _Prefab);
                    SyncDataFromEngine();

                    PrefabScene.GetInstance().GetWorld().Root.RefreshTree();
                    EditorScene.GetInstance().GetWorld().Root.RefreshTree();
                    HierarchyUI.GetInstance().UpdateHierarchy();
                    OperationQueue.GetInstance().AddOperation(() => { InspectorUI.GetInstance().InspectObject(); });
                }
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Primary mesh for foliage instances.",
            FileTypeDescriptor = "Mesh Assets#nda", ObjectClassID1 = ClassIDType.CLASS_MeshAssetDataResource)]
        public string PrimaryMeshAsset
        {
            get => _PrimaryMeshAsset;
            set
            {
                _PrimaryMeshAsset = value;
                FoliageSystemG.SetPrimaryMeshPath(Entity.World.GetNativePointer(), Entity.EntityID, PrimaryMeshAsset);

                // refresh submesh property
                FoliageComponentResources resources = FoliageSystemG.GetEditorResources(Entity.World.GetNativePointer(), Entity.EntityID);
                SubmeshProperties.Clear();
                foreach (SubmeshProperty prop in resources.mSubmeshProperty)
                {
                    ESubmeshProperty item = new ESubmeshProperty();
                    item.Visible = prop.visible;
                    SubmeshProperties.Add(item);
                }
                OperationQueue.GetInstance().AddOperation(() => { InspectorUI.GetInstance().InspectObject(); });
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Primary material for foliage instances.",
            FileTypeDescriptor = "Material Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Material)]
        public string PrimaryMaterial
        {
            get => _PrimaryMaterial;
            set
            {
                _PrimaryMaterial = value;
                FoliageSystemG.SetPrimaryMaterialPath(Entity.World.GetNativePointer(), Entity.EntityID, PrimaryMaterial);
            }
        }



        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Instance Resource for foliage",
            bReadOnly = true,
            FileTypeDescriptor = "Instance Resource#nda", ObjectClassID1 = ClassIDType.CLASS_BinaryResource)]
        public string InstanceResource
        {
            get => _InstanceResource;
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Instance Resource for foliage",
            bReadOnly = true,
            FileTypeDescriptor = "Instance Data Resource#nda", ObjectClassID1 = ClassIDType.CLASS_BinaryResource)]
        public string InstanceDataResource
        {
            get => _InstanceLightResource;
        }

        public void SetEditorResource()
        {
            FoliageComponentResources resources = new FoliageComponentResources();
            resources.mPrimaryMeshAssetPath = _PrimaryMeshAsset;
            resources.mPrimaryMaterialPath = _PrimaryMaterial;
            foreach (LoDSection LoD in LoDSections)
            {
                FoliageComponentLoDSection item = new FoliageComponentLoDSection();
                item.mDefaultMaterialPath = LoD.DefaultMaterial;
                foreach (string Sub in LoD.SubSectionMaterials)
                {
                    item.mSubSectionMaterialPaths.Add(Sub);
                }
                resources.mLoDSections.Add(item);
            }
            foreach (ESubmeshProperty prop in SubmeshProperties)
            {
                SubmeshProperty item = new SubmeshProperty();
                item.visible = prop.Visible;
                resources.mSubmeshProperty.Add(item);
            }
            resources.mEnabledIntersection = _EnabledIntersection;
            resources.mGlobalScale = _GlobalScale;
            FoliageSystemG.SetEditorResources(Entity.World.GetNativePointer(), Entity.EntityID, resources);
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "submesh property.")]
        public List<ESubmeshProperty> SubmeshProperties
        {
            get => _SubmeshProperties;
            set
            {
                _SubmeshProperties = value;
                SetEditorResource();
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "LoD sections for foliage instances.")]
        public List<LoDSection> LoDSections
        {
            get => _LoDSections;
            set
            {
                _LoDSections = value;
                SetEditorResource();
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Instance count", bReadOnly = true)]
        public int InstanceCount
        {
            get
            {
                if (_GenerationType == FoliageGenerationType.FOLIAGE_GENERATION_TYPE_LIGHT)
                {
                    return (int)FoliageSystemG.GetInstanceLightCount(Entity.World.GetNativePointer(), Entity.EntityID);
                }
                else
                {
                    return (int)FoliageSystemG.GetInstanceCount(Entity.World.GetNativePointer(), Entity.EntityID);
                }
            }
            set
            {

            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Visible Instance Data.")]
        public bool InstanceDataVisible
        {
            get => _InstanceDataVisible;
            set
            {
                if (value)
                {
                    SyncInstanceData();
                }
                _InstanceDataVisible = value;
                RefreshProperyVisibility();
                OperationQueue.GetInstance().AddOperation(() => { InspectorUI.GetInstance().InspectObject(); });
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Instance data.", bHide = true)]
        public List<RTSData> InstanceData
        {
            get
            {
                if (_InstanceData.Count == 0)
                {
                    SyncInstanceData();
                }
                return _InstanceData;
            }
            set
            {
                _InstanceData = value;
                FoliageComponentInstanceData instanceData = new FoliageComponentInstanceData();
                foreach (RTSData rts in _InstanceData)
                {
                    FoliageComponentTransform transform = new FoliageComponentTransform();
                    transform.mTranslation.x = (float)rts.Translation.X;
                    transform.mTranslation.y = (float)rts.Translation.Y;
                    transform.mTranslation.z = (float)rts.Translation.Z;
                    transform.mScale.x = (float)rts.Scale.X;
                    transform.mScale.y = (float)rts.Scale.Y;
                    transform.mScale.z = (float)rts.Scale.Z;
                    Quaterniond quaternionf = Quaterniond.FromEuler(rts.Rotation.X, rts.Rotation.Y, rts.Rotation.Z);
                    transform.mRotation.x = (float)quaternionf.X;
                    transform.mRotation.y = (float)quaternionf.Y;
                    transform.mRotation.z = (float)quaternionf.Z;
                    transform.mRotation.w = (float)quaternionf.W;
                    transform.mDelete = rts.Delete;
                    instanceData.mInstanceData.Add(transform);
                }

                vector_int IndexVector = new vector_int();
                if (_InstanceIndexVec.Count != 0)
                {
                    IndexVector.AddRange(_InstanceIndexVec);
                }
                FoliageSystemG.SetEditorInstanceData(Entity.World.GetNativePointer(), Entity.EntityID, instanceData, IndexVector);
                _InstanceIndexVec.Clear();
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable to intersect.")]
        public bool Intersection
        {
            get => _EnabledIntersection;
            set
            {
                _EnabledIntersection = value;
                FoliageSystemG.SetIntersection(Entity.World.GetNativePointer(), Entity.EntityID, _EnabledIntersection);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Foliage global scale.")]
        public float GlobalScale
        {
            get => _GlobalScale;
            set
            {
                _GlobalScale = value;
                FoliageSystemG.SetGlobalScale(Entity.World.GetNativePointer(), Entity.EntityID, _GlobalScale);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Light global range scale.")]
        public float GlobalRangeScale
        {
            get => _GlobalRangeScale;
            set
            {
                _GlobalRangeScale = value;
                FoliageSystemG.SetGlobalRangeScale(Entity.World.GetNativePointer(), Entity.EntityID, _GlobalRangeScale);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Max Random culling value.")]
        public float MaxRandomCulling
        {
            get => _MaxRandomCulling;
            set
            {
                _MaxRandomCulling = value;
                FoliageSystemG.SetMaxRandomCulling(Entity.World.GetNativePointer(), Entity.EntityID, _MaxRandomCulling);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Foliage density.", ValueMin = "0.0", ValueMax = "1.0")]
        public float Density
        {
            get => _Density;
            set
            {
                _Density = value;
                FoliageSystemG.SetDensity(Entity.World.GetNativePointer(), Entity.EntityID, _Density);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable light cast shadow.")]
        public bool LightCastShadow
        {
            get => _LightCastShadow;
            set
            {
                _LightCastShadow = value;
                FoliageSystemG.SetLightCastShadow(Entity.World.GetNativePointer(), Entity.EntityID, _LightCastShadow);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Foliage generation type.")]
        public FoliageGenerationType GenerationType
        {
            get
            {
                return _GenerationType;
            }
            set
            {
                _GenerationType = value;
                FoliageSystemG.SetFoliageGenerationType(Entity.World.GetNativePointer(), Entity.EntityID, _GenerationType);
                RefreshProperyVisibility();
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "PCG Foliage Reserve Insatnce Count.")]
        public uint PCGReservedCapacity
        {
            get => FoliageSystemG.GetPCGReservedCapacity(Entity.World.GetNativePointer(), Entity.EntityID);
            set
            {
                FoliageSystemG.SetPCGReservedCapacity(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Partition block size.")]
        public int PartitionBlockSize
        {
            get => _PartitionBlockSize;
            set
            {
                _PartitionBlockSize = value;
            }
        }

        [MethodInfo(PropertyType = "Button")]
        public void FoliagePartition()
        {
            // calculate boundingbox
            Float3 minP = new Float3(float.MaxValue, float.MaxValue, float.MaxValue);
            Float3 maxP = new Float3(float.MinValue, float.MinValue, float.MinValue);
            List<RTSData> instanceData = new List<RTSData>(InstanceData);
            foreach (RTSData rts in instanceData)
            {
                minP.x = (float)Math.Min(minP.x, rts.Translation.X);
                minP.y = (float)Math.Min(minP.y, rts.Translation.Y);
                minP.z = (float)Math.Min(minP.z, rts.Translation.Z);

                maxP.x = (float)Math.Max(maxP.x, rts.Translation.X);
                maxP.y = (float)Math.Max(maxP.y, rts.Translation.Y);
                maxP.z = (float)Math.Max(maxP.z, rts.Translation.Z);
            }
            InstanceData = new List<RTSData>();

            float ExtentX = Math.Abs(maxP.x - minP.x) + 0.1f;
            float ExtentY = Math.Abs(maxP.y - minP.y) + 0.1f;
            float ExtentZ = Math.Abs(maxP.z - minP.z) + 0.1f;

            int numBlockX = (int)Math.Ceiling(ExtentX / PartitionBlockSize);
            int numBlockZ = (int)Math.Ceiling(ExtentZ / PartitionBlockSize);
            Float3 blockSize = new Float3(PartitionBlockSize, 0f, PartitionBlockSize);

            List<PartitionData> partitionDatas = new List<PartitionData>();
            for (int x = 0; x < numBlockX; ++x)
            {
                for (int z = 0; z < numBlockZ; ++z)
                {
                    Float3 childMin = new Float3(minP.x + x * PartitionBlockSize, 0f, minP.z + z * PartitionBlockSize);
                    Float3 childMax = childMin.Add(blockSize);

                    PartitionData partitionData = new PartitionData();
                    partitionData.childMin = childMin;
                    partitionData.childMax = childMax;
                    partitionDatas.Add(partitionData);
                }
            }

            foreach (RTSData rts in instanceData)
            {
                bool isAdd = false;
                for (int i = 0; i < partitionDatas.Count; ++i)
                {
                    PartitionData data = partitionDatas[i];
                    if (rts.Translation.X >= data.childMin.x && rts.Translation.X <= data.childMax.x &&
                           //rts.Translation.Y >= data.childMin.y && rts.Translation.Y <= data.childMax.y &&
                           rts.Translation.Z >= data.childMin.z && rts.Translation.Z <= data.childMax.z)
                    {
                        isAdd = true;
                        data.childInstanceData.Add(rts);
                        break;
                    }
                }
                if (!isAdd)
                {
                    partitionDatas[0].childInstanceData.Add(rts);
                }
            }

            int index = 0;
            foreach (PartitionData data in partitionDatas)
            {
                if (data.childInstanceData.Count > 0)
                {
                    Entity NewEntity = Entity.Clone(Entity.Parent);
                    NewEntity.SetName(Entity.GetName() + "_" + index);
                    FoliageComponent foliageComp = NewEntity.GetFoliageComponent();
                    data.childEntity = NewEntity;
                    foliageComp.InstanceData = data.childInstanceData;
                    ++index;
                }
            }

            foreach (PartitionData data in partitionDatas)
            {
                if (data.childInstanceData.Count > 0)
                {
                    Entity.AddChildEntity(data.childEntity);
                    data.childEntity.RuntimeJointToParent();
                }
            }
            HierarchyUI.GetInstance().UpdateHierarchy();
        }

        public void SetInstanceIndexVec(vector_int VecInt)
        {
            _InstanceIndexVec = VecInt;
        }

        [MethodInfo(PropertyType = "Button")]
        public void FoliageBVHGenerate()
        {
            if (!DialogUIManager.GetInstance().ShowingDialogUI())
            {
                if (_InstanceData.Count == 0)
                {
                    CommonDialogUI.ShowSimpleOKDialog(UIManager.GetMainUIManager(), "Generate Fail", "Please set foliage instanceData.");
                    return;
                }
            }

            _IsFoliageBVHGenerate = true;
            FoliageSystemG.FoliageBVHGenerate(Entity.World.GetNativePointer(), Entity.EntityID);
            InstanceData = _InstanceData;
        }

        public bool GetIsFoliageBVHGenerate()
        {
            return _IsFoliageBVHGenerate;
        }

        public void FallToGround()
        {
            FoliageComponentInstanceData instanceData = new FoliageComponentInstanceData();
            //Random Random = new Random();

            List<RTSData> Data = new List<RTSData>();
            foreach (RTSData rts in InstanceData)
            {
                FoliageComponentTransform transform = new FoliageComponentTransform();

                RayPickResult RayPickResult;
                EditorScene EditorScene = EditorSceneUI.GetInstance().GetEditorScene();
                Double3 Translation = Entity.GetTransformComponent().GetWorldTranslation();

                Matrix4x4d Matrix4x4d = new Matrix4x4d();
                Entity.GetTransformComponent().GetWorldMatrix(ref Matrix4x4d);

                //Vector3d RayStart = new Vector3d(Translation.x, Translation.y, Translation.z) + rts.Translation;
                Vector3d RayStart = new Vector3d();
                Vector3d RayStartAbso = rts.Translation;
                Matrix4x4d.Transform(ref RayStartAbso);
                RayStart = RayStartAbso;
                Vector3d RayEnd = new Vector3d(0, 0, 0);
                if (EditorScene.GetInstance().GetCameraMode() == CameraMode.WGS84)
                {
                    RayEnd = new Vector3d(0, 0, 0);
                }
                else if (EditorScene.GetInstance().GetCameraMode() == CameraMode.Flat)
                {
                    RayEnd = RayStart + new Vector3d(0, -1, 0) * (100000);
                }
                //Vector3d RayBaseTilePosition = new Vector3d(TransformSystemG.GetWorldTranslationTile(Entity.World.GetNativePointer(), Entity.EntityID));
                Vector3d RayBaseTilePosition = new Vector3d(0, 0, 0);
                RayPickResult = EditorScene.RayPick(ref RayStart, ref RayEnd, ref RayBaseTilePosition, RayPickFlag.All ^ (RayPickFlag.Gizmo | RayPickFlag.Foliage));
                if (RayPickResult.HitEntity == null)
                {
                    transform.mTranslation.x = (float)rts.Translation.X;
                    transform.mTranslation.y = (float)rts.Translation.Y;
                    transform.mTranslation.z = (float)rts.Translation.Z;
                }
                else
                {
                    Vector3d Relative = RayPickResult.HitPoint;
                    Matrix4x4d.Inverse().Transform(ref Relative);
                    transform.mTranslation = new Float3((float)Relative.X, (float)Relative.Y, (float)Relative.Z);

                }
                transform.mScale.x = (float)rts.Scale.X;
                transform.mScale.y = (float)rts.Scale.Y;
                transform.mScale.z = (float)rts.Scale.Z;

                Quaterniond quaternionf = Quaterniond.FromEuler(rts.Rotation.X, rts.Rotation.Y, rts.Rotation.Z);
                transform.mRotation.x = (float)quaternionf.X;
                transform.mRotation.y = (float)quaternionf.Y;
                transform.mRotation.z = (float)quaternionf.Z;
                transform.mRotation.w = (float)quaternionf.W;
                instanceData.mInstanceData.Add(transform);

                RTSData rtsData = new RTSData();
                rtsData.Translation = new Vector3d(transform.mTranslation);
                rtsData.Rotation = new Vector3d(Quaternion.QuaternionToEuler(transform.mRotation));
                rtsData.Scale = new Vector3d(transform.mScale);
                Data.Add(rtsData);
            }
            InstanceData = Data;
            FoliageSystemG.SetEditorInstanceData(Entity.World.GetNativePointer(), Entity.EntityID, instanceData, new vector_int());
            InspectorUI.GetInstance().InspectObject();
        }
    }
}
