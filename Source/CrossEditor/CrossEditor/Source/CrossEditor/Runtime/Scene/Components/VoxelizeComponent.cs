using CEngine;

namespace CrossEditor
{
    class VoxelizeComponent : Component
    {
        static string[] _NativeNames = { "cross::VoxelizeComponentG" };

        VoxelizeComponentG mVoxelizeComp = new VoxelizeComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public VoxelizeComponent()
        {
        }

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 8;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void Reset()
        {
            base.Reset();
        }

        public override bool Enable
        {
            get
            {
                return VoxelizeComp.Enable;
            }
            set
            {
                VoxelizeComp.Enable = value;
                VoxelizeComp = VoxelizeComp;
            }
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "VoxelizeComponent")]
        public VoxelizeComponentG VoxelizeComp
        {
            get
            {
                if (Entity.World != null)
                {
                    VoxelizeSystemG.GetVoxelizeComponent(Entity.World.GetNativePointer(), Entity.EntityID, mVoxelizeComp);
                }
                return mVoxelizeComp;
            }
            set
            {
                mVoxelizeComp = value;
                VoxelizeSystemG.SetVoxelizeComponent(Entity.World.GetNativePointer(), Entity.EntityID, mVoxelizeComp);
            }
        }
    }
}
