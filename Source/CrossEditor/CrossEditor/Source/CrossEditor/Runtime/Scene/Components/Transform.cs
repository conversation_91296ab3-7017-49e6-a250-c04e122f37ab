using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [Flags]
    public enum TransformDirtyFlags
    {
        None = 0,
        TranslationDirty = 1,
        RotationVecDirty = 2,
        ScaleDirty = 4,
    }

    public enum CoordSystemType
    {
        CrossEngine,
        UnrealEngine
    }

    public class Transform : Component
    {
        static string _NativeSystemName = "cross::TransformSystemG";
        Double3 _Translation;
        Double3 _Rotation;
        Double3 _RotationUE;
        Double3 _Scale;
        public static CoordSystemType _CoordSystemType = CoordSystemType.CrossEngine;
        static CoordSystemType _CoordSystemTypeOld = CoordSystemType.CrossEngine;
        static Entity _EntityOld = null;

        static new List<Type> _AssociateComponents = new List<Type> { typeof(WorldPartitionProperty) };

        public static bool CoordSystemTypeDirtyChanged(Entity Entity)
        {
            if (_EntityOld != Entity)
            {
                _EntityOld = Entity;
                return true;
            }

            if (_CoordSystemTypeOld == _CoordSystemType)
                return false;

            _CoordSystemTypeOld = _CoordSystemType;
            return true;
        }

        static string[] _NativeNames = { "cross::LocalTransformComponentG", "cross::WorldTransformComponentG" };

        public override string NativeSystemName()
        {
            return _NativeSystemName;
        }

        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 0;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public Transform()
        {
            _Translation = new Double3(0.0f, 0.0f, 0.0f);
            _Rotation = new Double3(0.0f, 0.0f, 0.0f);
            _RotationUE = new Double3(0.0f, 0.0f, 0.0f);
            _Scale = new Double3(1.0f, 1.0f, 1.0f);
        }

        public override void Reset()
        {
            base.Reset();
        }

        public override void SyncDataFromEngine()
        {
            var Trans = TransformSystemG.GetLocalTranslationT(Entity.World.GetNativePointer(), Entity.EntityID);
            _Translation = new Double3(Trans);
            var Quat = TransformSystemG.GetLocalRotationT(Entity.World.GetNativePointer(), Entity.EntityID);
            _Rotation = Quaternion64.Quaternion64ToEuler(new Quaternion64(Quat.x, Quat.y, Quat.z, Quat.w)).ToDegree();
            var Scale = TransformSystemG.GetLocalScaleT(Entity.World.GetNativePointer(), Entity.EntityID);
            _Scale = new Double3(Scale);
        }

        public void TryToSyncRotationCacheForUE()
        {
            if (Transform.CoordSystemTypeDirtyChanged(Entity))
            {
                var CE_Q = TransformSystemG.GetLocalRotationT(Entity.World.GetNativePointer(), Entity.EntityID);
                var UE_Q = TransformConvert.CE2UE_Quaternion(CE_Q);
                _RotationUE = TransformConvert.CE2UE_Rotator(TransformConvert.UE_QuatToRotator(UE_Q));
            }
        }

        public Transform GetParentTransform()
        {
            Entity ParentEntity = Entity.Parent;
            if (ParentEntity != null)
            {
                return ParentEntity.GetTransformComponent();
            }
            return null;
        }

        public void RefreshTransform()
        {
            TransformSystemG.SetLocalTranslationT(Entity.World.GetNativePointer(), Entity.EntityID,
                TransformSystemG.CastDouble3ToTRSVector3(Entity.World.GetNativePointer(), _Translation),
                true, false);

            Quaternion64 Quaternion = Quaternion64.EulerToQuaternion64(_Rotation.ToRadian());
            TransformSystemG.SetLocalRotationT(Entity.World.GetNativePointer(), Entity.EntityID,
                TransformSystemG.CastQuaternion64ToTRSQuaternion(Entity.World.GetNativePointer(), Quaternion)
                , true, false);

            TransformSystemG.SetLocalScaleT(Entity.World.GetNativePointer(), Entity.EntityID,
                TransformSystemG.CastDouble3ToTRSVector3(Entity.World.GetNativePointer(), _Scale)
                , true, false);
        }

        public void SetMatrix(ref Matrix4x4d LocalMatrix)
        {
            var trs = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(LocalMatrix.ToClicrossDouble4x4());
            Quaterniond Quaternion = new Quaterniond(trs.rotation.x, trs.rotation.y, trs.rotation.z, trs.rotation.w);
            Vector3d Translation = new Vector3d(trs.translation.x, trs.translation.y, trs.translation.z);
            Vector3d Scale = new Vector3d(trs.scaling.x, trs.scaling.y, trs.scaling.z);
            _Rotation = Quaterniond.ToEuler(Quaternion);
            _Translation = Translation;
            _Scale = Scale;
            RefreshTransform();
        }

        public void GetMatrix(ref Matrix4x4d LocalMatrix)
        {
            Quaterniond Quaternion = Quaterniond.FromEuler(_Rotation.x, _Rotation.y, _Rotation.z);
            Clicross.Quaternion64 rot = new Clicross.Quaternion64(Quaternion.X, Quaternion.Y, Quaternion.Z, Quaternion.W);
            Clicross.Double3 Translation = new Clicross.Double3(_Translation.x, _Translation.y, _Translation.z);
            Clicross.Double3 Scale = new Clicross.Double3(_Scale.x, _Scale.y, _Scale.z);
            LocalMatrix = new Matrix4x4d(Clicross.math.MatrixUtil.Math_MatrixComposeD(Scale, rot, Translation));

        }

        public void SetWorldMatrix(ref Matrix4x4d WorldMatrix)
        {
            Matrix4x4d ParentWorldMatrix = new Matrix4x4d();
            Transform ParentTransform = GetParentTransform();
            if (ParentTransform != null)
            {
                ParentTransform.GetWorldMatrix(ref ParentWorldMatrix);
            }
            else
            {
                ParentWorldMatrix.LoadIdentity();
            }
            Matrix4x4d ParentWorldMatrixInverse = ParentWorldMatrix.Inverse();
            Matrix4x4d LocalMatrix = new Matrix4x4d();
            LocalMatrix.Mul(ref WorldMatrix, ref ParentWorldMatrixInverse);
            SetMatrix(ref LocalMatrix);
        }

        public void UpdateTransform()
        {
            SyncDataFromEngine();
        }

        public void GetWorldMatrix(ref Matrix4x4d WorldMatrix)
        {
            Matrix4x4d LocalMatrix = new Matrix4x4d();
            GetMatrix(ref LocalMatrix);
            Transform ParentTransform = GetParentTransform();
            if (ParentTransform != null)
            {
                Matrix4x4d ParentWorldMatrix = new Matrix4x4d();
                ParentTransform.GetWorldMatrix(ref ParentWorldMatrix);
                WorldMatrix.Mul(ref LocalMatrix, ref ParentWorldMatrix);
            }
            else
            {
                WorldMatrix = LocalMatrix;
            }
        }

        public void GetRelativeWorldMatrix(ref Matrix4x4d WorldMatrix, Entity BaseEntity)
        {
            Float3 BaseTile = TransformSystemG.GetWorldTranslationTile(BaseEntity.World.GetNativePointer(), BaseEntity.EntityID);
            WorldMatrix = new Matrix4x4d(TransformSystemG.GetRelativeWorldMatrix(Entity.World.GetNativePointer(), Entity.EntityID, BaseTile));
        }

        public void UpdateWorldTransform()
        {
            var Trans = TransformSystemG.GetWorldTranslationT(Entity.World.GetNativePointer(), Entity.EntityID);
            _Translation = new Double3(Trans);
            var Quat = TransformSystemG.GetWorldRotationT(Entity.World.GetNativePointer(), Entity.EntityID);
            _Rotation = Quaternion64.Quaternion64ToEuler(new Quaternion64(Quat.x, Quat.y, Quat.z, Quat.w)).ToDegree();
            var Scale = TransformSystemG.GetWorldScaleT(Entity.World.GetNativePointer(), Entity.EntityID);
            _Scale = new Double3(Scale);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Coordinate system type.")]
        public CoordSystemType CoordSystemType
        {
            get
            {
                return _CoordSystemType;
            }
            set
            {
                _CoordSystemTypeOld = _CoordSystemType;
                _CoordSystemType = value;
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    InspectorUI.GetInstance().InspectObject();
                });
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The translation of the entity.", bKeyFrame = true)]
        public Double3 Translation
        {
            get
            {
                return _Translation;
            }
            set
            {
                _Translation = value;
                TransformSystemG.SetLocalTranslationT(Entity.World.GetNativePointer(), Entity.EntityID,
                    TransformSystemG.CastDouble3ToTRSVector3(Entity.World.GetNativePointer(), _Translation),
                    true, false);
                Entity.SetDirty();
            }
        }

        public Double3 GetWorldTranslation()
        {
            Double3 Trans = TransformSystemG.GetWorldTranslationT(Entity.World.GetNativePointer(), Entity.EntityID);
            return new Double3(Trans);
        }

        public void SetWorldTranslation(Double3 WorldTranslation)
        {
            TransformSystemG.SetWorldTranslationT(Entity.World.GetNativePointer(), Entity.EntityID, TransformSystemG.CastDouble3ToTRSVector3(Entity.World.GetNativePointer(), WorldTranslation));
            UpdateTransform();
        }

        //[PropertyInfo(PropertyType = "Auto", ToolTips = "The rotation of the entity.", ValueMin = "0.0", ValueMax = "360.0", bKeyFrame = true)]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "The rotation of the entity.", bKeyFrame = true)]
        public Double3 Rotation
        {
            get
            {
                return _Rotation;
            }
            set
            {
                _Rotation = value;
                Quaternion64 Quaternion = Quaternion64.EulerToQuaternion64(_Rotation.ToRadian());
                TransformSystemG.SetLocalRotationT(Entity.World.GetNativePointer(), Entity.EntityID,
                    TransformSystemG.CastQuaternion64ToTRSQuaternion(Entity.World.GetNativePointer(), Quaternion)
                    , true, false);
                Entity.SetDirty();
            }
        }

        public Double3 GetWorldRotation()
        {
            Quaternion64 Quat = TransformSystemG.GetWorldRotationT(Entity.World.GetNativePointer(), Entity.EntityID);
            return Quaternion64.Quaternion64ToEuler(new Quaternion64(Quat.x, Quat.y, Quat.z, Quat.w)).ToDegree();
        }

        public void SetWorldRotation(Double3 Euler)
        {
            Quaternion64 Quat = Quaternion64.EulerToQuaternion64(Euler.ToRadian());
            TransformSystemG.SetWorldRotationT(Entity.World.GetNativePointer(), Entity.EntityID, TransformSystemG.CastQuaternion64ToTRSQuaternion(Entity.World.GetNativePointer(), Quat));
            UpdateTransform();
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The scale of the entity.", bKeyFrame = true)]
        public Double3 Scale
        {
            get
            {
                return _Scale;
            }
            set
            {
                _Scale = value;
                TransformSystemG.SetLocalScaleT(Entity.World.GetNativePointer(), Entity.EntityID,
                    TransformSystemG.CastDouble3ToTRSVector3(Entity.World.GetNativePointer(), _Scale),
                    true, false);
                Entity.SetDirty();
            }
        }

        public Double3 GetWorldScale()
        {
            Double3 Scale = TransformSystemG.GetWorldScaleT(Entity.World.GetNativePointer(), Entity.EntityID);
            return new Double3(Scale);
        }

        public void SetWorldScale(Double3 Scale)
        {
            TransformSystemG.SetWorldScaleT(Entity.World.GetNativePointer(), Entity.EntityID, TransformSystemG.CastDouble3ToTRSVector3(Entity.World.GetNativePointer(), Scale));
            UpdateTransform();
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The translation of the entity (UnrealEngine coordinate system).", bKeyFrame = true)]
        public Double3 Translation_UE
        {
            get
            {
                return TransformConvert.CE2UE_Translation(Translation);
            }
            set
            {
                if (_CoordSystemType == CoordSystemType.UnrealEngine)
                {
                    Translation = TransformConvert.UE2CE_Translation(value);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The rotation of the entity (UnrealEngine coordinate system).", ValueMin = "0.0", ValueMax = "360.0", bKeyFrame = true)]
        public Double3 Rotation_UE
        {
            get
            {
                return _RotationUE.Add(new Double3(0, 0, -90));
            }
            set
            {
                if (_CoordSystemType == CoordSystemType.UnrealEngine)
                {
                    value = value.Add(new Double3(0, 0, 90));
                    _RotationUE = value;
                    var CE_R = TransformConvert.UE2CE_Rotator(value);
                    Quaternion64 CE_Q = TransformConvert.UE_RotatorToQuat(CE_R);
                    Rotation = Quaternion64.Quaternion64ToEuler(TransformConvert.UE2CE_Quaternion(CE_Q)).ToDegree();
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The scale of the entity (UnrealEngine coordinate system).", bKeyFrame = true)]
        public Double3 Scale_UE
        {
            get
            {
                return TransformConvert.CE2UE_Scale(Scale);
            }
            set
            {
                if (_CoordSystemType == CoordSystemType.UnrealEngine)
                {
                    Scale = TransformConvert.UE2CE_Scale(value);
                }
            }
        }
    }

    public class RTSData
    {
        [JsonProperty("Rotation")]
        Quaterniond _Rotation = new Quaterniond(0.0f, 0.0f, 0.0f, 1.0f);
        [JsonProperty("Rotation_euler")]
        Vector3d _Rotation_euler = new Vector3d(0.0f, 0.0f, 0.0f);
        Vector3d _Translation = new Vector3d(0.0f, 0.0f, 0.0f);
        Vector3d _Scale = new Vector3d(1.0f, 1.0f, 1.0f);
        bool _Delete = false;

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Translation(x,y,z).")]
        public Vector3d Translation
        {
            get => _Translation;
            set => _Translation = value;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Rotation Euler angle in Degree."), JsonIgnore]
        public Vector3d Rotation
        {
            get => _Rotation_euler;
            set
            {
                _Rotation_euler = value;
                _Rotation = Quaterniond.FromEuler(_Rotation_euler.X, _Rotation_euler.Y, _Rotation_euler.Z);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Scale(x,y,z).")]
        public Vector3d Scale
        {
            get => _Scale;
            set => _Scale = value;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Delete or not.")]
        public bool Delete
        {
            get => _Delete;
            set => _Delete = value;
        }
    }


}
