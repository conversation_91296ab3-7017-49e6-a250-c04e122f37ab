using CEngine;

namespace CrossEditor
{
    class WorldPartitionProperty : Component
    {
        static string[] _NativeNames = { "" };

        EntityHashWay _HashWay;

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public WorldPartitionProperty()
        {
            _HashWay = EntityHashWay.BoudingBox;
        }

        static int _ComponentOrder = 1;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 0;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            base.Reset();
            HashWay = EntityHashWay.BoudingBox;
        }

        public override void SyncDataFromEngine()
        {
            _HashWay = CrossEngineApi.GetEntityHashWay(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Entity Hash Way")]
        public EntityHashWay HashWay
        {
            get { return _HashWay; }
            set
            {
                _HashWay = value;
                CrossEngineApi.SetEntityHashWay(Entity.World.GetNativePointer(), Entity.EntityID, _HashWay);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Belonged Block", bReadOnly = true)]
        public string BelongedBlock
        {
            get
            {
                return Entity.World == null ? "" : CrossEngineApi.GetEntityBlockInfo(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set { } // No set
        }
    }
}