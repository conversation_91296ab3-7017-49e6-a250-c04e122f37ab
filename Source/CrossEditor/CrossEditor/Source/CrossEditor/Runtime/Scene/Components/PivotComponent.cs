using CEngine;
namespace CrossEditor
{
    public class PivotComponent : Component
    {
        static string[] _NativeNames = { "cross::PivotComponentG" };

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public PivotComponent()
        {
        }
        static int _ComponentOrder = 5;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            base.Reset();
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Pivot")]
        public Double3 Pivot
        {
            get
            {
                return PivotSystemG.GetPivot(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PivotSystemG.SetPivot(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        public override void OnComponentAddToEntity()
        {
            base.OnComponentAddToEntity();
        }
    }
}