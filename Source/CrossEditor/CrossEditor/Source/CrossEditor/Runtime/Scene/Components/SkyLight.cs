using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Light/SkyLightComponent")]
    class SkyLightComponent : Component
    {
        static string[] _NativeNames = { "cross::SkyLightComponentG" };
        static string _NativeSystemName = "cross::SkyLightSystemG";

        SkyLightComponentG mSkyLight = new SkyLightComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        public override string NativeSystemName()
        {
            return _NativeSystemName;
        }

        public SkyLightComponent()
        {
        }

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 8;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void Reset()
        {
            base.Reset();
        }

        public override bool Enable
        {
            get
            {
                return SkyLight.Enable;
            }
            set
            {
                SkyLight.Enable = value;
                SkyLight = SkyLight;
            }
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "SkyLight")]
        public SkyLightComponentG SkyLight
        {
            get
            {
                if (Entity.World != null)
                {
                    SkyLightSystemG.GetSkyLightComponent(Entity.World.GetNativePointer(), Entity.EntityID, mSkyLight);

                }
                return mSkyLight;
            }
            set
            {
                mSkyLight = value;
                SkyLightSystemG.SetSkyLightComponent(Entity.World.GetNativePointer(), Entity.EntityID, mSkyLight);
            }
        }

        public override void SyncDataFromEngine()
        {
            SkyLightSystemG.GetSkyLightComponent(Entity.World.GetNativePointer(), Entity.EntityID, mSkyLight);
        }
    }
}
