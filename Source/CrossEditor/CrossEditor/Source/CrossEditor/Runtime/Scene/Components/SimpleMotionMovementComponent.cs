using CEngine;

namespace CrossEditor
{
    class SimpleMovementComponent : Component
    {
        static string[] _NativeNames = { "cross::SimpleMovementComponentG" };

        protected SimpleMovementComponentG mMotionMovement = new SimpleMovementComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public SimpleMovementComponent()
        { }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "MotionMovement")]
        public SimpleMovementComponentG MotionMovement
        {
            get
            {
                SimpleMovementSystemG.GetMotionMovementComponent(Entity.World.GetNativePointer(), Entity.EntityID, mMotionMovement);
                return mMotionMovement;
            }
            set
            {
                mMotionMovement = value;
                SimpleMovementSystemG.SetMotionMovementComponent(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
    }
}
