using CEngine;
using System;
using System.Reflection;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Fog Component")]
    class FogComponent : Component
    {
        static string[] _NativeNames = { "cross::FogComponentG" };
        static string _NativeSystemName = "cross::FogSystemG";

        

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 2;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override string NativeSystemName()
        {
            return _NativeSystemName;
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "Fog Setting")]
        public FogSetting FogSettingData
        {
            get
            {
                return FogSystemG.GetFogSetting(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                FogSystemG.SetFogSetting(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
    }
}
