using CEngine;
using Clicegf;
using System;
using System.Reflection;

namespace CrossEditor
{
    public interface IMultiTypeComponent
    {
        object GetInspectedObject();
        void SetToRuntime();
        void GetFromRuntime();
    }

    [ComponentAttribute(DisplayUINames = "Gameplay/Cinemachine", InspectorType = typeof(Inspector_MultiTypeComponent<CinemachineComponent>))]
    public class CinemachineComponent : GameObjectComponent, IMultiTypeComponent
    {
        static string[] _NativeNames = { "cegf::CinemachineComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        public override void SyncDataFromEngine()
        {
            _ApplyType = GetRunimeComponent().GetCameraApplyType();
            CmBrain = CreateInstance(_ApplyType);
            CmBrain.GetFromComponent(GetRunimeComponent());
        }

        // enum
        // enum -> Editor field struct Mix:brain Blend:brain Single:brain
        // base is cm brain
        // inspector:
        //      enum -> string -> type ->? property info List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(type);   
        //      set? -> enum -> string -> function -> invoke
        //      get? -> enum -> string -> function -> invoke
        // component->SetMix, SetBlend, SetSingle
        private CmBrainCameraApplyType _ApplyType = CmBrainCameraApplyType.Single;
        [PropertyInfo(PropertyType = "Auto", ToolTips = "CmBrainCameraApplyType")]
        public CmBrainCameraApplyType Type
        {
            get { return _ApplyType; }
            set
            {
                if (_ApplyType != value)
                {
                    _ApplyType = value;
                    CmBrain = CreateInstance(_ApplyType);
                    SetToRuntime();
                }
            }
        }

        private Clicegf.CinemachineComponent GetRunimeComponent()
        {
            var go = GetRuntimeOwnerGameObject();
            var comp = (Clicegf.CinemachineComponent)(go.GetComponentByMetaClassName(_NativeNames[0]));
            return comp;
        }

        public void SetToRuntime()
        {
            CmBrain.SetToComponent(GetRunimeComponent());
        }

        public void GetFromRuntime()
        {
            CmBrain.GetFromComponent(GetRunimeComponent());
        }

        public object GetInspectedObject()
        {
            return CmBrain;
        }

        private CmBrainEditorBase CmBrain;
        static protected string GetCmBrainTypeName(CmBrainCameraApplyType type)
        {
            return "Clicegf.CmBrainCam" + type.ToString();
        }

        protected CmBrainEditorBase CreateInstance(CmBrainCameraApplyType type)
        {
            Type typeInst = CrossEngine.GetType(GetCmBrainTypeName(type));
            return typeInst != null ? Activator.CreateInstance(typeInst) as CmBrainEditorBase : null;
        }
    }
}
