namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/DecalComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class DecalGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::DecalComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<DecalComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<DecalComponent>();
            }

            mECSEditorComponents["DecalComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["DecalComponent"] = mGameObject.mEntity.GetComponent<DecalComponent>();
        }
    }
}
