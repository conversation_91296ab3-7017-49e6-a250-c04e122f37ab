namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Light/LightComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class LightGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::LightComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<Light>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<Light>();
            }

            mECSEditorComponents["LightComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["LightComponent"] = mGameObject.mEntity.GetComponent<Light>();
        }
    }
}
