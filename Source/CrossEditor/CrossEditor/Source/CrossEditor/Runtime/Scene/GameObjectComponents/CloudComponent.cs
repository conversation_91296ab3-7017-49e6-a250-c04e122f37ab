namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/CloudComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class CloudGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::CloudComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<CloudComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<CloudComponent>();
            }

            mECSEditorComponents["CloudComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["CloudComponent"] = mGameObject.mEntity.GetComponent<CloudComponent>();
        }
    }
}
