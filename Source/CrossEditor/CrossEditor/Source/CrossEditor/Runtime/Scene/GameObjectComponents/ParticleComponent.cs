namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/ParticleComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class ParticleGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::ParticleComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<ParticleSystemComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<ParticleSystemComponent>();
            }

            mECSEditorComponents["ParticleComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["ParticleComponent"] = mGameObject.mEntity.GetComponent<ParticleSystemComponent>();
        }
    }
}
