namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/RenderPropertyComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class RenderPropertyGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::RenderPropertyComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<RenderProperty>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<RenderProperty>();
            }

            mECSEditorComponents["RenderPropertyComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["RenderPropertyComponent"] = mGameObject.mEntity.GetComponent<RenderProperty>();
        }
    }
}
