namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Physics/PhysicsComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class PhysicsComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::PhysicsComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<Physics>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<Physics>();
            }

            mECSEditorComponents["Physics"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["Physics"] = mGameObject.mEntity.GetComponent<Physics>();
        }
    }
    [ComponentAttribute(DisplayUINames = "Physics/BoxComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class BoxComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::BoxComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<Physics>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<Physics>();
            }

            mECSEditorComponents["Physics"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["Physics"] = mGameObject.mEntity.GetComponent<Physics>();
        }

        private Clicegf.BoxComponent GetRuntimeComponent()
        {
            var go = GetRuntimeOwnerGameObject();
            return go.GetComponentByMetaClassName(_NativeNames[0]) as Clicegf.BoxComponent;
        }
    }

    [ComponentAttribute(DisplayUINames = "Physics/CapsuleComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class CapsuleComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::CapsuleComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<Physics>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<Physics>();
            }

            mECSEditorComponents["Physics"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["Physics"] = mGameObject.mEntity.GetComponent<Physics>();
        }
    }

    [ComponentAttribute(DisplayUINames = "Physics/SphereComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class SphereComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::SphereComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<Physics>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<Physics>();
            }

            mECSEditorComponents["Physics"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["Physics"] = mGameObject.mEntity.GetComponent<Physics>();
        }
    }
}