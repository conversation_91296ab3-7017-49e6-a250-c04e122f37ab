namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Mesh/HierachicalInstancedStaticModelComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class HierachicalInstancedStaticModelGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::HierachicalInstancedStaticModelComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<HierachicalInstancedStaticModelComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<HierachicalInstancedStaticModelComponent>();
            }
            var rpComp = mGameObject.mEntity.GetComponent<RenderProperty>();
            if (rpComp == null)
            {
                rpComp = mGameObject.mEntity.CreateComponent<RenderProperty>();
            }
            mECSEditorComponents["HierachicalInstancedStaticModelComponent"] = comp;
            mECSEditorComponents["RenderPropertyComponent"] = rpComp;

        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["HierachicalInstancedStaticModelComponent"] = mGameObject.mEntity.GetComponent<HierachicalInstancedStaticModelComponent>();
            mECSEditorComponents["RenderPropertyComponent"] = mGameObject.mEntity.GetComponent<RenderProperty>();

        }
    }
}
