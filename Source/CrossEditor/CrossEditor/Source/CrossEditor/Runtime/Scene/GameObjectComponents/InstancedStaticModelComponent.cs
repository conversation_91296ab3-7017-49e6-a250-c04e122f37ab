namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Mesh/InstancedStaticModelComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class InstancedStaticModelGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::InstancedStaticModelComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<InstancedStaticModelComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<InstancedStaticModelComponent>();
            }
            var rpComp = mGameObject.mEntity.GetComponent<RenderProperty>();
            if (rpComp == null)
            {
                rpComp = mGameObject.mEntity.CreateComponent<RenderProperty>();
            }
            mECSEditorComponents["InstancedStaticModelComponent"] = comp;
            mECSEditorComponents["RenderPropertyComponent"] = rpComp;

        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["InstancedStaticModelComponent"] = mGameObject.mEntity.GetComponent<InstancedStaticModelComponent>();
            mECSEditorComponents["RenderPropertyComponent"] = mGameObject.mEntity.GetComponent<RenderProperty>();

        }
    }
}
