namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Misc/ControllableUnitComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class ControllableUnitGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::ControllableUnitComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<ControllableUnitComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<ControllableUnitComponent>();
            }

            mECSEditorComponents["ControllableUnitComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["ControllableUnitComponent"] = mGameObject.mEntity.GetComponent<ControllableUnitComponent>();
        }
    }
}
