namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/CameraComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class CameraComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::CameraComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<Camera>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<Camera>();
            }

            mECSEditorComponents["Camera"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["Camera"] = mGameObject.mEntity.GetComponent<Camera>();
        }
    }
}