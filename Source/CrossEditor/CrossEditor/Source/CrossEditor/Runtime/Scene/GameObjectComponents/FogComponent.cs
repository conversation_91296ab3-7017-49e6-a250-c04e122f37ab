namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/FogComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class FogGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::FogComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<FogComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<FogComponent>();
            }

            mECSEditorComponents["FogComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["FogComponent"] = mGameObject.mEntity.GetComponent<FogComponent>();
        }
    }
}