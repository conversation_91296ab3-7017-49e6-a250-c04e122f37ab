using CEngine;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace CrossEditor
{
	public class WaitForNextFrame : INotifyCompletion
	{
		public bool IsCompleted { get; private set; } = false;

		private Action mContinuation;

		public void GetResult()
		{
			Debug.Assert(IsCompleted);
			// No result to return, just wait for the next frame
		}

		void INotifyCompletion.OnCompleted(Action continuation)
		{
			mContinuation = continuation;
			CrossSynchronizationContext.Instance.Post(_ => {
				CrossSynchronizationContext.Instance.Post(_ => {
					IsCompleted = true;
					mContinuation?.Invoke();
				}, null);
			}, null);
		}

		public WaitForNextFrame GetAwaiter() => this;
	}
}
