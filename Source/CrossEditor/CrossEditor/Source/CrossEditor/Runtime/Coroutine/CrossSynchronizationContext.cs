using System.Collections.Generic;
using System.Threading;

namespace CrossEditor
{
    public sealed class CrossSynchronizationContext : SynchronizationContext
    {
        private const int kAwqInitialCapacity = 20;
        private readonly List<WorkRequest> m_AsyncWorkQueue = new List<WorkRequest>(kAwqInitialCapacity);
        private readonly List<WorkRequest> m_CurrentFrameWork = new List<WorkRequest>(kAwqInitialCapacity);
        private readonly int m_MainThreadID;

        private static CrossSynchronizationContext gInstance = null;
        public static CrossSynchronizationContext Instance
        {
            get
            {
                if (gInstance == null)
                {
                    gInstance = new CrossSynchronizationContext(Thread.CurrentThread.ManagedThreadId);
                }
                return gInstance;
            }
        }

        private CrossSynchronizationContext(int mainThreadID)
        {
            m_MainThreadID = mainThreadID;
        }

        private CrossSynchronizationContext(List<WorkRequest> queue, int mainThreadID)
        {
            m_AsyncWorkQueue = queue;
            m_MainThreadID = mainThreadID;
        }

        // SynchronizationContext must be set before any user code is executed. This is done on
        // Initial domain load and domain reload at MonoManager ReloadAssembly
        public void Initialize()
        {
            SetSynchronizationContext(this);
        }

        // Send will process the call synchronously. If the call is processed on the main thread, we'll invoke it
        // directly here. If the call is processed on another thread it will be queued up like POST to be executed
        // on the main thread and it will wait. Once the main thread processes the work we can continue
        public override void Send(SendOrPostCallback callback, object state)
        {
            if (m_MainThreadID == System.Threading.Thread.CurrentThread.ManagedThreadId)
            {
                callback(state);
            }
            else
            {
                using (var waitHandle = new ManualResetEvent(false))
                {
                    lock (m_AsyncWorkQueue)
                    {
                        m_AsyncWorkQueue.Add(new WorkRequest(callback, state, waitHandle));
                    }
                    waitHandle.WaitOne();
                }
            }
        }

        // Post will add the call to a task list to be executed later on the main thread then work will continue asynchronously
        public override void Post(SendOrPostCallback callback, object state)
        {
            lock (m_AsyncWorkQueue)
            {
                m_AsyncWorkQueue.Add(new WorkRequest(callback, state));
            }
        }

        // CreateCopy returns a new UnitySynchronizationContext object, but the queue is still shared with the original
        public override SynchronizationContext CreateCopy()
        {
            return new CrossSynchronizationContext(m_AsyncWorkQueue, m_MainThreadID);
        }

        // Exec will execute tasks off the task list
        public void ExecuteTasks()
        {
            lock (m_AsyncWorkQueue)
            {
                m_CurrentFrameWork.AddRange(m_AsyncWorkQueue);
                m_AsyncWorkQueue.Clear();
            }

            foreach (var work in m_CurrentFrameWork)
                work.Invoke();

            m_CurrentFrameWork.Clear();
        }

        private struct WorkRequest
        {
            private readonly SendOrPostCallback m_DelagateCallback;
            private readonly object m_DelagateState;
            private readonly ManualResetEvent m_WaitHandle;

            public WorkRequest(SendOrPostCallback callback, object state, ManualResetEvent waitHandle = null)
            {
                m_DelagateCallback = callback;
                m_DelagateState = state;
                m_WaitHandle = waitHandle;
            }

            public void Invoke()
            {
                m_DelagateCallback(m_DelagateState);

                if (m_WaitHandle != null)
                    m_WaitHandle.Set();
            }
        }
    }
}
