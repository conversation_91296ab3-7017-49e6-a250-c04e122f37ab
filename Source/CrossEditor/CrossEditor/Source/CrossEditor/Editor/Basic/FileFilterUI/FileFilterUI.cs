using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    delegate void FileSelectCallback(string Resource);

    class FileFilterUI
    {
        static FileFilterUI _Instance = new FileFilterUI();

        BackgroundMask _BackgroundMask;
        Panel _Panel;

        Edit _EditPattern;

        ScrollView _ScrollView;
        Panel _ScrollPanel;

        List<string> _ExtensionList;

        List<string> _ResourceList;
        List<string> _FilteredResourceList;

        FileSelectCallback _FileSelectCallback;

        public static FileFilterUI GetInstance()
        {
            return _Instance;
        }

        FileFilterUI()
        {
            _ResourceList = new List<string>();
            _FilteredResourceList = new List<string>();
            _ExtensionList = new List<string>();
        }

        void InitializeUI(UIManager UIManager)
        {
            Control Root = UIManager.GetRoot();

            _BackgroundMask = new BackgroundMask(UIManager);
            _BackgroundMask.Mask(OnBackgroundMaskClose);

            int Width = 800;

            _Panel = new Panel();
            _Panel.SetBackgroundColor(Color.EDITOR_UI_MENU_BACK_COLOR);
            _Panel.SetBorderColor(Color.EDITOR_UI_MENU_HILIGHT_COLOR);
            _Panel.SetSize(Width, 400);
            _Panel.LeftMouseDownEvent += OnPanelLeftMouseDown;
            _Panel.MouseMoveEvent += OnPanelMouseMove;
            _Panel.SetVisible(false);
            Root.AddChild(_Panel);

            _EditPattern = new Edit();
            _EditPattern.SetFontSize(18);
            _EditPattern.Initialize(EditMode.Simple_SingleLine);
            _EditPattern.LoadSource("");
            _EditPattern.CharInputedEvent += OnEditPatternCharInpupted;
            _EditPattern.KeyDownEvent += OnEditPatternKeyDown;
            _Panel.AddChild(_EditPattern);
            EditContextUI.GetInstance().RegisterEdit(_EditPattern);
            _EditPattern.SetPosition(5, 5, Width - 10, 18);
            _EditPattern.SetFocus();

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.SetPosition(5, 28, Width - 10, 367);
            _Panel.AddChild(_ScrollView);

            _ScrollPanel = _ScrollView.GetScrollPanel();

            UpdateResourceList();
            FilterResourceList();
            UpdateResourceListUI();
        }

        public UIManager GetUIManager()
        {
            if (_Panel != null)
            {
                return _Panel.GetUIManager();
            }
            else
            {
                return UIManager.GetActiveUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        bool MatchExtension(string Extension)
        {
            foreach (string Extension1 in _ExtensionList)
            {
                if (StringHelper.IgnoreCaseEqual(Extension, Extension1))
                {
                    return true;
                }
            }
            return false;
        }

        void CollectResources(string Directory)
        {
            DirectoryWalker DirectoryWalker = new DirectoryWalker();
            DirectoryWalker.WalkDirectory(Directory, true);
            int Count = DirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string EditorFilename = DirectoryWalkItem.Path;
                    string Extension = PathHelper.GetExtensionOfPath(EditorFilename);
                    if (MatchExtension(Extension))
                    {
                        _ResourceList.Add(EditorFilename);
                    }
                }
            }
        }

        void UpdateResourceList()
        {
            _ResourceList.Clear();

            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string ContentsDirectory = ProjectDirectory + "/Contents";
            CollectResources(ContentsDirectory);

            string ResourceDirectory = EditorUtilities.GetResourceDirectory();

            string EngineResourceDirectory = ResourceDirectory + "/EngineResource";
            CollectResources(EngineResourceDirectory);

            string PipelineResourceDirectory = ResourceDirectory + "/PipelineResource";
            CollectResources(PipelineResourceDirectory);
        }

        void FilterResourceList()
        {
            _FilteredResourceList.Clear();
            string Pattern = _EditPattern.GetText();
            foreach (string EditorFilename in _ResourceList)
            {
                string ResourceName = PathHelper.GetNameOfPath(EditorFilename);
                if (Pattern == "" ||
                    ResourceName.Contains(Pattern, StringComparison.OrdinalIgnoreCase))
                {
                    _FilteredResourceList.Add(EditorFilename);
                }
            }
        }

        void UpdateResourceListUI()
        {
            _ScrollPanel.ClearChildren();
            int Y = 3;
            for (int i = 0; i < _FilteredResourceList.Count; i++)
            {
                string EditorFilename = _FilteredResourceList[i];
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(EditorFilename);
                string ResourceName = PathHelper.GetNameOfPath(StandardFilename);

                int Width = _ScrollView.GetWidth();

                Button ButtonResource = new Button();
                ButtonResource.Initialize();
                ButtonResource.SetPosition(0, Y, Width, 64);
                ButtonResource.SetFontSize(16);
                ButtonResource.SetTextAlign(TextAlign.CenterLeft);
                ButtonResource.SetTagString1(StandardFilename);
                ButtonResource.SetDownColor(Color.EDITOR_UI_MENU_BACK_COLOR);
                ButtonResource.ClickedEvent += OnButtonResourceClicked;
                //ButtonResource.RightMouseUpEvent += OnButtonResourceRightMouseUp;
                _ScrollPanel.AddChild(ButtonResource);

                int X1 = 6;
                int Width1 = Width - X1 - 3;

                Label LabelResourceName = new Label();
                LabelResourceName.Initialize();
                LabelResourceName.SetPosition(X1, Y + 16, Width1, 18);
                LabelResourceName.SetText(ResourceName);
                LabelResourceName.SetTextAlign(TextAlign.CenterLeft);
                LabelResourceName.SetFontSize(18);
                _ScrollPanel.AddChild(LabelResourceName);

                Label LabelResourceFilename = new Label();
                LabelResourceFilename.Initialize();
                LabelResourceFilename.SetPosition(X1, Y + 38, Width1, 14);
                LabelResourceFilename.SetText(StandardFilename);
                LabelResourceFilename.SetTextAlign(TextAlign.CenterLeft);
                LabelResourceFilename.SetFontSize(14);
                _ScrollPanel.AddChild(LabelResourceFilename);

                Y += 64 + 3;
            }
            _ScrollPanel.SetSize(_ScrollView.GetWidth() - 22, Y);
            _ScrollView.UpdateScrollBar();
        }

        public void ShowUI(List<string> ExtensionList, int X, int Y, int Width, int Height, FileSelectCallback FileSelectCallback)
        {
            _ExtensionList = ExtensionList;
            _FileSelectCallback = FileSelectCallback;

            int X1 = X;
            int Y1 = Y + 1;
            int X2 = X + Width;
            int Y2 = Y + Height - 1;
            int MenuX = 0;
            int MenuY = 0;

            Control Root = GetUIManager().GetRoot();

            InitializeUI(GetUIManager());

            int MenuWidth = _Panel.GetWidth();
            int MenuHeight = _Panel.GetHeight();

            int RootWidth = Root.GetWidth();
            int RootHeight = Root.GetHeight();
            MenuX = X1;
            if (X1 + MenuWidth > RootWidth)
            {
                if (X2 - MenuWidth >= 0)
                {
                    MenuX = X2 - MenuWidth;
                }
                else
                {
                    MenuX = RootWidth - MenuWidth;
                }
                if (MenuX < 0)
                {
                    MenuX = 0;
                }
            }
            MenuY = Y2;
            if (Y2 + MenuHeight > RootHeight)
            {
                if (Y1 - MenuHeight >= 0)
                {
                    MenuY = Y1 - MenuHeight;
                }
                else
                {
                    MenuY = RootHeight - MenuHeight;
                }
                if (MenuY < 0)
                {
                    MenuY = 0;
                }
            }

            _Panel.SetPos(MenuX, MenuY);
            _Panel.SetVisible(true);
        }

        public void ShowUI(List<string> ExtensionList, Control Control, FileSelectCallback FileSelectCallback)
        {
            int X = Control.GetScreenX();
            int Y = Control.GetScreenY();
            int Width = Control.GetWidth();
            int Height = Control.GetHeight();
            ShowUI(ExtensionList, X, Y, Width, Height, FileSelectCallback);
        }

        public void HideUI()
        {
            OperationQueue.GetInstance().AddOperation(() =>
            {
                _BackgroundMask.Close();
                if (_Panel != null)
                {
                    UIManager UIManager = GetUIManager();
                    Control Root = UIManager.GetRoot();
                    _Panel.SetVisible(false);
                    Root.RemoveChild(_Panel);
                    _Panel = null;
                }
            });
        }

        public bool GetVisible()
        {
            if (_Panel != null)
            {
                return _Panel.GetVisible();
            }
            return false;
        }

        void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY))
            {
                bContinue = false;
            }
        }

        void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY))
            {
                bContinue = false;
            }
        }

        void OnEditPatternCharInpupted(Control Sender, char Char, ref bool bContinue)
        {
            FilterResourceList();
            UpdateResourceListUI();
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && (Char == '\r' || Char == '\n'))
            {
                if (_FilteredResourceList.Count == 1)
                {
                    string ResourceFilename = _FilteredResourceList[0];
                    TriggerSelection(ResourceFilename);
                }
            }
        }

        void OnEditPatternKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && Key == Key.Escape)
            {
                HideUI();
            }
        }

        void OnButtonResourceClicked(Button Sender)
        {
            Button ButtonResource = Sender;
            string ResourceFilename = ButtonResource.GetTagString1();
            TriggerSelection(ResourceFilename);
        }

        void TriggerSelection(string ResourceFilename)
        {
            if (ResourceFilename != "")
            {
                if (_FileSelectCallback != null)
                {
                    _FileSelectCallback(ResourceFilename);
                }
            }
            HideUI();
        }

        void OnBackgroundMaskClose(BackgroundMask Sender)
        {
            HideUI();
        }
    }
}