using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class ShaderErrorInfo
    {
        public string ErrorLocation;
        public string ErrorInformation;
        public string ErrorCode;
    }

    class ShaderLogTranslator
    {
        static ShaderLogTranslator _Instance = new ShaderLogTranslator();

        List<ShaderErrorInfo> _ShaderErrorInfoList;

        public static ShaderLogTranslator GetInstance()
        {
            return _Instance;
        }

        public ShaderLogTranslator()
        {
            _ShaderErrorInfoList = new List<ShaderErrorInfo>();
        }

        public bool IsShaderLog(string Log)
        {
            return Log.StartsWith("[DirectXShaderCompiler.cpp") && Log.Contains("cross::editor::DirectXShaderCompiler::Compile]:defines");
        }

        ShaderErrorInfo FindErrorInfo(string ErrorLocation, string ErrorInformation)
        {
            foreach (ShaderErrorInfo ShaderErrorInfo in _ShaderErrorInfoList)
            {
                if (ShaderErrorInfo.ErrorLocation == ErrorLocation &&
                    ShaderErrorInfo.ErrorInformation == ErrorInformation)
                {
                    return ShaderErrorInfo;
                }
            }
            return null;
        }

        void AddErrorInfo(string ErrorInfo)
        {
            string ErrorInfo1 = ErrorInfo.Trim();
            const string ConstString1 = ": ";
            const string ConstString2 = "\n";
            int Index1 = ErrorInfo1.IndexOf(ConstString1);
            if (Index1 >= 0)
            {
                Index1 += ConstString1.Length;
                string ErrorLocation = ErrorInfo1.Substring(0, Index1);
                string String1 = ErrorInfo1.Substring(Index1);
                int Index2 = String1.IndexOf(ConstString2);
                if (Index2 >= 0)
                {
                    string ErrorInformation = String1.Substring(0, Index2);
                    if (FindErrorInfo(ErrorLocation, ErrorInformation) == null)
                    {
                        ShaderErrorInfo ShaderErrorInfo = new ShaderErrorInfo();
                        ShaderErrorInfo.ErrorLocation = ErrorLocation;
                        ShaderErrorInfo.ErrorInformation = ErrorInformation;
                        ShaderErrorInfo.ErrorCode = String1.Substring(Index2 + ConstString2.Length);
                        _ShaderErrorInfoList.Add(ShaderErrorInfo);
                    }
                }
            }
        }

        public void TranslateShaderLog(string Log)
        {
            _ShaderErrorInfoList.Clear();

            string LogString = Log;

            LogString = LogString.Trim();
            int Index1 = LogString.IndexOf(":/");     // D:/
            int Index2 = LogString.IndexOf("In file included from");
            if (Index1 >= 0 || Index2 >= 0)
            {
                int Index3 = int.MaxValue;
                if (Index1 > 0)
                {
                    Index3 = Math.Min(Index3, Index1);
                }
                if (Index2 > 0)
                {
                    Index3 = Math.Min(Index3, Index2);
                }
                string LogStringWithOutDefines;
                if (Index3 == Index1)
                {
                    LogStringWithOutDefines = LogString.Substring(Index1 - 1);    // Skips "defines"
                }
                else //if (Index3 == Index2)
                {
                    LogStringWithOutDefines = LogString.Substring(Index2);    // Skips "defines"
                }
                string[] InfoItemStrings = LogStringWithOutDefines.Split('^', '~');
                foreach (string InfoItemString in InfoItemStrings)
                {
                    string InfoItemStringWithTail = InfoItemString + "^";
                    AddErrorInfo(InfoItemStringWithTail);
                }
            }

            foreach (ShaderErrorInfo ShaderErrorInfo in _ShaderErrorInfoList)
            {
                ConsoleUI ConsoleUI = ConsoleUI.GetInstance();
                LogMessageType LogMessageType = LogMessageType.Information;
                if (ShaderErrorInfo.ErrorInformation.StartsWith("error:"))
                {
                    LogMessageType = LogMessageType.Error;
                }
                else if (ShaderErrorInfo.ErrorInformation.StartsWith("warning:"))
                {
                    LogMessageType = LogMessageType.Warning;
                }
                ConsoleUI.RealAddLogItem(LogMessageType, ShaderErrorInfo.ErrorLocation + "\n" + ShaderErrorInfo.ErrorInformation + "\n" + ShaderErrorInfo.ErrorCode);

                MainUI.GetInstance().ActivateDockingCard_Console();
            }
        }
    }
}
