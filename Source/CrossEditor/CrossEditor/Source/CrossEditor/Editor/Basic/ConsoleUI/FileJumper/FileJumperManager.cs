using System.Collections.Generic;

namespace CrossEditor
{
    public class FileJumperManager
    {
        static FileJumperManager _Instance = new FileJumperManager();

        List<FileJumper> FileJumpers;

        public static FileJumperManager GetInstance()
        {
            return _Instance;
        }

        FileJumperManager()
        {
            FileJumpers = new List<FileJumper>();

            RegisterFileJumper(new SimpleFileJumper());
        }

        public void RegisterFileJumper(FileJumper FileJumper)
        {
            FileJumpers.Add(FileJumper);
        }

        public bool JumpFile(string Filename, int Line)
        {
            int Count = FileJumpers.Count;
            for (int i = Count - 1; i >= 0; i++)
            {
                FileJumper FileJumper = FileJumpers[i];
                if (FileJumper.JumpFile(Filename, Line))
                {
                    return true;
                }
            }
            return false;
        }
    }
}
