using CEngine;
using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class GameScene : SceneBase
    {
        static GameScene _Instance = new GameScene();
        public bool _IsPIEPlaying;
        Entity _DebugAnimatorEntity;

        public static GameScene GetInstance()
        {
            return _Instance;
        }

        public GameScene()
        {
            _bEditable = false;
            _IsPIEPlaying = false;
            _DebugAnimatorEntity = null;
        }

        #region Init&Load Scene

        protected async override System.Threading.Tasks.Task<bool> LoadSceneImp(string sceneName)
        {
            InitWorld();
            InitCallback();
            await System.Threading.Tasks.Task.Run(() => { });
            return true;
        }

        protected override void InitWorld()
        {
            if (_World != null)
            {
                if (_World.Root != null)
                {
                    _World.Root.ResetWorld();
                }
                _CrossEngine.DestroyWorld(_World);
            }
            _World = _CrossEngine.CreateWorld(GetSceneName(), WorldTypeTag.PIEWorld);
            _World.Initialize();

            _ImageScene = EditorUICanvas.GetInstance().CreateSceneImage(_World, _Width, _Height);

            Clicross.GameWorldInterface.World_LoadScene(_World._WorldInterface, _SceneTemplateFilename);
        }

        public void StartPIEPlay()
        {
            string currentscenefile = EditorScene.GetInstance().GetCurrentSceneFilename();
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string tempworldfilepath = ProjectDirectory + "/Intermediate/" + "PIE" + PathHelper.GetFileName(currentscenefile);
            Clicross.GameWorldInterface.World_SaveScene(EditorScene.GetInstance().GetWorld()._WorldInterface, tempworldfilepath);
            _SceneTemplateFilename = tempworldfilepath;
            LoadScene();

            _IsPIEPlaying = true;
            GetSceneUI().GetPanel().SetCanFocus(true);
            GetSceneUI().GetPanel().SetFocus();
            GetSceneUI().GetDockingCard().SetFocus();
            //Runtime.EditorInput_OnWindowActive(_CrossEngine.GetNativePointer(), true);

            MainUI.GetInstance().ActivateDockingCard_Game();
            GameToolBarUI.GetInstance().TogglePlayButton();
            Background.SuspendBackgroundThreads();
            CrossEngineApi.StartPIEPlay();
        }

        public void StopPIEPlay()
        {
            _IsPIEPlaying = false;
            GetSceneUI().GetPanel().SetCanFocus(false);
            GetSceneUI().GetPanel().ReleaseMouse();
            ClearSelection();
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            InspectorUI.SetObjectInspected(null);
            InspectorUI.InspectObject();
            if (_World != null)
            {
                SceneRuntime.GetInstance().FlushRenderingCommand();
                GetWorld().SetWorldEnable(false);
                _CrossEngine.DestroyWorld(_World);
                _World = null;
            }
            MainUI.GetInstance().ActivateDockingCard_Scene();
            Clicross.EditorApplication.EditorInput_OnWindowActive(_CrossEngine._CrossEngineInterface, false);

            GameToolBarUI.GetInstance().TogglePlayButton();
            DebugAnimatorEntity = null;
            Background.ResumeBackgroundThreads();
            CrossEngineApi.StopPIEPlay();
        }

        #endregion

        #region Get/Set

        protected override string GetSceneName()
        {
            return "WorldPIE" + EditorScene.GetInstance().GetCurrentSceneFilename();
        }

        public bool GetIsPIEPlaying()
        {
            return _IsPIEPlaying;
        }

        private void GetEntityUsingStbInternal(Entity e, string GraphName, ref List<Entity> entityList)
        {
            if (e.HasComponent(typeof(Animator)))
            {
                Animator animator = e.GetComponent(typeof(Animator)) as Animator;
                if (animator.IsUsingSpecificStb(GraphName))
                {
                    entityList.Add(e);
                }
            }
            foreach (var child in e.Children)
            {
                GetEntityUsingStbInternal(child, GraphName, ref entityList);
            }
        }

        public void GetPIEEntityUsingStb(string GraphName, ref List<Entity> entityList)
        {
            entityList.Clear();
            if (_World != null)
            {
                GetEntityUsingStbInternal(_World.Root, GraphName, ref entityList);
            }
        }

        #endregion

        #region Update

        protected override void UpdateImp()
        {
            if (_World.Enable && _bVisible)
            {
                // Do something
            }
        }

        public override void DrawScene()
        {
            if (_IsPIEPlaying)
            {
                base.DrawScene();
            }
        }

        #endregion

        public Entity DebugAnimatorEntity
        {
            get
            {
                if (_World == null)
                {
                    return null;
                }
                return _DebugAnimatorEntity;
            }
            set
            {
                //Set new one
                if (value != null)
                {
                    Animator newAnimator = value.GetComponent(typeof(Animator)) as Animator;
                    DebugHelper.Assert(newAnimator != null);
                    newAnimator.SetIsBeingDebugged(true);
                }

                //Reset old one
                if (_DebugAnimatorEntity != null && _DebugAnimatorEntity != value)
                {
                    Animator oldAnimator = _DebugAnimatorEntity.GetComponent(typeof(Animator)) as Animator;
                    oldAnimator.SetIsBeingDebugged(false);
                }
                _DebugAnimatorEntity = value;
            }
        }
    }
}