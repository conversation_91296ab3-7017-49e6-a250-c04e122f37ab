using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using FloatPtr = System.IntPtr;

namespace CrossEditor
{
    class GameSceneUI : SceneUIBase
    {
        static GameSceneUI _Instance = new GameSceneUI();

        Panel _GamePanel;

        Clicross.CrossEngine _CrossEnginePtr;

        Dictionary<Key, bool> _PressedKey = new Dictionary<Key, bool>();

        EditorFetchMousePosCallBack _FetchMousePosCallBack;
        EditorShowMouseCursorCallBack _ShowMouseCursorCallBack;
        EditorLockMouseCursorCallBack _LockMouseCursorCallBack;
        EditorGeneralCallBack _UnLockMouseCursorCallback;

        bool _MouseLocked = false;

        bool _RecordVideoPIE = false;

        Button _ReminderBtn;

        public bool IsPIEPlaying
        {
            get { return GetGameScene().GetIsPIEPlaying(); }
        }

        public static GameSceneUI GetInstance()
        {
            return _Instance;
        }

        public GameSceneUI()
        {
            _Name = "Game";
        }

        public override bool Initialize()
        {
            InitPanel();
            InitScene(GameScene.GetInstance());

            _GamePanel = new Panel();
            _GamePanel.Initialize();
            _GamePanel.SetPosition(0, 0, 512, 512);
            _GamePanel.PaintEvent += OnGamePanelPaint;
            _GamePanel.LeftMouseDownEvent += OnGamePanelLeftMouseDown;
            _GamePanel.LeftMouseUpEvent += OnGamePanelLeftMouseUp;
            _GamePanel.RightMouseDownEvent += OnGamePanelRightMouseDown;
            _GamePanel.RightMouseUpEvent += OnGamePanelRightMouseUp;
            _GamePanel.MiddleMouseDownEvent += OnGamePanelMiddleMouseDown;
            _GamePanel.MiddleMouseUpEvent += OnGamePanelMiddleMouseUp;
            _GamePanel.MouseMoveEvent += OnGamePanelMouseMove;
            _GamePanel.MouseWheelEvent += OnGamePanelMouseWheel;
            _Panel.AddChild(_GamePanel);

            _ReminderBtn = new Button();
            _ReminderBtn.Initialize();
            _ReminderBtn.SetText(" Click for Mouse Control ");
            _ReminderBtn.SetFontSize(16);
            _ReminderBtn.SetTextOffsetY(2);
            _ReminderBtn.SetPosition(8, 8, _ReminderBtn.CalculateTextWidth() + 8, 20);
            _ReminderBtn.SetBorderColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _ReminderBtn.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            _ReminderBtn.SetEnable(false);
            _ReminderBtn.SetVisible(false);
            _GamePanel.AddChild(_ReminderBtn);

            _CrossEnginePtr = CrossEngine.GetInstance()._CrossEngineInterface;

            // Init Engine Input by Editor Input( Actually by GameUI Input)
            InitializeEngineInput();

            base.Initialize(_Name, _Panel);
            return true;
        }

        protected override void InitPanel()
        {
            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.KeyDownEvent += OnPanelKeyDown;
            _Panel.KeyUpEvent += OnPanelKeyUp;
            _Panel.CharInputEvent += OnPanelCharInput;
            _Panel.ApplicationActivateEvent += OnPanelApplicationActivate;
        }

        bool InitializeEngineInput()
        {
            if (!Clicross.EditorApplication.EditorInput_InitializeEngineInput(_CrossEnginePtr))
            {
                return false;
            }

            // Get mouse position
            _FetchMousePosCallBack = (FloatPtr OutX, FloatPtr OutY) =>
            {
                Device Device = GetDevice();

                float[] X = new float[1];
                float[] Y = new float[1];
                X[0] = Device.GetMouseX() + Device.GetX();
                Y[0] = Device.GetMouseY() + Device.GetY();

                Marshal.Copy(X, 0, OutX, 1);
                Marshal.Copy(Y, 0, OutY, 1);

            };
            if (!Clicross.EditorApplication.EditorInput_SetFetchMousePosCallBack(_CrossEnginePtr, Marshal.GetFunctionPointerForDelegate(_FetchMousePosCallBack)))
            {
                return false;
            }

            // Show/Hide Mouse
            _ShowMouseCursorCallBack = (bool bShow) =>
            {
                Device Device = GetDevice();
                if (bShow)
                {
                    Device.ShowMouseCursor();
                }
                else
                {
                    Device.HideMouseCursor();
                }
            };

            if (!Clicross.EditorApplication.EditorInput_SetShowMouseCursorCallBack(_CrossEnginePtr, Marshal.GetFunctionPointerForDelegate(_ShowMouseCursorCallBack)))
            {
                return false;
            }

            // Lock Mouse
            _LockMouseCursorCallBack = (float X, float Y, float Width, float Height) =>
            {
                if (!IsMouseLocked())
                {
                    Device Device = GetDevice();
                    Device.LockMouse((int)X, (int)Y, (int)Width, (int)Height);
                    _MouseLocked = true;
                    // Device.HideMouseCursor();
                }
            };

            if (!Clicross.EditorApplication.EditorInput_SetLockMouseCursorCallBack(_CrossEnginePtr, Marshal.GetFunctionPointerForDelegate(_LockMouseCursorCallBack)))
            {
                return false;
            }

            // Unlock Mouse
            _UnLockMouseCursorCallback = () =>
            {
                if (IsMouseLocked())
                {
                    Device Device = GetDevice();
                    Device.UnlockMouse();
                    _MouseLocked = false;
                    Device.ShowMouseCursor();
                }
            };

            if (!Clicross.EditorApplication.EditorInput_SetUnLockMouseCursorCallBack(_CrossEnginePtr, Marshal.GetFunctionPointerForDelegate(_UnLockMouseCursorCallback)))
            {
                return false;
            }

            return true;
        }

        public GameScene GetGameScene()
        {
            return _Scene as GameScene;
        }

        # region Mouse & Key Event

        void OnPanelKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                //Console.WriteLine(string.Format("KEY: {0} Down", Key.ToString()));

                Device Device = GetDevice();
                bool bShift = Device.IsShiftDown();
                if (bShift && Key == Key.Num1)
                {
                    Clicross.EditorApplication.EditorInput_OnWindowActive(_CrossEnginePtr, false);
                    _PressedKey.Clear();
                }
                else if (Key == Key.Escape && bShift)
                {
                    GetGameScene().StopPIEPlay();
                    Clicross.EditorApplication.EditorInput_OnWindowActive(_CrossEnginePtr, false);
                    _PressedKey.Clear();
                }
                else if (Key == Key.R && _PressedKey.ContainsKey(Key.Control))
                {
                    _RecordVideoPIE = !_RecordVideoPIE;

                    bool IsRepeat = _PressedKey.ContainsKey(Key) && _PressedKey[Key] == true;
                    Clicross.EditorApplication.EditorInput_OnKeyDown(_CrossEnginePtr, (int)Key, (sbyte)KeyToChar(Key), IsRepeat);
                    _PressedKey[Key] = true;

                    if (_RecordVideoPIE)
                        SetupRecordCapturePIE();
                    else
                        EndRecordVideoPIE();
                }
                else
                {
                    bool IsRepeat = _PressedKey.ContainsKey(Key) && _PressedKey[Key] == true;
                    Clicross.EditorApplication.EditorInput_OnKeyDown(_CrossEnginePtr, (int)Key, (sbyte)KeyToChar(Key), IsRepeat);
                    _PressedKey[Key] = true;
                }
            }
        }

        void OnPanelKeyUp(Control Sender, Key Key, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                //Console.WriteLine(string.Format("KEY: {0} Up", Key.ToString()));

                Clicross.EditorApplication.EditorInput_OnKeyUp(_CrossEnginePtr, (int)Key, (sbyte)KeyToChar(Key), false);
                _PressedKey[Key] = false;
            }
        }

        void OnPanelCharInput(Control Sender, char Char, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                //Console.WriteLine(string.Format("Char Input: {0}", Char));

                Clicross.EditorApplication.EditorInput_OnCharInput(_CrossEnginePtr, (sbyte)Char, false);
            }
        }

        void OnGamePanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (GetGameUIActive())
            {
                if (!IsMouseLocked())
                    Clicross.EditorApplication.EditorInput_OnWindowActive(_CrossEnginePtr, true);

                //Console.WriteLine(string.Format("RightMouseDown"));
                Sender.CaptureMouse();

                Device Device = GetDevice();
                int Left = Device.GetX();
                int Top = Device.GetY();
                Clicross.EditorApplication.EditorInput_OnMouseDown(_CrossEnginePtr, (int)Key.LeftButton, Left + MouseX, Top + MouseY);
            }
        }

        void OnGamePanelLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                //Console.WriteLine(string.Format("LeftMouseUp"));
                Sender.ReleaseMouse();

                Device Device = GetDevice();
                int Left = Device.GetX();
                int Top = Device.GetY();
                Clicross.EditorApplication.EditorInput_OnMouseUp(_CrossEnginePtr, (int)Key.LeftButton, Left + MouseX, Top + MouseY);
            }
        }

        void OnGamePanelRightMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (GetGameUIActive())
            {
                if (!IsMouseLocked())
                    Clicross.EditorApplication.EditorInput_OnWindowActive(_CrossEnginePtr, true);

                //Console.WriteLine(string.Format("RightMouseDown"));
                Sender.CaptureMouse();

                Device Device = GetDevice();
                int Left = Device.GetX();
                int Top = Device.GetY();
                Clicross.EditorApplication.EditorInput_OnMouseDown(_CrossEnginePtr, (int)Key.RightButton, Left + MouseX, Top + MouseY);
            }
        }

        void OnGamePanelRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                //Console.WriteLine(string.Format("RightMouseUp"));
                Sender.ReleaseMouse();

                Device Device = GetDevice();
                int Left = Device.GetX();
                int Top = Device.GetY();
                Clicross.EditorApplication.EditorInput_OnMouseUp(_CrossEnginePtr, (int)Key.RightButton, Left + MouseX, Top + MouseY);
            }
        }

        void OnGamePanelMiddleMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                //Console.WriteLine(string.Format("MiddleMouseDown"));
                Sender.CaptureMouse();

                Device Device = GetDevice();
                int Left = Device.GetX();
                int Top = Device.GetY();
                Clicross.EditorApplication.EditorInput_OnMouseDown(_CrossEnginePtr, (int)Key.MiddleButton, Left + MouseX, Top + MouseY);
            }
        }

        void OnGamePanelMiddleMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                //Console.WriteLine(string.Format("MiddleMouseUp"));
                Sender.ReleaseMouse();

                Device Device = GetDevice();
                int Left = Device.GetX();
                int Top = Device.GetY();
                Clicross.EditorApplication.EditorInput_OnMouseUp(_CrossEnginePtr, (int)Key.MiddleButton, Left + MouseX, Top + MouseY);
            }
        }

        void OnGamePanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                Clicross.EditorApplication.EditorInput_OnMouseMove(_CrossEnginePtr);
            }
        }

        public void OnGamePanelRawMouseMove(int MouseDeltaX, int MouseDeltaY)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                if (MouseDeltaX != 0 || MouseDeltaY != 0)
                {
                    //Console.WriteLine(string.Format("Raw Mouse Move: X: {0}, Y: {1}", MouseDeltaX, MouseDeltaY));

                    Clicross.EditorApplication.EditorInput_OnRawMouseMove(_CrossEnginePtr, MouseDeltaX, MouseDeltaY);
                }
            }
        }

        void OnGamePanelMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            if (GetGameUIActive() && IsMouseLocked())
            {
                Device Device = GetDevice();
                int Left = Device.GetX();
                int Top = Device.GetY();
                Clicross.EditorApplication.EditorInput_OnMouseWheel(_CrossEnginePtr, MouseDeltaZ, Left + MouseX, Top + MouseY);
            }
        }

        #endregion

        void OnGamePanelPaint(Control Sender)
        {
            GetScene().DrawScene();
            Sender.PaintChildren();
        }

        public void OnPanelApplicationActivate(Control Sender, bool bApplicationActivated)
        {
            //Console.WriteLine(string.Format("Device Active State: {0}", bApplicationActivated));

            if (!bApplicationActivated)
            {
                Clicross.EditorApplication.EditorInput_OnWindowActive(_CrossEnginePtr, false);
                _PressedKey.Clear();
            }
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bPositionChanged || bSizeChanged)
            {
                Device Device = GetDevice();
                int Left = Device.GetX() + _GamePanel.GetScreenX();
                int Top = Device.GetY() + _GamePanel.GetScreenY();
                Clicross.EditorApplication.EditorInput_OnWindowResize(_CrossEnginePtr, Left, Top, _GamePanel.GetWidth(), _GamePanel.GetHeight());
            }
        }

        public override void OnFocusChangedHandler(Control Sender)
        {
            base.OnFocusChangedHandler(Sender);
        }

        protected override void OnEnter()
        {
            if (GetScene().GetWorld() != null && GetGamePanelVisible())
            {
                GetScene().GetWorld().SetWorldEnableDelay(true);
            }
            if (HierarchyUI.GetInstance().GetScene() != GetScene())
            {
                HierarchyUI.GetInstance().BindScene(GetScene());
            }
        }

        protected override void OnLeave()
        {
            if (GetScene().GetWorld() != null && !GetGamePanelVisible())
            {
                GetScene().GetWorld().SetWorldEnableDelay(false);
            }
        }

        public bool IsMouseLocked()
        {
            return _MouseLocked;
        }

        public bool GetGameUIActive()
        {
            // Editor Device Should Be Active
            if (!GetDevice().GetActivated())
            {
                return false;
            }
            // GameUI Should Be Visible
            if (!GetGamePanelVisible())
            {
                return false;
            }
            // GameUI Should Be Focused
            if (!IsFocused())
            {
                return false;
            }
            // GameUI Should Be Playing PIE World
            if (!IsPIEPlaying)
            {
                return false;
            }

            return true;
        }

        public bool GetGamePanelVisible()
        {
            return (_GamePanel == null) ? false : _GamePanel.GetVisible_Recursively();
        }

        public override void Update(long TimeElapsed)
        {
            if (GetScene().GetWorld() == null)
            {
                return;
            }

            UpdateGameScene(TimeElapsed);
            GetScene().Update();

            RecordVideoPIE();
        }

        protected override void UpdateInspector()
        {
            Inspector_Entity Inspector_Entity = InspectorUI.GetInstance().GetInspector() as Inspector_Entity;
            if (Inspector_Entity != null && GetScene().GetWorld().Enable)
            {
                Inspector_Component Inspector_Component_Transform = InspectorUI.GetInstance().GetEntityInspector().FindComponentInspector<Transform>();
                if (Inspector_Component_Transform != null)
                {
                    Inspector_Component_Transform.GetComponent().SyncDataFromEngine();
                    Inspector_Component_Transform.ReadValue();
                }
                Inspector_Component Inspector_Component_Wgs84 = InspectorUI.GetInstance().GetEntityInspector().FindComponentInspector<FFSWGS84Component>();
                if (Inspector_Component_Wgs84 != null)
                {
                    //Inspector_Component_Wgs84.GetComponent().SyncDataFromEngine();
                    Inspector_Component_Wgs84.ReadValue();
                }
            }
        }

        void UpdateReminderBtn(long TimeElapsed)
        {
            if (IsPIEPlaying && !IsMouseLocked())
            {
                _ReminderBtn.SetText(" Click for Mouse Control ");
                _ReminderBtn.SetVisible(true);
            }
            else if (IsPIEPlaying && IsMouseLocked())
            {
                _ReminderBtn.SetText(" Shift+1 for Mouse Cursor ");
                _ReminderBtn.SetVisible(true);
            }
            else
            {
                _ReminderBtn.SetVisible(false);
            }
        }

        void UpdateGameScene(long TimeElapsed)
        {
            bool bVisible = _Panel.GetVisible_Recursively();
            int X = _Panel.GetScreenX();
            int Y = _Panel.GetScreenY();
            int Width = _Panel.GetWidth();
            int Height = _Panel.GetHeight();

            int BasicWidth = Width;
            int BasicHeight = Height;

            GameScene GameScene = GetGameScene();
            if (GameScene.GetWorld() != null)
            {
                EntityIDStruct Maincam = CameraSystemG.GetMainCamera(GameScene.GetWorld().GetNativePointer());
                if (Maincam.GetValue() != ulong.MaxValue)
                {
                    Entity MainCameraCSharp = new Entity(GameScene.GetWorld());
                    MainCameraCSharp.EntityID = Maincam.GetValue();
                    Camera Camera = new Camera();
                    Camera.Entity = MainCameraCSharp;
                    MainCameraCSharp.AddComponent(Camera);
                    Camera.SyncDataFromEngine();
                    if (Camera != null)
                    {
                        float Aspect = 1.0f;
                        if (Camera.Mode == CameraProjectionMode.Perspective)
                        {
                            Aspect = Camera.PerspectiveAspect;
                        }
                        else
                        {
                            Aspect = Camera.OrthogonalWidth / Camera.OrthogonalHeight;
                        }

                        BasicWidth = Width;
                        BasicHeight = (int)(BasicWidth / Aspect);
                        if (BasicHeight >= Height)
                        {
                            BasicWidth = (int)(Height * Aspect);
                            BasicHeight = Height;
                        }

                        if (BasicWidth != _GamePanel.GetWidth() || BasicWidth != _GamePanel.GetHeight())
                        {
                            _GamePanel.SetSize(BasicWidth, BasicHeight);
                            Device Device = GetDevice();
                            int Left = Device.GetX() + _GamePanel.GetScreenX();
                            int Top = Device.GetY() + _GamePanel.GetScreenY();
                            Clicross.EditorApplication.EditorInput_OnWindowResize(_CrossEnginePtr, Left, Top, _GamePanel.GetWidth(), _GamePanel.GetHeight());
                        }
                    }
                }
            }
            GameScene.SetVisible(bVisible);
            GameScene.SetPosition(X, Y, BasicWidth, BasicHeight);

            UpdateReminderBtn(TimeElapsed);
        }

        private char KeyToChar(Key InKey)
        {
            return ' ';
        }

        private void SetupRecordCapturePIE()
        {
            CinematicUI.BeginRecordVideoPIE(out int FPS, out long CaptureFrameIndex, out string CaptureStagingFolder);
            CinematicUI.GetInstance().SetVideoFPS(FPS);
            CinematicUI.GetInstance().SetCaptureFrameIndex(CaptureFrameIndex);
            CinematicUI.GetInstance().SetCaptureFolder(CaptureStagingFolder);
            EditorLogger.Log(LogMessageType.Information, string.Format("Begin Video Capture,  Directory: {0} Created! ", CaptureStagingFolder));
        }
        private void RecordVideoPIE()
        {
            if (_RecordVideoPIE && IsPIEPlaying)
            {
                //EditorLogger.Log(LogMessageType.Error, "Begin Record");
                _ReminderBtn.SetVisible(false);

                long CaptureFrameIndex = CinematicUI.GetInstance().GetCaptureFrameIndex();
                CinematicUI.GetInstance().SetCaptureFrameIndex(++CaptureFrameIndex);

                string CaptureStagingFolder = CinematicUI.GetInstance().GetCaptureFolder();
                Device Dev = GetDevice();
                CinematicUI.DoRecordVideoPIE(Dev, CaptureStagingFolder, CaptureFrameIndex);

                EditorLogger.Log(LogMessageType.Information, string.Format("Recording Video Capture {0} !", CaptureFrameIndex));
            }
            else if (!_RecordVideoPIE && IsPIEPlaying)
            {
                //EditorLogger.Log(LogMessageType.Error, "End Record");
                _ReminderBtn.SetVisible(true);
            }
        }

        private void EndRecordVideoPIE()
        {
            int FPS = CinematicUI.GetInstance().GetVideoFPS();
            string CaptureFolder = CinematicUI.GetInstance().GetCaptureFolder();
            CinematicUI.EndRecordVideoPIE(FPS, CaptureFolder);

            EditorLogger.Log(LogMessageType.Information, string.Format("Stop Recording Video Capture and Generate Video !"));
        }

    }
}
