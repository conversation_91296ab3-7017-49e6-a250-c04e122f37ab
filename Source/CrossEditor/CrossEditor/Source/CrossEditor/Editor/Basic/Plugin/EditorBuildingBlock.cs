using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    public class EditorBuildingBlock
    {
        public virtual void Initialize()
        {

        }

        public void Update()
        {
            UpdateImp();
        }

        public virtual void UpdateImp() { }
    }

    // a little bit duplicated with pluginManager in EditorUI dlls
    public class BuildingBlockManager
    {
        public List<EditorBuildingBlock> editorBuildingBlocks = new List<EditorBuildingBlock>();

        public List<Assembly> loadedPlugins = new List<Assembly>();
        public BuildingBlockManager() { }

        public void Load(Clicross.CrossEngine crossEngine)
        {
            var modules = crossEngine.GetEditorModeModules();

            for (int i = 0; i < modules.mNames.Count; i++)
            {
                var module = modules.mNames[i];
                var path = modules.mPathes[i];
                if (module != "")
                {
                    var EditorName = module + "Editor.dll";
                    var assemblename = Path.Join(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), EditorName);
                    if (System.IO.File.Exists(assemblename))
                    {
                        loadedPlugins.Add(Assembly.LoadFrom(assemblename));
                        Assembly.LoadFrom(assemblename.Replace("Editor.dll", "_Cli.dll"));
                    }
                    else
                    {

                        assemblename = Path.Join(Path.GetDirectoryName(path), EditorName);
                        if (System.IO.File.Exists(assemblename))
                        {
                            loadedPlugins.Add(Assembly.LoadFrom(assemblename));
                            Assembly.LoadFrom(assemblename.Replace("Editor.dll", "_Cli.dll"));
                        }
                    }
                }
            }
        }


        public void Initialze(Assembly ass)
        {
            // reflect or load to get building blocks
            var buildingblocks = from t in ass.GetTypes() where t.IsClass && t.BaseType == typeof(EditorBuildingBlock) select t;

            foreach (var item in buildingblocks)
            {
                editorBuildingBlocks.Add((EditorBuildingBlock)Activator.CreateInstance(item));
            }

            foreach (var block in editorBuildingBlocks)
            {
                block.Initialize();
            }
        }

        public void Update()
        {
            foreach (var block in editorBuildingBlocks)
            {
                block.Update();
            }
        }
    }


}
