using Clicross;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_RenderState : Inspector
    {
        RenderStateInfo renderStateInfo;
        MaterialEditor mMaterialEditorContext;

        public Inspector_RenderState()
        {

        }
        public void SetMaterialEditorContext(MaterialEditor context)
        {
            mMaterialEditorContext = context;
        }
        public override void InspectObject(object Object, object Tag = null)
        {
            renderStateInfo = Object as RenderStateInfo;
            ClearChildInspectors();
            Inspector_GroupedProperty Inspector_Title = new Inspector_GroupedProperty(renderStateInfo, "Advanced Material Settings");
            AddChildInspector(Inspector_Title);
            Inspector_Title.BuildPropertyInspector = () =>
            {
                var renderStateTuples = renderStateInfo.mRenderStates.ToList();
                var passNames = renderStateInfo.mPassID.ToList();
                for (int i = 0; i < renderStateTuples.Count; i++)
                {
                    Inspector_GroupedProperty Inspector_GroupedProperty = new Inspector_GroupedProperty(renderStateTuples[i], passNames[i]);
                    Inspector_GroupedProperty.BuildPropertyInspector = () =>
                    {
                        Inspector_Struct_With_Property mInspector = new Inspector_Struct_With_Property();
                        //Inspector mInspector = InspectorManager.GetInstance().CreatePropertyInspector("Struct", false);
                        mInspector.SetPropertyModifiedFunction(OnRenderStatePropertyModified);
                        mInspector.SetIndent(20);
                        Inspector_GroupedProperty.AddChildInspector(mInspector);
                        mInspector.InspectObject(renderStateTuples[i]);
                    };
                    Inspector_Title.AddChildInspector(Inspector_GroupedProperty);
                    Inspector_GroupedProperty.InspectObject(renderStateTuples[i]);
                }
            };
            Inspector_Title.InspectObject(renderStateInfo);
        }

        private void OnRenderStatePropertyModified(object PropertyOwner, PropertyInfo Property)
        {
            mMaterialEditorContext.OnMaterialDefinesChange();
            PropertyModifiedEvent?.Invoke(PropertyOwner, Property);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
        }

        public override int GetIndent()
        {
            return 0;
        }
    }
}
