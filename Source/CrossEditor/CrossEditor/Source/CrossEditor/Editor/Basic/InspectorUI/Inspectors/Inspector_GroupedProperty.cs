using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    // Only Inspect Properties from specific object in the same group
    class Inspector_GroupedProperty : Inspector
    {
        List<PropertyInfo> _Properties = new List<PropertyInfo>();
        protected object _PropertyOwner = null;
        protected string _GroupName = "";

        protected Button _ButtonBar;
        protected Label _LabelName;

        public GetPropertyValueFunction GetPropertyValueFunction;
        public SetPropertyValueFunction SetPropertyValueFunction;

        public Action BuildPropertyInspector;

        public Inspector_GroupedProperty(object PropertyOwner, string GroupName)
        {
            _PropertyOwner = PropertyOwner;
            _GroupName = GroupName;
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            DebugHelper.Assert(Object == _PropertyOwner);

            _Properties = GetGroupProperties();

            _ButtonBar = new Button();
            _ButtonBar.Initialize();
            _ButtonBar.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.ClickedEvent += OnButtonBarClicked;
            _SelfContainer.AddChild(_ButtonBar);

            InitializeCheckExpand();
            _ButtonBar.AddChild(_CheckExpand);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetText(_GroupName);
            _LabelName.SetFontSize(Inspector_Property.PROPERTY_FONT_SIZE);
            _LabelName.SetTextAlign(TextAlign.CenterLeft);
            _ButtonBar.AddChild(_LabelName);

            RefreshChildInspectors();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            _SelfContainer.SetPosition(0, Y, Width, DEFAULT_HEIGHT);
            _ButtonBar.SetPosition(0, 0, Width, DEFAULT_HEIGHT);
            Y += DEFAULT_HEIGHT;

            _CheckExpand.SetPosition(GetIndent() + SPAN_X - 20, 0, 20, 20);
            int LabelNameWidth = _LabelName.CalculateTextWidth();
            _LabelName.SetPosition(_CheckExpand.GetEndX(), 0, LabelNameWidth, 20);

            base.UpdateLayout(Width, ref Y);
        }

        protected void RefreshChildInspectors()
        {
            ClearChildInspectors();

            if (BuildPropertyInspector != null)
            {
                BuildPropertyInspector.Invoke();
            }
            else
            {
                foreach (PropertyInfo PropertyInfo in _Properties)
                {
                    AddPropertyInspector(PropertyInfo, _PropertyOwner);
                }
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public override void SetPropertyReadOnly(string propertyName, bool readOnly)
        {
            foreach (Inspector inspector in _ChildInspectors)
            {
                if (inspector.GetChildInspectors().Count > 0)
                {
                    inspector.SetPropertyReadOnly(propertyName, readOnly);
                }
                else
                {
                    if (inspector.GetName() == propertyName)
                    {
                        inspector.SetPropertyReadOnly(propertyName, readOnly);
                    }
                }
            }
        }

        protected virtual List<PropertyInfo> GetGroupProperties()
        {
            List<PropertyInfo> Properties = new List<PropertyInfo>();
            if (_PropertyOwner == null)
            {
                return Properties;
            }

            Type Type = _PropertyOwner.GetType();
            List<PropertyInfo> AllProperties = PropertyCollector.CollectPropertiesOfType(Type);
            foreach (PropertyInfo PropertyInfo in AllProperties)
            {
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo, _PropertyOwner);
                if (PropertyInfoAttribute.Category != "" && PropertyInfoAttribute.Category == _GroupName)
                {
                    Properties.Add(PropertyInfo);
                }
            }

            return Properties;
        }

        protected void OnButtonBarClicked(Button Sender)
        {
            bool bChecked = _CheckExpand.GetChecked();
            SetCheckExpand(!bChecked);
        }

        public override string GetName()
        {
            return base.GetName() + "_" + _GroupName;
        }

        public bool IsGroupEmpty()
        {
            return _Properties.Count == 0;
        }
    }

    class Inspector_SpecificGroupedProperty : Inspector_GroupedProperty
    {
        private List<PropertyInfo> _SpecificProperties;
        public Inspector_SpecificGroupedProperty(object PropertyOwner, string GroupName, List<PropertyInfo> Properties) : base(PropertyOwner, GroupName)
        {
            _SpecificProperties = Properties;
        }

        protected override List<PropertyInfo> GetGroupProperties()
        {
            return _SpecificProperties;
        }

    }
}
