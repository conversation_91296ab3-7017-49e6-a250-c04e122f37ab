using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Rangef : Inspector_Property
    {
        Label _LabelValueMin;
        Label _LabelValueMax;

        EditWithProgress _EditMin;
        EditWithProgress _EditMax;
        Edit _EditValueMin;
        Edit _EditValueMax;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _LabelValueMin = CreateLabelValue(Container, "Min");
            _LabelValueMax = CreateLabelValue(Container, "Max");

            _EditMin = new EditWithProgress(Container);
            _EditMin.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditMin.TextChangedEvent += OnEditValueTextChanged;
            _EditValueMin = _EditMin.GetEditValue();

            _EditMax = new EditWithProgress(Container);
            _EditMax.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditMax.TextChangedEvent += OnEditValueTextChanged;
            _EditValueMax = _EditMax.GetEditValue();

            if (_ObjectProperty.ReadOnly)
            {
                _EditMin.SetReadOnly(true);
                _EditMax.SetReadOnly(true);
            }

            ReadValue();
        }

        public Label CreateLabelValue(Control Container, string Text)
        {
            Label LabelValue = new Label();
            LabelValue.Initialize();
            LabelValue.SetText(Text);
            LabelValue.SetFontSize(PROPERTY_FONT_SIZE);
            LabelValue.SetTextAlign(TextAlign.CenterLeft);
            Container.AddChild(LabelValue);
            return LabelValue;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = (GetValueWidth() - SPAN_X * 3) / 2 - 25;
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);
            _LabelValueMin.SetPosition(0, SPAN_Y, 25, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_LabelValueMin);
            _EditMin.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditMin, 0);
            _LabelValueMax.SetPosition(0, SPAN_Y, 25, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_LabelValueMax);
            _EditMax.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditMax, 0);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            Rangef Value = (Rangef)PropertyValue;
            string MinString = MathHelper.NumberToString(Value.Min);
            string MaxString = MathHelper.NumberToString(Value.Max);
            _EditMin.SetText(MinString);
            _EditMax.SetText(MaxString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueMinString = _EditValueMin.GetText();
            string ValueMaxString = _EditValueMax.GetText();
            Rangef NewValue = new Rangef();
            NewValue.Min = MathHelper.ParseFloat(ValueMinString);
            NewValue.Max = MathHelper.ParseFloat(ValueMaxString);
            SetPropertyValue(NewValue);
        }

        void OnEditValueTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            if (Sender == _EditValueMin)
            {
                SetSubProperty("Min");
            }
            else if (Sender == _EditValueMax)
            {
                SetSubProperty("Max");
            }

            RecordAndWriteValue();

            ClearSubProperty();
        }
    }
}
