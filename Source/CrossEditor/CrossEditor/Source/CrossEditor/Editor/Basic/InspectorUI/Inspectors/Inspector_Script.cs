using CEngine;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using Clicross;
using EditorUI;
using ClassIDType = CEngine.ClassIDType;

namespace CrossEditor
{
    public delegate void OnScriptDefaultValueChanged();
    
    /// <summary>
    /// Inspector for script properties that handles JSON objects with fields that can be grouped
    /// </summary>
    public class Inspector_Script : Inspector_Property
    {
        private JObject _JsonObj;
        public OnScriptDefaultValueChanged OnScriptDefaultValueChanged;
        
        public Inspector_Script()
        {
        }

        /// <summary>
        /// Try to get a boolean value from a JSON object by key
        /// </summary>
        /// <param name="key">The key to look for</param>
        /// <param name="jObj">The JSON object to search in</param>
        /// <returns>The boolean value or false if not found</returns>
        public static bool TryGetBoolean(string key, JObject jObj)
        {
            bool value = false;
            if (jObj.TryGetValue(key, out JToken token))
            {
                value = token.Value<bool>();
            }
            return value;
        }

        /// <summary>
        /// Try to get a string value from a JSON object by key
        /// </summary>
        /// <param name="key">The key to look for</param>
        /// <param name="jObj">The JSON object to search in</param>
        /// <returns>The string value or empty string if not found</returns>
        public static string TryGetString(string key, JObject jObj)
        {
            string value;
            if (jObj.TryGetValue(key, out JToken token))
            {
                value = token.ToString();
            }
            else
            {
                value = "";
            }

            return value;
        }

        /// <summary>
        /// Try to get a numeric value from a JSON object by key
        /// </summary>
        /// <typeparam name="T">The numeric type to return</typeparam>
        /// <param name="key">The key to look for</param>
        /// <param name="jObj">The JSON object to search in</param>
        /// <returns>The numeric value or 0 if not found</returns>
        public static T TryGetNumber<T>(string key, JObject jObj)
        {
            T value;
            if (jObj.TryGetValue(key, out JToken token))
            {
                value = token.Value<T>();
                return (T)value;
            }
            else
            {
                return (T)System.Convert.ChangeType(0, typeof(T));
            }
        }

        /// <summary>
        /// Try to get a numeric value from either "Value" or "DefaultValue" field
        /// </summary>
        /// <typeparam name="T">The numeric type to return</typeparam>
        /// <param name="jObj">The JSON object to search in</param>
        /// <returns>The numeric value from "Value" or "DefaultValue"</returns>
        public static T TryGetNumberValue<T>(JObject jObj)
        {
            return hasKey("Value", jObj) ? TryGetNumber<T>("Value", jObj) : TryGetNumber<T>("DefaultValue", jObj);
        }

        /// <summary>
        /// Try to get a string value from either "Value" or "DefaultValue" field
        /// </summary>
        /// <param name="jObj">The JSON object to search in</param>
        /// <returns>The string value from "Value" or "DefaultValue"</returns>
        public static string TryGetStringValue(JObject jObj)
        {
            return hasKey("Value", jObj) ? TryGetString("Value", jObj) : TryGetString("DefaultValue", jObj);
        }

        /// <summary>
        /// Try to get a boolean value from either "Value" or "DefaultValue" field
        /// </summary>
        /// <param name="jObj">The JSON object to search in</param>
        /// <returns>The boolean value from "Value" or "DefaultValue"</returns>
        public static bool TryGetBooleanValue(JObject jObj)
        {
            return hasKey("Value", jObj) ? TryGetBoolean("Value", jObj) : TryGetBoolean("DefaultValue", jObj);
        }

        /// <summary>
        /// Check if a JSON object has a specific key
        /// </summary>
        /// <param name="key">The key to check for</param>
        /// <param name="jObj">The JSON object to check</param>
        /// <returns>True if the key exists, false otherwise</returns>
        public static bool hasKey(string key, JObject jObj)
        {
            return jObj.TryGetValue(key, out JToken token);
        }

        /// <summary>
        /// Check if a type is an integer type
        /// </summary>
        /// <typeparam name="T">The type to check</typeparam>
        /// <returns>True if the type is an integer type, false otherwise</returns>
        public static bool IsIntegerType<T>()
        {
            Type type = typeof(T);
            if (type.IsPrimitive && (type == typeof(int) || type == typeof(long) || type == typeof(short) || type == typeof(byte) ||
                                     type == typeof(uint) || type == typeof(ulong) || type == typeof(ushort) || type == typeof(sbyte)))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// Check if a type is a floating-point type
        /// </summary>
        /// <typeparam name="T">The type to check</typeparam>
        /// <returns>True if the type is a floating-point type, false otherwise</returns>
        public static bool IsFloatType<T>()
        {
            Type type = typeof(T);
            if (type == typeof(float) || type == typeof(double))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// Inspect a property and create appropriate inspectors for its fields
        /// </summary>
        /// <param name="ObjectProperty">The property to inspect</param>
        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            _JsonObj = (JObject)ObjectProperty.Object;
            InitializeCheckExpand();
            GetNameContainer().AddChild(_CheckExpand);
            
            if (_JsonObj != null)
            {
                ClearChildInspectors();
                
                if (_JsonObj.TryGetValue("Fields", out JToken token))
                {
                    var jArray = token as JArray;
                    if (jArray != null)
                    {
                        // Collect all groups
                        Dictionary<string, List<JObject>> fieldsByGroup = new Dictionary<string, List<JObject>>();
                        string defaultGroup = "Default";
                        fieldsByGroup[defaultGroup] = new List<JObject>();
                        
                        foreach (JObject obj in jArray)
                        {
                            string group = TryGetString("Group", obj);
                            if (string.IsNullOrEmpty(group))
                            {
                                group = defaultGroup;
                            }
                            
                            if (!fieldsByGroup.ContainsKey(group))
                            {
                                fieldsByGroup[group] = new List<JObject>();
                            }
                            
                            fieldsByGroup[group].Add(obj);
                        }
                        
                        // Process other groups
                        foreach (string group in fieldsByGroup.Keys.OrderBy(g => g))
                        {
                            if (fieldsByGroup[group].Count > 0)
                            {
                                // Create a grouped property inspector
                                Inspector_GroupedProperty groupedProperty = new Inspector_GroupedProperty(_JsonObj, group);
                                groupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                                groupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                                groupedProperty.BuildPropertyInspector = () => 
                                {
                                    // Add all fields for this group
                                    foreach (var obj in fieldsByGroup[group])
                                    {
                                        InspectOneFieldToGroup(obj, groupedProperty);
                                    }
                                };
                                
                                // Initialize the grouped property inspector
                                groupedProperty.InspectObject(_JsonObj);
                                
                                // Add to child inspectors
                                AddChildInspector(groupedProperty);
                            }
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// Inspect a single field and add it to a grouped property inspector
        /// </summary>
        /// <param name="inFieldObj">The field to inspect</param>
        /// <param name="groupedProperty">The grouped property inspector to add the field to</param>
        private void InspectOneFieldToGroup(JObject inFieldObj, Inspector_GroupedProperty groupedProperty)
        {
            string type = TryGetString("Type", inFieldObj);
            string name = TryGetString("Name", inFieldObj);
            Inspector_Property subobj_inspector = null;
            
            if (type == "Integer" || type == "Int")
            {
                if (IsIntegerType<int>())
                    subobj_inspector = new Inspector_Property_General();
            }
            else if (type == "Float")
            {
                if (IsFloatType<float>())
                    subobj_inspector = new Inspector_Property_Float();
            }
            else if (type == "Double")
            {
                if (IsFloatType<double>())
                    subobj_inspector = new Inspector_Property_Float();
            }
            else if (type == "Boolean")
            {
                subobj_inspector = new Inspector_Property_Bool();
            }
            else if (type == "String")
            {
                subobj_inspector = new Inspector_Property_String();
            }
            else if (type == "Resource")
            {
                subobj_inspector = (Inspector_Property_Resource)InspectorManager.GetInstance().CreatePropertyInspector("StringAsResource", false);
            }
            else if (type == "GameObject")
            {
                subobj_inspector = new Inspector_Property_Entity();
            }
            else if (type == "Struct")
            {
                subobj_inspector = new Inspector_Script()
                {
                    OnScriptDefaultValueChanged = OnScriptDefaultValueChanged
                };
            }


            if (subobj_inspector != null)
            {
                ObjectProperty scriptProperty = CreateScriptProperty(inFieldObj, type, name);
                subobj_inspector.InspectProperty(scriptProperty);
                groupedProperty.AddChildInspector(subobj_inspector);
            }
        }
        
        /// <summary>
        /// Create a script property object for a field
        /// </summary>
        /// <param name="inFieldObj">The field to create a property for</param>
        /// <param name="type">The type of the field</param>
        /// <param name="name">The name of the field</param>
        /// <returns>An ObjectProperty for the field</returns>
        private ObjectProperty CreateScriptProperty(JObject inFieldObj, string type, string name)
        {
            ObjectProperty scriptProperty = new ObjectProperty();
            scriptProperty.Object = inFieldObj;
            scriptProperty.Name = name;
            scriptProperty.PropertyInfoAttribute = new PropertyInfoAttribute();

            scriptProperty.GetPropertyValueFunction = (object obj, string propName, ValueExtraProperty ValueExtraProperty) =>
            {
                if (type == "Integer" || type == "Int")
                    return TryGetNumberValue<int>(inFieldObj);
                else if (type == "Float")
                    return TryGetNumberValue<float>(inFieldObj);
                else if (type == "Double")
                    return TryGetNumberValue<double>(inFieldObj);
                else if (type == "Boolean")
                    return TryGetBooleanValue(inFieldObj);
                else if (type == "String" || type == "Resource")
                    return TryGetStringValue(inFieldObj);
                else if (type == "GameObject")
                {
                    object Obj = InspectorUI.GetInstance().GetObjectInspected();
                    if (Obj != null && Obj.GetType() == typeof(List<Entity>))
                    {
                        var entities = Obj as List<Entity>;
                        return (entities[0]).World.Root.SearchChildByEUID(TryGetStringValue(inFieldObj));
                    }
                }
                else if (type == "Struct")
                    return inFieldObj;
                return null;
            };

            scriptProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                inFieldObj["Value"] = JToken.FromObject(PropertyValue);
                if (OnScriptDefaultValueChanged != null)
                {
                    OnScriptDefaultValueChanged();
                }
            };

            if (type == "Integer" || type == "Int")
            {
                scriptProperty.Type = typeof(int);
                scriptProperty.PropertyInfoAttribute.DefaultValue = TryGetNumberValue<int>(inFieldObj);
            }
            else if (type == "Float")
            {
                scriptProperty.Type = typeof(float);
                scriptProperty.PropertyInfoAttribute.DefaultValue = TryGetNumberValue<float>(inFieldObj);
            }
            else if (type == "Double")
            {
                scriptProperty.Type = typeof(double);
                scriptProperty.PropertyInfoAttribute.DefaultValue = TryGetNumberValue<double>(inFieldObj);
            }
            else if (type == "Boolean")
            {
                scriptProperty.Type = typeof(bool);
                scriptProperty.PropertyInfoAttribute.DefaultValue = TryGetBooleanValue(inFieldObj);
            }
            else if (type == "String")
            {
                scriptProperty.Type = typeof(string);
                scriptProperty.PropertyInfoAttribute.DefaultValue = TryGetStringValue(inFieldObj);
            }
            else if (type == "Resource")
            {
                scriptProperty.Type = typeof(string);
                scriptProperty.PropertyInfoAttribute.DefaultValue = TryGetStringValue(inFieldObj);
                scriptProperty.PropertyInfoAttribute.FileTypeDescriptor = "Script Resource Assets#nda";
                scriptProperty.PropertyInfoAttribute.ObjectClassID1 = (ClassIDType)TryGetNumber<int>("ClassID", inFieldObj);
            }
            else if (type == "GameObject")
            {
                scriptProperty.Type = typeof(string);
                scriptProperty.PropertyInfoAttribute.DefaultValue = TryGetStringValue(inFieldObj);
                scriptProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
                {
                    var entity = (Entity)PropertyValue;
                    inFieldObj["Value"] = entity.EUID;
                    if (OnScriptDefaultValueChanged != null)
                    {
                        OnScriptDefaultValueChanged();
                    }
                };
            }

            else if (type == "Struct")
            {
                scriptProperty.Type = typeof(JObject);
            }

            // Set additional properties
            JToken token;
            if (inFieldObj.TryGetValue("Min", out token))
            {
                scriptProperty.ValueMin = token.Value<decimal>();
            }
            if (inFieldObj.TryGetValue("Max", out token))
            {
                scriptProperty.ValueMax = token.Value<decimal>();
            }
            if (inFieldObj.TryGetValue("Tips", out token))
            {
                scriptProperty.PropertyInfoAttribute.ToolTips = token.Value<string>();
            }
            
            return scriptProperty;
        }

        /// <summary>
        /// Get the value of a property
        /// </summary>
        /// <param name="Object">The object containing the property</param>
        /// <param name="PropertyName">The name of the property</param>
        /// <param name="ValueExtraProperty">Extra property information</param>
        /// <returns>The value of the property</returns>
        public virtual object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            return null;
        }

        /// <summary>
        /// Set the value of a property
        /// </summary>
        /// <param name="Object">The object containing the property</param>
        /// <param name="PropertyName">The name of the property</param>
        /// <param name="PropertyValue">The new value of the property</param>
        /// <param name="SubProperty">Sub-property information</param>
        public virtual void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
        }

        /// <summary>
        /// Bind property functions to an ObjectProperty
        /// </summary>
        /// <param name="ObjectProperty">The ObjectProperty to bind functions to</param>
        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        /// <summary>
        /// Update the layout of the inspector
        /// </summary>
        /// <param name="Width">The width of the inspector</param>
        /// <param name="Y">The current Y position, will be updated</param>
        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            _CheckExpand.SetPosition(SPAN_X + GetIndent() - 20, 0, 20, 20);
            _LabelName.SetX(_CheckExpand.GetEndX());
            if (_ButtonToolTips != null)
                _ButtonToolTips.SetX(_LabelName.GetX());
        }
    }
}
