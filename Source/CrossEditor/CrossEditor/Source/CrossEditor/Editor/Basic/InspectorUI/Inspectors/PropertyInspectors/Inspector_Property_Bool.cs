using EditorUI;

namespace CrossEditor
{
    class Inspector_Property_Bool : Inspector_Property
    {
        protected Check _CheckValue;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);

            _CheckValue = new Check();
            _CheckValue.Initialize();
            _CheckValue.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _CheckValue.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _CheckValue.SetAutoCheck(true);
            _CheckValue.ClickedEvent += OnCheckValueClicked;
            _CheckValue.SetEnable(!_ObjectProperty.ReadOnly);
            GetValueContainer().AddChild(_CheckValue);

            if (_ObjectProperty.DefaultValue == null)
            {
                _ObjectProperty.DefaultValue = GetPropertyValue();
                _bCanRevert = true;
            }

            ReadValue();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            _CheckValue.SetPosition(0, SPAN_Y, 14, 14);
            GetValueContainer().FloatToLeft(_CheckValue);
        }

        public override void ReadValue()
        {
            bool bChecked = (bool)GetPropertyValue();
            _CheckValue.SetChecked(bChecked);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            bool bValue = _CheckValue.GetChecked();
            SetPropertyValue(bValue);
        }

        void OnCheckValueClicked(Check Sender)
        {
            RecordAndWriteValue();
        }
    }
}
