using CEngine;
using EditorUI;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;

namespace CrossEditor
{
    public delegate void OnPostEditChangedProperty(Clicegf.PropertyChangedEvent evt);
    public class Inspector_ComponentBase : Inspector
    {
        public enum ShowOption
        {
            None = 0,
            ShowTitle = 1,
            ShowContent = ShowTitle << 1
        }

        protected Button _ButtonBar;
        protected Panel _PanelIcon;
        protected Check _CheckEnable;
        protected Label _LabelName;
        protected Button _ButtonRefresh;
        protected Button _ButtonDocument;
        protected Button _ButtonContextMenu;
        protected bool _bTriggerRefreshMenu;

        // if this is non null, basically means this a reflected property, means the actual memory is in C++
        protected PropertyInfo _AutoExpandedStructPropertyInfo = null;
        public OnPostEditChangedProperty OnPostEditChangedProperty;


        public Inspector_ComponentBase()
        {
            _bTriggerRefreshMenu = false;
            _SelfContainer = new AlignedPanel();
            _SelfContainer.SetSize(0, 0);
            _SelfContainer.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _AutoExpandedStructPropertyInfo = null;
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            string TypeName = Object.GetType().Name;
            _ButtonBar = new Button();
            _ButtonBar.Initialize();
            _ButtonBar.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.ClickedEvent += OnButtonBarClicked;
            _SelfContainer.AddChild(_ButtonBar);

            InitializeCheckExpand();
            _ButtonBar.AddChild(_CheckExpand);

            Texture Image = null;
            string ImageFilename = string.Format("Editor/Game/{0}.png", TypeName);
            if (FileHelper.IsFileExists(ImageFilename))
            {
                Image = UIManager.LoadUIImage(ImageFilename);
            }
            else
            {
                Image = UIManager.LoadUIImage("Editor/Game/GameComponent.png");
            }

            _PanelIcon = new Panel();
            _PanelIcon.Initialize();
            _PanelIcon.SetImage(Image);
            UpdatePanelIconColor();
            _ButtonBar.AddChild(_PanelIcon);

            _CheckEnable = new Check();
            _CheckEnable.Initialize();
            _CheckEnable.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _CheckEnable.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _CheckEnable.SetAutoCheck(true);
            _CheckEnable.ClickedEvent += OnCheckEnableClicked;
            _CheckEnable.SetChecked(((ComponentBase)GetInspectedObject()).Enable);
            _ButtonBar.AddChild(_CheckEnable);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelName.SetTextAlign(TextAlign.CenterLeft);

            _LabelName.SetText(TypeName);
            _ButtonBar.AddChild(_LabelName);

            _ButtonRefresh = new Button();
            _ButtonRefresh.Initialize();
            _ButtonRefresh.SetImage(UIManager.LoadUIImage("Editor/Others/ButtonRefresh.png"));
            _ButtonRefresh.SetToolTips("Refresh");
            _ButtonRefresh.ClickedEvent += OnButtonRefreshMenuClicked;
            _ButtonBar.AddChild(_ButtonRefresh);
            _ButtonRefresh.SetVisible(false);

            _ButtonDocument = new Button();
            _ButtonDocument.Initialize();
            _ButtonDocument.SetImage(UIManager.LoadUIImage("Editor/Others/ButtonDocument.png"));
            _ButtonDocument.SetToolTips("Document");
            _ButtonDocument.ClickedEvent += OnButtonDocumentClicked;
            _ButtonBar.AddChild(_ButtonDocument);

            _ButtonContextMenu = new Button();
            _ButtonContextMenu.Initialize();
            _ButtonContextMenu.SetImage(UIManager.LoadUIImage("Editor/Others/ButtonContextMenu.png"));
            _ButtonContextMenu.SetToolTips("Menu");
            _ButtonContextMenu.ClickedEvent += OnButtonContextMenuClicked;
            _ButtonBar.AddChild(_ButtonContextMenu);

            bool showTitle = Tag == null ? true : ((ShowOption)Tag & ShowOption.ShowTitle) != ShowOption.None;
            if (!showTitle)
            {
                _ButtonBar.SetEnable(false);
                _ButtonBar.SetVisible(false);
            }
        }
        public Inspector_Property FindChildInspector(string PropertyName)
        {
            foreach (Inspector Inspector in _ChildInspectors)
            {
                Inspector_Property Inspector_Property = Inspector as Inspector_Property;
                if (Inspector_Property != null)
                {
                    string PropertyName1 = Inspector_Property.GetPropertyName();
                    if (PropertyName1 == PropertyName)
                    {
                        return Inspector_Property;
                    }
                }
            }
            return null;
        }
        public override void Update()
        {
            foreach (Inspector Inspector in _ChildInspectors)
            {
                Inspector.Update();
            }
        }
        protected bool IsComponentBaseProperties(string PropertyName)
        {
            if (PropertyName == "Enable")
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        protected virtual void UpdateContentLayout(int Width, ref int Height, ref int Y)
        {
        }
        public override void UpdateLayout(int Width, ref int Y)
        {
            int thisH = 0;
            if (_ButtonBar.GetVisible())
            {
                _ButtonBar.SetPosition(0, thisH, Width, 30);
                thisH += 30;
                Y += 10;
            }
            UpdateContentLayout(Width, ref thisH, ref Y);
            _SelfContainer.SetPosition(0, Y, Width, thisH);
            Y += thisH;

            AlignedPanel Container = _SelfContainer as AlignedPanel;
            _CheckExpand.SetPosition(0, 2, 20, 20);
            Container.FloatToLeft(_CheckExpand);
            _PanelIcon.SetPosition(0, 2, 20, 20);
            Container.FloatToLeft(_PanelIcon, 0);

            if (_CheckEnable.GetVisible())
            {
                _CheckEnable.SetPosition(0, 5, 14, 14);
                Container.FloatToLeft(_CheckEnable);
            }

            int LabelNameWidth = _LabelName.CalculateTextWidth();
            _LabelName.SetPosition(0, 2, LabelNameWidth, 20);
            Container.FloatToLeft(_LabelName);

            _ButtonContextMenu.SetPosition(0, 2, 20, 20);
            Container.FloatToRight(_ButtonContextMenu);
            _ButtonDocument.SetPosition(0, 2, 20, 20);
            Container.FloatToRight(_ButtonDocument);

            if (_ButtonRefresh.GetVisible())
            {
                _ButtonRefresh.SetPosition(0, 2, 20, 20);
                Container.FloatToRight(_ButtonRefresh);
            }

            Container.ClearLastLayout();

            base.UpdateLayout(Width, ref Y);
        }
        protected void AddMainStructChildInspectors(object AutoExpandedStruct)
        {
            Type Type = AutoExpandedStruct.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(PropertyInfo);
                if (AttributeList != null)
                {
                    AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();

                    object Value = PropertyInfo.GetValue(AutoExpandedStruct);
                    string PorpertyTypeName = Value != null ? Value.GetType().ToString() : PropertyInfo.PropertyType.ToString();
                    bool bContainsProperty = InspectorManager.GetInstance().ContainsProperty(PorpertyTypeName);
                    bool isEnum = PropertyInfo.PropertyType.IsEnum;
                    if (AttributeData != null || bContainsProperty || isEnum)
                    {
                        PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
                        if (PropertyInfoAttribute.PropertyType == "")
                        {
                            AddPropertyInspector(PropertyInfo, AutoExpandedStruct, PorpertyTypeName);
                        }
                        else
                        {
                            AddPropertyInspector(PropertyInfo, AutoExpandedStruct);
                        }

                    }
                }
            }
        }
        protected virtual object GetInspectedObject()
        {
            return null;
        }
        protected virtual void RefreshChildInspectors()
        {

        }

        protected virtual void OnButtonContextMenuClicked(Button Sender)
        {
        }
        protected virtual void OnCheckEnableClicked(Check Sender)
        {
            ((ComponentBase)GetInspectedObject()).Enable = _CheckEnable.GetChecked();
            UpdatePanelIconColor();
        }
        protected virtual void UpdatePanelIconColor()
        {
            _PanelIcon.SetEnable(((ComponentBase)GetInspectedObject()).Enable);
        }

        protected void OnButtonDocumentClicked(Button Sender)
        {
        }

        protected void OnButtonBarClicked(Button Sender)
        {
            bool bChecked = _CheckExpand.GetChecked();
            SetCheckExpand(!bChecked);
        }
        protected virtual void OnButtonRefreshMenuClicked(Button Sender)
        {
            RefreshChildInspectors();
            GetInspectorHandler().UpdateLayout();
            EditorScene.GetInstance().SetDirty();
        }
        protected object GetNewPropertyValue(object OldValue, object PropertyValue, SubProperty SubProperty)
        {
            object NewPropertyValue = PropertyValue;
            if (SubProperty != null)
            {
                if (SubProperty._SubProperty != null)
                {
                    string SubPropertyString = SubProperty._SubProperty;
                    if (PropertyValue is Vector2f)
                    {
                        Vector2f NewVector1 = (Vector2f)PropertyValue;
                        Vector2f NewVector = (Vector2f)OldValue;
                        if (SubPropertyString == "X")
                        {
                            NewVector.X = NewVector1.X;
                        }
                        else if (SubPropertyString == "Y")
                        {
                            NewVector.Y = NewVector1.Y;
                        }
                        NewPropertyValue = NewVector;
                    }
                    else if (PropertyValue is Vector3f)
                    {
                        Vector3f NewVector1 = (Vector3f)PropertyValue;
                        Vector3f NewVector = (Vector3f)OldValue;
                        if (SubPropertyString == "X")
                        {
                            NewVector.X = NewVector1.X;
                        }
                        else if (SubPropertyString == "Y")
                        {
                            NewVector.Y = NewVector1.Y;
                        }
                        else if (SubPropertyString == "Z")
                        {
                            NewVector.Z = NewVector1.Z;
                        }
                        NewPropertyValue = NewVector;
                    }
                    else if (PropertyValue is Vector4f)
                    {
                        Vector4f NewVector1 = (Vector4f)PropertyValue;
                        Vector4f NewVector = (Vector4f)OldValue;
                        if (SubPropertyString == "X")
                        {
                            NewVector.X = NewVector1.X;
                        }
                        else if (SubPropertyString == "Y")
                        {
                            NewVector.Y = NewVector1.Y;
                        }
                        else if (SubPropertyString == "Z")
                        {
                            NewVector.Z = NewVector1.Z;
                        }
                        else if (SubPropertyString == "W")
                        {
                            NewVector.W = NewVector1.W;
                        }
                        NewPropertyValue = NewVector;
                    }
                    else if (PropertyValue is Rangef)
                    {
                        Rangef NewRange1 = (Rangef)PropertyValue;
                        Rangef NewRange = (Rangef)OldValue;
                        if (SubPropertyString == "Min")
                        {
                            NewRange.Min = NewRange1.Min;
                        }
                        else if (SubPropertyString == "Max")
                        {
                            NewRange.Max = NewRange1.Max;
                        }
                        NewPropertyValue = NewRange;
                    }
                    else if (PropertyValue is Float2)
                    {
                        Float2 NewVector1 = (Float2)PropertyValue;
                        Float2 NewVector = (Float2)CloneHelper.CloneByConstructor(OldValue);
                        if (SubPropertyString == "x")
                        {
                            NewVector.x = NewVector1.x;
                        }
                        else if (SubPropertyString == "y")
                        {
                            NewVector.y = NewVector1.y;
                        }
                        NewPropertyValue = NewVector;
                    }
                    else if (PropertyValue is Float3)
                    {
                        Float3 NewVector1 = (Float3)PropertyValue;
                        Float3 NewVector = (Float3)CloneHelper.CloneByConstructor(OldValue);
                        if (SubPropertyString == "x")
                        {
                            NewVector.x = NewVector1.x;
                        }
                        else if (SubPropertyString == "y")
                        {
                            NewVector.y = NewVector1.y;
                        }
                        else if (SubPropertyString == "z")
                        {
                            NewVector.z = NewVector1.z;
                        }
                        NewPropertyValue = NewVector;
                    }
                    else if (PropertyValue is Float4)
                    {
                        Float4 NewVector1 = (Float4)PropertyValue;
                        Float4 NewVector = (Float4)CloneHelper.CloneByConstructor(OldValue);
                        if (SubPropertyString == "x")
                        {
                            NewVector.x = NewVector1.x;
                        }
                        else if (SubPropertyString == "y")
                        {
                            NewVector.y = NewVector1.y;
                        }
                        else if (SubPropertyString == "z")
                        {
                            NewVector.z = NewVector1.z;
                        }
                        else if (SubPropertyString == "w")
                        {
                            NewVector.w = NewVector1.w;
                        }
                        NewPropertyValue = NewVector;
                    }
                    else if (PropertyValue is Double3)
                    {
                        Double3 NewVector1 = (Double3)PropertyValue;
                        Double3 NewVector = (Double3)CloneHelper.CloneByConstructor(OldValue);
                        if (SubPropertyString == "x")
                        {
                            NewVector.x = NewVector1.x;
                        }
                        else if (SubPropertyString == "y")
                        {
                            NewVector.y = NewVector1.y;
                        }
                        else if (SubPropertyString == "z")
                        {
                            NewVector.z = NewVector1.z;
                        }
                        NewPropertyValue = NewVector;
                    }
                }

            }
            return NewPropertyValue;
        }

    }
    public class Inspector_GameComponent : Inspector_ComponentBase
    {
        protected GameObjectComponent _Component;

        protected List<GameObject> _GameObjects;

        public Inspector_GameComponent(List<Entity> Entities)
        {
            _GameObjects = new List<GameObject>();
            foreach (Entity Entity in Entities)
            {
                _GameObjects.Add(GameObject.GetGameObjectFromEntity(Entity));
            }
        }

        public override string GetName()
        {
            return _Component.GetType().ToString();
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Component = (GameObjectComponent)Object;

            _Component.BeforeInspectObject();

            base.InspectObject(Object, Tag);

            RefreshChildInspectors();
        }
        protected override void RefreshChildInspectors()
        {
            // clear pre inspector children
            Type Type = _Component.GetType();
            ClearChildInspectors();
            // record cur inspector categories
            HashSet<string> Categories = new HashSet<string>();

            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                if (IsComponentBaseProperties(PropertyInfo.Name) == false)
                {
                    PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);

                    if (PropertyInfoAttribute.Category == "")
                    {
                        if (PropertyInfoAttribute.bAutoExpandStruct)
                        {
                            _AutoExpandedStructPropertyInfo = PropertyInfo;
                            object AutoExpandStruct = PropertyInfo.GetValue(_Component);
                            AddMainStructChildInspectors(AutoExpandStruct);
                        }
                        else
                        {
                            AddPropertyInspector(PropertyInfo, _Component);
                        }
                    }
                    else
                    {
                        Categories.Add(PropertyInfoAttribute.Category);
                    }
                }
            }

            List<MethodInfo> methodInfos = new List<MethodInfo>(Type.GetMethods());
            foreach (MethodInfo methodInfo in methodInfos)
            {
                MethodInfoAttribute methodAttribute = MethodInfoAttribute.GetMethodInfoAttribute(methodInfo);
                if (methodAttribute.PropertyType == "Button")
                {
                    AddButtonInspector(methodInfo, _Component, methodAttribute);
                }
            }

            // Grouped Properties
            foreach (string Category in Categories)
            {
                Inspector_GroupedProperty inspector_GroupedProperty = new Inspector_GroupedProperty(_Component, Category);
                inspector_GroupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                inspector_GroupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                AddChildInspector(inspector_GroupedProperty);
                inspector_GroupedProperty.InspectObject(_Component);
            }
        }

        protected override object GetInspectedObject()
        {
            return _Component;
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
            ObjectProperty.RawSetPropertyValueFunction = RawSetPropertyValueFunction;
        }
        public virtual object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            object Value = RawGetPropertyValueFunction(_Component, PropertyName, null);
            List<object> ValueList = new List<object>();
            Type ComponentType = _Component.GetType();
            foreach (GameObject gameObject in _GameObjects)
            {
                GameObjectComponent Component = gameObject.GetComponent(ComponentType);
                object Value1 = RawGetPropertyValueFunction(Component, PropertyName, null);
                ValueList.Add(Value1);
            }
            ValueListChecker.CheckValueList(ValueExtraProperty, ValueList);
            return Value;
        }
        public virtual void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            _bTriggerRefreshMenu = false;
            bool _bTriggerRefreshMenuByMeta = false;
            Type ComponentType = _Component.GetType();
            foreach (GameObject gameObject in _GameObjects)
            {
                GameObjectComponent Component = gameObject.GetComponent(ComponentType);
                object OldValue = RawGetPropertyValueFunction(Component, PropertyName, null);
                object NewPropertyValue = PropertyValue;
                NewPropertyValue = GetNewPropertyValue(OldValue, PropertyValue, SubProperty);
                if (SubProperty != null && SubProperty._EditOperation != null)
                {
                    ObjectProperty ObjectProperty = SubProperty._ObjectProperty;
                    ObjectProperty ObjectProperty1 = new ObjectProperty();
                    ObjectProperty1.Object = Component;
                    ObjectProperty1.Name = ObjectProperty.Name;
                    ObjectProperty1.Type = ObjectProperty.Type;
                    ObjectProperty1.PropertyInfoAttribute = ObjectProperty.PropertyInfoAttribute;
                    ObjectProperty1.RawSetPropertyValueFunction = RawSetPropertyValueFunction;
                    SubProperty._EditOperation.AddModifyProperty(Component, ObjectProperty1, OldValue, NewPropertyValue);
                }
                RawSetPropertyValueFunction1(ComponentType, Component, PropertyName, NewPropertyValue, null);

                if (!_bTriggerRefreshMenuByMeta)
                {
                    var MemoberInfoArr = ComponentType.GetMember(PropertyName);
                    foreach (var MemInfo in MemoberInfoArr)
                    {
                        PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(MemInfo, _Component);
                        if (PropertyInfoAttribute.bTriggerRefresh)
                        {
                            _bTriggerRefreshMenuByMeta = true;
                            break;
                        }
                    }
                }
            }

            if (_bTriggerRefreshMenuByMeta)
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    OnButtonRefreshMenuClicked(null);
                });
            }
            EditorScene.GetInstance().SetDirty();
        }
        public override int GetIndent()
        {
            return 0;
        }
        public GameObjectComponent GetComponent()
        {
            return _Component;
        }

        public void RawSetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type ComponentType = _Component.GetType();
            foreach (GameObject gameObject in _GameObjects)
            {
                GameObjectComponent Component = gameObject.GetComponent(ComponentType);
                RawSetPropertyValueFunction1(ComponentType, Component, PropertyName, PropertyValue, SubProperty);
            }
        }

        public void RawSetPropertyValueFunction1(Type ComponentType, GameObjectComponent Component, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            bool _modified = false;
            if (_AutoExpandedStructPropertyInfo != null && IsComponentBaseProperties(PropertyName) == false)
            {
                object AutoExpandedStruct = _AutoExpandedStructPropertyInfo.GetValue(Component);
                Type Type = AutoExpandedStruct.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(AutoExpandedStruct, PropertyValue);
                    _modified = true;
                }
                _AutoExpandedStructPropertyInfo.SetValue(Component, AutoExpandedStruct);
            }
            else
            {
                if (Component.mGameObject.mEntity.World.Enable == false)
                {
                    return;
                }
                PropertyInfo PropertyInfo = ComponentType.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(Component, PropertyValue);
                    _modified = true;
                    _bTriggerRefreshMenu = true;
                }
            }

            if (_modified)
            {
                Clicegf.PropertyChangedEvent evt = new Clicegf.PropertyChangedEvent();
                evt.PropertyName = PropertyName;
                OnPostEditChangedProperty?.Invoke(evt);
            }
        }

        object RawGetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            if (_AutoExpandedStructPropertyInfo != null && IsComponentBaseProperties(PropertyName) == false)
            {
                GameObjectComponent Component = (GameObjectComponent)Object;
                object AutoExpandedStruct = _AutoExpandedStructPropertyInfo.GetValue(Component);
                Type Type = AutoExpandedStruct.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    return PropertyInfo.GetValue(AutoExpandedStruct);
                }
            }
            else
            {
                GameObjectComponent Component = (GameObjectComponent)Object;
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    return PropertyInfo.GetValue(Object);
                }
            }
            return null;
        }
        protected override void OnButtonContextMenuClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuItem MenuItem_Reset = new MenuItem();
            MenuItem_Reset.SetText("Reset");
            MenuItem_Reset.ClickedEvent += OnMenuItemResetClicked;

            MenuItem MenuItem_RemoveComponent = new MenuItem();
            MenuItem_RemoveComponent.SetText("Remove Component");
            MenuItem_RemoveComponent.ClickedEvent += OnMenuItemRemoveComponentClicked;

            MenuItem MenuItem_MoveUp = new MenuItem();
            MenuItem_MoveUp.SetText("Move Up");
            MenuItem_MoveUp.ClickedEvent += OnMenuItemMoveUpClicked;

            MenuItem MenuItem_MoveDown = new MenuItem();
            MenuItem_MoveDown.SetText("Move Down");
            MenuItem_MoveDown.ClickedEvent += OnMenuItemMoveDownClicked;

            MenuItem MenuItem_CopyComponent = new MenuItem();
            MenuItem_CopyComponent.SetText("Copy Component");
            MenuItem_CopyComponent.ClickedEvent += OnMenuItemCopyComponentClicked;

            MenuItem MenuItem_PasteComponentAsNew = new MenuItem();
            MenuItem_PasteComponentAsNew.SetText("Paste Component As New");
            MenuItem_PasteComponentAsNew.ClickedEvent += OnMenuItemPasteComponentAsNewClicked;

            MenuItem MenuItem_PasteComponentValues = new MenuItem();
            MenuItem_PasteComponentValues.SetText("Paste Component Values");
            MenuItem_PasteComponentValues.ClickedEvent += OnMenuItemPasteComponentValuesClicked;

            MenuContextMenu.AddMenuItem(MenuItem_Reset);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_RemoveComponent);
            MenuContextMenu.AddMenuItem(MenuItem_MoveUp);
            MenuContextMenu.AddMenuItem(MenuItem_MoveDown);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_CopyComponent);
            MenuContextMenu.AddMenuItem(MenuItem_PasteComponentAsNew);
            MenuContextMenu.AddMenuItem(MenuItem_PasteComponentValues);

            GameObject gameObject = _Component.mGameObject;
            if (gameObject.CanMoveUp(_Component) == false)
            {
                MenuItem_MoveUp.SetEnable(false);
            }
            if (gameObject.CanMoveDown(_Component) == false)
            {
                MenuItem_MoveDown.SetEnable(false);
            }

            MenuItem_PasteComponentAsNew.SetEnable(false);
            MenuItem_PasteComponentValues.SetEnable(false);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }
        protected void OnMenuItemResetClicked(MenuItem Sender)
        {
            Type Type = _Component.GetType();
            PropertyInfo[] Properties = Type.GetProperties();
            int PropertyCount = Properties.Length;
            List<PropertyInfo> PropertyInfoList = new List<PropertyInfo>(PropertyCount);
            List<object> OldValueList = new List<object>(PropertyCount);
            List<object> NewValueList = new List<object>(PropertyCount);
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                PropertyInfoList.Add(PropertyInfo);
                object OldValue = PropertyInfo.GetValue(_Component);
                OldValueList.Add(OldValue);
            }
            _Component.Reset();
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                object NewValue = PropertyInfo.GetValue(_Component);
                NewValueList.Add(NewValue);
            }

            EditorScene.GetInstance().SetDirty();

            EditOperation_ModifyProperties EditOperation = new EditOperation_ModifyProperties(_Component, PropertyInfoList, OldValueList, NewValueList);
            EditOperationManager.GetInstance().AddOperation(EditOperation);

            InspectorUI.GetInstance().ReadValueAndUpdateLayout();
        }

        protected void OnMenuItemRemoveComponentClicked(MenuItem Sender)
        {
            Type ComponentType = _Component.GetType();
            EditOperation_RemoveGameObjectComponents EditOperation = new EditOperation_RemoveGameObjectComponents();
            foreach (GameObject gameObject in _GameObjects)
            {
                GameObjectComponent Component = gameObject.GetComponent(ComponentType);
                gameObject.RemoveComponent(Component);
                EditOperation.AddRemoveComponentItem(gameObject, Component);
            }
            EditOperationManager.GetInstance().AddOperation(EditOperation);

            EditorScene.GetInstance().SetDirty();
            InspectorUI.GetInstance().InspectObject();
        }

        protected void OnMenuItemMoveUpClicked(MenuItem Sender)
        {
            GameObject gameObject = _Component.mGameObject;
            if (gameObject.CanMoveUp(_Component))
            {
                gameObject.MoveUp(_Component);
                InspectorUI.GetInstance().InspectObject();
            }
        }

        protected void OnMenuItemMoveDownClicked(MenuItem Sender)
        {
            GameObject gameObject = _Component.mGameObject;
            if (gameObject.CanMoveDown(_Component))
            {
                gameObject.MoveDown(_Component);
                InspectorUI.GetInstance().InspectObject();
            }
        }

        protected void OnMenuItemCopyComponentClicked(MenuItem Sender)
        {

        }

        protected void OnMenuItemPasteComponentAsNewClicked(MenuItem Sender)
        {
            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Not Implemented.");
        }

        protected void OnMenuItemPasteComponentValuesClicked(MenuItem Sender)
        {
            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Not Implemented.");
        }
    }

    public class Inspector_GameObject : Inspector_Component
    {
        public Inspector_GameObject(List<Entity> Entities) : base(Entities)
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Component = (GOSerializerComponent)Object;
            GameObject go = ((GOSerializerComponent)_Component).GameObject;
            ClearChildInspectors();
            /*
             *  1. add GameObject properties
             */
            InspectGameObjectProperties();

            /*
             *  2. add transform
             */
            {
                ComponentAttribute attribute = CrossEngine.GetInstance().EditorComponents[typeof(Transform)];

                //if (!(bIsRoot && Component1 is Transform))

                if (typeof(Inspector_Component).IsAssignableFrom(attribute.InspectorType))
                {
                    Inspector_Component comp_inspector = (Inspector_Component)Activator.CreateInstance(attribute.InspectorType, new object[] { _Entities });
                    comp_inspector.InspectObject(go.mEntity.GetComponent(typeof(Transform)));
                    AddChildInspector(comp_inspector);
                }
            }
            /*
             * 3. collect all game component for the game object
             *    for all game component
             *         collect all game object(entity) have component
             *         create inspector and set the list of entity
             *          inspector.InspectObject(game component)
             */
            for (int i = 0; i < go.mComponents.Count; i++)
            {
                var comp = go.mComponents[i];
                if (comp.GetType() == typeof(RootComponent))
                {
                    continue;
                }
                Inspector_GameComponent game_comp_inspector = null;
                {
                    List<Entity> EntitiesContainsComponent = GetEntitiesContainsComponent(_Entities, comp);

                    ComponentAttribute attribute = CrossEngine.GetInstance().EditorComponents[comp.GetType()];

                    //if (!(bIsRoot && Component1 is Transform))

                    if (typeof(Inspector_GameComponent).IsAssignableFrom(attribute.InspectorType))
                    {
                        game_comp_inspector = (Inspector_GameComponent)Activator.CreateInstance(attribute.InspectorType, new object[] { EntitiesContainsComponent });
                        game_comp_inspector.OnPostEditChangedProperty += evt =>
                        {
                            go.GetRuntimeGameObject().PostEditChangeProperty(evt);
                        };
                        game_comp_inspector.InspectObject(comp);
                        AddChildInspector(game_comp_inspector);
                    }
                    else
                    {
                        Debug.Assert(false);
                    }
                }

                foreach (var mECSEditorComponent in comp.mECSEditorComponents.Values)
                {
                    var Component1 = mECSEditorComponent;
                    List<Entity> EntitiesContainsComponent = Inspector_Entity.GetEntitiesContainsComponent(_Entities, Component1);

                    ComponentAttribute attribute = CrossEngine.GetInstance().EditorComponents[Component1.GetType()];

                    {
                        Inspector_Component comp_inspector = (Inspector_Component)Activator.CreateInstance(attribute.InspectorType, new object[] { EntitiesContainsComponent });
                        comp_inspector.OnPostEditChangedProperty += evt =>
                        {
                            go.GetRuntimeGameObject().PostEditChangeProperty(evt);
                        };
                        comp_inspector.InspectObject(Component1, Inspector_ComponentBase.ShowOption.ShowContent);
                        game_comp_inspector.AddChildInspector(comp_inspector);
                    }
                }
            }
        }

        private void InpectorScript()
        {
            Inspector_Property_Resource scriptinspector = (Inspector_Property_Resource)InspectorManager.GetInstance().CreatePropertyInspector("StringAsResource", false);
            ObjectProperty ScriptProperty = new ObjectProperty();
            ScriptProperty.Object = ((GOSerializerComponent)_Component).GameObject;
            ScriptProperty.Type = typeof(string);
            ScriptProperty.PropertyInfoAttribute = new PropertyInfoAttribute();
            ScriptProperty.PropertyInfoAttribute.FileTypeDescriptor = "Script Files#js|lua|mts";
            ScriptProperty.PropertyInfoAttribute.DefaultValue = "";
            ScriptProperty.Name = "Script";
            ScriptProperty.GetPropertyValueFunction = (object obj, string name, ValueExtraProperty ValueExtraProperty) =>
            {
                return _Path;
            };
            ScriptProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                _Path = (string)PropertyValue;
                GameObject go = ((GOSerializerComponent)_Component).GameObject;
                go.GetRuntimeGameObject().SetScriptPath(_Path);
                InspectObject(_Component);
                if (_Path == "") return;
            };
            AddChildInspector(scriptinspector);
            scriptinspector.InspectProperty(ScriptProperty);

            JObject JsonObj = LoadScriptEditorFields();
            // Check is valid json
            if (JsonObj.ContainsKey("Fields"))
            {
                Inspector_Script ins = new Inspector_Script();

                ObjectProperty ScriptProperty2 = new ObjectProperty();
                ScriptProperty2.Object = JsonObj;
                ScriptProperty2.Type = typeof(JToken);
                ScriptProperty2.PropertyInfoAttribute = new PropertyInfoAttribute();
                ScriptProperty2.Name = "Script DefaultValue";
                ScriptProperty2.GetPropertyValueFunction = (object obj, string name, ValueExtraProperty ValueExtraProperty) =>
                {
                    return JsonObj;
                };
                ScriptProperty2.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
                {
                };
                ins.OnScriptDefaultValueChanged = () =>
                {
                    UpdateScriptDefaultValue(JsonObj);
                };
                ins.InspectProperty(ScriptProperty2);
                AddChildInspector(ins);
            }
        }

        private JObject LoadScriptEditorFields()
        {
            var runtimeGameObject = ((GOSerializerComponent)_Component).GameObject.GetRuntimeGameObject();
            string jstring = runtimeGameObject.GetScriptEditorFieldsJson();
            try
            {
                JObject parsedObject = JObject.Parse(jstring);
                return parsedObject;
            }
            catch (Exception)
            {
                return new JObject();
            }
        }

        private void UpdateScriptDefaultValue(JObject inJson)
        {
            var runtimeGameObject = ((GOSerializerComponent)_Component).GameObject.GetRuntimeGameObject();
            runtimeGameObject.RebindScriptWithEditorFieldsJson(inJson.ToString());
        }

        /*
         * Unlike Entity, GameObject can hold properties, so we need to inspect them.
         */
        protected virtual void InspectGameObjectProperties()
        {
            GameObject go = ((GOSerializerComponent)_Component).GameObject;
            Clicegf.GameObject RuntimeObject = go.GetRuntimeGameObject();
            if (RuntimeObject == null)
            {
                return;
            }

            _Path = go.GetRuntimeGameObject().GetScriptPath();
            InpectorScript();

            /*
             * Enumerate the properties through the inherit chain
             */
            Dictionary<Type, List<PropertyInfo>> typePropertyMap = PropertyCollector.CollectPropertiesOfTypeChain(RuntimeObject.GetType());

            foreach (var kvp in typePropertyMap)
            {
                if (!kvp.Key.IsSubclassOf(typeof(Clicegf.ObjectBase)))
                {
                    continue;
                }

                if (kvp.Value.Count == 0)
                {
                    continue;
                }
                // Show the properties for the type
                Inspector_SpecificGroupedProperty inspector_GroupedProperty = new Inspector_SpecificGroupedProperty(RuntimeObject, kvp.Key.Name, kvp.Value);
                inspector_GroupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                inspector_GroupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                AddChildInspector(inspector_GroupedProperty);
                inspector_GroupedProperty.InspectObject(RuntimeObject);
            }


        }

        public override object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            GameObject go = ((GOSerializerComponent)_Component).GameObject;
            Type type = go.GetRuntimeGameObject().GetType();
            PropertyInfo PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(go.GetRuntimeGameObject());
            }

            return null;
        }

        public override void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            GameObject go = ((GOSerializerComponent)_Component).GameObject;
            Type type = go.GetRuntimeGameObject().GetType();
            PropertyInfo PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
                // Refresh inspectors after setting PropertyInfo
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    GetInspectorHandler().InspectObject();
                });
                Clicegf.PropertyChangedEvent e = new Clicegf.PropertyChangedEvent();
                e.PropertyName = PropertyName;
                go.GetRuntimeGameObject().PostEditChangeProperty(e);
            }

            EditorScene.GetInstance().SetDirty();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            UpdateLayoutBaseImpl(Width, ref Y);
        }

        public bool GameObjectHasComponent(Type ComponentType)
        {
            foreach (Entity Entity in _Entities)
            {
                if (Entity.HasComponent(ComponentType))
                {
                    return true;
                }
            }
            return false;
        }

        public void OutputRepeatedTips(string Component)
        {
            string TipsMessage = "";
            if (_Entities.Count > 1)
            {
                TipsMessage = string.Format("There is one or more entities already have {0} component.", Component);
            }
            else
            {
                TipsMessage = string.Format("There is already a {0} component in this entity.", Component);
            }
            ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information, TipsMessage);
            MainUI.GetInstance().ActivateDockingCard_Console();
        }

        public delegate List<GameObjectComponent> CreateGameComponentDelegate(GameObject GameObject);
        bool GeneralAddComponent(List<Type> ComponentTypes, CreateGameComponentDelegate CreateGameComponentDelegate)
        {
            foreach (Type ComponentType in ComponentTypes)
            {
                if (GameObjectHasComponent(ComponentType))
                {
                    OutputRepeatedTips(ComponentType.Name);
                    return false;
                }
            }
            EditOperation_AddGameObjectComponents EditOperation = new EditOperation_AddGameObjectComponents();
            foreach (Entity Entity in _Entities)
            {
                if (!Entity.HasComponent(typeof(GOSerializerComponent)))
                {
                    continue;
                }

                GameObject GameObject = GameObject.GetGameObjectFromEntity(Entity);

                List<GameObjectComponent> ComponentList = CreateGameComponentDelegate(GameObject);
                foreach (GameObjectComponent Component in ComponentList)
                {
                    EditOperation.AddAddGameObjectComponentItem(GameObject, Component);
                }
            }
            EditOperationManager.GetInstance().AddOperation(EditOperation);
            InspectorUI.GetInstance().InspectObject();
            EditorScene.GetInstance().SetDirty();
            return true;
        }


        private List<Entity> GetEntitiesContainsComponent(List<Entity> Entities, GameObjectComponent Component)
        {
            List<Entity> EntitiesContainsComponent = new List<Entity>();
            Type ComponentType = Component.GetType();
            foreach (Entity Entity in Entities)
            {
                var go = GameObject.GetGameObjectFromEntity(Entity);
                if (go is null)
                {
                    continue;
                }
                foreach (GameObjectComponent Component1 in go.mComponents)
                {
                    if (Component1.GetType() == ComponentType)
                    {
                        EntitiesContainsComponent.Add(Entity);
                        break;
                    }
                }
            }
            return EntitiesContainsComponent;
        }
        public void OnMenuItemAddComponentClikced<T>(MenuItem Sender) where T : GameObjectComponent, new()
        {
            AddComponentClicked<T>(Sender != null ? Sender.GetText() : "");
        }

        void BeginRecord()
        {
            EditOperationManager.GetInstance().BeginRecordCompoundOperation();
        }

        void EndRecord()
        {
            EditOperationManager.GetInstance().EndRecordCompoundOperation();
        }

        public void AddComponentClicked<T>(string tags) where T : GameObjectComponent, new()
        {
            BeginRecord();
            List<Type> ComponentTypes = new List<Type> { typeof(T) };

            GeneralAddComponent(ComponentTypes, (GameObject GameObject) =>
            {
                var Result = new List<GameObjectComponent> { };

                var CreateComponentMethod = typeof(GameObject).GetMethod("CreateComponent");

                foreach (Type t in ComponentTypes)
                {
                    var SpecializeMethod = CreateComponentMethod.MakeGenericMethod(t);
                    GameObjectComponent comp = (GameObjectComponent)SpecializeMethod.Invoke(GameObject, new object[] { });
                    comp.Reset();
                    comp.Initialize(tags);
                    Result.Add(comp);
                }

                return Result;
            });

            EndRecord();
        }

        private string _Path;
    }
}
