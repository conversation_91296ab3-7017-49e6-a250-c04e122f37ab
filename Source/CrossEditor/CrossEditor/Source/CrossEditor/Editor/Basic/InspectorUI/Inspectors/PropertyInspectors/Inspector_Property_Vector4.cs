using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Vector4 : Inspector_Property
    {
        Label _LabelX;
        Label _LabelY;
        Label _LabelZ;
        Label _LabelW;

        EditWithProgress _EditX;
        EditWithProgress _EditY;
        EditWithProgress _EditZ;
        EditWithProgress _EditW;
        Edit _EditValueX;
        Edit _EditValueY;
        Edit _EditValueZ;
        Edit _EditValueW;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _LabelX = CreateLabelValue(Container, "X");
            _LabelY = CreateLabelValue(Container, "Y");
            _LabelZ = CreateLabelValue(Container, "Z");
            _LabelW = CreateLabelValue(Container, "W");

            _EditX = new EditWithProgress(Container);
            _EditX.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditX.TextChangedEvent += OnEditValueTextChanged;
            _EditValueX = _EditX.GetEditValue();

            _EditY = new EditWithProgress(Container);
            _EditY.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditY.TextChangedEvent += OnEditValueTextChanged;
            _EditValueY = _EditY.GetEditValue();

            _EditZ = new EditWithProgress(Container);
            _EditZ.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditZ.TextChangedEvent += OnEditValueTextChanged;
            _EditValueZ = _EditZ.GetEditValue();

            _EditW = new EditWithProgress(Container);
            _EditW.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditW.TextChangedEvent += OnEditValueTextChanged;
            _EditValueW = _EditW.GetEditValue();

            if (_ObjectProperty.ReadOnly)
            {
                _EditX.SetReadOnly(true);
                _EditY.SetReadOnly(true);
                _EditZ.SetReadOnly(true);
                _EditW.SetReadOnly(true);
            }

            ReadValue();
        }

        public Label CreateLabelValue(Control Container, string Text)
        {
            Label LabelValue = new Label();
            LabelValue.Initialize();
            LabelValue.SetText(Text);
            LabelValue.SetFontSize(PROPERTY_FONT_SIZE);
            LabelValue.SetTextAlign(TextAlign.CenterLeft);
            Container.AddChild(LabelValue);
            return LabelValue;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int ValueWidth = GetValueWidth();
            int EditWidth = (ValueWidth - SPAN_X * 5) / 4 - 12;
            EditWidth = Math.Clamp(EditWidth, 0, DEFAULT_WIDTH);
            _LabelX.SetPosition(0, SPAN_Y, 12, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_LabelX);
            _EditX.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditX, 0);
            _LabelY.SetPosition(0, SPAN_Y, 12, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_LabelY);
            _EditY.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditY, 0);
            _LabelZ.SetPosition(0, SPAN_Y, 12, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_LabelZ);
            _EditZ.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditZ, 0);
            _LabelW.SetPosition(0, SPAN_Y, 12, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_LabelW);
            _EditW.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditW, 0);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            Vector4f Value = (Vector4f)PropertyValue;
            string XString = MathHelper.NumberToString(Value.X);
            string YString = MathHelper.NumberToString(Value.Y);
            string ZString = MathHelper.NumberToString(Value.Z);
            string WString = MathHelper.NumberToString(Value.W);
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("X"))
                {
                    XString = MULTIPLE_VALUES_STRING;
                }
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("Y"))
                {
                    YString = MULTIPLE_VALUES_STRING;
                }
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("Z"))
                {
                    ZString = MULTIPLE_VALUES_STRING;
                }
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("W"))
                {
                    WString = MULTIPLE_VALUES_STRING;
                }
            }
            _EditX.SetText(XString);
            _EditY.SetText(YString);
            _EditZ.SetText(ZString);
            _EditW.SetText(WString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueXString = _EditValueX.GetText();
            string ValueYString = _EditValueY.GetText();
            string ValueZString = _EditValueZ.GetText();
            string ValueWString = _EditValueW.GetText();
            Vector4f NewValue = new Vector4f();
            NewValue.X = MathHelper.ParseFloat(ValueXString);
            NewValue.Y = MathHelper.ParseFloat(ValueYString);
            NewValue.Z = MathHelper.ParseFloat(ValueZString);
            NewValue.W = MathHelper.ParseFloat(ValueWString);
            SetPropertyValue(NewValue);
        }

        void OnEditValueTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            if (Sender == _EditValueX)
            {
                SetSubProperty("X");
            }
            else if (Sender == _EditValueY)
            {
                SetSubProperty("Y");
            }
            else if (Sender == _EditValueZ)
            {
                SetSubProperty("Z");
            }
            else if (Sender == _EditValueW)
            {
                SetSubProperty("W");
            }

            RecordAndWriteValue();

            ClearSubProperty();
        }
    }
}
