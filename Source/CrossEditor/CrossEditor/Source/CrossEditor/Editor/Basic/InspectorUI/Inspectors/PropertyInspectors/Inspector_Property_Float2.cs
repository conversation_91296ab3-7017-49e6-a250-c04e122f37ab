using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Float2 : Inspector_Property
    {
        Label _LabelX;
        Label _LabelY;

        EditWithProgress _EditX;
        EditWithProgress _EditY;
        Edit _EditValueX;
        Edit _EditValueY;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _LabelX = CreateLabelValue(Container, "x");
            _LabelY = CreateLabelValue(Container, "y");

            _EditX = new EditWithProgress(Container);
            _EditX.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditX.TextChangedEvent += OnEditValueTextChanged;
            _EditValueX = _EditX.GetEditValue();

            _EditY = new EditWithProgress(Container);
            _EditY.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditY.TextChangedEvent += OnEditValueTextChanged;
            _EditValueY = _EditY.GetEditValue();

            if (_ObjectProperty.ReadOnly)
            {
                _EditX.SetReadOnly(true);
                _EditY.SetReadOnly(true);
            }

            ReadValue();
        }

        public Label CreateLabelValue(Control Container, string Text)
        {
            Label LabelValue = new Label();
            LabelValue.Initialize();
            LabelValue.SetText(Text);
            LabelValue.SetFontSize(PROPERTY_FONT_SIZE);
            LabelValue.SetTextAlign(TextAlign.CenterLeft);
            Container.AddChild(LabelValue);
            return LabelValue;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = (GetValueWidth() - SPAN_X * 3) / 2 - 12;
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);
            _LabelX.SetPosition(0, SPAN_Y, 12, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_LabelX);
            _EditX.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditX, 0);
            _LabelY.SetPosition(0, SPAN_Y, 12, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_LabelY);
            _EditY.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditY, 0);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            Float2 Value = (Float2)PropertyValue;
            string XString = MathHelper.NumberToString(Value.x);
            string YString = MathHelper.NumberToString(Value.y);
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("x"))
                {
                    XString = MULTIPLE_VALUES_STRING;
                }
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("y"))
                {
                    YString = MULTIPLE_VALUES_STRING;
                }
            }
            _EditX.SetText(XString);
            _EditY.SetText(YString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueXString = _EditValueX.GetText();
            string ValueYString = _EditValueY.GetText();
            Float2 NewValue = new Float2();
            NewValue.x = MathHelper.ParseFloat(ValueXString);
            NewValue.y = MathHelper.ParseFloat(ValueYString);

            SetPropertyValue(NewValue);
        }

        void OnEditValueTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            if (Sender == _EditValueX)
            {
                SetSubProperty("x");
            }
            else if (Sender == _EditValueY)
            {
                SetSubProperty("y");
            }

            RecordAndWriteValue();

            ClearSubProperty();
        }
    }
}
