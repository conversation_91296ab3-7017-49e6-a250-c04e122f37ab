using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Color : Inspector_Property
    {
        Button _ButtonColor;
        Panel _PanelGrid;
        Panel _PanelColor;
        Button _ButtonPickColor;
        Panel _Mask;

        Color _SavedColor;

        bool _bLeftMouseDown;
        bool _bMouseMoved;
        int _SavedMouseX;
        int _SavedMouseY;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _ButtonColor = new Button();
            _ButtonColor.Initialize();
            _ButtonColor.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonColor.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonColor.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonColor.SetToolTips("Edit Color");
            Container.AddChild(_ButtonColor);

            _PanelGrid = UIHelper.GetInstance().CreatePanel(Container, "Editor/Others/PanelLongGrid.png");

            _PanelColor = UIHelper.GetInstance().CreatePanel(Container);
            _PanelColor.SetBackgroundColor(Color.FromRGBA(0, 0, 255, 128));
            _PanelColor.SetBorderColor(Color.FromRGB(255, 255, 255));

            _Mask = UIHelper.GetInstance().CreatePanel(Container);
            _Mask.SetBackgroundColor(Color.FromRGBA(255, 255, 255, 0));
            _Mask.LeftMouseDownEvent += OnMaskLeftMouseDown;
            _Mask.LeftMouseUpEvent += OnMaskLeftMouseUp;
            _Mask.SetCursorEvent += OnMaskSetCursor;

            _ButtonPickColor = UIHelper.GetInstance().CreateButton_Image(Container, "Editor/Others/ButtonPickColor.png");
            _ButtonPickColor.SetToolTips("Pick Color");
            _ButtonPickColor.LeftMouseDownEvent += OnButtonPickColorLeftMouseDown;

            if (!_ValueExtraProperty._bHasMultipleValues)
            {
                _bCanRevert = true;
            }

            Color Cur = GetPropertyValue_Color();
            SetDefaultValue(new CEngine.Float3(Cur.R, Cur.G, Cur.B));
            ReadValue();
        }

        #region Mask Event

        private void OnMaskLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Sender.CaptureMouse();

            _bLeftMouseDown = true;
            _bMouseMoved = false;
            _SavedMouseX = GetDevice().GetScreenMouseX();
            _SavedMouseY = GetDevice().GetScreenMouseY();

            _SavedColor = GetPropertyValue_Color();

            _Mask.MouseMoveEvent += OnMaskMouseMove;

            bContinue = false;
        }

        private void OnMaskLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_bLeftMouseDown)
            {
                Sender.ReleaseMouse();
                _bLeftMouseDown = false;
                // Click
                if (_bMouseMoved == false)
                {
                    OpenColorSelectUI();
                }
                // After Dragging
                else
                {
                    GetDevice().ShowMouseCursor();
                }

                _Mask.MouseMoveEvent -= OnMaskMouseMove;

                EditOperation EditOperation = EditOperationManager.GetInstance().GetLatestOperation();
                if (EditOperation != null)
                {
                    EditOperation._bCombinable = false;
                }

                bContinue = false;
            }
        }

        private void OnMaskMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_bLeftMouseDown)
            {
                MouseX = GetDevice().GetScreenMouseX();
                MouseY = GetDevice().GetScreenMouseY();

                if (_bMouseMoved == false)
                {
                    _bMouseMoved = Math.Abs(MouseX - _SavedMouseX) + Math.Abs(MouseY - _SavedMouseY) > 2;
                }

                if (_bMouseMoved)
                {
                    Device Device = GetDevice();
                    bool bControl = Device.IsControlDown();
                    bool bShift = Device.IsShiftDown();
                    bool bAlt = Device.IsAltDown();
                    bool bControlOnly = bControl && !bShift && !bAlt;

                    GetDevice().HideMouseCursor();
                    if (MouseX != _SavedMouseX)
                    {
                        float Step = 0.01f;
                        if (bControlOnly)
                        {
                            Step = Step / 50.0f;
                        }

                        Color Color = GetPropertyValue_Color();
                        float H, S, V;
                        HDRColorSelectUI.RGBtoHSV(Color.R, Color.G, Color.B, out H, out S, out V);
                        V += (MouseX - _SavedMouseX) * Step;
                        V = Math.Clamp(V, 0.0f, 1.0f);

                        float SavedH, SavedS, SavedV;
                        HDRColorSelectUI.RGBtoHSV(_SavedColor.R, _SavedColor.G, _SavedColor.B, out SavedH, out SavedS, out SavedV);

                        float R, G, B;
                        HDRColorSelectUI.HSVtoRGB(out R, out G, out B, SavedH, SavedS, V);
                        Color.R = R;
                        Color.G = G;
                        Color.B = B;
                        SetNewColor(Color);
                    }
                    GetDevice().SetMousePosition(_SavedMouseX, _SavedMouseY);
                }
            }
        }

        private void OnMaskSetCursor(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY))
            {
                SystemCursor SystemCursor = SystemCursor.SizeWE;
                Cursor Cursor = GetDevice().GetSystemCursor(SystemCursor);
                GetDevice().SetCursor(Cursor);
                bContinue = false;
            }
        }

        #endregion

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int ColorWidth = GetValueWidth() - 3 * SPAN_X - BUTTON_WIDTH;
            ColorWidth = Math.Min(ColorWidth, DEFAULT_WIDTH);
            _ButtonColor.SetPosition(0, 2, ColorWidth, 16);
            GetValueContainer().FloatToLeft(_ButtonColor);
            _PanelGrid.SetPosition(0, SPAN_Y, ColorWidth - 2, 14);
            GetValueContainer().PlaceAsLeft(_PanelGrid, 1);
            _PanelColor.SetPosition(0, 2, ColorWidth, 16);
            GetValueContainer().PlaceAsLeft(_PanelColor);
            _ButtonPickColor.SetPosition(0, 2, BUTTON_WIDTH, 16);
            GetValueContainer().FloatToLeft(_ButtonPickColor);

            _Mask.SetPosition(_PanelColor.GetX(), _PanelColor.GetY(), ColorWidth, 16);
        }

        public Color GetPropertyValue_Color()
        {
            Color Color = new Color();
            object Value = GetPropertyValue();
            if (Value.GetType() == typeof(Color))
            {
                Color = (Color)Value;
            }
            else if (Value.GetType() == typeof(Vector3f))
            {
                Vector3f Vector = (Vector3f)Value;
                Color.R = Vector.X;
                Color.G = Vector.Y;
                Color.B = Vector.Z;
                Color.A = 1.0f;
            }
            else if (Value.GetType() == typeof(Vector4f))
            {
                Vector4f Vector = (Vector4f)Value;
                Color.R = Vector.X;
                Color.G = Vector.Y;
                Color.B = Vector.Z;
                Color.A = Vector.W;
            }
            else if (Value.GetType() == typeof(Float3))
            {
                Float3 Float3 = (Float3)Value;
                Color.R = Float3.x;
                Color.G = Float3.y;
                Color.B = Float3.z;
                Color.A = 1.0f;
            }
            else if (Value.GetType() == typeof(Float4))
            {
                Float4 Float4 = (Float4)Value;
                Color.R = Float4.x;
                Color.G = Float4.y;
                Color.B = Float4.z;
                Color.A = Float4.w;
            }
            else if (Value.GetType() == typeof(Clicross.Float3))
            {
                Clicross.Float3 Float3 = (Clicross.Float3)Value;
                Color.R = Float3.x;
                Color.G = Float3.y;
                Color.B = Float3.z;
                Color.A = 1;
            }
            else if (Value.GetType() == typeof(Clicross.Float4))
            {
                Clicross.Float4 Float4 = (Clicross.Float4)Value;
                Color.R = Float4.x;
                Color.G = Float4.y;
                Color.B = Float4.z;
                Color.A = Float4.w;
            }
            else if (Value.GetType() == typeof(ColorRGBf))
            {
                ColorRGBf RGB = (ColorRGBf)Value;
                Color.R = RGB.r;
                Color.G = RGB.g;
                Color.B = RGB.b;
                Color.A = 1.0f;
            }
            else if (Value.GetType() == typeof(ColorRGBAf))
            {
                ColorRGBAf RGBA = (ColorRGBAf)Value;
                Color.R = RGBA.r;
                Color.G = RGBA.g;
                Color.B = RGBA.b;
                Color.A = RGBA.a;
            }
            return Color;
        }

        void SetPanelColorBackgroundColor(Color Color)
        {
            Color ColorForDisplay = HDRColorSelectUI.GetColorForDisplay(Color.R, Color.G, Color.B, Color.A);
            _PanelColor.SetBackgroundColor(ColorForDisplay);
            _PanelColor.SetTagObject(Color);
        }

        Color GetPanelColorBackgroundColor()
        {
            Color ColorForDisplay = (Color)_PanelColor.GetTagObject();
            return ColorForDisplay;
        }

        public override void ReadValue()
        {
            Color Color = GetPropertyValue_Color();
            SetPanelColorBackgroundColor(Color);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            Color Color = GetPanelColorBackgroundColor();
            object Value = GetPropertyValue();
            if (Value.GetType() == typeof(Color))
            {
                SetPropertyValue(Color);
            }
            else if (Value.GetType() == typeof(Vector3f))
            {
                Vector3f Vector = (Vector3f)GetPropertyValue();
                Vector.X = Color.R;
                Vector.Y = Color.G;
                Vector.Z = Color.B;
                SetPropertyValue(Vector);
            }
            else if (Value.GetType() == typeof(Vector4f))
            {
                Vector4f Vector = (Vector4f)GetPropertyValue();
                Vector.X = Color.R;
                Vector.Y = Color.G;
                Vector.Z = Color.B;
                Vector.W = Color.A;
                SetPropertyValue(Vector);
            }
            else if (Value.GetType() == typeof(Float3))
            {
                Float3 Float3 = (Float3)GetPropertyValue();
                //Float3 Float3 = new Float3(); 
                Float3.x = Color.R;
                Float3.y = Color.G;
                Float3.z = Color.B;
                SetPropertyValue(Float3);
            }
            else if (Value.GetType() == typeof(Float4))
            {
                Float4 Float4 = (Float4)GetPropertyValue();
                Float4.x = Color.R;
                Float4.y = Color.G;
                Float4.z = Color.B;
                Float4.w = Color.A;
                SetPropertyValue(Float4);
            }
            else if (Value.GetType() == typeof(Clicross.Float3))
            {
                Clicross.Float3 Float3 = new Clicross.Float3();
                Float3.x = Color.R;
                Float3.y = Color.G;
                Float3.z = Color.B;
                SetPropertyValue(Float3);
            }
            else if (Value.GetType() == typeof(Clicross.Float4))
            {
                Clicross.Float4 Float4 = new Clicross.Float4();
                Float4.x = Color.R;
                Float4.y = Color.G;
                Float4.z = Color.B;
                Float4.w = Color.A;
                SetPropertyValue(Float4);
            }
            else if (Value.GetType() == typeof(ColorRGBf))
            {
                ColorRGBf ColorRGB = (ColorRGBf)GetPropertyValue();
                ColorRGB.r = Color.R;
                ColorRGB.g = Color.G;
                ColorRGB.b = Color.B;
                SetPropertyValue(ColorRGB);
            }
            else if (Value.GetType() == typeof(ColorRGBAf))
            {
                ColorRGBAf ColorRGBA = (ColorRGBAf)GetPropertyValue();
                ColorRGBA.r = Color.R;
                ColorRGBA.g = Color.G;
                ColorRGBA.b = Color.B;
                ColorRGBA.a = Color.A;
                SetPropertyValue(ColorRGBA);
            }
        }

        void OpenColorSelectUI()
        {
            Color Color = GetPropertyValue_Color();
            HDRColorSelectUI HDRColorSelectUI = new HDRColorSelectUI();
            HDRColorSelectUI.Initialize(GetUIManager(), "Color", Color);
            HDRColorSelectUI.ColorSelectedEvent += (HDRColorSelectUI Sender1) =>
            {
                Color OldColor = Sender1.GetOldColor();
                SetPanelColorBackgroundColor(OldColor);
                WriteValue();
                bool bColorModified = Sender1.GetColorModified();
                if (bColorModified)
                {
                    Color NewColor = Sender1.GetNewColor();
                    RecordAndWriteValue(() => { SetPanelColorBackgroundColor(NewColor); });
                }
            };
            HDRColorSelectUI.ColorSelectingEvent += (Sender1) =>
            {
                Color NewColor = Sender1.GetNewColor();
                SetPanelColorBackgroundColor(NewColor);
                WriteValue();
            };
            DialogUIManager.GetInstance().ShowDialogUI(HDRColorSelectUI);
        }

        void SetNewColor(Color NewColor)
        {
            RecordAndWriteValue(() => { SetPanelColorBackgroundColor(NewColor); });
        }

        public CEngine.Float3 GetOldColor()
        {
            return new CEngine.Float3(_SavedColor.R, _SavedColor.G, _SavedColor.B);
        }

        void OnButtonPickColorLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            _SavedColor = GetPanelColorBackgroundColor();
            _ButtonPickColor.LeftMouseUpEvent += OnButtonPickColorLeftMouseUp;
            _ButtonPickColor.RightMouseDownEvent += OnButtonPickColorRightMouseDown;
            _ButtonPickColor.MouseMoveEvent += OnButtonPickColorMouseMove;
            Cursor Cusror = GetDevice().GetSystemCursor(SystemCursor.Cross);
            GetUIManager().SetOverridingCursor(Cusror);
        }

        void OnButtonPickColorLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            _ButtonPickColor.LeftMouseUpEvent -= OnButtonPickColorLeftMouseUp;
            _ButtonPickColor.RightMouseDownEvent -= OnButtonPickColorRightMouseDown;
            _ButtonPickColor.MouseMoveEvent -= OnButtonPickColorMouseMove;
            GetUIManager().SetOverridingCursor(null);

            if (_ButtonPickColor.IsPointIn(MouseX, MouseY))
            {
                return;
            }

            Device Device = GetDevice();
            int ScreenMouseX = Device.GetX() + MouseX;
            int ScreenMouseY = Device.GetY() + MouseY;

            Color NewColor = Device.PickScreenColor(ScreenMouseX, ScreenMouseY);
            SetNewColor(NewColor);
        }

        void OnButtonPickColorRightMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            _ButtonPickColor.LeftMouseUpEvent -= OnButtonPickColorLeftMouseUp;
            _ButtonPickColor.RightMouseDownEvent -= OnButtonPickColorRightMouseDown;
            _ButtonPickColor.MouseMoveEvent -= OnButtonPickColorMouseMove;
            GetUIManager().SetOverridingCursor(null);

            SetNewColor(_SavedColor);
        }

        void OnButtonPickColorMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Device Device = GetDevice();
            int ScreenMouseX = Device.GetX() + MouseX;
            int ScreenMouseY = Device.GetY() + MouseY;

            Color NewColor = Device.PickScreenColor(ScreenMouseX, ScreenMouseY);
            SetNewColor(NewColor);
        }

        public virtual void SetPropertyKeyFrameFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {

        }

        public override object GetPropertyEditValue()
        {
            return GetPanelColorBackgroundColor();
        }

        public Float3 Color_ValueToFloat3(object Value)
        {
            Float3 Float3 = new Float3();
            if (Value.GetType() == typeof(Color))
            {
                Color NewValue = (Color)Value;
                Float3 = new Float3(NewValue.R, NewValue.G, NewValue.B);
            }
            else if (Value.GetType() == typeof(Vector3f))
            {
                Vector3f NewValue = (Vector3f)Value;
                Float3 = new Float3(NewValue.X, NewValue.Y, NewValue.Z);
            }
            else if (Value.GetType() == typeof(Vector4f))
            {
                Vector4f NewValue = (Vector4f)Value;
                Float3 = new Float3(NewValue.X, NewValue.Y, NewValue.Z);
            }
            else if (Value.GetType() == typeof(Float3))
            {
                Float3 NewValue = (Float3)Value;
                Float3 = NewValue;
            }
            else if (Value.GetType() == typeof(Float4))
            {
                Float4 NewValue = (Float4)Value;
                Float3 = new Float3(NewValue.x, NewValue.y, NewValue.z);
            }
            else if (Value.GetType() == typeof(Clicross.Float4))
            {
                Clicross.Float4 NewValue = (Clicross.Float4)Value;
                Float3 = new Float3(NewValue.x, NewValue.y, NewValue.z);
            }
            else if (Value.GetType() == typeof(ColorRGBf))
            {
                ColorRGBf NewValue = (ColorRGBf)Value;
                Float3 = new Float3(NewValue.r, NewValue.g, NewValue.b);
            }
            else if (Value.GetType() == typeof(ColorRGBAf))
            {
                ColorRGBAf NewValue = (ColorRGBAf)Value;
                Float3 = new Float3(NewValue.r, NewValue.g, NewValue.b);
            }
            return Float3;
        }

        public object Color_Float3ToValue(ObjectProperty Property, object NewValue)
        {
            object Result = NewValue;
            Type Type = Property.Type;

            Float3 Float3Value = (Float3)NewValue;
            if (Type == typeof(Color))
            {
                Result = new Color(Float3Value.x, Float3Value.y, Float3Value.z, 255);
            }
            else if (Type == typeof(Vector3f))
            {
                Result = new Vector3f(Float3Value.x, Float3Value.y, Float3Value.z);
            }
            else if (Type == typeof(Vector4f))
            {
                Result = new Vector4f(Float3Value.x, Float3Value.y, Float3Value.z, 255);
            }
            else if (Type == typeof(Float3))
            {
                Result = NewValue;
            }
            else if (Type == typeof(Clicross.Float3))
            {
                Result = new Clicross.Float3(Float3Value.x, Float3Value.y, Float3Value.z);
            }
            else if (Type == typeof(Float4))
            {
                Result = new Float4(Float3Value.x, Float3Value.y, Float3Value.z, 255);
            }
            else if (Type == typeof(ColorRGBf))
            {
                Result = new ColorRGBf(Float3Value.x, Float3Value.y, Float3Value.z);
            }
            else if (Type == typeof(ColorRGBAf))
            {
                Result = new ColorRGBAf(Float3Value.x, Float3Value.y, Float3Value.z, 255);
            }
            return Result;
        }
    }
}
