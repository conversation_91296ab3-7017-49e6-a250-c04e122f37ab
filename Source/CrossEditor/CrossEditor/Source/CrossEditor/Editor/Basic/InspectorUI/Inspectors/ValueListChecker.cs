using CEngine;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class ValueListChecker
    {
        static List<object> _ValueList = new List<object>();
        static List<object> _FieldList = new List<object>();

        public static object GetNonNullObject(List<object> ValueList)
        {
            foreach (object Value in ValueList)
            {
                if (Value != null)
                {
                    return Value;
                }
            }
            return null;
        }

        public static void CheckFiledHaveMultipleValues(ValueExtraProperty ValueExtraProperty, List<object> ValueList, Type Type, string FiledName)
        {
            FieldInfo FieldInfo = Type.GetField(FiledName);
            _FieldList.Clear();
            foreach (object Value in ValueList)
            {
                object FieldValue = FieldInfo.GetValue(Value);
                if (_FieldList.Contains(FieldValue) == false)
                {
                    _FieldList.Add(FieldValue);
                }
                if (_FieldList.Count >= 2)
                {
                    ValueExtraProperty._HaveMultipleValuesSubProperties.Add(FiledName);
                    return;
                }
            }
        }

        public static void CheckPropertyHaveMultipleValues(ValueExtraProperty ValueExtraProperty, List<object> ValueList, Type Type, string PropertyName)
        {
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            _FieldList.Clear();
            foreach (object Value in ValueList)
            {
                object FieldValue = PropertyInfo.GetValue(Value);
                if (_FieldList.Contains(FieldValue) == false)
                {
                    _FieldList.Add(FieldValue);
                }
                if (_FieldList.Count >= 2)
                {
                    ValueExtraProperty._HaveMultipleValuesSubProperties.Add(PropertyName);
                    return;
                }
            }
        }

        public static void CheckValueList(ValueExtraProperty ValueExtraProperty, List<object> ValueList)
        {
            if (ValueExtraProperty == null)
            {
                return;
            }
            ValueExtraProperty.Clear();
            if (ValueList.Count <= 1)
            {
                return;
            }
            _ValueList.Clear();
            object Value1 = GetNonNullObject(ValueList);
            if (Value1 == null)
            {
                return;
            }
            Type Type1 = Value1.GetType();
            if (Type1 == typeof(string) ||
                Type1 == typeof(float) ||
                Type1 == typeof(Vector2f) ||
                Type1 == typeof(Vector3f) ||
                Type1 == typeof(Vector4f) ||
                Type1 == typeof(Rangef) ||
                Type1 == typeof(Float2) ||
                Type1 == typeof(Float3) ||
                Type1 == typeof(Double3) ||
                Type1 == typeof(Quaternion) ||
                Type1.IsEnum ||
                Type1 == typeof(Entity))
            {
                foreach (object Value in ValueList)
                {
                    if (_ValueList.Contains(Value) == false)
                    {
                        _ValueList.Add(Value);
                    }
                    if (_ValueList.Count >= 2)
                    {
                        ValueExtraProperty._bHasMultipleValues = true;
                        break;
                    }
                }
            }
            if (ValueExtraProperty._bHasMultipleValues)
            {
                if (Type1 == typeof(Vector2f))
                {
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "X");
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "Y");
                }
                else if (Type1 == typeof(Vector3f))
                {
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "X");
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "Y");
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "Z");
                }
                else if (Type1 == typeof(Vector4f))
                {
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "X");
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "Y");
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "Z");
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "W");
                }
                else if (Type1 == typeof(Rangef))
                {
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "Min");
                    CheckFiledHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "Max");
                }
                else if (Type1 == typeof(Float2))
                {
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "x");
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "y");
                }
                else if (Type1 == typeof(Float3))
                {
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "x");
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "y");
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "z");
                }
                else if (Type1 == typeof(Double3))
                {
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "x");
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "y");
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "z");
                }
                else if (Type1 == typeof(Quaternion))
                {
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "x");
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "y");
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "z");
                    CheckPropertyHaveMultipleValues(ValueExtraProperty, ValueList, Type1, "w");
                }
            }
        }
    }
}
