using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class PropertyCollector
    {
        public static List<PropertyInfo> CollectPropertiesOfType(Type ThisType)
        {
            List<Type> TypeChain = new List<Type>();
            Type CurrentType = ThisType;
            while (CurrentType != typeof(object))
            {
                TypeChain.Add(CurrentType);
                CurrentType = CurrentType.BaseType;
            }
            List<PropertyInfo> Properties = new List<PropertyInfo>();
            int Count = TypeChain.Count;
            for (int i = Count - 1; i >= 0; i--)
            {
                CollectPropertiesOfSpecificType(Properties, TypeChain[i]);
            }
            return Properties;
        }
        public static Dictionary<Type, List<PropertyInfo>> CollectPropertiesOfTypeChain(Type ThisType)
        {
            Dictionary<Type, List<PropertyInfo>> result = new Dictionary<Type, List<PropertyInfo>>();
            List<Type> TypeChain = new List<Type>();
            Type CurrentType = ThisType;
            while (CurrentType != typeof(object))
            {
                TypeChain.Add(CurrentType);
                CurrentType = CurrentType.BaseType;
            }
            int Count = TypeChain.Count;
            for (int i = Count - 1; i >= 0; i--)
            {
                List<PropertyInfo> Properties = new List<PropertyInfo>();
                CollectPropertiesOfSpecificType(Properties, TypeChain[i]);
                result[TypeChain[i]] = Properties;
            }
            return result;
        }


        static void CollectPropertiesOfSpecificType(List<PropertyInfo> Properties, Type ThisType)
        {
            Type BaseType = ThisType.BaseType;
            PropertyInfo[] ThisTypeProperties = ThisType.GetProperties();
            PropertyInfo[] BaseTypeProperties = BaseType.GetProperties();
            SortedSet<string> BaseTypePropertySet = new SortedSet<string>();
            foreach (PropertyInfo PropertyInfo in BaseTypeProperties)
            {
                BaseTypePropertySet.Add(PropertyInfo.Name);
            }

            foreach (PropertyInfo PropertyInfo in ThisTypeProperties)
            {
                if (BaseTypePropertySet.Contains(PropertyInfo.Name) == false)
                {
                    Properties.Add(PropertyInfo);
                }
            }
        }
    }
}
