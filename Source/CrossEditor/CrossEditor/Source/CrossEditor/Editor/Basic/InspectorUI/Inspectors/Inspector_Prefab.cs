using CEngine;
using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class Inspector_Prefab : Inspector
    {
        Entity _Entity;
        List<Entity> _Entities;

        Button _ButtonLocate;
        Button _ButtonOpen;
        Label _LabelName;

        public Inspector_Prefab()
        {

        }

        public override void InspectObject(object Object, object Tag = null)
        {
            if (Object is Entity)
            {
                _Entity = (Entity)Object;
                _Entities = new List<Entity>();
                _Entities.Add(_Entity);
            }
            else if (Object is List<Entity>)
            {
                _Entities = (List<Entity>)Object;
                if (_Entities.Count > 0)
                {
                    _Entity = _Entities[0];
                }
                else
                {
                    _Entity = null;
                }
            }
            else
            {
                DebugHelper.Assert(false);
            }

            _ButtonLocate = new Button();
            _ButtonLocate.Initialize();
            _ButtonLocate.SetImage(UIManager.LoadUIImage("Editor/Others/ButtonLocate.png"));
            _ButtonLocate.ClickedEvent += OnButtonLocateClicked;
            _SelfContainer.AddChild(_ButtonLocate);

            _ButtonOpen = new Button();
            _ButtonOpen.Initialize();
            _ButtonOpen.SetImage(UIManager.LoadUIImage("Editor/Others/ButtonDocument.png"));
            _ButtonOpen.ClickedEvent += OnButtonOpenClicked;
            _SelfContainer.AddChild(_ButtonOpen);

            _LabelName = new Label();
            _LabelName.Initialize();
            var prefabId = (_Entity.IsPrefabInstanceRoot() || !_Entity.IsInheritPrefabInstance()) ? _Entity.GetPrefabId() : _Entity.GetInheritPrefabId();
            _LabelName.SetText(ResourceManager.Instance().ConvertGuidToPath(prefabId));
            _LabelName.SetFontSize(16);
            _LabelName.SetTextAlign(TextAlign.CenterLeft);
            _SelfContainer.AddChild(_LabelName);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _ButtonLocate.SetPosition(SPAN_X, Height, 20, 24);
            _ButtonOpen.SetPosition(SPAN_X * 2 + 20, Height, 20, 24);
            _LabelName.SetPosition(_ButtonOpen.GetEndX() + SPAN_X, 0, Width - _ButtonOpen.GetEndX() - SPAN_X, 24);
            Height += 24;
            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height;
        }

        protected void OnButtonLocateClicked(Button Sender)
        {
            string abspath = EditorUtilities.StandardFilenameToEditorFilename(_LabelName.GetText());
            ProjectUI.GetInstance().JumpToPath(abspath);
        }

        protected void OnButtonOpenClicked(Button Sender)
        {
            MainUI.GetInstance().ActivateDockingCard_PrefabEditor();
            PrefabSceneUI.GetInstance().SetPrefab(_LabelName.GetText());
        }
    }
}
