using EditorUI;

namespace CrossEditor
{
    class Inspector_Object : Inspector
    {
        object _Object;

        Panel _PanelIcon;
        Edit _EditName;

        public Inspector_Object()
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Object = Object;

            _PanelIcon = new Panel();
            _PanelIcon.Initialize();
            _PanelIcon.SetImage(UIManager.LoadUIImage("Editor/Game/GameObject.png"));
            _SelfContainer.AddChild(_PanelIcon);

            _EditName = new Edit();
            _EditName.SetFontSize(16);
            _EditName.Initialize(EditMode.Simple_SingleLine);
            _EditName.LoadSource("");
            _EditName.SetReadOnly(true);
            EditContextUI.GetInstance().RegisterEdit(_EditName);
            _EditName.SetText(_Object.GetType().ToString());
            _SelfContainer.AddChild(_EditName);

            ClearChildInspectors();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _PanelIcon.SetPosition(SPAN_X, Height + 4, 20, 20);
            int EditNameX = _PanelIcon.GetEndX() + SPAN_X;
            int EditNameWidth = Width - SPAN_X - EditNameX;
            _EditName.SetPosition(EditNameX, Height + 7, EditNameWidth, 14);
            Height += 28;
            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height;

            base.UpdateLayout(Width, ref Y);
        }
    }
}
