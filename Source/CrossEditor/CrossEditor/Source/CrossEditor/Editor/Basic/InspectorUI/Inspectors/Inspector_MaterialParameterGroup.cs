using CEngine;
using Clicross;
using EditorUI;
using System;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_MaterialParameterGroup : Inspector
    {
        protected string mGroupName = "";

        protected Button mButtonBar;
        protected Label mLabelName;

        MaterialParameterGroup mParameterGroup;
        bool mInMaterialInstanceEditor;

        public Inspector_MaterialParameterGroup(bool inMaterialInstanceEditor)
        {
            mInMaterialInstanceEditor = inMaterialInstanceEditor;
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            mParameterGroup = Object as MaterialParameterGroup;
            mGroupName = mParameterGroup.Name;

            mButtonBar = new Button();
            mButtonBar.Initialize();
            mButtonBar.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            mButtonBar.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            mButtonBar.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            mButtonBar.ClickedEvent += OnButtonBarClicked;
            _SelfContainer.AddChild(mButtonBar);

            InitializeCheckExpand();
            mButtonBar.AddChild(_CheckExpand);

            mLabelName = new Label();
            mLabelName.Initialize();
            mLabelName.SetText(mGroupName);
            mLabelName.SetFontSize(Inspector_Property.PROPERTY_FONT_SIZE);
            mLabelName.SetTextAlign(TextAlign.CenterLeft);
            mButtonBar.AddChild(mLabelName);

            RefreshChildInspectors();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            _SelfContainer.SetPosition(0, Y, Width, DEFAULT_HEIGHT);
            mButtonBar.SetPosition(0, 0, Width, DEFAULT_HEIGHT);
            Y += DEFAULT_HEIGHT;

            _CheckExpand.SetPosition(GetIndent() + SPAN_X - 20, 0, 20, 20);
            int LabelNameWidth = mLabelName.CalculateTextWidth();
            mLabelName.SetPosition(_CheckExpand.GetEndX(), 0, LabelNameWidth, 20);

            base.UpdateLayout(Width, ref Y);
        }

        protected void RefreshChildInspectors()
        {
            ClearChildInspectors();

            foreach (MaterialParameter parameter in mParameterGroup.Parameters)
            {
                PropertyInfo propertyInfoEnable = parameter.GetType().GetProperty("Enable");
                PropertyInfo propertyInfoValue = parameter.GetType().GetProperty("Value");

                decimal? valueMin = null;
                decimal? valueMax = null;

                if (mInMaterialInstanceEditor && parameter is MaterialParameterScalar scalarParameter)
                {
                    if (scalarParameter.SliderMin < scalarParameter.SliderMax)
                    {
                        valueMin = Convert.ToDecimal(scalarParameter.SliderMin);
                        valueMax = Convert.ToDecimal(scalarParameter.SliderMax);
                    }
                }

                AddPropertyInspectorWithEnable(propertyInfoEnable, propertyInfoValue, parameter, valueMin, valueMax, parameter.DisplayName);
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    return PropertyInfo.GetValue(Object);
                }
                return null;
            };

            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(Object, PropertyValue);
                    PropertyModifiedEvent?.Invoke(Object, PropertyInfo);
                }
            };
        }

        public void AddPropertyInspectorWithEnable(PropertyInfo PropertyInfoEnable, PropertyInfo PropertyInfoValue, object BindObject, decimal? valueMin, decimal? valueMax, string displayName)
        {
            PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfoValue, BindObject);
            if (PropertyInfoAttribute.bHide)
            {
                return;
            }
            if (PropertyInfoAttribute.bAdvanced)
            {
                if (_bHasAdvancedChild == false)
                {
                    InitializeAdvancedButton();
                }
                _bHasAdvancedChild = true;
            }
            if (PropertyInfoAttribute.bModified)
            {
                if (_bHasModifyChild == false)
                {
                    InitializeModifiedButton(PropertyInfoAttribute.DisplayName);
                }
                _bHasModifyChild = true;
            }
            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = BindObject;
            ObjectProperty.Name = PropertyInfoValue.Name;
            if (PropertyInfoAttribute.DisplayName != "")
            {
                ObjectProperty.DisplayName = PropertyInfoAttribute.DisplayName;
            }
            else
            {
                ObjectProperty.DisplayName = displayName == null ? "" : displayName;
            }
            ObjectProperty.Type = PropertyInfoValue.PropertyType;
            ObjectProperty.ReadOnly = !(bool)PropertyInfoEnable.GetValue(BindObject);
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = valueMin != null ? valueMin.Value : Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = valueMax != null ? valueMax.Value : Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;
            ObjectProperty.CheckEnable = mInMaterialInstanceEditor;

            ObjectProperty.SetEnableFunction = (bool value) =>
            {
                PropertyInfoEnable.SetValue(BindObject, value);
                PropertyModifiedEvent?.Invoke(null, null);
                RefreshChildInspectors();
            };
            ObjectProperty.GetEnableFunction = () => { return (bool)PropertyInfoEnable.GetValue(BindObject); };

            BindPropertyFunction(ref ObjectProperty);

            string PropertyTypeString = PropertyInfoValue.PropertyType.ToString();
            if (PropertyInfoAttribute.PropertyType != "")
            {
                PropertyTypeString = PropertyInfoAttribute.PropertyType;
            }
            Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, ObjectProperty.Type.IsEnum);
            AddChildInspector(Inspector_Property);
            Inspector_Property.InspectProperty(ObjectProperty);
        }

        protected void OnButtonBarClicked(Button Sender)
        {
            bool bChecked = _CheckExpand.GetChecked();
            SetCheckExpand(!bChecked);
        }

        public override string GetName()
        {
            return base.GetName() + "_" + mGroupName;
        }
    }
}
