using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public delegate void InspectorManagerPropertyValueChangedEventHandler(Inspector_Property PropertyInspector, object Object, string Name, object NewValue);

    public class InspectorManager
    {
        static InspectorManager _Instance = new InspectorManager();

        Dictionary<Type, Type> _ObjectInspectors;
        Dictionary<string, Type> _PropertyInspectors;

        public InspectorManagerPropertyValueChangedEventHandler PropertyValueChangedEvent;

        public static InspectorManager GetInstance()
        {
            return _Instance;
        }

        public InspectorManager()
        {
            _ObjectInspectors = new Dictionary<Type, Type>();
            _PropertyInspectors = new Dictionary<string, Type>();

            PropertyValueChangedEvent = null;

            RegisterObjectInspector(typeof(World), typeof(Inspector_World));
            RegisterObjectInspector(typeof(Entity), typeof(Inspector_Entity));
            RegisterObjectInspector(typeof(List<Entity>), typeof(Inspector_Entity));
            RegisterObjectInspector(typeof(AnimatorStatePanel.InspectorParameter), typeof(Inspector_AnimatorState));
            RegisterObjectInspector(typeof(AnimatorStateLink.InspectorParameter), typeof(Inspector_AnimatorStateLink));
            RegisterObjectInspector(typeof(TransitionRule), typeof(Inspector_TransitionRule));
            RegisterObjectInspector(typeof(StoryBoardProperty), typeof(Inspector_StoryBoardProperty));
            RegisterObjectInspector(typeof(Material), typeof(Inspector_Material));
            RegisterObjectInspector(typeof(MaterialParameterCollection), typeof(Inspector_MaterialParameterCollection));
            RegisterObjectInspector(typeof(MaterialInstance), typeof(Inspector_MaterialInstance));
            RegisterObjectInspector(typeof(TextureResource), typeof(Inspector_TextureResources));
            RegisterObjectInspector(typeof(MotionMatchAsset), typeof(Inspector_MotionMatch));
            RegisterObjectInspector(typeof(SkeletonPhysicsShowItem), typeof(Inspector_Struct_With_Property));
            RegisterObjectInspector(typeof(MeshAssetDataResource), typeof(Inspector_MeshAssetDataResources));
            RegisterObjectInspector(typeof(TerrainResource), typeof(Inspector_TerrainResource));
            RegisterObjectInspector(typeof(SyncMarkerKeyFrame), typeof(Inspector_SyncMarkerKeyFrame));
            RegisterObjectInspector(typeof(NotifyKeyFrame), typeof(Inspector_NotifyKeyFrame));
            RegisterObjectInspector(typeof(AnimSegmentKeyFrame), typeof(Inspector_SegmentKeyFrame));
            RegisterObjectInspector(typeof(ClusterObjectDataInfo), typeof(Inspector_ClusterObjectData));
            RegisterObjectInspector(typeof(ParticleSystemResource), typeof(Inspector_ParticleSystemResource));
            RegisterObjectInspector(typeof(ParticleEmitterResource), typeof(Inspector_ParticleEmitterResource));
            RegisterObjectInspector(typeof(PreviewBone), typeof(Inspector_PreviewBone));
            RegisterObjectInspector(typeof(Texture2DArray), typeof(Inspector_Texture2DArray));
            RegisterObjectInspector(typeof(Fx.Property), typeof(Inspector_FxProperty));
            RegisterObjectInspector(typeof(Fx.Pass), typeof(Inspector_FxPass));
            RegisterObjectInspector(typeof(AdjustColor), typeof(Inspector_AdjustColor));
            RegisterObjectInspector(typeof(Clicross.MaterialExpression), typeof(Inspector_MaterialExpression));
            RegisterObjectInspector(typeof(Clicross.MaterialInstanceDefines), typeof(Inspector_MaterialInstanceDefines));

            RegisterSubclassObjectInspector(typeof(KeyFrame), typeof(Inspector_Struct_With_Property));
            RegisterSubclassObjectInspector(typeof(Node), typeof(Inspector_Node));
            RegisterSubclassObjectInspector(typeof(ValueType), typeof(Inspector_Struct));
            RegisterSubclassObjectInspector(typeof(MsaPreviewSingleAsset), typeof(Inspector_MSAPreviewAsset));

            RegisterPropertyInspector(typeof(sbyte).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(byte).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(short).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(ushort).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(int).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(uint).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(long).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(ulong).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(float).ToString(), typeof(Inspector_Property_Float));
            RegisterPropertyInspector("float", typeof(Inspector_Property_Float));
            RegisterPropertyInspector(typeof(double).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(decimal).ToString(), typeof(Inspector_Property_General));
            RegisterPropertyInspector(typeof(string).ToString(), typeof(Inspector_Property_String));
            RegisterPropertyInspector(typeof(bool).ToString(), typeof(Inspector_Property_Bool));
            RegisterPropertyInspector(typeof(Vector2f).ToString(), typeof(Inspector_Property_Vector2));
            RegisterPropertyInspector(typeof(Vector3f).ToString(), typeof(Inspector_Property_Vector3));
            RegisterPropertyInspector(typeof(Vector3d).ToString(), typeof(Inspector_Property_Vector3));
            RegisterPropertyInspector(typeof(Vector4f).ToString(), typeof(Inspector_Property_Vector4));
            RegisterPropertyInspector(typeof(Color).ToString(), typeof(Inspector_Property_Color));
            RegisterPropertyInspector(typeof(Rangef).ToString(), typeof(Inspector_Property_Rangef));
            RegisterPropertyInspector(typeof(Entity).ToString(), typeof(Inspector_Property_Entity));
            RegisterPropertyInspector(typeof(EntityIDStruct).ToString(), typeof(Inspector_Property_Entity));
            RegisterPropertyInspector(typeof(Clicross.ecs.EntityIDStruct).ToString(), typeof(Inspector_Property_Entity));
            RegisterPropertyInspector("Vector3fAsColor", typeof(Inspector_Property_Color));
            RegisterPropertyInspector("Vector4fAsColor", typeof(Inspector_Property_Color));
            RegisterPropertyInspector("Float3AsColor", typeof(Inspector_Property_Color));
            RegisterPropertyInspector("Float4AsColor", typeof(Inspector_Property_Color));
            RegisterPropertyInspector("StringAsFile", typeof(Inspector_Property_File));
            RegisterPropertyInspector("StringAsFolder", typeof(Inspector_Property_Folder));
            RegisterPropertyInspector("StringAsResource", typeof(Inspector_Property_Resource));
            RegisterPropertyInspector("StringAsCode", typeof(Inspector_Property_Code));
            RegisterPropertyInspector("MMAnimationResource", typeof(Inspector_MMAnimationResource));
            RegisterPropertyInspector(typeof(Vector2i).ToString(), typeof(Inspector_Property_Int2));
            RegisterPropertyInspector(typeof(Vector3i).ToString(), typeof(Inspector_Property_Int3));
            RegisterPropertyInspector(typeof(Vector4i).ToString(), typeof(Inspector_Property_Int4));
            RegisterPropertyInspector(typeof(Vector2ui).ToString(), typeof(Inspector_Property_UInt2));
            RegisterPropertyInspector(typeof(Vector3ui).ToString(), typeof(Inspector_Property_UInt3));
            RegisterPropertyInspector(typeof(Vector4ui).ToString(), typeof(Inspector_Property_UInt4));
            RegisterPropertyInspector(typeof(Float2).ToString(), typeof(Inspector_Property_GenericVector<Float2, VectorPropertiesXY>));
            RegisterPropertyInspector(typeof(Float3).ToString(), typeof(Inspector_Property_GenericVector<Float3, VectorPropertiesXYZ>));
            RegisterPropertyInspector(typeof(Float4).ToString(), typeof(Inspector_Property_GenericVector<Float4, VectorPropertiesXYZW>));
            RegisterPropertyInspector(typeof(Clicross.Float2).ToString(), typeof(Inspector_Property_GenericVector<Clicross.Float2, VectorPropertiesXY>));
            RegisterPropertyInspector(typeof(Clicross.Float3).ToString(), typeof(Inspector_Property_GenericVector<Clicross.Float3, VectorPropertiesXYZ>));
            RegisterPropertyInspector(typeof(Clicross.Float4).ToString(), typeof(Inspector_Property_GenericVector<Clicross.Float4, VectorPropertiesXYZW>));
            RegisterPropertyInspector(typeof(Double2).ToString(), typeof(Inspector_Property_GenericVector<Double2, VectorPropertiesXY>));
            RegisterPropertyInspector(typeof(Double3).ToString(), typeof(Inspector_Property_GenericVector<Double3, VectorPropertiesXYZ>));
            RegisterPropertyInspector(typeof(Double4).ToString(), typeof(Inspector_Property_GenericVector<Double4, VectorPropertiesXYZW>));
            RegisterPropertyInspector(typeof(Clicross.Double2).ToString(), typeof(Inspector_Property_GenericVector<Clicross.Double2, VectorPropertiesXY>));
            RegisterPropertyInspector(typeof(Clicross.Double3).ToString(), typeof(Inspector_Property_GenericVector<Clicross.Double3, VectorPropertiesXYZ>));
            RegisterPropertyInspector(typeof(Clicross.Double4).ToString(), typeof(Inspector_Property_GenericVector<Clicross.Double4, VectorPropertiesXYZW>));
            RegisterPropertyInspector(typeof(Quaternion).ToString(), typeof(Inspector_Property_Quaternion_Euler));
            RegisterPropertyInspector(typeof(UniqueString).ToString(), typeof(Inspector_Property_String));
            RegisterPropertyInspector("StbDebugEntity", typeof(Inspector_Property_StbDebugEntity));
            RegisterPropertyInspector("FloatWithTrack", typeof(Inspector_Property_FloatWithTrack));
            RegisterPropertyInspector("Enum", typeof(Inspector_Property_Enum));
            RegisterPropertyInspector("DynamicEnum", typeof(Inspector_Property_DynamicEnum));
            RegisterPropertyInspector("List", typeof(Inspector_Property_List));
            RegisterPropertyInspector("ParamList", typeof(Inspector_Property_ParamList));
            RegisterPropertyInspector("Struct", typeof(Inspector_Property_Struct));
            RegisterPropertyInspector("Dictionary", typeof(Inspector_Property_Dictionary));
            RegisterPropertyInspector("FloatCurveTrack", typeof(Inspector_Property_FloatCurveTrack));
            RegisterPropertyInspector(typeof(Clicross.FloatCurveTrack).ToString(), typeof(Inspector_Property_CliFloatCurveTrack));
            RegisterPropertyInspector("MinMaxCurve", typeof(Inspector_Property_MinMaxCurve));
            RegisterPropertyInspector("ColorCurve", typeof(Inspector_Property_ColorCurve));
            RegisterPropertyInspector("MinMaxGradient", typeof(Inspector_Property_MinMaxGradient));
            RegisterPropertyInspector("SelectionList", typeof(Inspector_Property_SelectionList));
            RegisterPropertyInspector("EnumWithTab", typeof(Inspector_Property_EnumWithTab));
            RegisterPropertyInspector("LayerMask", typeof(Inspector_Property_LayerMask));
            RegisterPropertyInspector("Layer", typeof(Inspector_Property_Layer));
            RegisterPropertyInspector(typeof(CollisionMask).FullName, typeof(Inspector_Property_CollisionMask));
            RegisterPropertyInspector(typeof(InstanceData).FullName, typeof(Inspector_Property_InstanceData));
            RegisterPropertyInspector(typeof(InstanceMemberData).FullName, typeof(Inspector_Property_InstanceMemeberData));
        }

        public void RegisterObjectInspector(Type ObjectType, Type InspectorType)
        {
            _ObjectInspectors[ObjectType] = InspectorType;
        }

        public void RegisterSubclassObjectInspector(Type BaseType, Type InspectorType)
        {
            RegisterObjectInspector(BaseType, InspectorType);

            IEnumerable<Type> Subclasses = from assembly in AppDomain.CurrentDomain.GetAssemblies()
                                           from type in assembly.GetTypes()
                                           where type.IsSubclassOf(BaseType)
                                           select type;
            foreach (Type Subclass in Subclasses)
            {
                if (_ObjectInspectors.ContainsKey(Subclass))
                    continue;
                RegisterObjectInspector(Subclass, InspectorType);
            }
        }

        public Inspector CreateObjectInspector(Type ObjectType)
        {
            Type InspectorType;
            _ObjectInspectors.TryGetValue(ObjectType, out InspectorType);
            if (InspectorType == null)
            {
                return new Inspector_Object();
            }
            Inspector Inspector = (Inspector)Activator.CreateInstance(InspectorType);
            return Inspector;
        }

        public void RegisterPropertyInspector(string PropertyType, Type Inspector)
        {
            _PropertyInspectors[PropertyType] = Inspector;
        }

        public Inspector CreatePropertyInspector(string PropertyType, bool bIsEnum)
        {
            if (bIsEnum && !PropertyType.Contains("Enum"))
            {
                PropertyType = "Enum";
            }
            Type InspectorType;
            _PropertyInspectors.TryGetValue(PropertyType, out InspectorType);
            if (InspectorType == null)
            {
                return new Inspector_Property_General();
            }
            Inspector Inspector = (Inspector)Activator.CreateInstance(InspectorType);
            return Inspector;
        }

        public bool ContainsProperty(string PropertyType)
        {
            return _PropertyInspectors.ContainsKey(PropertyType);
        }

        public void TriggerPropertyValueChangedEvent(Inspector_Property PropertyInspector, object Object, string Name, object NewValue)
        {
            if (PropertyValueChangedEvent != null)
            {
                PropertyValueChangedEvent(PropertyInspector, Object, Name, NewValue);
            }
        }
    }
}
