using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class ValueExtraProperty
    {
        public bool _bHasMultipleValues;
        public List<string> _HaveMultipleValuesSubProperties;

        public ValueExtraProperty()
        {
            _bHasMultipleValues = false;
            _HaveMultipleValuesSubProperties = new List<string>();
        }

        public void Clear()
        {
            _bHasMultipleValues = false;
            if (_HaveMultipleValuesSubProperties.Count > 0)
            {
                _HaveMultipleValuesSubProperties.Clear();
            }
        }
    }

    public class SubProperty
    {
        public string _SubProperty;
        public ObjectProperty _ObjectProperty;
        public EditOperation_ModifyProperties2 _EditOperation;

        public SubProperty()
        {
            _SubProperty = null;
            _ObjectProperty = null;
            _EditOperation = null;
        }
    }

    public delegate object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty);

    public delegate void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty);

    public delegate void RevertPropertyValueFunction(object Object, string PropertyName);

    public delegate void SetPropertyKeyFrameFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty);

    public delegate bool GetEnableFunction();

    public delegate void SetEnableFunction(bool value);

    public class ObjectProperty
    {
        public object Object;
        public string Name;
        public string DisplayName;
        public Type Type;
        public bool ReadOnly = false;
        public bool Advanced = false;
        public bool CheckEnable = false;
        public decimal ValueMin = decimal.MinValue;
        public decimal ValueMax = decimal.MaxValue;
        public decimal ValueStep = decimal.Zero;
        public bool KeyFrame = false;
        public object DefaultValue;
        public bool Override;
        public PropertyInfoAttribute PropertyInfoAttribute;
        public GetPropertyValueFunction GetPropertyValueFunction;
        public SetPropertyValueFunction SetPropertyValueFunction;
        public RevertPropertyValueFunction RevertPropertyValueFunction;
        public SetPropertyValueFunction RawSetPropertyValueFunction;
        public GetEnableFunction GetEnableFunction;
        public SetEnableFunction SetEnableFunction;

        public SetPropertyValueFunction Get_RawSetPropertyValueFunction()
        {
            if (RawSetPropertyValueFunction != null)
            {
                return RawSetPropertyValueFunction;
            }
            else
            {
                return SetPropertyValueFunction;
            }
        }
    }

    public class Inspector_Property : Inspector
    {
        public const int PROPERTY_FONT_SIZE = Control.UI_DEFAULT_FONT_SIZE;
        public const string MULTIPLE_VALUES_STRING = "<Multiple Values>";

        public static List<Type> NeedRangeTypes = new List<Type>{
            typeof(Inspector_Property_Double3),
            typeof(Inspector_Property_Float),
            typeof(Inspector_Property_Float2),
            typeof(Inspector_Property_Float3),
            typeof(Inspector_Property_Float4),
            typeof(Inspector_Property_General),
            typeof(Inspector_Property_Quaternion),
            typeof(Inspector_Property_Rangef),
            typeof(Inspector_Property_Vector2),
            typeof(Inspector_Property_Vector3),
            typeof(Inspector_Property_Vector4),
            typeof(Inspector_Property_FloatWithTrack)
        };

        protected object _Object;
        protected ObjectProperty _ObjectProperty;
        protected PropertyInfoAttribute _PropertyInfoAttribute;

        public InspectorSplitter _Splitter;
        protected Button _ButtonRevert;
        protected bool _bCanRevert = false;

        protected Button _ButtonKeyFrame;
        protected bool _bCanKeyFrame = true;

        protected Button _ButtonToolTips;
        protected Check _EnableCheck;
        protected Label _LabelName;

        protected ValueExtraProperty _ValueExtraProperty;
        protected SubProperty _SubProperty;


        public delegate void OnRevertClick(object obj, ObjectProperty prop);

        protected Inspector_Property()
        {
            _ValueExtraProperty = new ValueExtraProperty();
            _SubProperty = new SubProperty();
            _Splitter = new InspectorSplitter(this);
            _SelfContainer.AddChild(_Splitter);
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            _Object = ObjectProperty.Object;
            _ObjectProperty = ObjectProperty;
            _bIsAdvanced = ObjectProperty.Advanced;
            _bCanKeyFrame = ObjectProperty.KeyFrame;
            PropertyInfoAttribute PropertyInfoAttribute = ObjectProperty.PropertyInfoAttribute;
            if (PropertyInfoAttribute != null)
            {
                _PropertyInfoAttribute = PropertyInfoAttribute;
            }
            else
            {
                _PropertyInfoAttribute = new PropertyInfoAttribute();
            }

            if (_ObjectProperty.RawSetPropertyValueFunction == null)
            {
                _ObjectProperty.RawSetPropertyValueFunction = _ObjectProperty.SetPropertyValueFunction;
            }
            _SubProperty._ObjectProperty = _ObjectProperty;

            string PropertyName = _ObjectProperty.DisplayName;
            if (PropertyName == null || PropertyName == "")
            {
                PropertyName = _ObjectProperty.Name;
            }

            string ToolTips = _PropertyInfoAttribute.ToolTips;
            if (NeedRangeTypes.Contains(GetType()))
            {
                if (ToolTips.Length > 0)
                {
                    ToolTips += "\n";
                }
                if (ObjectProperty.ValueMin == decimal.MinValue && ObjectProperty.ValueMax == decimal.MaxValue)
                {
                    ToolTips += "(Please specify property range)";
                }
                else
                {
                    string ValueMin = ObjectProperty.ValueMin == decimal.MinValue ? "-∞" : ObjectProperty.ValueMin.ToString();
                    string ValueMax = ObjectProperty.ValueMax == decimal.MaxValue ? "+∞" : ObjectProperty.ValueMax.ToString();
                    ToolTips += string.Format("[{0}, {1}]", ValueMin, ValueMax);
                }
            }

            if (ToolTips != null && ToolTips != "")
            {
                _ButtonToolTips = new Button();
                _ButtonToolTips.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
                _ButtonToolTips.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
                _ButtonToolTips.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
                _ButtonToolTips.SetToolTips(ToolTips);
                _ButtonToolTips.LeftMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
                {
                    Inspector_Property_SelectionList.ChildControlHandleLeftMouseDown(Sender, MouseX, MouseY, ref bContinue);
                };
                GetNameContainer().AddChild(_ButtonToolTips);
            }

            _ButtonRevert = new Button();
            _ButtonRevert.SetImage(UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/Undo.nda"));
            _ButtonRevert.ClickedEvent += OnRevertButtonClicked;
            _ButtonRevert.SetVisible(false);

            _ButtonKeyFrame = new Button();
            _ButtonKeyFrame.SetImage(UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/KeyFrameGrey.nda"));
            _ButtonKeyFrame.ClickedEvent += OnKeyFrameButtonClicked;
            _ButtonKeyFrame.SetVisible(false);
            UpdateKeyFrame();

            // Special for material and general case
            if (_ObjectProperty.RevertPropertyValueFunction != null || _ObjectProperty.DefaultValue != null)
            {
                _bCanRevert = true;
            }
            if (!_ObjectProperty.ReadOnly)
            {
                _SelfContainer.AddChild(_ButtonRevert);
                GetNameContainer().AddChild(_ButtonKeyFrame);
            }

            if (_ObjectProperty.CheckEnable)
            {
                _EnableCheck = new Check();
                _EnableCheck.Initialize();
                _EnableCheck.SetChecked(_ObjectProperty.GetEnableFunction());
                _EnableCheck.ClickedEvent += (Check sender) => { _ObjectProperty.SetEnableFunction(sender.GetChecked()); };
                _EnableCheck.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
                _EnableCheck.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
                _EnableCheck.SetAutoCheck(true);
                _EnableCheck.SetEnable(true);
                GetNameContainer().AddChild(_EnableCheck);
            }

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetText(PropertyName);
            _LabelName.SetFontSize(PROPERTY_FONT_SIZE);
            _LabelName.SetTextAlign(TextAlign.CenterLeft);
            GetNameContainer().AddChild(_LabelName);
        }

        public void SetDefaultValue(object v)
        {
            _ObjectProperty.DefaultValue = v;
        }

        public override void SetPropertyReadOnly(string propertyName, bool readOnly)
        {
        }

        public void DisableToolTips()
        {
            if (_ButtonToolTips != null)
            {
                _ButtonToolTips.SetToolTips("");
            }
        }

        public string GetPropertyName()
        {
            return _ObjectProperty.Name;
        }
        public string GetPropertyDisplayName()
        {
            return _ObjectProperty.DisplayName;
        }

        public int GetNameWidth()
        {
            return GetNameContainer().GetWidth();
        }

        public Control GetNameContainer()
        {
            return _Splitter.GetLeftControl();
        }

        public AlignedPanel GetValueContainer()
        {
            return _Splitter.GetRightControl() as AlignedPanel;
        }

        protected int GetValueWidth()
        {
            int EditValueWidth = GetValueContainer().GetWidth();

            if (_ParentInspector is Inspector_Property_List)
            {
                EditValueWidth -= (20 + SPAN_X);
            }

            return EditValueWidth;
        }

        public void InnerUpdateLayout(int Width, int Height, ref int Y)
        {
            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height + SPAN_Y;
            _Splitter.UpdateLayout(Width - BUTTON_WIDTH, Height);
            if (_ButtonRevert != null)
            {
                _ButtonRevert.SetPosition(_Splitter.GetEndX(), 0, BUTTON_WIDTH, Height);
            }

            int LabelNameX = SPAN_X + GetIndent();
            if (_EnableCheck != null)
            {
                _EnableCheck.SetPosition(LabelNameX, SPAN_Y, 20, 20);
                LabelNameX += 30;
            }
            if (_LabelName != null)
            {
                int LabelNameWidth = _LabelName.CalculateTextWidth();
                _LabelName.SetPosition(LabelNameX, 2, LabelNameWidth, Height);
                _ButtonKeyFrame.SetPosition(_Splitter.GetLeftControl().GetEndX() - Height, 0, Height, Height);
                if (_ButtonToolTips != null)
                {
                    _ButtonToolTips.SetPosition(LabelNameX, 0, LabelNameWidth, Height);
                }
            }
            
           

            base.UpdateLayout(Width, ref Y);
        }
        public override void Update()
        {
            var lpanel = GetNameContainer() as Panel;
            var panel = _Splitter.GetRightControl() as AlignedPanel;
            if (lpanel._bHover || panel._bHover)
            {
                GetNameContainer().SetBackgroundColor(Color.FromRGBA(0, 100, 155, 255));
                _Splitter.GetRightControl().SetBackgroundColor(Color.FromRGBA(0, 100, 155, 255));
            }
            else
            {
                GetNameContainer().SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
                _Splitter.GetRightControl().SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);

            }
        }
        public override void UpdateLayout(int Width, ref int Y)
        {
            InnerUpdateLayout(Width, DEFAULT_HEIGHT, ref Y);
        }

        public void UpdateRevertButton(object Value)
        {
            if (_bCanRevert)
            {
                bool visible;
                if (_ObjectProperty.DefaultValue != null && _ObjectProperty.DefaultValue.ToString() != "MaterialOverride")
                {
                    if (this is Inspector_Property_Color)
                    {
                        Float3 Default = ((Inspector_Property_Color)this).Color_ValueToFloat3(_ObjectProperty.DefaultValue);
                        Float3 NewValue = ((Inspector_Property_Color)this).Color_ValueToFloat3(Value);
                        visible = !(Default.x == NewValue.x && Default.y == NewValue.y && Default.z == NewValue.z);
                    }
                    else
                    {
                        visible = !Equals(_ObjectProperty.DefaultValue, Value);
                    }
                }
                else
                {
                    visible = _ObjectProperty.Override;
                }
                _ButtonRevert.SetVisible(visible);
            }
            else
            {
                _ButtonRevert.SetVisible(false);
            }
        }

        public void UpdateKeyFrame()
        {
            if (_bCanKeyFrame && CinematicUI.GetInstance().IsVisible() && IsSequencerEditStatus())
            {
                _ButtonKeyFrame.SetVisible(true);
                if (IsExistMaterialProperty() || IsExistCompAndProp())
                {
                    _ButtonKeyFrame.SetImage(UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/KeyFrame.nda"));
                }
                else
                {
                    _ButtonKeyFrame.SetImage(UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/KeyFrameGrey.nda"));
                }
            }
            else
            {
                _ButtonKeyFrame.SetVisible(false);
            }
        }

        public object GetPropertyValue()
        {
            object PropertyValue = _ObjectProperty.GetPropertyValueFunction(_Object, _ObjectProperty.Name, _ValueExtraProperty);
            UpdateRevertButton(PropertyValue);
            return PropertyValue;
        }

        public void SetPropertyValue(object NewValue)
        {
            if (_ObjectProperty.SetPropertyValueFunction != null)
            {
                _ObjectProperty.SetPropertyValueFunction(_Object, _ObjectProperty.Name, NewValue, _SubProperty);
                _ObjectProperty.Override = true;
                UpdateRevertButton(NewValue);
            }
            InspectorManager.GetInstance().TriggerPropertyValueChangedEvent(this, _Object, _ObjectProperty.Name, NewValue);
        }

        public virtual void RevertPropertyValue()
        {
            if (this is Inspector_Property_Color)
            {
                SetPropertyValue(((Inspector_Property_Color)this).Color_Float3ToValue(_ObjectProperty, _ObjectProperty.DefaultValue));
            }
            else
            {
                SetPropertyValue(_ObjectProperty.DefaultValue);
            }
            ReadValue();
        }

        public void OnRevertButtonClicked(Button Sender)
        {
            if (_bCanRevert == false)
                return;

            object OldValue;
            if (this is Inspector_Property_Color)
            {
                OldValue = ((Inspector_Property_Color)this).GetOldColor();
            }
            else
            {
                OldValue = GetPropertyValue();
            }
            OldValue = CloneHelper.CloneByConstructor(OldValue);

            if (_ObjectProperty.RevertPropertyValueFunction != null)
            {
                _ObjectProperty.RevertPropertyValueFunction?.Invoke(_Object, _ObjectProperty.Name);
                _ObjectProperty.Override = false;
                if (_ObjectProperty.DefaultValue.ToString() != "MaterialOverride")
                    _ObjectProperty.DefaultValue = GetPropertyValue();
                _ButtonRevert.SetVisible(false);
            }
            else
            {
                RevertPropertyValue();
            }

            object NewValue = GetPropertyValue();
            NewValue = CloneHelper.CloneByConstructor(NewValue);
            if (object.Equals(OldValue, NewValue) == false)
            {
                EditOperation_ModifyProperty EditOperation = new EditOperation_ModifyProperty(_ObjectProperty, OldValue, NewValue, this);
                EditOperationManager.GetInstance().AddOperation(EditOperation);
            }
        }

        public string ProcessJointString(string OldString, Inspector Inspector)
        {
            string NewString = "";
            string[] stringArray = OldString.Split(".");
            List<string> stringList = new List<string>();
            foreach (var Value in stringArray)
            {
                if (Value == "CrossEditor")
                {
                    break;
                }
                else
                {
                    stringList.Add(Value);
                }
            }
            if (Inspector.GetName().Contains("SkyLightComponent"))
            {
                stringList.Add("SkyLight");
            }
            stringList.Reverse();
            for (int i = 0; i < stringList.Count; i++)
            {
                if (i == stringList.Count - 1)
                {
                    NewString += stringList[i];
                }
                else
                {
                    NewString += stringList[i] + ".";
                }
            }
            return NewString;
        }

        public virtual void OnKeyFrameButtonClicked(Button Sender)
        {
            if (!CinematicUI.GetInstance().IsVisible())
            {
                return;
            }

            object NewValue = GetPropertyValue();
            if (IsMaterialInstanceProperty())
            {
                if (Sender == null)
                {
                    if (CinematicUI.GetInstance().IsVisible() || CinematicUI.GetInstance().IsFocused())
                    {
                        NewValue = GetPropertyEditValue();
                    }
                }

                Entity Entity = null;
                World World = EditorSceneUI.GetInstance().GetScene().GetWorld();
                var Ptr = ControllableUnitSystemG.GetCurveControllerRes(World.GetNativePointer(), CinematicUI.GetInstance().GetCurveCtrPath());
                CurveControllerRes curveControllerRes = new CurveControllerRes(Ptr, false);

                Material InspectMtl = (_Object is Material) ? (_Object as Material) : null;
                if (InspectMtl == null) return;

                int ModelIndex = 0;
                int SubModelIndex = 0;
                bool FoundModel = false;
                foreach (var data in curveControllerRes.mCurveData)
                {
                    FoundModel = false;
                    Entity = World.Root.SearchChildByEUID(data.mEntityID) != null ? World.Root.SearchChildByEUID(data.mEntityID) : World.Root.SearchChildByPrefabEuid(data.mEntityID);
                    if (Entity != null && Entity.HasComponent(typeof(ModelComponent)))
                    {
                        ModelComponent Model = Entity.GetComponent(typeof(ModelComponent)) as ModelComponent;
                        ModelIndex = 0;
                        SubModelIndex = 0;
                        foreach (var M in Model.GetModels())
                        {
                            foreach (var Sub in M.LODProperties)
                            {
                                if (InspectMtl.Path == ResourceManager.Instance().ConvertGuidToPath(Sub.SubModels[0].MaterialPath) + "_Instance")
                                {
                                    FoundModel = true;
                                    break;
                                }

                                SubModelIndex++;
                            }
                            if (FoundModel) break;
                            ModelIndex++;
                            SubModelIndex = 0;
                        }
                    }
                    if (FoundModel) break;
                }

                if (!FoundModel) return;

                string ParamName = _ObjectProperty.Name;
                CinematicUI.GetInstance().InspectorAddKey(Entity, ModelIndex, SubModelIndex, ParamName, NewValue);
            }
            else
            {
                Inspector Inspector = this;
                string JointString = Inspector.GetName();
                while (!(Inspector is Inspector_Component))
                {
                    Inspector = Inspector.GetParentInspector();
                    JointString = JointString + "." + Inspector.GetName();
                }
                string ProcessString = ProcessJointString(JointString, Inspector);
                ((Inspector_Component)Inspector).SetPropertyKeyFrame(ProcessString, NewValue);
            }
            UpdateKeyFrame();
        }

        public bool IsExistCompAndProp()
        {
            if (IsMaterialInstanceProperty())
            {
                return false;
            }
            Inspector Inspector = this;
            string JointString = Inspector.GetName();
            string ProcessString = "";
            bool IsGroupedProperty = false;
            while (!(Inspector is Inspector_Component))
            {
                if (Inspector is Inspector_GroupedProperty)
                {
                    ProcessString = JointString.Substring(0, JointString.IndexOf("."));
                    IsGroupedProperty = true;
                    Inspector = Inspector.GetParentInspector();
                    break;
                }
                else
                {
                    Inspector = Inspector.GetParentInspector();
                    if (Inspector is null)
                        return false;
                    JointString = JointString + "." + Inspector.GetName();
                }
            }
            if (!IsGroupedProperty)
            {
                ProcessString = ProcessJointString(JointString, Inspector);
            }
            return ((Inspector_Component)Inspector).IsExistCompAndProp(ProcessString);
        }

        public bool IsExistMaterialProperty()
        {
            List<string> kFramedProperty = QueryCurMatrial();
            if (!(kFramedProperty.Count != 0 && IsSequencerNow())) return false;
            Inspector Inspector = this;
            while (Inspector != null)
            {
                if (Inspector is Inspector_MaterialInstance)
                {
                    if (kFramedProperty.Contains(_ObjectProperty.Name))
                        return true;
                    else
                        return false;
                }
                Inspector = Inspector.GetParentInspector();
            }
            return false;
        }

        public bool IsMaterialInstanceProperty()
        {
            List<string> kFramedProperty = QueryCurMatrial();
            Inspector Inspector = this;
            while (Inspector != null)
            {
                if (Inspector is Inspector_MaterialInstance)
                {
                    return true;
                }
                Inspector = Inspector.GetParentInspector();
            }
            return false;
        }

        public bool IsMaterialProperty()
        {
            List<string> kFramedProperty = QueryCurMatrial();
            Inspector Inspector = this;
            while (Inspector != null)
            {
                if (Inspector is Inspector_Material)
                {
                    return true;
                }
                Inspector = Inspector.GetParentInspector();
            }
            return false;
        }

        public bool IsSequencerNow()
        {
            bool IsSequencerNow = CinematicUI.GetInstance().IsDockingCardActive();
            return IsSequencerNow;
        }

        public bool IsSequencerEditStatus()
        {
            bool IsSequencerEdit = CinematicUI.GetInstance().GetEditStatus() == CinematicUI.EditStatus.EnterEdit;
            return IsSequencerEdit;
        }

        public List<string> QueryCurMatrial()
        {
            List<string> kFramedProperty = new List<string>();
            Inspector RootInspector = GetRootInspector();
            if (RootInspector is Inspector_MaterialInstance MaterialInstanceInspector && MaterialInstanceInspector.GetMaterial() != null)
            {
                ComponentTrackHolder ComponentTrackHolder = CinematicUI.GetInstance().QueryCurResourceMaterial(MaterialInstanceInspector.GetMaterial());
                if (ComponentTrackHolder != null)
                {
                    kFramedProperty = CinematicUI.GetInstance().RefreshCurResourceMaterial(MaterialInstanceInspector.GetMaterial(), ComponentTrackHolder);
                }
            }
            return kFramedProperty;
        }

        public void RecordAndWriteValue()
        {
            RecordAndWriteValue(() => { });
        }

        public void RecordAndWriteValue(Operation Operation)
        {
            if (_SubProperty._SubProperty != null)
            {
                _SubProperty._EditOperation = new EditOperation_ModifyProperties2();
            }

            object OldValue = GetPropertyValue();
            OldValue = CloneHelper.CloneByConstructor(OldValue);
            if (Operation != null)
            {
                Operation();
            }

            object NewValue = null;
            if (IsSequencerEditStatus() && IsMaterialInstanceProperty())
            {
                NewValue = GetPropertyEditValue();
            }
            else
            {
                if (IsMaterialInstanceProperty())
                {
                    return;
                }
                WriteValue();
                NewValue = GetPropertyValue();
            }

            NewValue = CloneHelper.CloneByConstructor(NewValue);
            if (_SubProperty._SubProperty != null)
            {
                DebugHelper.Assert(_SubProperty._EditOperation != null);
                _SubProperty._EditOperation.AddModifyProperty(_Object, _ObjectProperty, OldValue, NewValue);
                EditOperationManager.GetInstance().AddOperation_AutoCombine(_SubProperty._EditOperation);
                _SubProperty._EditOperation = null;
            }
            else
            {
                if (object.Equals(OldValue, NewValue) == false)
                {
                    EditOperation_ModifyProperty EditOperation = new EditOperation_ModifyProperty(_ObjectProperty, OldValue, NewValue, this);
                    EditOperationManager.GetInstance().AddOperation_AutoCombine(EditOperation);
                }
                if (IsSequencerEditStatus() && IsSequencerNow() && !IsMaterialProperty() && (IsMaterialInstanceProperty() || IsExistCompAndProp()))
                {
                    OnKeyFrameButtonClicked(null);
                }
            }
        }

        public override string GetName()
        {
            return GetPropertyName();
        }

        protected void SetSubProperty(string SubProperty)
        {
            _SubProperty._SubProperty = SubProperty;
        }

        protected void ClearSubProperty()
        {
            _SubProperty._SubProperty = null;
        }

        public void SetInspectorRevert(bool flag)
        {
            _bCanRevert = flag;
        }
    }
}
