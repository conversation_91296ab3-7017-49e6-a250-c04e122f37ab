using CEngine;
using Clicross;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_MaterialInstanceDefines : Inspector
    {
        MaterialInstanceDefines mDefines;

        public override void InspectObject(object Object, object Tag = null)
        {
            mDefines = Object as MaterialInstanceDefines;

            ClearChildInspectors();

            InspectProperties();
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    return PropertyInfo.GetValue(Object);
                }
                return null;
            };

            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(Object, PropertyValue);
                    PropertyModifiedEvent?.Invoke(Object, PropertyInfo);
                }
                WriteValue();
            };
        }

        void InspectProperties()
        {
            List<PropertyInfo> propertyInfos = PropertyCollector.CollectPropertiesOfType(mDefines.GetType());

            AddPropertyInspector(propertyInfos[0], mDefines);

            foreach (PropertyInfo propertyInfoEnable in propertyInfos)
            {
                if (propertyInfoEnable.Name.EndsWith("Enable"))
                {
                    PropertyInfo propertyInfoValue = propertyInfos.Find((PropertyInfo info) => { return propertyInfoEnable.Name.StartsWith(info.Name) && info != propertyInfoEnable; });
                    AddPropertyInspectorWithEnable(propertyInfoEnable, propertyInfoValue, mDefines);
                }
            }

            foreach (var parameterGroup in mDefines.ParameterGroups)
            {
                Inspector inspector = new Inspector_MaterialParameterGroup(true);
                inspector.SetPropertyModifiedFunction(PropertyModifiedEvent);
                AddChildInspector(inspector);
                inspector.InspectObject(parameterGroup);
            }
        }

        public void AddPropertyInspectorWithEnable(PropertyInfo PropertyInfoEnable, PropertyInfo PropertyInfoValue, object BindObject, string overrideName = "")
        {
            PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfoValue, BindObject);
            if (PropertyInfoAttribute.bHide)
            {
                return;
            }
            if (PropertyInfoAttribute.bAdvanced)
            {
                if (_bHasAdvancedChild == false)
                {
                    InitializeAdvancedButton();
                }
                _bHasAdvancedChild = true;
            }
            if (PropertyInfoAttribute.bModified)
            {
                if (_bHasModifyChild == false)
                {
                    InitializeModifiedButton(PropertyInfoAttribute.DisplayName);
                }
                _bHasModifyChild = true;
            }
            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = BindObject;
            ObjectProperty.Name = overrideName == "" ? PropertyInfoValue.Name : overrideName;
            if (PropertyInfoAttribute.DisplayName != "")
            {
                ObjectProperty.DisplayName = PropertyInfoAttribute.DisplayName;
            }
            else
            {
                ObjectProperty.DisplayName = PropertyInfoValue.Name;
            }
            ObjectProperty.Type = PropertyInfoValue.PropertyType;
            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;
            ObjectProperty.CheckEnable = true;
            ObjectProperty.SetEnableFunction = (bool value) =>
            {
                PropertyInfoEnable.SetValue(BindObject, value);
                PropertyModifiedEvent?.Invoke(null, null);
            };
            ObjectProperty.GetEnableFunction = () => { return (bool)PropertyInfoEnable.GetValue(BindObject); };
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) => { return PropertyInfoValue.GetValue(BindObject); };
            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                PropertyInfoValue.SetValue(BindObject, PropertyValue);
                PropertyModifiedEvent?.Invoke(null, null);
            };

            string PropertyTypeString = PropertyInfoValue.PropertyType.ToString();
            if (PropertyInfoAttribute.PropertyType != "")
            {
                PropertyTypeString = PropertyInfoAttribute.PropertyType;
            }
            Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, ObjectProperty.Type.IsEnum);
            AddChildInspector(Inspector_Property);
            Inspector_Property.InspectProperty(ObjectProperty);
        }
    }
}
