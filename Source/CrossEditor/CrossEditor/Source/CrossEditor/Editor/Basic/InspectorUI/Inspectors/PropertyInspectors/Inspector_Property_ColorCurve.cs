using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_Property_ColorCurve : Inspector_Property
    {
        private ColorCurve _Value;
        //private ColorCurveMode _RecordMode;

        private Button _ButtonCurve;
        private Panel _PanelCurve;

        public Inspector_Property_ColorCurve()
        {
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();
            _ButtonCurve = new Button();
            _ButtonCurve.Initialize();
            _ButtonCurve.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonCurve.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonCurve.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonCurve.SetToolTips("Edit Curve");
            _ButtonCurve.ClickedEvent += OnCurveEditOpen;
            Container.AddChild(_ButtonCurve);
            _PanelCurve = UIHelper.GetInstance().CreatePanel(Container);
            _PanelCurve.SetBackgroundColor(Color.FromRGBA(255, 255, 255, 88));
            _PanelCurve.SetBorderColor(Color.FromRGB(255, 255, 255));

            _Value = ObjectProperty.GetPropertyValueFunction(ObjectProperty.Object, ObjectProperty.Name, null) as ColorCurve;

            InitializeCheckExpand();
            GetNameContainer().AddChild(_CheckExpand);

            AddChildInspectors();
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
            }
            WriteValue();
            //if (_RecordMode != _Value.Mode)
            //{
            //    AddChildInspectors();
            //    GetInspectorHandler().UpdateLayout();
            //}
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        protected virtual void AddChildInspectors()
        {
            ClearChildInspectors();
            Type Type = _Value.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            //ColorCurveMode Mode = _Value.Mode;
            //_RecordMode = Mode;

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(PropertyInfo);
                if (AttributeList != null)
                {
                    AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();
                    object Value = PropertyInfo.GetValue(_Value);
                    string PorpertyTypeName = Value != null ? Value.GetType().ToString() : PropertyInfo.PropertyType.ToString();
                    bool bContainsProperty = InspectorManager.GetInstance().ContainsProperty(PorpertyTypeName);

                    if (PropertyInfo.PropertyType.IsEnum)
                    {
                        AddPropertyInspector(PropertyInfo, _Value, PorpertyTypeName);
                    }
                    else if (AttributeData != null || bContainsProperty)
                    {
                        PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
                        if (PropertyInfo.Name == "Scaler")
                        {
                            AddPropertyInspector(PropertyInfo, _Value, PorpertyTypeName);
                        }
                        else if (PropertyInfo.Name == "RCurve" || PropertyInfo.Name == "GCurve" || PropertyInfo.Name == "BCurve" || PropertyInfo.Name == "ACurve")
                            _PanelCurve.SetVisible(true);
                    }
                }
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            {
                int CurveWidth = GetValueWidth() - 3 * SPAN_X - BUTTON_WIDTH;
                _ButtonCurve.SetPosition(0, 2, CurveWidth, 16);
                GetValueContainer().FloatToLeft(_ButtonCurve);
                _PanelCurve.SetPosition(0, 2, CurveWidth, 16);
                GetValueContainer().PlaceAsLeft(_PanelCurve);
            }

            _CheckExpand.SetPosition(SPAN_X + GetIndent() - 20, 0, 20, 20);
            _LabelName.SetX(_CheckExpand.GetEndX());
            if (_ButtonToolTips != null)
                _ButtonToolTips.SetX(_LabelName.GetX());
        }

        public override void ReadValue()
        {
            base.ReadValue();
        }

        public override void WriteValue()
        {
            SetPropertyValue(_Value);
        }

        private void OnCurveEditOpen(Button Sender)
        {
            if (!_PanelCurve.GetVisible())
            {
                return;
            }
            List<CurveManager> Curves = new List<CurveManager>();

            {
                CurveManager RCurve = new CurveManager();
                RCurve.EnterRepeatType = CurveRepeatType.CRT_LINEAR;
                RCurve.LeaveRepeatType = CurveRepeatType.CRT_CONSTANT;
                RCurve.RuntimeCurve = _Value.RCurve;
                RCurve.PostModifiedCurveEvent += () =>
                {
                    _Value.RCurve = RCurve.RuntimeCurve;
                };
                RCurve.Name = "R";
                Curves.Add(RCurve);
            }
            {
                CurveManager GCurve = new CurveManager();
                GCurve.EnterRepeatType = CurveRepeatType.CRT_LINEAR;
                GCurve.LeaveRepeatType = CurveRepeatType.CRT_CONSTANT;
                GCurve.RuntimeCurve = _Value.GCurve;
                GCurve.PostModifiedCurveEvent += () =>
                {
                    _Value.GCurve = GCurve.RuntimeCurve;
                };
                GCurve.Name = "G";
                Curves.Add(GCurve);
            }
            {
                CurveManager BCurve = new CurveManager();
                BCurve.EnterRepeatType = CurveRepeatType.CRT_LINEAR;
                BCurve.LeaveRepeatType = CurveRepeatType.CRT_CONSTANT;
                BCurve.RuntimeCurve = _Value.BCurve;
                BCurve.PostModifiedCurveEvent += () =>
                {
                    _Value.BCurve = BCurve.RuntimeCurve;
                };
                BCurve.Name = "B";
                Curves.Add(BCurve);
            }
            {
                CurveManager ACurve = new CurveManager();
                ACurve.EnterRepeatType = CurveRepeatType.CRT_LINEAR;
                ACurve.LeaveRepeatType = CurveRepeatType.CRT_CONSTANT;
                ACurve.RuntimeCurve = _Value.ACurve;
                ACurve.PostModifiedCurveEvent += () =>
                {
                    _Value.ACurve = ACurve.RuntimeCurve;
                };
                ACurve.Name = "A";
                Curves.Add(ACurve);
            }

            LinearColorCurveEditorUI.GetInstance().LoadFromCurves(Curves);
            MainUI.GetInstance().ActivateDockingCard_LinearColorCurveEditor();
        }
    }
}