using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_Property_Struct : Inspector_Property
    {
        protected object _Struct;

        public Inspector_Property_Struct()
        {

        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);

            _Struct = ObjectProperty.GetPropertyValueFunction(ObjectProperty.Object, ObjectProperty.Name, null);

            InitializeCheckExpand();
            GetNameContainer().AddChild(_CheckExpand);

            AddChildInspectors();
        }

        public void UpdateProperty(Object NewStruct)
        {
            _Struct = NewStruct;
            InitializeCheckExpand();
            AddChildInspectors();
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            //Object = _ObjectProperty.GetPropertyValueFunction(_Object, _ObjectProperty.Name, null);

            if (Object == null) return null;
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
            }
            WriteValue();
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        protected virtual void AddChildInspectors()
        {
            Type Type = _Struct.GetType();
            ClearChildInspectors();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(PropertyInfo);
                if (AttributeList != null)
                {
                    AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();

                    object Value = PropertyInfo.GetValue(_Struct);
                    string PorpertyTypeName = Value != null ? Value.GetType().ToString() : PropertyInfo.PropertyType.ToString();
                    bool bContainsProperty = InspectorManager.GetInstance().ContainsProperty(PorpertyTypeName);
                    bool isEnum = PropertyInfo.PropertyType.IsEnum;
                    if (AttributeData != null || bContainsProperty || isEnum)
                    {
                        PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
                        if (PropertyInfoAttribute.PropertyType == "")
                        {
                            AddPropertyInspector(PropertyInfo, _Struct, PorpertyTypeName);
                        }
                        else
                        {
                            AddPropertyInspector(PropertyInfo, _Struct);
                        }
                    }
                }

            }
        }
        public override void Update()
        {
            foreach(var ins in _ChildInspectors)
            {
                ins.Update();
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            _CheckExpand.SetPosition(SPAN_X + GetIndent() - 20, 0, 20, 20);
            _LabelName.SetX(_CheckExpand.GetEndX());
            if (_ButtonToolTips != null)
                _ButtonToolTips.SetX(_LabelName.GetX());
        }

        public override void ReadValue()
        {
            base.ReadValue();
        }

        public override void WriteValue()
        {
            base.WriteValue();
            SetPropertyValue(_Struct);
        }
    }
}
