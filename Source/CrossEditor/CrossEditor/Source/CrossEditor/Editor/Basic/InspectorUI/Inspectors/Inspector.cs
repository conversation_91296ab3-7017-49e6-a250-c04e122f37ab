using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{

    public class Inspector
    {
        public const int SPAN_X = 5;
        public const int SPAN_Y = 6;
        public const int DEFAULT_HEIGHT = 25;
        public const int DEFAULT_WIDTH = 125;
        public const int BUTTON_WIDTH = 24;

        // 颜色常量
        public static Color Color_Red = Color.FromRGB(0xCB, 0x26, 0x00);
        public static Color Color_Green = Color.FromRGB(0x67, 0xA9, 0x00);
        public static Color Color_Blue = Color.FromRGB(0x2C, 0x7E, 0xED);

        public const string PROPERTY_TYPE_FILE = "file";
        public const string PROPERTY_TYPE_FOLDER = "folder";

        protected InspectorHandler _InspectorHandler;
        protected Inspector _ParentInspector;
        protected List<Inspector> _ChildInspectors;

        protected Panel _SelfContainer;
        protected Panel _ChildContainer;
        protected Panel _Separator;
        protected bool _bVisible;

        // For advanced properties
        protected bool _bIsAdvanced;
        protected bool _bHasAdvancedChild;
        protected bool _bShowAdvanced;
        protected Button _AdvancedButton;
        protected Panel _AdvancedSeparator;

        // For expand
        protected bool _bShowChild;
        protected Check _CheckExpand;

        // For modify button
        protected bool _bHasModifyChild;
        protected Button _ModifyButton;
        protected Panel _ModifySeparator;

        protected List<Button> _Buttons;
        protected List<Panel> _Separators;

        public delegate void PropertyModifiedHandler(object PropertyOwner, PropertyInfo Property);
        protected PropertyModifiedHandler PropertyModifiedEvent;

        public Inspector()
        {
            _ChildInspectors = new List<Inspector>();
            _SelfContainer = new Panel();
            _SelfContainer.SetSize(0, 0);
            _SelfContainer.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ChildContainer = new Panel();
            _ChildContainer.SetSize(0, 0);
            _Separator = new Panel();
            _Separator.SetSize(0, 0);
            _Separator.SetBackgroundColor(Color.EDITOR_UI_SEPARATOR);
            _bVisible = true;
            _bShowChild = true;
            _Buttons = new List<Button>();
            _Separators = new List<Panel>();
        }

        public UIManager GetUIManager()
        {
            if (_SelfContainer != null)
            {
                return _SelfContainer.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public virtual void InspectObject(object Object, object Tag = null)
        {
        }

        public virtual void InspectProperty(ObjectProperty ObjectProperty)
        {
        }

        public virtual void SetPropertyReadOnly(string propertyName, bool readOnly)
        {
        }

        public virtual void UpdateLayout(int Width, ref int Y)
        {
            UpdateLayoutBaseImpl(Width, ref Y);
        }
        protected void UpdateLayoutBaseImpl(int Width, ref int Y)
        {
            _Separator.SetPosition(0, Y, Width, 1);  // 加粗到1像素
            Y += 1;  // 同步增加间距

            int Height = 0;
            if (_bShowChild)
            {
                // Update visble normal child inspectors
                foreach (Inspector Inspector in _ChildInspectors)
                {
                    if (Inspector._bIsAdvanced == false && Inspector._bVisible == true)
                    {
                        Inspector.UpdateLayout(Width, ref Height);
                    }
                }

                // Update advanced child inspectors
                if (_bHasAdvancedChild)
                {
                    // Whether advanced inspectors have been folded
                    if (_bShowAdvanced)
                    {
                        foreach (Inspector Inspector in _ChildInspectors)
                        {
                            if (Inspector._bIsAdvanced == true)
                            {
                                Inspector.UpdateLayout(Width, ref Height);
                            }
                        }
                    }
                    else
                    {
                        foreach (Inspector Inspector in _ChildInspectors)
                        {
                            if (Inspector._bIsAdvanced == true)
                            {
                                int Temp = 65536;
                                Inspector.UpdateLayout(0, ref Temp);
                            }
                        }
                    }
                    // Update advanced button position
                    _AdvancedButton.SetPosition(0, Height, Width, DEFAULT_HEIGHT);
                    Height += DEFAULT_HEIGHT;
                    _AdvancedSeparator.SetPosition(0, Height, Width, 2);
                    Height += 2;
                }

                if (_bHasModifyChild)
                {
                    _ModifyButton.SetPosition(0, Height, Width, DEFAULT_HEIGHT);
                    Height += DEFAULT_HEIGHT;
                    _ModifySeparator.SetPosition(0, Height, Width, 2);
                    Height += 2;
                }

                for (int btnIndex = 0; btnIndex < _Buttons.Count; ++btnIndex)
                {
                    Button btn = _Buttons[btnIndex];
                    Panel separator = _Separators[btnIndex];

                    btn.SetPosition(0, Height, Width, DEFAULT_HEIGHT);
                    Height += DEFAULT_HEIGHT;
                    separator.SetPosition(0, Height, Width, 2);
                    Height += 2;
                }
            }
            else
            {
                // Clear last position infomation
                foreach (Inspector Inspector in _ChildInspectors)
                {
                    int Temp = 65536;
                    Inspector.UpdateLayout(int.MaxValue, ref Temp);
                }
            }
            _ChildContainer.SetVisible(_bShowChild);
            _ChildContainer.SetPosition(0, Y, Width, Height);
            Y += Height;
        }

        public virtual void ReadValue()
        {
            foreach (Inspector Inspector in _ChildInspectors)
            {
                Inspector.ReadValue();
            }
        }

        public virtual void WriteValue()
        {

        }

        public virtual bool OnDropPathes(int MouseX, int MouseY, List<string> PathesDragged)
        {
            return false;
        }

        public virtual void OnDropEntity(int MouseX, int MouseY, Entity EntityDragged)
        {

        }

        public void AddChildInspector(Inspector Inspector)
        {
            if (Inspector != null)
            {
                Inspector._ParentInspector = this;
                Inspector.SetContainer(_ChildContainer);
                _ChildInspectors.Add(Inspector);
            }
        }

        public void ClearChildInspectors()
        {
            _ChildContainer.ClearChildren();
            _ChildInspectors.Clear();
            _Buttons.Clear();
            _Separators.Clear();
        }

        public void SetPropertyModifiedFunction(PropertyModifiedHandler Function)
        {
            PropertyModifiedEvent = Function;
        }

        public Inspector GetParentInspector() => _ParentInspector;

        public Inspector GetRootInspector()
        {
            Inspector Root = this;
            while (Root._ParentInspector != null)
            {
                Root = Root._ParentInspector;
            }
            return Root;
        }

        public List<Inspector> GetChildInspectors()
        {
            return _ChildInspectors;
        }

        public Inspector FindChildInspectorByPropertyName(string PropertyName)
        {
            Inspector Result = null;
            for (int i = 0; i < _ChildInspectors.Count; i++)
            {
                Inspector Inspector = _ChildInspectors[i];
                if (Inspector is Inspector_Property)
                {
                    string Name = (Inspector as Inspector_Property).GetPropertyName();
                    if (Name == PropertyName)
                    {
                        Result = Inspector;
                        break;
                    }
                    else
                    {
                        Result = Inspector.FindChildInspectorByPropertyName(PropertyName);
                        if (Result != null) break;
                    }
                }
                else
                {
                    Result = Inspector.FindChildInspectorByPropertyName(PropertyName);
                    if (Result != null) break;
                }
            }

            return Result;
        }

        public Inspector FindChildInspector(Func<Inspector, bool> predicate, bool _Recursive = true)
        {
            foreach (var item in _ChildInspectors)
            {
                if (predicate(item))
                {
                    return item;
                }
                else if (_Recursive)
                {
                    var result = item.FindChildInspector(predicate, true);
                    if (result != null) return result;
                }
            }
            return null;
        }

        public bool GetVisible()
        {
            Inspector Temp = this;
            while (Temp != null)
            {
                if (Temp._bVisible == false)
                {
                    return false;
                }
                Temp = Temp._ParentInspector;
            }
            return true;
        }

        public void SetVisible(bool Visible)
        {
            _bVisible = Visible;
            _SelfContainer.SetVisible(Visible);
            _ChildContainer.SetVisible(Visible);
            _Separator.SetVisible(Visible);
        }

        public virtual string GetName()
        {
            return ToString();
        }

        public string GetCascadeName()
        {
            if (_ParentInspector != null)
            {
                string ParentCascadeName = _ParentInspector.GetCascadeName();
                return ParentCascadeName + "#" + GetName();
            }
            else
            {
                return GetName();
            }
        }

        public Panel GetSelfContainer()
        {
            return _SelfContainer;
        }

        public Panel GetChildContainer()
        {
            return _ChildContainer;
        }

        protected void InitializeCheckExpand()
        {
            _CheckExpand = new Check();
            _CheckExpand.Initialize();
            _CheckExpand.SetImageUnchecked(UIManager.LoadUIImage("Editor/Tree/Common/Folded.png"));
            _CheckExpand.SetImageChecked(UIManager.LoadUIImage("Editor/Tree/Common/NotFolded.png"));
            _CheckExpand.SetAutoCheck(true);
            _CheckExpand.SetChecked(true);
            _CheckExpand.ClickedEvent += OnCheckExpandClicked;
        }

        public void SetCheckExpand(bool bCheckExpand)
        {
            _CheckExpand?.SetChecked(bCheckExpand);
            _bShowChild = bCheckExpand;
            UpdateFoldState();
            GetInspectorHandler().UpdateLayout();
        }

        protected void OnCheckExpandClicked(Check Sender)
        {
            SetCheckExpand(Sender.GetChecked());
        }

        public virtual void UpdateCheckExpand()
        {
            // self
            string CascadeName = GetCascadeName();
            bool bFolded = FoldStateManager.GetInstance().GetFoldState(CascadeName);
            _bShowChild = !bFolded;
            if (_CheckExpand != null)
            {
                _CheckExpand.SetChecked(_bShowChild);
            }
            // children
            foreach (Inspector Inspector in _ChildInspectors)
            {
                Inspector.UpdateCheckExpand();
            }
        }

        public void UpdateFoldState()
        {
            string CascadeName = GetCascadeName();
            FoldStateManager.GetInstance().SetFoldState(CascadeName, !_bShowChild);
        }

        public void SetInspectorHandler(InspectorHandler Handler) => _InspectorHandler = Handler;

        public InspectorHandler GetInspectorHandler()
        {
            if (_InspectorHandler != null)
            {
                return _InspectorHandler;
            }
            if (_ParentInspector != null)
            {
                return _ParentInspector.GetInspectorHandler();
            }

            throw new NotImplementedException("Please set inspector handler correctly");
        }

        public void SetContainer(Control Container)
        {
            Container.AddChild(_SelfContainer);
            Container.AddChild(_Separator);
            Container.AddChild(_ChildContainer);
        }

        public void InitializeAdvancedButton()
        {
            _AdvancedButton = new Button();
            _AdvancedButton.Initialize();
            _AdvancedButton.SetText(_bShowAdvanced ? "Hide Advanced" : "Show Advanced");
            _AdvancedButton.SetFontSize(16);
            _AdvancedButton.SetTextOffsetY(2);
            _AdvancedButton.ClickedEvent += (Sender) =>
            {
                _bShowAdvanced = !_bShowAdvanced;
                Sender.SetText(_bShowAdvanced ? "Hide Advanced" : "Show Advanced");
                GetInspectorHandler().UpdateLayout();
            };
            _ChildContainer.AddChild(_AdvancedButton);

            _AdvancedSeparator = new Panel();
            _AdvancedSeparator.SetBackgroundColor(Color.EDITOR_UI_SEPARATOR);
            _ChildContainer.AddChild(_AdvancedSeparator);
        }

        public void InitializeModifiedButton(string TextName)
        {
            string Name = TextName.Length != 0 ? TextName : "Modify";
            _ModifyButton = new Button();
            _ModifyButton.Initialize();
            _ModifyButton.SetText(Name);
            _ModifyButton.SetFontSize(16);
            _ModifyButton.SetToolTips(Name.ToLower());
            _ModifyButton.SetTextOffsetY(2);
            _ModifyButton.ClickedEvent += ModifyButtonClickedEvent;
            _ChildContainer.AddChild(_ModifyButton);

            _ModifySeparator = new Panel();
            _ModifySeparator.SetBackgroundColor(Color.EDITOR_UI_SEPARATOR);
            _ChildContainer.AddChild(_ModifySeparator);
        }

        public virtual void ModifyButtonClickedEvent(Button Sender)
        {

        }

        public virtual int GetIndent()
        {
            if (_ParentInspector == null)
            {
                return 20;
            }
            else
            {
                return _ParentInspector.GetIndent() + 14;
            }
        }

        public virtual void AddButtonInspector(MethodInfo method, object bindObject, MethodInfoAttribute methodAttribute)
        {
            if (methodAttribute.bHide)
                return;
            string buttonName = methodAttribute.DisplayName == null ? method.Name : methodAttribute.DisplayName;
            Button button = new Button();
            button.Initialize();
            button.SetText(buttonName);
            button.SetFontSize(16);
            //_ModifyButton.SetToolTips(Name.ToLower());
            button.SetTextOffsetY(2);
            object[] param = { };
            button.ClickedEvent += (Button Sender) =>
            {
                method.Invoke(bindObject, param);
            };

            Panel panel = new Panel();
            panel.SetBackgroundColor(Color.EDITOR_UI_SEPARATOR);

            _Buttons.Add(button);
            _Separators.Add(panel);

            _ChildContainer.AddChild(button);
            _ChildContainer.AddChild(panel);
        }

        public virtual void AddPropertyInspector(PropertyInfo PropertyInfo, object BindObject, string SpecificTypeName = "")
        {
            PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo, BindObject);
            if (PropertyInfoAttribute.bHide)
            {
                return;
            }
            if (PropertyInfoAttribute.bAdvanced)
            {
                if (_bHasAdvancedChild == false)
                {
                    InitializeAdvancedButton();
                }
                _bHasAdvancedChild = true;
            }
            if (PropertyInfoAttribute.bModified)
            {
                if (_bHasModifyChild == false)
                {
                    InitializeModifiedButton(PropertyInfoAttribute.DisplayName);
                }
                _bHasModifyChild = true;
            }
            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = BindObject;
            ObjectProperty.Name = PropertyInfo.Name;
            if (PropertyInfoAttribute.DisplayName != "")
            {
                ObjectProperty.DisplayName = PropertyInfoAttribute.DisplayName;
            }
            else
            {
                ObjectProperty.DisplayName = PropertyInfo.Name;
            }
            ObjectProperty.Type = PropertyInfo.PropertyType;
            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;
            BindPropertyFunction(ref ObjectProperty);

            string PropertyTypeString = PropertyInfo.PropertyType.ToString();
            if (PropertyInfoAttribute.PropertyType != "")
            {
                PropertyTypeString = PropertyInfoAttribute.PropertyType;
            }
            if (SpecificTypeName != "")
            {
                PropertyTypeString = SpecificTypeName;
            }
            Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, ObjectProperty.Type.IsEnum);

            AddChildInspector(Inspector_Property);
            Inspector_Property.InspectProperty(ObjectProperty);
        }

        public virtual void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            // Bind get&set function here.
        }

        public object GetPropertyValue(object Object, string PropertyName)
        {
            Type type = Object.GetType();
            PropertyInfo propertyInfo = type.GetProperty(PropertyName);
            if (propertyInfo != null)
            {
                return propertyInfo.GetValue(Object, null);
            }
            return null;
        }

        public virtual void DoLeftLayout(Control Container, Queue<Control> Controls, int Indent = 0)
        {
            if (Controls.Count == 0)
                return;

            Control First = Controls.Dequeue();
            First.SetX(Indent + SPAN_X);
            Control Second;
            while ((Second = Controls.Dequeue()) != null)
            {
                Second.SetX(First.GetEndX() + SPAN_X);
                First = Second;
            }
        }

        public virtual void DoRightLayout(Control Container, Queue<Control> Controls)
        {
            if (Controls.Count == 0)
                return;

            int Width = Container.GetWidth();
            Control First = Controls.Dequeue();
            First.SetX(Width - SPAN_X - First.GetWidth());
            Control Second;
            while ((Second = Controls.Dequeue()) != null)
            {
                Second.SetX(First.GetX() - SPAN_X - Second.GetWidth());
                First = Second;
            }
        }
        public virtual void Update()
        {
            foreach (var ins in _ChildInspectors)
            {
                ins.Update();
            }
        }

        public void ForEach(Action<Inspector> Action)
        {
            Action(this);
            _ChildInspectors.ForEach((Child) =>
            {
                Child.ForEach(Action);
            });
        }

        public virtual object GetPropertyEditValue()
        {
            return null;
        }
    }
}
