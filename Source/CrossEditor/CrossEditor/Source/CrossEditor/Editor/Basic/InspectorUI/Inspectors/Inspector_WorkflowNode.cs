using CEngine;
using C<PERSON>ross;
using System.Text.Json;

namespace CrossEditor
{
    public delegate void OnWorkflowFieldUpdateEvent(string item);
    class Inspector_WorkflowNode : Inspector
    {
        protected WorkflowNode _Node;
        public event OnWorkflowFieldUpdateEvent OnWorkflowFieldUpdate;
        public Inspector_WorkflowNode()
        {

        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Node = Object as WorkflowNode;

            ClearChildInspectors();
            var field_keys = _Node.m_EditorFields.Keys();
            foreach (var field_key in field_keys)
            {
                var cur_field_value = _Node.m_EditorFields[field_key];
                ObjectProperty ObjectProperty = new ObjectProperty();
                ObjectProperty.Object = field_key;
                ObjectProperty.Name = field_key.ToString();
                switch (cur_field_value.FieldType)
                {
                    case GbfValueFieldType.Bool:
                        {
                            ObjectProperty.Type = typeof(bool);
                            break;
                        }
                    case GbfValueFieldType.Integer:
                        {
                            ObjectProperty.Type = typeof(int);
                            break;
                        }
                    case GbfValueFieldType.Real:
                        {
                            ObjectProperty.Type = typeof(double);
                            break;
                        }
                    case GbfValueFieldType.String:
                        {
                            ObjectProperty.Type = typeof(string);
                            break;
                        }
                    default:
                        {
                            ObjectProperty.Type = typeof(string);
                            break;
                        }
                }
                ObjectProperty.PropertyInfoAttribute = new PropertyInfoAttribute();
                ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                Inspector_Property field_inspector = (Inspector_Property)InspectorManager.GetInstance().CreatePropertyInspector(ObjectProperty.Type.ToString(), ObjectProperty.Type.IsEnum);
                field_inspector.InspectProperty(ObjectProperty);
                AddChildInspector(field_inspector);
            }

        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            string cur_key = Object as string;
            var cur_field_value = _Node.m_EditorFields[cur_key];
            switch (cur_field_value.FieldType)
            {
                case GbfValueFieldType.Bool:
                    {
                        return JsonSerializer.Deserialize<bool>(cur_field_value.FieldValue);
                    }
                case GbfValueFieldType.Integer:
                    {
                        return JsonSerializer.Deserialize<int>(cur_field_value.FieldValue);
                    }
                case GbfValueFieldType.Real:
                    {
                        return JsonSerializer.Deserialize<double>(cur_field_value.FieldValue);
                    }
                default:
                    {
                        return cur_field_value.FieldValue;
                    }
            }

        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            string cur_key = Object as string;
            var cur_value = _Node.m_EditorFields[cur_key];
            if (cur_value.FieldType == GbfValueFieldType.String || cur_value.FieldType == GbfValueFieldType.UserObject)
            {
                cur_value.FieldValue = PropertyValue.ToString();
            }
            else
            {
                cur_value.FieldValue = JsonSerializer.Serialize(PropertyValue);
            }

            if (OnWorkflowFieldUpdate != null)
            {
                OnWorkflowFieldUpdate(cur_key);
            }

        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
        }

        public override int GetIndent()
        {
            return 0;
        }
    }
}
