using EditorUI;
using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace CrossEditor
{
    class Inspector_Property_GenericVector<T, U> : Inspector_Property where T : new()
    {
        private List<Panel> _ColorPanels = new List<Panel>();

        private List<EditWithProgress> _EditControls = new List<EditWithProgress>();
        private List<Edit> _EditValuesControls = new List<Edit>();

        private List<string> propertyNames = new List<string>();
        private int propertyNum;
        private static readonly EditorUI.Color[] propertyColors = new[] { Color_Red, Color_Green, Color_Blue };
        static EditorUI.Color GetPropertyColor(int index)
        {
            if (index >= 0 && index <= 2)
            {
                return propertyColors[index];
            }

            return EditorUI.Color.White;
        }

        static object GetPropertyNumberValue(object obj, string propertyName)
        {
            var property = typeof(T).GetProperty(propertyName);

            if (property != null && property.CanRead)
            {
                return property.GetValue(obj);
            }

            return 0;
        }

        public Inspector_Property_GenericVector() : base()
        {
            foreach (string name in Enum.GetNames(typeof(U)))
            {
                if (typeof(T).GetProperty(name.ToLower()) != null)
                {
                    propertyNames.Add(name.ToLower());
                }
                else if (typeof(T).GetProperty(name.ToUpper()) != null)
                {
                    propertyNames.Add(name.ToUpper());
                }
                else if (typeof(T).GetProperty(name) != null)
                {
                    propertyNames.Add(name);
                }
                else
                {
                    Debug.Assert(false, "No matching property " + name + " found for type:" + typeof(T).FullName);
                }
            }
            propertyNum = propertyNames.Count;

        }

        static void SetPropertyNumberValue(object obj, string propertyName, object value)
        {
            var property = typeof(T).GetProperty(propertyName);

            if (property != null && property.CanWrite)
            {
                property.SetValue(obj, value);
            }
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();


            _EditValuesControls.Clear();
            _EditControls.Clear();
            _ColorPanels.Clear();
            for (int i = 0; i < propertyNum; i++)
            {
                var _Edit = new EditWithProgress(Container);
                _Edit.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
                _Edit.TextChangedEvent += OnEditValueTextChanged;
                var _EditValue = _Edit.GetEditValue();
                var _Color = CreateColorPanel(Container, GetPropertyColor(i));

                if (_ObjectProperty.ReadOnly)
                {
                    _Edit.SetReadOnly(true);
                }

                _EditValuesControls.Add(_EditValue);
                _EditControls.Add(_Edit);
                _ColorPanels.Add(_Color);
            }

            ReadValue();
        }

        public Panel CreateColorPanel(Control Container, EditorUI.Color Color)
        {
            Panel ColorPanel = new Panel();
            ColorPanel.Initialize();
            ColorPanel.SetBackgroundColor(Color);
            Container.AddChild(ColorPanel);
            return ColorPanel;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = (GetValueWidth() - SPAN_X * 4) / 3;
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);

            for (int i = 0; i < propertyNum; i++)
            {
                _EditControls[i].SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
                GetValueContainer().FloatToLeft(_EditControls[i]);
                _ColorPanels[i].SetPosition(0, SPAN_Y + 1, 3, PROPERTY_FONT_SIZE - 2);
                GetValueContainer().PlaceAsLeft(_ColorPanels[i], 2);
            }
        }

        public virtual void GetVectorString(out string PString, int index)
        {
            object PropertyValue = GetPropertyValue();
            PString = MathHelper.NumberToString(GetPropertyNumberValue(PropertyValue, propertyNames[index]));
        }

        public virtual void MakeVectorString(T vec, string PString, int index)
        {
            Type t = typeof(T).GetProperty(propertyNames[index]).PropertyType;
            SetPropertyNumberValue(vec, propertyNames[index], MathHelper.ParseNumber(PString, t));
        }

        public override void ReadValue()
        {

            for (int i = 0; i < propertyNum; i++)
            {
                GetVectorString(out string PString, i);
                if (_ValueExtraProperty._bHasMultipleValues)
                {
                    if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains(propertyNames[i]))
                    {
                        PString = MULTIPLE_VALUES_STRING;
                    }
                }
                _EditControls[i].SetText(PString);
            }
        }

        public override void WriteValue()
        {
            base.WriteValue();
            T value = new T();

            for (int i = 0; i < propertyNum; i++)
            {
                MakeVectorString(value, _EditValuesControls[i].GetText(), i);
            }

            SetPropertyValue(value);
        }

        void OnEditValueTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            for (int i = 0; i < propertyNum; i++)
            {
                if (Sender == _EditValuesControls[i])
                {
                    SetSubProperty(propertyNames[i]);
                }
            }

            RecordAndWriteValue();

            ClearSubProperty();
        }
    }
}
