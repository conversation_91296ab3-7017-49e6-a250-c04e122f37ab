using Clicross;
using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_DynamicEnum : Inspector_Property
    {
        protected ComboBoxEx _ComboBoxValue;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _ComboBoxValue = new ComboBoxEx();
            _ComboBoxValue.Initialize();
            _ComboBoxValue.GetValueEdit().SetFontSize(PROPERTY_FONT_SIZE);
            _ComboBoxValue.GetValueEdit().SetTextAlign(TextAlign.CenterLeft);
            _ComboBoxValue.ItemSelectedEvent += (Sender) =>
            {
                GetInspectorHandler().UpdateLayout();
            };

            var enumElemCount = ((MaterialExpressionMaterialParameterCollection)_Object).m_MpcEnum.GetEnumElementsNum();
            string[] dynamicEnumNames = new string[enumElemCount];
            for (uint i = 0; i < enumElemCount; i++)
            {
                dynamicEnumNames[i] = ((MaterialExpressionMaterialParameterCollection)_Object).m_MpcEnum.GetEnumElementByIndex(i);
            }

            foreach (string enumName in dynamicEnumNames)
            {
                _ComboBoxValue.AddItem(enumName);
            }
            _ComboBoxValue.ItemSelectedEvent += OnComboBoxValueItemSelected;
            Container.AddChild(_ComboBoxValue);

            _ComboBoxValue.SetEnable(!_ObjectProperty.ReadOnly);

            _ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            _ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;

            if (_ObjectProperty.DefaultValue == null && !_ValueExtraProperty._bHasMultipleValues)
            {
                _ObjectProperty.DefaultValue = GetPropertyValue();
                _bCanRevert = true;
            }

            ReadValue();
        }

        public int GetComboBoxWidth()
        {
            int EditWidth;
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(MULTIPLE_VALUES_STRING);
            }
            else
            {
                int Index = _ComboBoxValue.GetSelectedItemIndex();
                if (Index != -1)
                {
                    EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(_ComboBoxValue.GetItemText(Index));
                }
                else
                {
                    EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast("    ");
                }
            }
            return EditWidth + 30;
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            return ((MaterialExpressionMaterialParameterCollection)Object).m_MpcEnum.GetSelectElementCopy();
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue,
            SubProperty SubProperty)
        {
            // be careful the cli, ((MaterialExpressionMaterialParameterCollection)Object).m_MpcEnum.SetXXX will only set a copy  of m_MPCEnum, it is the acutual member
            var mpc_Enum = ((MaterialExpressionMaterialParameterCollection)Object).m_MpcEnum;
            mpc_Enum.SelectElement(PropertyValue.ToString());
            ((MaterialExpressionMaterialParameterCollection)Object).m_MpcEnum = mpc_Enum;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int ComboWidth = Math.Max(GetComboBoxWidth(), DEFAULT_WIDTH);
            ComboWidth = Math.Min(ComboWidth, GetValueWidth() - 2 * SPAN_X);

            _ComboBoxValue.SetPosition(0, 2, ComboWidth, 16);
            GetValueContainer().FloatToLeft(_ComboBoxValue);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = PropertyValue.ToString();
            _ComboBoxValue.SetSelectedItemByText(PropertyValueString);
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                _ComboBoxValue.GetValueEdit().SetText(MULTIPLE_VALUES_STRING);
            }
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _ComboBoxValue.GetSelectedItemText();
            SetPropertyValue(ValueString);
        }

        void OnComboBoxValueItemSelected(ComboBoxEx Sender)
        {
            RecordAndWriteValue();
            MaterialEditorUIManager.Instance.NotifyMaterialParameterCollectionSelectChange();
        }

        public override void RevertPropertyValue()
        {
            base.RevertPropertyValue();
            GetInspectorHandler().UpdateLayout();
        }
    }
}
