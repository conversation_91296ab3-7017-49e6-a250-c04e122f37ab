using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_CharacterMovementComponent : Inspector_Component
    {
        protected CharacterMovementComponent _CharacterMovementComponenet = null;

        public Inspector_CharacterMovementComponent(List<Entity> Entities)
            : base(Entities)
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _CharacterMovementComponenet = Object as CharacterMovementComponent;
            _CharacterMovementComponenet.SyncDataFromEngine();

            base.InspectObject(Object, Tag);

            var ComponentData = _CharacterMovementComponenet.ComponentData;
            if (ComponentData == null)
            {
                return;
            }

            // None-Grouped Properties
            Type Type = ComponentData.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            HashSet<string> Categories = new HashSet<string>();
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
                if (PropertyInfoAttribute.Category == "")
                {
                    AddPropertyInspector(PropertyInfo, ComponentData);
                }
                else
                {
                    Categories.Add(PropertyInfoAttribute.Category);
                }
            }

            // Grouped Properties
            foreach (string Category in Categories)
            {
                Inspector_GroupedProperty Inspector_GroupedProperty = new Inspector_GroupedProperty(ComponentData, Category);
                Inspector_GroupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                Inspector_GroupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                Inspector_GroupedProperty.InspectObject(ComponentData);
                AddChildInspector(Inspector_GroupedProperty);
            }
        }

        public override object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            var ComponentData = _CharacterMovementComponenet.ComponentData;
            if (ComponentData == null)
            {
                return null;
            }

            Type Type = ComponentData.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(ComponentData);
            }

            return null;
        }

        public override void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            var ComponentData = _CharacterMovementComponenet.ComponentData;
            if (ComponentData == null)
            {
                return;
            }

            Type Type = ComponentData.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);

            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(ComponentData, PropertyValue);

                OnPropertyChanged();
            }
        }

        protected void OnPropertyChanged()
        {
            _CharacterMovementComponenet.OnComponentDataChanged();
            _CharacterMovementComponenet.SyncDataFromEngine();

            // Need refresh inspectors after setting PropertyInfo
            OperationQueue.GetInstance().AddOperation(() =>
            {
                ReadValue();
                GetInspectorHandler().UpdateLayout();
            });

            EditorScene.GetInstance().SetDirty();
        }
    }
}
