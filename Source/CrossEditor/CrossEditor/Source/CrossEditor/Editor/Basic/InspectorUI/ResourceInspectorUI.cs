using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class ResourceInspectorUI : InspectorUI
    {
        static Texture LockedTexture = UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/KeyFrame.nda");
        static Texture UnlockedTexture = UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/KeyFrameGrey.nda");

        static Dictionary<object, ResourceInspectorUI> _ResourceTabs = new Dictionary<object, ResourceInspectorUI>();

        static ResourceInspectorUI Instance = new ResourceInspectorUI();

        bool bLocked = false;
        Button ButtonLocked;

        public static new ResourceInspectorUI GetInstance() => Instance;

        static ResourceInspectorUI GetAvaliableInstance(object Object)
        {
            ResourceInspectorUI Instance;
            if (!_ResourceTabs.ContainsKey(Object))
            {
                // Find first unlocked tab
                ResourceInspectorUI UnlockedInstance = null;
                foreach (KeyValuePair<object, ResourceInspectorUI> Item in _ResourceTabs)
                {
                    if (Item.Value.bLocked == false)
                    {
                        UnlockedInstance = Item.Value;
                        _ResourceTabs.Remove(Item.Key);
                        break;
                    }
                }

                string Name;
                if (Object is Resource Resource)
                {
                    Name = PathHelper.GetFileName(Resource.Path);
                }
                else
                {
                    Name = Object.GetType().Name;
                }

                if (UnlockedInstance != null)
                {
                    // Use first unlocked tab
                    UnlockedInstance.GetDockingCard().SetText(Name);
                    _ResourceTabs.Add(Object, UnlockedInstance);
                }
                else
                {
                    // Otherwise create new tab
                    ResourceInspectorUI NewInstance = new ResourceInspectorUI();
                    NewInstance.Initialize(Name);
                    _ResourceTabs.Add(Object, NewInstance);
                }
            }
            Instance = _ResourceTabs[Object];
            return Instance;
        }

        public static void Inspect(object ObjectInspected, Object Tag = null)
        {
            if (ObjectInspected == null)
            {
                return;
            }

            ResourceInspectorUI Tab = GetAvaliableInstance(ObjectInspected);
            Tab.SetObjectInspected(ObjectInspected);
            Tab.SetObjectTag(Tag);
            Tab.InspectObject();

            MainUI.GetInstance().ActivateDockingCard_ResourceInspector(Tab.GetDockingCard());
        }

        public static void ForEach(Action<object, ResourceInspectorUI> Action)
        {
            foreach (KeyValuePair<object, ResourceInspectorUI> Item in _ResourceTabs)
            {
                Action.Invoke(Item.Key, Item.Value);
            }
        }
        public override bool Initialize(string Title)
        {
            base.Initialize(Title);

            ButtonLocked = new Button();
            ButtonLocked.SetImage(bLocked ? LockedTexture : UnlockedTexture);
            ButtonLocked.ClickedEvent += OnButtonLockedClicked;
            ButtonLocked.SetPosition(0, 2, 20, 20);
            _OperationBarUI.AddLeft(ButtonLocked);

            return true;
        }

        private void OnButtonLockedClicked(Button Sender)
        {
            bLocked = !bLocked;
            ButtonLocked.SetImage(bLocked ? LockedTexture : UnlockedTexture);
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bSizeChanged)
            {
                _SearchUI.GetPanelBack().SetWidth(_Container.GetWidth() - 36);
                _ScrollView.SetWidth(_Container.GetWidth());
                UpdateLayout();
            }
        }

        public override void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            base.OnClose(Sender, ref bNotToClose);

            if (_ObjectInspected != null)
            {
                _ResourceTabs.Remove(_ObjectInspected);
            }
        }
    }
}
