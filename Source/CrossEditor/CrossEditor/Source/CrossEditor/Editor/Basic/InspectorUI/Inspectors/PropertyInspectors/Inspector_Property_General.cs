using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_General : Inspector_Property
    {
        protected EditWithProgress _Edit;
        protected Edit _EditValue;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _Edit = new EditWithProgress(Container);
            _Edit.SetStep(1m);
            _Edit.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _Edit.TextChangedEvent += OnEditTextChanged;
            _EditValue = _Edit.GetEditValue();

            if (_ObjectProperty.ReadOnly)
            {
                _Edit.SetReadOnly(true);
                _EditValue.SetReadOnly(true);
            }

            if (_ObjectProperty.DefaultValue == null && !_ValueExtraProperty._bHasMultipleValues)
            {
                _ObjectProperty.DefaultValue = GetPropertyValue();
                _bCanRevert = true;
            }

            ReadValue();
        }

        public override void SetPropertyReadOnly(string propertyName, bool readOnly)
        {
            _Edit.SetReadOnly(readOnly);
            _EditValue.SetReadOnly(readOnly);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = GetValueWidth();
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);
            _Edit.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_Edit);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = "";
            if (PropertyValue != null)
            {
                PropertyValueString = PropertyValue.ToString();
            }
            else
            {
                PropertyValueString = "<null>";
            }
            _Edit.SetText(PropertyValueString);
        }

        double StrToDouble(string str)
        {
            double num = 0;
            double sign = 1;
            int i = 0;
            if (str[0] == '-')
            {
                sign = -1;
                i = 1;
            }
            for (; i < str.Length && str[i] != '.'; i++)
            {
                num = num * 10 + (str[i] - '0');
            }
            if (i < str.Length)
            {
                double frac = 0.1;
                for (i++; i < str.Length; i++)
                {
                    num += (str[i] - '0') * frac;
                    frac *= 0.1;
                }
            }
            return num * sign;
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetText();
            object NewValue = null;
            Type Type = _ObjectProperty.Type;
            if (Type == typeof(string))
            {
                NewValue = ValueString;
            }
            else
            {
                //maybe crash cuz decimal point
                var MinValue = StrToDouble(_Edit.GetMin().ToString());
                var MaxValue = StrToDouble(_Edit.GetMax().ToString());
                double tempValue = StrToDouble(ValueString);
                ValueString = Math.Clamp(tempValue, MinValue, MaxValue).ToString();
                _EditValue.SetText(ValueString);
                NewValue = MathHelper.ParseNumber(ValueString, Type);
                if (NewValue == null)
                {
                    return;
                }
            }
            SetPropertyValue(NewValue);
        }

        void OnEditTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            RecordAndWriteValue();
        }
    }
}
