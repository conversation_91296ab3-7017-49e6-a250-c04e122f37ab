using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class Inspector_Component : Inspector_ComponentBase
    {
        protected Component _Component;


        protected List<Entity> _Entities;

        protected Button _ButtonBakeRP;
        protected Button _ButtonFoliagePartition;
        protected Button _ButtonFoliageBVHGenerate;
        protected Button _ButtonTerrainGenerate;
        protected bool _IsShowBakeRPButton;
        protected bool _IsTerrainGenerateButton;

        void CopyPropetyValue(object src, object dst)
        {
            if (src == null || dst == null || src.GetType() != dst.GetType())
            {
                EditorLogger.Log(LogMessageType.Warning, "wrong coppy");
                return;
            }

            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(src.GetType());
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                PropertyInfo.SetValue(dst, PropertyInfo.GetValue(src, null), null);
            }
        }


        public Inspector_Component(List<Entity> Entities)
        {
            _Entities = Entities;
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Component = (Component)Object;

            // sync component so that, the value would be realtime during inspection
            _Component.SyncDataFromEngine();

            _Component.BeforeInspectObject();
            base.InspectObject(Object, Tag);

            _IsShowBakeRPButton = false;
            _IsTerrainGenerateButton = false;
            string TypeName = GetName();

            if (TypeName == "ReflectionProbeComponent")
            {
                _IsShowBakeRPButton = true;
            }
            else if (TypeName == "TerrainComponent")
            {
                _IsTerrainGenerateButton = false;
            }

            if (_IsShowBakeRPButton)
            {
                _ButtonBakeRP = new Button();
                _ButtonBakeRP.Initialize();
                _ButtonBakeRP.SetText("Bake This One");
                _ButtonBakeRP.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonBakeRP.SetFontSize(12);
                _ButtonBakeRP.SetTextOffsetY(2);
                _ButtonBakeRP.ClickedEvent += OnButtonBakeRPClicked;
                _SelfContainer.AddChild(_ButtonBakeRP);
            }

            if (_IsTerrainGenerateButton)
            {
                _ButtonTerrainGenerate = new Button();
                _ButtonTerrainGenerate.Initialize();
                _ButtonTerrainGenerate.SetText("Terrain BVH Generate");
                _ButtonTerrainGenerate.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonTerrainGenerate.SetFontSize(12);
                _ButtonTerrainGenerate.SetTextOffsetY(2);
                _ButtonTerrainGenerate.ClickedEvent += OnButtonTerrainGenerateClicked;
                _SelfContainer.AddChild(_ButtonTerrainGenerate);
            }

            if (TypeName == "Transform" || TypeName == "WorldSettings")
            {
                _CheckEnable.SetVisible(false);
            }
            RefreshChildInspectors();
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
            ObjectProperty.RawSetPropertyValueFunction = RawSetPropertyValueFunction;
        }

        protected override void RefreshChildInspectors()
        {
            // clear pre inspector children
            Type Type = _Component.GetType();
            ClearChildInspectors();
            // record cur inspector categories
            HashSet<string> Categories = new HashSet<string>();

            bool bIsTransform = (_Component is Transform);
            if (bIsTransform)
                ((Transform)_Component).TryToSyncRotationCacheForUE();

            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                if (IsComponentBaseProperties(PropertyInfo.Name) == false)
                {
                    PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo, _Component);

                    if (PropertyInfoAttribute.Category == "")
                    {
                        if (PropertyInfoAttribute.bAutoExpandStruct)
                        {
                            _AutoExpandedStructPropertyInfo = PropertyInfo;
                            object AutoExpandStruct = PropertyInfo.GetValue(_Component);
                            Type t = AutoExpandStruct.GetType();
                            var _AutoExpandedObjectNewValue = Activator.CreateInstance(t);
                            // construct a new value that is unaffected by access;
                            // will be multiple component access during inspection (To optimize)
                            CopyPropetyValue(AutoExpandStruct, _AutoExpandedObjectNewValue);
                            AddMainStructChildInspectors(_AutoExpandedObjectNewValue);
                        }
                        else
                        {
                            if (bIsTransform)
                            {
                                string PropertyName = PropertyInfo.Name;
                                if (PropertyName != "CoordSystemType")
                                {
                                    bool bContainsUE = PropertyName.Contains("_UE");
                                    if (Transform._CoordSystemType == CoordSystemType.CrossEngine)
                                    {
                                        if (bContainsUE)
                                            continue;
                                    }
                                    else
                                    {
                                        if (!bContainsUE)
                                            continue;
                                    }
                                }
                            }

                            AddPropertyInspector(PropertyInfo, _Component);
                        }
                    }
                    else if (PropertyInfoAttribute.bHide == false)
                    {
                        Categories.Add(PropertyInfoAttribute.Category);
                    }
                }
            }

            List<MethodInfo> methodInfos = new List<MethodInfo>(Type.GetMethods());
            foreach (MethodInfo methodInfo in methodInfos)
            {
                MethodInfoAttribute methodAttribute = MethodInfoAttribute.GetMethodInfoAttribute(methodInfo);
                if (methodAttribute.PropertyType == "Button")
                {
                    AddButtonInspector(methodInfo, _Component, methodAttribute);
                }
            }

            // Grouped Properties
            foreach (string Category in Categories)
            {
                Inspector_GroupedProperty inspector_GroupedProperty = new Inspector_GroupedProperty(_Component, Category);
                inspector_GroupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                inspector_GroupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                AddChildInspector(inspector_GroupedProperty);
                inspector_GroupedProperty.InspectObject(_Component);

            }

            if (_Component is Script)
            {
                _ButtonRefresh.SetVisible(true);
                Script Script = (Script)_Component;
                Script.TryUpdateScript();
                List<Property> ScriptProperties = Script.GetScriptProperties();
                foreach (Property ScriptProperty in ScriptProperties)
                {
                    Type PropertyType = ScriptProperty.Value.GetType();
                    string PropertyTypeString = PropertyType.ToString();
                    bool bIsEnum = PropertyType.IsEnum;
                    ObjectProperty ObjectProperty = new ObjectProperty();
                    ObjectProperty.Object = Script;
                    ObjectProperty.Name = ScriptProperty.Name;
                    ObjectProperty.Type = PropertyType;
                    ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                    ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                    ObjectProperty.RawSetPropertyValueFunction = RawSetPropertyValueFunction;
                    Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, bIsEnum);
                    AddChildInspector(Inspector_Property);
                    Inspector_Property.InspectProperty(ObjectProperty);
                }
            }

            if (_Component is CanvasComponent)
            {
                _ButtonRefresh.SetVisible(true);
            }
        }
        object RawGetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            if (_AutoExpandedStructPropertyInfo != null && IsComponentBaseProperties(PropertyName) == false)
            {
                Component Component = (Component)Object;
                object AutoExpandedStruct = _AutoExpandedStructPropertyInfo.GetValue(Component);
                Type Type = AutoExpandedStruct.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    return PropertyInfo.GetValue(AutoExpandedStruct);
                }
            }
            else
            {
                Component Component = (Component)Object;
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    return PropertyInfo.GetValue(Object);
                }
                if (Component is Script)
                {
                    Script Script = (Script)Component;
                    List<Property> ScriptProperties = Script.GetScriptProperties();
                    int Count = ScriptProperties.Count;
                    for (int i = 0; i < Count; i++)
                    {
                        if (ScriptProperties[i].Name == PropertyName)
                        {
                            return ScriptProperties[i].Value;
                        }
                    }
                }
            }
            return null;
        }

        protected virtual Component FindComponent(Entity Entity, Type ComponentType)
        {
            return Entity.GetComponent(ComponentType);
        }

        public virtual object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            object Value = GetPropertyValue(Object, PropertyName); ;

            if (ValueExtraProperty != null)
            {
                List<object> ValueList = new List<object>();
                Type ComponentType = _Component.GetType();
                foreach (Entity Entity in _Entities)
                {
                    Component Component = FindComponent(Entity, ComponentType);
                    object Value1 = RawGetPropertyValueFunction(Component, PropertyName, null);
                    ValueList.Add(Value1);
                }
                ValueListChecker.CheckValueList(ValueExtraProperty, ValueList);
            }

            return Value;
        }

        public void RawSetPropertyValueFunction1(Type ComponentType, Component Component, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            bool _modified = false;
            if (_AutoExpandedStructPropertyInfo != null && IsComponentBaseProperties(PropertyName) == false)
            {
                object AutoExpandedStruct = _AutoExpandedStructPropertyInfo.GetValue(Component);
                Type Type = AutoExpandedStruct.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(AutoExpandedStruct, PropertyValue);
                    _modified = true;
                }
                _AutoExpandedStructPropertyInfo.SetValue(Component, AutoExpandedStruct);
            }
            else
            {
                if (Component is Script)
                {
                    Script Script = (Script)Component;
                    Script.SetProperty(PropertyName, PropertyValue);
                    _modified = true;
                }
                if (Component.Entity.World.Enable == false)
                {
                    return;
                }
                PropertyInfo PropertyInfo = ComponentType.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(Component, PropertyValue);
                    _modified = true;
                    _bTriggerRefreshMenu = true;
                }
                else
                {
                    if (Component is Script)
                    {
                        Script Script = (Script)Component;
                        List<Property> ScriptProperties = Script.GetScriptProperties();
                        int Count = ScriptProperties.Count;
                        for (int i = 0; i < Count; i++)
                        {
                            if (ScriptProperties[i].Name == PropertyName)
                            {
                                ScriptProperties[i].Value = PropertyValue;
                            }
                        }
                    }
                }
            }

            if (_modified)
            {
                Clicegf.PropertyChangedEvent evt = new Clicegf.PropertyChangedEvent();
                evt.PropertyName = PropertyName;
                OnPostEditChangedProperty?.Invoke(evt);
            }
        }

        public void RawSetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type ComponentType = _Component.GetType();
            foreach (Entity Entity in _Entities)
            {
                Component Component = FindComponent(Entity, ComponentType);
                RawSetPropertyValueFunction1(ComponentType, Component, PropertyName, PropertyValue, SubProperty);
            }
        }

        public virtual void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            _bTriggerRefreshMenu = false;
            bool _bTriggerRefreshMenuByMeta = false;
            Type ComponentType = _Component.GetType();
            foreach (Entity Entity in _Entities)
            {
                Component Component = FindComponent(Entity, ComponentType);

                // old value is for revert option
                object OldValue = OldValue = RawGetPropertyValueFunction(Component, PropertyName, null); ;
                object NewPropertyValue = PropertyValue;
                NewPropertyValue = GetNewPropertyValue(OldValue, PropertyValue, SubProperty);
                if (SubProperty != null && SubProperty._EditOperation != null)
                {
                    ObjectProperty ObjectProperty = SubProperty._ObjectProperty;
                    ObjectProperty ObjectProperty1 = new ObjectProperty();
                    ObjectProperty1.Object = Component;
                    ObjectProperty1.Name = ObjectProperty.Name;
                    ObjectProperty1.Type = ObjectProperty.Type;
                    ObjectProperty1.PropertyInfoAttribute = ObjectProperty.PropertyInfoAttribute;
                    ObjectProperty1.RawSetPropertyValueFunction = RawSetPropertyValueFunction;
                    SubProperty._EditOperation.AddModifyProperty(Component, ObjectProperty1, OldValue, NewPropertyValue);
                }
                RawSetPropertyValueFunction1(ComponentType, Component, PropertyName, NewPropertyValue, null);

                if (!_bTriggerRefreshMenuByMeta)
                {
                    var MemoberInfoArr = ComponentType.GetMember(PropertyName);
                    foreach (var MemInfo in MemoberInfoArr)
                    {
                        PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(MemInfo, _Component);
                        if (PropertyInfoAttribute.bTriggerRefresh)
                        {
                            _bTriggerRefreshMenuByMeta = true;
                            break;
                        }
                    }
                }
            }

            if (_bTriggerRefreshMenuByMeta)
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    OnButtonRefreshMenuClicked(null);
                });
            }

            if (_bTriggerRefreshMenu)
            {
                if (_Component is Script ||
                    _Component is CanvasComponent)
                {
                    OperationQueue.GetInstance().AddOperation(() =>
                    {
                        OnButtonRefreshMenuClicked(null);
                    });
                }
                if (_Component is Camera)
                {
                    ReadValue();
                }
                if (_Component is Transform)
                {
                    OperationQueue.GetInstance().AddOperation(() =>
                    {
                        OperationQueue.GetInstance().AddOperation(() =>
                        {
                            Inspector_Entity Inspector_Entity = GetParentInspector() as Inspector_Entity;
                            if (Inspector_Entity != null)
                            {
                                Inspector_Component Inspector_Component_Model = Inspector_Entity.FindComponentInspector<ModelComponent>();
                                if (Inspector_Component_Model != null)
                                {
                                    Inspector_Component_Model.FindChildInspector("Center").ReadValue();
                                    Inspector_Component_Model.FindChildInspector("Extents").ReadValue();
                                }
                            }
                        });
                    });
                }
            }
            EditorScene.GetInstance().SetDirty();
        }


        public void SetPropertyKeyFrame(string PropertyName, object PropertyValue)
        {
            Type ComponentType = _Component.GetType();
            foreach (Entity Entity in _Entities)
            {
                Component Component = FindComponent(Entity, ComponentType);
                CinematicUI.GetInstance().InspectorAddKey(Entity, Component.GetType().Name, PropertyName, PropertyValue);
            }
        }
        public bool IsExistCompAndProp(string PropertyName)
        {
            Type ComponentType = _Component.GetType();
            bool flag = false;
            foreach (Entity Entity in _Entities)
            {
                Component Component = FindComponent(Entity, ComponentType);
                flag = CinematicUI.GetInstance().IsExistCompAndProp(Entity, Component.GetType().Name, PropertyName);
            }
            return flag;
        }


        public override int GetIndent()
        {
            return 0;
        }

        protected override object GetInspectedObject()
        {
            return _Component;
        }
        protected override void UpdateContentLayout(int Width, ref int Height, ref int Y)
        {
            if (_IsShowBakeRPButton)
            {
                _ButtonBakeRP.SetPosition(0, Height, Width, 24);
                Height += 24;
            }
            if (_IsTerrainGenerateButton)
            {
                _ButtonTerrainGenerate.SetPosition(0, Height, Width, 24);
                Height += 24;
            }
        }

        public Component GetComponent()
        {
            return _Component;
        }

        public override string GetName()
        {
            return _Component.GetType().ToString();
        }

        protected void OnButtonBakeRPClicked(Button Sender)
        {
            CrossEngineApi.BakeOneReflectionProbe(_Component.Entity.World.GetNativePointer(), _Component.Entity.EntityID);
        }

        protected void OnButtonFoliagePartitionClicked(Button Sender)
        {
            FoliageComponent foliageComponent = _Component.Entity.GetFoliageComponent();
            foliageComponent.FoliagePartition();
            //_Component.Entity.RemoveComponent(foliageComponent);
        }

        protected void OnButtonFoliageBVHGenerateCicked(Button Sender)
        {
            FoliageComponent foliageComponent = _Component.Entity.GetFoliageComponent();
            foliageComponent.FoliageBVHGenerate();
        }

        protected void OnButtonTerrainGenerateClicked(Button Sender)
        {
            TerrainComponent terrainComponent = _Component.Entity.GetTerrainComponent();
            terrainComponent.TerrainBVHGenerate();
        }

        protected override void OnButtonRefreshMenuClicked(Button Sender)
        {
            if (_Component is Script)
            {
                Script Script = (Script)_Component;
                Script.RefreshScript();
                Entity Root = EditorScene.GetInstance().GetRoot();
                Root.RefreshScriptProperties(Script.Path);
            }

            if (_Component is SkeltSocket)
            {
                return;
            }

            RefreshChildInspectors();
            GetInspectorHandler().UpdateLayout();
            EditorScene.GetInstance().SetDirty();
        }

        protected override void OnButtonContextMenuClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuItem MenuItem_Reset = new MenuItem();
            MenuItem_Reset.SetText("Reset");
            MenuItem_Reset.ClickedEvent += OnMenuItemResetClicked;

            MenuItem MenuItem_RemoveComponent = new MenuItem();
            MenuItem_RemoveComponent.SetText("Remove Component");
            MenuItem_RemoveComponent.ClickedEvent += OnMenuItemRemoveComponentClicked;

            MenuItem MenuItem_MoveUp = new MenuItem();
            MenuItem_MoveUp.SetText("Move Up");
            MenuItem_MoveUp.ClickedEvent += OnMenuItemMoveUpClicked;

            MenuItem MenuItem_MoveDown = new MenuItem();
            MenuItem_MoveDown.SetText("Move Down");
            MenuItem_MoveDown.ClickedEvent += OnMenuItemMoveDownClicked;

            MenuItem MenuItem_CopyComponent = new MenuItem();
            MenuItem_CopyComponent.SetText("Copy Component");
            MenuItem_CopyComponent.ClickedEvent += OnMenuItemCopyComponentClicked;

            MenuItem MenuItem_PasteComponentAsNew = new MenuItem();
            MenuItem_PasteComponentAsNew.SetText("Paste Component As New");
            MenuItem_PasteComponentAsNew.ClickedEvent += OnMenuItemPasteComponentAsNewClicked;

            MenuItem MenuItem_PasteComponentValues = new MenuItem();
            MenuItem_PasteComponentValues.SetText("Paste Component Values");
            MenuItem_PasteComponentValues.ClickedEvent += OnMenuItemPasteComponentValuesClicked;

            MenuItem MenuItem_EditScript = new MenuItem();
            MenuItem_EditScript.SetText("Edit Script");
            MenuItem_EditScript.ClickedEvent += OnMenuItemEditScriptClicked;

            MenuContextMenu.AddMenuItem(MenuItem_Reset);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_RemoveComponent);
            MenuContextMenu.AddMenuItem(MenuItem_MoveUp);
            MenuContextMenu.AddMenuItem(MenuItem_MoveDown);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_CopyComponent);
            MenuContextMenu.AddMenuItem(MenuItem_PasteComponentAsNew);
            MenuContextMenu.AddMenuItem(MenuItem_PasteComponentValues);
            bool bScriptComponent = false;
            if (bScriptComponent)
            {
                MenuContextMenu.AddSeperator();
                MenuContextMenu.AddMenuItem(MenuItem_EditScript);
            }

            if (_Component is Transform)
            {
                MenuItem_RemoveComponent.SetEnable(false);
            }
            Entity Entity = _Component.Entity;
            if (Entity.CanMoveUp(_Component) == false)
            {
                MenuItem_MoveUp.SetEnable(false);
            }
            if (Entity.CanMoveDown(_Component) == false)
            {
                MenuItem_MoveDown.SetEnable(false);
            }

            MenuItem_PasteComponentAsNew.SetEnable(false);
            MenuItem_PasteComponentValues.SetEnable(false);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        protected void OnMenuItemResetClicked(MenuItem Sender)
        {
            Type Type = _Component.GetType();
            PropertyInfo[] Properties = Type.GetProperties();
            int PropertyCount = Properties.Length;
            List<PropertyInfo> PropertyInfoList = new List<PropertyInfo>(PropertyCount);
            List<object> OldValueList = new List<object>(PropertyCount);
            List<object> NewValueList = new List<object>(PropertyCount);
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                PropertyInfoList.Add(PropertyInfo);
                object OldValue = PropertyInfo.GetValue(_Component);
                OldValueList.Add(OldValue);
            }
            _Component.Reset();
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                object NewValue = PropertyInfo.GetValue(_Component);
                NewValueList.Add(NewValue);
            }

            EditorScene.GetInstance().SetDirty();

            EditOperation_ModifyProperties EditOperation = new EditOperation_ModifyProperties(_Component, PropertyInfoList, OldValueList, NewValueList);
            EditOperationManager.GetInstance().AddOperation(EditOperation);

            InspectorUI.GetInstance().ReadValueAndUpdateLayout();
        }

        protected void OnMenuItemRemoveComponentClicked(MenuItem Sender)
        {
            if (_Component is Physics)
            {
                foreach (Entity Entity in _Entities)
                {
                    if (Entity.HasComponent(typeof(Joint)))
                    {
                        CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Can not remove PhysicsComponent, for there is still a JointComponent in this entity.");
                        return;
                    }
                }
            }

            Type ComponentType = _Component.GetType();
            EditOperation_RemoveComponents EditOperation = new EditOperation_RemoveComponents();
            foreach (Entity Entity in _Entities)
            {
                Component Component = FindComponent(Entity, ComponentType);
                Entity.RemoveComponent(Component);
                EditOperation.AddRemoveComponentItem(Entity, Component);
            }
            EditOperationManager.GetInstance().AddOperation(EditOperation);

            EditorScene.GetInstance().SetDirty();
            InspectorUI.GetInstance().InspectObject();
        }

        protected void OnMenuItemMoveUpClicked(MenuItem Sender)
        {
            Entity Entity = _Component.Entity;
            if (Entity.CanMoveUp(_Component))
            {
                Entity.MoveUp(_Component);
                InspectorUI.GetInstance().InspectObject();
            }
        }

        protected void OnMenuItemMoveDownClicked(MenuItem Sender)
        {
            Entity Entity = _Component.Entity;
            if (Entity.CanMoveDown(_Component))
            {
                Entity.MoveDown(_Component);
                InspectorUI.GetInstance().InspectObject();
            }
        }

        protected void OnMenuItemCopyComponentClicked(MenuItem Sender)
        {

        }

        protected void OnMenuItemPasteComponentAsNewClicked(MenuItem Sender)
        {
            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Not Implemented.");
        }

        protected void OnMenuItemPasteComponentValuesClicked(MenuItem Sender)
        {
            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Not Implemented.");
        }

        protected void OnMenuItemEditScriptClicked(MenuItem Sender)
        {

        }
    }
}
