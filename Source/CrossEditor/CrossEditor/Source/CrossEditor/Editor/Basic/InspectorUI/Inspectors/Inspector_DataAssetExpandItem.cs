namespace CrossEditor
{
    class Inspector_DataAssetExpandItem : Inspector_Property
    {
        public Inspector_DataAssetExpandItem()
        {
        }

        public void Initialize()
        {
            InitializeCheckExpand();
            GetNameContainer().AddChild(_CheckExpand);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            _CheckExpand.SetPosition(SPAN_X + GetIndent() - 20, 0, 20, 20);
        }
    }
}