using EditorUI;

namespace CrossEditor
{
    internal class Inspector_Texture2DArray : Inspector_Struct_With_Property
    {
        protected Texture2DArray _Texture;
        protected Button _Button;

        public override void InspectObject(object Object, object Tag = null)
        {
            _Texture = (Texture2DArray)Object;

            base.InspectObject(_Texture);

            _Button = new Button();
            _Button.Initialize();
            _Button.SetText("Apply");
            _Button.SetBorderColor(Color.FromRGBA(81, 82, 84, 255));
            _Button.SetFontSize(18);
            _Button.SetTextOffsetY(2);
            _Button.ClickedEvent += (x) =>
            {
                _Texture.Save();
                _Texture.Reload();
            };
            _ChildContainer.AddChild(_Button);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
            int Y1 = _ChildContainer.GetHeight();
            _Button.SetPosition(SPAN_X, Y1 + 5, Width - 2 * SPAN_X, 18);
            Y1 += 18 + 5;
            _ChildContainer.SetHeight(Y1);
            Y += 18 + 5;
        }
    }
}
