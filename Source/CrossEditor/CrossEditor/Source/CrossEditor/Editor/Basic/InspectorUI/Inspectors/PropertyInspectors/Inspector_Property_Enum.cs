using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class Inspector_Property_Enum : Inspector_Property
    {
        const string NumberOnlyPrefix = "__";

        protected ComboBoxEx _ComboBoxValue;
        protected bool _bNumberOnly = false;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _ComboBoxValue = new ComboBoxEx();
            _ComboBoxValue.Initialize();
            _ComboBoxValue.GetValueEdit().SetFontSize(PROPERTY_FONT_SIZE);
            _ComboBoxValue.GetValueEdit().SetTextAlign(TextAlign.CenterLeft);
            _ComboBoxValue.ItemSelectedEvent += (Sender) =>
            {
                GetInspectorHandler().UpdateLayout();
            };

            Type Type = _ObjectProperty.Type;
            string[] EnumNames = Type.GetEnumNames();
            foreach (string EnumName in EnumNames)
            {
                string EnumName1 = ConvertNumberOnlyEnumName(EnumName);
                if (Type == typeof(TextureFormat) || Type == typeof(ColorSpace))
                {
                    List<string> ListString = TextureEditorUI.GetInstance().GetTextureUesdEnum();
                    if (ListString.Contains(EnumName))
                    {
                        _ComboBoxValue.AddItem(EnumName1);
                    }
                    else
                    {
                        _ComboBoxValue.AddItem(EnumName1, false);
                    }
                }
                else
                {
                    _ComboBoxValue.AddItem(EnumName1);
                }
            }
            _ComboBoxValue.ItemSelectedEvent += OnComboBoxValueItemSelected;
            Container.AddChild(_ComboBoxValue);

            _ComboBoxValue.SetEnable(!_ObjectProperty.ReadOnly);

            if (_ObjectProperty.DefaultValue == null && !_ValueExtraProperty._bHasMultipleValues)
            {
                _ObjectProperty.DefaultValue = GetPropertyValue();
                _bCanRevert = true;
            }

            ReadValue();
        }

        string ConvertNumberOnlyEnumName(string EnumName)
        {
            if (EnumName.StartsWith(NumberOnlyPrefix))
            {
                _bNumberOnly = true;
                return EnumName.Substring(NumberOnlyPrefix.Length);
            }
            return EnumName;
        }

        public int GetComboBoxWidth()
        {
            int EditWidth;
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(MULTIPLE_VALUES_STRING);
            }
            else
            {
                int Index = _ComboBoxValue.GetSelectedItemIndex();
                if (Index != -1)
                {
                    EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(_ComboBoxValue.GetItemText(Index));
                }
                else
                {
                    EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast("    ");
                }
            }
            return EditWidth + 30;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int ComboWidth = Math.Max(GetComboBoxWidth(), DEFAULT_WIDTH);
            ComboWidth = Math.Min(ComboWidth, GetValueWidth() - 2 * SPAN_X);

            _ComboBoxValue.SetPosition(0, 2, ComboWidth, 16);
            GetValueContainer().FloatToLeft(_ComboBoxValue);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = PropertyValue.ToString();
            string EnumName1 = ConvertNumberOnlyEnumName(PropertyValueString);
            _ComboBoxValue.SetSelectedItemByText(EnumName1);
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                _ComboBoxValue.GetValueEdit().SetText(MULTIPLE_VALUES_STRING);
            }
        }

        public override void WriteValue()
        {
            base.WriteValue();
            Type Type = _ObjectProperty.Type;
            string ValueString = _ComboBoxValue.GetSelectedItemText();
            if (_bNumberOnly)
            {
                ValueString = NumberOnlyPrefix + ValueString;
            }
            object NewValue = Enum.Parse(Type, ValueString);
            SetPropertyValue(NewValue);
        }

        void OnComboBoxValueItemSelected(ComboBoxEx Sender)
        {
            RecordAndWriteValue();
        }

        public override void RevertPropertyValue()
        {
            base.RevertPropertyValue();
            GetInspectorHandler().UpdateLayout();
        }
    }
}
