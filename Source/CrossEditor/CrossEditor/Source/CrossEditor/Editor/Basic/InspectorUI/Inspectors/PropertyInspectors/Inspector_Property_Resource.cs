using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    class Inspector_Property_Resource : Inspector_Property
    {
        protected Panel _PanelResource;
        protected Button _ButtonToolTips1;
        protected Edit _EditValue;
        protected Button _ButtonToolTips2;
        protected Button _ButtonFilter;

        protected Button _ButtonAssignWithSelectedFile;
        protected Button _ButtonShowInProjectView;
        protected Button _ButtonOperations;

        protected string _FileTypeDescriptor;
        protected ClassIDType _ObjectClassID1;
        protected ClassIDType _ObjectClassID2;
        protected ClassIDType _ObjectClassID3;

        protected string _FileType;
        protected List<string> _FileExtensions;

        public Inspector_Property_Resource()
        {
            _FileTypeDescriptor = "All Files#*";
            ParseFileTypeDescriptor();
            _ObjectClassID1 = ClassIDType.CLASS_NullType;
            _ObjectClassID2 = ClassIDType.CLASS_NullType;
            _ObjectClassID3 = ClassIDType.CLASS_NullType;
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _ButtonToolTips1 = new Button();
            _ButtonToolTips1.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips1.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips1.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips1.SetToolTips("Tooltips");
            _ButtonToolTips1.LeftMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                Inspector_Property_SelectionList.ChildControlHandleLeftMouseDown(Sender, MouseX, MouseY, ref bContinue);
            };
            Container.AddChild(_ButtonToolTips1);

            _PanelResource = new Panel();
            _PanelResource.SetBackgroundColor(new Color(0.3f, 0.3f, 0.3f, 1.0f));
            _PanelResource.LeftMouseDoubleClickedEvent += OnPanelResourceLeftMouseDoubleClicked;
            ThumbnailHelper.GetInstance().EnableThumbnail(_PanelResource);
            Container.AddChild(_PanelResource);

            _ButtonToolTips2 = new Button();
            _ButtonToolTips2.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips2.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips2.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips2.SetToolTips("Tooltips");
            Container.AddChild(_ButtonToolTips2);

            _EditValue = new Edit();
            _EditValue.SetFontSize(PROPERTY_FONT_SIZE);
            _EditValue.Initialize(EditMode.Simple_SingleLine);
            _EditValue.SetReadOnly(true);
            _EditValue.LoadSource("");
            _EditValue.CharInputedEvent += OnEditValueCharInpupted;
            Container.AddChild(_EditValue);
            EditContextUI.GetInstance().RegisterEdit(_EditValue);
            _EditValue.SetSize(200, PROPERTY_FONT_SIZE);

            _ButtonFilter = new Button();
            _ButtonFilter.Initialize();
            _ButtonFilter.SetFontSize(12);
            _ButtonFilter.SetTextOffsetY(1);
            _ButtonFilter.SetText("v");
            _ButtonFilter.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonFilter.SetToolTips("Filter");
            _ButtonFilter.ClickedEvent += OnButtonFilterClicked;
            Container.AddChild(_ButtonFilter);

            _ButtonAssignWithSelectedFile = new Button();
            _ButtonAssignWithSelectedFile.Initialize();
            _ButtonAssignWithSelectedFile.SetImage(UIManager.LoadUIImage("Editor/Icons/Common/UseContent.png"));
            _ButtonAssignWithSelectedFile.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonAssignWithSelectedFile.SetToolTips("Assign With Selected File");
            _ButtonAssignWithSelectedFile.ClickedEvent += OnButtonAssignWithSelectedFileClicked;
            Container.AddChild(_ButtonAssignWithSelectedFile);

            _ButtonShowInProjectView = new Button();
            _ButtonShowInProjectView.Initialize();
            _ButtonShowInProjectView.SetImage(UIManager.LoadUIImage("Editor/Icons/Common/BrowseInContent.png"));
            _ButtonShowInProjectView.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonShowInProjectView.SetToolTips("Show In Resource View");
            _ButtonShowInProjectView.ClickedEvent += OnButtonShowInProjectViewClicked;
            Container.AddChild(_ButtonShowInProjectView);

            _ButtonOperations = new Button();
            _ButtonOperations.Initialize();
            _ButtonOperations.SetFontSize(12);
            _ButtonOperations.SetText("...");
            _ButtonOperations.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonOperations.SetToolTips("Operations");
            _ButtonOperations.ClickedEvent += OnButtonOperationsClicked;
            Container.AddChild(_ButtonOperations);

            SetFilePropertyInfo(_PropertyInfoAttribute.FileTypeDescriptor, _PropertyInfoAttribute.ObjectClassID1, _PropertyInfoAttribute.ObjectClassID2, _PropertyInfoAttribute.ObjectClassID3);
            SetReadOnly(_ObjectProperty.ReadOnly);

            ReadValue();
        }

        public int GetEditValueWidth()
        {
            int EditWidth;
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(MULTIPLE_VALUES_STRING);
            }
            else
            {
                string ResourceName = PathHelper.GetNameOfPath(_EditValue.GetTagString1());
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(ResourceName);
            }
            return EditWidth + 10;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int Height = 72;
            Y += Height - DEFAULT_HEIGHT;

            _Separator.SetY(Y - 1);

            _SelfContainer.SetHeight(Height);
            _Splitter.UpdateLayout(_Splitter.GetWidth(), Height);
            if (_ButtonRevert != null)
            {
                _ButtonRevert.SetY((Height - DEFAULT_HEIGHT) / 2);
            }

            _LabelName.SetY((Height - DEFAULT_HEIGHT) / 2);
            if (_ButtonToolTips != null)
            {
                _ButtonToolTips.SetY((Height - DEFAULT_HEIGHT) / 2);
            }

            int EditValueWidth = Math.Max(GetEditValueWidth(), DEFAULT_WIDTH);
            EditValueWidth = Math.Min(EditValueWidth, GetValueWidth() - 4 * SPAN_X - 64 - BUTTON_WIDTH);
            int EditY = (Height - PROPERTY_FONT_SIZE - SPAN_Y - 16) / 2;
            int ButtonY = EditY + PROPERTY_FONT_SIZE + SPAN_Y;
            _PanelResource.SetPosition(0, SPAN_Y, 64, 64);
            GetValueContainer().FloatToLeft(_PanelResource);
            _ButtonToolTips1.SetPosition(0, SPAN_Y, 64, 64);
            GetValueContainer().PlaceAsLeft(_ButtonToolTips1);
            _EditValue.SetPosition(0, EditY, EditValueWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditValue);
            _ButtonToolTips2.SetPosition(0, EditY, EditValueWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().PlaceAsLeft(_ButtonToolTips2);
            _ButtonAssignWithSelectedFile.SetPosition(0, ButtonY, 16, 16);
            GetValueContainer().PlaceAsLeft(_ButtonAssignWithSelectedFile);
            _ButtonShowInProjectView.SetPosition(0, ButtonY, 16, 16);
            GetValueContainer().PlaceAsLeft(_ButtonShowInProjectView, 16 + SPAN_X);
            _ButtonOperations.SetPosition(0, ButtonY, BUTTON_WIDTH, 16);
            GetValueContainer().PlaceAsLeft(_ButtonOperations, (16 + SPAN_X) * 2);
            _ButtonFilter.SetPosition(0, EditY, BUTTON_WIDTH, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_ButtonFilter);

            if (_ObjectProperty.ReadOnly)
            {
                _ButtonAssignWithSelectedFile.SetEnable(false);
            }

            if (_EnableCheck != null)
            {
                _EnableCheck.SetPos(_EnableCheck.GetX(), _EnableCheck.GetY() + Height / 2 - 13);
            }
        }

        public void SetResourcePathToUI(string ResourcePath)
        {
            string ResourceName = PathHelper.GetNameOfPath(ResourcePath);
            _EditValue.SetText_End(ResourceName);
            _EditValue.SetTagString1(ResourcePath);
            _ButtonToolTips1.SetToolTips(ResourcePath);
            _ButtonToolTips2.SetToolTips(ResourcePath);
            _PanelResource.SetImage(null);
            _PanelResource.SetTagString1(ResourcePath);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = PropertyValue.ToString();
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                PropertyValueString = MULTIPLE_VALUES_STRING;
            }
            else
            {
                PropertyValueString = ResourceManager.Instance().ConvertGuidToPath(PropertyValueString);
            }
            SetResourcePathToUI(PropertyValueString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetTagString1();
            ValueString = ResourceManager.Instance().ConvertPathToGuid(ValueString);
            SetPropertyValue(ValueString);
            GetInspectorHandler().UpdateLayout();
        }

        public new object GetPropertyValue()
        {
            object Value = base.GetPropertyValue();
            if (Value == null)
            {
                return "";
            }

            string ValueString = Value.ToString();
            if (ResourceManager.Instance().HasGuid(ValueString))
            {
                return ResourceManager.Instance().ConvertGuidToPath(ValueString);
            }
            else
            {
                return ValueString;
            }
        }

        public bool IsLegalExtension(string Path)
        {
            string Extension = PathHelper.GetExtensionOfPath(Path);
            foreach (string Extension1 in _FileExtensions)
            {
                if (Extension1 == "*")
                {
                    return true;
                }
                if (StringHelper.IgnoreCaseEqual(Extension, Extension1))
                {
                    return true;
                }
            }
            return false;
        }

        public bool ExtensionIsAsset()
        {
            if (_FileExtensions.Count == 1)
            {
                string Extension = _FileExtensions[0];
                if (StringHelper.IgnoreCaseEqual(Extension, "nda"))
                {
                    return true;
                }
            }
            return false;
        }

        public bool CheckPath(string Path)
        {
            if (IsLegalExtension(Path) == false)
            {
                string Message = string.Format("File extension mismatch: {0}.", Path);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Message);
                return false;
            }
            if (ExtensionIsAsset())
            {
                ClassIDType ObjectClassID2 = Resource.GetResourceTypeStatic(Path);
                if (EditorUtilities.IsSpecificAsset(ObjectClassID2, _ObjectClassID1) == false &&
                    EditorUtilities.IsSpecificAsset(ObjectClassID2, _ObjectClassID2) == false &&
                    EditorUtilities.IsSpecificAsset(ObjectClassID2, _ObjectClassID3) == false)
                {
                    string Message = string.Format("Resource type mismatch: {0}.", Path);
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Message);
                    return false;
                }
            }
            return true;
        }

        public override bool OnDropPathes(int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (_LabelName.IsPointIn(MouseX, MouseY) ||
                _PanelResource.IsPointIn(MouseX, MouseY) ||
                _EditValue.IsPointIn(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (PathesDragged.Count > 0)
                {
                    string PathDragged = PathesDragged[0];
                    string PathDragged1 = EditorUtilities.EditorFilenameToStandardFilename(PathDragged);
                    if (CheckPath(PathDragged1))
                    {
                        SetResourcePathToUI(PathDragged1);
                        RecordAndWriteValue();
                    }
                }
                return true;
            }
            return false;
        }

        void SetFilePropertyInfo(string FileTypeDescriptor, ClassIDType ObjectClassID1, ClassIDType ObjectClassID2, ClassIDType ObjectClassID3)
        {
            _FileTypeDescriptor = FileTypeDescriptor;
            ParseFileTypeDescriptor();
            _ObjectClassID1 = ObjectClassID1;
            _ObjectClassID2 = ObjectClassID2;
            _ObjectClassID3 = ObjectClassID3;
        }

        void ParseFileTypeDescriptor()
        {
            string[] Strings1 = _FileTypeDescriptor.Split("#");
            DebugHelper.Assert(Strings1.Length == 2);
            _FileType = Strings1[0];
            string[] Strings2 = Strings1[1].Split("|");
            DebugHelper.Assert(Strings2.Length >= 1);
            _FileExtensions = new List<string>();
            _FileExtensions.AddRange(Strings2);
        }

        void OnPanelResourceLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            DoInspectInResourceProperties();
            bContinue = false;
        }

        void OnEditValueCharInpupted(Control Sender, char Char, ref bool bContinue)
        {
            RecordAndWriteValue();
        }

        void OnButtonFilterClicked(Button Sender)
        {
            ResourceFilterUI ResourceFilterUI = ResourceFilterUI.GetInstance();
            ResourceFilterUI.ShowUI(GetUIManager(), _ObjectClassID1, _ObjectClassID2, _ObjectClassID3, _ButtonFilter, (string Resource) =>
            {
                SetResourcePathToUI(Resource);
                RecordAndWriteValue();
            });
        }

        void OnButtonAssignWithSelectedFileClicked(Button Sender)
        {
            DoAssignWithSelectedFile();
        }

        void OnButtonShowInProjectViewClicked(Button Sender)
        {
            string ValueString = _EditValue.GetTagString1();
            if (!EditorUtilities.IsStandardFileExist(ValueString))
            {
                ReadValue();
            }
            DoShowInProjectView();
        }

        void SetReadOnly(bool bReadOnly)
        {
            _ButtonAssignWithSelectedFile.SetEnable(!bReadOnly);
            _ButtonFilter.SetEnable(!bReadOnly);
        }

        public void OnButtonOperationsClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuItem MenuItem_InspectInResourceProperties = new MenuItem();
            MenuItem_InspectInResourceProperties.SetText("Inspect In Resource Properties");
            MenuItem_InspectInResourceProperties.ClickedEvent += OnMenuItemInspectInResourcePropertiesClicked;

            MenuItem MenuItem_AssignWithSelectedFile = new MenuItem();
            MenuItem_AssignWithSelectedFile.SetText("Assign With Selected File");
            MenuItem_AssignWithSelectedFile.ClickedEvent += OnMenuItemAssignWithSelectedFileClicked;

            MenuItem MenuItem_ExploreFile = new MenuItem();
            MenuItem_ExploreFile.SetText("Explore File");
            MenuItem_ExploreFile.ClickedEvent += OnMenuItemExploreFileClicked;

            MenuItem MenuItem_ResetToDefault = new MenuItem();
            MenuItem_ResetToDefault.SetText("Reset To Default");
            MenuItem_ResetToDefault.ClickedEvent += OnMenuItemResetToDefaultClicked;

            MenuItem MenuItem_ShowInProjectView = new MenuItem();
            MenuItem_ShowInProjectView.SetText("Show In Resource View");
            MenuItem_ShowInProjectView.ClickedEvent += OnMenuItemShowInProjectViewClicked;

            MenuItem MenuItem_ShowInExplorer = new MenuItem();
            MenuItem_ShowInExplorer.SetText("Show In Explorer");
            MenuItem_ShowInExplorer.ClickedEvent += OnMenuItemShowInExplorerClicked;

            MenuItem MenuItem_MakeRelativeToMaterial = new MenuItem();
            MenuItem_MakeRelativeToMaterial.SetText("Make Relative To Material");
            MenuItem_MakeRelativeToMaterial.ClickedEvent += OnMenuItemMakeRelativeToMaterialClicked;

            MenuContextMenu.AddMenuItem(MenuItem_InspectInResourceProperties);
            MenuContextMenu.AddMenuItem(MenuItem_AssignWithSelectedFile);
            MenuContextMenu.AddMenuItem(MenuItem_ExploreFile);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_ResetToDefault);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_ShowInProjectView);
            MenuContextMenu.AddMenuItem(MenuItem_ShowInExplorer);

            Material material = _Object as Material;
            if (material != null)
            {
                MenuContextMenu.AddSeperator();
                MenuContextMenu.AddMenuItem(MenuItem_MakeRelativeToMaterial);
            }

            if (_PropertyInfoAttribute.DefaultValue == null)
            {
                MenuItem_ResetToDefault.SetEnable(false);
            }

            if (_ObjectProperty.ReadOnly)
            {
                MenuItem_AssignWithSelectedFile.SetEnable(false);
                MenuItem_ResetToDefault.SetEnable(false);
                MenuItem_ExploreFile.SetEnable(false);
            }

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        void OnMenuItemInspectInResourcePropertiesClicked(MenuItem MenuItem)
        {
            DoInspectInResourceProperties();
        }

        void OnMenuItemAssignWithSelectedFileClicked(MenuItem MenuItem)
        {
            DoAssignWithSelectedFile();
        }

        void OnMenuItemExploreFileClicked(MenuItem MenuItem)
        {
            DoExploreFile();
        }

        void OnMenuItemShowInProjectViewClicked(MenuItem MenuItem)
        {
            DoShowInProjectView();
        }

        void OnMenuItemShowInExplorerClicked(MenuItem MenuItem)
        {
            DoShowInExplorer();
        }

        void OnMenuItemResetToDefaultClicked(MenuItem MenuItem)
        {
            DoResetToDefault();
        }

        void OnMenuItemMakeRelativeToMaterialClicked(MenuItem MenuItem)
        {
            DoMakeRelativeToMaterial();
        }

        void DoAssignWithSelectedFile()
        {
            string Path = ProjectUI.GetInstance().GetSelectedListViewPath();
            if (Path != "" && FileHelper.IsFileExists(Path))
            {
                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                if (CheckPath(Path1))
                {
                    SetResourcePathToUI(Path1);
                    RecordAndWriteValue();
                }
            }
        }

        void DoExploreFile()
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = _FileType;
            PathInputUIFilterItem.Extensions = _FileExtensions;
            string s = PathInputUIFilterItem.ToString();

            bool bContentsOnly = true;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.Initialize(GetUIManager(), "", PathInputUIType.OpenFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                string EditorFilename = PathInputed;
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(EditorFilename);
                if (CheckPath(StandardFilename))
                {
                    SetResourcePathToUI(StandardFilename);
                    RecordAndWriteValue();
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        string ConvertFilePath(string FilePath)
        {
            if (StringHelper.GetChar(FilePath, 0) == '.')
            {
                Material material = (Material)_Object;
                if (material != null)
                {
                    string MaterialPath = material.Path;
                    string MaterialPath1 = EditorUtilities.StandardFilenameToEditorFilename(MaterialPath);
                    string MaterialDirectory = PathHelper.GetDirectoryName(MaterialPath1);
                    FilePath = Path.Combine(MaterialDirectory, FilePath);
                    FilePath = Path.GetFullPath(FilePath);
                    FilePath = PathHelper.ToStandardForm(FilePath);
                    FilePath = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                }
            }
            return FilePath;
        }

        void DoInspectInResourceProperties()
        {
            string FilePath = (string)GetPropertyValue();
            if (FilePath != null)
            {
                FilePath = ConvertFilePath(FilePath);
                string FilePath1 = EditorUtilities.StandardFilenameToEditorFilename(FilePath);
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                ProjectUI.InspectResourceItem(FilePath1);
            }
        }

        void DoShowInProjectView()
        {
            string FilePath = (string)GetPropertyValue();
            if (FilePath != null)
            {
                FilePath = ConvertFilePath(FilePath);
                string FilePath1 = EditorUtilities.StandardFilenameToEditorFilename(FilePath);
                MainUI.GetInstance().ActivateDockingCard_Project();
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                ProjectUI.JumpToPath(FilePath1);
            }
        }

        void DoShowInExplorer()
        {
            string FilePath = (string)GetPropertyValue();
            if (FilePath != null)
            {
                FilePath = ConvertFilePath(FilePath);
                string FilePath1 = EditorUtilities.StandardFilenameToEditorFilename(FilePath);
                ProcessHelper.OpenContainingFolder(FilePath1);
            }
        }

        void DoResetToDefault()
        {
            string DefaultValue = (string)_PropertyInfoAttribute.DefaultValue;
            SetResourcePathToUI(DefaultValue);
            RecordAndWriteValue();
        }

        void DoMakeRelativeToMaterial()
        {
            string FilePath = (string)GetPropertyValue();
            if (FilePath != null)
            {
                if (StringHelper.GetChar(FilePath, 0) == '.')
                {
                    return;
                }

                Material material = (Material)_Object;
                if (material != null)
                {
                    string MaterialPath = material.Path;
                    string MaterialPath1 = EditorUtilities.StandardFilenameToEditorFilename(MaterialPath);
                    string MaterialDirectory = PathHelper.GetDirectoryName(MaterialPath1);

                    string FilePath1 = EditorUtilities.StandardFilenameToEditorFilename(FilePath);

                    string RelativePath = Path.GetRelativePath(MaterialDirectory, FilePath1);
                    string RelativePath1 = PathHelper.ToStandardForm(RelativePath);

                    if (PathHelper.IsAbsolutePath(RelativePath1) == false &&
                        StringHelper.GetChar(RelativePath1, 0) != '.')
                    {
                        RelativePath1 = "./" + RelativePath1;
                    }

                    SetResourcePathToUI(RelativePath1);
                    RecordAndWriteValue();
                }
            }
        }
    }
}
