using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_UInt3 : Inspector_Property
    {
        Panel _ColorX;
        Panel _ColorY;
        Panel _ColorZ;

        EditWithProgress _EditX;
        EditWithProgress _EditY;
        EditWithProgress _EditZ;
        Edit _EditValueX;
        Edit _EditValueY;
        Edit _EditValueZ;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _EditX = new EditWithProgress(Container);
            _EditX.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditX.TextChangedEvent += OnEditValueTextChanged;
            _EditValueX = _EditX.GetEditValue();

            _EditY = new EditWithProgress(Container);
            _EditY.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditY.TextChangedEvent += OnEditValueTextChanged;
            _EditValueY = _EditY.GetEditValue();

            _EditZ = new EditWithProgress(Container);
            _EditZ.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditZ.TextChangedEvent += OnEditValueTextChanged;
            _EditValueZ = _EditZ.GetEditValue();

            _ColorX = CreateColorPanel(Container, Color_Red);
            _ColorY = CreateColorPanel(Container, Color_Green);
            _ColorZ = CreateColorPanel(Container, Color_Blue);

            if (_ObjectProperty.ReadOnly)
            {
                _EditX.SetReadOnly(true);
                _EditY.SetReadOnly(true);
                _EditZ.SetReadOnly(true);
            }

            ReadValue();
        }

        public Panel CreateColorPanel(Control Container, Color Color)
        {
            Panel ColorPanel = new Panel();
            ColorPanel.Initialize();
            ColorPanel.SetBackgroundColor(Color);
            Container.AddChild(ColorPanel);
            return ColorPanel;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = (GetValueWidth() - SPAN_X * 4) / 3;
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);
            _EditX.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditX);
            _ColorX.SetPosition(0, SPAN_Y + 1, 3, PROPERTY_FONT_SIZE - 2);
            GetValueContainer().PlaceAsLeft(_ColorX, 2);
            _EditY.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditY);
            _ColorY.SetPosition(0, SPAN_Y + 1, 3, PROPERTY_FONT_SIZE - 2);
            GetValueContainer().PlaceAsLeft(_ColorY, 2);
            _EditZ.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditZ);
            _ColorZ.SetPosition(0, SPAN_Y + 1, 3, PROPERTY_FONT_SIZE - 2);
            GetValueContainer().PlaceAsLeft(_ColorZ, 2);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            Vector3ui Value = (Vector3ui)PropertyValue;
            string XString = MathHelper.NumberToString(Value.X);
            string YString = MathHelper.NumberToString(Value.Y);
            string ZString = MathHelper.NumberToString(Value.Z);
            _EditX.SetText(XString);
            _EditY.SetText(YString);
            _EditZ.SetText(ZString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueXString = _EditValueX.GetText();
            string ValueYString = _EditValueY.GetText();
            string ValueZString = _EditValueZ.GetText();
            Vector3ui NewValue = new Vector3ui();
            NewValue.X = MathHelper.ParseUInt(ValueXString);
            NewValue.Y = MathHelper.ParseUInt(ValueYString);
            NewValue.Z = MathHelper.ParseUInt(ValueZString);
            SetPropertyValue(NewValue);
        }

        void OnEditValueTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            if (Sender == _EditValueX)
            {
                SetSubProperty("X");
            }
            else if (Sender == _EditValueY)
            {
                SetSubProperty("Y");
            }
            else if (Sender == _EditValueZ)
            {
                SetSubProperty("Z");
            }

            RecordAndWriteValue();

            ClearSubProperty();
        }
    }
}
