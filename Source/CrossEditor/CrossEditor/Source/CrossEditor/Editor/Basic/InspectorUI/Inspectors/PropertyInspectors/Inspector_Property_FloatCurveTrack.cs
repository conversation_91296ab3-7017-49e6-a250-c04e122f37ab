using CEngine;
using EditorUI;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_Property_FloatCurveTrack : Inspector_Property
    {
        protected object _Value;

        private Button _ButtonCurve;
        private Panel _PanelCurve;

        public Inspector_Property_FloatCurveTrack()
        {
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();
            _ButtonCurve = new Button();
            _ButtonCurve.Initialize();
            _ButtonCurve.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonCurve.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonCurve.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonCurve.SetToolTips("Edit Curve");
            _ButtonCurve.ClickedEvent += OnCurveEditOpen;
            Container.AddChild(_ButtonCurve);
            _PanelCurve = UIHelper.GetInstance().CreatePanel(Container);
            _PanelCurve.SetBackgroundColor(Color.FromRGBA(255, 255, 255, 88));
            _PanelCurve.SetBorderColor(Color.FromRGB(255, 255, 255));

            _Value = ObjectProperty.GetPropertyValueFunction(ObjectProperty.Object, ObjectProperty.Name, null);

            InitializeCheckExpand();
            GetNameContainer().AddChild(_CheckExpand);

            AddChildInspectors();
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
            }
            WriteValue();
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        protected virtual void AddChildInspectors()
        {
            ClearChildInspectors();
            Type Type = _Value.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            _PanelCurve.SetVisible(true);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int CurveWidth = GetValueWidth() - 3 * SPAN_X - BUTTON_WIDTH;
            _ButtonCurve.SetPosition(0, 2, CurveWidth, 16);
            GetValueContainer().FloatToLeft(_ButtonCurve);
            _PanelCurve.SetPosition(0, 2, CurveWidth, 16);
            GetValueContainer().PlaceAsLeft(_PanelCurve);

            _CheckExpand.SetPosition(SPAN_X + GetIndent() - 20, 0, 20, 20);
            _LabelName.SetX(_CheckExpand.GetEndX());
            if (_ButtonToolTips != null)
                _ButtonToolTips.SetX(_LabelName.GetX());
        }

        public override void ReadValue()
        {
            base.ReadValue();
        }

        public override void WriteValue()
        {
            SetPropertyValue(_Value);
        }

        public virtual FloatCurveTrack GetFloatCurveTrack()
        {
            return _Value as FloatCurveTrack;
        }

        public virtual void SetFloatCurveTrack(FloatCurveTrack inTrack)
        {
            _Value = inTrack;
        }
        private void OnCurveEditOpen(Button Sender)
        {
            if (!_PanelCurve.GetVisible())
            {
                return;
            }

            List<CurveManager> Curves = new List<CurveManager>();

            CurveManager curve = new CurveManager();
            curve.EnterRepeatType = CurveRepeatType.CRT_LINEAR;
            curve.LeaveRepeatType = CurveRepeatType.CRT_CONSTANT;
            curve.RuntimeCurve = GetFloatCurveTrack();
            curve.PostModifiedCurveEvent += () =>
            {
                SetFloatCurveTrack(curve.RuntimeCurve);
                WriteValue();
            };
            curve.Name = "Curve";
            Curves.Add(curve);

            CurveEditorUI.GetInstance().LoadFromCurves(Curves);
            MainUI.GetInstance().ActivateDockingCard_CurveEditor();
        }
    }


    class Inspector_Property_CliFloatCurveTrack : Inspector_Property_FloatCurveTrack
    {
        public Inspector_Property_CliFloatCurveTrack()
        {
        }

        public static T Convert<T>(object source) where T : new()
        {
            T result = new T();
            Type sourceType = source.GetType();
            Type targetType = typeof(T);

            foreach (PropertyInfo sourceProperty in sourceType.GetProperties())
            {
                PropertyInfo targetProperty = targetType.GetProperty(sourceProperty.Name);
                if (targetProperty != null && targetProperty.CanWrite)
                {
                    object sourceValue = sourceProperty.GetValue(source, null);

                    // check if is enum
                    if (sourceProperty.PropertyType.IsEnum && targetProperty.PropertyType.IsEnum)
                    {
                        // enum
                        int enumValue = (int)sourceValue;
                        object targetEnumValue = Enum.ToObject(targetProperty.PropertyType, enumValue);
                        targetProperty.SetValue(result, targetEnumValue, null);
                    }
                    else if (targetProperty.PropertyType.IsAssignableFrom(sourceProperty.PropertyType))
                    {
                        // basic type
                        targetProperty.SetValue(result, sourceValue, null);
                    }
                }
            }

            return result;
        }

        public override FloatCurveTrack GetFloatCurveTrack()
        {
            var track = new FloatCurveTrack();
            var cli_track = _Value as Clicross.FloatCurveTrack;
            if (cli_track != null)
            {
                track = Convert<FloatCurveTrack>(cli_track);
                foreach (var cli_key in cli_track.Keys)
                {
                    track.Keys.Add(Convert<FloatCurveKey>(cli_key));
                }
            }

            return track;
        }

        public override void SetFloatCurveTrack(FloatCurveTrack inTrack)
        {
            var cli_track  = new Clicross.FloatCurveTrack();
            if (inTrack != null)
            {
                cli_track= Convert<Clicross.FloatCurveTrack>(inTrack);
                foreach (var cross_key in inTrack.Keys)
                {
                    cli_track.Keys.Add(Convert<Clicross.FloatCurveKey>(cross_key));
                }
            }

            _Value = cli_track;
        }
    }

}