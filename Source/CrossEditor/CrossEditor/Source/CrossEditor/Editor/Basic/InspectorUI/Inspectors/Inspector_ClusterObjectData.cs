using CEngine;

namespace CrossEditor
{
    class Inspector_ClusterObjectData : Inspector_Struct_With_Property
    {
        protected ClusterObjectDataInfo _Info;

        public override void InspectObject(object Object, object Tag = null)
        {
            _Info = (ClusterObjectDataInfo)Object;
            base.InspectObject(Object);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
        }
    }
}
