using EditorUI;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    class FoldStateManager
    {
        public const string FOLD_STATES_FILENAME = "FoldStates.config";

        static FoldStateManager _Instance = new FoldStateManager();

        SortedSet<string> _FoldStateSet;

        public static FoldStateManager GetInstance()
        {
            return _Instance;
        }

        public FoldStateManager()
        {
            _FoldStateSet = new SortedSet<string>();
        }

        public void SetFoldState(string Name, bool bFolded)
        {
            if (bFolded)
            {
                _FoldStateSet.Add(Name);
            }
            else
            {
                if (_FoldStateSet.Contains(Name))
                {
                    _FoldStateSet.Remove(Name);
                }
            }
        }

        public bool GetFoldState(string Name)
        {
            return _FoldStateSet.Contains(Name);
        }

        public void SaveFoldStates()
        {
            MainUI MainUI = MainUI.GetInstance();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            if (ProjectDirectory == "")
            {
                return;
            }
            string Filename = ProjectDirectory + "/" + FOLD_STATES_FILENAME;

            XmlScript Xml = new XmlScript();
            Record RootRecord = Xml.GetRootRecord();

            Record RecordFoldStates = RootRecord.AddChild();
            RecordFoldStates.SetTypeString("FoldStates");

            foreach (string CascadeName in _FoldStateSet)
            {
                Record RecordFoldState = RecordFoldStates.AddChild();
                RecordFoldState.SetTypeString("FoldState");
                RecordFoldState.SetString("CascadeName", CascadeName);
            }

            Xml.Save(Filename);
        }

        public void LoadFoldStates()
        {
            MainUI MainUI = MainUI.GetInstance();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            string Filename = ProjectDirectory + "/" + FOLD_STATES_FILENAME;
            if (File.Exists(Filename) == false)
            {
                return;
            }
            _FoldStateSet.Clear();
            XmlScript Xml = new XmlScript();
            Xml.Open(Filename);
            Record RootRecord = Xml.GetRootRecord();

            Record RecordFoldStates = RootRecord.FindByTypeString("FoldStates");
            if (RecordFoldStates != null)
            {
                int ChildCount = RecordFoldStates.GetChildCount();
                for (int i = 0; i < ChildCount; i++)
                {
                    Record RecordFoldState = RecordFoldStates.GetChild(i);
                    DebugHelper.Assert(RecordFoldState.GetTypeString() == "FoldState");
                    string CascadeName = RecordFoldState.GetString("CascadeName");
                    SetFoldState(CascadeName, true);
                }
            }
        }
    }
}
