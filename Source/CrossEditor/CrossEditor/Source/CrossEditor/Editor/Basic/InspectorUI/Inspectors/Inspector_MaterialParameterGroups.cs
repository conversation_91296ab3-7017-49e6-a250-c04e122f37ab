using Clicross;
using System.Collections.Generic;

namespace CrossEditor
{
    class Inspector_MaterialParameterGroups : Inspector
    {
        List<MaterialParameterGroup> mParameterGroups;

        public override void InspectObject(object Object, object Tag = null)
        {
            mParameterGroups = Object as List<MaterialParameterGroup>;

            ClearChildInspectors();

            foreach (var parameterGroup in mParameterGroups)
            {
                Inspector inspector = new Inspector_MaterialParameterGroup(false);
                inspector.SetPropertyModifiedFunction(PropertyModifiedEvent);
                AddChildInspector(inspector);
                inspector.InspectObject(parameterGroup);
            }
        }
    }
}
