using EditorUI;
using System;

namespace CrossEditor
{
    public class EditWithProgress
    {
        static Color Transparent = Color.FromRGBA(120, 120, 120, 0);
        static Color White = Color.FromRGBA(92, 92, 92, 10);
        static Color translucence = Color.FromRGBA(120, 120, 120, 70);

        protected Control _Container;
        protected Panel _BackgroundBar;
        protected Panel _ProgressBar;
        protected Edit _EditBox;
        protected Panel _Mask;

        protected bool _bLeftMouseDown;
        protected bool _bMouseMoved;
        protected int _SavedMouseX;
        protected int _SavedMouseY;

        public delegate void EditAfterChangedEventHandler(Control Sender);
        public EditAfterChangedEventHandler TextChangedEvent;

        protected decimal _Min;
        protected decimal _Max;
        protected decimal _DragValue;
        protected decimal _Step;

        string _OriginalText;
        string _OldText;

        protected bool _bReadOnly;
        protected bool _bDraggable;

        public EditWithProgress(Control Container)
        {
            Container.PositionChangedEvent += ContainerPositionChangedEvent;

            _Min = decimal.MinValue;
            _Max = decimal.MaxValue;
            _DragValue = 0;
            _Step = 0.1m;
            _OriginalText = "";
            _OldText = "";
            _bReadOnly = false;
            _bDraggable = true;

            _BackgroundBar = new Panel();
            Container.AddChild(_BackgroundBar);

            _ProgressBar = new Panel();
            _ProgressBar.SetBackgroundColor(translucence);
            Container.AddChild(_ProgressBar);

            _EditBox = new Edit();
            _BackgroundBar.SetBackgroundColor(_EditBox.GetBackgroundColor());
            _EditBox.SetBackgroundColor(Transparent);
            _EditBox.SetFontSize(Inspector_Property.PROPERTY_FONT_SIZE);
            _EditBox.Initialize(EditMode.Simple_SingleLine);
            _EditBox.LoadSource("");
            EditContextUI.GetInstance().RegisterEdit(_EditBox);
            _EditBox.SelfFocusChangedEvent += OnEditFocusChanged;
            _EditBox.CharInputEvent += OnEditCharInput;
            Container.AddChild(_EditBox);

            _Mask = new Panel();
            _Mask.SetBackgroundColor(White);
            _Mask.LeftMouseDownEvent += OnMaskLeftMouseDown;
            _Mask.LeftMouseUpEvent += OnMaskLeftMouseUp;
            _Mask.SetCursorEvent += OnMaskSetCursor;
            Container.AddChild(_Mask);
        }

        private void ContainerPositionChangedEvent(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            _BackgroundBar.SetWidth(Sender.GetWidth());
            _EditBox.SetWidth(Sender.GetWidth() - 8);
            _Mask.SetWidth(Sender.GetWidth());
        }

        public UIManager GetUIManager()
        {
            if (_BackgroundBar != null)
            {
                return _BackgroundBar.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public void SetPosition(int X, int Y, int Width, int Height)
        {
            _BackgroundBar.SetPosition(X, Y, Width, Height);
            _ProgressBar.SetPosition(X, Y, 0, Height);
            UpdateProgress();
            _EditBox.SetPosition(X + 8, Y, Width - 8, Height);
            _Mask.SetPosition(X, Y, Width, Height);
        }

        public void SetX(int X)
        {
            _BackgroundBar.SetX(X);
            _ProgressBar.SetX(X);
            _EditBox.SetX(X + 8);
            _Mask.SetX(X);
        }

        public void SetRange(decimal Min, decimal Max)
        {
            _Min = Min;
            _Max = Max;
        }

        /// <summary>
        /// Set EditBox text from inspector which after reading value from property.
        /// </summary>
        /// <param name="Text"></param>
        public void SetText(string Text)
        {
            decimal ParseValue;
            // If text is <multple-value> or <null>, set draggable to false
            SetDraggable(decimal.TryParse(Text, out ParseValue));
            if (_bDraggable)
            {
                // Init DragValue
                _DragValue = ParseValue;
                UpdateProgress();
            }
            else
            {
                _OriginalText = Text;
            }
            _EditBox.SetText(Text);
        }

        public void SetReadOnly(bool ReadOnly)
        {
            _bReadOnly = ReadOnly;
            _EditBox.SetReadOnly(_bReadOnly);
            _EditBox.SetTextColor(ReadOnly ? Color.EDITOR_UI_GRAY_DRAW_COLOR : Transparent);
            _BackgroundBar.SetBackgroundColor(ReadOnly ? Color.EDITOR_UI_GRAY_DRAW_COLOR : Transparent);
            _Mask.SetVisible(!ReadOnly);
        }

        public void SetDraggable(bool Enable)
        {
            _bDraggable = Enable;
            _ProgressBar.SetVisible(Enable);
        }

        public void SetStep(decimal Step)
        {
            _Step = Step;
        }

        public void SetVisible(bool flag)
        {
            _BackgroundBar.SetVisible(flag);
            _ProgressBar.SetVisible(flag);
            _EditBox.SetVisible(flag);
            _Mask.SetVisible(flag);
        }

        public decimal GetMax()
        {
            return _Max;
        }

        public decimal GetMin()
        {
            return _Min;
        }

        public Edit GetEditValue() => _EditBox;
        public float GetEditBoxValue()
        {
            return float.Parse(_EditBox.GetText() != "" ? _EditBox.GetText() : "0");
        }

        public Panel GetBackground() => _BackgroundBar;

        public int GetEndX() => _BackgroundBar.GetEndX();

        public int GetX() => _BackgroundBar.GetX();

        public string GetOldText() => _OldText;

        /// <summary>
        /// Set EditBox text from self, it will be called after enter input and losing focus.
        /// </summary>
        protected void UpdateEdit()
        {
            decimal InputValue;
            if (decimal.TryParse(_EditBox.GetText(), out InputValue))
            {
                TextChangedEvent?.Invoke(_EditBox);
                _DragValue = InputValue;
                // If the input is valid, make this edit draggable
                SetDraggable(true);
                UpdateProgress();
            }
            else
            {
                // Revert value if DragValue can be used
                if (_bDraggable)
                {
                    _EditBox.SetText(_DragValue.ToString());
                }
                else
                {
                    _EditBox.SetText(_OriginalText);
                }
            }
        }

        public void UpdateProgress()
        {
            if (_Min == decimal.MinValue || _Max == decimal.MaxValue)
            {
                return;
            }

            decimal Ratio = (_DragValue - _Min) / (_Max - _Min);
            Ratio = Math.Clamp(Ratio, 0.0m, 1.0m);
            _ProgressBar.SetWidth((int)(_BackgroundBar.GetWidth() * Ratio));
        }

        #region Edit Event

        protected virtual void OnEditFocusChanged(Control Sender)
        {
            if (_bReadOnly == false)
            {
                if (Sender.IsFocused() == false)
                {
                    _Mask.SetVisible(true);
                    UpdateEdit();
                    _EditBox.SetSelection(new Selection());
                    _EditBox.LocateToHome();
                    _BackgroundBar.SetBorderColor(_BackgroundBar.GetBackgroundColor());
                }
                else
                {
                    _BackgroundBar.SetBorderColor(Color.EDITOR_UI_ACTIVE_TOOL_OR_MENU);
                }
            }
        }

        private void OnEditCharInput(Control Sender, char Char, ref bool bContinue)
        {
            if (_bReadOnly == false)
            {
                if (Char == '\r')
                {
                    UpdateEdit();
                    _EditBox.DoSelectAll();
                }
            }
        }

        #endregion

        #region Mask Event

        protected virtual void OnMaskSetCursor(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY))
            {
                SystemCursor SystemCursor = _bDraggable ? SystemCursor.SizeWE : SystemCursor.Edit;
                Cursor Cursor = GetDevice().GetSystemCursor(SystemCursor);
                GetDevice().SetCursor(Cursor);
                bContinue = false;
            }
        }

        protected virtual void OnMaskLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Sender.CaptureMouse();

            _bLeftMouseDown = true;
            _bMouseMoved = false;
            _SavedMouseX = GetDevice().GetScreenMouseX();
            _SavedMouseY = GetDevice().GetScreenMouseY();

            _Mask.MouseMoveEvent += OnMaskMouseMove;

            _OldText = _EditBox.GetText();

            bContinue = false;
        }

        protected virtual void OnMaskLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Sender.ReleaseMouse();
            if (_bLeftMouseDown)
            {
                _bLeftMouseDown = false;
                // Click without move
                if (_bMouseMoved == false)
                {
                    Sender.SetVisible(false);
                    _EditBox.SetFocus();
                    _EditBox.DoSelectAll();
                }
                // After Dragging
                else
                {
                    GetDevice().ShowMouseCursor();
                    _BackgroundBar.SetBorderColor(_BackgroundBar.GetBackgroundColor());
                    _ProgressBar.SetBackgroundColor(translucence);
                }

                _Mask.MouseMoveEvent -= OnMaskMouseMove;

                EditOperation EditOperation = EditOperationManager.GetInstance().GetLatestOperation();
                if (EditOperation != null)
                {
                    EditOperation._bCombinable = false;
                }

                bContinue = false;
            }
        }

        protected virtual void OnMaskMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_bLeftMouseDown && _bDraggable)
            {
                MouseX = GetDevice().GetScreenMouseX();
                MouseY = GetDevice().GetScreenMouseY();



                if (_bMouseMoved == false)
                {
                    _bMouseMoved = Math.Abs(MouseX - _SavedMouseX) + Math.Abs(MouseY - _SavedMouseY) > 2;
                    _BackgroundBar.SetBorderColor(Color.EDITOR_UI_ACTIVE_TOOL_OR_MENU);
                    _ProgressBar.SetBackgroundColor(Color.EDITOR_UI_ACTIVE_TOOL_OR_MENU);
                    // Make all edit controls lose focus
                    GetUIManager().SetFocusControl(null);
                }

                if (_bMouseMoved)
                {
                    Device Device = GetDevice();
                    bool bControl = Device.IsControlDown();
                    bool bShift = Device.IsShiftDown();
                    bool bAlt = Device.IsAltDown();
                    bool bControlOnly = bControl && !bShift && !bAlt;

                    GetDevice().HideMouseCursor();
                    if (MouseX != _SavedMouseX)
                    {
                        decimal Step = _Step;
                        if (bControlOnly)
                        {
                            Step = Step / 50.0m;
                        }
                        _DragValue += (MouseX - _SavedMouseX) * Step;
                        _DragValue = Math.Clamp(_DragValue, _Min, _Max);
                        UpdateProgress();
                        _EditBox.SetText(_DragValue.ToString());
                        TextChangedEvent?.Invoke(_EditBox);
                    }
                    GetDevice().SetMousePosition(_SavedMouseX, _SavedMouseY);
                }
            }
        }

        #endregion
    }
}
