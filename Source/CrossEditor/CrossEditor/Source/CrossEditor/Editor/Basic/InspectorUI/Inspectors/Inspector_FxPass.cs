using EditorUI;

namespace CrossEditor
{
    class Inspector_FxPass : Inspector_Struct_With_Property
    {
        Button _Button;

        public override void InspectObject(object Object, object Tag = null)
        {
            base.InspectObject(Object, Tag);

            _Button = new Button();
            _Button.Initialize();
            _Button.SetText("Apply");
            _Button.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _Button.SetFontSize(18);
            _Button.SetTextOffsetY(2);
            _Button.SetToolTips("Apply changes and save to file.");
            _Button.ClickedEvent += OnButtonApplyClicked;
            _Button.SetVisible(true);
            _ChildContainer.AddChild(_Button);
        }

        private void OnButtonApplyClicked(Button Sender)
        {
            FxEditorUI.GetInstance().OnButtonSaveClicked(null);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            if (_Button != null)
            {
                _Button.SetPosition(SPAN_X, Y + SPAN_Y, Width - SPAN_X * 2, DEFAULT_HEIGHT);
                Y += DEFAULT_HEIGHT + 2 * SPAN_Y;
                _ChildContainer.SetHeight(Y);
            }
        }
    }
}
