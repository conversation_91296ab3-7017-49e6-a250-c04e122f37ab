using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    internal class Inspector_FFSWGS84Component : Inspector_Component
    {
        protected FFSWGS84Component _FFSWGS84Component = null;

        Button _ButtonSyncToCamera;

        public Inspector_FFSWGS84Component(List<Entity> Entities)
            : base(Entities)
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _FFSWGS84Component = Object as FFSWGS84Component;
            base.InspectObject(Object, Tag);

            _ButtonSyncToCamera = new Button();
            _ButtonSyncToCamera.Initialize();
            _ButtonSyncToCamera.SetImage(UIManager.LoadUIImage("Editor/Icons/Edit/TranslateMode.png"));
            _ButtonSyncToCamera.SetToolTips("Sync to editor camera");
            _ButtonSyncToCamera.ClickedEvent += OnButtonSyncToCameraClicked;
            _ButtonBar.AddChild(_ButtonSyncToCamera);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            AlignedPanel Container = _SelfContainer as AlignedPanel;
            _ButtonSyncToCamera.SetPosition(0, 2, 20, 20);
            Container.FloatToRight(_ButtonSyncToCamera);

            base.UpdateLayout(Width, ref Y);
        }

        public override object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type type = _FFSWGS84Component.GetType();
            PropertyInfo PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
                return PropertyInfo.GetValue(Object);

            return null;
        }

        public override void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type type = Object.GetType();
            PropertyInfo PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
                // Refresh inspectors after setting PropertyInfo
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    GetInspectorHandler().InspectObject();
                });
            }

            EditorScene.GetInstance().SetDirty();
        }

        public void OnButtonSyncToCameraClicked(Button sender)
        {
            DoSyncToCamera();
        }

        void DoSyncToCamera()
        {
            foreach (Entity Entity in _Entities)
            {
                GenerateWgs84(Entity);
                MoveCameraToEntity(Entity);
            }
        }

        void GenerateWgs84(Entity Entity)
        {
            WGS84SystemG.OnChangeTransform(Entity.World.GetNativePointer(), Entity.EntityID);

            Transform transComp = Entity.GetComponent<Transform>();

            if (transComp != null)
                transComp.UpdateTransform();
        }

        void MoveCameraToEntity(Entity Entity)
        {
            FPSCamera EditorCamera = EditorScene.GetInstance().GetFPSCamera();

            Double3 EntityWorldPosition = TransformSystemG.GetWorldTranslationT(Entity.World.GetNativePointer(), Entity.EntityID);
            Quaternion64 EntityWorldRotation = TransformSystemG.GetWorldRotationT(Entity.World.GetNativePointer(), Entity.EntityID);
            Double3 EntityWorldRotationInDegree = Quaternion64.Quaternion64ToEuler(EntityWorldRotation);

            EditorCamera.SetPosition(EntityWorldPosition);
            EditorCamera.SetRotationInDegree(EntityWorldRotationInDegree);
        }
    }
}
