using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class AddComponentMenu
    {
        public List<MenuItem> Menus = new List<MenuItem>();
    }

    public class Inspector_Entity : Inspector
    {
        Entity _Entity;
        List<Entity> _Entities;

        Panel _PanelIcon;
        Check _CheckEnable;
        Label _LabelName;
        Button _ButtonAddComponent;

        AlignedPanel _FilterPanel;
        Panel _FilterSeparator;
        List<Button> _FilterButtons;
        public static int _FilterIndex = 0;
        static Dictionary<string, List<Type>> _Filter = new Dictionary<string, List<Type>>
        {
            { "All", null},
            { "General", new List<Type>{
                typeof(Transform),
                typeof(ModelComponent),
                typeof(InstancedStaticModelComponent),
                typeof(Light),
                typeof(SkyLightComponent),
                typeof(Camera),
                typeof(PostProcessVolumeComponent),
                typeof(PivotComponent),
            }},
            { "Actor", new List<Type>{
                typeof(CharacterMovementComponent),
                typeof(ControllableUnitComponent),
                typeof(Trajectory),
                typeof(MotionMovementComponent),
                typeof(SimpleMovementComponent),
                typeof(Animator),
                typeof(Script),
                typeof(Skeleton),
            }},
            { "LOD", new List<Type>{
                typeof(ModelComponent),
                typeof(Camera),
            }},
            { "Misc", new List<Type>{
                typeof(LightProbeComponent),
                typeof(LightProbeVolumeComponent),
                typeof(LightMap),
                typeof(LightMapBaker),
                typeof(VRViewComponent),
                typeof(VehicleWheelComponent),
            }},
            { "Physics", new List<Type>{
                typeof(Physics),
                typeof(RenderProperty),
            }},
            { "Rendering", new List<Type>{
                typeof(ModelComponent),
                typeof(Light),
                typeof(SkyLightComponent),
                typeof(RenderProperty),
                typeof(ReflectionProbeComponent),
                typeof(Camera),
                typeof(FoliageComponent),
                typeof(WorldPartitionProperty),
                typeof(CloudComponent),
                typeof(ParticleSystemComponent),
            }},
        };

        public Inspector_Entity()
        {
        }

        public List<Component> GetAllComponents(List<Entity> Entities)
        {
            List<Component> Components = new List<Component>();
            SortedSet<string> ComponentTypeSet = new SortedSet<string>();
            foreach (Entity Entity in Entities)
            {
                foreach (Component Component in Entity.Components)
                {
                    Type ComponentType = Component.GetType();
                    if (ComponentTypeSet.Contains(ComponentType.FullName) == false)
                    {
                        Components.Add(Component);
                        ComponentTypeSet.Add(ComponentType.FullName);
                    }
                }
            }
            Components.Sort(Component.CompareByOrder);
            return Components;
        }

        public static List<Entity> GetEntitiesContainsComponent(List<Entity> Entities, Component Component)
        {
            List<Entity> EntitiesContainsComponent = new List<Entity>();
            Type ComponentType = Component.GetType();
            foreach (Entity Entity in Entities)
            {
                foreach (Component Component1 in Entity.Components)
                {
                    if (Component1.GetType() == ComponentType)
                    {
                        EntitiesContainsComponent.Add(Entity);
                    }
                }
            }
            return EntitiesContainsComponent;
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            if (Object is Entity)
            {
                _Entity = (Entity)Object;
                _Entities = new List<Entity>();
                _Entities.Add(_Entity);
            }
            else if (Object is List<Entity>)
            {
                _Entities = (List<Entity>)Object;
                if (_Entities.Count > 0)
                {
                    _Entity = _Entities[0];
                }
                else
                {
                    _Entity = null;
                }
            }
            else
            {
                DebugHelper.Assert(false);
            }

            if (_Entity == null)
                return;

            _PanelIcon = new Panel();
            _PanelIcon.Initialize();
            _PanelIcon.SetImage(UIManager.LoadUIImage("Editor/Game/GameObject.png"));
            UpdatePanelIconColor();
            _SelfContainer.AddChild(_PanelIcon);

            _CheckEnable = new Check();
            _CheckEnable.Initialize();
            _CheckEnable.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _CheckEnable.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _CheckEnable.SetAutoCheck(true);
            _CheckEnable.SetChecked(_Entity.Enable);
            _CheckEnable.ClickedEvent += OnCheckEnableClicked;
            _SelfContainer.AddChild(_CheckEnable);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelName.SetTextAlign(TextAlign.CenterLeft);
            _SelfContainer.AddChild(_LabelName);
            string NameText = "";
            if (_Entities.Count > 1)
            {
                NameText = string.Format("<Selected {0} Objects>", _Entities.Count);
            }
            else
            {
                NameText = _Entity.GetName();
            }
            _LabelName.SetText(NameText);

            bool bIsRoot = _Entity.IsRoot();

            ClearChildInspectors();

            if (_Entity.IsPrefabInstance())
            {
                Inspector_Prefab Inspector_Prefab = new Inspector_Prefab();
                Inspector_Prefab.InspectObject(Object);
                AddChildInspector(Inspector_Prefab);
            }

            List<Component> GameComponentList = GetAllComponents(_Entities);
            bool isGameobject = false;
            foreach (Component comp in GameComponentList)
            {
                if (comp is GOSerializerComponent)
                {
                    List<Entity> EntitiesContainsComponent = GetEntitiesContainsComponent(_Entities, comp);
                    ComponentAttribute attribute = CrossEngine.GetInstance().EditorComponents[comp.GetType()];
                    isGameobject = true;
                    Inspector_Component comp_inspector = (Inspector_Component)Activator.CreateInstance(attribute.InspectorType, new object[] { EntitiesContainsComponent });
                    comp_inspector.InspectObject(comp);
                    AddChildInspector(comp_inspector);
                    break;
                }
            }
            if (!isGameobject)
            {
                foreach (Component Component1 in GameComponentList)
                {
                    List<Entity> EntitiesContainsComponent = GetEntitiesContainsComponent(_Entities, Component1);

                    ComponentAttribute attribute = CrossEngine.GetInstance().EditorComponents[Component1.GetType()];

                    if (!(bIsRoot && Component1 is Transform))
                    {
                        Inspector_Component comp_inspector = (Inspector_Component)Activator.CreateInstance(attribute.InspectorType, new object[] { EntitiesContainsComponent });
                        comp_inspector.InspectObject(Component1);
                        AddChildInspector(comp_inspector);
                    }
                }

                _ButtonAddComponent = new Button();
                _ButtonAddComponent.Initialize();
                _ButtonAddComponent.SetText("Add Component");
                _ButtonAddComponent.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonAddComponent.SetFontSize(18);
                _ButtonAddComponent.SetTextOffsetY(2);
                _ButtonAddComponent.ClickedEvent += OnButtonAddComponentClicked;
                _ChildContainer.AddChild(_ButtonAddComponent);
            }
            else
            {
                _ButtonAddComponent = new Button();
                _ButtonAddComponent.Initialize();
                _ButtonAddComponent.SetText("Add GameObject Components");
                _ButtonAddComponent.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonAddComponent.SetFontSize(18);
                _ButtonAddComponent.SetTextOffsetY(2);
                _ButtonAddComponent.ClickedEvent += OnButtonAddGameObjectComponentClicked;
                _ChildContainer.AddChild(_ButtonAddComponent);

            }
            InitializeFilter();
        }

        public Entity GetEntity() => _Entity;

        public void InitializeFilter()
        {
            _FilterSeparator = new Panel();
            _FilterSeparator.SetBackgroundColor(Color.EDITOR_UI_SEPARATOR);
            _SelfContainer.AddChild(_FilterSeparator);

            _FilterPanel = new AlignedPanel(SPAN_X);
            _SelfContainer.AddChild(_FilterPanel);

            _FilterButtons = new List<Button>();

            int KeyIndex = 0;
            foreach (string Text in _Filter.Keys)
            {
                Button Button = new Button();
                Button.SetText(Text);
                Button.SetTagInt1(KeyIndex++);
                Button.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
                Button.SetTextAlign(TextAlign.CenterCenter);
                Button.SetSize(Button.CalculateTextWidth() + 2 * SPAN_X, DEFAULT_HEIGHT);
                Button.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                Button.ClickedEvent += OnFilterButtonClicked;
                _FilterButtons.Add(Button);
                _FilterPanel.AddChild(Button);
            }

            OnFilterButtonClicked(_FilterButtons[_FilterIndex]);
        }

        public void OnFilterButtonClicked(Button Sender)
        {
            _FilterIndex = Sender.GetTagInt1();

            _FilterButtons.ForEach(Item => Item.SetNormalColor(Color.EDITOR_UI_COLOR_KEY));
            Sender.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);

            foreach (Inspector Child in _ChildInspectors)
            {
                if (Child is Inspector_Component)
                {
                    if (Sender.GetText() == "All")
                    {
                        Child.SetVisible(true);
                    }
                    else
                    {
                        Child.SetVisible(
                            _Filter[Sender.GetText()].Contains(
                                (Child as Inspector_Component).GetComponent().GetType()));
                    }
                }
            }

            GetInspectorHandler().UpdateLayout();
        }

        public void UpdateFilter(int Width, ref int Y)
        {
            _FilterSeparator.SetPosition(0, Y, Width, 1);
            Y += 1;

            int Line = 0;
            foreach (Button Button in _FilterButtons)
            {
                _FilterPanel.FloatToLeft(Button);
                Button.SetY(Line * 24 + 2);
                if (Button.GetEndX() > Width - SPAN_X)
                {
                    _FilterPanel.ClearLastLayout();
                    _FilterPanel.FloatToLeft(Button);
                    Line++;
                    Button.SetY(Line * 24 + 2);
                }
            }

            int FilterHeight = (Line + 1) * 24;
            _FilterPanel.SetPosition(0, Y, Width, FilterHeight);
            Y += FilterHeight;

            _FilterPanel.ClearLastLayout();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _PanelIcon.SetPosition(SPAN_X, 4, 20, 20);
            _CheckEnable.SetPosition(_PanelIcon.GetEndX() + SPAN_X, 7, 14, 14);
            int LabelNameX = _CheckEnable.GetEndX() + SPAN_X;
            int LabelNameWidth = Width - SPAN_X - LabelNameX;
            _LabelName.SetPosition(LabelNameX, 6, LabelNameWidth, 16);
            Height += 28;
            UpdateFilter(Width, ref Height);

            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height;

            base.UpdateLayout(Width, ref Y);

            int Y1 = _ChildContainer.GetHeight();
            int AvailableWidth = Width - SPAN_X * 2;
            _ButtonAddComponent.SetPosition(SPAN_X, Y1 + 5, AvailableWidth, 30);
            Y1 += 40;
            _ChildContainer.SetHeight(Y1);
            Y += 40;
        }

        public override bool OnDropPathes(int MouseX, int MouseY, List<string> PathesDragged)
        {
            foreach (string PathDragged in PathesDragged)
            {
                string PathDragged1 = EditorUtilities.EditorFilenameToStandardFilename(PathDragged);
                string Extension = PathHelper.GetExtension(PathDragged1);

                var components = CrossEngine.GetInstance().EditorComponents;

                foreach (KeyValuePair<Type, ComponentAttribute> entry in components)
                {
                    if (ComponentUtil.OnDropPathes(entry.Key, this, PathDragged1, Extension))
                    {
                        break;
                    }
                }

            }
            return false;
        }

        public Inspector_Component FindComponentInspector<T>(bool _Recursive = true) where T : Component
        {
            var _inspector = FindChildInspector(item =>
            {
                Inspector_Component Inspector_Component = item as Inspector_Component;
                if (Inspector_Component != null)
                {
                    Component Component = Inspector_Component.GetComponent();
                    if (Component.GetType() == typeof(T))
                    {
                        return true;
                    }
                }
                return false;
            }, _Recursive);
            return _inspector as Inspector_Component;
        }

        void UpdatePanelIconColor()
        {
            if (_Entity == null)
                return;
            _PanelIcon.SetEnable(_Entity.Enable);
        }

        void OnCheckEnableClicked(Check Sender)
        {
            if (_Entity == null)
                return;
            _Entity.Enable = _CheckEnable.GetChecked();
            UpdatePanelIconColor();
            HierarchyUI.GetInstance().UpdateVisibilityButton();
        }

        public static AddComponentMenu BuildAddComponentMenu(Menu Menu, bool bMainMenu, bool AddGOComponent = false)
        {
            int SubMenuMinWidth = 200;

            AddComponentMenu AddComponentMenu = new AddComponentMenu();


            var components = CrossEngine.GetInstance().EditorComponents;

            List<(string, Type)> DisplayNameForComp = new List<(string, Type)>();
            Dictionary<string, Type> DisplayNameForCompMapping = new Dictionary<string, Type>();

            Func<string, Type, bool> safeAdd = ((name, type) =>
            {
                if (DisplayNameForCompMapping.ContainsKey(name))
                {
                    ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information, name + "is duplicated with" + type.Name + " " + DisplayNameForCompMapping[name].Name);
                    return false;
                }

                bool isGOComponent = (type == typeof(GameObjectComponent) ||
                                      type.IsSubclassOf(typeof(GameObjectComponent)));

                if (isGOComponent && AddGOComponent)
                {
                    if (type != typeof(GameObjectComponent))
                    {
                        DisplayNameForCompMapping.Add(name, type);
                    }
                }
                else if (!isGOComponent && !AddGOComponent)
                {
                    DisplayNameForCompMapping.Add(name, type);
                }

                return true;
            }
            );

            foreach (KeyValuePair<Type, ComponentAttribute> entry in components)
            {
                if (entry.Value.ShowOrder > 0)
                {
                    string dispalyName = entry.Value.DisplayUINames;
                    if (dispalyName == "")
                    {
                        dispalyName = entry.Key.Name;
                    }

                    {
                        safeAdd(dispalyName, entry.Key);
                    }
                }
            }

            foreach (var items in DisplayNameForCompMapping)
            {
                DisplayNameForComp.Add((items.Key, items.Value));
            }

            DisplayNameForComp.Sort(delegate ((string, Type) x, (string, Type) y)
            {
                if (!components[x.Item2].ShowOrder.Equals(components[y.Item2].ShowOrder))
                {
                    return components[x.Item2].ShowOrder.CompareTo(components[y.Item2].ShowOrder);
                }
                return x.Item1.CompareTo(y.Item1);
            }
            );
            var MenuItems = MenuItemUtils.CreateMenu(Menu, Menu.GetUIManager(), DisplayNameForComp,
                delegate ((List<string>, Type) entry)
                {
                    MenuItem menuitem = new MenuItem();
                    menuitem.ClickedEvent += (MenuItem Sender) =>
                    {
                        if (entry.Item2.IsSubclassOf(typeof(GameObjectComponent)))
                        {
                            Inspector_GameObject Inspector_GameObject = InspectorUI.GetInstance().GetGameObjectInspector();
                            if (Inspector_GameObject != null)
                            {
                                var OnMenuItemAddComponentClikcedMethod = typeof(Inspector_GameObject).GetMethod("OnMenuItemAddComponentClikced");
                                var SpecializedMethod = OnMenuItemAddComponentClikcedMethod.MakeGenericMethod(entry.Item2);
                                SpecializedMethod.Invoke(Inspector_GameObject, new object[] { Sender });
                            }
                        }
                        else
                        {
                            Inspector_Entity Inspector_Entity = InspectorUI.GetInstance().GetEntityInspector();
                            if (Inspector_Entity != null)
                            {
                                var OnMenuItemAddComponentClikcedMethod = typeof(Inspector_Entity).GetMethod("OnMenuItemAddComponentClikced");
                                var SpecializedMethod = OnMenuItemAddComponentClikcedMethod.MakeGenericMethod(entry.Item2);
                                SpecializedMethod.Invoke(Inspector_Entity, new object[] { Sender });
                            }
                        }
                    };

                    return menuitem;
                },
                delegate (Menu menu, int l)
            {
                if (bMainMenu)
                {
                    menu.SetTextOnly(true);
                    menu.SetMinWidth(SubMenuMinWidth);
                }
            });

            AddComponentMenu.Menus = MenuItems.Values.ToList();

            return AddComponentMenu;
        }

        public static void UpdateAddComponentMenu(AddComponentMenu AddComponentMenu)
        {
            Inspector_Entity Inspector_Entity = InspectorUI.GetInstance().GetEntityInspector();
            bool bCanAddComponent = (Inspector_Entity != null);
            foreach (var item in AddComponentMenu.Menus)
            {
                item.SetEnable(bCanAddComponent);
            }
        }

        void OnButtonAddComponentClicked(Button Sender)
        {
            OnButtonAddComponentClickedImpl(Sender, false);
        }
        void OnButtonAddGameObjectComponentClicked(Button Sender)
        {
            OnButtonAddComponentClickedImpl(Sender, true);
        }

        void OnButtonAddComponentClickedImpl(Button Sender, bool GOComponent)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            MenuContextMenu.SetTextOnly(true);
            MenuContextMenu.SetMinWidth(Sender.GetWidth());

            bool bMainMenu = false;
            BuildAddComponentMenu(MenuContextMenu, bMainMenu, GOComponent);

            bool bHCentered = true;
            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender, bHCentered);
        }

        void BeginRecord()
        {
            EditOperationManager.GetInstance().BeginRecordCompoundOperation();
        }

        void EndRecord()
        {
            EditOperationManager.GetInstance().EndRecordCompoundOperation();
        }

        public void OutputRepeatedTips(string Component)
        {
            string TipsMessage = "";
            if (_Entities.Count > 1)
            {
                TipsMessage = string.Format("There is one or more entities already have {0} component.", Component);
            }
            else
            {
                TipsMessage = string.Format("There is already a {0} component in this entity.", Component);
            }
            ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information, TipsMessage);
            MainUI.GetInstance().ActivateDockingCard_Console();
        }

        static public void OutputConsoleTips(string TipsMessage)
        {
            ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information, TipsMessage);
            MainUI.GetInstance().ActivateDockingCard_Console();
        }

        public bool EntitiesHasComponent(Type ComponentType)
        {
            foreach (Entity Entity in _Entities)
            {
                if (Entity.HasComponent(ComponentType))
                {
                    return true;
                }
            }
            return false;
        }

        public delegate List<Component> CreateComponentDelegate(Entity Entity);

        bool GeneralAddComponent(List<Type> ComponentTypes, CreateComponentDelegate CreateComponentDelegate)
        {
            foreach (Type ComponentType in ComponentTypes)
            {
                if (EntitiesHasComponent(ComponentType))
                {
                    OutputRepeatedTips(ComponentType.Name);
                    return false;
                }
            }
            EditOperation_AddComponents EditOperation = new EditOperation_AddComponents();
            foreach (Entity Entity in _Entities)
            {
                List<Component> ComponentList = CreateComponentDelegate(Entity);
                foreach (Component Component in ComponentList)
                {
                    EditOperation.AddAddComponentItem(Entity, Component);
                }
            }
            EditOperationManager.GetInstance().AddOperation(EditOperation);
            InspectorUI.GetInstance().InspectObject();
            EditorScene.GetInstance().SetDirty();
            return true;
        }


        public void OnMenuItemAddComponentClikced<T>(MenuItem Sender) where T : Component, new()
        {
            AddComponentClicked<T>(Sender != null ? Sender.GetText() : "");
        }

        public void AddComponentClicked<T>(string tags) where T : Component, new()
        {

            // need validtion
            if (!ComponentUtil.CheckForAddComponent<T>(_Entity))
            {
                return;
            }

            BeginRecord();
            List<Type> ComponentTypes = new List<Type> { typeof(T) };

            ComponentTypes.AddRange(ComponentUtil.ReflectForGetComponentAssociatedComp<T>());

            GeneralAddComponent(ComponentTypes, (Entity Entity) =>
            {
                var Result = new List<Component> { };

                var CreateComponentMethod = typeof(Entity).GetMethod("CreateComponent");

                foreach (Type t in ComponentTypes)
                {
                    var SpecializeMethod = CreateComponentMethod.MakeGenericMethod(t);
                    Component comp = (Component)SpecializeMethod.Invoke(Entity, new object[] { false });
                    comp.Reset();
                    comp.Initialize(tags);
                    Result.Add(comp);
                }

                return Result;
            });

            EndRecord();
        }
    }
}
