using EditorUI;
using System;
namespace CrossEditor
{
    class Inspector_Property_Folder : Inspector_Property
    {
        Edit _EditValue;
        Button _ButtonOpen;
        Button _ButtonBrowse;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _EditValue = new Edit();
            _EditValue.SetFontSize(PROPERTY_FONT_SIZE);
            _EditValue.Initialize(EditMode.Simple_SingleLine);
            _EditValue.SetReadOnly(true);
            _EditValue.LoadSource("");
            _EditValue.CharInputedEvent += OnEditValueCharInpupted;
            Container.AddChild(_EditValue);
            EditContextUI.GetInstance().RegisterEdit(_EditValue);
            _EditValue.SetSize(200, PROPERTY_FONT_SIZE);

            _ButtonOpen = new Button();
            _ButtonOpen.Initialize();
            _ButtonOpen.SetImage(UIManager.LoadUIImage("Editor/Icons/Common/BrowseInContent.png"));
            _ButtonOpen.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonOpen.SetToolTips("Show In Explorer");
            _ButtonOpen.ClickedEvent += OnButtonOpenClicked;
            Container.AddChild(_ButtonOpen);

            _ButtonBrowse = new Button();
            _ButtonBrowse.Initialize();
            _ButtonBrowse.SetFontSize(12);
            _ButtonBrowse.SetText("...");
            _ButtonBrowse.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonBrowse.SetToolTips("Browse Folder");
            _ButtonBrowse.ClickedEvent += OnButtonBrowseClicked;
            Container.AddChild(_ButtonBrowse);

            ReadValue();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditValueWidth = Math.Max(_EditValue.CalculateTextWidth(), DEFAULT_WIDTH);
            EditValueWidth = Math.Min(EditValueWidth, GetValueWidth() - 4 * SPAN_X - 2 * BUTTON_WIDTH);
            _EditValue.SetPosition(0, SPAN_Y, EditValueWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditValue);
            _ButtonOpen.SetPosition(0, 2, 16, 16);
            GetValueContainer().FloatToLeft(_ButtonOpen);
            _ButtonBrowse.SetPosition(0, 2, BUTTON_WIDTH, 16);
            GetValueContainer().FloatToLeft(_ButtonBrowse);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = PropertyValue.ToString();
            _EditValue.SetText(PropertyValueString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetText();
            SetPropertyValue(ValueString);
        }

        void OnEditValueCharInpupted(Control Sender, char Char, ref bool bContinue)
        {
            RecordAndWriteValue();
        }

        void OnButtonOpenClicked(Button Sender)
        {
            string FilePath = (string)GetPropertyValue();
            if (FilePath != null)
            {
                string FilePath1 = EditorUtilities.StandardDirectoryToEditorDirectory(FilePath);
                ProcessHelper.OpenContainingFolder(FilePath1);
            }
        }

        void OnButtonBrowseClicked(Button Sender)
        {
            string FilePath = (string)GetPropertyValue();

            bool bContentsOnly = true;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.Initialize(GetUIManager(), "", PathInputUIType.OpenFolder, null, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                string EditorFilename = PathInputed;
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(EditorFilename);
                _EditValue.SetText(StandardFilename);
                RecordAndWriteValue();
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }
    }
}
