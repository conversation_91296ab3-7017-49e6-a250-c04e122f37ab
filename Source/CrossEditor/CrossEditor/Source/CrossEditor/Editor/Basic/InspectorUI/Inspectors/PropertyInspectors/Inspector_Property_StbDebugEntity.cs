using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class Inspector_Property_StbDebugEntity : Inspector_Property
    {
        protected StoryBoardProperty _StbProperty;
        protected Panel _PanelEntity;
        protected Button _ButtonToolTips1;
        protected Edit _EditValue;
        protected Button _ButtonToolTips2;
        protected Button _ButtonFilter;
        protected Button _ButtonReset;

        public Inspector_Property_StbDebugEntity()
        {
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();
            _StbProperty = ObjectProperty.Object as StoryBoardProperty;

            _ButtonToolTips1 = new Button();
            _ButtonToolTips1.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips1.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips1.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips1.SetToolTips("Tooltips");
            Container.AddChild(_ButtonToolTips1);

            _PanelEntity = new Panel();
            _PanelEntity.SetBackgroundColor(new Color(0.3f, 0.3f, 0.3f, 1.0f));
            _PanelEntity.LeftMouseDoubleClickedEvent += OnPanelEntityLeftMouseDoubleClicked;
            ThumbnailHelper.GetInstance().EnableThumbnail(_PanelEntity);
            Container.AddChild(_PanelEntity);

            _ButtonToolTips2 = new Button();
            _ButtonToolTips2.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips2.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips2.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonToolTips2.SetToolTips("Tooltips");
            Container.AddChild(_ButtonToolTips2);

            _EditValue = new Edit();
            _EditValue.SetFontSize(PROPERTY_FONT_SIZE);
            _EditValue.Initialize(EditMode.Simple_SingleLine);
            _EditValue.SetReadOnly(true);
            _EditValue.LoadSource("");
            _EditValue.CharInputedEvent += OnEditValueCharInpupted;
            Container.AddChild(_EditValue);
            EditContextUI.GetInstance().RegisterEdit(_EditValue);
            _EditValue.SetSize(200, PROPERTY_FONT_SIZE);

            _ButtonFilter = new Button();
            _ButtonFilter.Initialize();
            _ButtonFilter.SetFontSize(12);
            _ButtonFilter.SetTextOffsetY(1);
            _ButtonFilter.SetText("v");
            _ButtonFilter.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonFilter.SetToolTips("Filter");
            _ButtonFilter.ClickedEvent += OnButtonFilterClicked;
            Container.AddChild(_ButtonFilter);

            _ButtonReset = new Button();
            _ButtonReset.Initialize();
            _ButtonReset.SetFontSize(12);
            _ButtonReset.SetTextOffsetY(2);
            _ButtonReset.SetText("Reset");
            _ButtonReset.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonReset.SetToolTips("Assign With Selected File");
            _ButtonReset.ClickedEvent += OnButtonResetClicked;
            Container.AddChild(_ButtonReset);

            SetReadOnly(_ObjectProperty.ReadOnly);

            ReadValue();
        }

        public int GetEditValueWidth()
        {
            int EditWidth;
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(MULTIPLE_VALUES_STRING);
            }
            else
            {
                string EntityName = PathHelper.GetNameOfPath(_EditValue.GetTagString1());
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(EntityName);
            }
            return EditWidth + 10;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int Height = 72;
            Y += Height - DEFAULT_HEIGHT;

            _Separator.SetY(Y - 1);

            _SelfContainer.SetHeight(Height);
            _Splitter.UpdateLayout(_Splitter.GetWidth(), Height);
            if (_ButtonRevert != null)
            {
                _ButtonRevert.SetY((Height - DEFAULT_HEIGHT) / 2);
            }

            _LabelName.SetY((Height - DEFAULT_HEIGHT) / 2);
            if (_ButtonToolTips != null)
            {
                _ButtonToolTips.SetY((Height - DEFAULT_HEIGHT) / 2);
            }

            int EditValueWidth = Math.Max(GetEditValueWidth(), DEFAULT_WIDTH);
            EditValueWidth = Math.Min(EditValueWidth, GetValueWidth() - 4 * SPAN_X - 64 - BUTTON_WIDTH);
            int EditY = (Height - PROPERTY_FONT_SIZE - SPAN_Y - 16) / 2;
            int ButtonY = EditY + PROPERTY_FONT_SIZE + SPAN_Y;

            _PanelEntity.SetPosition(0, SPAN_Y, 64, 64);
            GetValueContainer().FloatToLeft(_PanelEntity);
            _ButtonToolTips1.SetPosition(0, SPAN_Y, 64, 64);
            GetValueContainer().PlaceAsLeft(_ButtonToolTips1);
            _EditValue.SetPosition(0, EditY, EditValueWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditValue);
            _ButtonToolTips2.SetPosition(0, EditY, EditValueWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().PlaceAsLeft(_ButtonToolTips2);
            _ButtonReset.SetPosition(0, ButtonY, 48, 16);
            GetValueContainer().PlaceAsLeft(_ButtonReset);
            _ButtonFilter.SetPosition(0, EditY, 24, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_ButtonFilter);
        }

        void SetEntityNameToUI(string EntityPath)
        {
            string EntityName = PathHelper.GetNameOfPath(EntityPath);
            _EditValue.SetText_End(EntityName);
            _EditValue.SetTagString1(EntityPath);
            _ButtonToolTips1.SetToolTips(EntityPath);
            _ButtonToolTips2.SetToolTips(EntityPath);
            _PanelEntity.SetImage(null);
            _PanelEntity.SetTagString1(EntityPath);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            if (PropertyValue != null)
            {
                string PropertyValueString = PropertyValue.ToString();
                SetEntityNameToUI(PropertyValueString);
            }
            else
            {
                SetEntityNameToUI("");
            }
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetTagString1();
            SetPropertyValue(ValueString);
            GetInspectorHandler().UpdateLayout();
        }

        void OnPanelEntityLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
        }

        void OnEditValueCharInpupted(Control Sender, char Char, ref bool bContinue)
        {
            RecordAndWriteValue();
        }

        void OnButtonFilterClicked(Button Sender)
        {
            EntityFilterUI EntityFilterUI = EntityFilterUI.GetInstance();
            EntityFilterUI.ShowUI(_ButtonFilter,
                (Entity e) =>
                {
                    SetEntityNameToUI(e.GetName());
                    RecordAndWriteValue();
                    GameScene.GetInstance().DebugAnimatorEntity = e;
                },
                (ref List<Entity> entityList) =>
                {
                    GameScene.GetInstance().GetPIEEntityUsingStb(_StbProperty.Owner.Name, ref entityList);
                }
            );
        }

        void OnButtonResetClicked(Button Sender)
        {
            GameScene.GetInstance().DebugAnimatorEntity = null;
            DoResetToDefault();
        }

        void SetReadOnly(bool bReadOnly)
        {
            _ButtonFilter.SetEnable(!bReadOnly);
        }

        void DoResetToDefault()
        {
            SetEntityNameToUI("");
            RecordAndWriteValue();
        }

    }
}
