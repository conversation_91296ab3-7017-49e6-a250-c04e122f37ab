using CEngine;
using EditorUI;
using System;
using System.Text.RegularExpressions;
using static CrossEditor.Inspector_Property_List;

namespace CrossEditor
{
    class Inspector_MaterialParameterCollection : Inspector_Struct_With_Property
    {
        protected MaterialParameterCollection _MaterialParameterCollection;
        protected Panel _Panel;
        protected Label _LabelName;

        public Inspector_MaterialParameterCollection()
        {
            InspectorManager.GetInstance().PropertyValueChangedEvent += OnPropertyValueChanged;
        }

        public override void InspectObject(object Object, object Tag)
        {
            _MaterialParameterCollection = Object as MaterialParameterCollection;

            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.SetEnable(true);
            _Panel.SetTagString1(_MaterialParameterCollection.Path);
            ThumbnailHelper.GetInstance().EnableThumbnail(_Panel);
            _SelfContainer.AddChild(_Panel);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetFontSize(16);
            _LabelName.SetText(PathHelper.GetNameOfPath(_MaterialParameterCollection.Path) + "(MPC)");
            _SelfContainer.AddChild(_LabelName);

            base.InspectObject(_MaterialParameterCollection._MaterialParameterInfo, null);

            foreach (var childInspector in _ChildInspectors)
            {
                if (childInspector is Inspector_Property_List childListProperty)
                {
                    childListProperty.OnPropertyListChanged += HandleParameterListChanged;
                }
            }

        }

        private void RefreshIcon()
        {
            _Panel.SetImage(null);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _LabelName.SetPosition(SPAN_X, Height, _LabelName.CalculateTextWidth(), 16);
            Height += 18;

            if (_Panel != null)
            {
                Height += 65;
                _Panel.SetPosition(SPAN_X, Height, 64, 64);
                _SelfContainer.SetPosition(0, Y, Width, Height);
            }

            Y += Height;
            base.UpdateLayout(Width, ref Y);
        }

        public override void ModifyButtonClickedEvent(Button Sender)
        {
            GetUIManager().SetFocusControl(null);
            _MaterialParameterCollection.Save();
            _MaterialParameterCollection.Reload();
        }

        private void OnPropertyValueChanged(Inspector_Property PropertyInspector, object Object, string Name, object NewValue)
        {
            if (Object is int)
            {
                // Name is-like [0](ScalerParameter)
                (int index, string clsName) = ExtractParameterMeta(Name);
                if (index < 0 || clsName == null)
                {
                    return;
                }
                if (NewValue is ScalerParameter sp && sp.GetType().Name == clsName)
                {
                    _MaterialParameterCollection.SetScalerParameter(index, sp);
                }
                else if (NewValue is VectorParameter vp && vp.GetType().Name == clsName)
                {
                    _MaterialParameterCollection.SetVectorParameter(index, vp);
                }
            }
        }

        private Tuple<int, string> ExtractParameterMeta(string Input)
        {
            string pattern = @"\[(.*?)\]|\((.*?)\)";
            Regex regex = new Regex(pattern);
            MatchCollection matches = regex.Matches(Input);

            int index = -1;
            string clsName = null;

            foreach (Match match in matches)
            {
                if (match.Groups[1].Success)
                {
                    index = int.Parse(match.Groups[1].Value);
                }
                else
                {
                    clsName = match.Groups[2].Value;
                }
            }

            return new Tuple<int, string>(index, clsName);
        }

        private void HandleParameterListChanged(PropertyListChangeEvent Event, int Index, object Object)
        {
            switch (Event)
            {
                case PropertyListChangeEvent.Add:
                    {
                        if (Object is ScalerParameter sp)
                        {
                            _MaterialParameterCollection.AddScalerParameter(sp);
                        }
                        else if (Object is VectorParameter vp)
                        {
                            _MaterialParameterCollection.AddVectorParameter(vp);
                        }
                    }
                    break;
                case PropertyListChangeEvent.Delete:
                    {
                        if (Object is ScalerParameter sp)
                        {
                            _MaterialParameterCollection.DeleteScalerParameter(Index);
                        }
                        else if (Object is VectorParameter vp)
                        {
                            _MaterialParameterCollection.DeleteVectorParameter(Index);
                        }
                    }
                    break;
                case PropertyListChangeEvent.Clear:
                    {
                        if (Object is ScalerParameter sp)
                        {
                            _MaterialParameterCollection.ClearScalerParameter();
                        }
                        else if (Object is VectorParameter vp)
                        {
                            _MaterialParameterCollection.ClearVectorParameter();
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }
}