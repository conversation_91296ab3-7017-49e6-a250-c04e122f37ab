using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_TextureResources : Inspector_Struct_With_Property
    {
        protected TextureResource _Texture;
        protected Panel _PanelIcon;
        protected Button _ResetButton;

        public override void InspectObject(object Object, object Tag = null)
        {
            _Texture = (TextureResource)Object;

            _PanelIcon = new Panel();
            _PanelIcon.Initialize();
            _PanelIcon.SetEnable(true);
            _PanelIcon.SetTagString1(_Texture.Path);
            ThumbnailHelper.GetInstance().EnableThumbnail(_PanelIcon);
            _SelfContainer.AddChild(_PanelIcon);
            this.SetPropertyModifiedFunction(OnSettingsModified);

            base.InspectObject(_Texture.TextureResourceInfo);

            if (IsAdjustmentParameterEdit())
            {
                Inspector_Property_Struct Inspector = (Inspector_Property_Struct)this.FindChildInspectorByPropertyName("Adjustments");
                List<Inspector> InspectorList = Inspector.GetChildInspectors();
                foreach (var Valule in InspectorList)
                {
                    ((Inspector_Property_Float)Valule).SetReadOnly(true);
                }
            }

            _ResetButton = new Button();
            _ResetButton.Initialize();
            _ResetButton.SetText("Reset");
            _ResetButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ResetButton.SetFontSize(18);
            _ResetButton.SetTextOffsetY(2);
            _ResetButton.SetToolTips("Extract poses and features from animations");
            _ResetButton.ClickedEvent += OnResetButtonClicked;
            _ResetButton.SetVisible(true);
            _ChildContainer.AddChild(_ResetButton);

            RefreshIcon();
        }

        void OnResetButtonClicked(Button Sender)
        {
            TextureImportSetting setting = new TextureImportSetting();
            InspectorInfoTransformImportInfo(_Texture.TextureResourceInfo, out setting);
            string EditorFilename = EditorUtilities.StandardFilenameToEditorFilename(_Texture.Path);
            if (_Texture.TextureResourceInfo.MipmapGenerateSetting != MipmapGenerateSetting.Initial)
            {
                _Texture.TextureResourceInfo.MipmapGenerateSetting = MipmapGenerateSetting.Initial;
                AssetImporterManager.Instance().GenerateSharpenedMip(EditorFilename, setting, _Texture.TextureResourceInfo);
            }

            //add get Width/Height interface and son on
            if (_Texture.TextureResourceInfo.TextureSize != TextureSize.None)
            {
                _Texture.TextureResourceInfo.TextureSize = TextureSize.None;
            }
            if (!IsAdjustmentParameterEdit() && !IsAdjustmentParameterDefault())
            {
                _Texture.TextureResourceInfo.Adjustments.Brightness = 1.0f;
                _Texture.TextureResourceInfo.Adjustments.BrightnessCurve = 1.0f;
                _Texture.TextureResourceInfo.Adjustments.Vibrance = 1.0f;
                _Texture.TextureResourceInfo.Adjustments.Saturation = 1.0f;
                _Texture.TextureResourceInfo.Adjustments.Hue = 0.0f;
                _Texture.TextureResourceInfo.Adjustments.MinAlpha = 0.0f;
                _Texture.TextureResourceInfo.Adjustments.MaxAlpha = 1.0f;

                AssetImporterManager.Instance().UpdateTextureAsset(EditorFilename, setting, _Texture.TextureResourceInfo);
            }
            ResourceManager.Instance().ImmediateReloadResource(EditorFilename);
            RefreshEditorUIAndInspector();
        }

        private void OnSettingsModified(object PropertyOwner, PropertyInfo Property)
        {
            if (!IsAdjustmentParameterEdit() || Property.Name == "TextureGroup")
            {
                TextureImportSetting setting;
                InspectorInfoTransformImportInfo(_Texture.TextureResourceInfo, out setting);
                string EditorFilename = EditorUtilities.StandardFilenameToEditorFilename(_Texture.Path);
                if (IsMipmapGenerateSetting())
                {
                    if (AssetImporterManager.Instance().GetTextureAssetRawData(EditorFilename, _Texture.TextureResourceInfo))
                    {
                        AssetImporterManager.Instance().GenerateSharpenedMip(EditorFilename, setting, _Texture.TextureResourceInfo);
                    }
                    else
                    {
                        CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Texture Modify Info", "The texture does not contain original data, Import the texture again.");
                    }
                }

                if (setting.ColorSpace == ImportColorSpace.SRGB && setting.Compression == TextureCompression.BC5)
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Texture Modify Info", "The BC5 have not SRGB.");
                    return;
                }

                if (_Texture.TextureResourceInfo.TextureSize != TextureSize.None && !IsPowerOfTwo((int)_Texture.TextureResourceInfo.Width) && !IsPowerOfTwo((int)_Texture.TextureResourceInfo.Height))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Texture Modify Info", "Texture Size cannot be changed for this texture as it is a non power of two size..");
                    return;
                }

                AssetImporterManager.Instance().UpdateTextureAsset(EditorFilename, setting, _Texture.TextureResourceInfo);
                ResourceManager.Instance().ImmediateReloadResource(EditorFilename);
                RefreshEditorUIAndInspector();
            }
            else
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Texture Modify Info", "The texture does not contain original data, Import the texture again.");
            }
        }

        private void RefreshIcon()
        {
            _PanelIcon.SetImage(null);
            ProjectUI.GetInstance().RefreshListView(false);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _PanelIcon.SetPosition((Width - 256) / 2, Height + SPAN_Y, 256, 256);
            Height += 256 + 2 * SPAN_Y;
            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height;

            base.UpdateLayout(Width, ref Y);

            int Y1 = _ChildContainer.GetHeight();
            if (_ResetButton != null)
            {
                _ResetButton.SetPosition(SPAN_X, Y1 + 5, Width - SPAN_X * 2, 20);
                Y1 += 20 + 5;
                _ChildContainer.SetHeight(Y1);
                Y += 20 + 5;
            }
        }

        public bool IsAdjustmentParameterEdit()
        {
            bool flag = (
            _Texture.TextureResourceInfo.Adjustments.Brightness == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.BrightnessCurve == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.Vibrance == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.Saturation == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.Hue == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.MinAlpha == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.MaxAlpha == 0.0f);

            return flag;
        }

        public bool IsAdjustmentParameterDefault()
        {
            bool flag = (
            _Texture.TextureResourceInfo.Adjustments.Brightness == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.BrightnessCurve == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.Vibrance == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.Saturation == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.Hue == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.MinAlpha == 0.0f &&
            _Texture.TextureResourceInfo.Adjustments.MaxAlpha == 0.0f);

            return flag;
        }

        public bool IsMipmapGenerateSetting()
        {
            return _Texture.TextureResourceInfo.MipmapGenerateSetting != MipmapGenerateSetting.Initial;
        }

        void InspectorInfoTransformImportInfo(TextureResourceInfo Info, out TextureImportSetting Setting)
        {
            Setting = new TextureImportSetting();
            Setting.ColorSpace = (ImportColorSpace)(int)_Texture.TextureResourceInfo.ColorSpace;
            Setting.Type = _Texture.TextureResourceInfo.Type;
            Setting.Compression = CompressionTransform(_Texture.TextureResourceInfo.Format);
            Setting.ImportSize = (ImportTexureSize)_Texture.TextureResourceInfo.TextureSize;
            Setting.ImportTextureGroup = (ImportTextureGroup)_Texture.TextureResourceInfo.TextureGroup;
        }

        TextureCompression CompressionTransform(TextureFormat format)
        {
            if (IsBCFormat(format))
            {
                //12 enum subtract temp value
                return (TextureCompression)(format - 12);
            }
            if (IsETCFormat(format))
            {
                return TextureCompression.ETC2;
            }
            if (IsASTCFormat(format))
            {
                return TextureCompression.ASTC;
            }
            if (IsIntFormat(format) || IsFloatFormat(format))
            {
                return TextureCompression.Uncompressed;
            }
            if (IsCompressFormat(format))
            {
                return TextureCompression.PVRTC;
            }
            if (format == TextureFormat.BASIS_UNIVERSAL)
            {
                return TextureCompression.CompressedBasisHQ;
                //return TextureCompression.CompressedBasisLQ;
            }
            return TextureCompression.Uncompressed;
        }

        bool IsBCFormat(TextureFormat fmt)
        {
            return fmt >= TextureFormat.BC1 && fmt <= TextureFormat.BC7;
        }

        bool IsETCFormat(TextureFormat fmt)
        {
            return fmt >= TextureFormat.ETC_RGB4 && fmt <= TextureFormat.ETC2_RGBA8;
        }

        bool IsASTCFormat(TextureFormat fmt)
        {
            return fmt >= TextureFormat.ASTC_4x4 && fmt <= TextureFormat.ASTC_HDR_12x12;
        }

        bool IsIntFormat(TextureFormat fmt)
        {
            return fmt >= TextureFormat.A8 && fmt <= TextureFormat.R16;
        }

        bool IsFloatFormat(TextureFormat fmt)
        {
            return fmt >= TextureFormat.RHalf && fmt <= TextureFormat.R11G11B10Float;
        }

        bool IsCompressFormat(TextureFormat fmt)
        {
            return fmt >= TextureFormat.PVRTC_RGB2 && fmt <= TextureFormat.PVRTC_RGBA4;
        }

        public bool IsPowerOfTwo(int Value)
        {
            return (Value & (Value - 1)) == 0;
        }

        public void RefreshEditorUIAndInspector()
        {
            string TexturePath = _Texture.Path;
            string AbsolutePath = EditorUtilities.StandardFilenameToEditorFilename(TexturePath);
            TextureEditorUI TextureEditorUI = new TextureEditorUI();
            DockingUI DockingUI = TextureEditorManager.GetInstance().FindDockingUIByFilePath(AbsolutePath);
            if (DockingUI != null)
            {
                Resource.Get(EditorUtilities.EditorFilenameToStandardFilename(AbsolutePath), true);
                TextureEditorUI = (TextureEditorUI)DockingUI;
            }

            TextureEditorUI.OpenTexture(AbsolutePath);

            TextureEditorUI.UpdateInspector(AbsolutePath);
            MainUI.GetInstance().ActivateDockingCard_TextureEditor(TextureEditorUI.GetDockingCard(),
                TextureEditorUI.GetDockingCard() != null ? TextureEditorUI.GetDockingCard().GetDockingBlock() : null);
        }
    }
}
