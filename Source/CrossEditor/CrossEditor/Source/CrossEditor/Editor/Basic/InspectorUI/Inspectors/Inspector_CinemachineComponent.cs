using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    internal class Inspector_MultiTypeComponent<T> : Inspector_GameComponent where T : ComponentBase, IMultiTypeComponent
    {
        protected new T _Component;

        public Inspector_MultiTypeComponent(List<Entity> Entities)
            : base(Entities)
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Component = Object as T;
            base.InspectObject(Object, Tag);
        }

        /*
         * foreach (PropertyInfo PropertyInfo in Properties)
           {
               if (IsComponentBaseProperties(PropertyInfo.Name) == false)
               {
                   PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);

                   if (PropertyInfoAttribute.Category == "")
                   {
                       if (PropertyInfoAttribute.bAutoExpandStruct)
                       {
                           _AutoExpandedStructPropertyInfo = PropertyInfo;
                           object AutoExpandStruct = PropertyInfo.GetValue(_Component);
                           AddMainStructChildInspectors(AutoExpandStruct);
                       }
                       else
                       {
                           AddPropertyInspector(PropertyInfo, _Component);
                       }
                   }
                   else
                   {
                       Categories.Add(PropertyInfoAttribute.Category);
                   }
               }
           }

           List<MethodInfo> methodInfos = new List<MethodInfo>(Type.GetMethods());
           foreach (MethodInfo methodInfo in methodInfos)
           {
               MethodInfoAttribute methodAttribute = MethodInfoAttribute.GetMethodInfoAttribute(methodInfo);
               if (methodAttribute.PropertyType == "Button")
               {
                   AddButtonInspector(methodInfo, _Component, methodAttribute);
               }
           }

           // Grouped Properties
           foreach (string Category in Categories)
           {
               Inspector_GroupedProperty inspector_GroupedProperty = new Inspector_GroupedProperty(_Component, Category);
               inspector_GroupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
               inspector_GroupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
               AddChildInspector(inspector_GroupedProperty);
               inspector_GroupedProperty.InspectObject(_Component);
           }
         *
         *
         */

        protected override void RefreshChildInspectors()
        {
            base.RefreshChildInspectors();

            var inspected_obj = _Component.GetInspectedObject();
            if (inspected_obj == null)
                return;

            HashSet<string> Categories = new HashSet<string>();
            Type type = inspected_obj.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(type);

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);

                if (PropertyInfoAttribute.Category == "")
                {
                    if (PropertyInfo.Name != "Enable")
                    {
                        AddPropertyInspector(PropertyInfo, _Component);
                    }
                }
                else if (Categories.Contains(PropertyInfoAttribute.Category))
                {
                    // do nothing
                }
                else
                {
                    Categories.Add(PropertyInfoAttribute.Category);
                    Inspector_GroupedProperty inspector_GroupedProperty = new Inspector_GroupedProperty(inspected_obj, PropertyInfoAttribute.Category);
                    inspector_GroupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                    inspector_GroupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                    AddChildInspector(inspector_GroupedProperty);
                    inspector_GroupedProperty.InspectObject(inspected_obj);
                }
            }
        }

        public override object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            _Component.GetFromRuntime();
            Type type = _Component.GetType();
            PropertyInfo PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
                return PropertyInfo.GetValue(Object);

            type = _Component.GetInspectedObject().GetType();
            PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(_Component.GetInspectedObject());
            }

            return null;
        }

        public override void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type type = Object.GetType();
            PropertyInfo PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
                _Component.SetToRuntime();
                // need refresh inspectors after setting PropertyInfo
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    GetInspectorHandler().InspectObject();
                });
            }
            else
            {
                type = _Component.GetInspectedObject().GetType();
                PropertyInfo = type.GetProperty(PropertyName);

                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(_Component.GetInspectedObject(), PropertyValue);
                    _Component.SetToRuntime();
                    // need refresh inspectors after setting PropertyInfo
                    OperationQueue.GetInstance().AddOperation(() =>
                    {
                        GetInspectorHandler().InspectObject();
                    });
                }
            }

        }

    }

}
