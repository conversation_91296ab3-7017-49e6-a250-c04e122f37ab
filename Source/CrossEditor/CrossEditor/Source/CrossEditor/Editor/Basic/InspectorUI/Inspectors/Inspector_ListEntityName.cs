using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{

    public class Inspector_ListEntityName : Inspector
    {
        List<Entity> _Entities;
        List<Check> _CheckEnable;
        List<Label> _LabelName;
        Edit _EditName;

        public Inspector_ListEntityName()
        {
            _CheckEnable = new List<Check>();
            _LabelName = new List<Label>();
        }
        public Dictionary<Entity, bool> GetChooseName()
        {
            Dictionary<Entity, bool> ret = new Dictionary<Entity, bool>();
            int count = 0;
            foreach (Entity children in _Entities)
            {
                ret.Add(children, _CheckEnable[count].GetChecked());
                count++;
            }
            return ret;
        }
        public string GetHeadName()
        {
            return _EditName.GetText();
        }
        public override void InspectObject(object Object, object Tag = null)
        {
            if (!(Object is List<Entity>))
            {
                DebugHelper.Assert(false);
            }
            _Entities = (List<Entity>)Object;
            List<string> filter = new List<string>() { "CHN_Terrain", "DirectionalLight", "EditorCamera", "GameCamera", "Cube", "Sphere", "SkyLight1", "PostProcessVolume", "A310", "Entity" };
            foreach (Entity children in _Entities)
            {
                Check CheckEnable = new Check();
                CheckEnable.Initialize();
                CheckEnable.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
                CheckEnable.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
                CheckEnable.SetAutoCheck(true);
                string name = children.GetName();
                if (filter.Contains(name))
                    CheckEnable.SetChecked(false);
                else
                    CheckEnable.SetChecked(true);
                CheckEnable.ClickedEvent += OnCheckEnableClicked;
                _SelfContainer.AddChild(CheckEnable);

                Label LabelName = new Label();
                LabelName.Initialize();
                LabelName.SetFontSize(16);
                LabelName.SetTextAlign(TextAlign.CenterLeft);
                LabelName.SetText(name);
                _SelfContainer.AddChild(LabelName);

                _LabelName.Add(LabelName);
                _CheckEnable.Add(CheckEnable);
            }

            _EditName = new Edit();
            _EditName.SetFontSize(16);
            _EditName.Initialize(EditMode.Simple_SingleLine);
            _EditName.LoadSource("");
            EditContextUI.GetInstance().RegisterEdit(_EditName);
            _EditName.TextChangedEvent += OnEditTextChanged;
            _EditName.SetText("");
            _SelfContainer.AddChild(_EditName);

        }
        void OnEditTextChanged(Control Sender)
        {
            _EditName.SetTagString1(_EditName.GetText());
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            int Index = 0;
            _EditName.SetPosition(0 + SPAN_X, Height + 7, Width, 16);
            Height += 28;
            foreach (Entity children in _Entities)
            {
                _CheckEnable[Index].SetPosition(0 + SPAN_X, 7 + Height, 14, 14);
                int LabelNameX = _CheckEnable[Index].GetEndX() + SPAN_X;
                int LabelNameWidth = Width - SPAN_X - LabelNameX;
                _LabelName[Index].SetPosition(LabelNameX + SPAN_X, 6 + Height, LabelNameWidth, 16);
                Height += 28;
                Index++;
            }


            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height;

            base.UpdateLayout(Width, ref Y);

            int Y1 = _ChildContainer.GetHeight();
            Y1 += 40;
            _ChildContainer.SetHeight(Y1);
            Y += 40;
        }
        void OnCheckEnableClicked(Check Sender)
        {
            if (Sender.GetChecked())
                Sender.SetChecked(true);
            else
                Sender.SetChecked(false);
        }

    }
}
