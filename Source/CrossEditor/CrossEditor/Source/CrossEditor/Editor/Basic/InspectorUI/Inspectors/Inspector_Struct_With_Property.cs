using CEngine;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class Inspector_Struct_With_Property : Inspector
    {
        protected object _Object;

        protected int _Indent = 0;
        public Inspector_Struct_With_Property()
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Object = Object;

            ClearChildInspectors();
            Type Type = _Object.GetType();

            // None-Grouped Properties
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            List<PropertyInfo> NoneGroupedProperties = new List<PropertyInfo>();
            HashSet<string> Categories = new HashSet<string>();
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
                // hide property in some situation
                if (PropertyInfoAttribute.ToolTips.Equals(Tag))
                {
                    continue;
                }
                if (PropertyInfoAttribute.Category == "")
                {
                    NoneGroupedProperties.Add(PropertyInfo);
                }
                else
                {
                    Categories.Add(PropertyInfoAttribute.Category);
                }
            }
            // Grouped Properties
            foreach (string Category in Categories)
            {
                Inspector_GroupedProperty Inspector_GroupedProperty = new Inspector_GroupedProperty(_Object, Category);
                Inspector_GroupedProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                Inspector_GroupedProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                Inspector_GroupedProperty.InspectObject(_Object);

                AddChildInspector(Inspector_GroupedProperty);
            }
            foreach (PropertyInfo PropertyInfo in NoneGroupedProperties)
            {
                AddPropertyInspector(PropertyInfo, _Object);
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);

                PropertyModifiedEvent?.Invoke(Object, PropertyInfo);
            }
        }

        public override void SetPropertyReadOnly(string propertyName, bool readOnly)
        {
            foreach (Inspector inspector in _ChildInspectors)
            {
                if (inspector.GetChildInspectors().Count > 0)
                {
                    inspector.SetPropertyReadOnly(propertyName, readOnly);
                }
            }
        }
        public override void Update()
        {
           foreach(var inspector in _ChildInspectors)
            {
                inspector.Update();
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
        }

        public override int GetIndent()
        {
            return _Indent;
        }

        public void SetIndent(int indent)
        {
            _Indent = indent;
        }
    }
}
