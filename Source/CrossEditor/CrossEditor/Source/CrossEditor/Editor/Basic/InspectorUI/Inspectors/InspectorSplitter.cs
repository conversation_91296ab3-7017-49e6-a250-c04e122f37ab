using EditorUI;

namespace CrossEditor
{
    public class AlignedPanel : Panel
    {
        int _LeftSpan;

        Control _LeftOne;
        Control _RightOne;

        public AlignedPanel(int LeftSpan = 0)
        {
            _LeftSpan = LeftSpan;
        }

        public void FloatToLeft(Control Control, int Span = Inspector.SPAN_X)
        {
            if (_LeftOne == null)
            {
                Control.SetX(_LeftSpan);
            }
            else
            {
                Control.SetX(_LeftOne.GetEndX() + Span);
            }

            _LeftOne = Control;
        }

        public void FloatToLeft(EditWithProgress Edit, int Span = Inspector.SPAN_X)
        {
            if (_LeftOne == null)
            {
                Edit.SetX(_LeftSpan);
            }
            else
            {
                Edit.SetX(_LeftOne.GetEndX() + Span);
            }

            _LeftOne = Edit.GetBackground();
        }

        public void FloatToRight(Control Control, int Span = Inspector.SPAN_X)
        {
            if (_RightOne == null)
            {
                Control.SetX(GetWidth() - Span - Control.GetWidth());
            }
            else
            {
                Control.SetX(_RightOne.GetX() - Span - Control.GetWidth());
            }

            _RightOne = Control;
        }

        public void PlaceAsLeft(Control Control, int Padding = 0)
        {
            if (_LeftOne == null)
            {
                Control.SetX(Padding);
            }
            else
            {
                Control.SetX(_LeftOne.GetX() + Padding);
            }
        }

        public void ClearLastLayout()
        {
            _LeftOne = null;
            _RightOne = null;
        }
    }

    public class InspectorSplitter : HSplitter
    {
        static float ContentRatio = 0.618f;
        static int INTERVAL = 2;
        static int LEFT_SPAN = 14;

        Inspector _Owner;

        public InspectorSplitter(Inspector Owner)
        {
            _Owner = Owner;
            SetBackgroundColor(Color.EDITOR_UI_SEPARATOR);

            Panel LeftPanel = new Panel();
            AddChild(LeftPanel);
            LeftPanel.SetPos(0, 0);
            LeftPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            LeftPanel.PositionChangedEvent += (Sender, bPositionChanged, bSizeChanged) =>
            {
                if (!bPositionChanged && bSizeChanged)
                {
                    ContentRatio = 1 - (Sender.GetWidth() * 1.0f) / (GetWidth() - INTERVAL);
                    _Owner.GetInspectorHandler()?.UpdateLayout();
                }
            };

            AlignedPanel RightPanel = new AlignedPanel(LEFT_SPAN);
            RightPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            AddChild(RightPanel);
        }

        public static float GetRatio() => ContentRatio;

        public void UpdateLayout(int Width, int Height)
        {
            SetSize(Width, Height);
            int RightWidth = (int)((Width - INTERVAL) * ContentRatio);
            int LeftWidth = Width - INTERVAL - RightWidth;
            GetLeftControl().SetPosition(0, 0, LeftWidth, Height);
            GetRightControl().SetPosition(LeftWidth + INTERVAL, 0, RightWidth, Height);

            (GetRightControl() as AlignedPanel).ClearLastLayout();
        }

        public override void OnMouseMove(int MouseX, int MouseY, ref bool bContinue)
        {
            base.OnMouseMove(MouseX, MouseY, ref bContinue);
            if (bContinue == false)
            {
                int ActualInterval = GetRightControl().GetX() - GetLeftControl().GetEndX();
                int Delta = ActualInterval - INTERVAL;
                if (Delta != 0)
                {
                    GetRightControl().SetX(GetRightControl().GetX() - Delta);
                    GetRightControl().SetWidth(GetRightControl().GetWidth() + Delta);
                }
            }
        }
    }
}
