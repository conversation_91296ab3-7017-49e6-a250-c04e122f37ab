using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Code : Inspector_Property
    {
        protected Edit _EditValue;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _EditValue = new Edit();
            _EditValue.SetFontSize(PROPERTY_FONT_SIZE);
            _EditValue.Initialize(EditMode.Code_Txt);
            _EditValue.LoadSource("");
            _EditValue.LeftMouseDoubleClickedEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                var textUI = new CodeEditDialog();
                textUI.Initialize(GetUIManager(), "HLSL Editor", _EditValue.GetText());
                textUI._OnContentChanged += code =>
                {
                    _EditValue.SetText(code);
                    RecordAndWriteValue();
                };
                DialogUIManager.GetInstance().ShowDialogUI(textUI);
            };

            EditContextUI.GetInstance().RegisterEdit(_EditValue);
            _EditValue.CharInputEvent += OnEditCharInput;
            _EditValue.SelfFocusChangedEvent += OnEditFocusChanged;
            Container.AddChild(_EditValue);

            if (_ObjectProperty.ReadOnly)
            {
                _EditValue.SetReadOnly(true);
                _EditValue.SetBackgroundColor(Color.EDITOR_UI_GRAY_DRAW_COLOR);
            }
            ReadValue();
        }

        public int GetEditValueWidth()
        {
            return 400;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = PROPERTY_FONT_SIZE * 10;
            InnerUpdateLayout(Width, Height, ref Y);

            int EditValueWidth = Math.Max(GetEditValueWidth(), DEFAULT_WIDTH);
            EditValueWidth = Math.Min(EditValueWidth, GetValueWidth() - 2 * SPAN_X);
            _EditValue.SetPosition(0, SPAN_Y, EditValueWidth, Height);
            GetValueContainer().FloatToLeft(_EditValue);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = "";
            if (PropertyValue != null)
            {
                if (_ObjectProperty.Type == typeof(UniqueString))
                {
                    UniqueString UniStr = (UniqueString)PropertyValue;
                    PropertyValueString = UniStr.GetCString();
                }
                else
                {
                    PropertyValueString = PropertyValue.ToString();
                }
            }
            else
            {
                PropertyValueString = "<null>";
            }
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                PropertyValueString = MULTIPLE_VALUES_STRING;
            }
            _EditValue.SetText(PropertyValueString);
            _EditValue.SetTagString1(PropertyValueString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetText();
            object NewValue = null;
            Type Type = _ObjectProperty.Type;
            if (Type == typeof(string))
            {
                NewValue = ValueString;
            }
            else if (Type == typeof(UniqueString))
            {
                NewValue = new UniqueString(ValueString);
            }
            else
            {
                NewValue = MathHelper.ParseNumber(ValueString, Type);
                if (NewValue == null)
                {
                    return;
                }
            }
            SetPropertyValue(NewValue);
            GetInspectorHandler().UpdateLayout();
        }

        void OnEditCharInput(Control Sender, char Char, ref bool bContinue)
        {
            if (_EditValue.GetReadOnly())
                return;

            _EditValue.SetTagString1(_EditValue.GetText());
            GetInspectorHandler().UpdateLayout();

            if (Char == '\r' || Char == '\n')
            {
                //GetUIManager().SetFocusControl(null);
            }
        }

        void OnEditFocusChanged(Control Sender)
        {
            if (_EditValue.GetReadOnly())
                return;

            if (Sender.IsFocused() == false)
            {
                RecordAndWriteValue();
            }
        }
    }
}
