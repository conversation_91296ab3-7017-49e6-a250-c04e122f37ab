using CEngine;
using System;
using System.Reflection;

namespace CrossEditor
{
    public delegate void OnAttributeChanged(Object Sender, string PropertyName, object PropertyValue);

    public class Inspector_Struct : Inspector
    {
        object _Object;

        public event OnAttributeChanged AttributeChangedEvent;

        public Inspector_Struct()
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _Object = Object;

            Type Type = _Object.GetType();
            FieldInfo[] FieldInfos = Type.GetFields();
            foreach (FieldInfo FieldInfo in FieldInfos)
            {
                Type FieldType = FieldInfo.FieldType;
                string FiledTypeString = FieldType.ToString();
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(FieldInfo);
                if (PropertyInfoAttribute.PropertyType != "")
                {
                    FiledTypeString = PropertyInfoAttribute.PropertyType;
                }
                bool bIsEnum = FieldType.IsEnum;
                ObjectProperty ObjectProperty = new ObjectProperty();
                ObjectProperty.Object = _Object;
                ObjectProperty.Name = FieldInfo.Name;
                ObjectProperty.Type = FieldType;
                ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
                ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
                ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
                ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
                ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
                ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
                ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;
                ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                if (FiledTypeString == "List")
                {
                    ObjectProperty.Name = FieldInfo.Name + "11";
                }
                Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(FiledTypeString, bIsEnum);
                Inspector_Property.InspectProperty(ObjectProperty);
                AddChildInspector(Inspector_Property);
            }
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            FieldInfo FieldInfo = Type.GetField(PropertyName);
            return FieldInfo.GetValue(Object);
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            FieldInfo FieldInfo = Type.GetField(PropertyName);
            FieldInfo.SetValue(Object, PropertyValue);
            if (AttributeChangedEvent != null)
            {
                AttributeChangedEvent(this, PropertyName, PropertyValue);
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
        }

        public override int GetIndent()
        {
            return 0;
        }
    }
}
