using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_Property_InstanceMemeberData : Inspector_Property
    {
        InstanceMemberData mInstanceMemberData;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);

            mInstanceMemberData = ObjectProperty.GetPropertyValueFunction(ObjectProperty.Object, ObjectProperty.Name, null) as InstanceMemberData;

            AddChildInspectors();
        }

        protected virtual void AddChildInspectors()
        {
            Type Type = mInstanceMemberData.GetType();
            ClearChildInspectors();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);

            AddPropertyInspector(Properties.Find((PropertyInfo info) => { return info.Name == "Name"; }), mInstanceMemberData);
            AddPropertyInspector(Properties.Find((PropertyInfo info) => { return info.Name == "MemberType"; }), mInstanceMemberData);
            AddPropertyInspector(Properties.Find((PropertyInfo info) => { return info.Name == "Data"; }), mInstanceMemberData, "List");
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Object = _ObjectProperty.GetPropertyValueFunction(_Object, _ObjectProperty.Name, null);

            if (Object == null) return null;
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                // ReInspect
                if (PropertyName == "MemberType")
                {
                    OperationQueue.GetInstance().AddOperation(() =>
                    {
                        GetInspectorHandler().InspectObject();
                        GetInspectorHandler().UpdateLayout();
                    });

                    GetUIManager().SetFocusControl(null);
                }

                PropertyInfo.SetValue(Object, PropertyValue);
                WriteValue();
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public override void WriteValue()
        {
            base.WriteValue();
            SetPropertyValue(mInstanceMemberData);
        }
    }
}
