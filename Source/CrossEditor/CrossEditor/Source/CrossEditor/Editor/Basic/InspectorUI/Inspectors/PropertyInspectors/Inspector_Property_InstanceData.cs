using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_Property_InstanceData : Inspector_Property
    {
        InstanceData mInstanceData;
        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);

            mInstanceData = ObjectProperty.GetPropertyValueFunction(ObjectProperty.Object, ObjectProperty.Name, null) as InstanceData;
            //mInstanceData.UpdateInstanceDataFromResource();

            AddChildInspectors();
        }

        protected virtual void AddChildInspectors()
        {
            Type Type = mInstanceData.GetType();
            ClearChildInspectors();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);

            AddPropertyInspector(Properties.Find((PropertyInfo info) => { return info.Name == "InstanceDataResourcePath"; }), mInstanceData);

            if (mInstanceData.InstanceDataResourcePath != null && mInstanceData.InstanceDataResourcePath.Length != 0)
            {
                AddPropertyInspector(Properties.Find((PropertyInfo info) => { return info.Name == "InstanceCount"; }), mInstanceData);
                AddPropertyInspector(Properties.Find((PropertyInfo info) => { return info.Name == "InstanceTransformData"; }), mInstanceData);
                AddPropertyInspector(Properties.Find((PropertyInfo info) => { return info.Name == "InstanceMemberDatas"; }), mInstanceData);
            }
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Object = _ObjectProperty.GetPropertyValueFunction(_Object, _ObjectProperty.Name, null);

            if (Object == null) return null;
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);

                // RefreshUI
                if (PropertyName == "InstanceCount" || PropertyName == "InstanceDataResourcePath")
                {
                    OperationQueue.GetInstance().AddOperation(() =>
                    {
                        GetInspectorHandler().InspectObject();
                        GetInspectorHandler().UpdateLayout();
                    });
                }

                WriteValue();
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public override void WriteValue()
        {
            base.WriteValue();
            SetPropertyValue(mInstanceData);
        }
    }
}
