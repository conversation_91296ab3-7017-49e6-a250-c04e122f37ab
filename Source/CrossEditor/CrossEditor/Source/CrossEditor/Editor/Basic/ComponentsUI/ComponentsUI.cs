using EditorUI;

namespace CrossEditor
{
    class ComponentsUI : DockingUI
    {
        static ComponentsUI _Instance = new ComponentsUI();

        Panel _Panel;

        public static ComponentsUI GetInstance()
        {
            return _Instance;
        }

        public bool Initialize()
        {
            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.SetBackgroundColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);

            base.Initialize("Components", _Panel);

            return true;
        }

        public void OnDeviceActivate(bool bActivated)
        {
        }
    }
}
