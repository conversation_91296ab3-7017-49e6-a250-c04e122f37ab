using EditorUI;

namespace CrossEditor
{
    public delegate void CreateDataAssetUIInputedEventHandler(CreateDataAssetUI Sender);

    public class CreateDataAssetUI : DialogUI
    {
        Label _LabelTips;
        Edit _EditShader;
        Button _ButtonBrowse;

        public event CreateDataAssetUIInputedEventHandler InputedEvent;

        public void InitializeFromWorkflow(UIManager UIManager, string Title, string workflow)
        {
            Initialize(UIManager, Title);
            _EditShader.LoadSource(workflow);
            _LabelTips.SetText("Base Workflow:");
        }
        void Initialize(UIManager UIManager, string Title)
        {
            base.Initialize(UIManager, Title, 600, 200);

            _LabelTips = new Label();
            _LabelTips.SetPosition(30, 80, 500, 16);
            _LabelTips.SetFontSize(16);
            _LabelTips.SetTextAlign(TextAlign.CenterLeft);
            _PanelDialog.AddChild(_LabelTips);

            _EditShader = new Edit();
            _EditShader.SetFontSize(14);
            _EditShader.Initialize(EditMode.Simple_SingleLine);
            _PanelDialog.AddChild(_EditShader);
            EditContextUI.GetInstance().RegisterEdit(_EditShader);
            _EditShader.SetPosition(30, 100, 500, 14);

            _ButtonBrowse = new Button();
            _ButtonBrowse.Initialize();
            _ButtonBrowse.SetFontSize(12);
            _ButtonBrowse.SetText("...");
            _ButtonBrowse.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonBrowse.SetToolTips("Browse Workflow");
            _ButtonBrowse.ClickedEvent += OnButtonBrowseClicked;
            _PanelDialog.AddChild(_ButtonBrowse);
            _ButtonBrowse.SetPosition(540, 100, 24, 16);

            Button ButtonOK = new Button();
            ButtonOK.Initialize();
            ButtonOK.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonOK.SetText("OK");
            ButtonOK.SetFontSize(16);
            ButtonOK.SetTextOffsetY(3);
            ButtonOK.ClickedEvent += OnButtonOKClicked;
            _PanelDialog.AddChild(ButtonOK);

            ButtonOK.SetSize(100, 24);
            ButtonOK.MakeCenterX();
            ButtonOK.SetY(150);
        }

        void OnButtonBrowseClicked(Button Sender)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Workflow File";
            PathInputUIFilterItem.Extensions.Add("flow");

            bool bContentsOnly = true;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.Initialize(GetUIManager(), "", PathInputUIType.OpenFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                string EditorFilename = PathInputed;
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(EditorFilename);
                _EditShader.SetText(StandardFilename);
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        void OnButtonOKClicked(Button Sender)
        {
            if (_EditShader == null)
            {
                return;
            }
            string File = _EditShader.GetText();
            File = EditorUtilities.EditorFilenameToStandardFilename(File);
            string Shader1 = EditorUtilities.StandardFilenameToEditorFilename(File);
            if (FileHelper.IsFileExists(Shader1) == false)
            {
                string Tips = string.Format("File: {0} not exists.", File);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string Extension = PathHelper.GetExtension(Shader1);
            if (StringHelper.IgnoreCaseEqual(Extension, ".flow") == false)
            {
                string Tips = string.Format("File: {0} is not a flow file.", File);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            CloseDialog();
            if (InputedEvent != null)
            {
                InputedEvent(this);
            }
        }
    }
}