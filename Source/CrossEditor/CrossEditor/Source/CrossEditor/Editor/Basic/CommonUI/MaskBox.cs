using System.Collections.Generic;
using System.Text;

namespace EditorUI
{
    public delegate void MaskBoxMaskChangedEventHandler(MaskBox Sender);

    public class MaskBoxItem
    {
        public string _Text;
        public uint _Mask;

        public MaskBoxItem(string Text, uint Mask)
        {
            _Text = Text;
            _Mask = Mask;
        }
    }

    public class MaskBox : Control
    {
        uint _MaskValue;
        Edit _EditValue;
        Button _ButtonDropDown;
        List<MaskBoxItem> _ItemList;
        Texture _TextureCheckedMark;

        public event MaskBoxMaskChangedEventHandler MaskChangedEvent;

        public MaskBox()
        {
            _MaskValue = 0;
            _ItemList = new List<MaskBoxItem>();
        }

        public override void Initialize()
        {
            int FontSize = 14;

            _EditValue = UIHelper.GetInstance().CreateEditBox(this, "", FontSize);
            _EditValue.SetPosition(0, 1, 300, FontSize);
            _EditValue.SetReadOnly(true);
            _EditValue.SetEnable(false);

            _ButtonDropDown = UIHelper.GetInstance().CreateButton_Image(this, "Editor/Others/ButtonDropDown.png");
            _ButtonDropDown.ClickedEvent += OnButtonDropDownClicked;

            _TextureCheckedMark = UIManager.LoadUIImage("Editor/Icons/Special/CheckedMark.png");
        }

        public Edit GetValueEdit()
        {
            return _EditValue;
        }

        public override void OnPositionChanged(bool bPositionChanged, bool bSizeChanged)
        {
            base.OnPositionChanged(bPositionChanged, bSizeChanged);

            int ButtonDropDownWidth = _ButtonDropDown.GetWidth();
            int EditValueWidth = GetWidth() - ButtonDropDownWidth;
            _EditValue.SetWidth(EditValueWidth);
            _ButtonDropDown.SetX(_EditValue.GetEndX());
        }

        public void SetMaskValue(uint MaskValue)
        {
            _MaskValue = MaskValue;
            UpdateEditValueText();
        }

        void UpdateEditValueText()
        {
            StringBuilder StringBuilder = new StringBuilder();
            bool bFirst = true;
            int Count = _ItemList.Count;
            for (int i = 0; i < Count; i++)
            {
                MaskBoxItem MaskBoxItem = _ItemList[i];
                if ((_MaskValue & MaskBoxItem._Mask) != 0)
                {
                    if (!bFirst)
                    {
                        StringBuilder.Append("|");
                    }
                    StringBuilder.Append(MaskBoxItem._Text);
                    bFirst = false;
                }
            }
            _EditValue.SetText(StringBuilder.ToString());
        }

        public uint GetMaskValue()
        {
            return _MaskValue;
        }

        public void AddItem(string Text, uint Mask)
        {
            _ItemList.Add(new MaskBoxItem(Text, Mask));
        }

        public void ClearItems()
        {
            _ItemList.Clear();
        }

        void OnButtonDropDownClicked(Button Sender)
        {
            int Width = GetWidth();

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.SetMinWidth(Width);
            MenuContextMenu.Initialize();

            int Count = _ItemList.Count;
            int i;
            for (i = 0; i < Count; i++)
            {
                MaskBoxItem MaskBoxItem = _ItemList[i];
                MenuItem MenuItem_Item = new MenuItem();
                MenuItem_Item.SetText(MaskBoxItem._Text);
                if ((_MaskValue & MaskBoxItem._Mask) != 0)
                {
                    MenuItem_Item.SetImage(_TextureCheckedMark);
                }
                MenuItem_Item.SetTagString(MaskBoxItem._Mask.ToString());
                MenuItem_Item.ClickedEvent += OnMenuItemItemXClicked;
                MenuContextMenu.AddMenuItem(MenuItem_Item);
            }

            bool bHCentered = true;
            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, this, bHCentered);
        }

        public void TriggerMaskChangedEvent()
        {
            if (MaskChangedEvent != null)
            {
                MaskChangedEvent(this);
            }
        }

        void OnMenuItemItemXClicked(MenuItem MenuItem_Item)
        {
            string MaskString = MenuItem_Item.GetTagString();
            uint Mask = MathHelper.ParseUInt(MaskString);
            _MaskValue ^= Mask;
            UpdateEditValueText();
            TriggerMaskChangedEvent();
        }
    }
}
