using EditorUI;

namespace CrossEditor
{
    class DockContainerUI : DockingUI
    {
        protected DockingControl _Control;
        protected DockingBlock _Block;

        public DockContainerUI()
        {

        }

        public void Initialize(string Name)
        {
            _Block = new DockingBlock();
            _Block.Initialize();

            _Control = new DockingControl();
            _Control.Initialize();
            _Control.AddChild(_Block);
            _Control.PositionChangedEvent += OnPositionChangedEvent;

            base.Initialize(Name, _Control);
        }

        private void OnPositionChangedEvent(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            _Block.SetSize(Sender.GetWidth(), Sender.GetHeight());
        }

        public void AddDockingCard(DockingCard Card)
        {
            _Block.AddDockingCard(Card);
        }

        public DockingCard GetActiveCard()
        {
            return _Block.GetActiveCard();
        }
    }
}
