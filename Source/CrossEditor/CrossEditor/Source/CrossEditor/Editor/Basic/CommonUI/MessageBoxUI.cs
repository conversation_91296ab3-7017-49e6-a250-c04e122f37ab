using EditorUI;

namespace CrossEditor
{
    class MessageBoxUI : DialogUI
    {
        Label _LabelContent;

        public void Initialize(UIManager UIManager, string title, string msg)
        {
            base.Initialize(UIManager, title, 910, 300);

            _LabelContent = new Label();
            _LabelContent.SetPosition(120, 100, 710, 200);
            _LabelContent.SetMultiLine(true);
            _LabelContent.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelContent.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _LabelContent.SetText(msg);
            _PanelDialog.AddChild(_LabelContent);
        }
    }
}
