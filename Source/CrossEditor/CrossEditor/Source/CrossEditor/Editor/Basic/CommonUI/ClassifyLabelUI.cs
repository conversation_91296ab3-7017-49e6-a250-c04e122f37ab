using CEngine;
using Editor<PERSON>;
using System.Collections.Generic;

namespace CrossEditor
{
    public class ClassifyLabelUI
    {
        public const int BAR_HEIGHT = 24;
        public const int COLOR_WIDTH = 5;
        static int _Span = 2;

        public static Dictionary<ClassIDType, string> ClassifyTypeMap = new Dictionary<ClassIDType, string>
        {
            { ClassIDType.CLASS_World, "World" },
            { ClassIDType.CLASS_Material, "Material" },
            { ClassIDType.CLASS_Texture, "Texture" },
            { ClassIDType.CLASS_Texture2D, "Texture" },
            { ClassIDType.CLASS_Texture2DArray, "Texture" },
            { ClassIDType.CLASS_TextureCube, "Texture" },
            { ClassIDType.CLASS_Texture3D, "Texture" },
            { ClassIDType.CLASS_MeshAssetDataResource, "Mesh" },
            { ClassIDType.CLASS_Fx, "Fx" },
            { ClassIDType.CLASS_ComputeShader, "Shader" },
            { ClassIDType.CLASS_GraphicsShader, "Shader" },
            { ClassIDType.CLASS_PrefabResource, "Prefab" },
            { ClassIDType.CLASS_AnimatorRes, "Anim" },
            { ClassIDType.CLASS_AnimatrixRes, "Anim" },
            { ClassIDType.CLASS_AnimBlendSpaceRes, "Anim" },
            { ClassIDType.CLASS_AnimCompositeRes, "Anim" },
            { ClassIDType.CLASS_AnimSequenceRes, "Anim" },
            { ClassIDType.CLASS_SkeletonResource, "Skeleton" },
            { ClassIDType.CLASS_SkeletonPhysicsResource, "Skeleton" },
            { ClassIDType.CLASS_ParticleSystemResource, "ParticleSystem" },
            { ClassIDType.CLASS_ParticleEmitterResource, "ParticleEmitter" },
            { ClassIDType.CLASS_CurveControllerRes, "Sequencer" },
        };

        static Color DefaultColor = Color.EDITOR_UI_GRAY_TEXT_COLOR;
        static Dictionary<string, Color> ClassifyColorMap = new Dictionary<string, Color>
        {
            { "World", new Color(0.75f, 0.25f, 0.25f, 1.0f) },
            { "Material", new Color(0, 1.0f, 0, 1.0f) },
            { "Texture", new Color(1.0f, 0, 0, 1.0f) },
            { "Mesh", new Color(0, 0, 1.0f, 1.0f) },
            { "Fx", new Color(1.0f, 1.0f, 1.0f, 1.0f) },
            { "Shader", new Color(0.0f, 1.0f, 1.0f, 1.0f) },
            { "Prefab", new Color(0.6f, 0.4f, 0.6f, 1.0f) },
            { "Anim", new Color(0.6f, 0.7f, 0.2f, 1.0f) },
            { "Skeleton", new Color(1.0f, 0.6f, 0.0f, 1.0f) },
            { "ParticleSystem", new Color(0.9f, 0.0f, 0.0f, 1.0f) },
            { "ParticleEmitter", new Color(1.0f, 0.5f, 0.25f, 1.0f) },
            { "Sequencer", new Color(0.31f, 0.48f, 0.28f, 1.0f) },
            { "Audio", new Color(0.25f, 0.25f, 1.0f, 1.0f) },
            { "Other", Color.EDITOR_UI_GRAY_TEXT_COLOR }
        };

        static Dictionary<string, ClassIDType> NullTypeMatchMap = new Dictionary<string, ClassIDType>
        {
            {".world", ClassIDType.CLASS_World},
            {".shader", ClassIDType.CLASS_GraphicsShader }
        };

        Label _LabelLeft;
        Label _LabelRight;
        Panel _PanelSingle;
        Color _Color;
        bool _bEnable;

        public List<ClassIDType> _ClassIDList = new List<ClassIDType>();


        public delegate void LeftMouseDownEventHandler(ClassifyLabelUI Sender);
        public LeftMouseDownEventHandler LeftMouseDownEvent;

        public ClassifyLabelUI()
        {
            _bEnable = true;
        }

        public void Initialize(string Text, List<ClassIDType> ClassIDList = null)
        {
            _LabelLeft = new Label();
            _LabelLeft.SetSize(COLOR_WIDTH, BAR_HEIGHT - 8);
            _LabelLeft.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            if (!ClassifyColorMap.TryGetValue(Text, out _Color))
            {
                _Color = DefaultColor;
            }

            _LabelRight = new Label();
            _LabelRight.Initialize();
            _LabelRight.SetText(Text);
            _LabelRight.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelRight.SetPosition(7, 3, _LabelRight.CalculateTextWidth(), 14);

            _PanelSingle = new Panel();
            _PanelSingle.SetPosition(0, 4, COLOR_WIDTH + 2 + _LabelRight.CalculateTextWidth(), BAR_HEIGHT - 8);
            _PanelSingle.AddChild(_LabelLeft);
            _PanelSingle.AddChild(_LabelRight);
            _PanelSingle.LeftMouseDownEvent += PanelSingleLeftMouseDownEvent;
            _PanelSingle.LeftMouseUpEvent += PanelSingleLeftMouseUpEvent;

            _ClassIDList = ClassIDList;
        }

        private void PanelSingleLeftMouseUpEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (!_bEnable)
            {
                return;
            }

            Sender.ReleaseMouse();
            _LabelRight.SetY(3);
        }

        private void PanelSingleLeftMouseDownEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (!_bEnable)
            {
                return;
            }

            Sender.CaptureMouse();
            LeftMouseDownEvent?.Invoke(this);
            if (_LabelLeft.GetBackgroundColor() == _Color)
            {
                _LabelLeft.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            }
            else
            {
                _LabelLeft.SetBackgroundColor(_Color);
            }
            _LabelRight.SetY(3 + _Span);
        }

        public Panel GetPanel()
        {
            return _PanelSingle;
        }

        public void RestoreColor()
        {
            _LabelLeft.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
        }

        public Label GetTextLabel()
        {
            return _LabelRight;
        }

        public void SetEnable(bool bEnable)
        {
            _bEnable = bEnable;
            if (!_bEnable)
            {
                _LabelRight.SetTextColor(Color.FromRGB(128, 128, 128));
            }
            else
            {
                _LabelRight.SetTextColor(Color.White);
            }
        }

        public bool GetEnable()
        {
            return _bEnable;
        }

        public bool IsFileMatchClassIDType(string FilePath, string Extension)
        {
            ClassIDType FileClassIDType = ResourceTypeCache.GetInstance().GetResourceType_Cache(FilePath);
            if (FileClassIDType == ClassIDType.CLASS_NullType)
            {
                foreach (var Value in NullTypeMatchMap)
                {
                    if (Extension.Equals(Value.Key))
                    {
                        FileClassIDType = Value.Value;
                        break;
                    }
                }
            }
            return _ClassIDList.Contains(FileClassIDType);
        }

        public static Color GetClassificationColor(ClassIDType ClassIDType)
        {
            Color Color = DefaultColor;
            string Label;
            if (ClassifyTypeMap.TryGetValue(ClassIDType, out Label))
            {
                if (!ClassifyColorMap.TryGetValue(Label, out Color))
                {
                    Color = DefaultColor;
                }
            }
            return Color;
        }
    }
}
