using EditorUI;
using System;

namespace CrossEditor
{
    public class CodeEditDialog : DialogUI
    {
        Edit _Edit;

        public Action<string> _OnContentChanged;

        public void Initialize(UIManager UIManager, string Title, string content)
        {
            int width = 1200;
            int height = 800;
            int marginX = 15;
            int marginTop = 40;
            int marginBottom = 15;

            base.Initialize(UIManager, Title, width, height);

            _Edit = new Edit();
            _Edit.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _Edit.Initialize(EditMode.Code_Txt);
            _Edit.LoadSource("");
            _Edit.SetText(content);
            EditContextUI.GetInstance().RegisterEdit(_Edit);

            _PanelDialog.AddChild(_Edit);
            _Edit.SetPosition(marginX, marginTop, width - marginX * 2, height - marginTop - marginBottom);
        }

        public override void CloseDialog()
        {
            _OnContentChanged?.Invoke(_Edit.GetText());
            base.CloseDialog();
        }
    }
}
