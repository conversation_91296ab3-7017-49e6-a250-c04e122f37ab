using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;

namespace CrossEditor
{
    public delegate void HDRColorSelectUIColorSelectedEventHandler(HDRColorSelectUI Sender);

    public class HDRColorItem
    {
        public bool _bEnable;
        public Color _Color;
        public Button _ColorButton;
    }

    public class HDRColorSelectUI : DialogUI
    {
        const string COLORS_FILENAME = "Colors.config";
        const int MAX_COLOR = 42;

        Color _OldColor;
        Color _NewColor;
        float _VMax;

        TrackBar _TrackBarColorH;
        TrackBar _TrackBarColorS;
        TrackBar _TrackBarColorV;
        TrackBar _TrackBarColorR;
        TrackBar _TrackBarColorG;
        TrackBar _TrackBarColorB;
        TrackBar _TrackBarColorA;
        Edit _EditBoxColorH;
        Edit _EditBoxColorS;
        Edit _EditBoxColorV;
        Edit _EditBoxColorR;
        Edit _EditBoxColorG;
        Edit _EditBoxColorB;
        Edit _EditBoxColorA;
        Edit _EditBoxLinear;
        Edit _EditBox_sRGB;

        const int _Radius = 105;
        bool _bLeftMouseDown;
        Panel _ColorPicker;
        Panel _OrientationPanel;
        ComboBox _ComboBox;

        Panel _OldColorPanel;
        Panel _NewColorPanel;
        Panel _sRGBPanel;

        Button _ButtonPickColor;

        Color _SavedColor;

        Panel _ColorListMask;
        Panel _ColorListCombo;
        Panel _ColorListPanel;
        List<HDRColorItem> _ColorList;
        int _SelectedColorIndex;

        Color _ColorDragged;
        int _ItemDragged;
        int _ItemTarget;

        public event HDRColorSelectUIColorSelectedEventHandler ColorSelectedEvent;
        public event HDRColorSelectUIColorSelectedEventHandler ColorSelectingEvent;

        public HDRColorSelectUI()
        {
            _VMax = 1.0f;
            _ColorList = new List<HDRColorItem>();
            _SelectedColorIndex = 0;
        }

        public void Initialize(UIManager UIManager, string Title, Color Color)
        {
            base.Initialize(UIManager, Title, 370, 530);

            _OldColor = Color;
            _NewColor = Color;

            _NewColor.R = Math.Max(_NewColor.R, 0.0f);
            _NewColor.G = Math.Max(_NewColor.G, 0.0f);
            _NewColor.B = Math.Max(_NewColor.B, 0.0f);
            _NewColor.A = Math.Max(_NewColor.A, 0.0f);
            _VMax = Math.Max(_NewColor.R, Math.Max(_NewColor.G, _NewColor.B));
            _VMax = Math.Max(_VMax, 1.0f);

            int SpanX = 20;

            int Y = 40;
            int SpanY1 = 10;
            int SpanY2 = 20;
            int SpanY3 = 30;
            int PreviewX = 250;
            int PreviewHeight = 30;

            InitializeColorListCombo(SpanX, Y);
            InitializeButtonPickColor(325, Y);
            Y += SpanY3;

            InitializeColorPicker(SpanX, Y);
            InitializePanelOrientation();
            InitializeComboBox(PreviewX, Y);
            Y += PreviewHeight;

            InitializeOldColorPreview(PreviewX, Y);
            Y += 2 * PreviewHeight;

            InitializeNewColorPreview(PreviewX, Y);
            Y += 2 * PreviewHeight;

            InitializesRGBColorPreview(PreviewX, Y);
            Y += 2 * PreviewHeight;
            Y += SpanY1;

            int LabelX = SpanX;
            int TrackBarX = 110;
            int EditBoxX = 275;

            InitializeLabelColor(LabelX, Y, "Hue:");
            _TrackBarColorH = InitializeTrackBarColor(TrackBarX, Y);
            _EditBoxColorH = InitializeEditBoxColor(EditBoxX, Y, "1");
            Y += SpanY2;

            InitializeLabelColor(LabelX, Y, "Saturate:");
            _TrackBarColorS = InitializeTrackBarColor(TrackBarX, Y);
            _EditBoxColorS = InitializeEditBoxColor(EditBoxX, Y, "1");
            Y += SpanY2;

            InitializeLabelColor(LabelX, Y, "Value:");
            _TrackBarColorV = InitializeTrackBarColor(TrackBarX, Y);
            _EditBoxColorV = InitializeEditBoxColor(EditBoxX, Y, "1");
            Y += SpanY3;

            InitializeLabelColor(LabelX, Y, "Red:");
            _TrackBarColorR = InitializeTrackBarColor(TrackBarX, Y);
            _EditBoxColorR = InitializeEditBoxColor(EditBoxX, Y, "1");
            Y += SpanY2;

            InitializeLabelColor(LabelX, Y, "Green:");
            _TrackBarColorG = InitializeTrackBarColor(TrackBarX, Y);
            _EditBoxColorG = InitializeEditBoxColor(EditBoxX, Y, "1");
            Y += SpanY2;

            InitializeLabelColor(LabelX, Y, "Blue:");
            _TrackBarColorB = InitializeTrackBarColor(TrackBarX, Y);
            _EditBoxColorB = InitializeEditBoxColor(EditBoxX, Y, "1");
            Y += SpanY3;

            InitializeLabelColor(LabelX, Y, "Alpha:");
            _TrackBarColorA = InitializeTrackBarColor(TrackBarX, Y);
            _EditBoxColorA = InitializeEditBoxColor(EditBoxX, Y, "255");
            Y += SpanY3;

            InitializeLabelColor(LabelX, Y, "Hex Linear:");
            _EditBoxLinear = InitializeEditBoxHex(110, Y);
            _EditBoxLinear.CharInputEvent += OnEditBoxLinearCharInput;

            InitializeLabelColor(200, Y, "Hex sRGB:");
            _EditBox_sRGB = InitializeEditBoxHex(275, Y);
            _EditBox_sRGB.CharInputEvent += OnEditBox_sRGBCharInput;
            Y += SpanY3;

            ResetColorList();
            LoadColors();
            UpdateColorListUI();
            UpdateColorListThumbnail();

            SetupControlsByColor_RGBA(_NewColor);
            SetupControlsByColor_HSV(_NewColor);
            UpdatePanelColor(_NewColor);
            UpdateOrientation(_NewColor);
            UpdateHexString(_NewColor);

            EditContextUI.GetInstance().RegisterEdit(_EditBoxColorH);
            EditContextUI.GetInstance().RegisterEdit(_EditBoxColorS);
            EditContextUI.GetInstance().RegisterEdit(_EditBoxColorV);
            EditContextUI.GetInstance().RegisterEdit(_EditBoxColorR);
            EditContextUI.GetInstance().RegisterEdit(_EditBoxColorG);
            EditContextUI.GetInstance().RegisterEdit(_EditBoxColorB);
            EditContextUI.GetInstance().RegisterEdit(_EditBoxColorA);
            EditContextUI.GetInstance().RegisterEdit(_EditBoxLinear);
            EditContextUI.GetInstance().RegisterEdit(_EditBox_sRGB);

            Button ButtonOK = new Button();
            ButtonOK.Initialize();
            ButtonOK.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonOK.SetText("OK");
            ButtonOK.SetFontSize(16);
            ButtonOK.SetTextOffsetY(2);
            ButtonOK.ClickedEvent += OnButtonOKClicked;
            _PanelDialog.AddChild(ButtonOK);

            Button ButtonCancel = new Button();
            ButtonCancel.Initialize();
            ButtonCancel.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonCancel.SetText("Cancel");
            ButtonCancel.SetFontSize(16);
            ButtonCancel.SetTextOffsetY(2);
            ButtonCancel.ClickedEvent += OnButtonCancelClicked;
            _PanelDialog.AddChild(ButtonCancel);

            ButtonOK.SetSize(100, 25);
            ButtonCancel.SetSize(100, 25);
            int CenterX = _PanelDialog.GetWidth() / 2;
            ButtonOK.SetX(CenterX - 105);
            ButtonCancel.SetX(CenterX + 5);
            int ButtonY = _PanelDialog.GetHeight() - 40;
            ButtonOK.SetY(Y);
            ButtonCancel.SetY(Y);

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragBeginEvent += OnDragDropManagerDragBegin;
            DragDropManager.DragMoveEvent += OnDragDropManagerDragMove;
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
            DragDropManager.DragClearEvent += OnDragDropManagerDragClear;
            DragDropManager.DragCancelEvent += OnDragDropManagerDragCancel;
        }

        public override void CloseDialog()
        {
            CloseDialog(() =>
            {
                _NewColor = _OldColor;
                ColorSelectedEvent?.Invoke(this);
            });
        }

        public void CloseDialog(Action Func)
        {
            base.CloseDialog();

            SaveColors();

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragBeginEvent -= OnDragDropManagerDragBegin;
            DragDropManager.DragMoveEvent -= OnDragDropManagerDragMove;
            DragDropManager.DragEndEvent -= OnDragDropManagerDragEnd;
            DragDropManager.DragClearEvent -= OnDragDropManagerDragClear;
            DragDropManager.DragCancelEvent -= OnDragDropManagerDragCancel;

            Func();
        }

        public override void ShowDialog()
        {
            base.ShowDialog();
        }

        public bool GetColorModified()
        {
            return _NewColor != _OldColor;
        }

        public Color GetOldColor()
        {
            return _OldColor;
        }

        public Color GetNewColor()
        {
            return _NewColor;
        }

        void InitializeLabelColor(int X, int Y, string Text)
        {
            Label LabelColor = UIHelper.GetInstance().CreateLabel(_PanelDialog, Text, 12);
            LabelColor.SetPosition(X, Y, 75, 16);
            LabelColor.SetTextAlign(TextAlign.CenterLeft);
        }

        TrackBar InitializeTrackBarColor(int X, int Y)
        {
            TrackBar TrackBarColor = new TrackBar();
            TrackBarColor.Initialize();
            TrackBarColor.SetPosition(X, Y, 150, 14);
            TrackBarColor.SetValue(1.0f);
            TrackBarColor.ValueChangedEvent += OnTrackBarColorValueChanged;
            TrackBarColor.PaintBackBarEvent += OnTrackBarColorPaintBackBar;
            _PanelDialog.AddChild(TrackBarColor);
            return TrackBarColor;
        }

        Edit InitializeEditBoxColor(int X, int Y, string Text)
        {
            Edit EditBoxColor = UIHelper.GetInstance().CreateEditBox(_PanelDialog, Text, 12);
            EditBoxColor.SetPosition(X, Y, 75, 16);
            EditBoxColor.CharInputEvent += OnEditBoxColorCharInput;
            EditBoxColor.SelfFocusChangedEvent += OnEditBoxFocusChanged;
            return EditBoxColor;
        }

        Edit InitializeEditBoxHex(int X, int Y)
        {
            Edit EditBoxHex = UIHelper.GetInstance().CreateEditBox(_PanelDialog, "", 12);
            EditBoxHex.SetPosition(X, Y, 75, 16);
            EditBoxHex.LeftMouseDoubleClickedEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                GetDevice().SetClipboardText(Sender.GetText());
            };
            return EditBoxHex;
        }

        void InitializePanelOrientation()
        {
            _OrientationPanel = UIHelper.GetInstance().CreatePanel(_ColorPicker, "Editor/Others/Orientation.png");
            _OrientationPanel.SetSize(20, 20);
        }

        void SelectColorItemButton(Control ColorButton)
        {
            _ColorList[_SelectedColorIndex]._ColorButton.SetBorderChecked(false);

            int ColorIndex = ColorButton.GetTagInt1();
            _SelectedColorIndex = ColorIndex;
            ColorButton.SetBorderChecked(true);
        }

        public void SetSelectedColorItemColor(Color Color, bool bEnable)
        {
            HDRColorItem HDRColorItem = _ColorList[_SelectedColorIndex];
            HDRColorItem._Color = Color;
            HDRColorItem._bEnable = bEnable;
            Color ColorForDisplay = GetColorForDisplay(Color.R, Color.G, Color.B, Color.A);
            HDRColorItem._ColorButton.SetNormalColor(ColorForDisplay);
            HDRColorItem._ColorButton.SetHoverColor(ColorForDisplay);
            if (bEnable)
            {
                HDRColorItem._ColorButton.SetToolTips(GetColorToolTips(Color));
            }
            else
            {
                HDRColorItem._ColorButton.SetToolTips("");
            }
            UpdateColorListThumbnail();
        }

        void InitializeColorPicker(int X, int Y)
        {
            _ColorPicker = UIHelper.GetInstance().CreatePanel(_PanelDialog, "Editor/Others/ColorWheel.png");
            _ColorPicker.SetPosition(X, Y, _Radius * 2, _Radius * 2);
            _PanelDialog.AddChild(_ColorPicker);
            _ColorPicker.LeftMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                bContinue = false;
                _bLeftMouseDown = true;
                Sender.CaptureMouse();
                if (_ComboBox.GetSelectedItemIndex() == 0)
                {
                    UpdateColorByWheelPosition(MouseX - _ColorPicker.GetScreenX(), MouseY - _ColorPicker.GetScreenY());
                }
                else
                {
                    UpdateColorBySpectrumPosition(MouseX - _ColorPicker.GetScreenX(), MouseY - _ColorPicker.GetScreenY());
                }
            };
            _ColorPicker.LeftMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                bContinue = false;
                _bLeftMouseDown = false;
                Sender.ReleaseMouse();
            };
            _ColorPicker.MouseMoveEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                if (_bLeftMouseDown)
                {
                    if (_ComboBox.GetSelectedItemIndex() == 0)
                    {
                        UpdateColorByWheelPosition(MouseX - _ColorPicker.GetScreenX(), MouseY - _ColorPicker.GetScreenY());
                    }
                    else
                    {
                        UpdateColorBySpectrumPosition(MouseX - _ColorPicker.GetScreenX(), MouseY - _ColorPicker.GetScreenY());
                    }
                }
            };
        }

        void InitializeComboBox(int X, int Y)
        {
            _ComboBox = new ComboBox();
            _ComboBox.Initialize();
            _ComboBox.SetPosition(X, Y, 100, 20);
            _ComboBox.AddItem("Wheel");
            _ComboBox.AddItem("Spectrum");
            _ComboBox.ItemSelectedEvent += (Sender) =>
            {
                int Index = Sender.GetSelectedItemIndex();
                if (Index == 0)
                {
                    _ColorPicker.SetImage(UIManager.LoadUIImage("Editor/Others/ColorWheel.png"));
                }
                else
                {
                    _ColorPicker.SetImage(UIManager.LoadUIImage("Editor/Others/ColorSpectrum.png"));
                }
                if (_OrientationPanel != null)
                {
                    UpdateOrientation(_NewColor);
                }
            };
            _ComboBox.SetSelectedItemIndex(0);
            _PanelDialog.AddChild(_ComboBox);
        }

        void InitializeColorListCombo(int X, int Y)
        {
            _ColorListMask = UIHelper.GetInstance().CreatePanel(_PanelDialog);
            _ColorListMask.SetPosition(0, 0, 370, 530);
            _ColorListMask.SetVisible(false);

            _ColorListCombo = UIHelper.GetInstance().CreatePanel(_PanelDialog);
            _ColorListCombo.SetPosition(X, Y, 265, 16);
            _ColorListCombo.SetBackgroundColor(Color.FromRGBA(255, 255, 255, 10));

            _ColorListPanel = UIHelper.GetInstance().CreatePanel(_PanelDialog);
            _ColorListPanel.SetPosition(X, Y + 16, 290, 250);
            _ColorListPanel.SetVisible(false);
            _ColorListPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);

            Button FilterButton = UIHelper.GetInstance().CreateButton_Text(_PanelDialog, "v", 12, 1);
            FilterButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            FilterButton.SetPosition(285, Y, 25, 16);

            FilterButton.ClickedEvent += (Sender) =>
            {
                _PanelDialog.MoveToTop(_ColorListMask);
                _PanelDialog.MoveToTop(FilterButton);
                _PanelDialog.MoveToTop(_ColorListPanel);

                bool Visible = !_ColorListPanel.GetVisible();
                _ColorListMask.SetVisible(Visible);
                _ColorListPanel.SetVisible(Visible);
            };

            _ColorListMask.LeftMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                bContinue = false;
                _ColorListMask.SetVisible(false);
                _ColorListPanel.SetVisible(false);
            };

            _ColorListMask.MouseMoveEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                bContinue = false;
            };

            _ColorListPanel.LeftMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                bContinue = false;
            };

            _ButtonBackground.LeftMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                bContinue = false;
                _ColorListMask.SetVisible(false);
                _ColorListPanel.SetVisible(false);
            };
        }

        void InitializeButtonPickColor(int X, int Y)
        {
            _ButtonPickColor = UIHelper.GetInstance().CreateButton_Image(_PanelDialog, "Editor/Others/ButtonPickColor.png");
            _ButtonPickColor.LeftMouseDownEvent += OnButtonPickColorLeftMouseDown;
            _ButtonPickColor.SetPosition(X, Y, 25, 16);
        }

        void InitializeLabel(int X, int Y, string Text)
        {
            Label LabelColor = UIHelper.GetInstance().CreateLabel(_PanelDialog, Text, 12);
            LabelColor.SetPosition(X, Y, 100, 16);
            LabelColor.SetTextAlign(TextAlign.CenterLeft);
        }

        void InitializePanelGrid(int X, int Y)
        {
            Panel Grid = UIHelper.GetInstance().CreatePanel(_PanelDialog, "Editor/Others/PanelGrid.png");
            Grid.SetPosition(X, Y, 100, 30);
        }

        void InitializeOldColorPreview(int X, int Y)
        {
            InitializeLabel(X, Y, "Old Color");
            Y += 20;
            InitializePanelGrid(X, Y);
            _OldColorPanel = UIHelper.GetInstance().CreatePanel(_PanelDialog);
            _OldColorPanel.SetPosition(X, Y, 100, 30);
            _OldColorPanel.SetBackgroundColor(_OldColor);
            _OldColorPanel.SetBorderColor(Color.White);
            _OldColorPanel.SetEnableDragDrop(true);
            _OldColorPanel.LeftMouseDoubleClickedEvent +=
                (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
                {
                    InsertColor(_OldColor);
                };
        }

        void InitializeNewColorPreview(int X, int Y)
        {
            InitializeLabel(X, Y, "New Color");
            Y += 20;
            InitializePanelGrid(X, Y);
            _NewColorPanel = UIHelper.GetInstance().CreatePanel(_PanelDialog);
            _NewColorPanel.SetPosition(X, Y, 100, 30);
            _NewColorPanel.SetBorderColor(Color.White);
            _NewColorPanel.SetEnableDragDrop(true);
            _NewColorPanel.LeftMouseDoubleClickedEvent +=
                (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
                {
                    InsertColor(_NewColor);
                };
        }

        void InitializesRGBColorPreview(int X, int Y)
        {
            InitializeLabel(X, Y, "sRGB Preview");
            Y += 20;
            InitializePanelGrid(X, Y);
            _sRGBPanel = UIHelper.GetInstance().CreatePanel(_PanelDialog);
            _sRGBPanel.SetPosition(X, Y, 100, 30);
            _sRGBPanel.SetBorderColor(Color.White);
            _sRGBPanel.SetEnableDragDrop(true);
            _sRGBPanel.LeftMouseDoubleClickedEvent +=
                (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
                {
                    InsertColor(GetSRGBPreviewColor(_NewColor));
                };
        }

        void ResetColorList()
        {
            _ColorList.Clear();
            for (int i = 0; i < MAX_COLOR; i++)
            {
                Color Color = Color.FromRGBA(0, 0, 0, 255);
                HDRColorItem HDRColorItem = new HDRColorItem();
                HDRColorItem._bEnable = false;
                HDRColorItem._Color = Color;
                _ColorList.Add(HDRColorItem);
            }
        }

        string GetColorToolTips(Color Color)
        {
            return string.Format(
                            "R:{0:F4},G:{1:F4},B:{2:F4},A:{3:F4}",
                            Color.R,
                            Color.G,
                            Color.B,
                            Color.A);
        }

        void UpdateColorListUI()
        {
            _ColorListPanel.ClearChildren();
            for (int i = 0; i < MAX_COLOR; i++)
            {
                int j1 = i % 7;
                int i1 = i / 7;
                int X = j1 * 40 + 10;
                int Y = i1 * 40 + 10;

                HDRColorItem HDRColorItem = _ColorList[i];

                Color Color = HDRColorItem._Color;
                Color ColorForDisplay = GetColorForDisplay(Color.R, Color.G, Color.B, Color.A);

                Panel Grid = UIHelper.GetInstance().CreatePanel(_ColorListPanel, "Editor/Others/PanelGrid.png");
                Grid.SetPosition(X, Y, 30, 30);

                Button ColorButton = new Button();
                ColorButton.SetPosition(X, Y, 30, 30);
                ColorButton.SetNormalColor(ColorForDisplay);
                ColorButton.SetHoverColor(ColorForDisplay);
                ColorButton.SetTagInt1(i);
                ColorButton.SetEnableDragDrop(true);
                if (HDRColorItem._bEnable)
                {
                    ColorButton.SetToolTips(GetColorToolTips(Color));
                }
                ColorButton.ClickedEvent += (Sender) => { SelectColorItemButton(Sender); };
                ColorButton.LeftMouseDoubleClickedEvent += OnColorButtonLeftMouseDoubleClicked;
                _ColorListPanel.AddChild(ColorButton);

                if (i == _SelectedColorIndex)
                {
                    ColorButton.SetBorderChecked(true);
                }

                HDRColorItem._ColorButton = ColorButton;
            }
        }

        void UpdateColorListThumbnail()
        {
            List<HDRColorItem> ValidItems = new List<HDRColorItem>();
            foreach (HDRColorItem Item in _ColorList)
            {
                if (Item._bEnable)
                {
                    ValidItems.Add(Item);
                }
            }

            _ColorListCombo.ClearChildren();
            if (ValidItems.Count == 0)
            {
                Label Label = UIHelper.GetInstance().CreateLabel(_ColorListCombo, "Drag color here to save", 14);
                Label.SetSize(_ColorListCombo.GetWidth(), _ColorListCombo.GetHeight());
                Label.SetTextOffsetY(1);
            }
            else
            {
                int SpanX = 2;
                int Width = (_ColorListCombo.GetWidth() - (ValidItems.Count - 1) * SpanX) / ValidItems.Count;
                int Index = 0;
                foreach (HDRColorItem Item in ValidItems)
                {
                    Color Color = Item._Color;
                    Color ColorForDisplay = GetColorForDisplay(Color.R, Color.G, Color.B, Color.A);

                    int X = Index * (Width + SpanX);
                    Panel Grid = UIHelper.GetInstance().CreatePanel(_ColorListCombo, "Editor/Others/PanelGrid.png");
                    Grid.SetPosition(X, 0, Width, 16);
                    _ColorListCombo.AddChild(Grid);

                    Button Button = new Button();
                    Button.SetPosition(X, 0, Width, 16);
                    Button.SetNormalColor(ColorForDisplay);
                    Button.SetHoverColor(ColorForDisplay);
                    Button.SetToolTips(GetColorToolTips(Color));
                    Button.MouseMoveEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
                    {
                        for (int i = 0; i < _ColorListCombo.GetChildCount(); i++)
                        {
                            _ColorListCombo.GetChild(i).SetBorderChecked(false);
                        }

                        if (Sender.IsPointIn_Recursively(MouseX, MouseY))
                        {
                            Sender.SetBorderChecked(true);
                        }
                    };
                    Button.ClickedEvent += (Sender) =>
                    {
                        SetNewColor(Item._Color);
                    };
                    _ColorListCombo.AddChild(Button);

                    Index++;
                }
            }
        }

        void OnColorButtonLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            HDRColorItem HDRColorItem = _ColorList[_SelectedColorIndex];
            if (HDRColorItem._bEnable)
            {
                SetNewColor(HDRColorItem._Color);
            }

            _ColorListPanel.SetVisible(false);
        }

        void DeleteColor()
        {
            if (_ColorListPanel.GetVisible())
            {
                Color Color = Color.FromRGBA(0, 0, 0, 255);
                SetSelectedColorItemColor(Color, false);
            }
            else
            {
                int ValidIndex = -1;
                for (int i = 0; i < _ColorListCombo.GetChildCount(); i++)
                {
                    if (_ColorListCombo.GetChild(i).GetBorderChecked())
                    {
                        ValidIndex = i / 2;
                        break;
                    }
                }
                if (ValidIndex != -1)
                {
                    int Index = 0;
                    foreach (HDRColorItem Item in _ColorList)
                    {
                        if (Item._bEnable)
                        {
                            if (Index == ValidIndex)
                            {
                                int LastIndex = _SelectedColorIndex;
                                SelectColorItemButton(Item._ColorButton);
                                Color Color = Color.FromRGBA(0, 0, 0, 255);
                                SetSelectedColorItemColor(Color, false);
                                SelectColorItemButton(_ColorList[LastIndex]._ColorButton);
                                break;
                            }

                            Index++;
                        }
                    }
                }
            }
        }

        public override void OnDeviceChar(Device Sender, char Char)
        {
            base.OnDeviceChar(Sender, Char);

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bNone = !bControl && !bShift && !bAlt;

            if (bNone && Char == '\b')
            {
                DeleteColor();
            }
        }

        public override void OnDeviceKey(Device Sender, KeyAction Action, Key Key)
        {
            base.OnDeviceKey(Sender, Action, Key);

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bNone = !bControl && !bShift && !bAlt;

            if (Action == KeyAction.Down)
            {
                if (bNone && Key == Key.Delete)
                {
                    DeleteColor();
                }
            }
        }

        void OnButtonOKClicked(Button Sender)
        {
            CloseDialog(() =>
            {
                ColorSelectedEvent?.Invoke(this);
            });
        }

        void OnButtonCancelClicked(Button Sender)
        {
            CloseDialog(() =>
            {
                _NewColor = _OldColor;
                ColorSelectedEvent?.Invoke(this);
            });
        }

        void SetEditBoxTextByFloatValue(Edit EditBox, float Value)
        {
            EditBox.SetText(Value.ToString());
            EditBox.SetCaretOn(false);
        }

        void SetupControlsByColor_RGBA(Color Color)
        {
            float R = Color.R;
            float G = Color.G;
            float B = Color.B;
            float A = Color.A;

            float R1 = R / _VMax;
            float G1 = G / _VMax;
            float B1 = B / _VMax;
            float A1 = A / _VMax;

            _TrackBarColorR.SetValue(R1);
            _TrackBarColorG.SetValue(G1);
            _TrackBarColorB.SetValue(B1);
            _TrackBarColorA.SetValue(A1);

            SetEditBoxTextByFloatValue(_EditBoxColorR, R);
            SetEditBoxTextByFloatValue(_EditBoxColorG, G);
            SetEditBoxTextByFloatValue(_EditBoxColorB, B);
            SetEditBoxTextByFloatValue(_EditBoxColorA, A);
        }

        void SetupControlsByColor_HSV(Color Color)
        {
            float R = Color.R;
            float G = Color.G;
            float B = Color.B;

            float H = 0;
            float S = 0;
            float V = 0;
            RGBtoHSV(R, G, B, out H, out S, out V);

            _TrackBarColorH.SetValue(H / 359.0f);
            _TrackBarColorS.SetValue(S);
            _TrackBarColorV.SetValue(V / _VMax);

            SetEditBoxTextByFloatValue(_EditBoxColorH, H);
            SetEditBoxTextByFloatValue(_EditBoxColorS, S);
            SetEditBoxTextByFloatValue(_EditBoxColorV, V);
        }

        public static Color GetColorForDisplay(float R, float G, float B, float A)
        {
            float ColorMax = MathF.Max(R, MathF.Max(G, B));
            ColorMax = MathF.Max(1.0f, ColorMax);

            float R1 = R / ColorMax;
            float G1 = G / ColorMax;
            float B1 = B / ColorMax;
            float A1 = Math.Clamp(A, 0.0f, 1.0f);
            Color Color = new Color(R1, G1, B1, A1);
            return Color;
        }

        [StructLayout(LayoutKind.Explicit, Size = 32)]
        struct FP32
        {
            [FieldOffset(0)]
            public uint u;
            [FieldOffset(0)]
            public float f;
        }

        static FP32 AlmostOne = new FP32 { u = 0x3f7fffff };
        static FP32 MinVal = new FP32 { u = (127 - 13) << 23 };

        static uint[] FP32ToSrgb8Tab4 = new uint[104]
        {
            0x0073000d, 0x007a000d, 0x0080000d, 0x0087000d, 0x008d000d, 0x0094000d, 0x009a000d, 0x00a1000d,
            0x00a7001a, 0x00b4001a, 0x00c1001a, 0x00ce001a, 0x00da001a, 0x00e7001a, 0x00f4001a, 0x0101001a,
            0x010e0033, 0x01280033, 0x01410033, 0x015b0033, 0x01750033, 0x018f0033, 0x01a80033, 0x01c20033,
            0x01dc0067, 0x020f0067, 0x02430067, 0x02760067, 0x02aa0067, 0x02dd0067, 0x03110067, 0x03440067,
            0x037800ce, 0x03df00ce, 0x044600ce, 0x04ad00ce, 0x051400ce, 0x057b00c5, 0x05dd00bc, 0x063b00b5,
            0x06970158, 0x07420142, 0x07e30130, 0x087b0120, 0x090b0112, 0x09940106, 0x0a1700fc, 0x0a9500f2,
            0x0b0f01cb, 0x0bf401ae, 0x0ccb0195, 0x0d950180, 0x0e56016e, 0x0f0d015e, 0x0fbc0150, 0x10630143,
            0x11070264, 0x1238023e, 0x1357021d, 0x14660201, 0x156601e9, 0x165a01d3, 0x174401c0, 0x182401af,
            0x18fe0331, 0x1a9602fe, 0x1c1502d2, 0x1d7e02ad, 0x1ed4028d, 0x201a0270, 0x21520256, 0x227d0240,
            0x239f0443, 0x25c003fe, 0x27bf03c4, 0x29a10392, 0x2b6a0367, 0x2d1d0341, 0x2ebe031f, 0x304d0300,
            0x31d105b0, 0x34a80555, 0x37520507, 0x39d504c5, 0x3c37048b, 0x3e7c0458, 0x40a8042a, 0x42bd0401,
            0x44c20798, 0x488e071e, 0x4c1c06b6, 0x4f76065d, 0x52a50610, 0x55ac05cc, 0x5892058f, 0x5b590559,
            0x5e0c0a23, 0x631c0980, 0x67db08f6, 0x6c55087f, 0x70940818, 0x74a007bd, 0x787d076c, 0x7c330723
        };

        static uint LinearToSrgbUintFast(float LinearIn)
        {
            uint tab, bias, scale, t;
            FP32 f;

            if (!(LinearIn > MinVal.f))
                LinearIn = MinVal.f;
            if (LinearIn > AlmostOne.f)
                LinearIn = AlmostOne.f;

            f.u = 0;
            f.f = LinearIn;
            tab = FP32ToSrgb8Tab4[(f.u - MinVal.u) >> 20];
            bias = (tab >> 16) << 9;
            scale = tab & 0xffff;

            t = (f.u >> 12) & 0xff;
            return (bias + scale * t) >> 16;
        }

        public static Color GetSRGBPreviewColor(Color Color)
        {
            Color SRGBColor = Color.FromRGBA
                (
                    (int)LinearToSrgbUintFast(Color.R),
                    (int)LinearToSrgbUintFast(Color.G),
                    (int)LinearToSrgbUintFast(Color.B),
                    (int)(0.5 + Math.Clamp(Color.A, 0.0f, 1.0f) * 255.0f)
                );
            return SRGBColor;
        }

        void UpdateColorByWheelPosition(int X, int Y)
        {
            float PosX = X - _Radius;
            float PosY = Y - _Radius;
            float L = MathF.Sqrt(PosX * PosX + PosY * PosY);
            float H;
            float Angle = MathF.Acos(PosX / L) * 180.0f / MathF.PI;
            if (PosY >= 0)
            {
                H = Angle;
            }
            else
            {
                H = 360.0f - Angle;
            }
            float S = Math.Clamp(L / _Radius, 0.0f, 1.0f);
            float V = MathHelper.ParseFloat(_EditBoxColorV.GetText());
            float R, G, B;
            HSVtoRGB(out R, out G, out B, H, S, V);
            float A = _NewColor.A;
            Color NewColor = new Color(R, G, B, A);
            SetupControlsByColor_RGBA(NewColor);
            _TrackBarColorH.SetValue(H / 359.0f);
            _TrackBarColorS.SetValue(S);
            _TrackBarColorV.SetValue(V / _VMax);

            SetEditBoxTextByFloatValue(_EditBoxColorH, H);
            SetEditBoxTextByFloatValue(_EditBoxColorS, S);
            SetEditBoxTextByFloatValue(_EditBoxColorV, V);

            SetNewColor(NewColor, false);
        }

        void UpdateColorBySpectrumPosition(int X, int Y)
        {
            float PosX = X;
            float PosY = Y - _Radius;

            float H = (PosX / (2 * _Radius)) * 360.0f;
            H = Math.Clamp(H, 0.0f, 360.0f);
            float S = 1.0f;
            if (PosY < 0)
            {
                S = 1.0f + PosY / _Radius;
                S = Math.Clamp(S, 0.0f, 1.0f);
            }
            float V = 1.0f;
            if (PosY > 0)
            {
                V = 1.0f - PosY / _Radius;
                V = Math.Clamp(V, 0.0f, 1.0f);
            }
            float R, G, B;
            HSVtoRGB(out R, out G, out B, H, S, V);
            float A = _NewColor.A;
            Color NewColor = new Color(R, G, B, A);
            SetupControlsByColor_RGBA(NewColor);
            _TrackBarColorH.SetValue(H / 359.0f);
            _TrackBarColorS.SetValue(S);
            _TrackBarColorV.SetValue(V / _VMax);

            SetEditBoxTextByFloatValue(_EditBoxColorH, H);
            SetEditBoxTextByFloatValue(_EditBoxColorS, S);
            SetEditBoxTextByFloatValue(_EditBoxColorV, V);

            SetNewColor(NewColor, false);
        }

        void UpdatePanelColor(Color Color)
        {
            float R = Color.R;
            float G = Color.G;
            float B = Color.B;
            float A = Color.A;
            Color ColorForDisplay = GetColorForDisplay(R, G, B, A);
            _NewColorPanel.SetBackgroundColor(ColorForDisplay);
            Color ColorForDisplay_SRGB = GetSRGBPreviewColor(ColorForDisplay);
            _sRGBPanel.SetBackgroundColor(ColorForDisplay_SRGB);
        }

        void UpdateOrientation(Color Color)
        {
            int Index = _ComboBox.GetSelectedItemIndex();
            float H = MathHelper.ParseFloat(_EditBoxColorH.GetText());
            float S = MathHelper.ParseFloat(_EditBoxColorS.GetText());
            float V = MathHelper.ParseFloat(_EditBoxColorV.GetText());
            if (Index == 0)
            {
                float Radian = H / 180.0f * MathF.PI;
                float Radius = S * _Radius;
                float X = MathF.Cos(Radian) * Radius;
                float Y = MathF.Sin(Radian) * Radius;
                _OrientationPanel.SetPos((int)(_Radius + X) - 10, (int)(_Radius + Y) - 10);
            }
            else
            {
                float X = (H / 360.0f) * 2 * _Radius;
                float Y;
                if (S != 1.0f && V != 1.0f)
                {
                    _OrientationPanel.SetPos(int.MaxValue, int.MaxValue);
                }
                else
                {
                    if (S == 1.0f)
                    {
                        Y = (2 - V) * _Radius;
                    }
                    else
                    {
                        Y = S * _Radius;
                    }
                    _OrientationPanel.SetPos((int)X - 10, (int)Y - 10);
                }
            }
        }

        void UpdateHexString(Color Color)
        {
            uint Linear = 0;
            Linear += (uint)(0.5f + Math.Clamp(Color.R, 0.0f, 1.0f) * 255.0f) << 24;
            Linear += (uint)(0.5f + Math.Clamp(Color.G, 0.0f, 1.0f) * 255.0f) << 16;
            Linear += (uint)(0.5f + Math.Clamp(Color.B, 0.0f, 1.0f) * 255.0f) << 8;
            Linear += (uint)(0.5f + Math.Clamp(Color.A, 0.0f, 1.0f) * 255.0f);
            _EditBoxLinear.SetText(string.Format("#{0:X8}", Linear));

            Color sRGBColor = GetSRGBPreviewColor(Color);
            uint sRGB = 0;
            sRGB += (uint)(Math.Clamp(sRGBColor.R, 0.0f, 1.0f) * 255.0f) << 24;
            sRGB += (uint)(Math.Clamp(sRGBColor.G, 0.0f, 1.0f) * 255.0f) << 16;
            sRGB += (uint)(Math.Clamp(sRGBColor.B, 0.0f, 1.0f) * 255.0f) << 8;
            sRGB += (uint)(Math.Clamp(sRGBColor.A, 0.0f, 1.0f) * 255.0f);
            _EditBox_sRGB.SetText(string.Format("#{0:X8}", sRGB));
        }

        Color LoadColorFromControls_RGBA()
        {
            float R = MathHelper.ParseFloat(_EditBoxColorR.GetText());
            float G = MathHelper.ParseFloat(_EditBoxColorG.GetText());
            float B = MathHelper.ParseFloat(_EditBoxColorB.GetText());
            float A = MathHelper.ParseFloat(_EditBoxColorA.GetText());

            R = Math.Max(R, 0.0f);
            G = Math.Max(G, 0.0f);
            B = Math.Max(B, 0.0f);
            A = Math.Max(A, 0.0f);

            return new Color(R, G, B, A);
        }

        Color LoadColorFromControls_HSVA()
        {
            float H = MathHelper.ParseFloat(_EditBoxColorH.GetText());
            float S = MathHelper.ParseFloat(_EditBoxColorS.GetText());
            float V = MathHelper.ParseFloat(_EditBoxColorV.GetText());
            float A = MathHelper.ParseFloat(_EditBoxColorA.GetText());

            H = Math.Clamp(H, 0.0f, 359.0f);
            S = Math.Max(S, 0.0f);
            V = Math.Max(V, 0.0f);

            float R = 0.0f;
            float G = 0.0f;
            float B = 0.0f;
            HSVtoRGB(out R, out G, out B, H, S, V);

            return new Color(R, G, B, A);
        }

        void InvalidateTrackBars()
        {
            _TrackBarColorS.Invalidate();
            _TrackBarColorV.Invalidate();
            _TrackBarColorR.Invalidate();
            _TrackBarColorG.Invalidate();
            _TrackBarColorB.Invalidate();
            _TrackBarColorA.Invalidate();
        }

        void OnTrackBarColorValueChanged(TrackBar Sender)
        {
            float ValueMax = 1.0f;
            TrackBar TrackBar = Sender;
            Edit EditBox = null;

            if (TrackBar == _TrackBarColorH)
            {
                ValueMax = 359.0f;
                EditBox = _EditBoxColorH;
            }
            else if (TrackBar == _TrackBarColorS)
            {
                ValueMax = 1.0f;
                EditBox = _EditBoxColorS;
            }
            else if (TrackBar == _TrackBarColorV)
            {
                ValueMax = _VMax;
                EditBox = _EditBoxColorV;
            }
            else if (TrackBar == _TrackBarColorR)
            {
                ValueMax = _VMax;
                EditBox = _EditBoxColorR;
            }
            else if (TrackBar == _TrackBarColorG)
            {
                ValueMax = _VMax;
                EditBox = _EditBoxColorG;
            }
            else if (TrackBar == _TrackBarColorB)
            {
                ValueMax = _VMax;
                EditBox = _EditBoxColorB;
            }
            else if (TrackBar == _TrackBarColorA)
            {
                ValueMax = _VMax;
                EditBox = _EditBoxColorA;
            }

            if (EditBox != null)
            {
                float Value = TrackBar.GetValue();
                float ColorValue = Value * ValueMax;
                SetEditBoxTextByFloatValue(EditBox, ColorValue);
            }

            if (TrackBar == _TrackBarColorR ||
                TrackBar == _TrackBarColorG ||
                TrackBar == _TrackBarColorB ||
                TrackBar == _TrackBarColorA)
            {
                Color NewColor = LoadColorFromControls_RGBA();
                SetNewColor(NewColor, false);
                SetupControlsByColor_HSV(NewColor);
            }
            else
            {
                Color NewColor = LoadColorFromControls_HSVA();
                SetNewColor(NewColor, false);
                SetupControlsByColor_RGBA(NewColor);
            }

            InvalidateTrackBars();

            GetDevice().TriggerPaintEvent();
        }

        void OnTrackBarColorPaintBackBar(TrackBar Sender)
        {
            TrackBar TrackBar = Sender;

            if (TrackBar == _TrackBarColorH ||
                TrackBar == _TrackBarColorS ||
                TrackBar == _TrackBarColorV ||
                TrackBar == _TrackBarColorR ||
                TrackBar == _TrackBarColorG ||
                TrackBar == _TrackBarColorB ||
                TrackBar == _TrackBarColorA)
            {
                int AbsX = TrackBar.GetScreenX();
                int AbsY = TrackBar.GetScreenY();
                int Width = TrackBar.GetWidth();
                int Height = TrackBar.GetHeight();

                float R = _NewColor.R;
                float G = _NewColor.G;
                float B = _NewColor.B;

                float H_EditBox = MathHelper.ParseFloat(_EditBoxColorH.GetText());
                H_EditBox = Math.Clamp(H_EditBox, 0, 359.0f);

                float S_EditBox = MathHelper.ParseFloat(_EditBoxColorS.GetText());
                S_EditBox = Math.Max(S_EditBox, 0);

                float V_EditBox = MathHelper.ParseFloat(_EditBoxColorV.GetText());
                V_EditBox = Math.Max(V_EditBox, 0);

                int i;
                for (i = 0; i < Width; i++)
                {
                    int X1 = AbsX + i;
                    int Y1 = AbsY + Height / 4;
                    int X2 = X1;
                    int Y2 = Y1 + Height / 2;

                    float Ratio = i / (float)(Width - 1);
                    float R1 = R;
                    float G1 = G;
                    float B1 = B;

                    if (TrackBar == _TrackBarColorH)
                    {
                        float H1 = Ratio * 359.0f;
                        float S1 = 1.0f;
                        float V1 = 1.0f;
                        HSVtoRGB(out R1, out G1, out B1, H1, S1, V1);
                    }
                    else if (TrackBar == _TrackBarColorS)
                    {
                        float H1 = H_EditBox;
                        float S1 = Ratio;
                        float V1 = V_EditBox;
                        HSVtoRGB(out R1, out G1, out B1, H1, S1, V1);
                    }
                    else if (TrackBar == _TrackBarColorV)
                    {
                        float H1 = H_EditBox;
                        float S1 = S_EditBox;
                        float V1 = Ratio;
                        HSVtoRGB(out R1, out G1, out B1, H1, S1, V1);
                    }
                    else if (TrackBar == _TrackBarColorR)
                    {
                        R1 = Ratio;
                    }
                    else if (TrackBar == _TrackBarColorG)
                    {
                        G1 = Ratio;
                    }
                    else if (TrackBar == _TrackBarColorB)
                    {
                        B1 = Ratio;
                    }
                    else if (TrackBar == _TrackBarColorA)
                    {
                        float A1 = Ratio;
                        R1 = A1;
                        G1 = A1;
                        B1 = A1;
                    }

                    Color Color = GetColorForDisplay(R1, G1, B1, 1.0f);
                    GetUIManager().GetGraphics2D().DrawLine(Color, X1, Y1, X2, Y2);
                }
            }
        }

        void AfterEditBoxColorTextChanged(Edit Sender)
        {
            float ValueMax = 0.0f;
            Edit EditBox = Sender;

            TrackBar TrackBar = null;

            if (EditBox == _EditBoxColorH)
            {
                ValueMax = 359.0f;
                TrackBar = _TrackBarColorH;
            }
            else if (EditBox == _EditBoxColorS)
            {
                ValueMax = 1.0f;
                TrackBar = _TrackBarColorS;
            }
            else if (EditBox == _EditBoxColorV)
            {
                ValueMax = float.MaxValue;
                TrackBar = _TrackBarColorV;
            }
            else if (EditBox == _EditBoxColorR)
            {
                ValueMax = float.MaxValue;
                TrackBar = _TrackBarColorR;
            }
            else if (EditBox == _EditBoxColorG)
            {
                ValueMax = float.MaxValue;
                TrackBar = _TrackBarColorG;
            }
            else if (EditBox == _EditBoxColorB)
            {
                ValueMax = float.MaxValue;
                TrackBar = _TrackBarColorB;
            }
            else if (EditBox == _EditBoxColorA)
            {
                ValueMax = float.MaxValue;
                TrackBar = _TrackBarColorA;
            }

            if (TrackBar != null)
            {
                float Value = MathHelper.ParseFloat(EditBox.GetText());
                Value = Math.Clamp(Value, 0.0f, ValueMax);
                SetEditBoxTextByFloatValue(EditBox, Value);
                EditBox.SetCaretOn(true);
                // V decide the VMax directly
                if (EditBox == _EditBoxColorV)
                {
                    _VMax = MathF.Max(1.0f, Value);
                    ValueMax = _VMax;
                }
                // The max value of RGBA decide the VMax
                if (EditBox == _EditBoxColorR ||
                    EditBox == _EditBoxColorG ||
                    EditBox == _EditBoxColorB ||
                    EditBox == _EditBoxColorA)
                {
                    Color Temp = LoadColorFromControls_RGBA();
                    _VMax = MathF.Max(Temp.R, MathF.Max(Temp.G, MathF.Max(Temp.B, Temp.A)));
                    _VMax = MathF.Max(1.0f, _VMax);
                    ValueMax = _VMax;
                }
                // Update track bar
                TrackBar.SetValue(Value / ValueMax);
            }

            Color NewColor;
            if (EditBox == _EditBoxColorR ||
                EditBox == _EditBoxColorG ||
                EditBox == _EditBoxColorB ||
                EditBox == _EditBoxColorA)
            {
                NewColor = LoadColorFromControls_RGBA();
                SetNewColor(NewColor, false);
                SetupControlsByColor_HSV(NewColor);
                SetupControlsByColor_RGBA(NewColor);
            }
            else
            {
                NewColor = LoadColorFromControls_HSVA();
                SetNewColor(NewColor, false);
                SetupControlsByColor_RGBA(NewColor);
                SetupControlsByColor_HSV(NewColor);
            }

            InvalidateTrackBars();
        }

        void OnEditBoxColorCharInput(Control Sender, char Char, ref bool bContinue)
        {
            if (Char == '\r')
            {
                AfterEditBoxColorTextChanged(Sender as Edit);
            }
        }

        void OnEditBoxFocusChanged(Control Sender)
        {
            // Lost focus event
            if (!Sender.IsFocused())
            {
                AfterEditBoxColorTextChanged(Sender as Edit);
            }
        }

        void ParseHexStringToColor(string HexString, out Color Color)
        {
            Color = _NewColor;
            if (HexString.Length == 9 && HexString.StartsWith("#"))
            {
                if (uint.TryParse(HexString.Substring(1), System.Globalization.NumberStyles.HexNumber, null, out uint Dword))
                {
                    Dword =
                        ((Dword & ((uint)0xff << 24)) >> 24) +
                        ((Dword & ((uint)0xff << 16)) >> 8) +
                        ((Dword & ((uint)0xff << 8)) << 8) +
                        ((Dword & 0xff) << 24);
                    Color = Color.FromDword(Dword);
                }
            }
        }

        Color SrgbColorToLinear(Color SrgbColor)
        {
            float Red = SrgbColor.R;
            float Green = SrgbColor.G;
            float Blue = SrgbColor.B;
            float Alpha = SrgbColor.A;

            Red = Red <= 0.04045f ? Red / 12.92f : MathF.Pow((Red + 0.055f) / 1.055f, 2.4f);
            Green = Green <= 0.04045f ? Green / 12.92f : MathF.Pow((Green + 0.055f) / 1.055f, 2.4f);
            Blue = Blue <= 0.04045f ? Blue / 12.92f : MathF.Pow((Blue + 0.055f) / 1.055f, 2.4f);

            Color LinearColor = new Color(Red, Green, Blue, Alpha);
            return LinearColor;
        }

        void OnEditBoxLinearCharInput(Control Sender, char Char, ref bool bContinue)
        {
            if (Char == '\r')
            {
                Color OutColor;
                ParseHexStringToColor(Sender.GetText(), out OutColor);
                SetNewColor(OutColor);
            }
        }

        void OnEditBox_sRGBCharInput(Control Sender, char Char, ref bool bContinue)
        {
            if (Char == '\r')
            {
                Color OutColor;
                ParseHexStringToColor(Sender.GetText(), out OutColor);
                Color LinearColor = SrgbColorToLinear(OutColor);
                SetNewColor(LinearColor);
            }
        }

        void SetNewColor(Color NewColor, bool bUpdateControls = true)
        {
            _NewColor = NewColor;
            ColorSelectingEvent?.Invoke(this);
            if (bUpdateControls)
            {
                SetupControlsByColor_RGBA(NewColor);
                SetupControlsByColor_HSV(NewColor);
            }
            UpdatePanelColor(NewColor);
            UpdateOrientation(NewColor);
            UpdateHexString(NewColor);
        }

        void OnButtonPickColorLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            _SavedColor = _NewColor;
            _ButtonPickColor.LeftMouseUpEvent += OnButtonPickColorLeftMouseUp;
            _ButtonPickColor.RightMouseDownEvent += OnButtonPickColorRightMouseDown;
            _ButtonPickColor.MouseMoveEvent += OnButtonPickColorMouseMove;
            Cursor Cusror = GetDevice().GetSystemCursor(SystemCursor.Cross);
            GetUIManager().SetOverridingCursor(Cusror);
        }

        void OnButtonPickColorLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            _ButtonPickColor.LeftMouseUpEvent -= OnButtonPickColorLeftMouseUp;
            _ButtonPickColor.RightMouseDownEvent -= OnButtonPickColorRightMouseDown;
            _ButtonPickColor.MouseMoveEvent -= OnButtonPickColorMouseMove;
            GetUIManager().SetOverridingCursor(null);

            if (_ButtonPickColor.IsPointIn(MouseX, MouseY))
            {
                return;
            }

            Device Device = GetDevice();
            int ScreenMouseX = Device.GetX() + MouseX;
            int ScreenMouseY = Device.GetY() + MouseY;

            Color NewColor = Device.PickScreenColor(ScreenMouseX, ScreenMouseY);
            SetNewColor(NewColor);
        }

        void OnButtonPickColorRightMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            _ButtonPickColor.LeftMouseUpEvent -= OnButtonPickColorLeftMouseUp;
            _ButtonPickColor.RightMouseDownEvent -= OnButtonPickColorRightMouseDown;
            _ButtonPickColor.MouseMoveEvent -= OnButtonPickColorMouseMove;
            GetUIManager().SetOverridingCursor(null);

            SetNewColor(_SavedColor);
        }

        void OnButtonPickColorMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Device Device = GetDevice();
            int ScreenMouseX = Device.GetX() + MouseX;
            int ScreenMouseY = Device.GetY() + MouseY;

            Color NewColor = Device.PickScreenColor(ScreenMouseX, ScreenMouseY);
            SetNewColor(NewColor);
        }

        public static void RGBtoHSV(float r, float g, float b, out float h, out float s, out float v)
        {
            if (r == 0.0f && g == 0.0f && b == 0.0f)
            {
                h = 0.0f;
                s = 0.0f;
                v = 0.0f;
                return;
            }
            if (r == 1.0f && g == 1.0f && b == 1.0f)
            {
                h = 0.0f;
                s = 0.0f;
                v = 1.0f;
                return;
            }
            float min, max, delta;
            min = Math.Min(r, Math.Min(g, b));
            max = Math.Max(r, Math.Max(g, b));
            // V
            v = max;
            // S
            delta = max - min;
            s = max == 0 ? 0 : delta / max;
            // H
            h = max == min ? 0 :
                max == r ? (((g - b) / delta) * 60.0f) :
                max == g ? (((b - r) / delta) * 60.0f) + 120.0f :
                max == b ? (((r - g) / delta) * 60.0f) + 240.0f : 0.0f;

            if (h < 0)
                h += 360.0f;
        }

        public static void HSVtoRGB(out float r, out float g, out float b, float h, float s, float v)
        {
            int i;
            float f, p, q, t;
            h /= 60;
            i = (int)Math.Floor(h);
            f = h - i;
            p = v * (1 - s);
            q = v * (1 - s * f);
            t = v * (1 - s * (1 - f));
            switch (i)
            {
                case 0:
                    r = v;
                    g = t;
                    b = p;
                    break;
                case 1:
                    r = q;
                    g = v;
                    b = p;
                    break;
                case 2:
                    r = p;
                    g = v;
                    b = t;
                    break;
                case 3:
                    r = p;
                    g = q;
                    b = v;
                    break;
                case 4:
                    r = t;
                    g = p;
                    b = v;
                    break;
                case 5:
                    r = v;
                    g = p;
                    b = q;
                    break;
                default:	// case 6
                    r = v;
                    g = t;
                    b = p;
                    break;
            }
        }

        public void SaveColors()
        {
            MainUI MainUI = MainUI.GetInstance();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            if (ProjectDirectory == "")
            {
                return;
            }
            string Filename = ProjectDirectory + "/" + COLORS_FILENAME;

            XmlScript Xml = new XmlScript();
            Record RootRecord = Xml.GetRootRecord();

            Record RecordColors = RootRecord.AddChild();
            RecordColors.SetTypeString("Colors");
            RecordColors.SetInt("SelectedColorIndex", _SelectedColorIndex);

            foreach (HDRColorItem ColorItem in _ColorList)
            {
                Record RecordColor = RecordColors.AddChild();
                RecordColor.SetTypeString("Color");
                RecordColor.SetBool("Enable", ColorItem._bEnable);
                RecordColor.SetFloat("ColorR", ColorItem._Color.R);
                RecordColor.SetFloat("ColorG", ColorItem._Color.G);
                RecordColor.SetFloat("ColorB", ColorItem._Color.B);
                RecordColor.SetFloat("ColorA", ColorItem._Color.A);
            }

            Xml.Save(Filename);
        }

        public void LoadColors()
        {
            MainUI MainUI = MainUI.GetInstance();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            string Filename = ProjectDirectory + "/" + COLORS_FILENAME;
            if (File.Exists(Filename) == false)
            {
                return;
            }
            ResetColorList();
            XmlScript Xml = new XmlScript();
            Xml.Open(Filename);
            Record RootRecord = Xml.GetRootRecord();

            Record RecordColors = RootRecord.FindByTypeString("Colors");
            if (RecordColors != null)
            {
                _SelectedColorIndex = RecordColors.GetInt("SelectedColorIndex");

                int ChildCount = RecordColors.GetChildCount();
                DebugHelper.Assert(ChildCount == MAX_COLOR);
                for (int i = 0; i < ChildCount; i++)
                {
                    Record RecordColor = RecordColors.GetChild(i);
                    DebugHelper.Assert(RecordColor.GetTypeString() == "Color");
                    bool bEnable = RecordColor.GetBool("Enable");
                    float ColorR = RecordColor.GetFloat("ColorR");
                    float ColorG = RecordColor.GetFloat("ColorG");
                    float ColorB = RecordColor.GetFloat("ColorB");
                    float ColorA = RecordColor.GetFloat("ColorA");
                    HDRColorItem HDRColorItem = _ColorList[i];
                    HDRColorItem._bEnable = bEnable;
                    HDRColorItem._Color = new Color(ColorR, ColorG, ColorB, ColorA);
                }
            }
        }

        void OnDragDropManagerDragBegin(DragDropManager Sender, UIManager UIManager, int OriginalMouseX, int OriginalMouseY, ref bool bDragBegin, ref string ImageFilename)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (_ColorListPanel.GetVisible())
            {
                foreach (HDRColorItem Item in _ColorList)
                {
                    if (Item._bEnable && Item._ColorButton.IsPointIn_Recursively(OriginalMouseX, OriginalMouseY))
                    {
                        bDragBegin = true;
                        _ItemDragged = Item._ColorButton.GetTagInt1();
                        _ItemTarget = _ItemDragged;
                        SelectColorItemButton(Item._ColorButton);
                        break;
                    }
                }
            }
            else
            {
                if (_OldColorPanel.IsPointIn_Recursively(OriginalMouseX, OriginalMouseY))
                {
                    bDragBegin = true;
                    _ColorDragged = GetOldColor();
                }
                else if (_NewColorPanel.IsPointIn_Recursively(OriginalMouseX, OriginalMouseY))
                {
                    bDragBegin = true;
                    _ColorDragged = GetNewColor();
                }

                if (bDragBegin)
                {
                    bool bEnable = _ColorListCombo.GetChildCount() < MAX_COLOR * 2;
                    _ColorListCombo.ClearChildren();
                    Label Label = UIHelper.GetInstance().CreateLabel(_ColorListCombo, bEnable ? "Drag color here to save" : "Out of limit!", 14);
                    Label.SetSize(_ColorListCombo.GetWidth(), _ColorListCombo.GetHeight());
                    Label.SetTextOffsetY(1);
                }
            }
        }

        void SwapColorListItem(int Left, int Right)
        {
            if (Left == Right)
                return;
            if (Left < 0 || Left > MAX_COLOR)
                return;
            if (Right < 0 || Right > MAX_COLOR)
                return;

            HDRColorItem Temp = _ColorList[Left];
            _ColorList[Left] = _ColorList[Right];
            _ColorList[Right] = Temp;
        }

        void OnDragDropManagerDragMove(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (_ColorListPanel.GetVisible())
            {
                int Temp = -1;
                foreach (HDRColorItem Item in _ColorList)
                {
                    if (Item._ColorButton.IsPointIn_Recursively(MouseX, MouseY))
                    {
                        Temp = Item._ColorButton.GetTagInt1();
                        break;
                    }
                }

                if (Temp != -1)
                {
                    // Swap back
                    SwapColorListItem(_ItemTarget, _ItemDragged);
                    // Swap to current hover
                    _ItemTarget = Temp;
                    SwapColorListItem(_ItemTarget, _ItemDragged);

                    UpdateColorListUI();
                    UpdateColorListThumbnail();
                }
            }
            else
            {
                if (_ColorListCombo.IsPointIn_Recursively(MouseX, MouseY))
                {
                    _ColorListCombo.SetBackgroundColor(Color.FromRGBA(255, 255, 255, 50));
                }
                else
                {
                    _ColorListCombo.SetBackgroundColor(Color.FromRGBA(255, 255, 255, 10));
                }
            }
        }

        void InsertColor(Color Color)
        {
            // Find the first empty one
            int Index = -1;
            for (int i = 0; i < _ColorList.Count; ++i)
            {
                if (_ColorList[i]._bEnable == false)
                {
                    Index = i;
                    break;
                }
            }

            if (Index != -1)
            {
                // Swap the empty one to the head
                for (int i = Index; i > 0; --i)
                {
                    SwapColorListItem(i, i - 1);
                }
                UpdateColorListUI();
                // Remember selected index
                int Last = _SelectedColorIndex;
                // Set color to the head
                SelectColorItemButton(_ColorList[0]._ColorButton);
                SetSelectedColorItemColor(Color, true);
                // Revert the selected index back
                SelectColorItemButton(_ColorList[Last]._ColorButton);
            }
            else
            {
                UpdateColorListThumbnail();
            }
        }

        void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (_ColorListPanel.GetVisible())
            {
                // Swap end
                SelectColorItemButton(_ColorList[_ItemTarget]._ColorButton);
            }
            else
            {
                if (_ColorListCombo.IsPointIn_Recursively(MouseX, MouseY))
                {
                    InsertColor(_ColorDragged);
                }
                else
                {
                    UpdateColorListThumbnail();
                }
                _ColorListCombo.SetBackgroundColor(Color.FromRGBA(255, 255, 255, 10));
            }
        }

        void OnDragDropManagerDragClear(DragDropManager Sender)
        {
            DragClearStates();
        }

        void OnDragDropManagerDragCancel(DragDropManager Sender)
        {
            DragClearStates();
        }

        void DragClearStates()
        {
            _ColorDragged = new Color(0.0f, 0.0f, 0.0f, 0.0f);
        }
    }
}
