using System.Collections.Generic;

namespace EditorUI
{
    public delegate void ComboBoxExItemSelectedEventHandler(ComboBoxEx Sender);

    public class ComboBoxExItem
    {
        public string _Text;
        public bool _bEnable;

        public ComboBoxExItem(string Text, bool bEnable)
        {
            _Text = Text;
            _bEnable = bEnable;
        }
    }

    public class ComboBoxEx : Control
    {
        Edit _EditValue;
        Button _ButtonDropDown;
        List<ComboBoxExItem> _ItemList;
        Texture _TextureCheckedMark;

        public event ComboBoxExItemSelectedEventHandler ItemSelectedEvent;

        public ComboBoxEx()
        {
            _ItemList = new List<ComboBoxExItem>();
        }

        public override void Initialize()
        {
            int FontSize = Control.UI_DEFAULT_FONT_SIZE;

            _EditValue = UIHelper.GetInstance().CreateEditBox(this, "", FontSize);
            _EditValue.SetPosition(0, 1, 300, FontSize);
            _EditValue.SetReadOnly(true);
            _EditValue.SetEnable(false);

            _ButtonDropDown = UIHelper.GetInstance().CreateButton_Image(this, "Editor/Others/ButtonDropDown.png");
            _ButtonDropDown.ClickedEvent += OnButtonDropDownClicked;

            _TextureCheckedMark = UIManager.LoadUIImage("Editor/Icons/Special/CheckedMark.png");
        }

        public Edit GetValueEdit()
        {
            return _EditValue;
        }

        public override void OnPositionChanged(bool bPositionChanged, bool bSizeChanged)
        {
            base.OnPositionChanged(bPositionChanged, bSizeChanged);

            int ButtonDropDownWidth = _ButtonDropDown.GetWidth();
            int EditValueWidth = GetWidth() - ButtonDropDownWidth;
            _EditValue.SetWidth(EditValueWidth);
            _ButtonDropDown.SetX(_EditValue.GetEndX());
        }

        public void AddItem(string Text, bool bEnable = true)
        {
            _ItemList.Add(new ComboBoxExItem(Text, bEnable));
        }

        public void ClearItems()
        {
            _ItemList.Clear();
        }

        public string GetItemText(int ItemIndex)
        {
            return _ItemList[ItemIndex]._Text;
        }

        public int FindItemByText(string Text)
        {
            int Count = _ItemList.Count;
            for (int i = 0; i < Count; i++)
            {
                if (Text == _ItemList[i]._Text)
                {
                    return i;
                }
            }
            return -1;
        }

        public int GetSelectedItemIndex()
        {
            string CurrentText = _EditValue.GetText();
            return FindItemByText(CurrentText);
        }

        public void SetSelectedItemIndex(int ItemIndex)
        {
            string ItemText = GetItemText(ItemIndex);
            if (ItemText != null)
            {
                _EditValue.SetText(ItemText);
            }
        }

        public string GetSelectedItemText()
        {
            string CurrentText = _EditValue.GetText();
            return CurrentText;
        }

        public void SetSelectedItemByText(string Text)
        {
            int ItemIndex = FindItemByText(Text);
            if (ItemIndex != -1)
            {
                SetSelectedItemIndex(ItemIndex);
            }
        }

        void OnButtonDropDownClicked(Button Sender)
        {
            int Width = GetWidth();

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.SetTextOnly(true);
            MenuContextMenu.SetMinWidth(Width);
            MenuContextMenu.Initialize();

            int Count = _ItemList.Count;
            int i;
            for (i = 0; i < Count; i++)
            {
                MenuItem MenuItem_Item = new MenuItem();
                MenuItem_Item.SetText(_ItemList[i]._Text);
                if (_ItemList[i]._bEnable)
                {
                    MenuItem_Item.ClickedEvent += OnMenuItemItemXClicked;
                }
                else
                {
                    MenuItem_Item.SetEnable(false);
                }
                MenuContextMenu.AddMenuItem(MenuItem_Item);
            }

            bool bHCentered = true;
            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, this, bHCentered);
        }

        public void TriggerItemSelectedEvent()
        {
            if (ItemSelectedEvent != null)
            {
                ItemSelectedEvent(this);
            }
        }

        void OnMenuItemItemXClicked(MenuItem MenuItem_Item)
        {
            string ItemText = MenuItem_Item.GetText();
            _EditValue.SetText(ItemText);
            TriggerItemSelectedEvent();
        }
    }
}
