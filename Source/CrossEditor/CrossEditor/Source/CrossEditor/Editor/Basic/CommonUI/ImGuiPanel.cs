using Clicross;
using EditorUI;

namespace CrossEditor
{
    public class ImGuiPanel
    {
        public EditorImGuiContext Context;
        public Panel Panel;
        public SystemCursor Cursor = SystemCursor.Arrow;

        public ImGuiPanel(EditorImGuiContext InContext)
        {
            Context = InContext;
            Panel = new Panel();

            Panel.LeftMouseDownEvent += (Control sender, int x, int y, ref bool bContinue) =>
            {
                Context.OnKeyEvent((EditorKey)Key.LeftButton, true);
                Panel.GetUIManager().SetFocusControl(Panel);
                sender.CaptureMouse();
                bContinue = false;
            };
            Panel.LeftMouseUpEvent += (Control sender, int x, int y, ref bool bContinue) =>
            {
                Context.OnKeyEvent((EditorKey)Key.LeftButton, false);
                sender.ReleaseMouse();
                bContinue = false;
            };
            Panel.RightMouseDownEvent += (Control sender, int x, int y, ref bool bContinue) =>
            {
                Context.OnKeyEvent((EditorKey)Key.RightButton, true);
                Panel.GetUIManager().SetFocusControl(Panel);
                sender.CaptureMouse();
                bContinue = false;
            };
            Panel.RightMouseUpEvent += (Control sender, int x, int y, ref bool bContinue) =>
            {
                Context.OnKeyEvent((EditorKey)Key.RightButton, false);
                sender.ReleaseMouse();
                bContinue = false;
            };
            Panel.MiddleMouseDownEvent += (Control sender, int x, int y, ref bool bContinue) =>
            {
                Context.OnKeyEvent((EditorKey)Key.MiddleButton, true);
                Panel.GetUIManager().SetFocusControl(Panel);
                sender.CaptureMouse();
                bContinue = false;
            };
            Panel.MiddleMouseUpEvent += (Control sender, int x, int y, ref bool bContinue) =>
            {
                Context.OnKeyEvent((EditorKey)Key.MiddleButton, false);
                sender.ReleaseMouse();
                bContinue = false;
            };
            Panel.MouseMoveEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) => Context.OnMouseMoveEvent(MouseX - Panel.GetScreenX(), MouseY - Panel.GetScreenY());
            Panel.MouseWheelEvent += (Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue) => Context.OnMouseWheelEvent(MouseDeltaZ);
            Panel.KeyDownEvent += (Control Sender, Key Key, ref bool bContinue) => Context.OnKeyEvent((EditorKey)Key, true);
            Panel.KeyUpEvent += (Control Sender, Key Key, ref bool bContinue) => Context.OnKeyEvent((EditorKey)Key, false);
            Panel.CharInputEvent += (Control Sender, char Char, ref bool bContinue) =>
            {
                Context.OnInputCharacter(Char);
            };
            Panel.SetCursorEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                var device = Panel.GetUIManager().GetDevice();
                device.SetCursor(device.GetSystemCursor(Cursor));
                bContinue = true;
            };
            Panel.PaintEvent += (Control Sender) =>
            {
                var renderInterface = (Panel.GetUIManager().GetDevice().GetEditorUICanvas() as EditorUICanvas).GetUIRenderInterface();
                Context.Paint(new Clicross.Float2(Panel.GetScreenX(), Panel.GetScreenY()), new Clicross.Float2(Panel.GetWidth(), Panel.GetHeight()), renderInterface);
            };
            Panel.UpdateEvent += (Control Sender, int TimeElapsed) =>
            {
                Context.Update(new Clicross.Float2(Panel.GetScreenX(), Panel.GetScreenY()), new Clicross.Float2(Panel.GetWidth(), Panel.GetHeight()));
            };
        }
    }
}
