using EditorUI;

namespace CrossEditor
{
    public class FrameUI
    {
        bool _bSimple;

        Panel _PanelCaption;
        Panel _PanelApplication;
        Label _LabelTitle;

        Button _ButtonMinimize;
        Button _ButtonMaximize;
        Button _ButtonRestore;
        Button _ButtonClose;

        Panel _PanelStatusBar;
        ProgressBar _ProgressStatusBar;
        Label _LabelStatusBar1;
        Label _LabelStatusBar2;
        bool _ShowProgress;

        Panel _PanelRight;
        Panel _PanelBottom;
        Panel _PanelLeft;
        Panel _PanelTop;
        Panel _PanelTopRight;
        Panel _PanelBottomRight;
        Panel _PanelTopLeft;
        Panel _PanelBottomLeft;

        public FrameUI()
        {
            _bSimple = false;
            _ShowProgress = false;
        }

        public bool Initialize(UIManager UIManager, string Title, bool bTitleRightAlign, bool InSimple)
        {
            _bSimple = InSimple;

            Release();

            Control Root = UIManager.GetRoot();
            Root.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            Root.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);

            _PanelCaption = new Panel();
            _PanelCaption.Initialize();

            _PanelCaption.SetHitTestType(WindowHitTestType.Caption);
            _PanelCaption.LeftMouseDoubleClickedEvent += OnPanelCaptionLeftMouseDoubleClicked;
            Root.AddChild(_PanelCaption);

            _PanelApplication = new Panel();
            _PanelApplication.Initialize();
            _PanelApplication.SetPosition(8, 4, 24, 24);
            _PanelApplication.SetImage(UIManager.LoadUIImage("Editor/Frame/PanelApplication.png"));
            Root.AddChild(_PanelApplication);

            _LabelTitle = new Label();
            _LabelTitle.Initialize();
            _LabelTitle.SetPosition(40, 8, 1000, 23);
            _LabelTitle.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            if (bTitleRightAlign)
            {
                _LabelTitle.SetTextAlign(TextAlign.CenterRight);
            }
            else
            {
                _LabelTitle.SetTextAlign(TextAlign.CenterLeft);
            }
            _LabelTitle.SetTextColor(Color.EDITOR_UI_GENERAL_TEXT_COLOR);
            _LabelTitle.SetText(Title);
            Root.AddChild(_LabelTitle);

            _ButtonMinimize = new Button();
            _ButtonMinimize.Initialize();
            _ButtonMinimize.SetImage(UIManager.LoadUIImage("Editor/Frame/ButtonMinimize.png"));
            _ButtonMinimize.ClickedEvent += OnButtonMinimizeClicked;
            Root.AddChild(_ButtonMinimize);

            _ButtonMaximize = new Button();
            _ButtonMaximize.Initialize();
            _ButtonMaximize.SetImage(UIManager.LoadUIImage("Editor/Frame/ButtonMaximize.png"));
            _ButtonMaximize.ClickedEvent += OnButtonMaximizeClicked;
            if (!_bSimple)
            {
                Root.AddChild(_ButtonMaximize);
            }

            _ButtonRestore = new Button();
            _ButtonRestore.Initialize();
            _ButtonRestore.SetImage(UIManager.LoadUIImage("Editor/Frame/ButtonRestore.png"));
            _ButtonRestore.ClickedEvent += OnButtonRestoreClicked;
            if (!_bSimple)
            {
                Root.AddChild(_ButtonRestore);
            }

            _ButtonRestore.SetVisible(false);

            _ButtonClose = new Button();
            _ButtonClose.Initialize();
            _ButtonClose.SetImage(UIManager.LoadUIImage("Editor/Frame/ButtonClose.png"));
            _ButtonClose.ClickedEvent += OnButtonCloseClicked;
            Root.AddChild(_ButtonClose);

            _PanelStatusBar = new Panel();
            _PanelStatusBar.Initialize();
            if (!_bSimple)
            {
                Root.AddChild(_PanelStatusBar);
            }

            _ProgressStatusBar = new ProgressBar();
            _ProgressStatusBar.Initialize();
            if (!_bSimple)
            {
                _PanelStatusBar.AddChild(_ProgressStatusBar);
            }

            _LabelStatusBar1 = new Label();
            _LabelStatusBar1.Initialize();
            _LabelStatusBar1.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelStatusBar1.SetText("Ready");
            _LabelStatusBar1.SetTextAlign(TextAlign.CenterLeft);
            _LabelStatusBar1.SetTextColor(Color.FromRGBA(255, 255, 255, 255));
            _LabelStatusBar1.SetTextOffsetX(3);
            _LabelStatusBar1.SetTextOffsetY(2);
            if (!_bSimple)
            {
                _PanelStatusBar.AddChild(_LabelStatusBar1);
            }

            _LabelStatusBar2 = new Label();
            _LabelStatusBar2.Initialize();
            _LabelStatusBar2.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelStatusBar2.SetText("");
            _LabelStatusBar2.SetTextAlign(TextAlign.CenterRight);
            _LabelStatusBar2.SetTextColor(Color.FromRGBA(255, 255, 255, 255));
            _LabelStatusBar2.SetTextOffsetX(3);
            _LabelStatusBar2.SetTextOffsetY(2);
            if (!_bSimple)
            {
                _PanelStatusBar.AddChild(_LabelStatusBar2);
            }

            _PanelRight = new Panel();
            _PanelRight.Initialize();
            _PanelRight.SetHitTestType(WindowHitTestType.Right);
            if (!_bSimple)
            {
                Root.AddChild(_PanelRight);
            }

            _PanelBottom = new Panel();
            _PanelBottom.Initialize();
            _PanelBottom.SetHitTestType(WindowHitTestType.Bottom);
            if (!_bSimple)
            {
                Root.AddChild(_PanelBottom);
            }

            _PanelLeft = new Panel();
            _PanelLeft.Initialize();
            _PanelLeft.SetHitTestType(WindowHitTestType.Left);
            if (!_bSimple)
            {
                Root.AddChild(_PanelLeft);
            }

            _PanelTop = new Panel();
            _PanelTop.Initialize();
            _PanelTop.SetHitTestType(WindowHitTestType.Top);
            if (!_bSimple)
            {
                Root.AddChild(_PanelTop);
            }

            _PanelTopRight = new Panel();
            _PanelTopRight.Initialize();
            _PanelTopRight.SetHitTestType(WindowHitTestType.TopRight);
            if (!_bSimple)
            {
                Root.AddChild(_PanelTopRight);
            }

            _PanelBottomRight = new Panel();
            _PanelBottomRight.Initialize();
            _PanelBottomRight.SetImage(UIManager.LoadUIImage("Editor/Frame/PanelBottomRight.png"));
            _PanelBottomRight.SetHitTestType(WindowHitTestType.BottomRight);
            if (!_bSimple)
            {
                Root.AddChild(_PanelBottomRight);
            }

            _PanelTopLeft = new Panel();
            _PanelTopLeft.Initialize();
            _PanelTopLeft.SetHitTestType(WindowHitTestType.TopLeft);
            if (!_bSimple)
            {
                Root.AddChild(_PanelTopLeft);
            }

            _PanelBottomLeft = new Panel();
            _PanelBottomLeft.Initialize();
            _PanelBottomLeft.SetHitTestType(WindowHitTestType.BottomLeft);
            if (!_bSimple)
            {
                Root.AddChild(_PanelBottomLeft);
            }

            return true;
        }

        public UIManager GetUIManager()
        {
            if (_PanelCaption != null)
            {
                return _PanelCaption.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        void Release()
        {
            _PanelCaption = null;
            _PanelApplication = null;
            _LabelTitle = null;
            _ButtonMinimize = null;
            _ButtonMaximize = null;
            _ButtonRestore = null;
            _ButtonClose = null;
            _PanelStatusBar = null;
            _LabelStatusBar1 = null;
            _LabelStatusBar2 = null;
            _PanelRight = null;
            _PanelBottom = null;
            _PanelLeft = null;
            _PanelTop = null;
            _PanelTopRight = null;
            _PanelBottomRight = null;
            _PanelTopLeft = null;
            _PanelBottomLeft = null;
        }

        public void Update()
        {
            UpdateBorderColor();
        }

        public void SetTitle(string Title)
        {
            _LabelTitle.SetText(Title);
        }

        public void SetStatusBarText1(string Text)
        {
            _LabelStatusBar1.SetText(Text);
        }

        public string GetStatusBarText1()
        {
            return _LabelStatusBar1.GetText();
        }

        public void SetShowStatusProgress(bool show)
        {
            if (_ShowProgress != show)
            {
                _ShowProgress = show;
                UpdateStatusBar();
            }
        }

        public void SetStatusBarText1Color(Color TextColor)
        {
            _LabelStatusBar1.SetTextColor(TextColor);
            _ProgressStatusBar.SetBarColor(TextColor);
        }

        public void SetStatusBarProgress(float progress)
        {
            _ProgressStatusBar.SetProgress(progress);
        }

        public Color GetStatusBarText1Color()
        {
            return _LabelStatusBar1.GetTextColor();
        }

        public Label GetLabelStatusBar1()
        {
            return _LabelStatusBar1;
        }

        public void SetStatusBarText2(string Text)
        {
            int FontSize = _LabelStatusBar2.GetFontSize();
            Font Font = GetUIManager().GetDefaultFont(FontSize);
            int Width = Font.MeasureString_Fast(Text) + 6;
            _LabelStatusBar2.SetWidth(Width);
            _LabelStatusBar2.SetText(Text);
        }

        public Label GetLabelStatusBar2()
        {
            return _LabelStatusBar2;
        }

        public Panel GetPanelStatusBar()
        {
            return _PanelStatusBar;
        }

        public void OnDeviceResize(int ScreenWidth, int ScreenHeight)
        {
            if (_LabelTitle == null)
            {
                return;
            }

            int BorderSize1 = 4;
            int BorderSize2 = 6;
            int BorderSize3 = 20;

            int ButtonWidth1 = 34;
            int ButtonHeight1 = 26;

            _LabelTitle.SetWidth(ScreenWidth - ButtonWidth1 * 3 - _LabelTitle.GetX() - 2 - 20);

            if (!_bSimple)
            {
                _ButtonMinimize.SetPosition(ScreenWidth - ButtonWidth1 * 3 - 1, 1, ButtonWidth1, ButtonHeight1);
            }
            else
            {
                _ButtonMinimize.SetPosition(ScreenWidth - ButtonWidth1 * 2 - 1, 1, ButtonWidth1, ButtonHeight1);
            }
            _ButtonMaximize.SetPosition(ScreenWidth - ButtonWidth1 * 2 - 1, 1, ButtonWidth1, ButtonHeight1);
            _ButtonRestore.SetPosition(ScreenWidth - ButtonWidth1 * 2 - 1, 1, ButtonWidth1, ButtonHeight1);
            _ButtonClose.SetPosition(ScreenWidth - ButtonWidth1 - 1, 1, ButtonWidth1, ButtonHeight1);

            _PanelCaption.SetPosition(0, 0, ScreenWidth, 32);

            _PanelRight.SetPosition(ScreenWidth - BorderSize1, 0, BorderSize1, ScreenHeight);
            _PanelBottom.SetPosition(0, ScreenHeight - BorderSize1, ScreenWidth, BorderSize1);
            _PanelLeft.SetPosition(0, 0, BorderSize1, ScreenHeight);
            _PanelTop.SetPosition(0, 0, ScreenWidth, BorderSize1);
            _PanelTopRight.SetPosition(ScreenWidth - BorderSize2, 0, BorderSize2, BorderSize2);
            _PanelBottomRight.SetPosition(ScreenWidth - BorderSize3, ScreenHeight - BorderSize3, BorderSize3, BorderSize3);
            _PanelTopLeft.SetPosition(0, 0, BorderSize2, BorderSize2);
            _PanelBottomLeft.SetPosition(0, ScreenHeight - BorderSize2, BorderSize2, BorderSize2);

            int StatusBarHeight = 25;
            bool bDeviceMaximized = GetDevice().IsMaximized();
            if (bDeviceMaximized)
            {
                _PanelStatusBar.SetPosition(0, ScreenHeight - StatusBarHeight, ScreenWidth, StatusBarHeight);
            }
            else
            {
                _PanelStatusBar.SetPosition(1, ScreenHeight - StatusBarHeight - 1, ScreenWidth - 2, StatusBarHeight);
            }

            UpdateStatusBar();

            if (bDeviceMaximized)
            {
                _PanelBottomRight.SetVisible(false);
                _ButtonMaximize.SetVisible(false);
                _ButtonRestore.SetVisible(true);
            }
            else
            {
                _PanelBottomRight.SetVisible(true);
                _ButtonMaximize.SetVisible(true);
                _ButtonRestore.SetVisible(false);
            }
        }

        public void OnDeviceActivate(bool bActivated)
        {

        }

        void OnPanelCaptionLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Device Device = GetDevice();
            if (_bSimple)
            {
                return;
            }
            if (Device.IsMaximized() == false)
            {
                DeferredResizeTasks.Instance.Add(() =>
                {
                    Device.ShowMaximize();
                });
            }
            else
            {
                DeferredResizeTasks.Instance.Add(() =>
                {
                    Device.ShowNormal();
                });
            }
            bContinue = false;
        }

        void OnButtonCloseClicked(Button Sender)
        {
            GetDevice().Close();
        }

        void OnButtonMinimizeClicked(Button Sender)
        {
            SceneRuntime.GetInstance().Resize(0, 0);
            GetDevice().ShowMinimize();
        }

        void OnButtonMaximizeClicked(Button Sender)
        {
            DeferredResizeTasks.Instance.Add(() =>
            {
                GetDevice().ShowMaximize();
                _ButtonRestore.SetVisible(true);
                _ButtonMaximize.SetVisible(false);
            });

        }

        void OnButtonRestoreClicked(Button Sender)
        {
            DeferredResizeTasks.Instance.Add(() =>
            {
                GetDevice().ShowNormal();
                _ButtonRestore.SetVisible(false);
                _ButtonMaximize.SetVisible(true);
            });

        }

        Color GetBorderColor()
        {
            return Color.EDITOR_UI_HILIGHT_COLOR_BLUE;
        }

        void UpdateBorderColor()
        {
            UIManager UIManager = GetUIManager();
            Control Root = UIManager.GetRoot();
            bool bDeviceMaximized = GetDevice().IsMaximized();
            if (bDeviceMaximized)
            {
                Root.SetBorderColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            }
            else
            {
                bool bActivated = GetDevice().GetActivated();
                if (bActivated)
                {
                    Root.SetBorderColor(GetBorderColor());
                }
                else
                {
                    Root.SetBorderColor(Color.FromRGBA(66, 66, 69, 255));
                }
            }
        }

        void UpdateStatusBar()
        {
            int PanelStatusBarWidth = _PanelStatusBar.GetWidth();
            int PanelStatusBarHeight = _PanelStatusBar.GetHeight();

            _ProgressStatusBar.SetVisible(_ShowProgress);
            if (_ShowProgress)
            {
                _ProgressStatusBar.SetPosition(0, 2, PanelStatusBarWidth / 10, PanelStatusBarHeight - 4);
                _LabelStatusBar1.SetPosition(PanelStatusBarWidth / 10, 0, PanelStatusBarWidth * 9 / 10, PanelStatusBarHeight);
            }
            else
            {
                _LabelStatusBar1.SetPosition(0, 0, PanelStatusBarWidth, PanelStatusBarHeight);
            }

            int LabelStatusBar2Width = _LabelStatusBar2.GetWidth();
            int LabelStatusBar2X = PanelStatusBarWidth - _PanelBottomRight.GetWidth() - LabelStatusBar2Width;
            _LabelStatusBar2.SetPosition(LabelStatusBar2X, 0, LabelStatusBar2Width, PanelStatusBarHeight);
        }
    }
}
