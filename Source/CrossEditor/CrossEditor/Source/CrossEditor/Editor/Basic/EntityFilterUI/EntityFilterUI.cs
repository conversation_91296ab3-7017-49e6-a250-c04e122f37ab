using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    delegate void EntitySelectCallback(Entity e);
    delegate void EntitySelectFunc(ref List<Entity> entityList);

    class EntityFilterUI
    {
        static EntityFilterUI _Instance = new EntityFilterUI();

        BackgroundMask _BackgroundMask;
        Panel _Panel;

        Edit _EditPattern;

        ScrollView _ScrollView;
        Panel _ScrollPanel;

        List<Entity> _EntityList;
        List<Entity> _FilteredEntityList;

        EntitySelectCallback _EntitySelectCallback;
        EntitySelectFunc _EntitySelectFunc;

        public static EntityFilterUI GetInstance()
        {
            return _Instance;
        }

        EntityFilterUI()
        {
            _EntityList = new List<Entity>();
            _FilteredEntityList = new List<Entity>();
        }

        void InitializeUI(UIManager UIManager)
        {
            Control Root = UIManager.GetRoot();

            _BackgroundMask = new BackgroundMask(UIManager);
            _BackgroundMask.Mask(OnBackgroundMaskClose);

            int Width = 800;

            _Panel = new Panel();
            _Panel.SetBackgroundColor(Color.EDITOR_UI_MENU_BACK_COLOR);
            _Panel.SetBorderColor(Color.EDITOR_UI_MENU_HILIGHT_COLOR);
            _Panel.SetSize(Width, 400);
            _Panel.LeftMouseDownEvent += OnPanelLeftMouseDown;
            _Panel.MouseMoveEvent += OnPanelMouseMove;
            _Panel.SetVisible(false);
            Root.AddChild(_Panel);

            _EditPattern = new Edit();
            _EditPattern.SetFontSize(18);
            _EditPattern.Initialize(EditMode.Simple_SingleLine);
            _EditPattern.LoadSource("");
            _EditPattern.CharInputedEvent += OnEditPatternCharInpupted;
            _EditPattern.KeyDownEvent += OnEditPatternKeyDown;
            _Panel.AddChild(_EditPattern);
            EditContextUI.GetInstance().RegisterEdit(_EditPattern);
            _EditPattern.SetPosition(5, 5, Width - 10, 18);
            _EditPattern.SetFocus();

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.SetPosition(5, 28, Width - 10, 367);
            _Panel.AddChild(_ScrollView);

            _ScrollPanel = _ScrollView.GetScrollPanel();

            UpdateEntityList();
            FilterEntityList();
            UpdateEntityListUI();
        }

        public UIManager GetUIManager()
        {
            if (_Panel != null)
            {
                return _Panel.GetUIManager();
            }
            else
            {
                return UIManager.GetActiveUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        void CollectEntitys(string Directory)
        {
        }

        void UpdateEntityList()
        {
            _EntityList.Clear();
            _EntitySelectFunc(ref _EntityList);
        }

        void FilterEntityList()
        {
            _FilteredEntityList.Clear();
            string Pattern = _EditPattern.GetText();
            foreach (Entity e in _EntityList)
            {
                var name = e.GetName();
                if (Pattern == "" ||
                    name.Contains(Pattern, StringComparison.OrdinalIgnoreCase))
                {
                    _FilteredEntityList.Add(e);
                }
            }
        }

        void UpdateEntityListUI()
        {
            _ScrollPanel.ClearChildren();
            int Y = 3;
            for (int i = 0; i < _FilteredEntityList.Count; i++)
            {
                string EntityName = _FilteredEntityList[i].GetName();

                int Width = _ScrollView.GetWidth();

                Button ButtonEntity = new Button();
                ButtonEntity.Initialize();
                ButtonEntity.SetPosition(0, Y, Width, 64);
                ButtonEntity.SetFontSize(16);
                ButtonEntity.SetTextAlign(TextAlign.CenterLeft);
                ButtonEntity.SetTagString1(EntityName);
                ButtonEntity.SetTagObject(_FilteredEntityList[i]);
                ButtonEntity.SetDownColor(Color.EDITOR_UI_MENU_BACK_COLOR);
                ButtonEntity.ClickedEvent += OnButtonEntityClicked;
                //ButtonEntity.RightMouseUpEvent += OnButtonEntityRightMouseUp;
                _ScrollPanel.AddChild(ButtonEntity);

                Panel PanelEntity = new Panel();
                PanelEntity.SetBackgroundColor(new Color(0.3f, 0.3f, 0.3f, 1.0f));
                PanelEntity.SetTagString1(EntityName);
                PanelEntity.SetPosition(0, Y, 64, 64);
                _ScrollPanel.AddChild(PanelEntity);

                int X1 = 64 + 3;
                int Width1 = Width - X1 - 3;

                Label LabelEntityName = new Label();
                LabelEntityName.Initialize();
                LabelEntityName.SetPosition(X1, Y + 16, Width1, 18);
                LabelEntityName.SetText(EntityName);
                LabelEntityName.SetTextAlign(TextAlign.CenterLeft);
                LabelEntityName.SetFontSize(18);
                _ScrollPanel.AddChild(LabelEntityName);

                Label LabelEntityFilename = new Label();
                LabelEntityFilename.Initialize();
                LabelEntityFilename.SetPosition(X1, Y + 38, Width1, 14);
                LabelEntityFilename.SetText(EntityName);
                LabelEntityFilename.SetTextAlign(TextAlign.CenterLeft);
                LabelEntityFilename.SetFontSize(14);
                _ScrollPanel.AddChild(LabelEntityFilename);

                Y += 64 + 3;
            }
            _ScrollPanel.SetSize(_ScrollView.GetWidth() - 22, Y);
            _ScrollView.UpdateScrollBar();
        }

        public void ShowUI(int X, int Y, int Width, int Height, EntitySelectCallback EntitySelectCallback, EntitySelectFunc EntitySelectFunc)
        {
            _EntitySelectCallback = EntitySelectCallback;
            _EntitySelectFunc = EntitySelectFunc;
            int X1 = X;
            int Y1 = Y + 1;
            int X2 = X + Width;
            int Y2 = Y + Height - 1;
            int MenuX = 0;
            int MenuY = 0;

            Control Root = GetUIManager().GetRoot();

            InitializeUI(GetUIManager());

            int MenuWidth = _Panel.GetWidth();
            int MenuHeight = _Panel.GetHeight();

            int RootWidth = Root.GetWidth();
            int RootHeight = Root.GetHeight();
            MenuX = X1;
            if (X1 + MenuWidth > RootWidth)
            {
                if (X2 - MenuWidth >= 0)
                {
                    MenuX = X2 - MenuWidth;
                }
                else
                {
                    MenuX = RootWidth - MenuWidth;
                }
                if (MenuX < 0)
                {
                    MenuX = 0;
                }
            }
            MenuY = Y2;
            if (Y2 + MenuHeight > RootHeight)
            {
                if (Y1 - MenuHeight >= 0)
                {
                    MenuY = Y1 - MenuHeight;
                }
                else
                {
                    MenuY = RootHeight - MenuHeight;
                }
                if (MenuY < 0)
                {
                    MenuY = 0;
                }
            }

            _Panel.SetPos(MenuX, MenuY);
            _Panel.SetVisible(true);
        }

        public void ShowUI(Control Control, EntitySelectCallback EntitySelectCallback, EntitySelectFunc EntitySelectFunc)
        {
            int X = Control.GetScreenX();
            int Y = Control.GetScreenY();
            int Width = Control.GetWidth();
            int Height = Control.GetHeight();
            ShowUI(X, Y, Width, Height, EntitySelectCallback, EntitySelectFunc);
        }

        public void HideUI()
        {
            OperationQueue.GetInstance().AddOperation(() =>
            {
                _BackgroundMask.Close();
                if (_Panel != null)
                {
                    UIManager UIManager = GetUIManager();
                    Control Root = UIManager.GetRoot();
                    _Panel.SetVisible(false);
                    Root.RemoveChild(_Panel);
                    _Panel = null;
                }
            });
        }

        public bool GetVisible()
        {
            if (_Panel != null)
            {
                return _Panel.GetVisible();
            }
            return false;
        }

        void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY))
            {
                bContinue = false;
            }
        }

        void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY))
            {
                bContinue = false;
            }
        }

        void OnEditPatternCharInpupted(Control Sender, char Char, ref bool bContinue)
        {
            FilterEntityList();
            UpdateEntityListUI();
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && (Char == '\r' || Char == '\n'))
            {
                if (_FilteredEntityList.Count == 1)
                {
                    Entity e = _FilteredEntityList[0];
                    TriggerSelection(e);
                }
            }
        }

        void OnEditPatternKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && Key == Key.Escape)
            {
                HideUI();
            }
        }

        void OnButtonEntityClicked(Button Sender)
        {
            Button ButtonEntity = Sender;
            Entity e = ButtonEntity.GetTagObject() as Entity;
            TriggerSelection(e);
        }

        void TriggerSelection(Entity e)
        {
            if (e != null)
            {
                if (_EntitySelectCallback != null)
                {
                    _EntitySelectCallback(e);
                }
            }
            HideUI();
        }

        void OnBackgroundMaskClose(BackgroundMask Sender)
        {
            HideUI();
        }
    }
}