using CEngine;
using System;
using System.IO;
using System.Reflection;
using System.Xml;
using System.Xml.Serialization;

namespace CrossEditor
{
    [XmlRoot]
    public class GlobalConfig
    {
        [XmlElement("EnginePath")]
        [PropertyInfo(PropertyType = "StringAsFolder", ToolTips = "EnginePath")]
        public string EnginePath { set; get; }

        [XmlElement("AndroidSDK")]
        [PropertyInfo(PropertyType = "StringAsFolder", ToolTips = "AndroidSDK")]
        public string AndroidSDK { set; get; }

        [XmlElement("AndroidNDK")]
        [PropertyInfo(PropertyType = "StringAsFolder", ToolTips = "AndroidNDK")]
        public string AndroidNDK { set; get; }

        public const string GLOBAL_CONFIG_FILENAME = EditorConfig.EDITOR_CONFIG_RELATIVE_PATH + "GlobalConfig.config";

        public GlobalConfig()
        {
            var AD_SDK = Environment.GetEnvironmentVariable("ANDROID_SDK");
            var AD_NDK = Environment.GetEnvironmentVariable("ANDROID_NDK");

            if (!string.IsNullOrEmpty(AD_SDK))
            {
                AndroidSDK = AD_SDK;
            }
            else
            {
                if (SceneRuntime.IsUnix)
                    AndroidSDK = string.Format("{0}/Library/Android/sdk", Environment.GetEnvironmentVariable("HOME"));
                else
                    AndroidSDK = string.Format("{0}/Android/sdk", Environment.GetEnvironmentVariable("LOCALAPPDATA"));
            }

            if (!string.IsNullOrEmpty(AD_NDK))
            {
                AndroidNDK = AD_NDK;
            }
            else if (!string.IsNullOrEmpty(AndroidSDK))
            {
                const string DEFAULT_NDK = "20.0.5594570";
                AndroidNDK = string.Format("{0}/ndk/{1}", AndroidSDK, DEFAULT_NDK);
            }

            EnginePath = new DirectoryInfo("../../../..").FullName.Replace("\\", "/");
        }

        public void Load()
        {
            if (File.Exists(GLOBAL_CONFIG_FILENAME) == false)
            {
                return;
            }
            XmlSerializer Ser = new XmlSerializer(typeof(GlobalConfig));

            using (StreamReader Reader = new StreamReader(GLOBAL_CONFIG_FILENAME))
            {
                GlobalConfig config = (GlobalConfig)Ser.Deserialize(Reader);
                if (Directory.Exists(config.EnginePath)) EnginePath = config.EnginePath;
                if (Directory.Exists(config.AndroidSDK)) AndroidSDK = config.AndroidSDK;
                if (Directory.Exists(config.AndroidNDK)) AndroidNDK = config.AndroidNDK;
                Reader.Close();
            }
        }

        public void Dump()
        {
            XmlSerializer Ser = new XmlSerializer(typeof(GlobalConfig));
            using (StreamWriter Writer = new StreamWriter(GLOBAL_CONFIG_FILENAME))
            {
                Ser.Serialize(Writer, this);
                Writer.Close();
            }
        }
    }
}