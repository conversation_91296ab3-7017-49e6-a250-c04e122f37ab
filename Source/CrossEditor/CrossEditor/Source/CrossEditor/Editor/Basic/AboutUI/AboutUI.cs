using EditorUI;

namespace CrossEditor
{
    public class AboutUI : DialogUI
    {
        Label _LabelContent;

        public AboutUI()
        {
        }

        public void Initialize(UIManager UIManager)
        {
            base.Initialize(UIManager, "About", 910, 300);

            string Text = "Cross Engine (C) 2019 - 2022, Tencent, All Rights Reserved.\n\n";
            Text += "Cross Engine Team, ENGINE, RED, CROS, IEG, Tencent\n\n";

            _LabelContent = new Label();
            _LabelContent.SetPosition(120, 100, 710, 200);
            _LabelContent.SetMultiLine(true);
            _LabelContent.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelContent.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _LabelContent.SetText(Text);
            _PanelDialog.AddChild(_LabelContent);
        }
    }
}
