using CEngine;
using EditorUI;

namespace CrossEditor
{
    public delegate void CreateMaterialUIInputedEventHandler(CreateMaterialUI Sender, string Shader);

    public class CreateMaterialUI : DialogUI
    {
        Label _LabelTips;
        Edit _EditShader;
        Button _ButtonBrowse;
        string _DefaultImagePath = "";

        public event CreateMaterialUIInputedEventHandler InputedEvent;

        public CreateMaterialUI()
        {
        }

        public void InitializeFromFx(UIManager UIManager, string Title, string fx)
        {
            Initialize(UIManager, Title);
            _EditShader.LoadSource(fx);
            _DefaultImagePath = "";
            _LabelTips.SetText("Fx:");
        }

        public void InitializeFromMaterial(UIManager UIManager, string Title, string material)
        {
            Initialize(UIManager, Title);
            _EditShader.LoadSource(material);
            _DefaultImagePath = "";
            _LabelTips.SetText("Material:");
        }

        public void InitializeFromImage(UIManager UIManager, string Title, string image_path)
        {
            Initialize(UIManager, Title);
            var settingMgr = CrossEngineApi.GetSettingManager();
            var setting = settingMgr.GetRenderPipelineSettingForEditor();
            _EditShader.LoadSource(setting.DefaultFX);
            _DefaultImagePath = image_path;
            _LabelTips.SetText("Image:");
        }

        void Initialize(UIManager UIManager, string Title)
        {
            base.Initialize(UIManager, Title, 600, 200);

            _LabelTips = new Label();
            _LabelTips.SetPosition(30, 80, 500, 16);
            _LabelTips.SetFontSize(16);
            _LabelTips.SetTextAlign(TextAlign.CenterLeft);
            _PanelDialog.AddChild(_LabelTips);

            _EditShader = new Edit();
            _EditShader.SetFontSize(14);
            _EditShader.Initialize(EditMode.Simple_SingleLine);
            _PanelDialog.AddChild(_EditShader);
            EditContextUI.GetInstance().RegisterEdit(_EditShader);
            _EditShader.SetPosition(30, 100, 500, 14);

            _ButtonBrowse = new Button();
            _ButtonBrowse.Initialize();
            _ButtonBrowse.SetFontSize(12);
            _ButtonBrowse.SetText("...");
            _ButtonBrowse.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonBrowse.SetToolTips("Browse Shader");
            _ButtonBrowse.ClickedEvent += OnButtonBrowseClicked;
            _PanelDialog.AddChild(_ButtonBrowse);
            _ButtonBrowse.SetPosition(540, 100, 24, 16);

            Button ButtonOK = new Button();
            ButtonOK.Initialize();
            ButtonOK.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonOK.SetText("OK");
            ButtonOK.SetFontSize(16);
            ButtonOK.SetTextOffsetY(3);
            ButtonOK.ClickedEvent += OnButtonOKClicked;
            _PanelDialog.AddChild(ButtonOK);

            ButtonOK.SetSize(100, 24);
            ButtonOK.MakeCenterX();
            ButtonOK.SetY(150);
        }

        public string GetDefaultImage()
        {
            return _DefaultImagePath;
        }

        public void UpdateLayout()
        {

        }

        public override void ShowDialog()
        {
            base.ShowDialog();
        }

        public override void OnDeviceChar(Device Sender, char Char)
        {
            base.OnDeviceChar(Sender, Char);

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bNone = !bControl && !bShift && !bAlt;

            if (bNone && (Char == '\r' || Char == '\n'))
            {
                OnButtonOKClicked(null);
            }
        }

        void OnButtonBrowseClicked(Button Sender)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Shader File";
            PathInputUIFilterItem.Extensions.Add("nda");

            bool bContentsOnly = true;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.Initialize(GetUIManager(), "", PathInputUIType.OpenFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                string EditorFilename = PathInputed;
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(EditorFilename);
                _EditShader.SetText(StandardFilename);
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        void OnButtonOKClicked(Button Sender)
        {
            if (_EditShader == null)
            {
                return;
            }
            string Shader = _EditShader.GetText();
            Shader = EditorUtilities.EditorFilenameToStandardFilename(Shader);
            string Shader1 = EditorUtilities.StandardFilenameToEditorFilename(Shader);
            if (FileHelper.IsFileExists(Shader1) == false)
            {
                string Tips = string.Format("File: {0} not exists.", Shader);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string Extension = PathHelper.GetExtension(Shader1);
            if (StringHelper.IgnoreCaseEqual(Extension, ".nda") == false)
            {
                string Tips = string.Format("File: {0} is not a nda file.", Shader);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(Shader);
            if (ObjectClassID != ClassIDType.CLASS_GraphicsShader && ObjectClassID != ClassIDType.CLASS_Fx && ObjectClassID != ClassIDType.CLASS_Material)
            {
                string Tips = string.Format("File: {0} is not a shader asset.", Shader);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            CloseDialog();
            if (InputedEvent != null)
            {
                InputedEvent(this, Shader);
            }
        }
    }
}
