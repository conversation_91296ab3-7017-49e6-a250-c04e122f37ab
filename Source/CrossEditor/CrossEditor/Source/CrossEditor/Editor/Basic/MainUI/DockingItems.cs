using EditorUI;

namespace CrossEditor
{

    //public class DockingUIItems : DockingUI, IDockingUIItems
    //{
    //    public DockingBlock DockingBlock { set; get; }
    //    protected MenuItem _MenuItem = null;
    //    public DockingUIItems(string title = "")
    //    {
    //        _MenuItem = new MenuItem();
    //        if (title == "")
    //        {
    //            title = this.GetType().Name;
    //            _MenuItem.SetText(title);
    //        }
    //        _MenuItem.ClickedEvent += OnClick;
    //        DockingBlock = null;
    //    }


    //    public virtual bool Initialize(DockingBlock block)
    //    {
    //        DockingBlock = block;
    //        return true;
    //    }

    //    public virtual void OnClick(MenuItem sender)
    //    {
    //        MainUI.GetInstance().ActivateDockingCard(GetDockingCard(), DockingBlock);
    //    }

    //}





    public abstract class InspectorDockingBlockItems<InspectorType> : InspectorUI where InspectorType : Inspector, new()
    {

        protected object _InspectorObject = null;

        public InspectorDockingBlockItems(string title = "")
        {
            _MenuItem = new MenuItem();
            if (title == "")
            {
                title = this.GetType().Name;
            }
            _MenuItem.SetText(title);

            this.Initialize(title);
        }



        public bool Initialize()
        {
            InitializeInspectObject();

            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += InspectObject;
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };


            InspectObject();
            CollapseAll();
            GetInspector().SetCheckExpand(true);

            return true;
        }
        public abstract void InitializeInspectObject();

        public InspectorType GetTypedInsepctor()
        {
            return _Inspector as InspectorType;
        }

        public virtual void InitializeCustomItems() { }

        public new virtual void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 5;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
            }

            Y += 5;

            _ScrollPanel.SetSize(ScrollPanelWidth, Y);
            _ScrollView.UpdateScrollBar();
        }

        public new virtual void InspectObject()
        {

            _SearchUI.ClearSearchPattern();
            _ScrollPanel.ClearChildren();

            InitializeCustomItems();

            if (_InspectorObject != null)
            {
                _Inspector = new InspectorType();
                _Inspector.SetContainer(_ScrollPanel);
                _Inspector.SetInspectorHandler(_InspectorHandler);
                _Inspector.InspectObject(_InspectorObject);
                _Inspector.UpdateCheckExpand();
                SetInspector(_Inspector);

                UpdateInspector();
            }
            UpdateLayout();
        }

        public virtual void UpdateInspector() { }
    }

}
