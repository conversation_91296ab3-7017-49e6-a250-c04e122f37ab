using EditorUI;
using System.IO;

namespace CrossEditor
{
    public class EditorConfig
    {
        public const string EDITOR_CONFIG_RELATIVE_PATH = "Data/Config/";

        public const string EDITOR_CONFIG_FILENAME = EDITOR_CONFIG_RELATIVE_PATH + "EditorConfig.config";

        static EditorConfig _Instance = new EditorConfig();

        public bool bOpenLastScene;
        public int AverageFrames;
        public string LastProjectPath;
        public int AutoSaveMinutes;

        public static EditorConfig GetInstance()
        {
            return _Instance;
        }

        EditorConfig()
        {
            bOpenLastScene = false;
            AverageFrames = 20;
            LastProjectPath = "";
            AutoSaveMinutes = 0;
        }

        public void SaveEditorConfig()
        {
            string EditorConfigFilename = EDITOR_CONFIG_FILENAME;
            XmlScript Xml = new XmlScript();
            Record RootRecord = Xml.GetRootRecord();

            Record RecordEditorConfig = RootRecord.AddChild();
            RecordEditorConfig.SetTypeString("EditorConfig");

            RecordEditorConfig.SetBool("OpenLastScene", bOpenLastScene);
            RecordEditorConfig.SetInt("AverageFrames", AverageFrames);
            RecordEditorConfig.SetString("LastProjectPath", LastProjectPath);
            RecordEditorConfig.SetInt("AutoSaveMinutes", AutoSaveMinutes);

            Xml.Save(EditorConfigFilename);
        }

        public void LoadEditorConfig()
        {
            string EditorConfigFilename = EDITOR_CONFIG_FILENAME;
            if (File.Exists(EditorConfigFilename) == false)
            {
                return;
            }
            XmlScript Xml = new XmlScript();
            Xml.Open(EditorConfigFilename);
            Record RootRecord = Xml.GetRootRecord();

            Record RecordEditorConfig = RootRecord.FindByTypeString("EditorConfig");
            if (RecordEditorConfig != null)
            {
                bOpenLastScene = RecordEditorConfig.GetBool("OpenLastScene");
                AverageFrames = RecordEditorConfig.GetInt("AverageFrames");
                LastProjectPath = RecordEditorConfig.GetString("LastProjectPath");
                if (AverageFrames == 0)
                {
                    AverageFrames = FPSDisplayUI.DefaultAverageFrames;
                }
                AutoSaveMinutes = RecordEditorConfig.GetInt("AutoSaveMinutes");
            }

            FPSDisplayUI.GetInstance().SetAverageFrames(AverageFrames);
        }
    }
}
