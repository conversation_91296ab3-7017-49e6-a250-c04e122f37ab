using EditorUI;

namespace CrossEditor
{
    class FPSDisplayUI
    {
        static FPSDisplayUI _Instance = new FPSDisplayUI();

        public const int DefaultAverageFrames = 20;

        Label _LabelFPS;
        double _FPSValue;
        TimeElapsedRingBuffer _TimeElapsedRingBuffer;

        public static FPSDisplayUI GetInstance()
        {
            return _Instance;
        }

        FPSDisplayUI()
        {
            SetAverageFrames(DefaultAverageFrames);
        }

        public bool Initialize(UIManager UIManager)
        {
            Control Root = UIManager.GetRoot();

            _LabelFPS = new Label();
            _LabelFPS.Initialize();
            _LabelFPS.SetPosition(0, 0, 500, 20);
            _LabelFPS.SetFontSize(16);
            _LabelFPS.SetTextAlign(TextAlign.CenterRight);
            _LabelFPS.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _LabelFPS.SetText("FPS:60 16.6ms");
            _LabelFPS.SetTextOffsetY(4);
            Root.AddChild(_LabelFPS);

            return true;
        }

        public void OnDeviceResize(int Width, int Height)
        {
            int LabelFPSWidth = 200;
            _LabelFPS.SetPosition(Width - LabelFPSWidth - 2, 32, LabelFPSWidth, 23);
        }

        public void Update(long TimeElapsed)
        {
            _TimeElapsedRingBuffer.Put((double)TimeElapsed);
            double AverageTimeElapsed = _TimeElapsedRingBuffer.CalculateAverageTimeElapsed();
            double FPS = 99;
            if (AverageTimeElapsed > 0.001)
            {
                FPS = (1000.0 / AverageTimeElapsed);
                _FPSValue = FPS;
            }
            TerrainEditor TerrainEditor = TerrainEditor.GetInstance();
            string FPSText = string.Format("{0:0.0}FPS {1:0.0}ms", FPS, AverageTimeElapsed);
            _LabelFPS.SetText(FPSText);
        }

        public void SetAverageFrames(int AverageFrames)
        {
            _TimeElapsedRingBuffer = new TimeElapsedRingBuffer(AverageFrames);
        }

        public double GetFPS()
        {
            return _FPSValue;
        }
    }
}
