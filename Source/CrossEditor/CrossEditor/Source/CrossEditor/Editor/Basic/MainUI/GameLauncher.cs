using EditorUI;
using System;

namespace CrossEditor
{
    class GameLauncher
    {
        static GameLauncher _Instance = new GameLauncher();

        public static GameLauncher GetInstance()
        {
            return _Instance;
        }

        public void LaunchGame(string ScenePath)
        {
            //SaveEngineLaunchJson(ScenePath);
            MainUI MainUI = MainUI.GetInstance();
            string ClientForEditorPath = GetClientForEditorPath();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            string ResourceDirectory = EditorUtilities.GetResourceDirectory();
            ProjectDirectory = string.Format("\"{0}\"", ProjectDirectory);
            ResourceDirectory = string.Format("\"{0}\"", ResourceDirectory);
            ScenePath = string.Format("\"{0}\"", ScenePath);
            if (Environment.OSVersion.Platform != PlatformID.Unix)
            {
                ClientForEditorPath = string.Format("\"{0}\"", ClientForEditorPath);
            }
            string Parameters = string.Format("--work_dir {0} --engine_resource {1} --launch_scene {2}", ProjectDirectory, ResourceDirectory, ScenePath);
            ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information, string.Format("Execute Command: {0} {1}", ClientForEditorPath, Parameters));
            ProcessHelper.Execute(ClientForEditorPath, Parameters);
        }

        public void LaunchGame()
        {
            MainUI MainUI = MainUI.GetInstance();
            string ClientForEditorPath = GetClientForEditorPath();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            string ResourceDirectory = EditorUtilities.GetResourceDirectory();
            ProjectDirectory = string.Format("\"{0}\"", ProjectDirectory);
            ResourceDirectory = string.Format("\"{0}\"", ResourceDirectory);
            if (Environment.OSVersion.Platform != PlatformID.Unix)
            {
                ClientForEditorPath = string.Format("\"{0}\"", ClientForEditorPath);
            }
            string Parameters = string.Format("--work_dir {0} --engine_resource {1}", ProjectDirectory, ResourceDirectory);
            ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information, string.Format("Execute Command: {0} {1}", ClientForEditorPath, Parameters));
            ProcessHelper.Execute(ClientForEditorPath, Parameters);
        }

        public static string GetClientForEditorPath()
        {
            string ClientForEditorPath = string.Format("{0}/{1}", GetClientForEditorDirectory(), "ClientForEditor");
            if (Environment.OSVersion.Platform == PlatformID.Unix)
            {
                ClientForEditorPath = string.Format("{0}{1}", ClientForEditorPath, ".app/Contents/MacOS/ClientForEditor");
            }
            else
            {
                ClientForEditorPath = string.Format("{0}{1}", ClientForEditorPath, ".exe");
            }
            return ClientForEditorPath;
        }

        public static string GetClientForEditorDirectory()
        {
            string ExecutableDirectory = DirectoryHelper.GetExecutableDirectory();
            return ExecutableDirectory;
        }

        void SaveEngineLaunchJson(string ScenePath)
        {
            string ScenePath1 = EditorUtilities.EditorFilenameToStandardFilename(ScenePath);
            string EngineLaunch = string.Format("{{\"Launch\":\"{0}\", \"PointLightCount\":4, \"RunNewEngine\": true}}", ScenePath1);

            MainUI MainUI = MainUI.GetInstance();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            string Filename = ProjectDirectory + "/EngineLaunch.json";
            FileHelper.WriteTextFile(Filename, EngineLaunch, TextFileFormat.ANSI);
        }
    }
}
