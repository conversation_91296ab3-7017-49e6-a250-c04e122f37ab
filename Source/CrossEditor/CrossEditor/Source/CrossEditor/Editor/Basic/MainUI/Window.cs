using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class Window
    {
        Device _Device;
        VideoDriver _VideoDriver;
        Graphics2D _Graphics2D;
        UIManager _UIManager;
        IntPtr _RenderWindowHandle;
        Clicross.IRenderWindow _RenderWindow;
        EditorUICanvas _EditorUICanvas;
        Panel _PanelApplication;
        FrameUI _FrameUI;
        DockingControl _DockingControl;

        public Window()
        {

        }

        public UIManager GetUIManager()
        {
            return _UIManager;
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public FrameUI GetFrameUI()
        {
            return _FrameUI;
        }

        public void Initialize(int X, int Y, int Width, int Height, Control Dragged)
        {
            _Device = DeviceManager.GetInstance().CreateDevice("New");
            VideoDriverType VideoDriverType = VideoDriverType.Null;
            string Icon = "Editor/Icons/TestIcon.ico";
            string Title = "Cross Engine";
            _VideoDriver = _Device.CreateBuiltinVideoDriver(VideoDriverType);

            if (_Device.IsPointInScreens(X, Y) == false &&
                _Device.IsPointInScreens(X + Width / 2, Y + Height / 2) == false &&
                _Device.IsPointInScreens(X + Width, Y + Height) == false)
            {
                X = 100;
                Y = 100;
                Width = 1280;
                Height = 720;
            }
            bool bSuccess = _Device.Initialize(_VideoDriver, Icon, Title, X, Y, Width, Height);
            if (Environment.OSVersion.Platform != PlatformID.Unix)
            {
                _Device.EnableRawInput();
            }

            _Graphics2D = new Graphics2D(_Device);
            _Graphics2D.Initialize();
            _UIManager = UIManagerList.GetInstance().CreateUIManager(_Device, _Graphics2D);
            _UIManager.Initialize();

            SceneRuntime SceneRuntime = SceneRuntime.GetInstance();

            _UIManager.RawMouseEvent += SceneRuntime.OnUIManagerRawMouse;
            _UIManager.RawKeyEvent += SceneRuntime.OnUIManagerRawKey;
            _UIManager.RawCharEvent += SceneRuntime.OnUIManagerRawChar;

            _RenderWindowHandle = _Device.GetNativeWindowPointer();

            CrossEngine CrossEngine = SceneRuntime.GetCrossEngine();
            int DeviceWidth = _Device.GetWidth();
            int DeviceHeight = _Device.GetHeight();
            float DPR = 1.0f;

            _RenderWindow = CrossEngine.CreateRenderWindow(_RenderWindowHandle, 0, 0, DeviceWidth, DeviceHeight, DPR, Title);

            _EditorUICanvas = new EditorUICanvas();
            _Device.SetEditorUICanvas(_EditorUICanvas);

            _EditorUICanvas.Initialize(OnEditorUI, _RenderWindow);
            _EditorUICanvas.SetSize(DeviceWidth, DeviceHeight);

            Control Root = _UIManager.GetRoot();
            Root.SetBackgroundColor(Color.FromRGB(255, 0, 0));

            _PanelApplication = new Panel();
            _PanelApplication.Initialize();
            _PanelApplication.SetPosition(8, 4, 24, 24);
            _PanelApplication.SetImage(UIManager.LoadUIImage("Editor/Frame/PanelApplication.png"));
            Root.AddChild(_PanelApplication);

            bool bTitleRightAlign = false;
            bool bSimple = false;
            _FrameUI = new FrameUI();
            _FrameUI.Initialize(_UIManager, _Device.GetTitle(), bTitleRightAlign, bSimple);

            _Device.ResizingEvent += (d, w, h) =>
            {
                CrossEngine.GetInstance().FlushRendering();
            };
            _Device.ResizeEvent += OnDeviceResize;
            _Device.ActivateEvent += OnDeviceActivate;
            _Device.DropFilesBeginEvent += OnDeviceDropFilesBegin;
            _Device.DropFilesFileEvent += OnDeviceDropFilesFile;
            _Device.DropFilesEndEvent += OnDeviceDropFilesEnd;
            _Device.DestroyEvent += OnDeviceDestroy;

            _DockingControl = new DockingControl();
            Root.AddChild(_DockingControl);
            _DockingControl.Initialize();
            _DockingControl.SetSize(1000, 1000);
            _DockingControl.LayoutedEvent += OnDockingControlLayout;
            _DockingControl.LoadDockingCardEvent += OnDockingControlLoadDockingCard;
            _DockingControl.NewWindowEvent += OnDockingControlNewWindow;

            DockingNode LayoutRoot = _DockingControl.GetDockingRoot();
            LayoutRoot.SetDockingDirection(DockingDirection.Horizontal);

            if (Dragged is DockingBlock)
            {
                DockingBlock SourceDockingBlock = (DockingBlock)Dragged;
                DockingControl OldDockingControl = SourceDockingBlock.GetDockingControl();
                DockingNode OldDockingRoot = OldDockingControl.GetDockingRoot();
                OldDockingRoot.RemoveDockingBlock(SourceDockingBlock);
                LayoutRoot.AddDockingBlock(SourceDockingBlock);
            }
            else if (Dragged is DockingCard)
            {
                DockingCard SourceDockingCard = (DockingCard)Dragged;
                DockingBlock OldDockingBlock = SourceDockingCard.GetDockingBlock();
                OldDockingBlock.RemoveDockingCard(SourceDockingCard);
                if (OldDockingBlock.GetDockingCardCount() == 0)
                {
                    DockingControl OldDockingControl = OldDockingBlock.GetDockingControl();
                    DockingNode OldDockingRoot = OldDockingControl.GetDockingRoot();
                    OldDockingRoot.RemoveDockingBlock(OldDockingBlock);
                }
                DockingBlock NewDockingBlock = new DockingBlock();
                NewDockingBlock.Initialize();
                if (SourceDockingCard.GetDocument())
                {
                    NewDockingBlock.SetDocument(true);
                }
                DockingNode NewDockingNode = LayoutRoot.AddDockingBlock(NewDockingBlock);
                NewDockingBlock.AddDockingCard(SourceDockingCard);
            }

            _Device.ShowNormal();
        }

        void RemoveDockingCardsAndBlocks()
        {
            DockingNode DockingRoot = _DockingControl.GetDockingRoot();
            List<DockingBlock> DockingBlocks = new List<DockingBlock>();
            DockingRoot.CollectDockingBlocks(DockingBlocks);
            foreach (DockingBlock DockingBlock in DockingBlocks)
            {
                DockingBlock.ClearDockingCards();
                DockingRoot.RemoveDockingBlock(DockingBlock);
            }
        }

        void OnDeviceDestroy(Device Sender)
        {
            RemoveDockingCardsAndBlocks();

            CrossEngine.GetInstance().FlushRendering();
            _EditorUICanvas.GetUIRenderInterface().Dispose();
            CrossEngine.GetInstance().DestroyWindow(_RenderWindow);
            _RenderWindow = null;
            WindowManager.GetInstance().RemoveWindow(this);
        }

        void OnEditorUI()
        {
            SceneRuntime.GetInstance().OnEditorUI();
        }

        void CloseWindowIfNoDockingCard()
        {
            int TotalDockingCardCount = 0;
            DockingNode DockingRoot = _DockingControl.GetDockingRoot();
            List<DockingBlock> DockingBlocks = new List<DockingBlock>();
            DockingRoot.CollectDockingBlocks(DockingBlocks);
            foreach (DockingBlock DockingBlock in DockingBlocks)
            {
                TotalDockingCardCount += DockingBlock.GetDockingCardCount();
            }
            if (TotalDockingCardCount == 0)
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    _Device.Close();
                });
            }
        }

        public void Update()
        {
            CloseWindowIfNoDockingCard();
            _FrameUI.Update();
            _UIManager.Update();
        }

        public void Paint()
        {
            if (!_Device.IsMinimized() && _RenderWindow != null)
            {
                _UIManager.Paint();
            }
        }

        public void OnDeviceResize(Device Sender, int Width, int Height)
        {
            bool bDeviceMinimized = GetDevice().IsMinimized();
            bool bDeviceMaximized = GetDevice().IsMaximized();

            _FrameUI.OnDeviceResize(Width, Height);
            ConsoleUI.GetInstance().OnDeviceResize(Width, Height);

            UIManager UIManager = GetUIManager();
            Control Root = UIManager.GetRoot();

            int TitleHeight = 28;
            int StatusBarHeight = 25;
            int StatusBarOffsetY = DockingControl.DOCKING_INTERVAL * 2 - 1;

            int DockingControlX = 0;
            int DockingControlY = TitleHeight;
            int DockingControlWidth = Root.GetWidth();
            int DockingControlHeight = Root.GetHeight() - DockingControlY - StatusBarHeight;
            DockingControlHeight += StatusBarOffsetY;

            if (_DockingControl != null)
            {
                _DockingControl.SetPosition(DockingControlX, DockingControlY, DockingControlWidth, DockingControlHeight);
            }

            UIManager.Invalidate();

            _EditorUICanvas.GetUIRenderInterface().SetSize(Width, Height);
            if (bDeviceMinimized)
            {
                _RenderWindow.Resize(0, 0, 0u);
            }
            else
            {
                _RenderWindow.Resize((uint)Width, (uint)Height, 0u);
            }
        }

        void OnDeviceActivate(Device Sender, bool bActivated)
        {
            _FrameUI.OnDeviceActivate(bActivated);
            GetUIManager().Invalidate();
        }

        void OnDeviceDropFilesBegin(Device Sender)
        {
            MainUI.GetInstance().OnDeviceDropFilesBegin(Sender);
        }

        void OnDeviceDropFilesFile(Device Sender, string Filename)
        {
            MainUI.GetInstance().OnDeviceDropFilesFile(Sender, Filename);
        }

        void OnDeviceDropFilesEnd(Device Sender)
        {
            MainUI.GetInstance().OnDeviceDropFilesEnd(Sender);
        }

        void OnDockingControlLayout(DockingControl Sender)
        {
        }

        void OnDockingControlLoadDockingCard(DockingControl Sender, DockingBlock DockingBlock, string DockingCardText, string DockingCardTag, out DockingCard DockingCard, Record Record_Child)
        {
            MainUI.GetInstance().OnDockingControlLoadDockingCard(Sender, DockingBlock, DockingCardText, DockingCardTag, out DockingCard, Record_Child);
        }

        void OnDockingControlNewWindow(DockingControl Sender, Control Dragged, int MouseX, int MouseY)
        {
            MainUI.GetInstance().OnDockingControlNewWindow(Sender, Dragged, MouseX, MouseY);
        }

        public void SaveUserConfig(Record RecordWindow)
        {
            int X = _Device.GetOriginalX();
            int Y = _Device.GetOriginalY();
            int Width = _Device.GetOriginalWidth();
            int Height = _Device.GetOriginalHeight();
            bool bMaximized = _Device.IsMaximized();

            RecordWindow.SetTypeString("Window");
            RecordWindow.SetInt("X", X);
            RecordWindow.SetInt("Y", Y);
            RecordWindow.SetInt("Width", Width);
            RecordWindow.SetInt("Height", Height);
            RecordWindow.SetBool("Maximized", bMaximized);

            _DockingControl.SaveDockingInfo(RecordWindow);
        }

        public void LoadUserConfig(Record RecordWindow)
        {
            int X = RecordWindow.GetInt("X");
            int Y = RecordWindow.GetInt("Y");
            int Width = RecordWindow.GetInt("Width");
            int Height = RecordWindow.GetInt("Height");
            bool bMaximized = RecordWindow.GetBool("Maximized");

            Initialize(X, Y, Width, Height, null);
            if (bMaximized)
            {
                _Device.ShowMaximize();
            }

            _DockingControl.LoadDockingInfo(RecordWindow);
        }
    }
}
