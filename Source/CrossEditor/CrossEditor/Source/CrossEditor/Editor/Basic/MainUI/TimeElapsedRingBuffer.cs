using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class TimeElapsedRingBuffer
    {
        List<double> _RingBuffer;
        int _RingBufferSize;

        public TimeElapsedRingBuffer(int Capacity)
        {
            _RingBuffer = new List<double>();
            ListHelper.Resize(_RingBuffer, Capacity);
            _RingBufferSize = 0;
        }

        public void Put(double TimeElapsed)
        {
            int Index = _RingBufferSize % _RingBuffer.Count;
            _RingBuffer[Index] = TimeElapsed;
            _RingBufferSize++;
        }

        public double CalculateAverageTimeElapsed()
        {
            int Count = Math.Min(_RingBufferSize, _RingBuffer.Count);
            if (Count == 0)
            {
                return 0.0;
            }
            double TotalTimeElapsed = 0.0;
            for (int i = 0; i < Count; i++)
            {
                TotalTimeElapsed += _RingBuffer[i];
            }
            return TotalTimeElapsed / Count;
        }
    }
}
