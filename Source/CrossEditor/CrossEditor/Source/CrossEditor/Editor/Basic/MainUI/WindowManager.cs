using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class WindowManager
    {
        static WindowManager _Instance = new WindowManager();

        List<Window> WindowList;

        WindowManager()
        {
            WindowList = new List<Window>();
        }

        public static WindowManager GetInstance()
        {
            return _Instance;
        }

        public void CreateWindow(int X, int Y, int Width, int Height, Control Dragged)
        {
            Window Window = new Window();
            Window.Initialize(X, Y, Width, Height, Dragged);
            WindowList.Add(Window);
        }

        public void RemoveWindow(Window Window)
        {
            WindowList.Remove(Window);
        }

        public Window FindWindow(UIManager UIManager)
        {
            foreach (Window Window in WindowList)
            {
                if (Window.GetUIManager() == UIManager)
                {
                    return Window;
                }
            }
            return null;
        }

        public void Update()
        {
            for (int i = 0; i < WindowList.Count; i++)
            {
                Window Window = WindowList[i];
                Window.Update();
            }
        }

        public void Paint()
        {
            foreach (Window Window in WindowList)
            {
                Window.Paint();
            }
        }

        public void SaveUserConfig(Record RootRecord)
        {
            Record RecordWindowList = RootRecord.AddChild();
            RecordWindowList.SetTypeString("WindowList");
            foreach (Window Window in WindowList)
            {
                Record RecordWindow = RecordWindowList.AddChild();
                Window.SaveUserConfig(RecordWindow);
            }
        }

        public void LoadUserConfig(Record RootRecord)
        {
            Record RecordWindowList = RootRecord.FindByTypeString("WindowList");
            if (RecordWindowList != null)
            {
                int RecordWindowCount = RecordWindowList.GetChildCount();
                for (int i = 0; i < RecordWindowCount; i++)
                {
                    Record RecordWindow = RecordWindowList.GetChild(i);
                    Window Window = new Window();
                    Window.LoadUserConfig(RecordWindow);
                    WindowList.Add(Window);
                }
            }
        }
    }
}
