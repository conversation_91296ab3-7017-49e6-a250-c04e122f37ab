using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using static CrossEditor.DockingUI;

public class Program
{
    [DllImport("user32.dll")]
    public static extern int MessageBox(IntPtr hWnd, String text, String caption, int options);
}


namespace CrossEditor
{
    public enum DockingBlockSection
    {
        LeftHierachy,
        RightInspector,
        MiddleScene,
        BottomResource,
    }

    public class MainUI
    {
        const int TitleHeight = 60;
        const int StatusBarHeight = 20;

        static MainUI _Instance = new MainUI();

        UIManager _UIManager;
        FrameUI _FrameUI;

        DockingCard _DockingCard_Project;
        DockingCard _DockingCard_Hierarchy;
        DockingCard _DockingCard_Inspector;
        DockingCard _DockingCard_ResourceInspector;
        DockingCard _DockingCard_Terrain;
        DockingCard _DockingCard_TodSetting;
        DockingCard _DockingCard_Console;
        DockingCard _DockingCard_Welcome;
        DockingCard _DockingCard_FxView;
        DockingCard _DockingCard_Scene;
        DockingCard _DockingCard_Game;
        DockingCard _DockingCard_REDVisualizer;
        DockingCard _DockingCard_NavMeshBaker;
        DockingCard _DockingCard_MSAPreview;
        DockingCard _DockingCard_MEPreview;
        DockingCard _DockingCard_ParticleSystemEditor;
        DockingCard _DockingCard_NodeGraph;
        DockingCard _DockingCard_StateMachine;
        DockingCard _DockingCard_AnimGraph;
        DockingCard _DockingCard_CurveEditor;
        DockingCard _DockingCard_LinearColorCurveEditor;
        DockingCard _DockingCard_TimelineEditor;
        DockingCard _DockingCard_AnimTimelineEditor;
        DockingCard _DockingCard_CinematicEditor;
        DockingCard _DockingCard_WorldPartition;
        DockingCard _DockingCard_TextureEditor;
        DockingCard _DockingCard_Tasks;
        DockingCard _DockingCard_CaptureComparer;
        DockingCard _DockingCard_ImGuiConsole;
        DockingCard _DockingCard_PrefabEditor;
        DockingCard _DockingCard_PreviewScene;

        public DockingBlock _DockingBlock1;     // DockingBlockSection.LeftHierachy
        public DockingBlock _DockingBlock2;     // DockingBlockSection.BottomResource
        public DockingBlock _DockingBlock3;     // DockingBlockSection.RightInspector

        public DockingBlock _DockingBlockScene; //DockingBlockSection.MiddleScene

        DockingControl _DockingControl;

        long _LastTime;

        string _ProjectFilename;
        string _ProjectDirectory;
        string _ProjectName;

        DockingCard _CurrentDockingCard;
        DockingCard _CurrentProjectCard;

        bool _bExitRun;

        bool _bInitialized;

        bool _bSceneSaveAsDialogInputed;

        long _TimeAutoSaving;
        long _CountDownAutoSaving;

        Random _Random = new Random();

        public static MainUI GetInstance()
        {
            return _Instance;
        }

        MainUI()
        {
            _LastTime = 0;
            _bExitRun = false;
            _bInitialized = false;
            _bSceneSaveAsDialogInputed = false;
            _ProjectFilename = "";
            _ProjectDirectory = "";
            _ProjectName = "";
            _TimeAutoSaving = 0;
            _CountDownAutoSaving = 0;
        }

        ~MainUI()
        {
        }

        public UIManager GetUIManager()
        {
            return _UIManager;
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public FrameUI GetFrameUI()
        {
            return _FrameUI;
        }

        public bool Initialize(UIManager UIManager)
        {
            _UIManager = UIManager;

            Device Device = UIManager.GetDevice();
            Graphics2D Graphics2D = UIManager.GetGraphics2D();

            Device.SetFixedSize(false);

            Device.PaintEvent += OnDevicePaint;
            Device.ResizeEvent += OnDeviceResize;
            Device.ApplicationActivateEvent += OnDeviceApplicationActivate;
            Device.ActivateEvent += OnDeviceActivate;
            Device.DropFilesBeginEvent += OnDeviceDropFilesBegin;
            Device.DropFilesFileEvent += OnDeviceDropFilesFile;
            Device.DropFilesEndEvent += OnDeviceDropFilesEnd;
            Device.CloseEvent += OnDeviceClose;
            Device.DestroyEvent += OnDeviceDestroy;
            UIManager.PreKeyEvent += OnUIManagerPreKey;

            Device.SetMinSize(640, 480);
            Control Root = UIManager.GetRoot();
            Root.ClearChildren();

            bool bTitleRightAlign = true;
            bool bSimple = false;
            _FrameUI = new FrameUI();
            _FrameUI.Initialize(UIManager, Device.GetTitle(), bTitleRightAlign, bSimple);
            UpdateStatusBar();

            _DockingControl = new DockingControl();
            Root.AddChild(_DockingControl);
            _DockingControl.Initialize();
            _DockingControl.SetSize(1000, 1000);
            _DockingControl.LayoutedEvent += OnDockingControlLayout;
            _DockingControl.LoadDockingBlockEvent += OnDockingControlLoadDockingBlock;
            _DockingControl.LoadDockingCardEvent += OnDockingControlLoadDockingCard;
            _DockingControl.LeftMouseDownEvent += OnDockingControlLeftMouseDown;
            _DockingControl.RightMouseUpEvent += OnDockingControlRightMouseUp;
            _DockingControl.NewWindowEvent += OnDockingControlNewWindow;

            ProjectUI.GetInstance().Initialize();
            _DockingCard_Project = ProjectUI.GetInstance().GetDockingCard();

            HierarchyUI.GetInstance().Initialize();
            _DockingCard_Hierarchy = HierarchyUI.GetInstance().GetDockingCard();

            InspectorUI.GetInstance().Initialize("Properties");
            _DockingCard_Inspector = InspectorUI.GetInstance().GetDockingCard();

            ResourceInspectorUI.GetInstance().Initialize("ResourceProperties");
            _DockingCard_ResourceInspector = ResourceInspectorUI.GetInstance().GetDockingCard();

            TODSettingUI.GetInstance().Initialize();
            _DockingCard_TodSetting = TODSettingUI.GetInstance().GetDockingCard();

            TerrainEditorUI.GetInstance().Initialize();
            _DockingCard_Terrain = TerrainEditorUI.GetInstance().GetDockingCard();

            ConsoleUI.GetInstance().Initialize();
            _DockingCard_Console = ConsoleUI.GetInstance().GetDockingCard();

            WelcomeUI.GetInstance().Initialize();
            _DockingCard_Welcome = WelcomeUI.GetInstance().GetDockingCard();
            _DockingCard_Welcome.SetDocument(true);

            FxEditorUI.GetInstance().Initialize();
            _DockingCard_FxView = FxEditorUI.GetInstance().GetDockingCard();
            _DockingCard_FxView.SetDocument(true);

            EditorSceneUI.GetInstance().Initialize();
            _DockingCard_Scene = EditorSceneUI.GetInstance().GetDockingCard();
            _DockingCard_Scene.SetDocument(true);

            GameSceneUI.GetInstance().Initialize();
            _DockingCard_Game = GameSceneUI.GetInstance().GetDockingCard();
            _DockingCard_Game.SetDocument(true);

            MSAPreviewUI.GetInstance().Initialize();
            _DockingCard_MSAPreview = MSAPreviewUI.GetInstance().GetDockingCard();
            _DockingCard_MSAPreview.SetDocument(true);

            MEPreviewUI.GetInstance().Initialize();
            _DockingCard_MEPreview = MEPreviewUI.GetInstance().GetDockingCard();
            _DockingCard_MEPreview.SetDocument(true);

            ParticleSystemUI.GetInstance().Initialize();
            _DockingCard_ParticleSystemEditor = ParticleSystemUI.GetInstance().GetDockingCard();
            _DockingCard_ParticleSystemEditor.SetDocument(true);

            BasicNodeGraphUI.GetInstance().Initialize();
            _DockingCard_NodeGraph = BasicNodeGraphUI.GetInstance().GetDockingCard();
            _DockingCard_NodeGraph.SetDocument(true);

            StateMachineGraphUI.GetInstance().Initialize();
            _DockingCard_StateMachine = StateMachineGraphUI.GetInstance().GetDockingCard();
            _DockingCard_StateMachine.SetDocument(true);

            StoryBoardUI.GetInstance().Initialize();
            _DockingCard_AnimGraph = StoryBoardUI.GetInstance().GetDockingCard();
            _DockingCard_AnimGraph.SetDocument(true);

            TextureEditorUI TextureEditorUI = new TextureEditorUI();
            TextureEditorUI.Initialize();
            _DockingCard_TextureEditor = TextureEditorUI.GetDockingCard();

            REDVisualizer.Instance.Initialize();
            _DockingCard_REDVisualizer = REDVisualizer.Instance.GetDockingCard();

            WorldPartitionUI.GetInstance().Initialize();
            _DockingCard_WorldPartition = WorldPartitionUI.GetInstance().GetDockingCard();

            NavMeshBakerUI.GetInstance().Initialize();
            _DockingCard_NavMeshBaker = NavMeshBakerUI.GetInstance().GetDockingCard();

            CurveEditorUI.GetInstance().Initialize();
            _DockingCard_CurveEditor = CurveEditorUI.GetInstance().GetDockingCard();

            LinearColorCurveEditorUI.GetInstance().Initialize();
            _DockingCard_LinearColorCurveEditor = LinearColorCurveEditorUI.GetInstance().GetDockingCard();

            TimelineEditorUI.GetInstance().Initialize();
            _DockingCard_TimelineEditor = TimelineEditorUI.GetInstance().GetDockingCard();

            AnimTimelineEditorUI.GetInstance().Initialize();
            _DockingCard_AnimTimelineEditor = AnimTimelineEditorUI.GetInstance().GetDockingCard();

            CinematicUI.GetInstance().Initialize();
            _DockingCard_CinematicEditor = CinematicUI.GetInstance().GetDockingCard();

            PrefabSceneUI.GetInstance().Initialize();
            _DockingCard_PrefabEditor = PrefabSceneUI.GetInstance().GetDockingCard();

            PreviewSceneUI.GetInstance().Initialize();
            _DockingCard_PreviewScene = PreviewSceneUI.GetInstance().GetDockingCard();

            //PaintToolEditor.GetInstance().Initialize();
            //_DockingCard_Brush = PaintToolEditor.GetInstance().GetDockingCard();

            //PCGBuildingEditor.GetInstance().Initialize();
            //_DockingCard_PCGBuilding = PCGBuildingEditor.GetInstance().GetDockingCard();

            TaskUI.GetInstance().Initialize();
            _DockingCard_Tasks = TaskUI.GetInstance().GetDockingCard();

            CaptureComparerUI.GetInstance().Initialize();
            _DockingCard_CaptureComparer = CaptureComparerUI.GetInstance().GetDockingCard();

            ImGuiConsoleUI.GetInstance().Initialize();
            _DockingCard_ImGuiConsole = ImGuiConsoleUI.GetInstance().GetDockingCard();





            _DockingBlock1 = new DockingBlock();
            _DockingBlock1.Initialize();
            _DockingBlock1.SetSize(300, 200);
            _DockingBlock1.AddDockingCard(_DockingCard_Hierarchy);

            _DockingBlock1.SetActiveCard(_DockingCard_Hierarchy);

            _DockingBlockScene = new DockingBlock();
            _DockingBlockScene.Initialize();
            _DockingBlockScene.SetSize(600, 300);
            _DockingBlockScene.SetDocument(true);
            _DockingBlockScene.AddDockingCard(_DockingCard_Scene);
            _DockingBlockScene.AddDockingCard(_DockingCard_Game);
            _DockingBlockScene.AddDockingCard(_DockingCard_CaptureComparer);
            _DockingBlockScene.AddDockingCard(_DockingCard_NodeGraph);
            _DockingBlockScene.AddDockingCard(_DockingCard_Welcome);
            _DockingBlockScene.AddDockingCard(_DockingCard_FxView);
            _DockingBlockScene.AddDockingCard(_DockingCard_MSAPreview);
            _DockingBlockScene.AddDockingCard(_DockingCard_MEPreview);
            _DockingBlockScene.AddDockingCard(_DockingCard_CurveEditor);
            _DockingBlockScene.AddDockingCard(_DockingCard_LinearColorCurveEditor);
            _DockingBlockScene.AddDockingCard(_DockingCard_ImGuiConsole);
            _DockingBlockScene.SetActiveCard(_DockingCard_Scene);

            _DockingBlock2 = new DockingBlock();
            _DockingBlock2.Initialize();
            _DockingBlock2.SetSize(300, 200);
            _DockingBlock2.AddDockingCard(_DockingCard_Project);
            _DockingBlock2.AddDockingCard(_DockingCard_Console);
            _DockingBlock2.AddDockingCard(_DockingCard_TimelineEditor);
            _DockingBlock2.AddDockingCard(_DockingCard_AnimTimelineEditor);
            _DockingBlock2.SetActiveCard(_DockingCard_Project);

            _DockingBlock3 = new DockingBlock();
            _DockingBlock3.Initialize();
            _DockingBlock3.SetSize(300, 300);
            _DockingBlock3.AddDockingCard(_DockingCard_Inspector);
            _DockingBlock3.AddDockingCard(_DockingCard_ResourceInspector);
            _DockingBlock3.AddDockingCard(_DockingCard_Terrain);
            _DockingBlock3.AddDockingCard(_DockingCard_TodSetting);
            _DockingBlock3.AddDockingCard(_DockingCard_Tasks);
            _DockingBlock3.SetActiveCard(_DockingCard_Inspector);

            ReflectExtendedDockingView();

            DockingNode LayoutRoot = _DockingControl.GetDockingRoot();
            LayoutRoot.SetDockingDirection(DockingDirection.Horizontal);

            DockingNode LayoutNode1 = LayoutRoot.AddDockingNode(DockingDirection.Vertical);
            DockingNode LayoutNode1_1 = LayoutNode1.AddDockingNode(DockingDirection.Horizontal);
            DockingNode LayoutNode1_1_1 = LayoutNode1_1.AddDockingBlock(_DockingBlock1);
            DockingNode LayoutNode1_1_2 = LayoutNode1_1.AddDockingBlock(_DockingBlockScene);
            DockingNode LayoutNode1_2 = LayoutNode1.AddDockingBlock(_DockingBlock2);

            DockingNode LayoutNode2 = LayoutRoot.AddDockingBlock(_DockingBlock3);

            GameMainMenuUI.GetInstance().Initialize(UIManager);
            GameToolBarUI.GetInstance().Initialize(UIManager);
            FPSDisplayUI.GetInstance().Initialize(UIManager);

            ResetAutoSavingCountDown();

            string ProjectFilename = ProjectsUI.GetInstance().GetTargetProjectFilename();
            OpenProject(ProjectFilename);
            ThumbnailUI.GetInstance().Initialize(ProjectFilename);
            if (Environment.OSVersion.Platform != PlatformID.Unix)
            {
                ThumbnailHost ThumbnailHost = ThumbnailHost.GetInstance();
                ThumbnailHost.CreateThumbnailMutex();
                ThumbnailHost.CreateThumbnailHostThread();
            }

            _bInitialized = true;

            NodeRegister.GetInstance().RegisterNodes();
            return true;
        }

        public List<TaskItem> UpdateImportTask()
        {
            List<TaskItem> tasks = new List<TaskItem>();
            Type type = typeof(AssetType);
            string[] EnumNames = type.GetEnumNames();
            for (int tp = 1; tp < (int)AssetType.Count; tp++)
            {
                float progress = AssetImporterManager.Instance().GetProgress((AssetType)tp);
                if (progress > 0.001f)
                {
                    TaskItem item = new TaskItem(EnumNames[tp], progress);
                    tasks.Add(item);
                }
            }
            return tasks;
        }


        public void ReflectExtendedDockingView()
        {
            foreach (var item in CrossEngine.GetInstance().ViewDockingItems)
            {
                if (item.Value.ShowOrder > 0)
                    RegisterExtendView(item.Key, item.Value);
            }
        }

        void RegisterExtendView(Type type, DockingMenuAttribute dockingSetting)
        {
            var StaticGetInstanceMethod = type.GetMethod("GetInstance", BindingFlags.Static | BindingFlags.Public | BindingFlags.FlattenHierarchy);
            var methods = type.GetMethods(BindingFlags.Public | BindingFlags.FlattenHierarchy | BindingFlags.Instance);

            var InitializeMethod = Array.Find(methods, x => x.Name == "Initialize" && x.GetParameters().Length == 0);
            var InitMenuItemMethod = type.GetMethod("InitMenuItem", BindingFlags.Public | BindingFlags.FlattenHierarchy | BindingFlags.Instance);

            var instance = StaticGetInstanceMethod.Invoke(null, null);

            ClickDockingDelegate func = delegate (DockingCard card) { ActivateDockingCard(card, dockingSetting.DefaultDocking); };

            InitializeMethod.Invoke(instance, null);
            InitMenuItemMethod.Invoke(instance, new object[] { func, dockingSetting.DisplayUINames
            });
        }

        public void UpdateStatusBar()
        {
            FrameUI FrameUI = _FrameUI;
            ConsoleUI ConsoleUI = ConsoleUI.GetInstance();
            LogCounter LogCounter = ConsoleUI.GetLogCounter();

            int warningSceneTs = EditorSceneUI.GetInstance().GetSaveWarningTime();
            if (warningSceneTs >= 0)
            {
                string Text = string.Format("Auto Save Scene After {0}s", warningSceneTs);
                FrameUI.SetStatusBarText1(Text);
                FrameUI.SetStatusBarText1Color(new Color(1.0f, 0.0f, 0.0f, 1.0f));
                return;
            }
            int warningPrefabTs = PrefabSceneUI.GetInstance().GetSaveWarningTime();
            if (warningPrefabTs >= 0)
            {
                string Text = string.Format("Auto Save Prefab After {0}s", warningPrefabTs);
                FrameUI.SetStatusBarText1(Text);
                FrameUI.SetStatusBarText1Color(new Color(1.0f, 0.0f, 0.0f, 1.0f));
                return;
            }

            List<TaskItem> tasks = UpdateImportTask();
            FrameUI.SetShowStatusProgress(tasks.Count > 0);
            if (tasks.Count > 0)
            {
                string Text = string.Format("Import {0}: {1}%", tasks[0].mTitle, (tasks[0].mProgress * 100).ToString("0.00"));
                FrameUI.SetStatusBarText1(Text);
                FrameUI.SetStatusBarText1Color(new Color(1.0f, 1.0f, 1.0f, 1.0f));
                FrameUI.SetStatusBarProgress(tasks[0].mProgress);
            }
            else if (LogCounter != null && LogCounter.ErrorCount > 0)
            {
                int ErrorCount = LogCounter.ErrorCount;
                string Text = string.Format("{0} {1}{2}", ErrorCount.ToString(), "Error", ErrorCount > 1 ? "s" : "");
                FrameUI.SetStatusBarText1(Text);
                FrameUI.SetStatusBarText1Color(new Color(1.0f, 0.0f, 0.0f, 1.0f));
            }
            else
            {
                FrameUI.SetStatusBarText1("Ready");
                FrameUI.SetStatusBarText1Color(new Color(1.0f, 1.0f, 1.0f, 1.0f));
            }
        }

        public void Release()
        {
        }

        public void Run()
        {
            bool Profile = false;
            UIManager UIManager = GetUIManager();
            while (!_bExitRun)
            {
                long Time1 = 0;
                if (Profile)
                {
                    Time1 = SystemHelper.GetTimeMs();
                }
                SceneRuntime.GetInstance().Tick(Time1);
                if (Profile)
                {
                    long Time2 = SystemHelper.GetTimeMs();
                    long DeltaTime = Time2 - Time1;
                    Console.WriteLine("FPS: {0}", DeltaTime);
                }
            }
        }

        public void Update()
        {
            if (_LastTime == 0)
            {
                _LastTime = SystemHelper.GetTimeMs();
            }
            long CurrentTime = SystemHelper.GetTimeMs();
            long TimeElapsed = Math.Max(0, CurrentTime - _LastTime);
            _LastTime = CurrentTime;

            if (_DockingControl == null)
            {
                return;
            }

            Device Device = GetDevice();
            if (Device.IsLeftButtonDown() == false && Device.GetY() < 0)
            {
                Device.SetPosition(Device.GetX(), 0, Device.GetWidth(), Device.GetHeight());
            }

            UpdateStatusBar();
            //PCGManager.GetInstance().Update();
            HierarchyUI.GetInstance().Update();
            EditorSceneUI.GetInstance().Update(TimeElapsed);
            GameSceneUI.GetInstance().Update(TimeElapsed);
            FxEditorUI.GetInstance().Update();
            MSAPreviewUI.GetInstance().Update(TimeElapsed);
            MEPreviewUI.GetInstance().Update(TimeElapsed);
            ParticleSystemUI.GetInstance().Update(TimeElapsed);
            //PCGScene.GetInstance().Update();
            GameToolBarUI.GetInstance().Update();
            _FrameUI.Update();
            SceneRuntime.GetInstance().Update();
            FPSDisplayUI.GetInstance().Update(TimeElapsed);
            //SceneRuntime.GetInstance().Tick(TimeElapsed);
            StoryBoardUI.GetInstance().RefreshDebugData();
            //InspectorUI.GetInstance().Update();
            PrefabSceneUI.GetInstance().Update(TimeElapsed);
            PreviewSceneUI.GetInstance().Update(TimeElapsed);
            TerrainEditorUI.GetInstance().Update(TimeElapsed);
            TODSettingUI.GetInstance().Update(TimeElapsed);
            REDVisualizer.Instance.Update(TimeElapsed);
            MaterialEditorUIManager.Instance.Update(TimeElapsed);
            WorkflowEditorUIManager.Instance.Update(TimeElapsed);

            //UpdateAutoSavingCountDown(TimeElapsed);
            FrameExecutor.GetInstance().Update();
            CaptureComparerUI.GetInstance().Update(TimeElapsed);
            ThumbnailUI.GetInstance().Update(TimeElapsed);
            InspectorUI.GetInstance().Update(TimeElapsed);
        }

        public void UpdateTitle()
        {
            string CurrentSceneFilename = EditorScene.GetInstance().GetCurrentSceneFilename();
            string SceneName = PathHelper.GetNameOfPath(CurrentSceneFilename);
            if (SceneName == "")
            {
                SceneName = "<NewScene>";
            }
            else
            {
                SceneName = SceneName + ".world";
            }

            string Title = string.Format("{0} - {1} - {2} - {3}", SceneName, _ProjectName, "Cross Engine", CrossEngine.GetInstance().GetRendererMode().ToString());

            GetDevice().SetTitle(Title);
            _FrameUI.SetTitle(Title);
        }

        public void CloseDockingCard()
        {
            if (_DockingCard_CinematicEditor != null)
                _DockingCard_CinematicEditor.CloseCard();
        }

        public string GetProjectName()
        {
            return _ProjectName;
        }

        public void SetProjectDirectory(string ProjectDirectory)
        {
            _ProjectDirectory = ProjectDirectory;
        }

        public string GetProjectDirectory()
        {
            return _ProjectDirectory;
        }

        public bool GetInitialized()
        {
            return _bInitialized;
        }

        public DockingControl GetDockingControl()
        {
            return _DockingControl;
        }

        public void CopyProjectConfigJson(string ProjectDirectory)
        {
            string ResourceDirectory = EditorUtilities.GetResourceDirectory();
            string SourceFilename = ResourceDirectory + "/ProjectConfig.json";
            string DestinationFilename = ProjectDirectory + "/ProjectConfig.json";
            if (FileHelper.IsFileExists(DestinationFilename) == false)
            {
                FileHelper.CopyFile(SourceFilename, DestinationFilename);
            }
        }

        void OpenProject(string InProjectFilename)
        {
            if (InProjectFilename == _ProjectFilename)
            {
                return;
            }
            _ProjectFilename = InProjectFilename;
            _ProjectDirectory = PathHelper.GetDirectoryName(_ProjectFilename);
            _ProjectName = PathHelper.GetFileName(_ProjectDirectory);

            EditorConfig EditorConfig = EditorConfig.GetInstance();
            EditorConfig.LastProjectPath = _ProjectFilename;
            EditorConfig.SaveEditorConfig();
            EditorConfigManager.GetInstance().Initialize();
            //ResourceManager.Instance().InitResourceMap();

            string ResourceDirectory = EditorUtilities.GetResourceDirectory();
            ShaderWatcher.Instance.Initialize(_ProjectDirectory, ResourceDirectory);
            CanvasWatcher.GetInstance().Initialize(_ProjectDirectory, ResourceDirectory);
            ScriptWatcher.GetInstance().Initialize(_ProjectDirectory, ResourceDirectory);

            CopyProjectConfigJson(_ProjectDirectory);

            SceneRuntime.GetInstance().OpenProject(_ProjectDirectory);

            RecentList.GetInstance().LoadRecentItemList();
            WelcomeUI.GetInstance().RefreshUI();

            ResourceTypeCache.GetInstance().StartCache();
            ResourceTypeCache.GetInstance().EndCache();
            WelcomeUI.GetInstance().OpenLastScene();

            // Start TypeScript compilation asynchronously
            _ = TypeScriptCompile();

            LoadUserConfig();
            UpdateTitle();

            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.ClearTempFile();
            ProjectUI.GetInstance().UpdateDirectory();

            CaptureComparerUI.GetInstance().UpdateCaptureTrees();

            //ResourceTypeCache.GetInstance().StartCache();
        }

        void LoadUserConfig()
        {
            UserConfig.GetInstance().LoadUserConfig();
            FoldStateManager.GetInstance().LoadFoldStates();
            BookmarkManager.GetInstance().LoadBookmarkLists();
        }

        void SaveUserConfig()
        {
            UserConfig.GetInstance().SaveUserConfig();
            FoldStateManager.GetInstance().SaveFoldStates();
            BookmarkManager.GetInstance().SaveBookmarkLists();
        }

        DockingBlock GetDockingBlockOfControl(Control Control)
        {
            if (Control == null)
            {
                return null;
            }
            if (Control is DockingBlock)
            {
                return (DockingBlock)Control;
            }
            Control Parent = Control.GetParent();
            return GetDockingBlockOfControl(Parent);
        }


        public void ActivateDockingCard(DockingCard DockingCard, DockingBlockSection section, bool bSetCardFocus = true)
        {
            switch (section)
            {
                case DockingBlockSection.LeftHierachy:
                    ActivateDockingCard(DockingCard, _DockingBlock1, bSetCardFocus); break;
                case DockingBlockSection.MiddleScene:
                    ActivateDockingCard(DockingCard, _DockingBlockScene, bSetCardFocus); break;
                case DockingBlockSection.RightInspector:
                    ActivateDockingCard(DockingCard, _DockingBlock3, bSetCardFocus); break;
                case DockingBlockSection.BottomResource:
                    ActivateDockingCard(DockingCard, _DockingBlock2, bSetCardFocus); break;
            }
        }

        public void ActivateDockingCard(DockingCard DockingCard, DockingBlock DockingBlock = null, bool bSetCardFocus = true)
        {
            DockingBlock CurrentParent = DockingCard.GetDockingBlock();
            if (CurrentParent != null && CurrentParent.GetDockingControl() != null)
            {
                CurrentParent.SetActiveCard(DockingCard);
                bool bIsDockingCardVisibleInTabs1 = CurrentParent.IsDockingCardVisibleInTabs(DockingCard);
                if (bIsDockingCardVisibleInTabs1 == false)
                {
                    CurrentParent.RemoveDockingCard(DockingCard);
                    CurrentParent.InsertDockingCard(-1, DockingCard);
                }
                if (bSetCardFocus)
                {
                    DockingCard.SetCardFocus();
                }
                return;
            }
            DockingControl DockingControl = DockingBlock.GetDockingControl();
            if (DockingControl == null)
            {
                DockingBlock ExsitingDockingBlock = null;
                Control Root = GetUIManager().GetRoot();
                int Width = Root.GetWidth();
                int Height = Root.GetHeight();
                Control Control = null;
                if (DockingBlock == _DockingBlock1)
                {
                    Control = Root.GetControlAt(50, Height / 3, false);
                }
                else if (DockingBlock == _DockingBlock2)
                {
                    Control = Root.GetControlAt(Width / 2, Height - 50, false);
                }
                else if (DockingBlock == _DockingBlock3)
                {
                    Control = Root.GetControlAt(Width - 50, Height / 3, false);
                }
                if (Control != null)
                {
                    ExsitingDockingBlock = GetDockingBlockOfControl(Control);
                }
                if (ExsitingDockingBlock != null)
                {
                    if (ExsitingDockingBlock.GetDocument())
                    {
                        ExsitingDockingBlock = null;
                    }
                }
                if (ExsitingDockingBlock == null)
                {
                    DockingBlock.ClearDockingCards();
                    DockingBlock.AddDockingCard(DockingCard);
                    DockingNode DockingRoot = this._DockingControl.GetDockingRoot();
                    DockingRoot.AddDockingBlock(DockingBlock);
                    this._DockingControl.Refresh();
                }
                else
                {
                    DockingBlock = ExsitingDockingBlock;
                }
            }
            else
            {
                DockingBlock.RemoveDockingCard(DockingCard);
                DockingBlock.AddDockingCard(DockingCard);
            }
            bool bIsDockingCardVisibleInTabs = DockingBlock.IsDockingCardVisibleInTabs(DockingCard);
            if (bIsDockingCardVisibleInTabs == false)
            {
                DockingBlock.RemoveDockingCard(DockingCard);
                DockingBlock.InsertDockingCard(-1, DockingCard);
            }
            if (bSetCardFocus)
            {
                DockingCard.SetCardFocus();
            }
            _CurrentProjectCard = DockingCard;
        }

        public DockingCard FindDockingCardByFilePath(string FilePath)
        {
            for (int i = 0; i < _DockingBlockScene.GetDockingCardCount(); ++i)
            {
                if (_DockingBlockScene.GetDockingCard(i).GetTagString1() == FilePath)
                {
                    return _DockingBlockScene.GetDockingCard(i);
                }
            }

            return null;
        }

        public List<DockingCard> FindDockingCardsByFileSuffix(string FileSuffix)
        {
            List<DockingCard> DockingCards = new List<DockingCard>();

            for (int i = 0; i < _DockingBlockScene.GetDockingCardCount(); ++i)
            {
                if (_DockingBlockScene.GetDockingCard(i).GetTagString1().Contains(FileSuffix))
                {
                    DockingCards.Add(_DockingBlockScene.GetDockingCard(i));
                }
            }

            return DockingCards;
        }

        public void ActivateDockingCard_Project()
        {
            ActivateDockingCard(_DockingCard_Project, _DockingBlock2);
        }

        void ActivateDockingCard_Hierarchy()
        {
            ActivateDockingCard(_DockingCard_Hierarchy, _DockingBlock1);
        }

        public void ActivateDockingCard_Inspector(bool bSetCardFocus = true)
        {
            ActivateDockingCard(_DockingCard_Inspector, _DockingBlock3, bSetCardFocus);
        }

        public void ActivateDockingCard_ResourceInspector()
        {
            ActivateDockingCard(_DockingCard_ResourceInspector, _DockingBlock3);
        }

        public void ActivateDockingCard_ResourceInspector(DockingCard Card)
        {
            DockingBlock Block = _DockingBlock3;
            if (_DockingCard_ResourceInspector.GetDockingBlock() != null)
            {
                Block = _DockingCard_ResourceInspector.GetDockingBlock();
            }
            ActivateDockingCard(Card, Block);
        }

        void ActivateDockingCard_Terrain()
        {
            ActivateDockingCard(_DockingCard_Terrain, _DockingBlock3);
        }
        void ActivateDockingCard_TodSetting()
        {
            ActivateDockingCard(_DockingCard_TodSetting, _DockingBlock3);
        }

        public void ActivateDockingCard_Console()
        {
            ActivateDockingCard(_DockingCard_Console, _DockingBlock2);
        }

        public void ActivateDockingCard_Welcome()
        {
            ActivateDockingCard(_DockingCard_Welcome, _DockingBlockScene);
        }

        public void ActivateDockingCard_FxView()
        {
            ActivateDockingCard(_DockingCard_FxView, _DockingBlockScene);
        }

        public void ActivateDockingCard_FxView(DockingCard card)
        {
            ActivateDockingCard(card, _DockingBlockScene);
        }


        public void ActivateDockingCard_Scene()
        {
            ActivateDockingCard(_DockingCard_Scene, _DockingBlockScene);
        }

        public void ActivateDockingCard_Game()
        {
            ActivateDockingCard(_DockingCard_Game, _DockingBlockScene);
        }


        void ActivateDockingCard_REDVisualizer()
        {
            ActivateDockingCard(_DockingCard_REDVisualizer, _DockingBlock3);
        }




        void ActivateDockingCard_NavMeshBaker()
        {
            ActivateDockingCard(_DockingCard_NavMeshBaker, _DockingBlock3);
        }

        public void ActivateDockingCard_MSAPreivew()
        {
            ActivateDockingCard(_DockingCard_MSAPreview, _DockingBlockScene);
        }

        public void ActivateDockingCard_MEPreview()
        {
            ActivateDockingCard(_DockingCard_MEPreview, _DockingBlockScene);
        }

        public void ActivateDockingCard_ParticleSystem()
        {
            ActivateDockingCard(_DockingCard_ParticleSystemEditor, _DockingBlockScene);
        }

        public void ActivateDockingCard_NodeGraph()
        {
            ActivateDockingCard(_DockingCard_NodeGraph, _DockingBlockScene);
        }

        public void ActivateDockingCard_BasicNodeGraph(DockingCard Card)
        {
            ActivateDockingCard(Card, _DockingBlockScene);
        }

        public void ActivateDockingCard_StateMachine()
        {
            ActivateDockingCard(_DockingCard_StateMachine, _DockingBlockScene);
        }

        public void ActivateDockingCard_Animator()
        {
            ActivateDockingCard(_DockingCard_AnimGraph, _DockingBlockScene);
        }

        public void ActivateDockingCard_Animator(DockingCard Card)
        {
            ActivateDockingCard(Card, _DockingBlockScene);
        }

        public void ActivateDockingCard_CurveEditor()
        {
            ActivateDockingCard(_DockingCard_CurveEditor, _DockingBlockScene);
        }

        public void ActivateDockingCard_LinearColorCurveEditor()
        {
            ActivateDockingCard(_DockingCard_LinearColorCurveEditor, _DockingBlockScene);
        }

        public void ActivateDockingCard_TimelineEditor()
        {
            ActivateDockingCard(_DockingCard_TimelineEditor, _DockingBlock2);
        }

        public void ActivateDockingCard_AnimTimelineEditor()
        {
            ActivateDockingCard(_DockingCard_AnimTimelineEditor, _DockingBlock2);
        }

        public void ActivateDockingCard_Cinematic()
        {
            DockingBlock DockingBlock = _DockingCard_Project.GetDockingBlock();
            ActivateDockingCard(_DockingCard_CinematicEditor, DockingBlock);
        }

        public void ActivateDockingCard_MaterialEditor(DockingCard Card)
        {
            ActivateDockingCard(Card, _DockingBlockScene);
        }

        public void ActivateDockingCard_WorkflowEditor(DockingCard Card)
        {
            ActivateDockingCard(Card, _DockingBlockScene);
        }

        void ActivateDockingCard_WorldPartition()
        {
            ActivateDockingCard(_DockingCard_WorldPartition, _DockingBlock3);
        }

        //void ActivateDockingCard_AutomationGreatwall()
        //{
        //    ActivateDockingCard(_DockingCard_AutomationGreatWallTemporay, _DockingBlock3);
        //}

        public void ActivateDockingCard_TextureEditor()
        {
            ActivateDockingCard(_DockingCard_TextureEditor, _DockingBlockScene);
        }
        public void ActivateDockingCard_TextureEditor(DockingCard Card, DockingBlock Block = null)
        {
            if (Block == null)
            {
                Block = _DockingBlockScene;
            }
            ActivateDockingCard(Card, Block);
        }
        public void ActivateDockingCard_EcotopeMapEditor(DockingCard Card)
        {
            ActivateDockingCard(Card, _DockingBlock2);
        }
        public void ActivateDockingCard_PrefabEditor()
        {
            ActivateDockingCard(_DockingCard_PrefabEditor, _DockingBlockScene);
        }

        public void ActivateDockingCard_PreviewScene()
        {
            ActivateDockingCard(_DockingCard_PreviewScene, _DockingBlockScene);
        }

        public void ActivateDockingCard_Tasks()
        {
            ActivateDockingCard(_DockingCard_Tasks, _DockingBlock3);
        }

        public void ActivateDockingCard_CaptureComparer()
        {
            ActivateDockingCard(_DockingCard_CaptureComparer, _DockingBlockScene);
        }

        public void ActivateDockingCard_ImGuiConsole()
        {
            ActivateDockingCard(_DockingCard_ImGuiConsole, _DockingBlockScene);
        }

        public void OnDeviceResize(Device Sender, int Width, int Height)
        {
            bool bDeviceMinimized = GetDevice().IsMinimized();
            bool bDeviceMaximized = GetDevice().IsMaximized();

            _FrameUI.OnDeviceResize(Width, Height);
            GameMainMenuUI.GetInstance().OnDeviceResize(Width, Height);
            GameToolBarUI.GetInstance().OnDeviceResize(Width, Height);
            FPSDisplayUI.GetInstance().OnDeviceResize(Width, Height);
            ConsoleUI.GetInstance().OnDeviceResize(Width, Height);

            UIManager UIManager = GetUIManager();
            Control Root = UIManager.GetRoot();

            int StatusBarOffsetY = DockingControl.DOCKING_INTERVAL * 2 - 1;

            int DockingControlX = 0;
            int DockingControlY = TitleHeight;
            int DockingControlWidth = Root.GetWidth();
            int DockingControlHeight = Root.GetHeight() - DockingControlY - StatusBarHeight;
            DockingControlHeight += StatusBarOffsetY;

            if (_DockingControl != null)
            {
                _DockingControl.SetPosition(DockingControlX, DockingControlY, DockingControlWidth, DockingControlHeight);
            }

            UIManager.Invalidate();
        }

        void OnDeviceApplicationActivate(Device Sender, bool bActivated)
        {
            ShaderWatcher.Instance.OnDeviceApplicationActivate(bActivated);
            if (bActivated)
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    GetDevice().SetConversionStatus(false, false);
                });
            }
        }

        void OnDeviceActivate(Device Sender, bool bActivated)
        {
            _FrameUI.OnDeviceActivate(bActivated);
            GetUIManager().Invalidate();
        }

        public void OnDeviceDropFilesBegin(Device Sender)
        {
            ProjectUI.GetInstance().OnDeviceDropFilesBegin(Sender);
        }

        public void OnDeviceDropFilesFile(Device Sender, string Filename)
        {
            ProjectUI.GetInstance().OnDeviceDropFilesFile(Sender, Filename);
        }

        public void OnDeviceDropFilesEnd(Device Sender)
        {
            ProjectUI.GetInstance().OnDeviceDropFilesEnd(Sender);
        }

        void OnDevicePaint(Device Sender)
        {

        }

        void OnDeviceClose(Device Sender, ref bool bNotToClose)
        {
            EditorScene EditorScene = EditorScene.GetInstance();
            bool bDirty = EditorScene.GetDirty();
            if (bDirty)
            {
                bNotToClose = true;
                bool bShowDialogOnNewScene = false;
                ShowSaveChangesDialog((bool bSaved) =>
                {
                    Sender.Destroy();
                }, bShowDialogOnNewScene);
            }
            SaveUserConfig();
        }

        void OnDeviceDestroy(Device Sender)
        {
            SystemHelper.ExitProcess(0);
        }

        void OnUIManagerPreKey(UIManager Sender, KeyAction Action, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bControlOnly = bControl && !bShift && !bAlt;
            bool bShiftOnly = !bControl && bShift && !bAlt;
            bool bAltOnly = !bControl && !bShift && bAlt;
            bool bNone = !bControl && !bShift && !bAlt;
            bool bControlShift = bControl && bShift && !bAlt;
            bool bControlAlt = bControl && !bShift && bAlt;
            bool bAltShift = !bControl && bShift && bAlt;
            if (DialogUIManager.GetInstance().ShowingDialogUI())
            {
                return;
            }
            ShortcutConfig ShortcutConfig = ShortcutConfig.GetInstance();
            if (Action == KeyAction.Down)
            {
                // File
                if (ShortcutConfig.IsShortcutDown(GetDevice(), "FileSave", Key))
                {
                    Control FocusedControl = GetUIManager().GetFocusControl();
                    if (FocusedControl == null)
                    {
                        return;
                    }
                    object TagObject = FocusedControl.GetTagObject();
                    if (TagObject is NodeGraphView)
                    {
                        (TagObject as NodeGraphView).Save();
                    }

                    if (CurveEditorUI.GetInstance().IsFocused())
                    {
                        CurveEditorUI.GetInstance().DoSave();
                    }
                    else if (TimelineEditorUI.GetInstance().IsFocused())
                    {
                        TimelineEditorUI.GetInstance().DoSave();
                    }
                    else if (AnimTimelineEditorUI.GetInstance().IsFocused())
                    {
                        AnimTimelineEditorUI.GetInstance().DoSave();
                    }
                    else if (CinematicUI.GetInstance().IsFocused())
                    {
                        CinematicUI.GetInstance().DoSave();
                    }
                    else if (StoryBoardUI.GetInstance().GetGraphView().IsFocused() ||
                             StateMachineGraphUI.GetInstance().GetGraphView().IsFocused())
                    {
                        StoryBoardUI.GetInstance().OnSave();
                    }
                    else if (EditorSceneUI.GetInstance().IsFocused() || HierarchyUI.GetInstance().IsFocused())
                    {
                        OnMenuItemFileSaveClicked(null);
                    }
                    else if (WorkflowEditorUIManager.Instance.IsFocused())
                    {
                        WorkflowEditorUIManager.Instance.DoSave();
                    }
                }
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "FileExit", Key))
                {
                    Device.Close();
                }
                // Edit
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EditUndo", Key))
                {
                    EditOperationManager EditOperationManager = EditOperationManager.GetInstance();
                    if (EditOperationManager.CanUndo())
                    {
                        OnMenuItemEditUndoClicked(null);
                        bContinue = false;
                    }
                }
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EditRedo", Key))
                {
                    EditOperationManager EditOperationManager = EditOperationManager.GetInstance();
                    if (EditOperationManager.CanRedo())
                    {
                        OnMenuItemEditRedoClicked(null);
                        bContinue = false;
                    }
                }
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EditCopy", Key))
                {
                    if (EditorSceneUI.GetInstance().IsFocused() || HierarchyUI.GetInstance().IsFocused())
                    {
                        HierarchyUI.GetInstance().OnMenuItemCopyClicked(null);
                    }
                }
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EditPaste", Key))
                {
                    if (EditorSceneUI.GetInstance().IsFocused() || HierarchyUI.GetInstance().IsFocused())
                    {
                        HierarchyUI.GetInstance().OnMenuItemPasteClicked(null);
                    }
                }
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "EditDuplicate", Key))
                {
                    OnMenuItemEditDuplicateClicked(null);
                }
                //Etc
                else if (bControlOnly && Key == Key.N)
                {
                    OnMenuItemFileNewSceneClicked(null);
                }
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "FileOpen", Key))
                {
                    OnMenuItemFileOpenSceneClicked(null);
                }
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "FileSaveAs", Key))
                {
                    ShowSceneSaveAsDialog(null);
                }
                else if (ShortcutConfig.IsShortcutDown(GetDevice(), "GameRun", Key))
                {
                    MainUI.GetInstance().OnButtonToolPlayCurrentSceneClicked(null);
                }
                else if (bNone && Key == Key.Delete)
                {
                    Control FocusedControl = GetUIManager().GetFocusControl();
                    if ((FocusedControl is Edit) == false)
                    {
                        if (FocusedControl == null)
                            return;
                        object TagObject = FocusedControl.GetTagObject();
                        if (TagObject is NodeGraphView)
                        {
                            (TagObject as NodeGraphView).Delete();
                        }

                        EditorSceneUI SceneUI = EditorSceneUI.GetInstance();

                        if (SceneUI.IsFocused() || HierarchyUI.GetInstance().IsFocused())
                        {
                            SceneUI.OnDeleteKeyDown();
                            bContinue = false;
                        }
                    }
                }
                else if ((bNone || bControlOnly) && (Key >= Key.Num0 && Key <= Key.Num9))
                {
                    EditorSceneUI SceneUI = EditorSceneUI.GetInstance();
                    if (SceneUI.IsFocused())
                    {
                        BookmarkUI.OnKeyDown(bControlOnly, Key);
                    }
                }
                else if (bNone && Key == Key.Escape)
                {
                    Control FocusedControl = GetUIManager().GetFocusControl();
                    if ((FocusedControl is Edit) == false)
                    {
                        /*StateMachineUI StateMachineUIInstance = StateMachineUI.GetInstance();

                        if (StateMachineUIInstance.IsFocused())
                        {
                            StateMachineUIInstance.DoEscape();
                                  bContinue = false;
                        }*/

                        if (EditorSceneUI.GetInstance().IsFocused() || HierarchyUI.GetInstance().IsFocused())
                        {
                            EditorScene.GetInstance().ClearSelection();
                        }
                    }
                }
                else if (bNone && Key == Key.G)
                {
                    EditorSceneUI SceneUI = EditorSceneUI.GetInstance();
                    if (SceneUI.IsFocused())
                    {
                        // EditorScene.GetInstance().ChangeDrawIcons();
                        // TODO: GameMode Switch
                    }
                }
                else if (bControlOnly && Key == Key.T)
                {
                    //OnMenuItemHelpTestClicked(null);
                }
                else
                {
                    Control Control = GetUIManager().GetFocusControl();
                    if (Device.IsNoneModifiersDown() && !(Control is Edit) && Device.IsRightButtonDown() == false)
                    {
                        if (Key == Key.Q)
                        {
                            OnButtonToolTranslateModeClicked(null);
                        }
                        else if (Key == Key.W)
                        {
                            OnButtonToolTranslateModeClicked(null);
                        }
                        else if (Key == Key.E)
                        {
                            OnButtonToolRotateModeClicked(null);
                        }
                        else if (Key == Key.R)
                        {
                            OnButtonToolScaleModeClicked(null);
                        }
                        else if (Key == Key.F)
                        {
                            HierarchyUI.GetInstance().LocateToCurrentItem();

                        }
                        else if(Key == Key.X)
                        {
                            EditorSceneUI.GetInstance().OnMaximizePreviewCamera();
                        }
                        else if (Key == Key.H)
                        {
                            HierarchyUI.GetInstance().SwitchSelectedVisibility();
                        }
                        else if (Key == Key.V)
                        {
                            HierarchyUI.GetInstance().DropToPointer();
                        }
                        else if (Key == Key.S)
                        {
                            if (CinematicUI.GetInstance().GetEditStatus() == CinematicUI.EditStatus.EnterEdit)
                            {
                                CinematicUI.GetInstance().AutoKeyTRSByS();
                            }
                        }
                    }
                }
            }
        }

        void OnDockingControlLayout(DockingControl Sender)
        {
        }

        public DockingBlock GetSceneDockingBlock()
        {
            return _DockingBlockScene;
        }

        void OnDockingControlLoadDockingBlock(DockingControl Sender, DockingBlock DockingBlock)
        {
            if (DockingBlock.GetDocument())
            {
                _DockingBlockScene = DockingBlock;
            }
        }

        public void OnDockingControlLoadDockingCard(DockingControl Sender, DockingBlock DockingBlock, string DockingCardText, string DockingCardTag, out DockingCard DockingCard, Record Record_Child)
        {
            DockingCard = null;
            List<DockingCard> DockingCards = new List<DockingCard>();
            DockingCards.Add(_DockingCard_Project);
            DockingCards.Add(_DockingCard_Hierarchy);
            DockingCards.Add(_DockingCard_Inspector);
            DockingCards.Add(_DockingCard_ResourceInspector);
            DockingCards.Add(_DockingCard_Terrain);
            DockingCards.Add(_DockingCard_TodSetting);
            DockingCards.Add(_DockingCard_Console);
            DockingCards.Add(_DockingCard_Welcome);
            DockingCards.Add(_DockingCard_FxView);
            DockingCards.Add(_DockingCard_Scene);
            DockingCards.Add(_DockingCard_Game);

            DockingCards.Add(_DockingCard_REDVisualizer);
            DockingCards.Add(_DockingCard_NavMeshBaker);
            DockingCards.Add(_DockingCard_MSAPreview);
            DockingCards.Add(_DockingCard_MEPreview);
            DockingCards.Add(_DockingCard_ParticleSystemEditor);
            DockingCards.Add(_DockingCard_NodeGraph);
            //DockingCards.Add(_DockingCard_StateMachine);
            //DockingCards.Add(_DockingCard_AnimGraph);
            DockingCards.Add(_DockingCard_CurveEditor);
            DockingCards.Add(_DockingCard_LinearColorCurveEditor);
            DockingCards.Add(_DockingCard_TimelineEditor);
            DockingCards.Add(_DockingCard_AnimTimelineEditor);
            //DockingCards.Add(_DockingCard_CinematicEditor);
            //DockingCards.Add(_DockingCard_MaterialEditor);
            DockingCards.Add(_DockingCard_WorldPartition);
            DockingCards.Add(_DockingCard_Tasks);
            DockingCards.Add(_DockingCard_PrefabEditor);
            DockingCards.Add(_DockingCard_PreviewScene);
            DockingCards.Add(_DockingCard_CaptureComparer);
            DockingCards.Add(_DockingCard_ImGuiConsole);


            if (DockingCardTag == "")
            {
                if (DockingCardText == "Project")
                {
                    DockingCard = _DockingCard_Project;
                    return;
                }
                else if (DockingCardText == "Hierarchy")
                {
                    DockingCard = _DockingCard_Hierarchy;
                    return;
                }
                else if (DockingCardText == "Inspector")
                {
                    DockingCard = _DockingCard_Inspector;
                    return;
                }
                else if (DockingCardText == "Console")
                {
                    DockingCard = _DockingCard_Console;
                    return;
                }
                foreach (DockingCard DockingCard1 in DockingCards)
                {
                    if (DockingCard1 != null && DockingCard1.GetText() == DockingCardText)
                    {
                        DockingCard = DockingCard1;
                        break;
                    }
                }
            }
        }

        void OnDockingControlRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            DockingNodeHitTest DockingNodeHitTest = new DockingNodeHitTest();
            DockingNodeHitTest.X = MouseX - _DockingControl.GetScreenX();
            DockingNodeHitTest.Y = MouseY - _DockingControl.GetScreenY();
            _DockingControl.GetDockingRoot().HitTest(DockingNodeHitTest);
            if (DockingNodeHitTest.HitResult == DockingNodeHitResult.HitDockingBlock && DockingNodeHitTest.HitDockingBlock != null)
            {
                DockingCard HitDockingCard = null;
                DockingBlockHitResult DockingBlockHitResult = DockingNodeHitTest.HitDockingBlock.HitTest(MouseX, MouseY, out HitDockingCard);
                if (DockingBlockHitResult == DockingBlockHitResult.HitCardTab && HitDockingCard != null)
                {
                    _CurrentDockingCard = HitDockingCard;
                    ShowContextMenu_DockingCard(MouseX, MouseY);
                    bContinue = false;
                }
            }
        }

        void OnDockingControlLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            DockingNodeHitTest DockingNodeHitTest = new DockingNodeHitTest();
            DockingNodeHitTest.X = MouseX - _DockingControl.GetScreenX();
            DockingNodeHitTest.Y = MouseY - _DockingControl.GetScreenY();
            _DockingControl.GetDockingRoot().HitTest(DockingNodeHitTest);
            if (DockingNodeHitTest.HitResult == DockingNodeHitResult.HitDockingBlock && DockingNodeHitTest.HitDockingBlock != null)
            {
                DockingCard HitDockingCard = null;
                DockingBlockHitResult DockingBlockHitResult = DockingNodeHitTest.HitDockingBlock.HitTest(MouseX, MouseY, out HitDockingCard);
                if (DockingBlockHitResult == DockingBlockHitResult.HitCardTab && HitDockingCard != null)
                {
                    if (HitDockingCard.GetDockingBlock() == _DockingCard_Project.GetDockingBlock())
                    {
                        _CurrentProjectCard = HitDockingCard;
                    }
                }
            }
        }

        public void OnDockingControlNewWindow(DockingControl Sender, Control Dragged, int MouseX, int MouseY)
        {
            Device Device = Sender.GetDevice();
            int X = Device.GetScreenMouseX();
            int Y = Device.GetScreenMouseY();
            int Width = Dragged.GetWidth() + DockingControl.DOCKING_INTERVAL * 2;
            int Height = Dragged.GetHeight() + DockingControl.DOCKING_INTERVAL * 2 + TitleHeight + StatusBarHeight;
            int WindowMinWidth = 400;
            int WindowMinHeight = 300;
            Width = Math.Max(Width, WindowMinWidth);
            Height = Math.Max(Height, WindowMinHeight);
            WindowManager.GetInstance().CreateWindow(X, Y, Width, Height, Dragged);
        }

        void ShowContextMenu_DockingCard(int MouseX, int MouseY)
        {
            ShortcutConfig ShortcutConfig = ShortcutConfig.GetInstance();

            MenuItem MenuItem_CloseDockingCard = new MenuItem();
            MenuItem_CloseDockingCard.SetText("Close");
            MenuItem_CloseDockingCard.ClickedEvent += OnMenuItemCloseDockingCardClicked;

            MenuItem MenuItem_CloseAllButThis = new MenuItem();
            MenuItem_CloseAllButThis.SetText("Close All But This");
            MenuItem_CloseAllButThis.ClickedEvent += OnMenuItemCloseAllButThisClicked;

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            MenuContextMenu.AddMenuItem(MenuItem_CloseDockingCard);
            MenuContextMenu.AddMenuItem(MenuItem_CloseAllButThis);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, MouseX, MouseY);
        }

        void OnMenuItemCloseDockingCardClicked(MenuItem Sender)
        {
            _CurrentDockingCard.CloseCard();
            if (_CurrentDockingCard.GetText().Contains("Cinematic"))
            {
                //Runtime.CrossEngine_DeleteResource(CinematicUI.GetInstance().GetCurveCtrPath());
                CinematicUI.GetInstance().RestoreSceneValue(true);
            }
            _CurrentDockingCard = null;
        }

        void OnMenuItemCloseAllButThisClicked(MenuItem Sender)
        {
            DockingBlock DockingBlock = _CurrentDockingCard.GetDockingBlock();
            if (DockingBlock != null)
            {
                DockingBlock.RemoveDockingCardsExcept(_CurrentDockingCard);
            }
        }

        public void OnMenuItemFileProjectsClicked(MenuItem Sender)
        {
            bool bShowDialogOnNewScene = false;
            ShowSaveChangesDialog((bool bSaved) =>
            {
                RestartEditor();
                GetDevice().Destroy();
            }, bShowDialogOnNewScene);
        }

        public void ShowSaveChangesDialog(SimpleBoolDelegate SimpleBoolDelegate, bool bShowDialogOnNewScene)
        {
            InnerShowSaveChangesDialog((CommonDialogResult Result) =>
            {
                if (Result == CommonDialogResult.Yes)
                {
                    ShowSceneSaveDialog((CommonDialogResult Result1) =>
                    {
                        if (Result1 == CommonDialogResult.OK)
                        {
                            if (SimpleBoolDelegate != null)
                            {
                                SimpleBoolDelegate(true);
                            }
                        }
                    });
                }
                else if (Result == CommonDialogResult.No)
                {
                    if (SimpleBoolDelegate != null)
                    {
                        SimpleBoolDelegate(false);
                    }
                }
                else if (Result == CommonDialogResult.UserDefined1)  // not dirty
                {
                    if (SimpleBoolDelegate != null)
                    {
                        SimpleBoolDelegate(true);
                    }
                }
            }, bShowDialogOnNewScene);
        }

        public void InnerShowSaveChangesDialog(SimpleCommonDialogResultDelegate SimpleCommonDialogResultDelegate, bool bShowDialogOnNewScene)
        {
            EditorScene EditorScene = EditorScene.GetInstance();
            bool bDirty = EditorScene.GetDirty() || CinematicUI.GetInstance().GetIsModified();
            string CurrentSceneFilename = EditorScene.GetCurrentSceneFilename();
            if (bDirty)
            {
                CommonDialogUI CommonDialogUI = new CommonDialogUI();
                CommonDialogUI.Initialize(GetUIManager(), "Tips", "Do you want to save changes of modified scenes?", CommonDialogType.YesNoCancel);
                CommonDialogUI.CloseEvent += (CommonDialogUI Sender1, CommonDialogResult Result) =>
                {
                    SimpleCommonDialogResultDelegate(Result);
                };
                DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
            }
            else if (bShowDialogOnNewScene && CurrentSceneFilename == "")
            {
                SimpleCommonDialogResultDelegate(CommonDialogResult.Yes);
            }
            else
            {
                SimpleCommonDialogResultDelegate(CommonDialogResult.UserDefined1);  // not dirty
            }
        }

        public void NewScene(bool hewAll = true)
        {
            EditorScene.GetInstance().NewScene();
            if (hewAll)
            {
                MSAPreviewScene.GetInstance().NewScene();
                MEPreviewScene.GetInstance().NewScene();
                ParticleSystemScene.GetInstance().NewScene();
                PrefabScene.GetInstance().NewScene();
                PreviewScene.GetInstance().NewScene();
                //PCGScene.GetInstance().NewScene();
            }
            UpdateTitle();
        }

        public void OnMenuItemFileNewSceneClicked(MenuItem Sender)
        {
            bool bShowDialogOnNewScene = false;
            ShowSaveChangesDialog((bool bSaved) =>
            {
                CloseDockingCard();
                NewScene(false);
                ActivateDockingCard_Scene();
                ShowNewSceneUI(() =>
                {
                    WorldPartitionUI.GetInstance().Refresh();
                });
            }, bShowDialogOnNewScene);
        }

        public void ShowNewSceneUI(SimpleDelegate SimpleDelegate)
        {
            NewSceneUI NewSceneUI = new NewSceneUI();
            NewSceneUI.Initialize(GetUIManager(), "New Scene Setting");
            NewSceneUI.InputedEvent += (NewSceneUI Sender1) =>
            {
                if (SimpleDelegate != null) SimpleDelegate();
            };
            DialogUIManager.GetInstance().ShowDialogUI(NewSceneUI);
        }

        public void ShowSceneSaveAsDialog(SimpleCommonDialogResultDelegate SimpleCommonDialogResultDelegate)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Scene Files";
            PathInputUIFilterItem.Extensions.Add("world");

            string DefaultDriveName = _ProjectName;
            string DefaultDrivePath = _ProjectDirectory;

            _bSceneSaveAsDialogInputed = false;

            PathInputUIEx PathInputUI = new PathInputUIEx();
            PathInputUI.AddDrive(DefaultDriveName, DefaultDrivePath);
            PathInputUI.Initialize(GetUIManager(), "Save Scene As", PathInputUIType.SaveFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                _bSceneSaveAsDialogInputed = true;
                EditorScene.GetInstance().SaveScene(PathInputed);
                ProjectUI.GetInstance().UpdateAll();
                UpdateTitle();
            };
            PathInputUI.DialogCloseEvent += (DialogUI Sender) =>
            {
                if (SimpleCommonDialogResultDelegate != null)
                {
                    if (_bSceneSaveAsDialogInputed)
                    {
                        SimpleCommonDialogResultDelegate(CommonDialogResult.OK);
                    }
                    else
                    {
                        SimpleCommonDialogResultDelegate(CommonDialogResult.Cancel);
                    }
                    _bSceneSaveAsDialogInputed = false;
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        public void ShowSceneSaveDialog(SimpleCommonDialogResultDelegate SimpleCommonDialogResultDelegate)
        {
            EditorScene EditorScene = EditorScene.GetInstance();
            string CurrentSceneFilename = EditorScene.GetCurrentSceneFilename();
            if (CurrentSceneFilename != null && CurrentSceneFilename != "")
            {
                EditorScene.SaveScene();
                ProjectUI.GetInstance().UpdateAll();
                CinematicUI.GetInstance().DoSave();
                if (SimpleCommonDialogResultDelegate != null)
                {
                    SimpleCommonDialogResultDelegate(CommonDialogResult.OK);
                }
            }
            else
            {
                ShowSceneSaveAsDialog((CommonDialogResult Result) =>
                {
                    if (Result == CommonDialogResult.OK)
                    {
                        if (SimpleCommonDialogResultDelegate != null)
                        {
                            SimpleCommonDialogResultDelegate(CommonDialogResult.OK);
                        }
                    }
                    else
                    {
                        if (SimpleCommonDialogResultDelegate != null)
                        {
                            SimpleCommonDialogResultDelegate(CommonDialogResult.Cancel);
                        }
                    }
                });
            }
        }

        public void GeneralOpenScene(string Path)
        {
            bool bShowDialogOnNewScene = false;

            ShowSaveChangesDialog((bool bSaved) =>
            {
                EditorScene.GetInstance().LoadScene(Path);
                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                RecentList RecentList = RecentList.GetInstance();
                RecentList.RemoveDeletedRecentItems();
                RecentList.AddRecentItem(Path1);
                RecentList.SaveRecentItemList();
                WelcomeUI.GetInstance().RefreshUI();
                UpdateTitle();
                ActivateDockingCard_Scene();
                CinematicUI.GetInstance().ClearAsset();
                CloseDockingCard();
                CurveEditorUI.GetInstance().Clear();
                ResetAutoSavingCountDown();
            }, bShowDialogOnNewScene);
        }

        public void OnMenuItemFileOpenSceneClicked(MenuItem Sender)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Scene Files";
            PathInputUIFilterItem.Extensions.Add("world");

            string DefaultDriveName = _ProjectName;
            string DefaultDrivePath = _ProjectDirectory;

            PathInputUIEx PathInputUI = new PathInputUIEx();
            PathInputUI.AddDrive(DefaultDriveName, DefaultDrivePath);
            PathInputUI.Initialize(GetUIManager(), "Open Scene", PathInputUIType.OpenFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                GeneralOpenScene(PathInputed);
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        public void OnMenuItemFileSaveClicked(MenuItem Sender)
        {
            ShowSceneSaveDialog(null);
        }

        public void OnMenuItemFileSaveAsClicked(MenuItem Sender)
        {
            ShowSceneSaveAsDialog(null);
        }

        public void ResetAutoSavingCountDown()
        {
            EditorConfig EditorConfig = EditorConfig.GetInstance();
            long MilliSeconds = EditorConfig.AutoSaveMinutes * 60 * 1000;
            _TimeAutoSaving = MilliSeconds;
            _CountDownAutoSaving = _TimeAutoSaving;
        }

        void UpdateAutoSavingCountDown(long TimeElapsed)
        {
            if (_TimeAutoSaving > 0)
            {
                if (_CountDownAutoSaving > 0)
                {
                    _CountDownAutoSaving -= TimeElapsed;
                    if (_CountDownAutoSaving <= 0)
                    {
                        DoAutoSaving();
                        _CountDownAutoSaving = _TimeAutoSaving;
                    }
                }
            }
        }

        void DoAutoSaving()
        {
            EditorScene EditorScene = EditorScene.GetInstance();
            if (EditorScene.GetDirty())
            {
                string CurrentSceneFilename = EditorScene.GetCurrentSceneFilename();
                if (CurrentSceneFilename != null && CurrentSceneFilename != "")
                {
                    EditorScene.SaveScene();
                }
            }
        }

        void SetAutoSaveMinutes(int AutoSaveMinutes)
        {
            EditorConfig EditorConfig = EditorConfig.GetInstance();
            EditorConfig.AutoSaveMinutes = AutoSaveMinutes;
            EditorConfig.SaveEditorConfig();

            ResetAutoSavingCountDown();
        }

        public void OnMenuItemFileAutoSaveNoAutoSaveClicked(MenuItem Sender)
        {
            SetAutoSaveMinutes(0);
        }

        public void OnMenuItemFileAutoSaveAutoSaveIn1MinuteClicked(MenuItem Sender)
        {
            SetAutoSaveMinutes(1);
        }

        public void OnMenuItemFileAutoSaveAutoSaveIn5MinutesClicked(MenuItem Sender)
        {
            SetAutoSaveMinutes(5);
        }

        public void OnMenuItemFileAutoSaveAutoSaveIn10MinutesClicked(MenuItem Sender)
        {
            SetAutoSaveMinutes(10);
        }

        public void OnMenuItemFileAutoSaveAutoSaveIn30MinutesClicked(MenuItem Sender)
        {
            SetAutoSaveMinutes(30);
        }

        public void OnMenuItemUpdateResourceClicked(MenuItem Sender)
        {
            ResourceManager.Instance().BuildResourceList();
        }

        public void OnMenuItemFileExitClicked(MenuItem Sender)
        {
            GetDevice().Close();
        }

        public void OnMenuItemEditUndoClicked(MenuItem Sender)
        {
            EditOperationManager EditOperationManager = EditOperationManager.GetInstance();
            if (EditOperationManager.CanUndo())
            {
                EditOperationManager.Undo();
                EditorScene.GetInstance().SetDirty();
            }
        }

        public void OnMenuItemEditRedoClicked(MenuItem Sender)
        {
            EditOperationManager EditOperationManager = EditOperationManager.GetInstance();
            if (EditOperationManager.CanRedo())
            {
                EditOperationManager.Redo();
                EditorScene.GetInstance().SetDirty();
            }
        }

        public void OnMenuItemEditCopyClicked(MenuItem Sender)
        {
            HierarchyUI.GetInstance().OnMenuItemCopyClicked(Sender);
        }

        public void OnMenuItemEditPasteClicked(MenuItem Sender)
        {
            HierarchyUI.GetInstance().OnMenuItemPasteClicked(Sender);
        }

        public void OnMenuItemEditDeleteClicked(MenuItem Sender)
        {
            HierarchyUI.GetInstance().OnMenuItemDeleteClicked(Sender);
        }

        public void OnMenuItemEditDuplicateClicked(MenuItem Sender)
        {
            Control Control = GetUIManager().GetFocusControl();
            if (Control != null)
            {
                if (Control == _DockingCard_Hierarchy || Control.IsChildOf(_DockingCard_Hierarchy) ||
                    Control == _DockingCard_Scene || Control.IsChildOf(_DockingCard_Scene))
                {
                    HierarchyUI.GetInstance().DuplicateSelectedItem();
                }
                else if (Control == _DockingCard_Project || Control.IsChildOf(_DockingCard_Project))
                {
                    ProjectUI.GetInstance().DuplicateSelectedFiles();
                }
            }
        }

        public void OnMenuItemEditConfigClicked(MenuItem Sender)
        {
            EditorConfigUI EditorConfigUI = new EditorConfigUI();
            EditorConfigUI.Initialize(GetUIManager());
            DialogUIManager.GetInstance().ShowDialogUI(EditorConfigUI);
        }

        public void OnMenuItemAssetsImportAssetClicked(MenuItem Sender)
        {
            ProjectUI.GetInstance().OnMenuItemImportAssetClicked(null);
        }

        public void OnMenuItemAssetsImportAssetsClicked(MenuItem Sender)
        {
            ProjectUI.GetInstance().OnMenuItemImportAssetsClicked(null);
        }

        public void OnMenuItemAssetsRecompileShadersClicked(MenuItem Sender, string shaderDirectory)
        {
            ProjectUI.GetInstance().OnMenuItemRecompileShadersClicked(shaderDirectory);
        }

        public void OnMenuItemAssetsRegenerateFxClicked(MenuItem Sender, List<string> fxDirectories)
        {
            _ = ProjectUI.GetInstance().OnMenuItemRegenerateAllFxClicked(fxDirectories);
        }

        public void OnMenuItemGenerateGenerateFoliageClicked(MenuItem Sender)
        {
            //Runtime.EditorPCG_EcotopeMap_UpdateTexture();
            // PCGManager.GetInstance().GenerateFoliage();
        }

        //public void OnMenuItemGenerateGenerateAirportJsonClicked(MenuItem Sender)
        //{
        //    ProjectUI.GetInstance().ExportAirportJson();
        //}

        public void OnMenuItemGenerateGenerateMSDFClicked(MenuItem Sender)
        {
            ProjectUI.GetInstance().GenerateMSDF();
        }


        public void OnMenuItemDebugPlayGameWithProjectConfigClicked(MenuItem Sender)
        {
            GameLauncher.GetInstance().LaunchGame();
        }

        Entity SearchForGameCamera(Entity Entity1)
        {
            if (Entity1.GetComponent(typeof(Camera)) != null)
            {
                if (Entity1.GetName() != "EditorCamera")
                {
                    return Entity1;
                }
            }
            foreach (Entity Child in Entity1.Children)
            {
                Entity Entity = SearchForGameCamera(Child);
                if (Entity != null)
                {
                    return Entity;
                }
            }
            return null;
        }

        bool IsThereAnyGameCamera()
        {
            Entity Root = EditorScene.GetInstance().GetRoot();
            return SearchForGameCamera(Root) != null;
        }

        bool CheckGameCamera()
        {
            if (!IsThereAnyGameCamera())
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "There is no game camera in this scene.");
                return false;
            }
            return true;
        }

        public void OnMenuItemDebugPlayGameClicked(MenuItem Sender)
        {
            if (!CheckGameCamera())
            {
                return;
            }

            bool bShowDialogOnNewScene = true;
            ShowSaveChangesDialog((bool bSaved) =>
            {
                if (bSaved)
                {
                    StoryBoardUI StoryBoardUI = StoryBoardUI.GetInstance();
                    StoryBoardUI.OnSave();

                    string ScenePath = EditorScene.GetInstance().GetCurrentSceneFilename();
                    GameLauncher.GetInstance().LaunchGame(ScenePath);
                }
            }, bShowDialogOnNewScene);
        }

        public void OnMenuItemDebugPlayCurrentSceneClicked(MenuItem Sender)
        {
            if (!CheckGameCamera())
            {
                return;
            }

            bool bShowDialogOnNewScene = true;
            ShowSaveChangesDialog((bool bSaved) =>
            {
                if (bSaved)
                {
                    StoryBoardUI StoryBoardUI = StoryBoardUI.GetInstance();
                    StoryBoardUI.OnSave();

                    string ScenePath = EditorScene.GetInstance().GetCurrentSceneFilename();
                    if (ScenePath != "")
                    {
                        if (!GameSceneUI.GetInstance().IsPIEPlaying)
                        {
                            GameScene.GetInstance().StartPIEPlay();
                        }
                        else
                        {
                            GameScene.GetInstance().StopPIEPlay();
                        }
                    }
                }
            }, bShowDialogOnNewScene);
        }

        public void OnMenuItemBuildProjectClicked(MenuItem Sender)
        {
            BuildProjectUI BuildProjectUI = BuildProjectUI.GetInstance();
            BuildProjectUI.Initialize(GetUIManager());
            DialogUIManager.GetInstance().ShowDialogUI(BuildProjectUI);
        }

        public void OnMenuItemViewProjectClicked(MenuItem Sender)
        {
            ActivateDockingCard_Project();
        }

        public void OnMenuItemViewHierarchyClicked(MenuItem Sender)
        {
            ActivateDockingCard_Hierarchy();
        }

        public void OnMenuItemViewInspectorClicked(MenuItem Sender)
        {
            ActivateDockingCard_Inspector();
        }

        public void OnMenuItemViewResourceInspectorClicked(MenuItem Sender)
        {
            ActivateDockingCard_ResourceInspector();
        }

        public void OnMenuItemViewTerrainClicked(MenuItem Sender)
        {
            ActivateDockingCard_Terrain();
        }
        public void OnMenuItemViewTodClicked(MenuItem Sender)
        {
            ActivateDockingCard_TodSetting();
        }

        public void OnMenuItemViewConsoleClicked(MenuItem Sender)
        {
            ActivateDockingCard_Console();
        }

        public void OnMenuItemViewWelcomeClicked(MenuItem Sender)
        {
            ActivateDockingCard_Welcome();
        }

        public void OnMenuItemViewFxViewClicked(MenuItem Sender)
        {
            ActivateDockingCard_FxView();
        }

        public void OnMenuItemViewSceneClicked(MenuItem Sender)
        {
            ActivateDockingCard_Scene();
        }

        public void OnMenuItemViewGameClicked(MenuItem Sender)
        {
            ActivateDockingCard_Game();
        }

        public void OnMenuItemViewCaptureComparerClicked(MenuItem Sender)
        {
            ActivateDockingCard_CaptureComparer();
        }

        public void OnMenuItemImGuiConsoleClicked(MenuItem Sender)
        {
            ActivateDockingCard_ImGuiConsole();
        }


        public void OnMenuItemViewREDVisualizerClicked(MenuItem Sender)
        {
            ActivateDockingCard_REDVisualizer();
        }



        public void OnMenuItemViewNavMeshBakerClicked(MenuItem Sender)
        {
            ActivateDockingCard_NavMeshBaker();
        }

        public void OnMenuItemViewMSAPreivewClicked(MenuItem Sender)
        {
            ActivateDockingCard_MSAPreivew();
        }

        public void OnMenuItemViewMEPreviewClicked(MenuItem Sender)
        {
            ActivateDockingCard_MEPreview();
        }

        public void OnMenuItemViewNodeGraphClicked(MenuItem Sender)
        {
            ActivateDockingCard_NodeGraph();
        }

        public void OnMenuItemViewStateMachineClicked(MenuItem Sender)
        {
            ActivateDockingCard_StateMachine();
        }

        public void OnMenuItemViewAnimatorClicked(MenuItem Sender)
        {
            ActivateDockingCard_Animator();
        }


        public void OnMenuItemViewCurveClicked(MenuItem Sender)
        {
            ActivateDockingCard_CurveEditor();
        }

        public void OnMenuItemViewLinearColorCurveClicked(MenuItem Sender)
        {
            ActivateDockingCard_LinearColorCurveEditor();
            LinearColorCurveEditorUI.GetInstance().InspectAdjustColor();
        }

        public void OnMenuItemViewTimelineClicked(MenuItem Sender)
        {
            ActivateDockingCard_TimelineEditor();
        }

        public void OnMenuItemViewAnimTimelineClicked(MenuItem Sender)
        {
            ActivateDockingCard_AnimTimelineEditor();
        }

        public void OnMenuItemViewCinematicClicked(MenuItem Sender)
        {
            ActivateDockingCard_Cinematic();
        }

        public void OnMenuItemViewWorldPartition(MenuItem Sender)
        {
            ActivateDockingCard_WorldPartition();
        }

        public void OnMenuItemOptionsScreenShotModeClicked(MenuItem Sender)
        {
            EditorScene EditorScene = EditorScene.GetInstance();
            bool bScreenShotMode = EditorScene.GetScreenShotMode();
            bScreenShotMode = !bScreenShotMode;
            EditorScene.SetScreenShotMode(bScreenShotMode);
            EditorSceneUI.GetInstance().SetScreenShotMode(bScreenShotMode);
        }

        public void OnMenuItemOptionsOpenConfigDirectoryClicked(MenuItem Sender)
        {
            ProcessHelper.OpenFolder(EditorConfig.EDITOR_CONFIG_RELATIVE_PATH);
        }

        void SetAverageFrames(int AverageFrames)
        {
            EditorConfig EditorConfig = EditorConfig.GetInstance();
            EditorConfig.AverageFrames = AverageFrames;
            EditorConfig.SaveEditorConfig();
            FPSDisplayUI.GetInstance().SetAverageFrames(AverageFrames);
        }

        public void OnMenuItemShowAverageSceneLuminanceClicked(MenuItem Sender)
        {
            var EditorWorld = EditorScene.GetInstance().GetWorld();
            PostProcessVolumeSystemG.ShowAverageSceneLuminance(EditorWorld.GetNativePointer());
        }

        public void OnMenuItemOptionsAverageCount1Clicked(MenuItem Sender)
        {
            SetAverageFrames(1);
        }

        public void OnMenuItemOptionsAverageCount20Clicked(MenuItem Sender)
        {
            SetAverageFrames(20);
        }

        public void OnMenuItemOptionsAverageCount60Clicked(MenuItem Sender)
        {
            SetAverageFrames(60);
        }

        public void OnMenuItemOptionsConfigClicked(MenuItem Sender)
        {
            OnMenuItemEditConfigClicked(Sender);
        }

        public void OnMenuItemHelpOfficialSiteClicked(MenuItem Sender)
        {
            ProcessHelper.OpenWebPage("https://luaperfect.net");
        }

        public void OnMenuItemHelpTestClicked(MenuItem Sender)
        {
            /*
            TerrainEditor TerrainEditor = TerrainEditor.GetInstance();
            Entity TerrainEntity = TerrainEditor.SearchTerrainEntity();
            if (TerrainEntity != null)
            {
                TerrainTraverser TerrainTraverser = new TerrainTraverser(TerrainEntity, 
                    (Double3 Center, Double3 Extent) => 
                    {
                        Console.WriteLine("Center: {0} {1} {2}, Extent: {3} {4} {5}", Center.x, Center.y, Center.z, Extent.x, Extent.y, Extent.z);
                        return true;
                    },
                    (TileIndex TileIndex) => 
                    {
                        Console.WriteLine("Traverse: {0} {1} {2} {3} {4}", TileIndex.mBlockX, TileIndex.mBlockY, TileIndex.mLevel, TileIndex.mTileX, TileIndex.mTileY);
                    });
                TerrainTraverser.TraverseTerrainTiles();
            }
            */
        }

        public void OnMenuItemHelpAboutClicked(MenuItem Sender)
        {
            AboutUI AboutUI = new AboutUI();
            AboutUI.Initialize(GetUIManager());
            DialogUIManager.GetInstance().ShowDialogUI(AboutUI);
        }

        public void OnMenuItemTasksClicked(MenuItem Sender)
        {
            ActivateDockingCard_Tasks();
        }

        public void OnButtonToolSaveClicked(Button Sender)
        {
            OnMenuItemFileSaveClicked(null);
        }

        public void OnButtonToolUndoClicked(Button Sender)
        {
            OnMenuItemEditUndoClicked(null);
        }

        public void OnButtonToolRedoClicked(Button Sender)
        {
            OnMenuItemEditRedoClicked(null);
        }

        public void OnButtonToolMoveModeClicked(Button Sender)
        {
            OnButtonToolTranslateModeClicked(null);
        }

        public void OnButtonToolTranslateModeClicked(Button Sender)
        {
            ManipulatorManager ManipulatorManager = ManipulatorManager.GetInstance();
            if (ManipulatorManager.GetGlobal())
            {
                ManipulatorManager.SetManipulatorType(ManipulatorType.GlobalTranslator);
            }
            else
            {
                ManipulatorManager.SetManipulatorType(ManipulatorType.LocalTranslator);
            }
        }

        public void OnButtonToolRotateModeClicked(Button Sender)
        {
            ManipulatorManager ManipulatorManager = ManipulatorManager.GetInstance();
            if (ManipulatorManager.GetGlobal())
            {
                ManipulatorManager.SetManipulatorType(ManipulatorType.GlobalRotator);
            }
            else
            {
                ManipulatorManager.SetManipulatorType(ManipulatorType.LocalRotator);
            }
        }

        public void OnButtonToolScaleModeClicked(Button Sender)
        {
            ManipulatorManager ManipulatorManager = ManipulatorManager.GetInstance();
            ManipulatorManager.SetManipulatorType(ManipulatorType.LocalScaler);
        }

        public void OnButtonToolLocalModeClicked(Button Sender)
        {
            ManipulatorManager ManipulatorManager = ManipulatorManager.GetInstance();
            ManipulatorType ManipulatorType = ManipulatorManager.GetManipulatorType();
            if (ManipulatorType == ManipulatorType.GlobalTranslator)
            {
                ManipulatorType = ManipulatorType.LocalTranslator;
            }
            else if (ManipulatorType == ManipulatorType.GlobalRotator)
            {
                ManipulatorType = ManipulatorType.LocalRotator;
            }
            ManipulatorManager.SetManipulatorType(ManipulatorType);
            ManipulatorManager.SetGlobal(false);
        }

        public void OnButtonToolGlobalModeClicked(Button Sender)
        {
            ManipulatorManager ManipulatorManager = ManipulatorManager.GetInstance();
            ManipulatorType ManipulatorType = ManipulatorManager.GetManipulatorType();
            if (ManipulatorType == ManipulatorType.LocalTranslator)
            {
                ManipulatorType = ManipulatorType.GlobalTranslator;
            }
            else if (ManipulatorType == ManipulatorType.LocalRotator)
            {
                ManipulatorType = ManipulatorType.GlobalRotator;
            }
            ManipulatorManager.SetManipulatorType(ManipulatorType);
            ManipulatorManager.SetGlobal(true);
        }

        public void OnButtonToolTypeScriptCompile(Button Sender) => _ = TypeScriptCompile();

        public async System.Threading.Tasks.Task TypeScriptCompile()
        {
            // index.d.ts of build in class 
            {
                string TypeScriptDeclaration = CrossEngineApi.GenerateTypeScriptDeclaration(true);

                DirectoryHelper.CreateDirectory(EditorUtilities.GetResourceDirectory() + "/EngineResource/Puerts/Typing/cpp");
                File.WriteAllText(EditorUtilities.GetResourceDirectory() + "/EngineResource/Puerts/Typing/cpp/index.d.ts", TypeScriptDeclaration);
            }

            // index.d.ts of all class
            {
                string TypeScriptDeclaration = CrossEngineApi.GenerateTypeScriptDeclaration(false);

                DirectoryHelper.CreateDirectory(MainUI.GetInstance().GetProjectDirectory() + "/Typing/cpp");
                File.WriteAllText(MainUI.GetInstance().GetProjectDirectory() + "/Typing/cpp/index.d.ts", TypeScriptDeclaration);
            }

            string ProjectTscConfig = MainUI.GetInstance().GetProjectDirectory() + "/tsconfig.json";

            // Copy tsconfig file
            File.Copy(EditorUtilities.GetResourceDirectory() + "/EngineResource/Puerts/Typing/tsconfig.json", ProjectTscConfig, true);

            // Copy *.d.ts file
            DirectoryHelper.CopyDirectory(EditorUtilities.GetResourceDirectory() + "/EngineResource/Puerts/Typing/puerts", MainUI.GetInstance().GetProjectDirectory() + "/Typing/puerts");

            // call tsc to build TypeScript
            // "cmd", $"/c tsc"
            {
                bool bWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
                string tscScript = EditorUtilities.GetResourceDirectory() + "/EngineResource/Puerts/node_modules/typescript/bin/tsc";
                string nodePath = bWindows
                    ? EditorUtilities.GetResourceDirectory() + "/../Tools/node-v22.11.0-win-x64/node.exe"
                    : "node";
                string shell = bWindows ? "cmd" : "/bin/sh";

                string commandArg = $"\"{nodePath}\" \"{tscScript}\" -p \"{ProjectTscConfig}\"";
                if (bWindows)
                {
                    commandArg = $"/c \"{commandArg}\"";
                }
                else
                {
                    commandArg = commandArg.Replace("\"", "\\\"");
                    commandArg = $"-c \"{commandArg}\"";
                }
                
                ProcessStartInfo startInfo = new ProcessStartInfo(shell, commandArg)
                {
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                };

                using (Process process = Process.Start(startInfo))
                {
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();

                    await process.WaitForExitAsync();
                    if (string.IsNullOrEmpty(output) && string.IsNullOrEmpty(error))
                    {
                        EditorLogger.Log(LogMessageType.Information, "Build TypeScript Success");
                    }
                    else
                    {
                        EditorLogger.Log(LogMessageType.Error, string.Format("{0}\n{1}", output, error));
                    }
                }
            }
        }

        public void OnComboBoxEditorModeSelect(ComboBox Sender)
        {
            if (Sender.GetSelectedItemIndex() == 0)
            {
                EditorSceneUI.GetInstance().SetEditorMode(EditorMode.Object_Mode);
                EditorSceneUI.GetInstance().DeletePaintSphere();
                ActivateDockingCard_Inspector();
            }
            else if (Sender.GetSelectedItemIndex() == 1)
            {
                EditorSceneUI.GetInstance().SetEditorMode(EditorMode.Paint_Mode);
                EditorSceneUI.GetInstance().CreatePaintSphere();
                //PaintToolEditor.GetInstance().UpdateBorderCheckedStates();
                EditorScene.GetInstance().SetDirty();
                //ActivateDockingCard_PaintTool();
            }
            else if (Sender.GetSelectedItemIndex() == 2)
            {
                EditorSceneUI.GetInstance().SetEditorMode(EditorMode.Terrain_Mode);
                EditorSceneUI.GetInstance().CreatePaintSphere();
                ActivateDockingCard_Terrain();
            }
            else if (Sender.GetSelectedItemIndex() == 3)
            {
                EditorSceneUI.GetInstance().SetEditorMode(EditorMode.PCG_Mode);
                EditorSceneUI.GetInstance().CreatePaintSphere();
            }

            EditorSceneUI.GetInstance().SwitchObject();
        }

        public void OnButtonToolPlayGameFromConfigClicked(Button Sender)
        {
            MainUI.GetInstance().OnMenuItemDebugPlayGameWithProjectConfigClicked(null);
        }

        public void OnButtonToolPlayGameClicked(Button Sender)
        {
            MainUI.GetInstance().OnMenuItemDebugPlayGameClicked(null);
        }

        public void OnButtonToolPlayCurrentSceneClicked(Button Sender)
        {
            MainUI.GetInstance().OnMenuItemDebugPlayCurrentSceneClicked(null);
        }

        void RestartEditor()
        {
            string DotNetProgramPath = EditorUtilities.GetDotNetProgramPath();
            string ExecutableDirectory = DirectoryHelper.GetExecutableDirectory();
            string EditorProgramPath = string.Format("\"{0}/CrossEditor.exe\"", ExecutableDirectory);

            EditorConfig.GetInstance().LastProjectPath = "";
            EditorConfig.GetInstance().SaveEditorConfig();

            SaveUserConfig();

            ProcessHelper.Execute(EditorProgramPath, "");
            SystemHelper.ExitProcess(0);
        }

        public DockingCard GetCurrentProjectCard()
        {
            return _CurrentProjectCard;
        }

        public void DockingBlockSceneRemove(DockingCard DockingCard)
        {
            _DockingBlockScene.RemoveDockingCard(DockingCard);
            DockingCard.CloseCard();
        }

        public void OnMenuItemTestSuperResolutionClicked(MenuItem Sender)
        {
            string inFile = MainUI.GetInstance().GetProjectDirectory() + "/Saved/input.jpg";
            string outFile = MainUI.GetInstance().GetProjectDirectory() + "/Saved/output.bmp";
            //Runtime.SuperResolution_AddTask(inFile, outFile);
        }
    }
}
