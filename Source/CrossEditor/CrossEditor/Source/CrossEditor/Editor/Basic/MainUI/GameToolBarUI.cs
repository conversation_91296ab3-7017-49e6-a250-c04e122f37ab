using EditorUI;

namespace CrossEditor
{
    public class GameToolBarUI
    {
        static GameToolBarUI _Instance = new GameToolBarUI();

        ToolBar _ToolBar;

        ComboBox _ComboBox_Mode;

        Button _ButtonTool_Save;
        Button _ButtonTool_Undo;
        Button _ButtonTool_Redo;

        //Button _ButtonTool_MoveMode;
        Button _ButtonTool_TranslateMode;
        Button _ButtonTool_RotateMode;
        Button _ButtonTool_ScaleMode;
        Button _ButtonTool_LocalMode;
        Button _ButtonTool_GlobalMode;

        //Button _ButtonTool_PlayGame;
        Button _ButtonTool_PlayCurrentScene;

        // TypeScript Compile
        Button _ButtonTool_TypeScriptCompile;


        public static GameToolBarUI GetInstance()
        {
            return _Instance;
        }

        public bool Initialize(UIManager UIManager)
        {
            Control Root = UIManager.GetRoot();
            MainUI MainUI = MainUI.GetInstance();

            _ToolBar = new ToolBar();
            _ToolBar.Initialize();
            _ToolBar.SetBackgroundColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            Root.AddChild(_ToolBar);

            _ToolBar.AddSeperator();
            _ComboBox_Mode = new ComboBox();
            _ComboBox_Mode.Initialize();
            _ComboBox_Mode.AddItem("Object Mode");
            _ComboBox_Mode.AddItem("Paint Mode");
            _ComboBox_Mode.AddItem("Terrain Mode");
            _ComboBox_Mode.AddItem("PCG Mode");
            _ComboBox_Mode.SetSelectedItemIndex(0);
            _ComboBox_Mode.SetPosition(5, 5, 140, 32);
            _ComboBox_Mode.ItemSelectedEvent += (ComboBox Sender) => { MainUI.GetInstance().OnComboBoxEditorModeSelect(Sender); };
            _ToolBar.AddTool(_ComboBox_Mode);
            _ToolBar.AddSeperator();

            _ButtonTool_Save = _ToolBar.AddTool("Editor/Icons/File/Save.png");
            _ButtonTool_Save.SetToolTips("Save File (Ctrl+S)");
            _ButtonTool_Save.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolSaveClicked(null); };

            _ToolBar.AddSeperator();

            _ButtonTool_Undo = _ToolBar.AddTool("Editor/Icons/Edit/Undo.png");
            _ButtonTool_Undo.SetToolTips("Undo (Ctrl+Z)");
            _ButtonTool_Undo.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolUndoClicked(null); };

            _ButtonTool_Redo = _ToolBar.AddTool("Editor/Icons/Edit/Redo.png");
            _ButtonTool_Redo.SetToolTips("Redo (Ctrl+Y)");
            _ButtonTool_Redo.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolRedoClicked(null); };

            _ToolBar.AddSeperator();

            //_ButtonTool_MoveMode = _ToolBar.AddTool("Editor/Icons/Edit/MoveMode.png");
            //_ButtonTool_MoveMode.SetToolTips("Move Mode");
            //_ButtonTool_MoveMode.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolMoveModeClicked(null); };

            _ButtonTool_TranslateMode = _ToolBar.AddTool("Editor/Icons/Edit/TranslateMode.png");
            _ButtonTool_TranslateMode.SetToolTips("Translate Mode (W)");
            _ButtonTool_TranslateMode.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolTranslateModeClicked(null); };

            _ButtonTool_RotateMode = _ToolBar.AddTool("Editor/Icons/Edit/RotateMode.png");
            _ButtonTool_RotateMode.SetToolTips("Rotate Mode (E)");
            _ButtonTool_RotateMode.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolRotateModeClicked(null); };

            _ButtonTool_ScaleMode = _ToolBar.AddTool("Editor/Icons/Edit/ScaleMode.png");
            _ButtonTool_ScaleMode.SetToolTips("Scale Mode (R)");
            _ButtonTool_ScaleMode.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolScaleModeClicked(null); };

            _ToolBar.AddSeperator();

            _ButtonTool_LocalMode = _ToolBar.AddTool("Editor/Icons/Edit/LocalMode.png");
            _ButtonTool_LocalMode.SetToolTips("Local Mode");
            _ButtonTool_LocalMode.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolLocalModeClicked(null); };

            _ButtonTool_GlobalMode = _ToolBar.AddTool("Editor/Icons/Edit/GlobalMode.png");
            _ButtonTool_GlobalMode.SetToolTips("Global Mode");
            _ButtonTool_GlobalMode.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolGlobalModeClicked(null); };

            _ToolBar.AddSeperator();

            _ButtonTool_TypeScriptCompile = _ToolBar.AddTool("Editor/Icons/TypeScript/Icon128.png");
            _ButtonTool_TypeScriptCompile.SetToolTips("Compile TypeScript");
            _ButtonTool_TypeScriptCompile.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolTypeScriptCompile(null); };

            _ToolBar.AddSeperator();

            //_ButtonTool_PlayGame = _ToolBar.AddTool("Editor/Icons/Debug/RunGame.png");
            //_ButtonTool_PlayGame.SetToolTips("Play Game");
            //_ButtonTool_PlayGame.ClickedEvent += (Button Sender) => { MainUI.GetInstance().OnButtonToolPlayGameClicked(null); };

            _ButtonTool_PlayCurrentScene = _ToolBar.AddTool("Editor/Icons/Debug/PreviewScene.png");
            _ButtonTool_PlayCurrentScene.SetToolTips("Play Current Scene (Alt+P)");
            _ButtonTool_PlayCurrentScene.ClickedEvent += (Button Sender) =>
            {
                MainUI.GetInstance().OnButtonToolPlayCurrentSceneClicked(null);
            };

            return true;
        }

        public void ComboBoxModeSetEditorMode(EditorMode EditorMode)
        {
            _ComboBox_Mode.SetSelectedItemIndex((int)EditorMode);
            _ComboBox_Mode.TriggerItemSelectedEvent();
        }

        public void TogglePlayButton()
        {
            if (!GameSceneUI.GetInstance().IsPIEPlaying)
            {
                _ButtonTool_PlayCurrentScene.SetToolTips("Play Current Scene (Alt+P)");
                _ButtonTool_PlayCurrentScene.SetImage(UIManager.LoadUIImage("Editor/Icons/Debug/PreviewScene.nda"));
            }
            else
            {
                _ButtonTool_PlayCurrentScene.SetToolTips("Stop Playing Current Scene");
                _ButtonTool_PlayCurrentScene.SetImage(UIManager.LoadUIImage("Editor/Icons/Profile/StopProfiling.nda"));
            }
        }

        public void Update()
        {
            bool bCanUndo = EditOperationManager.GetInstance().CanUndo();
            bool bCanRedo = EditOperationManager.GetInstance().CanRedo();
            _ButtonTool_Undo.SetEnable(bCanUndo);
            _ButtonTool_Redo.SetEnable(bCanRedo);

            ManipulatorType ManipulatorType1 = ManipulatorManager.GetInstance().GetManipulatorType();
            bool bTranslator = ManipulatorType1 == ManipulatorType.LocalTranslator || ManipulatorType1 == ManipulatorType.GlobalTranslator;
            bool bRotator = ManipulatorType1 == ManipulatorType.LocalRotator || ManipulatorType1 == ManipulatorType.GlobalRotator;
            bool bScaler = ManipulatorType1 == ManipulatorType.LocalScaler || ManipulatorType1 == ManipulatorType.GlobalScaler;
            _ButtonTool_TranslateMode.SetBorderChecked(bTranslator);
            _ButtonTool_RotateMode.SetBorderChecked(bRotator);
            _ButtonTool_ScaleMode.SetBorderChecked(bScaler);

            bool bGlobal = ManipulatorManager.GetInstance().GetGlobal();
            _ButtonTool_LocalMode.SetBorderChecked(!bGlobal);
            _ButtonTool_GlobalMode.SetBorderChecked(bGlobal);
        }

        public void OnDeviceResize(int Width, int Height)
        {
            _ToolBar.SetPosition(1 + 5, 32, Width - 2 - 10, 30);
        }
    }
}
