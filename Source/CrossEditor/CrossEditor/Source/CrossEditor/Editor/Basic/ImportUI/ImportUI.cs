using CEngine;
using EditorUI;

namespace CrossEditor
{
    public delegate void ImportUIInputEventHandler(ImportUI Sender);

    public class ImportUI : DialogUI
    {
        private protected ScrollView _ScrollView;
        private protected Panel _ScrollPanel;

        private protected ImportSetting _ImportSettings;
        private protected Inspector _Inspector;
        private protected InspectorHandler _InspectorHandler;

        private protected Button ButtonOK;

        public event ImportUIInputEventHandler InputedEvent;

        public virtual void Initialize(UIManager UIManager, string Title, string NdaPath, ImportSetting ImportSetting)
        {
            base.Initialize(UIManager, Title, 650, 500);

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.GetHScroll().SetEnable(false);
            _PanelDialog.AddChild(_ScrollView);
            _ScrollView.SetPosition(10, 50, 630, 400);

            _ScrollPanel = _ScrollView.GetScrollPanel();

            _ImportSettings = ImportSetting;
            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_ImportSettings); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            CreateInspector();

            ButtonOK = new Button();
            ButtonOK.Initialize();
            ButtonOK.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonOK.SetText("OK");
            ButtonOK.SetFontSize(16);
            ButtonOK.SetTextOffsetY(3);
            ButtonOK.ClickedEvent += OnButtonOKClicked;
            _PanelDialog.AddChild(ButtonOK);

            ButtonOK.SetSize(100, 24);
            ButtonOK.MakeCenterX();
            ButtonOK.SetY(460);
            UpdateLayout();
        }


        public virtual void CreateInspector()
        {
            _Inspector = new Inspector_Struct_With_Property();
            _Inspector.InspectObject(_ImportSettings);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);
        }

        public virtual void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }


        public override void OnDeviceChar(Device Sender, char Char)
        {
            base.OnDeviceChar(Sender, Char);

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bNone = !bControl && !bShift && !bAlt;

            if (bNone && (Char == '\r' || Char == '\n'))
            {
                if (BackgroundMask.HasActiveBackgroundMask(GetUIManager()) == false)
                {
                    OnButtonOKClicked(null);
                }
            }
        }

        public virtual void OnButtonOKClicked(Button Sender)
        {
            CloseDialog();
            CallEvent();
        }

        public void CallEvent()
        {
            if (InputedEvent != null)
            {
                InputedEvent(this);
            }
        }
    }

}