using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;

namespace CrossEditor
{
    public enum AtlasType
    {
        hardmask,
        softmask,
        sdf,
        psdf,
        msdf,
        mtsdf,
    }

    public enum AtlasImageFormat
    {
        png,
        bmp,
        tiff,
        text,
        textfloat,
        bin,
        binfloat,
    }

    public class MSDFGenerateSetting
    {
        [PropertyInfo(PropertyType = "StringAsFile", DisplayName = "Font File", FileTypeDescriptor = "#otf|ttf")]
        public string FontFile { get; set; }

        [PropertyInfo(PropertyType = "StringAsFile", DisplayName = "Charset File", FileTypeDescriptor = "#txt")]
        public string Charset { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Atlas Type", ToolTips = "Generally use default type MSDF.")]
        public AtlasType Type { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Image Format", ToolTips = "Generally use default format png.")]
        public AtlasImageFormat Format { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Glyph Size", ToolTips = "Set the size of glyphs in the atlas in pixels per EM.")]
        public float Size { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Pixel Range", ToolTips = "Set the distance field range in outpu pixels.")]
        public int PxRange { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Image Suffix", ToolTips = "The output image filename suffix.")]
        public string ImageSuffix { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Json Suffix", ToolTips = "The output json filename suffix.")]
        public string JsonSuffix { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Use Default Charset", ToolTips = "Use default charset, which contains some major english characters.")]
        public bool UseDefaultCharset { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Use All Glyph Charset", ToolTips = "Use All Glyph Charset.")]
        public bool UseAllGlyph { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Use Custom String", ToolTips = "Use custom string to generate atlas.")]
        public bool UseCustomString { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Custom String")]
        public string CustomString { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Generate Mipmap", ToolTips = "Generate multiple mip levels for better LOD support.")]
        public bool GenerateMipmap { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Mip Levels", ToolTips = "Number of mip levels to generate (1 = no mipmaps).")]
        public int MipLevels { get; set; }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Uniform Columns", ToolTips = "Number of columns in the uniform grid layout for UV alignment.")]
        public int UniformColumns { get; set; }

        public MSDFGenerateSetting()
        {
            FontFile = "";
            Charset = "";
            Type = AtlasType.msdf;
            Format = AtlasImageFormat.png;
            Size = 128.0f;
            PxRange = 8;
            ImageSuffix = "msdf";
            JsonSuffix = "info";
            UseDefaultCharset = false;
            UseCustomString = false;
            CustomString = "";
            GenerateMipmap = false;
            MipLevels = 4;
            UniformColumns = 10; // Better for typical Chinese character sets
        }
    }

    public delegate void MSDFGenerateUIInputEventHandler(MSDFGenerateUI Sender);

    public class MSDFGenerateUI : ImportUI
    {
        static string EXE_PATH = "Editor/Tools/msdf-atlas-gen.exe";

        //ScrollView _ScrollView;
        //Panel _ScrollPanel;

        MSDFGenerateSetting _MSDFGenerateSetting;
        //Inspector _Inspector;
        //InspectorHandler _InspectorHandler;

        //public MSDFGenerateUIInputEventHandler InputedEvent;

        public void Initialize(UIManager UIManager, string Title, string fontPath)
        {
            base.Initialize(UIManager, Title, 650, 500);

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.GetHScroll().SetEnable(false);
            _PanelDialog.AddChild(_ScrollView);
            _ScrollView.SetPosition(10, 50, 630, 430);

            _ScrollPanel = _ScrollView.GetScrollPanel();

            _MSDFGenerateSetting = new MSDFGenerateSetting();
            _MSDFGenerateSetting.FontFile = EditorUtilities.EditorFilenameToStandardFilename(fontPath);
            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_MSDFGenerateSetting); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            _Inspector = new Inspector_Struct_With_Property();
            _Inspector.InspectObject(_MSDFGenerateSetting);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);
            UpdateLayout();

            Button ButtonOK = new Button();
            ButtonOK.Initialize();
            ButtonOK.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonOK.SetText("OK");
            ButtonOK.SetFontSize(16);
            ButtonOK.SetTextOffsetY(3);
            ButtonOK.ClickedEvent += OnButtonOKClicked;
            _PanelDialog.AddChild(ButtonOK);

            ButtonOK.SetSize(100, 24);
            ButtonOK.MakeCenterX();
            ButtonOK.SetY(450);
        }


        public override void OnButtonOKClicked(Button Sender)
        {
            CloseDialog();
            // Miss font file.
            if (_MSDFGenerateSetting.FontFile.Length == 0)
            {
                return;
            }
            // Miss charset file or custom charset is empty.
            if (!_MSDFGenerateSetting.UseAllGlyph && !_MSDFGenerateSetting.UseDefaultCharset &&
                (!_MSDFGenerateSetting.UseCustomString && _MSDFGenerateSetting.Charset.Length == 0 ||
                _MSDFGenerateSetting.UseCustomString && _MSDFGenerateSetting.CustomString.Length == 0))
            {
                return;
            }
            string ToolPath = PathHelper.ToAbsolutePath(EXE_PATH);
            string FontPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.FontFile);
            string Charset = _MSDFGenerateSetting.UseCustomString ? GetTempCharsetPath() : EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.Charset);
            // Create temp charset txt file for custom string.
            if (_MSDFGenerateSetting.UseCustomString)
            {
                string FileContent = "\"" + _MSDFGenerateSetting.CustomString + "\"";
                File.WriteAllText(GetTempCharsetPath(), FileContent, Encoding.UTF8);
            }
            string Type = _MSDFGenerateSetting.Type.ToString();
            string Format = _MSDFGenerateSetting.Format.ToString();
            if (_MSDFGenerateSetting.GenerateMipmap && _MSDFGenerateSetting.MipLevels > 1)
            {
                GenerateMipmapAtlas();
            }
            else
            {
                GenerateSingleAtlas();
            }

            // Delete temp file after generating.
            if (_MSDFGenerateSetting.UseCustomString)
            {
                File.Delete(GetTempCharsetPath());
            }

            CallEvent();
        }

        private void GenerateSingleAtlas()
        {
            string ToolPath = PathHelper.ToAbsolutePath(EXE_PATH);
            string FontPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.FontFile);
            string Charset = _MSDFGenerateSetting.UseCustomString ? GetTempCharsetPath() : EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.Charset);
            string Type = _MSDFGenerateSetting.Type.ToString();
            string Format = _MSDFGenerateSetting.Format.ToString();
            string Size = _MSDFGenerateSetting.Size.ToString();
            string PxRange = _MSDFGenerateSetting.PxRange.ToString();
            string ImagePath = GetGenerateMSDFPath();
            string JsonPath = GetGenerateInfoPath();
            string Arguments = string.Format("-font {0} -type {1} -format {2} -size {3} -pxrange {4} -imageout {5} -json {6}",
                FontPath, Type, Format, Size, PxRange, ImagePath, JsonPath);
            if (_MSDFGenerateSetting.UseAllGlyph)
            {
                Arguments += " -allglyphs ";
            }
            else if (!_MSDFGenerateSetting.UseDefaultCharset)
            {
                Arguments += " -charset " + Charset;
            }

            ProgressUI LoadingUI = new ProgressUI();
            Progress Progress = new Progress("Generating Progress", 1);
            LoadingUI.Initialize(GetUIManager(), Progress, false);
            DialogUIManager.GetInstance().ShowDialogUI(LoadingUI);

            Progress.SetStep(0, "Generating...", 1);
            Process process = new Process();
            try
            {
                process.StartInfo.FileName = ToolPath;
                process.StartInfo.Arguments = Arguments;
                process.StartInfo.CreateNoWindow = false;
                process.StartInfo.WorkingDirectory = PathHelper.GetDirectoryName(ToolPath);
                process.Start();
                process.WaitForExit();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }

            Progress.SetItem(0, "Done.");
            process.Kill();
            Progress.Done();
            Progress.Close();
            
            FontImportSetting setting = new FontImportSetting();
            setting.FontInfoResource = JsonPath;
            setting.MSDFResource = ImagePath;
            AssetImporterManager.Instance().SetFontImporterSettings(setting);
            AssetImporterManager.Instance().ImportAsset(FontPath, Path.ChangeExtension(FontPath, "nda"));
        }

        private void GenerateMipmapAtlas()
        {
            string ToolPath = PathHelper.ToAbsolutePath(EXE_PATH);
            string FontPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.FontFile);
            string Charset = _MSDFGenerateSetting.UseCustomString ? GetTempCharsetPath() : EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.Charset);
            string Type = _MSDFGenerateSetting.Type.ToString();
            string Format = _MSDFGenerateSetting.Format.ToString();

            // Optimize UniformColumns based on character count
            int optimalColumns = CalculateOptimalColumns();
            if (optimalColumns != _MSDFGenerateSetting.UniformColumns)
            {
                Console.WriteLine($"Auto-adjusting UniformColumns from {_MSDFGenerateSetting.UniformColumns} to {optimalColumns} for better packing");
                _MSDFGenerateSetting.UniformColumns = optimalColumns;
            }

            ProgressUI LoadingUI = new ProgressUI();
            Progress Progress = new Progress("Generating Mipmap Progress", _MSDFGenerateSetting.MipLevels);
            LoadingUI.Initialize(GetUIManager(), Progress, false);
            DialogUIManager.GetInstance().ShowDialogUI(LoadingUI);

            vector_string GeneratedMipPaths = new vector_string();
            int baseAtlasWidth = 0;
            int baseAtlasHeight = 0;
            int baseCellWidth = 0;
            int baseCellHeight = 0;

            // Step 1: Generate mip0 to determine base atlas dimensions
            Progress.SetStep(0, "Generating Mip Level 0 (Base)...", 1);
            
            string Mip0ImagePath = GetGenerateMSDFMipPath(0);
            string Mip0JsonPath = GetGenerateInfoMipPath(0);
            GeneratedMipPaths.Add(Mip0ImagePath);

            string Mip0Arguments = string.Format("-font {0} -type {1} -format {2} -size {3} -pxrange {4} -imageout {5} -json {6} -uniformgrid -uniformcols {7} -pots -pxpadding {4} -outerpxpadding 0 -pxalign on -uniformorigin on -uniformcellconstraint pots",
                FontPath, Type, Format, _MSDFGenerateSetting.Size.ToString(), _MSDFGenerateSetting.PxRange.ToString(), Mip0ImagePath, Mip0JsonPath, _MSDFGenerateSetting.UniformColumns);

            if (_MSDFGenerateSetting.UseAllGlyph)
            {
                Mip0Arguments += " -allglyphs ";
            }
            else if (!_MSDFGenerateSetting.UseDefaultCharset)
            {
                Mip0Arguments += " -charset " + Charset;
            }

            // Generate mip0
            Process mip0Process = new Process();
            try
            {
                mip0Process.StartInfo.FileName = ToolPath;
                mip0Process.StartInfo.Arguments = Mip0Arguments;
                mip0Process.StartInfo.CreateNoWindow = false;
                mip0Process.StartInfo.WorkingDirectory = PathHelper.GetDirectoryName(ToolPath);
                mip0Process.Start();
                mip0Process.WaitForExit();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }

            Progress.SetItem(0, "Mip Level 0 Done.");
            mip0Process.Kill();

            // Read base atlas dimensions from mip0 json
            if (File.Exists(Mip0JsonPath))
            {
                try
                {
                    string jsonContent = File.ReadAllText(Mip0JsonPath);
                    dynamic jsonObj = JsonConvert.DeserializeObject(jsonContent);
                    baseAtlasWidth = jsonObj.atlas.width;
                    baseAtlasHeight = jsonObj.atlas.height;
                    Console.WriteLine($"Base atlas dimensions: {baseAtlasWidth}x{baseAtlasHeight}");

                    if (jsonObj.atlas.grid != null)
                    {
                        baseCellWidth = jsonObj.atlas.grid.cellWidth;
                        baseCellHeight = jsonObj.atlas.grid.cellHeight;
                        Console.WriteLine($"Base cell dimensions: {baseCellWidth}x{baseCellHeight}");
                    }
                    else
                    {
                        Console.WriteLine("Warning: grid info not found in mip0 json.");
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine($"Failed to read base atlas dimensions: {e.Message}");
                    // Fallback to default behavior
                    baseAtlasWidth = 0;
                    baseAtlasHeight = 0;
                }
            }

            // Step 2: Generate remaining mip levels with fixed dimensions
            for (int mipLevel = 1; mipLevel < _MSDFGenerateSetting.MipLevels; mipLevel++)
            {
                Progress.SetStep(mipLevel, $"Generating Mip Level {mipLevel}...", 1);

                // Restore original scaling logic for size and pxrange
                float currentSize = _MSDFGenerateSetting.Size / (float)Math.Pow(2, mipLevel);
                int currentPxRange = Math.Max(1, (int)(_MSDFGenerateSetting.PxRange / Math.Pow(2, mipLevel)));

                string MipImagePath = GetGenerateMSDFMipPath(mipLevel);
                string MipJsonPath = GetGenerateInfoMipPath(mipLevel);
                GeneratedMipPaths.Add(MipImagePath);

                string Arguments;
                
                if (baseAtlasWidth > 0 && baseAtlasHeight > 0)
                {
                    // Calculate target dimensions maintaining exact aspect ratio
                    int targetWidth = baseAtlasWidth / (int)Math.Pow(2, mipLevel);
                    int targetHeight = baseAtlasHeight / (int)Math.Pow(2, mipLevel);
                    
                    // Remove minimum size constraint to maintain exact proportions
                    // targetWidth = Math.Max(16, targetWidth);
                    // targetHeight = Math.Max(16, targetHeight);

                    string cellArgs = "";
                    if (baseCellWidth > 0 && baseCellHeight > 0)
                    {
                        int targetCellWidth = baseCellWidth / (int)Math.Pow(2, mipLevel);
                        int targetCellHeight = baseCellHeight / (int)Math.Pow(2, mipLevel);

                        targetCellWidth = Math.Max(1, targetCellWidth);
                        targetCellHeight = Math.Max(1, targetCellHeight);

                        cellArgs = $" -uniformcell {targetCellWidth} {targetCellHeight}";
                        Console.WriteLine($"Mip{mipLevel}: Target {targetWidth}x{targetHeight}, Cell {targetCellWidth}x{targetCellHeight}, size={currentSize}, pxrange={currentPxRange}");
                    }
                    else
                    {
                        Console.WriteLine($"Mip{mipLevel}: Target {targetWidth}x{targetHeight}, size={currentSize}, pxrange={currentPxRange}");
                    }
                    
                    // Add explicit padding control to ensure consistent layout
                    Arguments = string.Format("-font {0} -type {1} -format {2} -size {3} -pxrange {4} -imageout {5} -json {6} -uniformgrid -uniformcols {7} -dimensions {8} {9} -pxpadding {4} -outerpxpadding 0 -pxalign on -uniformorigin on{10}",
                        FontPath, Type, Format, currentSize.ToString(), currentPxRange.ToString(), MipImagePath, MipJsonPath, _MSDFGenerateSetting.UniformColumns, targetWidth, targetHeight, cellArgs);
                }
                else
                {
                    // Fallback to auto-size if we couldn't read base dimensions
                    Arguments = string.Format("-font {0} -type {1} -format {2} -size {3} -pxrange {4} -imageout {5} -json {6} -uniformgrid -uniformcols {7} -pots -pxpadding {4} -outerpxpadding 0 -pxalign on -uniformorigin on -uniformcellconstraint pots",
                        FontPath, Type, Format, currentSize.ToString(), currentPxRange.ToString(), MipImagePath, MipJsonPath, _MSDFGenerateSetting.UniformColumns);
                }

                if (_MSDFGenerateSetting.UseAllGlyph)
                {
                    Arguments += " -allglyphs ";
                }
                else if (!_MSDFGenerateSetting.UseDefaultCharset)
                {
                    Arguments += " -charset " + Charset;
                }

                Process process = new Process();
                try
                {
                    process.StartInfo.FileName = ToolPath;
                    process.StartInfo.Arguments = Arguments;
                    process.StartInfo.CreateNoWindow = false;
                    process.StartInfo.WorkingDirectory = PathHelper.GetDirectoryName(ToolPath);
                    process.Start();
                    process.WaitForExit();
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                }

                Progress.SetItem(mipLevel, $"Mip Level {mipLevel} Done.");
                process.Kill();
                
                // If dimensions parameter failed, try post-processing the image
                if (baseAtlasWidth > 0 && baseAtlasHeight > 0 && File.Exists(MipImagePath))
                {
                    int expectedWidth = baseAtlasWidth / (int)Math.Pow(2, mipLevel);
                    int expectedHeight = baseAtlasHeight / (int)Math.Pow(2, mipLevel);
                    
                    // TODO: Add image resizing logic here if needed
                    // This would involve loading the image, checking its size,
                    // and resizing it to match expected dimensions while preserving content
                }
            }

            Progress.Done();
            Progress.Close();

            // Now combine the mip levels into a single texture
            string CombinedTexturePath = GetGenerateMSDFPath();
            string JsonPath = GetGenerateInfoPath(); // Use the mip0 json for font info
            //string Mip0JsonPath = GetGenerateInfoMipPath(0);

            // Copy the json info from mip0
            if (File.Exists(Mip0JsonPath))
            {
                File.Copy(Mip0JsonPath, JsonPath, true);
            }

            // Call C++ function to combine mip levels
            FontImportSetting setting = new FontImportSetting();
            setting.FontInfoResource = JsonPath;
            setting.MSDFResource = CombinedTexturePath;
            setting.MipPaths = GeneratedMipPaths;
            AssetImporterManager.Instance().SetFontImporterSettings(setting);
            AssetImporterManager.Instance().ImportAsset(FontPath, Path.ChangeExtension(FontPath, "nda"));

            // Delete temp file after generating.
            if (_MSDFGenerateSetting.UseCustomString)
            {
                File.Delete(GetTempCharsetPath());
            }

            CallEvent();
        }

        public string GetGenerateMSDFPath()
        {
            string FontPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.FontFile);
            string Directory = PathHelper.GetDirectoryName(FontPath);
            string Filename = PathHelper.GetNameOfPath(FontPath);
            return Directory + "/" + Filename + "_" + _MSDFGenerateSetting.ImageSuffix + "." + _MSDFGenerateSetting.Format.ToString();
        }

        public string GetGenerateInfoPath()
        {
            string FontPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.FontFile);
            string Directory = PathHelper.GetDirectoryName(FontPath);
            string Filename = PathHelper.GetNameOfPath(FontPath);
            return Directory + "/" + Filename + "_" + _MSDFGenerateSetting.JsonSuffix + ".json";
        }

        string GetTempCharsetPath()
        {
            string FontPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.FontFile);
            string Directory = PathHelper.GetDirectoryName(FontPath);
            string Filename = "TempCharset.txt";
            return Directory + "/" + Filename;
        }

        string GetGenerateMSDFMipPath(int mipLevel)
        {
            string FontPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.FontFile);
            string Directory = PathHelper.GetDirectoryName(FontPath);
            string Filename = PathHelper.GetNameOfPath(FontPath);
            return Directory + "/" + Filename + "_" + _MSDFGenerateSetting.ImageSuffix + "_mip" + mipLevel + "." + _MSDFGenerateSetting.Format.ToString();
        }

        string GetGenerateInfoMipPath(int mipLevel)
        {
            string FontPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.FontFile);
            string Directory = PathHelper.GetDirectoryName(FontPath);
            string Filename = PathHelper.GetNameOfPath(FontPath);
            return Directory + "/" + Filename + "_" + _MSDFGenerateSetting.JsonSuffix + "_mip" + mipLevel + ".json";
        }

        int CalculateOptimalColumns()
        {
            int estimatedCharCount = EstimateCharacterCount();
            
            // Calculate a square-ish grid
            int optimalColumns = (int)Math.Ceiling(Math.Sqrt(estimatedCharCount));
            
            // Clamp to reasonable range (8-16 for most use cases)
            optimalColumns = Math.Max(8, Math.Min(16, optimalColumns));
            
            return optimalColumns;
        }

        int EstimateCharacterCount()
        {
            if (_MSDFGenerateSetting.UseAllGlyph)
            {
                return 300; // Rough estimate for "all glyphs"
            }
            else if (_MSDFGenerateSetting.UseCustomString)
            {
                // Count unique characters in custom string
                return _MSDFGenerateSetting.CustomString.Distinct().Count();
            }
            else if (!string.IsNullOrEmpty(_MSDFGenerateSetting.Charset))
            {
                try
                {
                    // Try to read charset file and estimate character count
                    string charsetPath = EditorUtilities.StandardFilenameToEditorFilename(_MSDFGenerateSetting.Charset);
                    if (File.Exists(charsetPath))
                    {
                        string content = File.ReadAllText(charsetPath);
                        // Remove quotes and count unique characters
                        content = content.Replace("\"", "");
                        return content.Distinct().Count();
                    }
                }
                catch
                {
                    // Fall through to default
                }
            }
            
            // Default estimate for common Chinese character sets
            return 100;
        }
    }
}
