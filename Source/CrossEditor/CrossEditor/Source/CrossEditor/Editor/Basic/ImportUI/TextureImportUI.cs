using CEngine;
using EditorUI;

namespace CrossEditor
{
    public delegate void TextureImportUIInputEventHandler(TextureImportUI Sender);

    public class TextureImportUI : ImportUI
    {

        bool MultipleTetxure = false;

        public void Initialize(UIManager UIManager, string Title, string NdaPath, ImportSetting TextureImportSetting, bool bMultipleTexture)
        {
            MultipleTetxure = bMultipleTexture;
            base.Initialize(UIManager, Title, NdaPath, TextureImportSetting);
        }

        public override void CreateInspector()
        {
            _Inspector = new Inspector_Struct_With_Property();
            if (MultipleTetxure)
            {
                _Inspector.InspectObject(_ImportSettings);
            }
            else
            {
                _Inspector.InspectObject(_ImportSettings, "Auto import");
            }

            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);
        }
    }
}