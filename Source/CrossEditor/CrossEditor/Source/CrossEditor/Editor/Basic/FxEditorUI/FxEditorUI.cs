using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    //the .FX file now display as follow
    //--Fx                      (depth=0)
    //  |--properties           (depth=1)
    //  |  |--group1            (depth=2)
    //  |  |  |--property1      (depth=3)
    //  |  |  |--property2
    //  |  |  |-- ...
    //  |  |
    //  |  |--group2
    //  |  |  |--property1
    //  |  |  |--property2
    //  |  |  |-- ...
    //  |  |
    //  |  |-- ..
    //  |  
    //  |--passinfo             (depth=1)
    //  |  |--passinfo1         (depth=2)
    //  |  |--passinfo2
    //  |  |-- ...

    public class FxEditorUI : DockingUI
    {
        enum ItemType
        {
            Root,
            Properties,
            Group,
            Property,
            Passes,
            Pass,
            Invalid
        }

        static FxEditorUI _Instance = new FxEditorUI();

        Fx _Fx;

        VContainer _VContainer1;
        OperationBarUI _OperationBarUI;
        SearchUI _SearchUI;

        Tree _Tree;
        TreeItem _RootItem;
        TreeItem _PropertiesItem;
        TreeItem _PassesItem;

        List<TreeItem> _ItemsDragged;

        bool _bSearchMode = false;
        List<object> _SelectedTagObjectInSearchMode;

        protected FxEditorUI()
        {
            _SelectedTagObjectInSearchMode = new List<object>();
        }

        public static FxEditorUI GetInstance() => _Instance;

        public bool Initialize()
        {
            _VContainer1 = new VContainer();
            _VContainer1.Initialize();
            _VContainer1.SetSize(500, 500);

            _OperationBarUI = new OperationBarUI();
            _OperationBarUI.Initialize();
            _VContainer1.AddFixedChild(_OperationBarUI.GetPanelBar());

            Button ButtonSave = OperationBarUI.CreateTextButton("Save");
            ButtonSave.ClickedEvent += OnButtonSaveClicked;
            _OperationBarUI.AddLeft(ButtonSave);

            Button ButtonRefrensh = OperationBarUI.CreateTextButton("Refresh");
            ButtonRefrensh.ClickedEvent += OnButtonRefreshClicked;
            _OperationBarUI.AddLeft(ButtonRefrensh);

            Button ButtonReload = OperationBarUI.CreateTextButton("Reload");
            ButtonReload.ClickedEvent += OnButtonReloadClicked;
            _OperationBarUI.AddLeft(ButtonReload);

            Button ButtonRemoveUnusedProps = OperationBarUI.CreateTextButton("Remove Unused Properties");
            ButtonRemoveUnusedProps.ClickedEvent += OnButtonRemoveUnusedPropsClicked;
            _OperationBarUI.AddLeft(ButtonRemoveUnusedProps);

            _bSearchMode = false;
            _SearchUI = new SearchUI();
            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 130, 20);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);
            _OperationBarUI.Refresh();

            _Tree = new Tree();
            _Tree.Initialize();
            _Tree.SetEnableDragDrop(true);
            _Tree.SetEnableRename(true);
            _Tree.SetCanSelectNothing(true);
            _Tree.SetEnableMultipleSelection(true);
            _Tree.SetSelectOnMouseUp(true);
            _Tree.RightMouseUpEvent += OnTreeRightMouseUp;
            _Tree.KeyDownEvent += OnTreeKeyDown;
            _Tree.ItemSelectedEvent += OnTreeItemSelected;
            _Tree.ItemRenameEvent += OnTreeItemRename;
            _VContainer1.AddSizableChild(_Tree, 1.0f);

            _RootItem = _Tree.GetRootItem();
            _RootItem.ClearChildren();
            _RootItem.SetFolder(true);
            _RootItem.SetExpanded(true);
            _RootItem.SetText("Fx");
            _RootItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Material.png"));

            _PropertiesItem = _Tree.CreateItem();
            _PropertiesItem.ClearChildren();
            _PropertiesItem.SetFolder(true);
            _PropertiesItem.SetExpanded(true);
            _PropertiesItem.SetText("Properties");
            _PropertiesItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Properties.png"));
            _RootItem.AddChild(_PropertiesItem);

            _PassesItem = _Tree.CreateItem();
            _PassesItem.ClearChildren();
            _PassesItem.SetFolder(true);
            _PassesItem.SetExpanded(true);
            _PassesItem.SetText("PassInfo");
            _PassesItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Pass.png"));
            _RootItem.AddChild(_PassesItem);

            UpdateHierarchy();

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragBeginEvent += OnDragDropManagerDragBegin;
            DragDropManager.DragMoveEvent += OnDragDropManagerDragMove;
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
            DragDropManager.DragClearEvent += OnDragDropManagerDragClear;
            DragDropManager.DragCancelEvent += OnDragDropManagerDragCancel;

            base.Initialize("FxEditor", _VContainer1);

            GetDockingCard().CloseEvent += OnDockingCardClose;

            return true;
        }

        public void OpenFx(Fx Fx)
        {
            if (_Fx != null && _Fx.bModified)
            {
                CommonDialogUI SaveDialogUI = new CommonDialogUI();
                SaveDialogUI.Initialize(GetUIManager(), "Save", "Do you want to save changes?", CommonDialogType.OKCancel);
                SaveDialogUI.CloseEvent += (Sender, Result) =>
                {
                    if (Result == CommonDialogResult.OK)
                    {
                        _Fx.Save();
                    }
                    else if (Result == CommonDialogResult.Cancel)
                    {
                        _Fx.ImmediateReload();
                    }

                    _Fx = Fx;
                    _Fx.Refresh();
                    UpdateHierarchy();
                };
                DialogUIManager.GetInstance().ShowDialogUI(SaveDialogUI);
            }
            else
            {
                _Fx = Fx;
                _Fx.Refresh();
                UpdateHierarchy();
            }
        }

        public Fx GetFx()
        {
            return _Fx;
        }

        public void Update()
        {
            if (_Fx != null)
            {
                // Update docking card text
                _DockingCard.SetText(_Fx.bModified ? "FxEditor*" : "FxEditor");
            }
        }

        void OnTreeItemSelected(Tree Sender, TreeItem TreeItem)
        {
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            object ObjectInspected = null;
            if (TreeItem != null)
            {
                if (GetTreeItemType(TreeItem) == ItemType.Property)
                {
                    ObjectInspected = _Fx.GetProperty(TreeItem.GetText());
                }
                else if (GetTreeItemType(TreeItem) == ItemType.Pass)
                {
                    ObjectInspected = _Fx.GetPass(TreeItem.GetText());
                }
            }
            InspectorUI.SetObjectInspected(ObjectInspected);
            InspectorUI.InspectObject();
        }

        void OnTreeRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY) == false)
            {
                return;
            }
            ShowContextMenu(MouseX, MouseY);
            bContinue = false;
        }

        #region Context Menu

        void ShowContextMenu(int MouseX, int MouseY)
        {
            if (_bSearchMode)
            {
                return;
            }

            List<TreeItem> SelectedItems = _Tree.GetSelectedItems();
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            if (SelectedItems.Count == 1)
            {
                TreeItem Item = SelectedItems[0];
                if (GetTreeItemType(Item) == ItemType.Properties)
                {
                    MenuItem MenuItem_NewGroup = new MenuItem();
                    MenuItem_NewGroup.SetText("Create New Group");
                    MenuItem_NewGroup.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Group.png"));
                    MenuItem_NewGroup.ClickedEvent += OnMenuItemNewGroupClicked;
                    MenuContextMenu.AddMenuItem(MenuItem_NewGroup);
                }
                else if (GetTreeItemType(Item) == ItemType.Group)
                {
                    MenuItem MenuItem_Rename = new MenuItem();
                    MenuItem_Rename.SetText("Rename");
                    MenuItem_Rename.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Rename.png"));
                    MenuItem_Rename.ClickedEvent += OnMenuItemRenameClicked;

                    MenuItem MenuItem_Delete = new MenuItem();
                    MenuItem_Delete.SetText("Delete");
                    MenuItem_Delete.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Delete.png"));
                    MenuItem_Delete.SetEnable(CanDeleteGroup(Item.GetText()));
                    MenuItem_Delete.ClickedEvent += OnMenuItemDeleteClicked;

                    MenuContextMenu.AddMenuItem(MenuItem_Delete);
                    MenuContextMenu.AddMenuItem(MenuItem_Rename);
                }
                else if (GetTreeItemType(Item) == ItemType.Property)
                {
                    MenuItem MenuItem_Visible = new MenuItem();
                    MenuItem_Visible.SetText("Visible");
                    MenuItem_Visible.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/visible.png"));
                    MenuItem_Visible.ClickedEvent += OnMenuItemPropertyVisibleClicked;

                    MenuItem MenuItem_Invisible = new MenuItem();
                    MenuItem_Invisible.SetText("Invisible");
                    MenuItem_Invisible.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/invisible.png"));
                    MenuItem_Invisible.ClickedEvent += OnMenuItemPropertyInvisibleClicked;

                    if (_Fx.GetProperty(Item.GetText()).Visible)
                        MenuContextMenu.AddMenuItem(MenuItem_Invisible);
                    else
                        MenuContextMenu.AddMenuItem(MenuItem_Visible);
                }
                else if (GetTreeItemType(Item) == ItemType.Passes)
                {
                    MenuItem MenuItem_NewEmptyPass = new MenuItem();
                    MenuItem_NewEmptyPass.SetText("EmptyPass");
                    MenuItem_NewEmptyPass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewEmptyPass.ClickedEvent += OnMenuItemNewEmptyPassClicked;

                    MenuItem MenuItem_NewForwardPass = new MenuItem();
                    MenuItem_NewForwardPass.SetText("Forward");
                    MenuItem_NewForwardPass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewForwardPass.SetEnable(_Fx.GetPassIndex("forward") == -1);
                    MenuItem_NewForwardPass.ClickedEvent += OnMenuItemNewForwardPassClicked;

                    MenuItem MenuItem_NewGPass = new MenuItem();
                    MenuItem_NewGPass.SetText("GPass");
                    MenuItem_NewGPass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewGPass.SetEnable(_Fx.GetPassIndex("gpass") == -1);
                    MenuItem_NewGPass.ClickedEvent += OnMenuItemNewGPassClicked;

                    MenuItem MenuItem_NewShadowPass = new MenuItem();
                    MenuItem_NewShadowPass.SetText("Shadow");
                    MenuItem_NewShadowPass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewShadowPass.SetEnable(_Fx.GetPassIndex("shadow") == -1);
                    MenuItem_NewShadowPass.ClickedEvent += OnMenuItemNewShadowPassClicked;

                    MenuItem MenuItem_NewShadowAllPass = new MenuItem();
                    MenuItem_NewShadowAllPass.SetText("ShadowAll");
                    MenuItem_NewShadowAllPass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewShadowAllPass.SetEnable(_Fx.GetPassIndex("shadow_all") == -1);
                    MenuItem_NewShadowAllPass.ClickedEvent += OnMenuItemNewShadowAllPassClicked;

                    MenuItem MenuItem_NewPreDepthPass = new MenuItem();
                    MenuItem_NewPreDepthPass.SetText("PreDepth");
                    MenuItem_NewPreDepthPass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewPreDepthPass.SetEnable(_Fx.GetPassIndex("preDepth") == -1);
                    MenuItem_NewPreDepthPass.ClickedEvent += OnMenuItemNewPreDepthPassClicked;

                    MenuItem MenuItem_NewVoxelizePass = new MenuItem();
                    MenuItem_NewVoxelizePass.SetText("VoxelizePass");
                    MenuItem_NewVoxelizePass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewVoxelizePass.SetEnable(_Fx.GetPassIndex("VoxelizePass") == -1);
                    MenuItem_NewVoxelizePass.ClickedEvent += OnMenuItemNewVoxelizePassClicked;

                    MenuItem MenuItem_NewVSMDepthPass = new MenuItem();
                    MenuItem_NewVSMDepthPass.SetText("VSMDepth");
                    MenuItem_NewVSMDepthPass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewVSMDepthPass.SetEnable(_Fx.GetPassIndex("VSMDepth") == -1);
                    MenuItem_NewVSMDepthPass.ClickedEvent += OnMenuItemNewVSMDepthPassClicked;

                    MenuItem MenuItem_NewParticlePass = new MenuItem();
                    MenuItem_NewParticlePass.SetText("Particle");
                    MenuItem_NewParticlePass.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));
                    MenuItem_NewParticlePass.SetEnable(_Fx.GetPassIndex("Particle") == -1);
                    MenuItem_NewParticlePass.ClickedEvent += OnMenuItemNewParticlePassClicked;

                    MenuContextMenu.AddMenuItem(MenuItem_NewEmptyPass);
                    MenuContextMenu.AddMenuItem(MenuItem_NewForwardPass);
                    MenuContextMenu.AddMenuItem(MenuItem_NewGPass);
                    MenuContextMenu.AddMenuItem(MenuItem_NewShadowPass);
                    MenuContextMenu.AddMenuItem(MenuItem_NewShadowAllPass);
                    MenuContextMenu.AddMenuItem(MenuItem_NewPreDepthPass);
                    MenuContextMenu.AddMenuItem(MenuItem_NewVoxelizePass);
                    MenuContextMenu.AddMenuItem(MenuItem_NewVSMDepthPass);
                    MenuContextMenu.AddMenuItem(MenuItem_NewParticlePass);
                }
                else if (GetTreeItemType(Item) == ItemType.Pass)
                {
                    MenuItem MenuItem_Delete = new MenuItem();
                    MenuItem_Delete.SetText("Delete");
                    MenuItem_Delete.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Delete.png"));
                    MenuItem_Delete.ClickedEvent += OnMenuItemDeleteClicked;

                    MenuItem MenuItem_Rename = new MenuItem();
                    MenuItem_Rename.SetText("Rename");
                    MenuItem_Rename.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Rename.png"));
                    MenuItem_Rename.ClickedEvent += OnMenuItemRenameClicked;

                    MenuContextMenu.AddMenuItem(MenuItem_Delete);
                    MenuContextMenu.AddMenuItem(MenuItem_Rename);
                }
                GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, MouseX, MouseY);
            }
        }

        public void OnMenuItemPropertyInvisibleClicked(MenuItem Sender)
        {
            List<TreeItem> SelectiontList = _Tree.GetSelectedItems();

            foreach (TreeItem Item in SelectiontList)
            {
                if (GetTreeItemType(Item) == ItemType.Property)
                {
                    Fx.Property Property = Item.GetTagObject() as Fx.Property;
                    Property.Visible = false;
                }
            }

            UpdateHierarchy();
        }

        public void OnMenuItemPropertyVisibleClicked(MenuItem Sender)
        {
            List<TreeItem> SelectiontList = _Tree.GetSelectedItems();

            foreach (TreeItem Item in SelectiontList)
            {
                if (GetTreeItemType(Item) == ItemType.Property)
                {
                    Fx.Property Property = Item.GetTagObject() as Fx.Property;
                    Property.Visible = true;
                }
            }

            UpdateHierarchy();
        }

        public void OnMenuItemRenameClicked(MenuItem Sender)
        {
            DoRename();
        }

        public void OnMenuItemGroupDeleteClicked(TreeItem SelectedItem)
        {
            if (SelectedItem.GetChildCount() != 0)
            {
                return;
            }
            string GroupName = SelectedItem.GetText();
            _Fx.DeleteGroup(GroupName);
        }

        public void OnMenuItemPassDeleteClicked(TreeItem SelectedItem)
        {
            if (SelectedItem.GetParent().GetChildCount() <= 1)
            {
                return;
            }
            string PassName = SelectedItem.GetText();
            _Fx.DeletePass(PassName);
        }

        public bool HasGroup(string Name)
        {
            return _Fx.GetGroup(Name) != null;
        }

        public bool CanDeleteGroup(string GroupName)
        {
            Fx.Group Group = _Fx.GetGroup(GroupName);
            if (Group != null)
            {
                return Group.Properties.Count == 0;
            }
            return true;
        }

        public void OnMenuItemNewGroupClicked(MenuItem Sender)
        {
            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Input Folder Name", "Please input a folder name:", "");
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                GetUIManager().SetFocusControl(null);
                if (StringInputed == "")
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Group name can not be empty!");
                    return;
                }
                if (HasGroup(StringInputed))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Duplicated group name: " + StringInputed);
                    return;
                }
                _Fx.CreateGroup(StringInputed);
                ScrollTo(ItemType.Group, StringInputed);
            };
            TextInputUI.ShowDialog();
        }

        void ScrollTo(ItemType Type, string Name)
        {
            TreeItem Item = null;
            if (Type == ItemType.Group)
            {
                for (int i = 0; i < _PropertiesItem.GetChildCount(); i++)
                {
                    TreeItem Child = _PropertiesItem.GetChild(i);
                    if (Child.GetText() == Name)
                        Item = Child;
                }
            }

            if (Item != null)
            {
                _Tree.SelectItemNoEvent(Item);
            }
        }

        public bool HasPass(string Name)
        {
            return _Fx.GetPass(Name) != null;
        }

        public void OnMenuItemNewEmptyPassClicked(MenuItem Sender)
        {
            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Input Pass Name", "Please input a Pass name:", "");
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                GetUIManager().SetFocusControl(null);
                if (StringInputed == "")
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Pass name can not be empty!");
                    return;
                }
                if (HasGroup(StringInputed))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Duplicated pass name: " + StringInputed);
                    return;
                }
                _Fx.CreatePass(StringInputed);
            };
            TextInputUI.ShowDialog();
        }

        public void OnMenuItemNewForwardPassClicked(MenuItem Sender)
        {
            string Name = "forward";
            if (HasPass(Name))
                return;
            _Fx.CreatePass(Name);
            _Fx.GetPass(Name).Shader = "PipelineResource/FFSRP/Shader/Material/Lit/LitForwardUE.shader.nda";
            _Fx.GetPass(Name).DepthCompareFunction = Clicross.ComparisonOp.GreaterEqual;
            _Fx.GetPass(Name).BlendState = Clicross.FxRenderState.Opacity;
            _Fx.Refresh();
        }

        public void OnMenuItemNewGPassClicked(MenuItem Sender)
        {
            string Name = "gpass";
            if (HasPass(Name))
                return;
            _Fx.CreatePass(Name);
            _Fx.GetPass(Name).Shader = "PipelineResource/FFSRP/Shader/Material/Lit/LitGBuffer.shader.nda";
            _Fx.GetPass(Name).DepthCompareFunction = Clicross.ComparisonOp.GreaterEqual;
            _Fx.GetPass(Name).BlendState = Clicross.FxRenderState.Opacity;
            _Fx.Refresh();
        }

        public void OnMenuItemNewShadowPassClicked(MenuItem Sender)
        {
            string Name = "shadow";
            if (HasPass(Name))
                return;
            _Fx.CreatePass(Name);
            _Fx.GetPass(Name).Shader = "PipelineResource/FFSRP/Shader/Material/Lit/LitShadow.shader.nda";
            _Fx.GetPass(Name).DepthCompareFunction = Clicross.ComparisonOp.Less;
            _Fx.GetPass(Name).BlendState = Clicross.FxRenderState.Opacity;
            _Fx.Refresh();
        }

        public void OnMenuItemNewShadowAllPassClicked(MenuItem Sender)
        {
            string Name = "shadow_all";
            if (HasPass(Name))
                return;
            _Fx.CreatePass(Name);
            _Fx.GetPass(Name).Shader = "PipelineResource/FFSRP/Shader/Material/Lit/LitShadow.shader.nda";
            _Fx.GetPass(Name).DepthCompareFunction = Clicross.ComparisonOp.Less;
            _Fx.GetPass(Name).BlendState = Clicross.FxRenderState.Opacity;
            _Fx.Refresh();
        }

        public void OnMenuItemNewPreDepthPassClicked(MenuItem Sender)
        {
            string Name = "preDepth";
            if (HasPass(Name))
                return;
            _Fx.CreatePass(Name);
            _Fx.GetPass(Name).Shader = "PipelineResource/FFSRP/Shader/Material/Lit/PreDepth.shader.nda";
            _Fx.GetPass(Name).DepthCompareFunction = Clicross.ComparisonOp.GreaterEqual;
            _Fx.GetPass(Name).BlendState = Clicross.FxRenderState.Opacity;
            _Fx.Refresh();
        }

        public void OnMenuItemNewVoxelizePassClicked(MenuItem Sender)
        {
            string Name = "VoxelizePass";
            if (HasPass(Name))
                return;
            _Fx.CreatePass(Name);
            _Fx.GetPass(Name).Shader = "PipelineResource/FFSRP/Shader/Material/Lit/LitGBufferVoxelizer.shader.nda";
            _Fx.GetPass(Name).DepthCompareFunction = Clicross.ComparisonOp.GreaterEqual;
            _Fx.GetPass(Name).BlendState = Clicross.FxRenderState.Opacity;
            _Fx.GetPass(Name).CullMode = Clicross.CullMode.None;
            _Fx.GetPass(Name).RenderGroup = 2000;
            _Fx.Refresh();
        }

        public void OnMenuItemNewVSMDepthPassClicked(MenuItem Sender)
        {
            string Name = "VSMDepth";
            if (HasPass(Name))
                return;
            _Fx.CreatePass(Name);
            _Fx.GetPass(Name).Shader = "PipelineResource/FFSRP/Shader/Material/Lit/LitVSMDepth.shader.nda";
            _Fx.GetPass(Name).DepthCompareFunction = Clicross.ComparisonOp.Always;
            _Fx.GetPass(Name).BlendState = Clicross.FxRenderState.Opacity;
            _Fx.Refresh();
        }

        public void OnMenuItemNewParticlePassClicked(MenuItem Sender)
        {
            string Name = "Particle";
            if (HasPass(Name))
                return;
            _Fx.CreatePass(Name);

            _Fx.GetPass(Name).Shader = "EngineResource/Shader/ParticleSpriteDefault.shader.nda";
            _Fx.GetPass(Name).BlendState = Clicross.FxRenderState.Transparent;

            _Fx.GetPass(Name).RenderGroup = 3700;

            _Fx.GetPass(Name).DepthWrite = false;
            _Fx.GetPass(Name).CullMode = Clicross.CullMode.Front;

            List<BlendTarget> BlendTargets = new List<BlendTarget>
            {
                new BlendTarget
                {
                    EnableBlend = true,
                    SourceBlend = Clicross.BlendFactor.Src1Alpha,
                    DestinationBlend = Clicross.BlendFactor.InvSrcAlpha,
                    BlendOperation = Clicross.BlendOp.Add,
                    SourceAlphaBlend = Clicross.BlendFactor.InvDestAlpha,
                    DestinationAlphaBlend = Clicross.BlendFactor.One,
                    AlphaBlendOperation = Clicross.BlendOp.Add,
                    ColorWriteMask = new Float4(1, 1, 1, 1)
                },
                new BlendTarget
                {
                    EnableBlend = true,
                    SourceBlend = Clicross.BlendFactor.Src1Alpha,
                    DestinationBlend = Clicross.BlendFactor.InvSrcAlpha,
                    BlendOperation = Clicross.BlendOp.Add,
                    SourceAlphaBlend = Clicross.BlendFactor.InvDestAlpha,
                    DestinationAlphaBlend = Clicross.BlendFactor.One,
                    AlphaBlendOperation = Clicross.BlendOp.Add,
                    ColorWriteMask = new Float4(1, 1, 1, 1)
                }
            };

            _Fx.GetPass(Name).BlendTargets = BlendTargets;
            _Fx.Refresh();
        }


        public void OnMenuItemDeleteClicked(MenuItem Sender)
        {
            DoDelete();
        }

        #endregion

        void OnTreeKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            if (Key == Key.Delete)
            {
                if (Device.IsNoneModifiersDown() || Device.IsShiftDownOnly())
                {
                    DoDelete();
                    bContinue = false;
                }
            }
            else if (Key == Key.F2)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoRename();
                    bContinue = false;
                }
            }
            else if (Key == Key.Escape)
            {
                if (Device.IsNoneModifiersDown())
                {
                    DoCancelSearch();
                    bContinue = false;
                }
            }
        }

        public void DoDelete()
        {
            List<TreeItem> SelectiontList = _Tree.GetSelectedItems();
            foreach (var TreeItem in SelectiontList)
            {
                if (GetTreeItemType(TreeItem) == ItemType.Group)
                {
                    OnMenuItemGroupDeleteClicked(TreeItem);
                }
                else if (GetTreeItemType(TreeItem) == ItemType.Pass)
                {
                    OnMenuItemPassDeleteClicked(TreeItem);
                }
            }
        }

        void DoRename()
        {
            List<TreeItem> SelectiontList = _Tree.GetSelectedItems();
            if (SelectiontList.Count == 1)
            {
                TreeItem SelectedItem = SelectiontList[0];
                _Tree.StartRename(SelectedItem);
            }
        }

        void OnTreeItemRename(Tree Sender, string NewName, string OldName, TreeItem TreeItem)
        {
            if (NewName == "")
            {
                return;
            }
            if (NewName == OldName)
            {
                return;
            }
            // Check if there has the same name in it's level or not

            if (GetTreeItemType(TreeItem) == ItemType.Group)
            {
                if (HasGroup(NewName))
                {
                    UpdateHierarchy();
                    return;
                }

                Fx.Group Group = _Fx.GetGroup(OldName);
                if (Group != null)
                {
                    Group.Rename(NewName);
                }
            }
            else if (GetTreeItemType(TreeItem) == ItemType.Pass)
            {
                if (HasPass(NewName))
                {
                    UpdateHierarchy();
                    return;
                }

                Fx.Pass Pass = _Fx.GetPass(OldName);
                if (Pass != null)
                {
                    Pass.Name = NewName;
                }
            }
        }

        string GetPropertyText(Fx.Property Property)
        {
            return Property.Usage ? Property.Name : Property.Name + " - unused";
        }

        Color GetPropertyColor(Fx.Property Property)
        {
            return Property.Usage && Property.Visible ? Color.White : Color.EDITOR_UI_GRAY_TEXT_COLOR;
        }

        public void UpdateHierarchy()
        {
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            object ObjectInspected = InspectorUI.GetObjectInspected();
            var LastExpandState = GetGroupsExpandState();

            _PropertiesItem.ClearChildren();
            _PassesItem.ClearChildren();

            if (_Fx == null)
            {
                return;
            }

            string FilePath = _Fx.Path;
            _RootItem.SetText("Fx: " + FilePath);

            if (_bSearchMode)
            {
                string SearchPattern = _SearchUI.GetSearchPattern();

                foreach (Fx.Group Group in _Fx.Groups)
                {
                    TreeItem GroupItem = _Tree.CreateItem();
                    GroupItem.SetFolder(true);
                    GroupItem.SetText(Group.Name);
                    GroupItem.SetTagObject(Group);
                    GroupItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Group.png"));
                    if (LastExpandState.ContainsKey(Group.Name))
                        GroupItem.SetExpanded(LastExpandState[Group.Name]);
                    else
                        GroupItem.SetExpanded(true);

                    foreach (Fx.Property Property in Group.Properties)
                    {
                        if (Property.Name.Contains(SearchPattern, StringComparison.OrdinalIgnoreCase))
                        {
                            TreeItem PropertyTreeItem = _Tree.CreateItem();
                            PropertyTreeItem.SetText(GetPropertyText(Property));
                            PropertyTreeItem.SetTextColor(GetPropertyColor(Property));
                            PropertyTreeItem.SetTagObject(Property);
                            PropertyTreeItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Property.png"));

                            GroupItem.AddChild(PropertyTreeItem);
                        }
                    }

                    _PropertiesItem.AddChild(GroupItem);
                }

                foreach (Fx.Pass Pass in _Fx.Passes)
                {
                    if (Pass.Name.Contains(SearchPattern, StringComparison.OrdinalIgnoreCase))
                    {
                        TreeItem ChildTreeItem = _Tree.CreateItem();
                        ChildTreeItem.SetText(Pass.Name);
                        ChildTreeItem.SetTextColor(Color.White);
                        ChildTreeItem.SetTagObject(Pass);
                        ChildTreeItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));

                        _PassesItem.AddChild(ChildTreeItem);
                    }
                }
            }
            else
            {
                foreach (Fx.Group Group in _Fx.Groups)
                {
                    TreeItem GroupItem = _Tree.CreateItem();
                    GroupItem.SetFolder(true);
                    GroupItem.SetText(Group.Name);
                    GroupItem.SetTagObject(Group);
                    GroupItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Group.png"));
                    if (LastExpandState.ContainsKey(Group.Name))
                        GroupItem.SetExpanded(LastExpandState[Group.Name]);
                    else
                        GroupItem.SetExpanded(true);

                    foreach (Fx.Property Property in Group.Properties)
                    {
                        TreeItem PropertyItem = _Tree.CreateItem();
                        PropertyItem.SetText(GetPropertyText(Property));
                        PropertyItem.SetTextColor(GetPropertyColor(Property));
                        PropertyItem.SetTagObject(Property);
                        PropertyItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Property.png"));

                        GroupItem.AddChild(PropertyItem);
                    }

                    _PropertiesItem.AddChild(GroupItem);
                }

                foreach (Fx.Pass Pass in _Fx.Passes)
                {
                    TreeItem PassItem = _Tree.CreateItem();
                    PassItem.SetText(Pass.Name);
                    PassItem.SetTextColor(Color.White);
                    PassItem.SetTagObject(Pass);
                    PassItem.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/PassInfo.png"));

                    _PassesItem.AddChild(PassItem);
                }
            }

            if (_SelectedTagObjectInSearchMode.Count > 0)
            {
                foreach (object TagObject in _SelectedTagObjectInSearchMode)
                {
                    TreeItem Item = _Tree.FindItemByTagObject(TagObject);
                    if (Item != null)
                    {
                        _Tree.AddSelectItemNoEvent(Item);
                    }
                }
                _SelectedTagObjectInSearchMode.Clear();
            }

            if (ObjectInspected != null)
            {
                if (ObjectInspected is Fx.Property PropertyInspected)
                {
                    InspectorUI.SetObjectInspected(_Fx.GetProperty(PropertyInspected.Name));
                    InspectorUI.InspectObject();
                }
                else if (ObjectInspected is Fx.Pass PassInspected)
                {
                    InspectorUI.SetObjectInspected(_Fx.GetPass(PassInspected.Name));
                    InspectorUI.InspectObject();
                }
            }
        }

        public void OnButtonSaveClicked(Button Sender)
        {
            _Fx.Save();
        }

        public void OnButtonRefreshClicked(Button Sender)
        {
            _Fx.Refresh(true);
        }

        public void OnButtonReloadClicked(Button Sender)
        {
            _Fx.ImmediateReload();
        }

        public void OnButtonRemoveUnusedPropsClicked(Button Sender)
        {
            _Fx.RemoveUnusedProps();
        }

        public bool IsDragging()
        {
            return _ItemsDragged != null && _ItemsDragged.Count > 0;
        }

        #region Drag Drop

        void OnDragDropManagerDragBegin(DragDropManager Sender, UIManager UIManager, int OriginalMouseX, int OriginalMouseY, ref bool bDragBegin, ref string ImageFilename)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (_Tree.IsPointIn_Recursively(OriginalMouseX, OriginalMouseY) && !_bSearchMode)
            {
                TreeItemHitTest HitTest = _Tree.HitTest(OriginalMouseX, OriginalMouseY);
                if (HitTest.HitResult != TreeItemHitResult.Nothing)
                {
                    TreeItem TreeItemDrag = HitTest.ItemHit;
                    if (TreeItemDrag != null)
                    {
                        List<TreeItem> ItemsToDrag = new List<TreeItem>();
                        List<TreeItem> SelectedItems = _Tree.GetSelectedItems();
                        if (SelectedItems.Contains(TreeItemDrag))
                        {
                            ItemsToDrag = SelectedItems.Clone();
                        }
                        else
                        {
                            _Tree.SelectItem(TreeItemDrag);
                            ItemsToDrag.Add(TreeItemDrag);
                        }
                        if (ItemsToDrag.FindIndex(Item => GetTreeItemType(Item) != ItemType.Property) == -1 ||
                            ItemsToDrag.FindIndex(Item => GetTreeItemType(Item) != ItemType.Pass) == -1 ||
                            ItemsToDrag.FindIndex(Item => GetTreeItemType(Item) != ItemType.Group) == -1)
                        {
                            bDragBegin = true;
                            _ItemsDragged = ItemsToDrag;
                        }
                        else
                        {
                            bDragBegin = false;
                        }

                    }
                }
            }
        }

        void OnDragDropManagerDragMove(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY)
        {
            TreeDropInfo DropInfo = null;
            if (UIManager == GetUIManager() && IsDragging())
            {
                if (_Tree.IsPointIn(MouseX, MouseY))
                {
                    DropInfo = new TreeDropInfo();
                    DropInfo.bDetailed = true;
                    _Tree.HitTest(MouseX, MouseY, false, DropInfo);
                }
            }
            _Tree.SetDropInfo(DropInfo);
        }

        void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (IsDragging())
            {
                if (_Tree.IsPointIn(MouseX, MouseY))
                {
                    TreeDropInfo DropInfo = new TreeDropInfo();
                    DropInfo.bDetailed = true;
                    _Tree.HitTest(MouseX, MouseY, false, DropInfo);
                    TreeItem TreeItemDrop = DropInfo.InsertParent;
                    if (TreeItemDrop != null)
                    {
                        foreach (TreeItem ItemDragged in _ItemsDragged)
                        {
                            if (ItemDragged != TreeItemDrop)
                            {
                                ChangeHierarchy(ItemDragged, TreeItemDrop, DropInfo.InsertPosition);
                            }
                        }
                    }
                }
            }
        }

        void OnDragDropManagerDragClear(DragDropManager Sender)
        {
            ClearDragStates();
        }

        void OnDragDropManagerDragCancel(DragDropManager Sender)
        {
            ClearDragStates();
        }

        void ClearDragStates()
        {
            _ItemsDragged = null;
            _Tree.SetDropInfo(null);
        }

        void OnDockingCardClose(DockingCard Sender, ref bool bNotToClose)
        {
            if (_Fx != null && _Fx.bModified)
            {
                CommonDialogUI SaveDialogUI = new CommonDialogUI();
                SaveDialogUI.Initialize(GetUIManager(), "Save", "Do you want to save changes?", CommonDialogType.OKCancel);
                SaveDialogUI.CloseEvent += (Sender, Result) =>
                {
                    if (Result == CommonDialogResult.OK)
                    {
                        _Fx.Save();
                    }
                    else if (Result == CommonDialogResult.Cancel)
                    {
                        _Fx.ImmediateReload();
                    }

                    _Fx = null;
                    UpdateHierarchy();
                    GetDockingCard().CloseCard();
                };
                DialogUIManager.GetInstance().ShowDialogUI(SaveDialogUI);
                bNotToClose = true;
            }
        }

        void ChangeHierarchy(TreeItem ItemDragged, TreeItem ItemToDrop, int InsertPosition)
        {
            if (GetTreeItemType(ItemDragged) == ItemType.Property && GetTreeItemType(ItemToDrop) == ItemType.Group)
            {
                Fx.Property Property = _Fx.GetProperty(ItemDragged.GetText());

                string Prev = "";
                string Next = "";
                if (InsertPosition - 1 >= 0 && InsertPosition - 1 < ItemToDrop.GetChildCount())
                {
                    Fx.Property PrevProperty = _Fx.GetProperty(ItemToDrop.GetChild(InsertPosition - 1).GetText());
                    Prev = PrevProperty.Name;
                }
                if (InsertPosition >= 0 && InsertPosition < ItemToDrop.GetChildCount())
                {
                    Fx.Property NextProperty = _Fx.GetProperty(ItemToDrop.GetChild(InsertPosition).GetText());
                    Next = NextProperty.Name;
                }
                var fx = _Fx.ResourcePtr as Clicross.resource.Fx;
                fx.ChangePropertyPosition(Property.Name, Prev, Next);

                Fx.Group Group = _Fx.GetGroup(ItemToDrop.GetText());
                if (Group.Name != Property.Group)
                {
                    fx.SetPropertyGroup(Property.Name, Group.Name);
                }

                _Fx.SetModified(true);
                _Fx.Refresh(true);
            }
            else if (GetTreeItemType(ItemDragged) == ItemType.Pass && GetTreeItemType(ItemToDrop) == ItemType.Passes)
            {
                Fx.Pass Pass = _Fx.GetPass(ItemDragged.GetText());
                string Next = "";
                if (InsertPosition >= 0 && InsertPosition < ItemToDrop.GetChildCount())
                {
                    Fx.Pass NextPass = _Fx.GetPass(ItemToDrop.GetChild(InsertPosition).GetText());
                    Next = NextPass.Name;
                }
                var fx = _Fx.ResourcePtr as Clicross.resource.Fx;
                fx.ChangePassPosition(Pass.Name, Next);

                _Fx.SetModified(true);
                _Fx.Refresh(true);
            }
            else if (GetTreeItemType(ItemDragged) == ItemType.Group && GetTreeItemType(ItemToDrop) == ItemType.Properties)
            {
                Fx.Group Group = _Fx.GetGroup(ItemDragged.GetText());
                string Next = "";
                if (InsertPosition >= 0 && InsertPosition < ItemToDrop.GetChildCount())
                {
                    Fx.Group NextGroup = _Fx.GetGroup(ItemToDrop.GetChild(InsertPosition).GetText());
                    Next = NextGroup.Name;
                }
                var fx = _Fx.ResourcePtr as Clicross.resource.Fx;
                fx.ChangeGroupPosition(Group.Name, Next);
                _Fx.SetModified(true);
                _Fx.Refresh(true);
            }
        }

        #endregion

        Dictionary<string, bool> GetGroupsExpandState()
        {
            Dictionary<string, bool> ExpandState = new Dictionary<string, bool>();
            foreach (TreeItem group in _PropertiesItem.GetChildList())
            {
                ExpandState.Add(group.GetText(), group.GetExpanded());
            }
            return ExpandState;
        }

        ItemType GetTreeItemType(TreeItem Item)
        {
            if (Item == _RootItem)
            {
                return ItemType.Root;
            }
            else if (Item == _PropertiesItem)
            {
                return ItemType.Properties;
            }
            else if (Item.GetTagObject() is Fx.Group)
            {
                return ItemType.Group;
            }
            else if (Item.GetTagObject() is Fx.Property)
            {
                return ItemType.Property;
            }
            else if (Item == _PassesItem)
            {
                return ItemType.Passes;
            }
            else if (Item.GetTagObject() is Fx.Pass)
            {
                return ItemType.Pass;
            }
            DebugHelper.Assert(false);
            return ItemType.Invalid;
        }

        #region Search Event

        void EnterSearchMode()
        {
            _bSearchMode = true;
        }

        public void ExitSearchMode()
        {
            if (_bSearchMode)
            {
                _bSearchMode = false;
                _SearchUI.ClearSearchPattern();
                foreach (TreeItem SelectedItem in _Tree.GetSelectedItems())
                {
                    object TagObject = SelectedItem.GetTagObject();
                    if (TagObject != null)
                    {
                        _SelectedTagObjectInSearchMode.Add(TagObject);
                    }
                }
            }
        }

        void DoCancelSearch()
        {
            if (_bSearchMode)
            {
                ExitSearchMode();
                UpdateHierarchy();
                _SearchUI.GetEdit().SetFocus();
            }
        }

        void DoSearch()
        {
            string SearchPattern = _SearchUI.GetSearchPattern();
            if (SearchPattern != "")
            {
                EnterSearchMode();
            }
            else
            {
                ExitSearchMode();
            }
            UpdateHierarchy();
        }

        void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            DoSearch();
        }

        void OnSearchUICancel(SearchUI Sender)
        {
            DoCancelSearch();
        }

        #endregion
    }
}