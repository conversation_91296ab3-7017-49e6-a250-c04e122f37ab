using System;

namespace CrossEditor
{
    class EditOperation_ModifyProperty : EditOperation
    {
        public object _Object;
        public ObjectProperty _ObjectProperty;
        public object _OldValue;
        public object _NewValue;
        public Inspector _ValueConatainer;

        public EditOperation_ModifyProperty(ObjectProperty InObjectProperty, object InOldValue, object InNewValue, Inspector ValueConatainer)
        {
            _bCombinable = true;
            _Object = InObjectProperty.Object;
            _ObjectProperty = InObjectProperty;
            _OldValue = InOldValue;
            _NewValue = InNewValue;
            _ValueConatainer = ValueConatainer;
        }

        bool ValueIsList()
        {
            if (_OldValue == null)
            {
                return false;
            }
            Type ValueType = _OldValue.GetType();
            return (ValueType.Name == "List`1" && ValueType.Namespace == "System.Collections.Generic" || ValueType.Name.Contains("vector_cross"));
        }

        public override void Undo()
        {
            _ObjectProperty.SetPropertyValueFunction(_Object, _ObjectProperty.Name, _OldValue, null);
            if (_ValueConatainer != null)
            {
                if (ValueIsList() || _Object is Resource)
                {
                    _ValueConatainer.GetInspectorHandler().InspectObject();
                }
                else
                {
                    _ValueConatainer.GetInspectorHandler().ReadValue();
                }
                _ValueConatainer.GetInspectorHandler().UpdateLayout();
            }
            else
            {
                InspectorUI.GetInstance().InspectObject();
            }
        }

        public override void Redo()
        {
            _ObjectProperty.SetPropertyValueFunction(_Object, _ObjectProperty.Name, _NewValue, null);
            if (_ValueConatainer != null)
            {
                if (ValueIsList() || _Object is Resource)
                {
                    _ValueConatainer.GetInspectorHandler().InspectObject();
                }
                else
                {
                    _ValueConatainer.GetInspectorHandler().ReadValue();
                }
                _ValueConatainer.GetInspectorHandler().UpdateLayout();
            }
            else
            {
                InspectorUI.GetInstance().InspectObject();
            }
        }
    }
}
