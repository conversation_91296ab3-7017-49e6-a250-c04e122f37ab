namespace CrossEditor
{
    class EditOperation_AddChildEntity : EditOperation
    {
        Entity _ParentEntity;
        Entity _ChildEntity;

        public EditOperation_AddChildEntity(Entity ParentEntity, Entity ChildEntity)
        {
            _ParentEntity = ParentEntity;
            _ChildEntity = ChildEntity;
        }

        public override void Undo()
        {
            _ParentEntity.RemoveChildEntity(_ChildEntity);
            if (_ChildEntity.IsAlive())
            {
                _ChildEntity.RuntimeRemove();
            }
            InspectorUI.GetInstance().SetObjectInspected(null);
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_ParentEntity);
        }

        public override void Redo()
        {
            _ParentEntity.AddChildEntity(_ChildEntity);
            _ChildEntity.RuntimeAdd();
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_ChildEntity);
        }
    }
}
