using System.Collections.Generic;

namespace CrossEditor
{
    class RemoveComponentItem
    {
        Entity _Entity;
        Component _Component;

        public RemoveComponentItem(Entity Entity, Component Component)
        {
            _Entity = Entity;
            _Component = Component;
        }

        public void Undo()
        {
            _Entity.AddComponent(_Component);
            _Component.RuntimeReapplyProperties();
        }

        public void Redo()
        {
            _Entity.RemoveComponent(_Component);
        }
    }

    class EditOperation_RemoveComponents : EditOperation
    {
        List<RemoveComponentItem> _RemoveComponentItemList;

        public EditOperation_RemoveComponents()
        {
            _RemoveComponentItemList = new List<RemoveComponentItem>();
        }

        public void AddRemoveComponentItem(Entity Entity, Component Component)
        {
            RemoveComponentItem RemoveComponentItem = new RemoveComponentItem(Entity, Component);
            _RemoveComponentItemList.Add(RemoveComponentItem);
        }

        public override void Undo()
        {
            foreach (RemoveComponentItem RemoveComponentItem in _RemoveComponentItemList)
            {
                RemoveComponentItem.Undo();
            }
            InspectorUI.GetInstance().InspectObject();
        }

        public override void Redo()
        {
            foreach (RemoveComponentItem RemoveComponentItem in _RemoveComponentItemList)
            {
                RemoveComponentItem.Redo();
            }
            InspectorUI.GetInstance().InspectObject();
        }
    }
    class RemoveGameObjectComponentItem
    {
        GameObject _GameObject;
        GameObjectComponent _Component;

        public RemoveGameObjectComponentItem(GameObject gameObject, GameObjectComponent Component)
        {
            _GameObject = gameObject;
            _Component = Component;
        }

        public void Undo()
        {
            _GameObject.AddComponent(_Component);
            _Component.RuntimeReapplyProperties();
            var transform = _GameObject.mEntity.GetComponent<Transform>();
            if (transform != null)
            {
                transform.RefreshTransform();
            }
        }

        public void Redo()
        {
            _GameObject.RemoveComponent(_Component);
        }
    }

    class EditOperation_RemoveGameObjectComponents : EditOperation
    {
        List<RemoveGameObjectComponentItem> _RemoveComponentItemList;

        public EditOperation_RemoveGameObjectComponents()
        {
            _RemoveComponentItemList = new List<RemoveGameObjectComponentItem>();
        }

        public void AddRemoveComponentItem(GameObject GameObject, GameObjectComponent Component)
        {
            RemoveGameObjectComponentItem RemoveComponentItem = new RemoveGameObjectComponentItem(GameObject, Component);
            _RemoveComponentItemList.Add(RemoveComponentItem);
        }

        public override void Undo()
        {
            foreach (RemoveGameObjectComponentItem RemoveComponentItem in _RemoveComponentItemList)
            {
                RemoveComponentItem.Undo();
            }
            InspectorUI.GetInstance().InspectObject();
        }

        public override void Redo()
        {
            foreach (RemoveGameObjectComponentItem RemoveComponentItem in _RemoveComponentItemList)
            {
                RemoveComponentItem.Redo();
            }
            InspectorUI.GetInstance().InspectObject();
        }
    }
}
