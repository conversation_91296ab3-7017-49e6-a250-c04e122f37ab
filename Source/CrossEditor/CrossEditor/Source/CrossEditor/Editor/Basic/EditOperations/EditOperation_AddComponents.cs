using System.Collections.Generic;

namespace CrossEditor
{
    class AddComponentItem
    {
        Entity _Entity;
        Component _Component;

        public AddComponentItem(Entity Entity, Component Component)
        {
            _Entity = Entity;
            _Component = Component;
        }

        public void Undo()
        {
            _Entity.RemoveComponent(_Component);
        }

        public void Redo()
        {
            _Entity.AddComponent(_Component);
            _Component.RuntimeReapplyProperties();
        }
    }

    class EditOperation_AddComponents : EditOperation
    {
        List<AddComponentItem> _AddComponentItemList;

        public EditOperation_AddComponents()
        {
            _AddComponentItemList = new List<AddComponentItem>();
        }

        public void AddAddComponentItem(Entity Entity, Component Component)
        {
            AddComponentItem AddComponentItem = new AddComponentItem(Entity, Component);
            _AddComponentItemList.Add(AddComponentItem);
        }

        public override void Undo()
        {
            foreach (AddComponentItem AddComponentItem in _AddComponentItemList)
            {
                AddComponentItem.Undo();
            }
            InspectorUI.GetInstance().InspectObject();
        }

        public override void Redo()
        {
            foreach (AddComponentItem AddComponentItem in _AddComponentItemList)
            {
                AddComponentItem.Redo();
            }
            InspectorUI.GetInstance().InspectObject();
        }
    }
    class AddGameObjectComponentItem
    {
        GameObject _GameObject;
        GameObjectComponent _Component;

        public AddGameObjectComponentItem(GameObject GameObject, GameObjectComponent Component)
        {
            _GameObject = GameObject;
            _Component = Component;
        }

        public void Undo()
        {
            _GameObject.RemoveComponent(_Component);
        }

        public void Redo()
        {
            _GameObject.AddComponent(_Component);
            _Component.RuntimeReapplyProperties();
        }
    }

    class EditOperation_AddGameObjectComponents : EditOperation
    {
        List<AddGameObjectComponentItem> _AddComponentItemList;

        public EditOperation_AddGameObjectComponents()
        {
            _AddComponentItemList = new List<AddGameObjectComponentItem>();
        }

        public void AddAddGameObjectComponentItem(GameObject GameObject, GameObjectComponent Component)
        {
            AddGameObjectComponentItem AddGameObjectComponentItem = new AddGameObjectComponentItem(GameObject, Component);
            _AddComponentItemList.Add(AddGameObjectComponentItem);
        }

        public override void Undo()
        {
            foreach (AddGameObjectComponentItem AddGameObjectComponentItem in _AddComponentItemList)
            {
                AddGameObjectComponentItem.Undo();
            }
            InspectorUI.GetInstance().InspectObject();
        }

        public override void Redo()
        {
            foreach (AddGameObjectComponentItem AddComponentItem in _AddComponentItemList)
            {
                AddComponentItem.Redo();
            }
            InspectorUI.GetInstance().InspectObject();
        }
    }
}
