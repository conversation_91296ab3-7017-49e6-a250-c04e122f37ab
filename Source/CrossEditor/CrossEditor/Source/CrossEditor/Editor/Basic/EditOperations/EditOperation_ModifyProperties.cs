using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class EditOperation_ModifyProperties : EditOperation
    {
        object _Object;
        List<PropertyInfo> _PropertyInfoList;
        List<object> _OldValueList;
        List<object> _NewValueList;

        public EditOperation_ModifyProperties(object Object, List<PropertyInfo> PropertyInfoList, List<object> OldValueList, List<object> NewValueList)
        {
            _Object = Object;
            _PropertyInfoList = PropertyInfoList;
            _OldValueList = OldValueList;
            _NewValueList = NewValueList;
        }

        public override void Undo()
        {
            int Count = _PropertyInfoList.Count;
            for (int i = 0; i < Count; i++)
            {
                PropertyInfo PropertyInfo = _PropertyInfoList[i];
                object OldValue = _OldValueList[i];
                PropertyInfo.SetValue(_Object, OldValue);
            }
            InspectorUI.GetInstance().InspectObject();
        }

        public override void Redo()
        {
            int Count = _PropertyInfoList.Count;
            for (int i = 0; i < Count; i++)
            {
                PropertyInfo PropertyInfo = _PropertyInfoList[i];
                object NewValue = _NewValueList[i];
                PropertyInfo.SetValue(_Object, NewValue);
            }
            InspectorUI.GetInstance().InspectObject();
        }
    }
}
