using System.Collections.Generic;

namespace CrossEditor
{
    class RemoveEntityItem
    {
        Entity _ParentEntity;
        Entity _ChildEntity;
        int _EntityIndex;

        public RemoveEntityItem(Entity ParentEntity, Entity ChildEntity, int EntityIndex)
        {
            _ParentEntity = ParentEntity;
            _ChildEntity = ChildEntity;
            _EntityIndex = EntityIndex;
        }

        public void Undo()
        {
            _ParentEntity.InsertChildEntity(_ChildEntity, _EntityIndex);
            _ChildEntity.RuntimeAdd();
        }

        public void Redo()
        {
            _ParentEntity.RemoveChildEntity(_ChildEntity);
            _ChildEntity.RuntimeRemove();
        }
    }

    class EditOperation_RemoveEntities : EditOperation
    {
        List<RemoveEntityItem> _RemoveEntityItemList;

        public EditOperation_RemoveEntities()
        {
            _RemoveEntityItemList = new List<RemoveEntityItem>();
        }

        public void AddRemoveEntityItem(Entity ParentEntity, Entity ChildEntity, int EntityIndex)
        {
            RemoveEntityItem RemoveEntityItem = new RemoveEntityItem(ParentEntity, ChildEntity, EntityIndex);
            _RemoveEntityItemList.Add(RemoveEntityItem);
        }

        public override void Undo()
        {
            foreach (RemoveEntityItem RemoveEntityItem in _RemoveEntityItemList)
            {
                RemoveEntityItem.Undo();
            }
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(null);
        }

        public override void Redo()
        {
            foreach (RemoveEntityItem RemoveEntityItem in _RemoveEntityItemList)
            {
                RemoveEntityItem.Redo();
            }
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(null);
        }
    }
}
