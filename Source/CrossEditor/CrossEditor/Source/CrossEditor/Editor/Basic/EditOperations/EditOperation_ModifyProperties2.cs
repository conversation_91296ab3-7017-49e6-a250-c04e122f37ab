using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class ModifyPropertyItem
    {
        public object _Object;
        public ObjectProperty _ObjectProperty;
        public object _OldValue;
        public object _NewValue;

        public ModifyPropertyItem(object Object, ObjectProperty ObjectProperty, object OldValue, object NewValue)
        {
            _Object = Object;
            _ObjectProperty = ObjectProperty;
            _OldValue = OldValue;
            _NewValue = NewValue;
        }

        public void Undo()
        {
            _ObjectProperty.Get_RawSetPropertyValueFunction()(_Object, _ObjectProperty.Name, _OldValue, null);
        }

        public void Redo()
        {
            _ObjectProperty.Get_RawSetPropertyValueFunction()(_Object, _ObjectProperty.Name, _NewValue, null);
        }

        public bool ValueIsList()
        {
            if (_OldValue == null)
            {
                return false;
            }
            Type ValueType = _OldValue.GetType();
            return ValueType.Name == "List`1" && ValueType.Namespace == "System.Collections.Generic";
        }
    }

    public class EditOperation_ModifyProperties2 : EditOperation
    {
        public List<ModifyPropertyItem> _ModifyPropertyItemList;

        public EditOperation_ModifyProperties2()
        {
            _bCombinable = true;
            _ModifyPropertyItemList = new List<ModifyPropertyItem>();
        }

        public void AddModifyProperty(object Object, ObjectProperty ObjectProperty, object OldValue, object NewValue)
        {
            ModifyPropertyItem ModifyPropertyItem = new ModifyPropertyItem(Object, ObjectProperty, OldValue, NewValue);
            _ModifyPropertyItemList.Add(ModifyPropertyItem);
        }

        public override void Undo()
        {
            foreach (ModifyPropertyItem ModifyPropertyItem in _ModifyPropertyItemList)
            {
                ModifyPropertyItem.Undo();
            }
            if (_ModifyPropertyItemList[0].ValueIsList())
            {
                InspectorUI.GetInstance().InspectObject();
            }
            else
            {
                InspectorUI.GetInstance().ReadValueAndUpdateLayout();
            }
        }

        public override void Redo()
        {
            foreach (ModifyPropertyItem ModifyPropertyItem in _ModifyPropertyItemList)
            {
                ModifyPropertyItem.Redo();
            }
            if (_ModifyPropertyItemList[0].ValueIsList())
            {
                InspectorUI.GetInstance().InspectObject();
            }
            else
            {
                InspectorUI.GetInstance().ReadValueAndUpdateLayout();
            }
        }
    }
}
