using System.Collections.Generic;

namespace CrossEditor
{
    class EditorOperation_FallGound : EditOperation
    {
        public List<Entity> _Entities;
        public List<Vector3d> _OldPoses;
        public List<Vector3d> _NewPoses;

        public EditorOperation_FallGound(List<Entity> Entities, List<Vector3d> OldPoses, List<Vector3d> NewPoses)
        {
            _Entities = Entities;
            _OldPoses = OldPoses;
            _NewPoses = NewPoses;
        }

        public override void Undo()
        {
            for (int i = 0; i < _Entities.Count; i++)
            {
                Entity Value = _Entities[i];
                Transform Transform = Value.GetTransformComponent();
                Transform.SetWorldTranslation(_OldPoses[i]);
                Transform.SyncDataFromEngine();
                Transform.RefreshTransform();
            }
        }

        public override void Redo()
        {
            for (int i = 0; i < _Entities.Count; i++)
            {
                Entity Value = _Entities[i];
                Transform Transform = Value.GetTransformComponent();
                Transform.SetWorldTranslation(_NewPoses[i]);
                Transform.SyncDataFromEngine();
                Transform.RefreshTransform();
            }
        }
    }
}
