using CEngine;
namespace CrossEditor
{
    class EditOperation_ChangeTRS : EditOperation
    {
        Entity _TargetEntity;
        Double3 _OldTranslation;
        Double3 _NewTranslation;
        Double3 _OldRotation;
        Double3 _NewRotation;
        Double3 _OldScale;
        Double3 _NewScale;

        public EditOperation_ChangeTRS(Entity TargetEntity, Double3 OldTranslation, Double3 NewTranslation)
        {
            _TargetEntity = TargetEntity;
            _OldTranslation = OldTranslation;
            _NewTranslation = NewTranslation;
        }

        public EditOperation_ChangeTRS(Entity TargetEntity, Double3 OldTranslation, Double3 NewTranslation, Double3 OldRotation, Double3 NewRotation)
        {
            _TargetEntity = TargetEntity;
            _OldTranslation = OldTranslation;
            _NewTranslation = NewTranslation;
            _OldRotation = OldRotation;
            _NewRotation = NewRotation;
        }

        public EditOperation_ChangeTRS(Entity TargetEntity, Double3 OldTranslation, Double3 NewTranslation, Double3 OldRotation, Double3 NewRotation, Double3 OldScale, Double3 NewScale)
        {
            _TargetEntity = TargetEntity;
            _OldTranslation = OldTranslation;
            _NewTranslation = NewTranslation;
            _OldRotation = OldRotation;
            _NewRotation = NewRotation;
            _OldScale = OldScale;
            _NewScale = NewScale;
        }

        public override void Undo()
        {
            Transform Transform = _TargetEntity.GetTransformComponent();
            if (Transform != null)
            {
                if (_OldTranslation != null)
                {
                    Transform.SetWorldTranslation(_OldTranslation);
                }
                if (_OldRotation != null)
                {
                    Transform.SetWorldRotation(_OldRotation);
                }
                if (_OldScale != null)
                {
                    Transform.SetWorldScale(_OldScale);
                }
            }
        }

        public override void Redo()
        {
            Transform Transform = _TargetEntity.GetTransformComponent();
            if (Transform != null)
            {
                if (_OldTranslation != null)
                {
                    Transform.SetWorldTranslation(_NewTranslation);
                }
                if (_OldRotation != null)
                {
                    Transform.SetWorldRotation(_NewRotation);
                }
                if (_OldScale != null)
                {
                    Transform.SetWorldScale(_NewScale);
                }
            }
        }
    }
}
