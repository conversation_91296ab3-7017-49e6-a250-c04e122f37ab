using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class EditOperation_ChangeVisibility : EditOperation
    {
        List<Entity> _EntitiesToHide;
        List<Entity> _EntitiesToShow;

        public EditOperation_ChangeVisibility(List<Entity> EntitiesToHide, List<Entity> EntitiesToShow)
        {
            _EntitiesToHide = EntitiesToHide.Clone();
            _EntitiesToShow = EntitiesToShow.Clone();
        }

        public override void Undo()
        {
            foreach (Entity Entity in _EntitiesToShow)
            {
                Entity.Enable = false;
            }

            foreach (Entity Entity in _EntitiesToHide)
            {
                Entity.Enable = true;
            }

            HierarchyUI.GetInstance().UpdateVisibilityButton();
            HierarchyUI.GetInstance().GetScene().SetDirty();
        }

        public override void Redo()
        {
            foreach (Entity Entity in _EntitiesToHide)
            {
                Entity.Enable = false;
            }

            foreach (Entity Entity in _EntitiesToShow)
            {
                Entity.Enable = true;
            }

            HierarchyUI.GetInstance().UpdateVisibilityButton();
            HierarchyUI.GetInstance().GetScene().SetDirty();
        }
    }
}
