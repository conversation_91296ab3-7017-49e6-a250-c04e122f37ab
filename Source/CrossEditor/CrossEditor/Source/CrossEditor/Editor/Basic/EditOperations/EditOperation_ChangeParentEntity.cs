namespace CrossEditor
{
    class EditOperation_ChangeParentEntity : EditOperation
    {
        Entity _OldParentEntity;
        Entity _NewParentEntity;
        Entity _Entity;
        int _OldInsertIndex;
        int _NewInsertIndex;

        public EditOperation_ChangeParentEntity(Entity OldParentEntity, Entity NewParentEntity, Entity Entity, int OldInsertIndex, int NewInsertIndex)
        {
            _OldParentEntity = OldParentEntity;
            _NewParentEntity = NewParentEntity;
            _Entity = Entity;
            _OldInsertIndex = OldInsertIndex;
            _NewInsertIndex = NewInsertIndex;
        }

        public override void Undo()
        {
            _NewParentEntity.RemoveChildEntity(_Entity);
            _OldParentEntity.InsertChildEntity(_Entity, _OldInsertIndex);
            _Entity.RuntimeJointToParent();
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_Entity);
        }

        public override void Redo()
        {
            _OldParentEntity.RemoveChildEntity(_Entity);
            _NewParentEntity.InsertChildEntity(_Entity, _NewInsertIndex);
            _Entity.RuntimeJointToParent();
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_Entity);
        }
    }
}
