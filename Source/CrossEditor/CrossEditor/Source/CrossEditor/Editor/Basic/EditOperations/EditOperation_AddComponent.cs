namespace CrossEditor
{
    class EditOperation_AddComponent : EditOperation
    {
        Entity _Entity;
        Component _Component;

        public EditOperation_AddComponent(Entity Entity, Component Component)
        {
            _Entity = Entity;
            _Component = Component;
        }

        public override void Undo()
        {
            _Entity.RemoveComponent(_Component);
            InspectorUI.GetInstance().InspectObject();
        }

        public override void Redo()
        {
            _Entity.AddComponent(_Component);
            _Component.RuntimeReapplyProperties();
            InspectorUI.GetInstance().InspectObject();
        }
    }
}
