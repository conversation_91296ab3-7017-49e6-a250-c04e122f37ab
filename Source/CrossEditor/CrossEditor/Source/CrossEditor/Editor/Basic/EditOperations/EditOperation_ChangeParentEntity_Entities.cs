using System.Collections.Generic;

namespace CrossEditor
{
    class ChangeParentEntityItem
    {
        Entity _OldParentEntity;
        Entity _NewParentEntity;
        Entity _Entity;
        int _OldInsertIndex;
        int _NewInsertIndex;

        public ChangeParentEntityItem(Entity OldParentEntity, Entity NewParentEntity, Entity Entity, int OldInsertIndex, int NewInsertIndex)
        {
            _OldParentEntity = OldParentEntity;
            _NewParentEntity = NewParentEntity;
            _Entity = Entity;
            _OldInsertIndex = OldInsertIndex;
            _NewInsertIndex = NewInsertIndex;
        }

        public void Undo()
        {
            _NewParentEntity.RemoveChildEntity(_Entity);
            _OldParentEntity.InsertChildEntity(_Entity, _OldInsertIndex);
            _Entity.RuntimeJointToParent();
        }

        public void Redo()
        {
            _OldParentEntity.RemoveChildEntity(_Entity);
            _NewParentEntity.InsertChildEntity(_Entity, _NewInsertIndex);
            _Entity.RuntimeJointToParent();
        }
    }

    class EditOperation_ChangeParentEntity_Entities : EditOperation
    {
        List<ChangeParentEntityItem> _ChangeParentEntityItemList;

        public EditOperation_ChangeParentEntity_Entities()
        {
            _ChangeParentEntityItemList = new List<ChangeParentEntityItem>();
        }

        public void AddChangeParentEntityItem(Entity OldParentEntity, Entity NewParentEntity, Entity Entity, int OldInsertIndex, int NewInsertIndex)
        {
            ChangeParentEntityItem RemoveComponentItem = new ChangeParentEntityItem(OldParentEntity, NewParentEntity, Entity, OldInsertIndex, NewInsertIndex);
            _ChangeParentEntityItemList.Add(RemoveComponentItem);
        }

        public override void Undo()
        {
            int Count = _ChangeParentEntityItemList.Count;
            for (int i = Count - 1; i >= 0; i--)
            {
                ChangeParentEntityItem ChangeParentEntityItem = _ChangeParentEntityItemList[i];
                ChangeParentEntityItem.Undo();
            }
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
        }

        public override void Redo()
        {
            foreach (ChangeParentEntityItem ChangeParentEntityItem in _ChangeParentEntityItemList)
            {
                ChangeParentEntityItem.Redo();
            }
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
        }

        public override bool Valid()
        {
            return _ChangeParentEntityItemList.Count > 0;
        }
    }
}
