using System;

namespace CrossEditor
{
    class RandomNumber
    {
        static RandomNumber _RandomNumber = new RandomNumber();

        public static RandomNumber GetInstance()
        {
            return _RandomNumber;
        }

        public Vector3d GenerateRandomSize(Vector3d size, double sizeVariation)
        {
            Vector3d Result = new Vector3d();
            if (sizeVariation <= 0.25)
            {
                Result = size;
            }
            else if (sizeVariation <= 0.5)
            {
                Result = new Vector3d((float)new Random().Next(10, 20) / 10, size.Y, size.Z);
            }
            else if (sizeVariation <= 0.75)
            {
                Result = new Vector3d((float)new Random().Next(10, 20) / 10, size.Y, new Random().Next(10, 20) / 10);
            }
            else if (sizeVariation <= 1.0)
            {
                Result = new Vector3d((float)new Random().Next(10, 20) / 10, new Random().Next(10, 20) / 10,
                    new Random().Next(10, 20) / 10);
            }
            return Result;
        }

        public Vector3d GenerateUniformRandomSize(Vector3d size, float sizeVariation)
        {
            Vector3d Result = new Vector3d();
            if (sizeVariation <= 0.25)
            {
                Result = size;
            }
            else if (sizeVariation <= 0.5)
            {
                double Value1 = new Random().Next(1, (int)(size.X * 10)) / 10;
                Result = new Vector3d(Value1, Value1, Value1);
            }
            else if (sizeVariation <= 0.75)
            {
                double Value2 = new Random().Next(2, (int)(size.X * 10)) / 10;
                Result = new Vector3d(Value2, Value2, Value2);
            }
            else if (sizeVariation <= 1.0)
            {
                double Value3 = new Random().Next(3, (int)(size.X * 10)) / 10;
                Result = new Vector3d(Value3, Value3, Value3);
            }
            return Result;
        }
    }
}
