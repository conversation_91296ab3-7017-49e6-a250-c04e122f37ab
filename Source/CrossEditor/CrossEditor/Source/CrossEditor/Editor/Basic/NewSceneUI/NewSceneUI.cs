using CEngine;
using EditorUI;

namespace CrossEditor
{
    public delegate void NewSceneUIInputEventHandler(NewSceneUI Sender);

    public class NewSceneUI : DialogUI
    {
        ScrollView _ScrollView;
        Panel _ScrollPanel;

        Inspector _Inspector;
        InspectorHandler _InspectorHandler;
        WorldPartitionConfiguration _Setting = new WorldPartitionConfiguration();

        public event NewSceneUIInputEventHandler InputedEvent;

        public void Initialize(UIManager UIManager, string Title)
        {
            base.Initialize(UIManager, Title, 650, 250);

            _Setting.mMapSize = new Float2(80000, 80000);
            _Setting.mBlockSize = new Float3(80000, 1, 80000);
            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.GetHScroll().SetEnable(false);
            _PanelDialog.AddChild(_ScrollView);
            _ScrollView.SetPosition(10, 50, 630, 430);

            _ScrollPanel = _ScrollView.GetScrollPanel();

            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_Setting); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            _Inspector = new Inspector_Struct_With_Property();
            _Inspector.InspectObject(_Setting);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);
            UpdateLayout();

            Button ButtonOK = new Button();
            ButtonOK.Initialize();
            ButtonOK.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonOK.SetText("OK");
            ButtonOK.SetFontSize(16);
            ButtonOK.SetTextOffsetY(3);
            ButtonOK.ClickedEvent += OnButtonOKClicked;
            _PanelDialog.AddChild(ButtonOK);

            ButtonOK.SetSize(100, 24);
            ButtonOK.MakeCenterX();
            ButtonOK.SetY(200);
        }

        public void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }

        public override void ShowDialog()
        {
            base.ShowDialog();
        }

        public override void OnDeviceChar(Device Sender, char Char)
        {
            base.OnDeviceChar(Sender, Char);

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bNone = !bControl && !bShift && !bAlt;

            if (bNone && (Char == '\r' || Char == '\n'))
            {
                OnButtonOKClicked(null);
            }
        }

        void OnButtonOKClicked(Button Sender)
        {
            CrossEngineApi.SetWorldPartitionConfig(EditorScene.GetInstance().GetWorld().GetNativePointer(), _Setting);
            CloseDialog();
            if (InputedEvent != null)
            {
                InputedEvent(this);
            }
        }
    }

}