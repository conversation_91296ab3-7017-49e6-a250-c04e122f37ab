using CEngine;
using EditorUI;

namespace CrossEditor
{
    class GlobalRotator : ManipulatorBase
    {
        public GlobalRotator(ManipulatorHelper Owner) : base(Owner)
        {
        }

        public override void OnMouseDown(RayPickResult RayPickResult, int MouseX, int MouseY)
        {
            base.OnMouseDown(RayPickResult, MouseX, MouseY);

            Matrix4x4d TargetWorldMatrix = _Owner.GetTargetWorldMatrix();

            Vector3d Center = new Vector3d();
            TargetWorldMatrix.GetPosition(out Center);
            Vector3d Origin = new Vector3d(0.0f, 0.0f, 0.0f);
            Vector3d Unit1 = new Vector3d(1.0f, 0.0f, 0.0f);
            Vector3d Unit2 = new Vector3d(1.0f, 0.0f, 0.0f);
            Vector3d HitPoint = _HitPoint;
            if (_HitTag == GeometryTag.RotatorInner)
            {
                Origin = HitPoint;
                {
                    Vector3d Unit = new Vector3d(1.0f, 0.0f, 0.0f);
                    Unit.Normalize();
                    Vector3d Diff = HitPoint - Center;
                    Diff.Normalize();
                    Vector3d Dir = Vector3d.CrossProduct(ref Diff, ref Unit);
                    Dir.Normalize();
                    Unit1 = HitPoint - Dir;
                }
                {
                    Vector3d Unit = new Vector3d(0.0f, 1.0f, 0.0f);
                    Unit.Normalize();
                    Vector3d Diff = HitPoint - Center;
                    Diff.Normalize();
                    Vector3d Dir = Vector3d.CrossProduct(ref Diff, ref Unit);
                    Dir.Normalize();
                    Unit2 = HitPoint - Dir;
                }
            }
            else if (_HitTag == GeometryTag.RotatorOutter)
            {
                Transform CameraEntityTransform = _Owner.GetCameraEntity().GetTransformComponent();
                Matrix4x4d CameraWorldMatrix = new Matrix4x4d();
                CameraEntityTransform.GetWorldMatrix(ref CameraWorldMatrix);
                CameraWorldMatrix.SetPosition(0.0f, 0.0f, 0.0f);

                Origin = HitPoint;
                Vector3d Unit = new Vector3d(0.0f, 0.0f, 1.0f);
                CameraWorldMatrix.TransformNormal(ref Unit);
                Unit.Normalize();
                Vector3d Diff = HitPoint - Center;
                Diff.Normalize();
                Vector3d Dir = Vector3d.CrossProduct(ref Diff, ref Unit);
                Dir.Normalize();
                Unit1 = HitPoint - Dir;
            }
            else if (_HitTag == GeometryTag.RotatorX)
            {
                Origin = HitPoint;
                Vector3d Unit = new Vector3d(1.0f, 0.0f, 0.0f);
                Unit.Normalize();
                Vector3d Diff = HitPoint - Center;
                Diff.Normalize();
                Vector3d Dir = Vector3d.CrossProduct(ref Diff, ref Unit);
                Dir.Normalize();
                Unit1 = HitPoint - Dir;
            }
            else if (_HitTag == GeometryTag.RotatorY)
            {
                Origin = HitPoint;
                Vector3d Unit = new Vector3d(0.0f, 1.0f, 0.0f);
                Unit.Normalize();
                Vector3d Diff = HitPoint - Center;
                Diff.Normalize();
                Vector3d Dir = Vector3d.CrossProduct(ref Diff, ref Unit);
                Dir.Normalize();
                Unit1 = HitPoint - Dir;
            }
            else if (_HitTag == GeometryTag.RotatorZ)
            {
                Origin = HitPoint;
                Vector3d Unit = new Vector3d(0.0f, 0.0f, 1.0f);
                Unit.Normalize();
                Vector3d Diff = HitPoint - Center;
                Diff.Normalize();
                Vector3d Dir = Vector3d.CrossProduct(ref Diff, ref Unit);
                Dir.Normalize();
                Unit1 = HitPoint - Dir;
            }
            Vector3d OldTranslation = _Owner._OldTranslation;
            Origin += OldTranslation;
            Unit1 += OldTranslation;
            Unit2 += OldTranslation;
            _Manipulator1 = CalculateManipulator_Global(Origin, Unit1);
            _Manipulator2 = CalculateManipulator_Global(Origin, Unit2);
        }

        public override void OnMouseMove(int MouseX, int MouseY)
        {
            base.OnMouseMove(MouseX, MouseY);
            if (_MouseDown == false)
            {
                return;
            }

            int DeltaMouseX = MouseX - _SavedMouseX;
            int DeltaMouseY = MouseY - _SavedMouseY;
            double DistanceMoved1 = _Manipulator1.X * DeltaMouseX + _Manipulator1.Y * DeltaMouseY;
            double DistanceMoved2 = _Manipulator2.X * DeltaMouseX + _Manipulator2.Y * DeltaMouseY;
            double RotationScale = 0.008;
            double Rotation1 = DistanceMoved1 * RotationScale;
            double Rotation2 = DistanceMoved2 * RotationScale;

            double RotationStep = _Owner.GetRotationStep();
            if (RotationStep != 0)
            {
                Rotation1 = MathHelper.RadiansToDegree(Rotation1);
                Rotation2 = MathHelper.RadiansToDegree(Rotation2);

                Rotation1 /= RotationStep;
                Rotation1 = (int)Rotation1;
                Rotation1 *= RotationStep;

                Rotation2 /= RotationStep;
                Rotation2 = (int)Rotation2;
                Rotation2 *= RotationStep;

                Rotation1 = MathHelper.DegreeToRadians(Rotation1);
                Rotation2 = MathHelper.DegreeToRadians(Rotation2);
            }

            Quaterniond DeltaRotationQuaternion = Quaterniond.FromAxisRotation(new Vector3d(1.0f, 0.0f, 0.0f), 0.0f);
            if (_HitTag == GeometryTag.RotatorInner)
            {
                Vector3d XAxis = new Vector3d(1.0f, 0.0f, 0.0f);
                XAxis.Normalize();
                Vector3d YAxis = new Vector3d(0.0f, 1.0f, 0.0f);
                YAxis.Normalize();
                Quaterniond DeltaRotationQuaternion1 = Quaterniond.FromAxisRotation(XAxis, Rotation1);
                DeltaRotationQuaternion1.Normalize();
                Quaterniond DeltaRotationQuaternion2 = Quaterniond.FromAxisRotation(YAxis, Rotation2);
                DeltaRotationQuaternion2.Normalize();
                DeltaRotationQuaternion = Quaterniond.Concatenate(DeltaRotationQuaternion1, DeltaRotationQuaternion2);
            }
            else if (_HitTag == GeometryTag.RotatorOutter)
            {
                Transform CameraEntityTransform = _Owner.GetCameraEntity().GetTransformComponent();
                Matrix4x4d CameraWorldMatrix = new Matrix4x4d();
                CameraEntityTransform.GetWorldMatrix(ref CameraWorldMatrix);
                CameraWorldMatrix.SetPosition(0.0f, 0.0f, 0.0f);

                Vector3d CameraAxis = new Vector3d(0.0f, 0.0f, 1.0f);
                CameraWorldMatrix.TransformNormal(ref CameraAxis);
                CameraAxis.Normalize();
                DeltaRotationQuaternion = Quaterniond.FromAxisRotation(CameraAxis, Rotation1);
            }
            else if (_HitTag == GeometryTag.RotatorX)
            {
                Vector3d XAxis = new Vector3d(1.0f, 0.0f, 0.0f);
                XAxis.Normalize();
                DeltaRotationQuaternion = Quaterniond.FromAxisRotation(XAxis, Rotation1);
            }
            else if (_HitTag == GeometryTag.RotatorY)
            {
                Vector3d YAxis = new Vector3d(0.0f, 1.0f, 0.0f);
                YAxis.Normalize();
                DeltaRotationQuaternion = Quaterniond.FromAxisRotation(YAxis, Rotation1);
            }
            else if (_HitTag == GeometryTag.RotatorZ)
            {
                Vector3d ZAxis = new Vector3d(0.0f, 0.0f, 1.0f);
                ZAxis.Normalize();
                DeltaRotationQuaternion = Quaterniond.FromAxisRotation(ZAxis, Rotation1);
            }
            DeltaRotationQuaternion.Normalize();
            _Owner._NewRotation = Quaterniond.Concatenate(_Owner._OldRotation, DeltaRotationQuaternion);
            _Owner._NewRotation.Normalize();
            _Owner.UpdatTargetMatrix();
        }

        public override void OnMouseUp(int MouseX, int MouseY)
        {
            base.OnMouseUp(MouseX, MouseY);
        }

        public override void AddEditOperation_ModifyProperty(object Object)
        {
            AddEditOperation_ModifyProperty(Object, "Translation", _Owner._OldTranslation_Pivot, _Owner._NewTranslation_Pivot);
            Vector3d OldRotation = Quaterniond.ToEuler(_Owner._OldRotation_Pivot);
            Vector3d NewRotation = Quaterniond.ToEuler(_Owner._NewRotation_Pivot);
            AddEditOperation_ModifyProperty(Object, "Rotation", OldRotation, NewRotation);
        }
    }
}
