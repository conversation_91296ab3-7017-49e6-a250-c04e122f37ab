using CEngine;
using System;

namespace CrossEditor
{
    class LocalTranslator : ManipulatorBase
    {
        Plane _Plane;
        Vector3d _SavedIntersection;
        Vector3d _SavedUpAxis;

        public LocalTranslator(ManipulatorHelper Owner) : base(Owner)
        {
        }

        public override void OnMouseDown(RayPickResult RayPickResult, int MouseX, int MouseY)
        {
            base.OnMouseDown(RayPickResult, MouseX, MouseY);

            Ray Ray = CreateRayByMousePosition(MouseX, MouseY);
            Vector3d Position;
            _Owner.GetSavedWorldMatrix().GetPosition(out Position);
            if (_HitTag == GeometryTag.TranslatorX || _HitTag == GeometryTag.TranslatorY || _HitTag == GeometryTag.TranslatorZ)
            {
                Vector3d Axis = new Vector3d(0, 0, 0);
                if (_HitTag == GeometryTag.TranslatorX) { Axis.X = 1; }
                else if (_HitTag == GeometryTag.TranslatorY) { Axis.Y = 1; }
                else if (_HitTag == GeometryTag.TranslatorZ) { Axis.Z = 1; }

                _Owner.GetSavedWorldMatrix().Transform(ref Axis);
                Axis = Axis - Position;
                Axis.Normalize();
                _Manipulator1 = Axis;

                Camera Camera = (Camera)_Owner.GetCameraEntity().GetComponent(typeof(Camera));
                Vector3d Up = Camera.CalculateUp();
                Vector3d Normal;
                if (Up == Axis)
                {
                    Normal = -Camera.CalculateLookAt();
                }
                else
                {
                    Normal = Vector3d.Cross(Up, Axis);
                    Vector3d Binormal = Vector3d.Cross(Normal, Axis);
                    if (Math.Abs(Vector3d.Dot(Ray.Direction, Normal)) < Math.Abs(Vector3d.Dot(Ray.Direction, Binormal)))
                    {
                        Normal = Binormal;
                    }
                }
                Normal.Normalize();
                _Plane = new Plane(Position, Normal);
            }
            else if (_HitTag == GeometryTag.TranslatorXY || _HitTag == GeometryTag.TranslatorYZ || _HitTag == GeometryTag.TranslatorXZ)
            {
                Vector3d Axis1 = new Vector3d(0, 0, 0);
                Vector3d Axis2 = new Vector3d(0, 0, 0);
                if (_HitTag == GeometryTag.TranslatorXY) { Axis1.X = 1; Axis2.Y = 1; }
                else if (_HitTag == GeometryTag.TranslatorYZ) { Axis1.Y = 1; Axis2.Z = 1; }
                else if (_HitTag == GeometryTag.TranslatorXZ) { Axis1.Z = 1; Axis2.X = 1; }

                _Owner.GetSavedWorldMatrix().Transform(ref Axis1);
                _Owner.GetSavedWorldMatrix().Transform(ref Axis2);
                Axis1 = Axis1 - Position;
                Axis2 = Axis2 - Position;
                Axis1.Normalize();
                Axis2.Normalize();
                _Manipulator1 = Axis1;
                _Manipulator2 = Axis2;

                Vector3d Normal = Vector3d.Cross(Axis1, Axis2);
                Normal.Normalize();
                _Plane = new Plane(Position, Normal);
            }
            else if (_HitTag == GeometryTag.TranslatorSphere)
            {
                Camera Camera = (Camera)_Owner.GetCameraEntity().GetComponent(typeof(Camera));
                Vector3d LookAt = Camera.CalculateLookAt();
                Vector3d Up = Camera.CalculateUp();
                Vector3d CameraAxisX = Vector3d.Cross(Up, LookAt);
                CameraAxisX.Normalize();

                _Manipulator1 = CameraAxisX;
                _Manipulator2 = Up;

                Vector3d Normal = -LookAt;
                _Plane = new Plane(Position, Normal);

                Vector3d YAxisPoint = new Vector3d(0, 1, 0);
                _Owner.GetSavedWorldMatrix().Transform(ref YAxisPoint);
                _SavedUpAxis = YAxisPoint - Position;
                _SavedUpAxis.Normalize();
            }

            LineIntersectPlane(Ray, _Plane, out _SavedIntersection);
        }

        public double CalculateDistanceTargetToCamera()
        {
            Camera Camera = (Camera)_Owner.GetCameraEntity().GetComponent(typeof(Camera));
            Vector3d TargetPosition;

            _Owner.GetSavedWorldMatrix().GetPosition(out TargetPosition);
            Vector3d EyePoint = Camera.CalculateEyePoint();
            Vector3d LookAt = Camera.CalculateLookAt();
            Vector3d VectorDistance = (TargetPosition - EyePoint);
            double Distance = Vector3d.DotProduct(ref VectorDistance, ref LookAt);
            return Distance;
        }

        public Matrix4x4d GetRotationOnlyMatrix(Matrix4x4d Matrix)
        {
            var result = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(Matrix.ToClicrossDouble4x4());
            Vector3d Scaling = new Vector3d(result.scaling.x, result.scaling.y, result.scaling.z);
            Quaterniond Rotation = new Quaterniond(result.rotation.x, result.rotation.y, result.rotation.z, result.rotation.w);
            Vector3d Translation = new Vector3d(result.translation.x, result.translation.y, result.translation.z);
            var Matrix1 = Clicross.math.MatrixUtil.Math_MatrixComposeD(new Clicross.Double3(1.0, 1.0, 1.0), new Clicross.Quaternion64(result.rotation.x, result.rotation.y, result.rotation.z, result.rotation.w), new Clicross.Double3(0.0, 0.0, 0.0));
            result.Dispose();
            return new Matrix4x4d(Matrix1);
        }

        public Matrix4x4d GetRotationOnlyMatrixD(Matrix4x4d Matrix)
        {
            var result = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(Matrix.ToClicrossDouble4x4());
            Vector3d Scaling = new Vector3d(result.scaling.x, result.scaling.y, result.scaling.z);
            Quaterniond Rotation = new Quaterniond(result.rotation.x, result.rotation.y, result.rotation.z, result.rotation.w);
            Vector3d Translation = new Vector3d(result.translation.x, result.translation.y, result.translation.z);
            var Matrix1 = Clicross.math.MatrixUtil.Math_MatrixComposeD(new Clicross.Double3(1.0, 1.0, 1.0), new Clicross.Quaternion64(result.rotation.x, result.rotation.y, result.rotation.z, result.rotation.w), new Clicross.Double3(0.0, 0.0, 0.0));
            result.Dispose();
            return new Matrix4x4d(Matrix1);
        }

        public override void OnMouseMove(int MouseX, int MouseY)
        {
            base.OnMouseMove(MouseX, MouseY);

            if (_MouseDown == false)
            {
                return;
            }

            Ray Ray = CreateRayByMousePosition(MouseX, MouseY);
            Vector3d Intersection;
            if (!LineIntersectPlane(Ray, _Plane, out Intersection))
            {
                return;
            }
            Vector3d Movement = new Vector3d(0, 0, 0);
            Quaterniond Rotation = new Quaterniond(0, 0, 0, 1);

            if (_HitTag == GeometryTag.TranslatorX || _HitTag == GeometryTag.TranslatorY || _HitTag == GeometryTag.TranslatorZ)
            {
                double DistanceMoved = Vector3d.Dot((Intersection - _SavedIntersection), _Manipulator1);
                int TranslationStep = _Owner.GetTranslationStep();
                if (TranslationStep != 0)
                {
                    DistanceMoved = ((int)DistanceMoved) / TranslationStep * TranslationStep;
                }
                Movement = _Manipulator1 * DistanceMoved;
            }
            else if (_HitTag == GeometryTag.TranslatorXY || _HitTag == GeometryTag.TranslatorYZ || _HitTag == GeometryTag.TranslatorXZ || _HitTag == GeometryTag.TranslatorSphere)
            {
                double DistanceMoved1 = Vector3d.Dot((Intersection - _SavedIntersection), _Manipulator1);
                double DistanceMoved2 = Vector3d.Dot((Intersection - _SavedIntersection), _Manipulator2);
                int TranslationStep = _Owner.GetTranslationStep();
                if (TranslationStep != 0)
                {
                    DistanceMoved1 = ((int)DistanceMoved1) / TranslationStep * TranslationStep;
                    DistanceMoved2 = ((int)DistanceMoved2) / TranslationStep * TranslationStep;
                }

                Movement = _Manipulator1 * DistanceMoved1 + _Manipulator2 * DistanceMoved2;
                if (_HitTag == GeometryTag.TranslatorSphere && _Owner.GetSurfaceSnapping())
                {
                    Entity CameraEntity = _Owner.GetCameraEntity();
                    Vector3d Tile = new Vector3d(TransformSystemG.GetWorldTranslationTile(CameraEntity.World.GetNativePointer(), CameraEntity.EntityID));
                    Camera Camera = (Camera)CameraEntity.GetComponent(typeof(Camera));
                    Vector3d RayStart = Camera.CalculateEyePoint() - Tile * LENGTH_PER_TILE;
                    Vector3d SavedPosition;
                    _Owner.GetSavedWorldMatrix().GetPosition(out SavedPosition);
                    Vector3d RayEnd = SavedPosition + Movement - Tile * LENGTH_PER_TILE;

                    RayPickResult Result = _Owner.GetSceneUI().GetScene().RayPick(ref RayStart, ref RayEnd, ref Tile, RayPickFlag.Model | RayPickFlag.Terrain, _Owner.GetRelatedEntities());
                    if (Result.HitEntity != null)
                    {
                        Vector3d TargetPosition = Result.HitPoint + Result.HitFaceNormal * _Owner.GetSurfaceOffset();
                        Movement = TargetPosition - SavedPosition;
                        if (_Owner.GetRotateToNormal())
                        {
                            Rotation = new Quaterniond(Quaternion64.CreateFrom2Vectors(_SavedUpAxis.ToDouble3(), Result.HitFaceNormal.ToDouble3(), new Double3(0, 1, 0)));
                        }
                    }
                }
            }

            Matrix4x4d TranslationMatrix = new Matrix4x4d();
            TranslationMatrix.LoadIdentity();
            TranslationMatrix.SetPosition(Movement.X, Movement.Y, Movement.Z);

            _Owner._TargetWorldMatrix.Mul(ref _Owner._SavedWorldMatrix, ref TranslationMatrix);
            _Owner.UpdateTargetWorldMatrix();

            _Owner._NewRotation = Quaterniond.Concatenate(_Owner._OldRotation, Rotation);
            _Owner._NewRotation.Normalize();
            _Owner.UpdatTargetMatrix();
        }

        public override void OnMouseUp(int MouseX, int MouseY)
        {
            base.OnMouseUp(MouseX, MouseY);
        }

        public override void AddEditOperation_ModifyProperty(object Object)
        {
            AddEditOperation_ModifyProperty(Object, "Translation", _Owner._OldTranslation_Pivot, _Owner._NewTranslation_Pivot);
        }
    }
}
