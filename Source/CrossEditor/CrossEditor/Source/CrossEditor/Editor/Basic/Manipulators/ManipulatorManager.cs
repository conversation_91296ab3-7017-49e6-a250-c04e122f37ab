namespace CrossEditor
{
    class ManipulatorManager
    {
        static ManipulatorManager _Instance = new ManipulatorManager();

        ManipulatorType _ManipulatorType;
        ManipulatorHelper _ManipulatorHelper;

        bool _bGlobal;

        public static ManipulatorManager GetInstance()
        {
            return _Instance;
        }

        ManipulatorManager()
        {
            _ManipulatorType = ManipulatorType.None;
            _bGlobal = false;
        }

        public ManipulatorType GetManipulatorType()
        {
            return _ManipulatorType;
        }

        public ManipulatorHelper GetManipulatorHelper()
        {
            return _ManipulatorHelper;
        }

        public void SetManipulatorHelper(ManipulatorHelper ManipulatorHelper)
        {
            if (_ManipulatorHelper != ManipulatorHelper)
            {
                _ManipulatorType = ManipulatorHelper.GetManipulatorType();
                _ManipulatorHelper = ManipulatorHelper;
                if (_ManipulatorType == ManipulatorType.None)
                {
                    SetManipulatorType(ManipulatorType.LocalTranslator);
                }
            }
        }

        public void SetManipulatorType(ManipulatorType ManipulatorType)
        {
            if (_ManipulatorHelper != null && ManipulatorType != _ManipulatorType)
            {
                _ManipulatorType = ManipulatorType;
                _ManipulatorHelper.SetManipulatorType(ManipulatorType);
                Clicross.GizmoManager.Gizmo_SetManipulatorType((int)ManipulatorType);
            }
        }

        public void SetGlobal(bool bGlobal)
        {
            _bGlobal = bGlobal;
        }

        public bool GetGlobal()
        {
            return _bGlobal;
        }
    }
}
