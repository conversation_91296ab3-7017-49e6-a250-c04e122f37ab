using CEngine;
using EditorUI;
using System;
using System.Reflection;

namespace CrossEditor
{
    class ManipulatorBase : Manipulator
    {
        protected static double LENGTH_PER_TILE = 65536;

        protected struct Ray
        {
            // Definition: L(t) = origin + t * direction
            public Vector3d Origin;
            public Vector3d Direction;

            public Ray(Vector3d Origin, Vector3d Direction)
            {
                this.Origin = Origin;
                this.Direction = Direction;
            }
        }

        protected struct Plane
        {
            public Vector3d Origin;
            // Definition: (normal dot p) + d = 0
            public Vector3d Normal;
            public double d;

            public Plane(Vector3d Origin, Vector3d Normal)
            {
                this.Origin = Origin;
                this.Normal = Normal;
                this.d = -Vector3d.Dot(Origin, Normal);
            }
        }

        protected ManipulatorHelper _Owner;
        protected GeometryTag _HitTag;
        protected Vector3d _HitPoint;
        protected int _SavedMouseX;
        protected int _SavedMouseY;
        protected Vector3d _Manipulator1;
        protected Vector3d _Manipulator2;
        protected bool _MouseDown;

        public ManipulatorBase(ManipulatorHelper Owner)
        {
            _Owner = Owner;
            _SavedMouseX = 0;
            _SavedMouseY = 0;
            _MouseDown = false;
        }

        ~ManipulatorBase()
        {
            _Owner = null;
        }

        public override void OnMouseDown(RayPickResult RayPickResult, int MouseX, int MouseY)
        {
            _HitTag = RayPickResult.HitTag;
            _HitPoint = RayPickResult.HitPoint;
            _SavedMouseX = MouseX;
            _SavedMouseY = MouseY;
            _MouseDown = true;
        }

        public override void OnMouseMove(int MouseX, int MouseY)
        {
        }

        public override void OnMouseUp(int MouseX, int MouseY)
        {
            _MouseDown = false;
        }

        protected Vector3d CalculateManipulator_Local(Vector3d Origin, Vector3d Unit)
        {
            Matrix4x4d TargetWorldMatrix = _Owner.GetTargetWorldMatrix();
            Vector3d Scaling;
            TargetWorldMatrix.GetScale(out Scaling);
            TargetWorldMatrix.SetScale(new Vector3d(1.0, 1.0, 1.0));
            TargetWorldMatrix.Transform(ref Origin);
            TargetWorldMatrix.Transform(ref Unit);
            TargetWorldMatrix.SetScale(Scaling);
            return CalculateManipulator_Global(Origin, Unit);
        }

        protected Vector3d CalculateManipulator_Global(Vector3d Origin, Vector3d Unit)
        {
            double OriginX;
            double OriginY;
            _Owner.GetSceneUI().WorldToScreen(ref Origin, out OriginX, out OriginY);
            double UnitX;
            double UnitY;
            _Owner.GetSceneUI().WorldToScreen(ref Unit, out UnitX, out UnitY);
            Vector3d Manipulator = new Vector3d(UnitX - OriginX, UnitY - OriginY, 0.0);
            Manipulator.Normalize();
            return Manipulator;
        }

        protected void AddEditOperation_ModifyProperty(object Object, string PropertyName, Vector3d OldValue, Vector3d NewValue)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (NewValue != OldValue)
            {
                ObjectProperty ObjectProperty = new ObjectProperty();
                ObjectProperty.Object = Object;
                ObjectProperty.Name = PropertyInfo.Name;
                ObjectProperty.Type = PropertyInfo.PropertyType;
                ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                ObjectProperty.RawSetPropertyValueFunction = SetPropertyValueFunction;

                EditOperation_ModifyProperty EditOperation = new EditOperation_ModifyProperty(ObjectProperty, new Double3(OldValue), new Double3(NewValue), null);
                EditOperationManager.GetInstance().AddOperation(EditOperation);
            }
        }

        protected object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        protected void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
            }
        }

        protected Ray CreateRayByMousePosition(int MouseX, int MouseY)
        {
            Camera Camera = (Camera)_Owner.GetCameraEntity().GetComponent(typeof(Camera));
            Vector3d EyePoint = Camera.CalculateEyePoint();
            Vector3d RayStart, RayEnd;
            _Owner.GetSceneUI().ScreenToWorld(MouseX, MouseY, out RayStart, out RayEnd);
            Vector3d Direction = RayEnd - RayStart;
            Direction.Normalize();
            Ray Ray = new Ray(EyePoint, Direction);
            return Ray;
        }

        protected bool LineIntersectPlane(Ray Ray, Plane Plane, out Vector3d Intersection)
        {
            double vdot = Vector3d.Dot(Ray.Direction, Plane.Normal);
            double ndot = -(Vector3d.Dot(Ray.Origin, Plane.Normal)) - Plane.d;

            if (Math.Abs(vdot) < MathHelper.Epsilon)
            {
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Warning, "Line almost parallel to the plane");
            }

            double t = ndot / vdot;
            Intersection = Ray.Origin + Ray.Direction * t;
            return t > 0;
        }
    }
}
