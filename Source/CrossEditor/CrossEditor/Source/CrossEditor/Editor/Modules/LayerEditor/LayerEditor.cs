using CEngine;

namespace CrossEditor
{
    [DockingMenuAttribute(DefaultDocking = DockingBlockSection.RightInspector)]
    class LayerEditor : InspectorDockingBlockItems<Inspector_LayerEditor>
    {
        const int SPAN_X = 5;

        static LayerEditor _Instance = new LayerEditor();

        public new static LayerEditor GetInstance() { return _Instance; }
        LayerSetting _LayerSetting { get { return _InspectorObject as LayerSetting; } }

        public override void InitializeInspectObject()
        {
            var settingMgr = CrossEngineApi.GetSettingManager();
            _InspectorObject = settingMgr.GetLayerSetting();

        }
    }
}
