using EditorUI;
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

//namespace CrossEditor.Source.CrossEditor.Editor.Modules.VideoGenerator
namespace CrossEditor
{
    class FFmpegWrapper
    {
        const string toolRelativePath = "Editor/Tools/ffmpeg.exe";
        public static void GeneratorVideoFromImages(string imagesDirectory, string imageSaveTypeSuffix, int outputFrameRate)
        {
            string imagesPath = imagesDirectory + "/img%05d" + imageSaveTypeSuffix;
            string outputPath = imagesDirectory + "/out.mp4";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                string inputArgs = " -hwaccel auto -framerate " + outputFrameRate.ToString();
                string inputPath = " -i " + imagesPath;

                //-crf: the lower value, the higher output video quality, range: 0-51 for 8bit videos, typically 18 can be visually lossless;
                //-r: frame rate
                string outputArgs = " -c:v libx264 -preset fast -pix_fmt yuv420p -vf \"scale=trunc(iw/2)*2:trunc(ih/2)*2\" -crf 12  -r " + outputFrameRate.ToString() + " ";

                string command = inputArgs + inputPath + outputArgs + outputPath;
                CommandManager(command);
            }
            else
            {
                EditorUI.CommonDialogUI.ShowSimpleOKDialog(UIManager.GetActiveUIManager(), "Error", string.Format("Currently, only Windows can use ffmpeg!"));
            }
        }

        public static void CommandManager(string cmdArgs)
        {
            try
            {
                using (Process process = new Process())
                {

                    ProcessStartInfo startInfo = new ProcessStartInfo();
                    startInfo.FileName = EditorUI.PathHelper.ToAbsolutePath(toolRelativePath);
                    startInfo.Arguments = cmdArgs;
                    startInfo.UseShellExecute = true;
                    startInfo.Verb = "runas";

                    process.StartInfo = startInfo;
                    bool s = process.Start();
                    process.WaitForExit();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
        }
    }
}
