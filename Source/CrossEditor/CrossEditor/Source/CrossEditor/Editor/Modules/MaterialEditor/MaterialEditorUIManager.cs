using CEngine;
using Clicross;
using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class MaterialEditorUIManager
    {
        MaterialEditorUIManager()
        {
            SceneRuntime.GetInstance().EditorGlobalUpdateEvent += OnEditorGlobalUpdate;
        }

        public static MaterialEditorUIManager Instance { get; } = new MaterialEditorUIManager();

        // key: Guid
        private Dictionary<string, MaterialEditorUI> mMaterialEditors = new Dictionary<string, MaterialEditorUI>();
        // key: Guid
        private Dictionary<string, MaterialInstanceEditorUI> mMaterialInstanceEditors = new Dictionary<string, MaterialInstanceEditorUI>();

        public void OnEditorGlobalUpdate(Device Sender, long TimeElapsed)
        {
            if (mDelayOpenResourceGuid != null)
            {
                OpenMaterial(mDelayOpenResourceGuid, mDelayOpenResourceType);
                mDelayOpenResourceGuid = null;
            }
        }

        public void Update(long TimeElapsed)
        {
            foreach (var item in mMaterialEditors.Values)
            {
                item.Update(TimeElapsed);
            }

            foreach (var item in mMaterialInstanceEditors.Values)
            {
                item.Update(TimeElapsed);
            }
        }

        public void OpenMaterial(string filePath, CEngine.ClassIDType type)
        {
            UpdateMaterialFunctionList();

            var guid = ResourceManager.Instance().ConvertPathToGuid(filePath);
            bool isFxOrMaterialFunction = type == CEngine.ClassIDType.CLASS_Fx || type == CEngine.ClassIDType.CLASS_MaterialFunction;

            DockingCard dockingCard = null;
            if (isFxOrMaterialFunction)
            {
                bool isTemplate = guid == "111faeaf98b9d4d3bbecaeafb76dfab4";
                if (isTemplate)
                {
                    var messageBox = new MessageBoxUI();
                    messageBox.Initialize(UIManager.GetActiveUIManager(), "Error", "Don't modify default fx, create a new one instead.");
                    DialogUIManager.GetInstance().ShowDialogUI(messageBox);
                    return;
                }

                if (mMaterialEditors.ContainsKey(guid))
                {
                    dockingCard = mMaterialEditors[guid].GetDockingCard();
                }
                else
                {
                    var editor = new MaterialEditorUI(guid, filePath, type);
                    editor.Initialize();
                    dockingCard = editor.GetDockingCard();
                    dockingCard.SetDocument(true);
                    dockingCard.CloseEvent += (DockingCard Sender, ref bool bNotToClose) =>
                    {
                        mMaterialEditors.Remove(guid);
                    };

                    mMaterialEditors.Add(guid, editor);
                }

            }
            else if (!isFxOrMaterialFunction)
            {
                if (mMaterialInstanceEditors.ContainsKey(guid))
                {
                    dockingCard = mMaterialInstanceEditors[guid].GetDockingCard();
                }
                else
                {
                    var editor = new MaterialInstanceEditorUI(guid);
                    editor.Initialize();
                    dockingCard = editor.GetDockingCard();
                    dockingCard.SetDocument(true);
                    dockingCard.CloseEvent += (DockingCard Sender, ref bool bNotToClose) =>
                    {
                        mMaterialInstanceEditors.Remove(guid);
                    };

                    mMaterialInstanceEditors.Add(guid, editor);
                }
            }

            MainUI.GetInstance().ActivateDockingCard_MaterialEditor(dockingCard);
        }

        string mDelayOpenResourceGuid;
        CEngine.ClassIDType mDelayOpenResourceType;

        public void OpenMaterialDelay(string filePath, CEngine.ClassIDType type)
        {
            mDelayOpenResourceGuid = filePath;
            mDelayOpenResourceType = type;
        }

        void UpdateMaterialFunctionList()
        {
            MaterialEditor.ClearMaterialFunctions();

            foreach (var materialFunction in ResourceTypeCache.GetInstance().GetResourceListByType(CEngine.ClassIDType.CLASS_MaterialFunction))
            {
                MaterialEditor.RegisterMaterialFunction(EditorUtilities.EditorFilenameToStandardFilename(materialFunction));
            }
        }

        public void NotifyResourceChange(string resourcePathOrGuid)
        {
            var guid = ResourceManager.Instance().ConvertPathToGuid(resourcePathOrGuid);
            var type = Resource.GetResourceTypeStatic(guid);

            if (type == CEngine.ClassIDType.CLASS_Fx || type == CEngine.ClassIDType.CLASS_MaterialFunction)
            {
                foreach (var (materialGuid, materialEditor) in mMaterialEditors)
                {
                    if (materialGuid == guid)
                    {
                        materialEditor.OnResourceRenamed();
                        break;
                    }
                }
            }

            if (type == CEngine.ClassIDType.CLASS_Fx)
            {
                foreach (var (_, materialInstanceEditor) in mMaterialInstanceEditors)
                {
                    materialInstanceEditor.OnMaterialChange(guid);
                }
            }

            if (type == CEngine.ClassIDType.CLASS_Material)
            {
                if (mMaterialInstanceEditors.TryGetValue(guid, out MaterialInstanceEditorUI currentEditor))
                {
                    currentEditor.OnResourceRenamed();
                    if (currentEditor.IsParentChanged())
                    {
                        foreach (var (materialInstanceGuid, materialInstanceEditor) in mMaterialInstanceEditors)
                        {
                            materialInstanceEditor.OnParentChange(guid);
                        }
                        currentEditor.SetParentModifiedStatus(false);
                    }
                }
            }

            if (type == CEngine.ClassIDType.CLASS_MaterialFunction)
            {
                foreach (var materialEditor in mMaterialEditors)
                {
                    materialEditor.Value.OnMaterialFunctionChange(guid);
                }
            }

            if (type == CEngine.ClassIDType.CLASS_MaterialParameterCollection)
            {
                foreach (var materialEditor in mMaterialEditors)
                {
                    materialEditor.Value.OnMaterialParameterCollectionChange(guid);
                }
            }

            UpdateMaterialFunctionList();
        }

        public void NotifyMaterialParameterCollectionSelectChange()
        {
            foreach (var materialEditor in mMaterialEditors)
            {
                materialEditor.Value.OnMaterialParameterCollectionSelectChange();
            }
        }

        private string mCopyExpressionsStr;

        public void SetCopyExpressionsStr(string str)
        {
            mCopyExpressionsStr = str;
        }

        public string GetCopyExpressionsStr()
        {
            return mCopyExpressionsStr;
        }
    }
}