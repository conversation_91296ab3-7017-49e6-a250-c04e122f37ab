using EditorUI;

namespace CrossEditor
{
    public class Stats : VContainer
    {
        LogView mLogView;

        public override void Initialize()
        {
            mLogView = new LogView();
            mLogView.Initialize();
            AddFixedChild(mLogView);
        }

        public void AddWarningMessage(string msg)
        {
            mLogView.AddLogViewItem(new LogViewItem(LogMessageType.Warning, msg));
        }

        public void AddErrorMessage(string msg)
        {
            mLogView.AddLogViewItem(new LogViewItem(LogMessageType.Error, msg));
        }

        public void ClearMessages()
        {
            mLogView.ClearLogViewItems();
        }

        public void AddInformationMessage(string msg)
        {
            mLogView.AddLogViewItem(new LogViewItem(LogMessageType.Information, msg));
        }
    }
}