using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class MaterialInstanceDetailView : VContainer
    {
        MaterialInstanceEditor mMaterialInstanceEditor;

        OperationBarUI mOperationBarUI = new OperationBarUI();
        SearchUI mSearchUI = new SearchUI();
        ScrollView mScrollView = new ScrollView();
        Panel mScrollPanel;
        object mObjectInspected;
        object mObjectTag;
        Inspector mInspector;
        InspectorHandler mInspectorHandler = new InspectorHandler();

        public MaterialInstanceDetailView(MaterialInstanceEditor editor)
        {
            mMaterialInstanceEditor = editor;

            mOperationBarUI.Initialize();
            mOperationBarUI.GetPanelBar().SetText("Details");
            mOperationBarUI.GetPanelBar().SetTextAlign(TextAlign.CenterLeft);
            mOperationBarUI.GetPanelBar().SetFontSize(12);
            mOperationBarUI.GetPanelBar().SetTextOffsetX(6);

            mSearchUI.Initialize();
            mSearchUI.SearchEvent += OnSearchUISearch;
            mSearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = mSearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 130, 20);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            mOperationBarUI.AddRight(PanelBack);

            mScrollView.Initialize();
            mScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            mScrollView.GetHScroll().SetEnable(false);

            mScrollPanel = mScrollView.GetScrollPanel();
            mScrollPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            mScrollPanel.PaintEvent += (Sender) =>
            {
                SetChildInnerEnable(Sender);
                Sender.PaintThis();
                Sender.PaintChildren();
            };

            mInspectorHandler.InspectObject = DoInspectObject;
            mInspectorHandler.UpdateLayout = DoUpdateLayout;
            mInspectorHandler.ReadValue = DoReadValue;

            if (mMaterialInstanceEditor.HasVirtualTextureMismatch())
            {
                var messageBox = new MessageBoxUI();
                messageBox.Initialize(UIManager.GetActiveUIManager(), "Warning", "A virtual texture parameter mismatch was detected. Please verify VT slots use VT textures and non-VT slots use regular textures.");
                DialogUIManager.GetInstance().ShowDialogUI(messageBox);
            }
        }

        public void OnAddToParent(VContainer mContainer)
        {
            mContainer.AddFixedChild(mOperationBarUI.GetPanelBar());
            mContainer.AddSizableChild(Control, 1);
        }

        public void InspectObject()
        {
            mInspectorHandler.InspectObject();
            mInspectorHandler.UpdateLayout();
        }

        private void SetChildInnerEnable(Control Control)
        {
            for (int i = 0; i < Control.GetChildCount(); ++i)
            {
                Control Child = Control.GetChild(i);
                if (UIManager.RectInRect(Child.GetScreenX(), Child.GetScreenY(), Child.GetWidth(), Child.GetHeight(),
                    mScrollView.GetScreenX(), mScrollView.GetScreenY(), mScrollView.GetWidth(), mScrollView.GetHeight()))
                {
                    Child.SetInnerEnable(true);

                    if (Child.GetChildCount() != 0)
                    {
                        SetChildInnerEnable(Child);
                    }
                }
                else
                {
                    Child.SetInnerEnable(false);
                }
            }
        }

        void OnPropertyChange(object PropertyOwner, PropertyInfo Property)
        {
            mMaterialInstanceEditor.OnPropertyChange();
        }

        public void Tick()
        {
            // load mDefines
            Inspect(mMaterialInstanceEditor.GetMaterialInstanceDefines());

            UpdateLayout();
        }

        private void DoInspectObject()
        {
            mScrollPanel.ClearChildren();
            if (mObjectInspected != null)
            {
                mInspector = new Inspector_MaterialInstanceDefines();
                mInspector.SetPropertyModifiedFunction(OnPropertyChange);
                mInspector.SetContainer(mScrollPanel);
                mInspector.SetInspectorHandler(mInspectorHandler);
                mInspector.InspectObject(mObjectInspected, mObjectTag);
                mInspector.UpdateCheckExpand();

                UpdateLayout();
            }
        }

        private void DoUpdateLayout()
        {
            int ScrollPanelWidth = mScrollView.GetWidth();
            int Y = 0;
            if (mInspector != null && mObjectInspected != null)
            {
                mInspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > mScrollView.GetHeight())
                {
                    ScrollPanelWidth = mScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    mInspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            mScrollPanel.SetSize(ScrollPanelWidth, Height);
            mScrollView.UpdateScrollBar();
        }

        private void UpdateLayout()
        {
            mInspectorHandler.UpdateLayout();
        }

        private void DoReadValue()
        {
            if (mInspector != null)
            {
                mInspector.ReadValue();
            }
        }

        public void Inspect(object ObjectInspected, Object Tag = null)
        {
            if (ObjectInspected == null)
            {
                return;
            }

            if (mObjectInspected != null && mObjectInspected.Equals(ObjectInspected))
            {
                return;
            }

            SetObjectInspected(ObjectInspected);
            SetObjectTag(Tag);
            InspectObject();
        }

        public void SetObjectInspected(object ObjectInspected)
        {
            mObjectInspected = ObjectInspected;
            if (mInspector != null)
            {
                mInspector.WriteValue();
            }

            if (mObjectInspected == null)
            {
                mInspector = null;
            }
        }

        public void SetObjectTag(Object Tag)
        {
            mObjectTag = Tag;
        }

        public void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            if (mInspector != null)
            {
                List<Inspector> SearchResult = new List<Inspector>();
                mInspector.ForEach((Inspector) =>
                {
                    if (Inspector == mInspector)
                    {
                        return;
                    }
                    // Traverse all controls
                    Queue<Control> Controls = new Queue<Control>();
                    Controls.Enqueue(Inspector.GetSelfContainer());
                    while (Controls.Count > 0)
                    {
                        Control Control = Controls.Dequeue();
                        for (int i = 0; i < Control.GetChildCount(); ++i)
                        {
                            Controls.Enqueue(Control.GetChild(i));
                        }
                        // Try to match label text
                        if (Control is Label)
                        {
                            Label Label = Control as Label;
                            if (StringHelper.IgnoreCaseContains(Label.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                        // Try to match edit text
                        else if (Control is Edit)
                        {
                            Edit Edit = Control as Edit;
                            if (StringHelper.IgnoreCaseContains(Edit.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                    }
                });
                // Hide all inspectors
                mInspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(false);
                });
                // Show search result
                foreach (Inspector Result in SearchResult)
                {
                    Inspector This = Result;
                    while (This != null)
                    {
                        This.SetVisible(true);
                        This = This.GetParentInspector();
                    }

                    Result.ForEach((Inspector) =>
                    {
                        Inspector.SetVisible(true);
                    });
                }
                if (SearchResult.Count == 0)
                {
                    mInspector.SetVisible(true);
                }
                UpdateLayout();
            }
        }

        public void OnSearchUICancel(SearchUI Sender)
        {
            if (mInspector != null)
            {
                mInspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(true);
                });

                UpdateLayout();
            }
        }

        public void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (mScrollView.GetVisible_Recursively() && mScrollView.IsPointIn_Recursively(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (ProjectUI.IsPathesDragging())
                {
                    List<string> PathesDragged = ProjectUI.GetPathesDragged();
                    DropPathOnInspector(mInspector, MouseX, MouseY, PathesDragged);
                }
            }
        }

        bool DropPathOnInspector(Inspector Inspector, int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (Inspector == null)
            {
                return false;
            }
            List<Inspector> ChildInspectors = Inspector.GetChildInspectors();
            foreach (Inspector ChildInspector in ChildInspectors)
            {
                if (DropPathOnInspector(ChildInspector, MouseX, MouseY, PathesDragged))
                {
                    return true;
                }
            }
            return Inspector.OnDropPathes(MouseX, MouseY, PathesDragged);
        }

        public Control Control => mScrollView;
    }
}
