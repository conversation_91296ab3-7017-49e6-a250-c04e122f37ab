using CEngine;
using Clicross;
using EditorUI;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace CrossEditor
{
    public class MaterialEditorUI : DockingUI
    {
        public static string PreviewContextStr = "Preview Context";
        public static string MaterialStr = "Material";

        private static readonly Dictionary<Key, string> ExpressionShortcuts = new Dictionary<Key, string>
        {
            { Key.C, "Comment" },
            { Key.M, "Multiply" },
            { Key.D, "Divide" },
            { Key.A, "Add" },
            { Key.L, "Lerp" },
            { Key.U, "TextureCoordinate" },
            { Key.T, "TextureSample" },
            { Key.S, "ScalarParameter" },
            { Key.Num1, "Constant"},
            { Key.Num2, "Constant2Vector"},
            { Key.Num3, "Constant3Vector"},
            { Key.Num4, "Constant4Vector"},
        };

        string mFileGuid;
        string mFileRelativePath;
        CEngine.ClassIDType mType;

        ImGuiPanel mImGuiPanel;
        MaterialEditor mContext;
        MaterialPreview mPreview;
        public MaterialDetailView mDetailView;
        public MaterialParametersView mParametersView;
        public MaterialEditorSettingView mEditorSettingView;
        public MaterialSearchView mSearchView;

        VContainer mMainContainer = new VContainer();
        Panel mPreviewPanel = new Panel();
        Tab mTab = new Tab();
        Stats mStats = new Stats();

        class GraphOperation : EditOperation
        {
            public MaterialEditorUI Editor { get; set; }

            public override void Undo()
            {
                Editor.mContext?.Undo();
            }

            public override void Redo()
            {
                Editor.mContext?.Redo();
            }
        }

        public class NativeListener : MaterialEditorCallback
        {
            //menu
            List<ExpressionCreateGroupInfo> menuInfoList;
            ExpressionCreateMenuInfo menuCategoryInfo;
            public MaterialEditorUI Editor { get; set; }

            public override void OnOperation()
            {
                EditOperationManager.GetInstance().AddOperation(new GraphOperation { Editor = Editor });
            }

            public void CreateTreeByStringArray(string keyword, List<ExpressionCreateGroupInfo> groupInfos, Tree nodeTree, bool firstCreate)
            {
                TreeItem treeRoot = nodeTree.GetRootItem();
                treeRoot.ClearChildren();
                for (int index = 0; index < groupInfos.Count; index++)
                {
                    string categoryName = groupInfos.ElementAt(index).categoryName;
                    TreeItem treeItem = nodeTree.CreateItem();
                    treeRoot.AddChild(treeItem);
                    treeItem.SetFolder(true);
                    if (firstCreate)
                        treeItem.SetExpanded(false);
                    else
                        treeItem.SetExpanded(true);
                    treeItem.SetText(categoryName);
                    for (int i = 0; i < groupInfos.ElementAt(index).ExpressionInfos.Count; i++)
                    {
                        ExpressionCreateNodeInfo expressionCreateNodeInfo = groupInfos.ElementAt(index).ExpressionInfos.ElementAt(i);
                        TreeItem leafTreeItem = nodeTree.CreateItem();
                        treeItem.AddChild(leafTreeItem);
                        leafTreeItem.SetFolder(true);
                        leafTreeItem.SetExpanded(true);
                        leafTreeItem.SetText(expressionCreateNodeInfo.menuName);
                        leafTreeItem.SetTagString(index.ToString() + "|" + i.ToString());
                    }
                }

                if (keyword != "")
                {
                    TreeItems treeItemAll = new TreeItems();
                    treeItemAll.ClearItems();
                    treeItemAll.CollectVisibleItems(nodeTree.GetRootItem());
                    TreeItem treeItemSelected = nodeTree.GetRootItem();
                    for (int i = 0; i < treeItemAll.GetItemCount(); i++)
                    {
                        TreeItem treeItemInList = treeItemAll.GetItem(i);
                        if (treeItemInList.GetChildCount() == 0)
                        {
                            if (treeItemSelected == nodeTree.GetRootItem())
                            {
                                treeItemSelected = treeItemInList;
                            }
                            else
                            {
                                if (treeItemInList.GetText().IndexOf(keyword) < treeItemSelected.GetText().IndexOf(keyword))
                                {
                                    treeItemSelected = treeItemInList;
                                }
                            }
                        }
                    }
                    nodeTree.SelectItemNoEvent(treeItemSelected);
                }
            }

            public List<ExpressionCreateGroupInfo> ModifyMenuInfoByKeyword(List<ExpressionCreateGroupInfo> menuInfo, string keyword)
            {
                if (keyword == "")
                {
                    return menuInfo;
                }
                List<ExpressionCreateGroupInfo> menuInfoModified = new List<ExpressionCreateGroupInfo>();
                foreach (ExpressionCreateGroupInfo groupInfo in menuInfo)
                {
                    ExpressionCreateGroupInfo expressionCreateGroupInfo = new ExpressionCreateGroupInfo();
                    expressionCreateGroupInfo.categoryName = groupInfo.categoryName;
                    List<ExpressionCreateNodeInfo> expressionCreateNodeInfo = groupInfo.ExpressionInfos.ToList();
                    foreach (ExpressionCreateNodeInfo item in expressionCreateNodeInfo)
                    {
                        if (item.menuName.ToLower().Contains(keyword.ToLower()))
                        {
                            expressionCreateGroupInfo.ExpressionInfos.Add(item);
                        }
                    }
                    if (expressionCreateGroupInfo.ExpressionInfos.Count > 0)
                    {
                        menuInfoModified.Add(expressionCreateGroupInfo);
                    }
                }
                return menuInfoModified;
            }

            public override void OnCreateMenuAtPosition(int position_x, int position_y, ExpressionCreateMenuInfo menuCategories)
            {
                Menu MenuContextMenu = new Menu(Editor.GetUIManager());
                MenuItem testMenuItem = new MenuItem();
                Panel Container = new Panel();
                Tree nodeTree = new Tree();
                Edit editBox = new Edit();
                editBox.SetUIManager_Recursively(Editor.GetUIManager());
                //prevent memory crash 
                menuCategoryInfo = new ExpressionCreateMenuInfo(menuCategories);
                menuInfoList = menuCategoryInfo.Groups.ToList();
                menuInfoList.Sort((x, y) => x.categoryName.CompareTo(y.categoryName));
                MenuContextMenu.Initialize();
                nodeTree.Initialize();
                nodeTree.SetTextAlign(TextAlign.TopLeft);
                nodeTree.GetRootItem().SetExpanded(true);
                nodeTree.SetPosition(2, 2, 300, 400);
                nodeTree.ItemSelectedEvent += (Sender, TreeItem) =>
                {
                    if (TreeItem.GetParent() != nodeTree.GetRootItem())
                    {
                        if (TreeItem.GetText() != "")
                        {
                            string selectItemInfo = TreeItem.GetTagString();
                            int groupIndex = int.Parse(selectItemInfo.Split('|')[0]);
                            int expressionIndex = int.Parse(selectItemInfo.Split('|')[1]);
                            List<ExpressionCreateGroupInfo> currentMenuInfoList = editBox.GetText() != "" ? ModifyMenuInfoByKeyword(menuInfoList, editBox.GetText()) : menuInfoList;
                            ExpressionCreateNodeInfo expressionCreateNodeInfo = currentMenuInfoList.ElementAt(groupIndex).ExpressionInfos.ElementAt(expressionIndex);
                            Editor.mContext.CreateMaterialExpression(expressionCreateNodeInfo, position_x, position_y);
                            Editor.GetUIManager().GetContextMenu().HideMenu();
                        }
                    }
                };

                nodeTree.KeyDownEvent += (Control Sender, Key Key, ref bool bContinue) =>
                {
                    bContinue = false;
                    if (Key == Key.Up)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        TreeItem rootItem = nodeTree.GetRootItem();
                        TreeItems treeItems = new TreeItems();
                        treeItems.ClearItems();
                        if (editBox.GetText() == "")
                            treeItems.CollectVisibleItems(rootItem);
                        else
                        {
                            TreeItems treeItemAll = new TreeItems();
                            treeItemAll.ClearItems();
                            treeItemAll.CollectVisibleItems(rootItem);
                            for (int i = 0; i < treeItemAll.GetItemCount(); i++)
                            {
                                TreeItem tempTreeItem = treeItemAll.GetItem(i);
                                if (tempTreeItem != nodeTree.GetRootItem() && tempTreeItem.GetParent() != nodeTree.GetRootItem())
                                {
                                    treeItems.CollectItems(tempTreeItem);
                                }
                            }
                        }
                        int num = treeItems.FindItem(selectedItem);
                        if (num > 0)
                        {
                            TreeItem item = treeItems.GetItem(num - 1);
                            nodeTree.SelectItemNoEvent(item);
                        }
                    }
                    else if (Key == Key.Down)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        TreeItem rootItem = nodeTree.GetRootItem();
                        TreeItems treeItems = new TreeItems();
                        treeItems.ClearItems();
                        if (editBox.GetText() == "")
                            treeItems.CollectVisibleItems(rootItem);
                        else
                        {
                            TreeItems treeItemAll = new TreeItems();
                            treeItemAll.ClearItems();
                            treeItemAll.CollectVisibleItems(rootItem);
                            for (int i = 0; i < treeItemAll.GetItemCount(); i++)
                            {
                                TreeItem tempTreeItem = treeItemAll.GetItem(i);
                                if (tempTreeItem != nodeTree.GetRootItem() && tempTreeItem.GetParent() != nodeTree.GetRootItem())
                                {
                                    treeItems.CollectItems(tempTreeItem);
                                }
                            }
                        }
                        int num = treeItems.FindItem(selectedItem);
                        if (num < treeItems.GetItemCount() - 1)
                        {
                            TreeItem item = treeItems.GetItem(num + 1);
                            nodeTree.SelectItemNoEvent(item);
                        }
                    }
                    else if (Key == Key.Enter)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        nodeTree.SelectItem(selectedItem);
                    }
                    else if (Key == Key.Left)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        if (selectedItem.GetExpanded() && selectedItem != nodeTree.GetRootItem())
                        {
                            selectedItem.SetExpanded(false);
                        }
                    }
                    else if (Key == Key.Right)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        if (!selectedItem.GetExpanded() && selectedItem != nodeTree.GetRootItem())
                        {
                            selectedItem.SetExpanded(true);
                        }
                    }
                };
                nodeTree.KeyUpEvent += (Control Sender, Key Key, ref bool bContinue) =>
                {
                    if (Key == Key.Down)
                    {
                        bContinue = false;
                    }
                };
                int FontSize = 16;
                CreateTreeByStringArray("", menuInfoList, nodeTree, true);
                Container.AddChild(nodeTree);
                editBox.SetBackgroundColor(Color.FromRGBA(30, 50, 76, 255));
                editBox.SetFontSize(FontSize);
                editBox.Initialize(EditMode.Simple_SingleLine);
                editBox.SetWidth(300);
                editBox.SetHeight(FontSize + 4);
                editBox.SetPos(2, 2);
                editBox.SetFocus();
                editBox.KeyDownEvent += (Control Sender, Key Key, ref bool bContinue) =>
                {
                    if (Key == Key.Left || Key == Key.Right || Key == Key.Down || Key == Key.Up || Key == Key.Enter)
                    {
                        bool keyDownContinue = false;
                        nodeTree.OnKeyDown(Key, ref keyDownContinue);
                    }
                };
                editBox.TextChangedEvent += (Sender) =>
                {
                    if (editBox.GetText() == "")
                        CreateTreeByStringArray("", menuInfoList, nodeTree, true);
                    else
                        CreateTreeByStringArray(editBox.GetText(), ModifyMenuInfoByKeyword(menuInfoList, editBox.GetText()), nodeTree, false);
                };
                Container.AddChild(editBox);
                Container.SetSize(304, 400);
                testMenuItem.SetControl(Container);
                MenuContextMenu.AddMenuItem(testMenuItem);
                Editor.GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, position_x + Editor.mImGuiPanel.Panel.GetScreenX(), position_y + Editor.mImGuiPanel.Panel.GetScreenY());
            }

            public override void OnSetCursor(int cursor)
            {
                switch (cursor)
                {
                    case 0: Editor.mImGuiPanel.Cursor = SystemCursor.Arrow; break;
                    case 1: Editor.mImGuiPanel.Cursor = SystemCursor.Edit; break;
                    case 2: Editor.mImGuiPanel.Cursor = SystemCursor.Dragging; break;
                    case 3: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNS; break;
                    case 4: Editor.mImGuiPanel.Cursor = SystemCursor.SizeWE; break;
                    case 5: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNESW; break;
                    case 6: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNWSE; break;
                    case 7: Editor.mImGuiPanel.Cursor = SystemCursor.Hand; break;
                    default: break;
                }
            }

            public override void OnSetInputScreenPosition(int x, int y)
            {
                Editor.GetUIManager().GetDevice().SetCaret(true, x + Editor.mImGuiPanel.Panel.GetScreenX(), y + Editor.mImGuiPanel.Panel.GetScreenY(), 1, 1);
            }

            public override void OnOpenMaterialFunction(string materialFunctionGuid)
            {
                MaterialEditorUIManager.Instance.OpenMaterialDelay(materialFunctionGuid, CEngine.ClassIDType.CLASS_MaterialFunction);
            }

            public override void OnActiveDetailTab()
            {
                Editor.ActiveDetailTab();
            }

            public override void OnReInspect()
            {
                Editor.mDetailView.InspectObject();
                Editor.mParametersView.InspectObject();
                Editor.mEditorSettingView.InspectObject();
            }

            public override void AddWarningMessage(string msg)
            {
                Editor.mStats.AddWarningMessage(msg);
            }

            public override void AddErrorMessage(string msg)
            {
                Editor.mStats.AddErrorMessage(msg);
            }

            public void AddInformationMessage(string msg)
            {
                Editor.mStats.AddInformationMessage(msg);
            }

            public override void ClearMessages()
            {
                Editor.mStats.ClearMessages();
            }

            public override void PassCopyExpressionsToGlobal(string str)
            {
                MaterialEditorUIManager.Instance.SetCopyExpressionsStr(str);
            }
        }

        NativeListener mListener;

        public MaterialEditorUI(string fileGuid, string relvativePath, CEngine.ClassIDType type)
        {
            mFileGuid = fileGuid;
            mFileRelativePath = relvativePath;
            mListener = new NativeListener { Editor = this };
            mContext = new MaterialEditor(fileGuid, mListener);
            mImGuiPanel = new ImGuiPanel(mContext);
            mPreview = new MaterialPreview(mContext);
            mDetailView = new MaterialDetailView(mContext, type);
            mDetailView.Initialize();
            mParametersView = new MaterialParametersView(mContext);
            mParametersView.Initialize();
            mEditorSettingView = new MaterialEditorSettingView(mPreview);
            mEditorSettingView.Initialize();
            mSearchView = new MaterialSearchView(mContext);
            mSearchView.Initialize();
            mType = type;
        }

        public void Initialize()
        {
            mMainContainer.Initialize();
            mMainContainer.SetSize(1, 1);

            // OperationBar
            {
                OperationBarUI operationBar = new OperationBarUI();
                operationBar.Initialize();

                var saveBtn = OperationBarUI.CreateTextButton("Save");
                saveBtn.ClickedEvent += sender =>
                {
                    _ = SaveFxOrMaterialFunction();
                };
                operationBar.AddLeft(saveBtn);

                var saveBtnForce = OperationBarUI.CreateTextButton("SaveForce");
                saveBtnForce.ClickedEvent += sender =>
                {
                    _ = SaveFxOrMaterialFunction(true);
                };
                operationBar.AddLeft(saveBtnForce);

                var browseBtn = OperationBarUI.CreateTextButton("Browse");
                browseBtn.ClickedEvent += sender =>
                {
                    string SPath = ResourceManager.Instance().ConvertGuidToPath(mFileGuid);
                    string Path1 = EditorUtilities.StandardFilenameToEditorFilename(SPath);
                    ProjectUI.GetInstance().JumpToPath(Path1);
                    MainUI.GetInstance().ActivateDockingCard_Project();
                };
                operationBar.AddLeft(browseBtn);

                if (mType == CEngine.ClassIDType.CLASS_Fx)
                {
                    Button HLSLCodeBtn = OperationBarUI.CreateTextButton("HLSLCode");
                    HLSLCodeBtn.ClickedEvent += sender =>
                    {
                        ShowHLSLCode(mContext.HLSLCode());
                    };
                    operationBar.AddLeft(HLSLCodeBtn);

                    Button HomeBtn = OperationBarUI.CreateTextButton("Home");
                    HomeBtn.ClickedEvent += sender =>
                    {
                        mContext.ZoomToSurfaceDataExpression();
                    };

                    operationBar.AddLeft(HomeBtn);
                }
                else if (mType == CEngine.ClassIDType.CLASS_MaterialFunction)
                {
                    Button HomeBtn = OperationBarUI.CreateTextButton("Home");
                    HomeBtn.ClickedEvent += sender =>
                    {
                        mContext.ZoomToFunctionOutput();
                    };
                    operationBar.AddLeft(HomeBtn);
                }

                mMainContainer.AddFixedChild(operationBar.GetPanelBar());
            }

            // Left
            VContainer leftContainer = new VContainer();
            {
                leftContainer.Initialize();
                leftContainer.SetSize(1, 1);

                // Preview
                mPreviewPanel.Initialize();
                mPreviewPanel.SetEnable(true);
                mPreviewPanel.SetTagString1(mFileGuid);
                mPreviewPanel.SetHeight(60);

                var comboBox = new ComboBox();
                comboBox.Initialize();
                comboBox.SetPos(10, 10);
                comboBox.SetWidth(100);
                comboBox.AddItem("Sphere");
                comboBox.AddItem("Cube");
                comboBox.AddItem("Capsule");
                comboBox.AddItem("Cylinder");
                comboBox.AddItem("Plane");
                comboBox.AddItem("Cone");
                comboBox.AddItem("Custom");
                comboBox.SetSelectedItemIndex(0);
                comboBox.ItemSelectedEvent += (ComboBox sender) =>
                    {
                        mPreview.SetPreviewMeshType(sender.GetSelectedItemText());
                    }
                ;
                mPreviewPanel.AddChild(comboBox);


                // Parameters + Detail
                {
                    mTab.AddTabElement("Detail", mDetailView);
                    mTab.AddTabElement("Parameters", mParametersView);
                    mTab.AddTabElement("Setting", mEditorSettingView);
                    mTab.Initialize();
                    mTab.SetHeight(700); // no effect...
                }

                VSplitter vSplitter = new VSplitter();
                vSplitter.Initialize();
                vSplitter.AddChild(mPreviewPanel);
                vSplitter.AddChild(mTab);

                leftContainer.AddSizableChild(vSplitter, 1.0f);
                leftContainer.SetWidth(10);
            }

            // Right
            VContainer rightContainer = new VContainer();
            {
                rightContainer.Initialize();

                Tab tab = new Tab();
                {
                    mStats = new Stats();
                    mStats.Initialize();
                    tab.AddTabElement("Stats", mStats);
                    tab.AddTabElement("Search", mSearchView);
                    tab.Initialize();
                    tab.SetHeight(40);
                    tab.SetWidth(299);
                }

                VSplitter vSplitter = new VSplitter();
                vSplitter.Initialize();
                vSplitter.AddChild(mImGuiPanel.Panel);
                vSplitter.AddChild(tab);

                rightContainer.AddSizableChild(vSplitter, 1.0f);
                rightContainer.SetWidth(299);
            }

            // Splitter
            HSplitter HSplitter = new HSplitter();
            HSplitter.Initialize();
            HSplitter.AddChild(leftContainer);
            HSplitter.AddChild(rightContainer);

            mMainContainer.AddSizableChild(HSplitter, 1);

            var fileName = Path.GetFileName(ResourceManager.Instance().ConvertGuidToPath(mFileGuid));
            base.Initialize(fileName, mMainContainer);

            mPreviewPanel.LeftMouseDownEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.CaptureMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.LeftButton, true);
            };
            mPreviewPanel.LeftMouseUpEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.ReleaseMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.LeftButton, false);
            };
            mPreviewPanel.RightMouseDownEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.CaptureMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.RightButton, true);
            };
            mPreviewPanel.RightMouseUpEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.ReleaseMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.RightButton, false);
            };
            mPreviewPanel.MiddleMouseDownEvent += (Control sender, int x, int y, ref bool _) => mPreview.OnKeyEvent((EditorKey)EditorKey.MiddleButton, true);
            mPreviewPanel.MiddleMouseUpEvent += (Control sender, int x, int y, ref bool _) => mPreview.OnKeyEvent((EditorKey)EditorKey.MiddleButton, false);
            mPreviewPanel.MouseMoveEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) => mPreview.OnMouseMoveEvent(MouseX - mPreviewPanel.GetScreenX(), MouseY - mPreviewPanel.GetScreenY());
            mPreviewPanel.MouseWheelEvent += (Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue) => mPreview.OnMouseWheelEvent(MouseDeltaZ);

            mPreviewPanel.PositionChangedEvent += (_, posChanged, sizeChanged) =>
            {
                if (sizeChanged)
                {
                    CrossEngineApi.SetSizeChanging(sizeChanged);
                    mPreview.OnResize(new Clicross.Float2(mPreviewPanel.GetWidth(), mPreviewPanel.GetHeight()));
                }
            };
            mPreviewPanel.PaintEvent += sender =>
            {
                var texture = mPreview.GetTexture();
                var width = Clicross.EditorUICanvasInterface.Instance().GetImageWidth(texture);
                var height = Clicross.EditorUICanvasInterface.Instance().GetImageHeight(texture);
                var color = new Color(1, 1, 1, 1);
                (GetDevice().GetEditorUICanvas() as EditorUICanvas).DrawImage(texture, mPreviewPanel.GetScreenX(), mPreviewPanel.GetScreenY(), width, height, ref color);

                sender.PaintChildren();
            };

            mImGuiPanel.Panel.KeyDownEvent += (Control Sender, Key Key, ref bool bContinue) =>
            {
                if (!GetDevice().IsKeyDown(Key.Control))
                {
                    var mouseX = GetDevice().GetMouseX() - mImGuiPanel.Panel.GetScreenX();
                    var mouseY = GetDevice().GetMouseY() - mImGuiPanel.Panel.GetScreenY();

                    if (0 <= mouseX && mouseX < mImGuiPanel.Panel.GetWidth() && 0 <= mouseY && mouseY < mImGuiPanel.Panel.GetHeight())
                    {
                        if (ExpressionShortcuts.TryGetValue(Key, out var expression))
                        {
                            var expressionCreateNodeInfo = new ExpressionCreateNodeInfo();
                            expressionCreateNodeInfo.className = $"cross::MaterialExpression{expression}";
                            mContext.CreateMaterialExpression(expressionCreateNodeInfo, mouseX, mouseY);
                        }
                    }
                }
                else
                {
                    if (Key == Key.V)
                    {
                        var mouseX = GetDevice().GetMouseX() - mImGuiPanel.Panel.GetScreenX();
                        var mouseY = GetDevice().GetMouseY() - mImGuiPanel.Panel.GetScreenY();
                        if (0 <= mouseX && mouseX < mImGuiPanel.Panel.GetWidth() && 0 <= mouseY && mouseY < mImGuiPanel.Panel.GetHeight())
                        {
                            mContext.Action_PasteExpressions(MaterialEditorUIManager.Instance.GetCopyExpressionsStr(), mouseX, mouseY);
                        }
                    }
                }
            };

            // Set TagName
            GetDockingCard().SetTagString1(mFileGuid);

            // Handle DragDrop Event
            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
        }


        public new void Update(long TimeElapsed)
        {
            base.Update(TimeElapsed);

            var fileName = Path.GetFileName(ResourceManager.Instance().ConvertGuidToPath(mFileGuid));
            GetDockingCard().SetText(fileName + (mContext.IsResourceChanged() ? "*" : ""));

            if (GetDockingCard().GetActive())
            {
                mPreview.SetWorldEnable(true);
            }
            else
            {
                mPreview.SetWorldEnable(false);
            }
            mPreview.Tick();
            mParametersView.Tick();
            mEditorSettingView.Tick();
            mDetailView.Tick();
        }

        public string GetGuid()
        {
            return mFileGuid;
        }

        public void ActiveDetailTab()
        {
            mTab.SelectTab("Detail");
        }

        async void DoClose(DockingCard Sender, bool bNotToClose)
        {
            if (mContext.IsResourceChanged())
            {
                await SaveFxOrMaterialFunction();
            }

            base.OnClose(Sender, ref bNotToClose);
            mPreview.Dispose();
            mContext.Dispose();
            mPreview = null;
            mContext = null;

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragEndEvent -= OnDragDropManagerDragEnd;
        }

        public override void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            DoClose(Sender, bNotToClose);
        }

        protected override void OnEnter()
        {
            base.OnEnter();
            mContext.OnActivate(true);
        }

        protected override void OnLeave()
        {
            base.OnLeave();
            mContext.OnActivate(false);
        }

        public void OnResourceRenamed()
        {
            var fileName = Path.GetFileName(ResourceManager.Instance().ConvertGuidToPath(mFileGuid));
            GetDockingCard().SetText(fileName);
        }

        public void OnMaterialFunctionChange(string materialFunctionGuid)
        {
            mContext.OnMaterialFunctionChange(materialFunctionGuid);
        }

        public void OnMaterialParameterCollectionChange(string materialFunctionGuid)
        {
            mContext.OnMaterialParameterCollectionChange(materialFunctionGuid);
        }

        public void OnMaterialParameterCollectionSelectChange()
        {
            mContext.OnMaterialParameterCollectionSelectChange();
        }

        protected void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager() || !GetDockingCard().GetActive())
            {
                return;
            }

            mDetailView.OnDragDropManagerDragEnd(Sender, UIManager, MouseX, MouseY, ref bContinue);
            mEditorSettingView.OnDragDropManagerDragEnd(Sender, UIManager, MouseX, MouseY, ref bContinue);

            ProjectUI ProjectUI = ProjectUI.GetInstance();
            if (ProjectUI.IsPathesDragging())
            {
                List<string> PathesDragged = ProjectUI.GetPathesDragged();

                if (mImGuiPanel.Panel.IsPointIn(MouseX, MouseY) && PathesDragged.Count == 1)
                {
                    mContext.OnResourceDragEnd(PathesDragged[0], (uint)(MouseX - mImGuiPanel.Panel.GetScreenX()), (uint)(MouseY - mImGuiPanel.Panel.GetScreenY()));
                }
            }
        }

        public async System.Threading.Tasks.Task SaveFxOrMaterialFunction(bool force = false)
        {
            ProgressUI LoadingUI = new ProgressUI();
            Progress _Progress = new Progress("Saving Material", 1);
            _Progress.SetStep(0, "", 1);
            LoadingUI.Initialize(GetUIManager(), _Progress, false);
            DialogUIManager.GetInstance().ShowDialogUI(LoadingUI);

            Background.SuspendBackgroundThreads();

            await System.Threading.Tasks.Task.Delay(1);

            if (mContext.Apply(force))
            {
                mListener.AddInformationMessage("Material compiled successfully.");
            }
            else
            {
                if (force || mContext.IsResourceChanged())
                {
                    mListener.AddErrorMessage("Material compiled failed!");
                }
            }

            Background.ResumeBackgroundThreads();

            _Progress.Done();
            _Progress.Close();
            ThumbnailHost.GetInstance().AddThumbnailTask(mFileRelativePath);
        }

        void ShowHLSLCode(string code)
        {
            ReadOnlyTextUI textUI = new ReadOnlyTextUI();
            textUI.Initialize(GetUIManager(), "HLSL Code", code);
            DialogUIManager.GetInstance().ShowDialogUI(textUI);
        }
    }
}
