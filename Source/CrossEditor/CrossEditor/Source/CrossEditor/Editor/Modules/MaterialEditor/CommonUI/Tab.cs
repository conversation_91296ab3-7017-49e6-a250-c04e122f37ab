

using ClangenCli;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class Tab : VContainer
    {
        List<Pair<String, Control>> mTabElements = new List<Pair<string, Control>>();

        AlignedPanel mTabHeaderPanel = new AlignedPanel();
        List<Button> mTabHeaderButtons = new List<Button>();

        public void AddTabElement(string name, Control control)
        {
            mTabElements.Add(new Pair<String, Control>(name, control));
        }

        public override void Initialize()
        {
            AddFixedChild(mTabHeaderPanel);

            foreach (var element in mTabElements)
            {
                Button button = new Button();
                button.SetText(element.first);
                button.SetFontSize(12);
                button.SetTextAlign(TextAlign.CenterCenter);
                button.SetSize(button.CalculateTextWidth() + 2 * 5, 25);
                button.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                button.ClickedEvent += OnTabButtonClick;

                mTabHeaderButtons.Add(button);
                mTabHeaderPanel.AddChild(button);

                AddSizableChild(element.second, 1.0f);
            }

            UpdateTabHeaderLayout();

            SelectTab(0);
        }


        void OnTabButtonClick(Button button)
        {
            SelectTab(mTabHeaderButtons.IndexOf(button));
        }

        public void SelectTab(string tabElementName)
        {
            var elementPairs = mTabElements.FindAll((Pair<string, Control> pair) => { return pair.first == tabElementName; });
            if (elementPairs.Count > 0)
            {
                // select the first match tab element
                SelectTab(mTabElements.IndexOf(elementPairs[0]));
            }
        }

        public void SelectTab(int index)
        {
            if (index < mTabElements.Count)
            {
                mTabHeaderButtons.ForEach(Item => Item.SetNormalColor(Color.EDITOR_UI_COLOR_KEY));
                mTabHeaderButtons[index].SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);

                mTabElements.ForEach(Item => Item.second.SetVisible(false));

                var content = mTabElements[index].second;
                content.SetVisible(true);

                Type type = content.GetType();
                var inspectMethod = type.GetMethod("InspectObject");
                if (inspectMethod != null)
                {
                    inspectMethod.Invoke(content, null);
                }
            }
        }

        public void UpdateTabHeaderLayout()
        {
            int width = mTabHeaderPanel.GetWidth();
            int line = 0;
            foreach (Button button in mTabHeaderButtons)
            {
                mTabHeaderPanel.FloatToLeft(button);
                button.SetY(line * 24 + 2);
                //if (button.GetEndX() > width - 5)
                //{
                //    mTabHeaderPanel.ClearLastLayout();
                //    mTabHeaderPanel.FloatToLeft(button);
                //    line++;
                //    button.SetY(line * 24 + 2);
                //}
            }

            int FilterHeight = (line + 1) * 24;
            mTabHeaderPanel.SetPosition(0, 0, width, FilterHeight);
            mTabHeaderPanel.ClearLastLayout();
        }

        public override void OnPositionChanged(bool bPositionChanged, bool bSizeChanged)
        {
            mTabHeaderPanel.SetPosition(0, 0, GetWidth(), mTabHeaderPanel.GetHeight());

            int elementY = mTabHeaderPanel.GetHeight();
            int elementHeight = GetHeight() - mTabHeaderPanel.GetHeight();
            if (elementHeight >= 0)
            {
                foreach (var elementPair in mTabElements)
                {
                    elementPair.second.SetPosition(0, elementY, GetWidth(), elementHeight);
                }
            }
        }
    }
}