using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class MaterialParametersView : VContainer
    {
        ScrollView m_ScrollView = new ScrollView();
        Panel m_ScrollPanel;
        object mObjectInspected;
        object mObjectTag;
        Inspector mInspector;
        InspectorHandler mInspectorHandler = new InspectorHandler();

        MaterialEditor mMaterialEditorContext;
        List<MaterialParameterGroup> mParameterGroups = new List<MaterialParameterGroup>();

        public MaterialParametersView(MaterialEditor context)
        {
            mMaterialEditorContext = context;

            m_ScrollView.Initialize();
            m_ScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            m_ScrollView.GetHScroll().SetEnable(false);

            m_ScrollPanel = m_ScrollView.GetScrollPanel();
            m_ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            m_ScrollPanel.PaintEvent += (Sender) =>
            {
                SetChildInnerEnable(Sender);
                Sender.PaintThis();
                Sender.PaintChildren();
            };

            mInspectorHandler.InspectObject = DoInspectObject;
            mInspectorHandler.UpdateLayout = DoUpdateLayout;
            mInspectorHandler.ReadValue = DoReadValue;
        }

        public override void Initialize()
        {
            base.Initialize();

            AddSizableChild(Control, 1);
        }

        public void InspectObject()
        {
            mInspectorHandler.InspectObject();
            mInspectorHandler.UpdateLayout();
        }

        private void SetChildInnerEnable(Control Control)
        {
            for (int i = 0; i < Control.GetChildCount(); ++i)
            {
                Control Child = Control.GetChild(i);
                if (UIManager.RectInRect(Child.GetScreenX(), Child.GetScreenY(), Child.GetWidth(), Child.GetHeight(),
                    m_ScrollView.GetScreenX(), m_ScrollView.GetScreenY(), m_ScrollView.GetWidth(), m_ScrollView.GetHeight()))
                {
                    Child.SetInnerEnable(true);

                    if (Child.GetChildCount() != 0)
                    {
                        SetChildInnerEnable(Child);
                    }
                }
                else
                {
                    Child.SetInnerEnable(false);
                }
            }
        }

        void OnParameterChange(object PropertyOwner, PropertyInfo Property)
        {
            mMaterialEditorContext.OnParameterChange(PropertyOwner as MaterialParameter);
        }

        private void DoInspectObject()
        {
            m_ScrollPanel.ClearChildren();
            if (mObjectInspected != null)
            {
                if (mObjectInspected is List<MaterialParameterGroup>)
                {
                    mInspector = new Inspector_MaterialParameterGroups();
                }

                if (mInspector != null)
                {
                    mInspector.SetContainer(m_ScrollPanel);
                    mInspector.SetInspectorHandler(mInspectorHandler);
                    mInspector.SetPropertyModifiedFunction(OnParameterChange);
                    mInspector.InspectObject(mObjectInspected, mObjectTag);
                    mInspector.UpdateCheckExpand();
                }
                UpdateLayout();
            }
        }

        private void DoUpdateLayout()
        {
            int ScrollPanelWidth = m_ScrollView.GetWidth();
            int Y = 0;
            if (mInspector != null && mObjectInspected != null)
            {
                mInspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > m_ScrollView.GetHeight())
                {
                    ScrollPanelWidth = m_ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    mInspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            m_ScrollPanel.SetSize(ScrollPanelWidth, Height);
            m_ScrollView.UpdateScrollBar();
        }

        private void UpdateLayout()
        {
            mInspectorHandler.UpdateLayout();
        }

        private void DoReadValue()
        {
            if (mInspector != null)
            {
                mInspector.ReadValue();
            }
        }

        public void Tick()
        {
            List<MaterialParameterGroup> parameterGroups = new List<MaterialParameterGroup>();
            for (int i = 0; i < mMaterialEditorContext.GetMaterialParameterGroupsCount(); i++)
            {
                parameterGroups.Add(mMaterialEditorContext.GetMaterialParameterGroup(i));
            }

            bool isParameterGroupChange = false;
            if (parameterGroups.Count != mParameterGroups.Count)
            {
                isParameterGroupChange = true;
            }
            else
            {
                for (int i = 0; i < parameterGroups.Count; i++)
                {
                    if (parameterGroups[i].GetPointer() != mParameterGroups[i].GetPointer())
                    {
                        isParameterGroupChange = true;
                    }
                }
            }

            if (isParameterGroupChange)
            {
                mParameterGroups = parameterGroups;

                SetObjectInspected(mParameterGroups);
                InspectObject();
            }

            UpdateLayout();
        }

        public void SetObjectInspected(object ObjectInspected)
        {
            mObjectInspected = ObjectInspected;
            if (mInspector != null)
            {
                mInspector.WriteValue();
            }

            if (mObjectInspected == null)
            {
                mInspector = null;
            }
        }

        public void SetObjectTag(Object Tag)
        {
            mObjectTag = Tag;
        }

        public void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (m_ScrollView.GetVisible_Recursively() && m_ScrollView.IsPointIn_Recursively(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (ProjectUI.IsPathesDragging())
                {
                    List<string> PathesDragged = ProjectUI.GetPathesDragged();
                    DropPathOnInspector(mInspector, MouseX, MouseY, PathesDragged);
                }
            }
        }

        bool DropPathOnInspector(Inspector Inspector, int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (Inspector == null)
            {
                return false;
            }
            List<Inspector> ChildInspectors = Inspector.GetChildInspectors();
            foreach (Inspector ChildInspector in ChildInspectors)
            {
                if (DropPathOnInspector(ChildInspector, MouseX, MouseY, PathesDragged))
                {
                    return true;
                }
            }
            return Inspector.OnDropPathes(MouseX, MouseY, PathesDragged);
        }

        public Control Control => m_ScrollView;
    }
}
