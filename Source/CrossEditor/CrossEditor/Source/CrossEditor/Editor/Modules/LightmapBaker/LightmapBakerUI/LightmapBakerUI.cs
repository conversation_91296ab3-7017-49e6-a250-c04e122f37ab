using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [DockingMenuAttribute(DefaultDocking = DockingBlockSection.RightInspector)]
    class LightingBakerUI : InspectorDockingBlockItems<Inspector_Struct>
    {
        const int SPAN_X = 5;
        const int LABEL_FONT_SIZE = 20;
        const int BUTTON_FONT_SIZE = 12;
        const int LABEL_HEIGHT = 30;
        const int BUTTON_HEIGHT = 20;
        const int SEPARATOR_HEIGHT = 1;

        static LightingBakerUI _Instance = new LightingBakerUI();
        //static InspectorUI _InspectorUIInstance = new InspectorUI();

        //ScrollView _ScrollView;
        //Panel _ScrollPanel;
        //Inspector_Struct _Inspector;
        //InspectorHandler _InspectorHandler;

        Label _LightmapBakerLabel;
        Button _ResetButton;
        Button _BakeButton;
        Button _BatchButton;
        Button _ClearForceBakeLightMapBtn;
        Label _ReflectionProbeBakerLabel;
        Button _BakeRPButton;
        bool _DataInited = false;

        LightmapBakerSettings _LightmapBakerSettings
        {
            get { return _InspectorObject as LightmapBakerSettings; }
        }
        bool _BakeLightMapEnable = true;
        List<Entity> _LightMapBakeEntityList = new List<Entity>();

        public new static LightingBakerUI GetInstance()
        {
            return _Instance;
        }

        //public DockingCard GetDockingCard() => _InspectorUIInstance.GetDockingCard();

        LightingBakerUI() : base(nameof(LightingBakerUI))
        {
            _InspectorObject = new LightmapBakerSettings();
        }

        public void CopyLightMapBakerConfigJson(string ProjectDirectory, bool reset)
        {
            string ResourceDirectory = EditorUtilities.GetResourceDirectory();
            string SourceFilename = ResourceDirectory + "/BakeConfig.json";
            string DestinationFilename = ProjectDirectory + "/BakeConfig.json";
            if (FileHelper.IsFileExists(DestinationFilename) == false || reset)
            {
                FileHelper.CopyFile(SourceFilename, DestinationFilename);
            }
        }

        public void RefreshConfig(bool reset = false, bool bakeTypeOnly = false)
        {
            CopyLightMapBakerConfigJson(MainUI.GetInstance().GetProjectDirectory(), reset);
            _LightmapBakerSettings.RefreshConfig(bakeTypeOnly);
            if (_DataInited)
            {
                this.ReadValue();
            }
            else
            {
                this.InspectObject();
            }
            _DataInited = true;
        }

        public virtual void OnAttributeChanged(Object Sender, string PropertyName, object PropertyValue)
        {
            if (PropertyName == "BakeType")
            {
                RefreshConfig(false, true);
            }
        }


        public override void InitializeInspectObject()
        {
            _LightmapBakerSettings.Reset();
        }

        public override void InitializeCustomItems()
        {
            _LightmapBakerLabel = new Label();
            InitializeLabel(_LightmapBakerLabel, "LightmapBaker");

            _BakeButton = new Button();
            InitializeButton(_BakeButton, "Bake", OnBakeButtonClicked);

            _ResetButton = new Button();
            InitializeButton(_ResetButton, "Reset", OnResetButtonClicked);

            _BatchButton = new Button();
            InitializeButton(_BatchButton, "BatchSetComponent", OnBatchButtonClicked);

            _ClearForceBakeLightMapBtn = new Button();
            InitializeButton(_ClearForceBakeLightMapBtn, "ClearForceBakeLightMap", OnClearForceBakeLightMap);

            _ReflectionProbeBakerLabel = new Label();
            InitializeLabel(_ReflectionProbeBakerLabel, "ReflectionProbeBaker");

            _BakeRPButton = new Button();
            InitializeButton(_BakeRPButton, "BakeAll", OnBakeRPButtonClicked);
        }

        void InitializeLabel(Label Label, string Text)
        {
            Label.SetFontSize(LABEL_FONT_SIZE);
            Label.SetTextAlign(TextAlign.CenterLeft);
            Label.SetText(Text);
            _ScrollPanel.AddChild(Label);
        }

        void InitializeButton(Button Button, string Text, ButtonClickedEventHandler OnButtonClicked)
        {
            Button.Initialize();
            Button.SetText(Text);
            Button.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            Button.SetFontSize(BUTTON_FONT_SIZE);
            Button.SetTextOffsetY(2);
            Button.ClickedEvent += OnButtonClicked;
            _ScrollPanel.AddChild(Button);
        }

        public TextureCompression GetTextureCompressMode()
        {
            return _LightmapBakerSettings.TextureCompressMode;
        }

        public override void UpdateInspector()
        {
            GetDockingCard().FocusChangedEvent += OnFocusChangedHandler;
            GetTypedInsepctor().AttributeChangedEvent += OnAttributeChanged;
        }

        new void OnFocusChangedHandler(Control Sender)
        {
            if (!_DataInited)
            {
                RefreshConfig();
            }
        }

        public override void UpdateLayout()
        {
            int Y = 5;
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int AvailableWidth = _ScrollView.GetWidth() - 2 * SPAN_X;

            Y += 5;
            _LightmapBakerLabel.SetPosition(SPAN_X, Y, AvailableWidth, LABEL_HEIGHT);
            Y += LABEL_HEIGHT;

            int InspectorY = Y;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
            }

            Y += 5;
            _BakeButton.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;

            Y += 5;
            _ResetButton.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;

            Y += 5;
            _BatchButton.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;

            Y += 5;
            _ClearForceBakeLightMapBtn.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;

            Y += 20;
            _ReflectionProbeBakerLabel.SetPosition(SPAN_X, Y, AvailableWidth, LABEL_HEIGHT);
            Y += LABEL_HEIGHT;

            Y += 5;
            _BakeRPButton.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;

            if (_Inspector != null && Y > _ScrollView.GetHeight())
            {
                ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                _Inspector.UpdateLayout(ScrollPanelWidth, ref InspectorY);
            }

            _ScrollPanel.SetSize(ScrollPanelWidth, Y);
            _ScrollView.UpdateScrollBar();
        }

        void OnResetButtonClicked(Button Sender)
        {
            RefreshConfig(true);
        }

        void OnBakeButtonClicked(Button Sender)
        {
            _LightmapBakerSettings.SaveConfig();
            _BakeLightMapEnable = _LightmapBakerSettings.BakeLightMapEnable;
            EditorScene curScene = EditorScene.GetInstance();
            List<Entity> bakeEntityList = curScene.GetLightMapBakeEntity();
            if (_BakeLightMapEnable)
            {
                _LightMapBakeEntityList = bakeEntityList;
            }
            else
            {
                _LightMapBakeEntityList = new List<Entity>();
                foreach (Entity entity in bakeEntityList)
                {
                    LightMapBaker lmComp = (LightMapBaker)entity.GetComponent(typeof(LightMapBaker));
                    if (lmComp != null && lmComp.ForceBakeLightMap)
                    {
                        _LightMapBakeEntityList.Add(entity);
                    }
                }
            }
            curScene.BakeLightmap();
        }

        void OnBatchButtonClicked(Button Sender)
        {
            _LightmapBakerSettings.SaveConfig();
            EditorScene.GetInstance().LightmapSetComponentBatch(_LightmapBakerSettings);
        }

        void OnClearForceBakeLightMap(Button Sender)
        {
            EditorScene.GetInstance().ClearForceBakeLightMap();
        }

        void OnBakeRPButtonClicked(Button Sender)
        {
            CrossEngineApi.SetReflectionProbeBakeState(EditorScene.GetInstance().GetWorld().GetNativePointer(), true);
        }

        public List<Entity> GetLightMapBakeEntityList()
        {
            return _LightMapBakeEntityList;
        }
    }
}