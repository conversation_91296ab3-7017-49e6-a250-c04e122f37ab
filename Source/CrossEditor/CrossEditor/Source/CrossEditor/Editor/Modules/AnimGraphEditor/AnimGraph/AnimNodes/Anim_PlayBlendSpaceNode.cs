using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    public enum PlayBlendSpaceNodeInParamType
    {
        Vector2,
        X_And_Y
    };

    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_PlayBlendSpaceNodeData : NodeData
    {
        [JsonProperty()]
        public PlayBlendSpaceNodeInParamType InParamType = PlayBlendSpaceNodeInParamType.Vector2;

        [JsonProperty("BlendSpacePath")]
        public string _BlendSpacePath = "";

        [JsonProperty("SyncParams")]
        public SyncParams _SyncParams = new SyncParams();

        [JsonProperty("InParamLinks")]
        public List<string> ParamLinks = new List<string>();
    }

    class Anim_PlayBlendSpaceNode : Anim_PlayAnimBaseNode
    {
        public Anim_PlayBlendSpaceNode() : base()
        {
            AddOutSlot("Output", SlotType.LocalPoseLink);
            AddInSlot("PlayRate", SlotType.ParamImplLink, SlotSubType.Float);
            AddInSlot("Pos", SlotType.ParamImplLink, SlotSubType.Vector2);
            NodeData = new Anim_PlayBlendSpaceNodeData();
        }

        public override void FromData(NodeData NewNodeData)
        {
            base.FromData(NewNodeData);
            InParamType = (NodeData as Anim_PlayBlendSpaceNodeData).InParamType;
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_PlayBlendSpaceNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.ParamLinks = new List<string>();
            Data.InParamType = InParamType;

            foreach (var InSlot in InSlots)
            {
                var PosConnections = InSlot.GetConnections();
                if (PosConnections.Count == 0)
                    Data.ParamLinks.Add("");
                else
                    Data.ParamLinks.Add(PosConnections[0].ID.ToString());
            }
            return Data;
        }

        public string GetStringContent()
        {
            string Content = AnimUtil.TrimAnimPath(PathHelper.GetFileName(BlendSpacePath));
            return Content.Length == 0 ? "Anim" : Content;
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = GetStringContent();
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, GetStringContent(), Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }

        public override void UpdateUsedAnims()
        {
            _UsedAnims.Clear();
            _UsedAnims.Add(BlendSpacePath);
        }

        // Move these two properties into anim_node, level up ui
        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Animation SequencePath", FileTypeDescriptor = "Animation Assets#nda", ObjectClassID1 = ClassIDType.CLASS_AnimBlendSpaceRes)]
        public string BlendSpacePath
        {
            get { return ResourceManager.Instance().ConvertGuidToPath(((Anim_PlayBlendSpaceNodeData)NodeData)._BlendSpacePath); }
            set
            {
                ((Anim_PlayBlendSpaceNodeData)NodeData)._BlendSpacePath = value;
                UpdateUsedAnims();
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "SyncParams")]
        public SyncParams SyncParams
        {
            get { return ((Anim_PlayBlendSpaceNodeData)NodeData)._SyncParams; }
            set { ((Anim_PlayBlendSpaceNodeData)NodeData)._SyncParams = value; }
        }

        private PlayBlendSpaceNodeInParamType _InParamType = PlayBlendSpaceNodeInParamType.Vector2;

        [PropertyInfo()]
        public PlayBlendSpaceNodeInParamType InParamType
        {
            get { return _InParamType; }
            set
            {

                if (_InParamType != value)
                {
                    _InParamType = value;
                    List<Connection> ConnectionsToRemove = new List<Connection>();
                    while (InSlots.Count > 1)
                    {
                        var LastSlot = InSlots[InSlots.Count - 1];
                        if (GetOwner().View != null)
                            GetOwner().View.RemoveConnections(LastSlot.GetConnections());
                        foreach (var connection in LastSlot.GetConnections())
                        {
                            var OutSlot = connection.OutSlot;
                            OutSlot.RemoveConnection(connection);
                        }
                        RemoveLastInSlot();
                    }

                    if (_InParamType == PlayBlendSpaceNodeInParamType.X_And_Y)
                    {
                        AddInSlot("X", SlotType.ParamImplLink, SlotSubType.Float);
                        AddInSlot("Y", SlotType.ParamImplLink, SlotSubType.Float);
                    }
                    else
                    {
                        AddInSlot("Pos", SlotType.ParamImplLink, SlotSubType.Vector2);
                    }
                }
            }
        }
    }
}
