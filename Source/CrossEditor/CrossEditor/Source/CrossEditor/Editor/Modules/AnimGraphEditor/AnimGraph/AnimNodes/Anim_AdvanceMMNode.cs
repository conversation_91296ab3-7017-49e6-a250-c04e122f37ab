using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class AnimMotionMatchVisualizer
    {
        [JsonProperty("FutureTraj")]
        bool _FutureTraj = false;

        [JsonProperty("SelectedTraj")]
        bool _SelectedTraj = false;

        [JsonProperty("Pose")]
        bool _Pose = false;

        [JsonProperty("PreviewAnimID")]
        int _PreviewAnimID = -1;

        [PropertyInfo(PropertyType = "Auto", ToolTips = "FutureTraj")]
        public bool FutureTraj
        {
            get { return _FutureTraj; }
            set { _FutureTraj = value; }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "SelectedTraj")]
        public bool SelectedTraj
        {
            get { return _SelectedTraj; }
            set { _SelectedTraj = value; }
        }


        [PropertyInfo(PropertyType = "Auto", ToolTips = "Pose")]
        public bool Pose
        {
            get { return _Pose; }
            set { _Pose = value; }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "_PreviewAnimID")]
        public int PreviewAnimID
        {
            get { return _PreviewAnimID; }
            set { _PreviewAnimID = value; }
        }

    };

    [JsonObject(MemberSerialization.OptIn)]
    public class AnimMotionMatchCurrentPoseSearch
    {
        [JsonProperty("PositionThreshold")]
        float _PositionThreshold = 50.0f;

        [JsonProperty("AngleThreshold")]
        float _AngleThreshold = 2.0f;

        [PropertyInfo(PropertyType = "Auto", ToolTips = "PositionThreshold")]
        public float PositionThreshold
        {
            get { return _PositionThreshold; }
            set { _PositionThreshold = value; }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "AngleThreshold")]
        public float AngleThreshold
        {
            get { return _AngleThreshold; }
            set { _AngleThreshold = value; }
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class AnimMotionMatchConfig
    {
        [JsonProperty("BlendTime")]
        float _BlendTime = 0.3f;

        [JsonProperty("TimeBetweenBlends")]
        float _TimeBetweenBlends = 0.1f;

        [JsonProperty("TransitMode")]
        string _TransitMode = "CrossFade";

        [JsonProperty("LeasetAnimInQueue")]
        int _LeasetAnimInQueue = 3;

        [JsonProperty("EnableCurrentPoseSearch")]
        bool _EnableCurrentPoseSearch = true;

        [JsonProperty("CurrentPoseSearch")]
        AnimMotionMatchCurrentPoseSearch _CurrentPoseSearchConfig = new AnimMotionMatchCurrentPoseSearch();



        [PropertyInfo(PropertyType = "Auto", ToolTips = "BlendTime")]
        public float BlendTime
        {
            set
            {
                _BlendTime = value;
            }
            get
            {
                return _BlendTime;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "TransitMode")]
        public BlendMode TransitMode
        {
            set
            {
                _TransitMode = value.ToString();
            }
            get
            {
                return (BlendMode)Enum.Parse(typeof(BlendMode), _TransitMode);
            }
        }


        [PropertyInfo(PropertyType = "Auto", ToolTips = "TimeBetweenBlends")]
        public float TimeBetweenBlends
        {
            set
            {
                _TimeBetweenBlends = value;
            }
            get
            {
                return _TimeBetweenBlends;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "EnableCurrentPoseSearch")]
        public bool EnableCurrentPoseSearch
        {
            set
            {
                _EnableCurrentPoseSearch = value;
            }
            get
            {
                return _EnableCurrentPoseSearch;
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "CurrentPoseSearchConfig")]
        public AnimMotionMatchCurrentPoseSearch CurrentPoseSearchConfig
        {
            set
            {
                _CurrentPoseSearchConfig = value;
            }
            get
            {
                return _CurrentPoseSearchConfig;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "LeasetAnimInQueue")]
        public int LeasetAnimInQueue
        {
            set
            {
                _LeasetAnimInQueue = value;
            }
            get
            {
                return _LeasetAnimInQueue;
            }
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class MotionMatchNodeWarp
    {
        [JsonProperty("Configs")]
        public AnimMotionMatchConfig _config;

        [JsonProperty("Data")]
        public string _Data = "";

        [JsonProperty("Visualizer")]
        public AnimMotionMatchVisualizer _Visualizer;

        [PropertyInfo(PropertyType = "Struct", ToolTips = "MotionMatchConfig")]
        public AnimMotionMatchConfig RuntimeConfig
        {
            get
            {
                return _config;
            }
            set
            {
                _config = value;
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "MotionMatchConfig")]
        public AnimMotionMatchVisualizer Visualzer
        {
            get
            {
                return _Visualizer;
            }
            set
            {
                _Visualizer = value;
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "MotionMatcAsset", FileTypeDescriptor = "MotionMatch Assets#nda", ObjectClassID1 = ClassIDType.CLASS_MotionDataAsset)]
        public string Data
        {
            get
            {
                return _Data;
            }
            set
            {
                if (value == "") return;

                MotionMatchAsset dataset = (MotionMatchAsset)Resource.Get(value);
                if (dataset.PoseMatching)
                {
                    // incompatible type 
                    string Message = string.Format("Resource type mismatch: {0}, PoseMatching Asset cannot used in MotionMatchNode.", value);
                    CommonDialogUI.ShowSimpleOKDialog(UIManager.GetMainUIManager(), "Tips", Message);
                }
                else
                {
                    _Data = value;
                }

            }
        }


        public MotionMatchNodeWarp()
        {
            _config = new AnimMotionMatchConfig();
            _Visualizer = new AnimMotionMatchVisualizer();
        }
    }




    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_MotionMatchNodeData : NodeData
    {
        [JsonProperty("InParamLinks")]
        public List<string> ParamLinks;

        [JsonProperty("AdvanceMMNode")]
        public MotionMatchNodeWarp AdvanceMMNode;

        public Anim_MotionMatchNodeData()
        {
            AdvanceMMNode = new MotionMatchNodeWarp();
        }
    }

    class Anim_AdvanceMMNode : Anim_FlowNode
    {

        private const string Trajectory = "InTrajectory";


        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 40;
            ContentHeight = 20;
        }

        public Anim_AdvanceMMNode() : base()
        {
            AddOutSlot("Result", SlotType.LocalPoseLink);
            AddInSlot(Trajectory, SlotType.ParamImplLink, SlotSubType.Customized);
            NodeData = new Anim_MotionMatchNodeData();
        }
        protected Anim_MotionMatchNodeData GetMMNodeData()
        {
            return NodeData as Anim_MotionMatchNodeData;
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "MotionMatchNodeConfig")]
        public MotionMatchNodeWarp MotionMatchConfig
        {
            get { return GetMMNodeData().AdvanceMMNode; }
            set { GetMMNodeData().AdvanceMMNode = value; }
        }

        public override NodeData ToData()
        {
            var data = new Anim_MotionMatchNodeData();
            data.Name = base.ToData().Name;
            data.Type = TrimAnimNodeType(this.GetType().Name);
            data.ParamLinks = new List<string>();

            var indexSlot = FindInSlot(Trajectory);
            foreach (var connection in indexSlot.GetConnections())
            {
                data.ParamLinks.Add(connection.ID.ToString());
            }

            data.AdvanceMMNode = GetMMNodeData().AdvanceMMNode;

            return data;
        }

    }
}
