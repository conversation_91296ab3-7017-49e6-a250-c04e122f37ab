using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_StoryBoardProperty : Inspector
    {
        StoryBoardProperty _StoryBoardProperty;

        Panel _PanelIcon;
        Edit _EditName;

        public Inspector_StoryBoardProperty()
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _StoryBoardProperty = (StoryBoardProperty)Object;

            _PanelIcon = new Panel();
            _PanelIcon.Initialize();
            _PanelIcon.SetImage(UIManager.LoadUIImage("Editor/Game/GameObject.png"));
            _SelfContainer.AddChild(_PanelIcon);

            _EditName = new Edit();
            _EditName.SetFontSize(16);
            _EditName.Initialize(EditMode.Simple_SingleLine);
            _EditName.LoadSource("");
            _EditName.SetReadOnly(true);
            _SelfContainer.AddChild(_EditName);
            EditContextUI.GetInstance().RegisterEdit(_EditName);
            _EditName.SetSize(100, 16);
            _EditName.SetText(_StoryBoardProperty.GetType().Name.ToString());

            RefreshChildInspectors();
        }

        protected virtual void RefreshChildInspectors()
        {
            Type Type = _StoryBoardProperty.GetType();
            ClearChildInspectors();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AddPropertyInspector(PropertyInfo, _StoryBoardProperty);
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public virtual object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public virtual void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
                _StoryBoardProperty.Owner.GetView().SetModified();
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            _SelfContainer.SetPosition(0, Y, Width, 30);
            Y += 30;

            _PanelIcon.SetPosition(SPAN_X, SPAN_Y, 20, 20);
            int EditNameWidth = Width - 3 * SPAN_X - 20;
            _EditName.SetPosition(_PanelIcon.GetEndX() + SPAN_X, 7, EditNameWidth, 16);

            base.UpdateLayout(Width, ref Y);
        }
    }
}
