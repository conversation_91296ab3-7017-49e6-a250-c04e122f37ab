using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptOut)]
    class Anim_TransformBoneNodeData_Property
    {
        [PropertyInfo()]
        public BoneControlSpace Space { get; set; } = BoneControlSpace.RootSpace;
        [PropertyInfo()]
        public BoneModificationMode Mode { get; set; } = BoneModificationMode.Ignore;
    }

    [JsonObject(MemberSerialization.OptOut)]
    class Anim_TransformBoneNodeData : NodeData
    {
        public string BoneName = "None";
        public Anim_TransformBoneNodeData_Property Translation = new Anim_TransformBoneNodeData_Property();
        public Anim_TransformBoneNodeData_Property Rotation = new Anim_TransformBoneNodeData_Property();
        public Anim_TransformBoneNodeData_Property Scale = new Anim_TransformBoneNodeData_Property();

        public List<string> InPoseLinks;
        public List<string> InParamLinks;
    }

    class Anim_TransformBoneNode : Anim_FlowNode
    {
        static readonly string IN_POSE_SLOT_NAME = "InPose";
        static readonly string TRANLATION_SLOT_NAME = "Translation";
        static readonly string ROTATION_SLOT_NAME = "Rotation";
        static readonly string SCALE_SLOT_NAME = "Scale";
        static readonly string OUT_POSE_SLOT_NAME = "OutPose";

        public Anim_TransformBoneNode() : base()
        {
            NodeData = new Anim_TransformBoneNodeData();

            AddInSlot(IN_POSE_SLOT_NAME, SlotType.LocalPoseLink);
            AddInSlot(TRANLATION_SLOT_NAME, SlotType.ParamImplLink, SlotSubType.Vector3);
            AddInSlot(ROTATION_SLOT_NAME, SlotType.ParamImplLink, SlotSubType.Vector3);
            AddInSlot(SCALE_SLOT_NAME, SlotType.ParamImplLink, SlotSubType.Vector3);

            AddOutSlot(OUT_POSE_SLOT_NAME, SlotType.LocalPoseLink);
        }

        Anim_TransformBoneNodeData DeriveNodeData => NodeData as Anim_TransformBoneNodeData;

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Bone name")]
        public string BoneName
        {
            get => DeriveNodeData.BoneName;
            set => DeriveNodeData.BoneName = value;
        }
        [PropertyInfo(PropertyType = "Struct", ToolTips = "Translation")]
        public Anim_TransformBoneNodeData_Property Translation
        {
            get => DeriveNodeData.Translation;
            set => DeriveNodeData.Translation = value;
        }
        [PropertyInfo(PropertyType = "Struct", ToolTips = "Rotation")]
        public Anim_TransformBoneNodeData_Property Rotation
        {
            get => DeriveNodeData.Rotation;
            set => DeriveNodeData.Rotation = value;
        }
        [PropertyInfo(PropertyType = "Struct", ToolTips = "Scale")]
        public Anim_TransformBoneNodeData_Property Scale
        {
            get => DeriveNodeData.Scale;
            set => DeriveNodeData.Scale = value;
        }

        public override NodeData ToData()
        {
            DeriveNodeData.Name = base.ToData().Name;
            DeriveNodeData.Type = TrimAnimNodeType(this.GetType().Name);
            DeriveNodeData.InParamLinks = new List<string>();
            DeriveNodeData.InPoseLinks = new List<string>();

            foreach (var slot in InSlots)
            {
                string inputNodeID = "";
                if (slot.GetConnections().Count > 0)
                    inputNodeID = slot.GetConnections()[0].ID.ToString();

                if (slot.Name == IN_POSE_SLOT_NAME)
                    DeriveNodeData.InPoseLinks.Add(inputNodeID);
                else
                    DeriveNodeData.InParamLinks.Add(inputNodeID);
            }

            return DeriveNodeData;
        }

        public string GetStringContent()
        {
            return BoneName;
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = GetStringContent();
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, GetStringContent(), Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }
    }
}
