using CEngine;
using EditorUI;
using Newtonsoft.Json;


namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class PoseMatchingConfig
    {
        [JsonProperty("Data")]
        public string _Data = "";
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_PoseMatchingNodeData : Anim_PlayCompositeNodeData
    {

        [JsonProperty("PoseMacthing")]
        public PoseMatchingConfig PoseMacthing;

        public Anim_PoseMatchingNodeData() : base()
        {
            PoseMacthing = new PoseMatchingConfig();
        }
    }
    class Anim_PoseMatchingNode : Anim_PlayCompositeNode
    {
        public Anim_PoseMatchingNode() : base()
        {
            NodeData = new Anim_PoseMatchingNodeData();
        }

        public override NodeData ToData()
        {
            var Dataret = base.ToData();
            Dataret.Type = "PlayPoseMatch";
            return Dataret;
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "MotionMatcAsset", FileTypeDescriptor = "PoseMatching Assets#nda", ObjectClassID1 = ClassIDType.CLASS_MotionDataAsset)]
        public string PoseMatchingData
        {
            get { return ((Anim_PoseMatchingNodeData)NodeData).PoseMacthing._Data; }
            set
            {
                if (value == "")
                    return;

                MotionMatchAsset Dataset = (MotionMatchAsset)Resource.Get(value);
                if (!Dataset.PoseMatching)
                {
                    // incompatible type 
                    string Message = string.Format("Resource type mismatch: {0}, MotionMatching Asset cannot used in PoseMatching.", value);
                    CommonDialogUI.ShowSimpleOKDialog(UIManager.GetMainUIManager(), "Tips", Message);
                }
                else
                {
                    ((Anim_PoseMatchingNodeData)NodeData).PoseMacthing._Data = value;
                    CompositePath = Dataset.Animations[0];

                    InspectorUI.GetInstance().InspectObject();

                }
            }
        }
    }
}
