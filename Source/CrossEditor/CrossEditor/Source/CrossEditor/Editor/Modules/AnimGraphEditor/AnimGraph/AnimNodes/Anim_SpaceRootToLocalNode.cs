using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_SpaceRootToLocalNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> Links;
    }
    public class Anim_SpaceRootToLocalNode : Anim_FlowNode
    {
        public Anim_SpaceRootToLocalNode() : base()
        {
            NodeData = new Anim_SpaceRootToLocalNodeData();
            AddInSlot("Root Pose", SlotType.RootPoseLink);
            AddOutSlot("Local Pose", SlotType.LocalPoseLink);
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 40;
            ContentHeight = 15;
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_SpaceRootToLocalNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.Links = new List<string>();

            var ResultSlot = FindInSlot("Root Pose");
            if (ResultSlot != null)
            {
                foreach (var Connection in ResultSlot.GetConnections())
                {
                    Data.Links.Add(Connection.ID.ToString());
                }
            }
            return Data;
        }
    }
}