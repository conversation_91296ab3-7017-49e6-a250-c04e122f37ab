using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_RootNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> InPoseLinks;

    }
    public class Anim_RootNode : Anim_FlowNode
    {
        public Anim_RootNode() : base()
        {
            NodeData = new Anim_RootNodeData();
            bOperable = false;
            AddInSlot("Result", SlotType.LocalPoseLink);
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 80;
            ContentHeight = 15;
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_RootNodeData;
            Data.Name = ID.ToString();
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.InPoseLinks = new List<string>();

            var ResultSlot = FindInSlot("Result");
            if (ResultSlot != null)
            {
                foreach (var Connection in ResultSlot.GetConnections())
                {
                    Data.InPoseLinks.Add(Connection.ID.ToString());
                }
            }

            return Data;
        }
    }
}