using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text.Json.Nodes;

namespace CrossEditor
{
    [JsonObject()]
    public class LinkData
    {
        [JsonProperty()]
        public string Name = "BaseLink";
        [JsonProperty()] 
        public string Type = "BaseLinkType";
        [JsonProperty()] 
        public string TargetNode;
        [JsonProperty()] 
        public int TargetSlot;
        [JsonProperty()] 
        public string SourceNode;
        [JsonProperty()] 
        public int SourceSlot;
    }

    [JsonObject()]
    public class NodeGraphData
    {
        [JsonProperty("Name")]
        public string Name = "NodeGraph";
        [JsonProperty("Nodes")]
        public List<NodeData> Nodes = new();
        [JsonProperty("Links")]
        public List<LinkData> Links = new();
    }

    public class NodeGraphProperty
    {
        public NodeGraphModel Owner;
    }

    class AnimGraph : NodeGraphModel
    {
        public string FilePath;
        public string HashName;
        public Anim_RootNode RootNode;
        public StoryBoardProperty Property;
        public StbDebugData DebugData;

        public AnimGraph()
        {
            Type = NodeGraphType.AnimGraph;
            FilePath = "";
            CreateAnimRootNode();
            Property = new StoryBoardProperty();
            Property.Owner = this;
            DebugData = new StbDebugData();
            IsLoadedFromXml = false;
        }

        public void CreateAnimRootNode()
        {
            RootNode = new Anim_RootNode();
            RootNode.X = 200;
            RootNode.Y = 200;

            ImportNode(RootNode);
        }

        #region Build Menu

        public override List<MenuBuilder> BuildNodeMenu(Action<Node> ProcessNode)
        {
            List<MenuBuilder> MenuBuilders = new List<MenuBuilder>();

            MenuBuilders.Add(CreateMenuBuilder<Anim_AccumulatePoseNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SwitchPosesByIntNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SwitchPosesByBoolNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SpaceRootToLocalNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SpaceLocalToRootNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_MirrorNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_BlendByLayeredFilterNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_BlendPosesNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_TransformBoneNode>(ProcessNode));
            MenuBuilders.Add(new MenuBuilder
            {
                Text = "Param Nodes",
                bHasChild = true,
                Children = BuildParamMenu(ProcessNode)
            });
            MenuBuilders.Add(new MenuBuilder
            {
                Text = "Motion Match",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    CreateMenuBuilder<Anim_AdvanceMMNode>(ProcessNode),
                    CreateMenuBuilder<Anim_PoseMatchingNode>(ProcessNode),
                    CreateMenuBuilder<Anim_MotionRecordNode>(ProcessNode)
                }
            });
            MenuBuilders.Add(new MenuBuilder
            {
                Text = "Play Animation",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    CreateMenuBuilder<Anim_EvaluateSequenceNode>(ProcessNode),
                    CreateMenuBuilder<Anim_PlaySequenceNode>(ProcessNode),
                    CreateMenuBuilder<Anim_PlayCompositeNode>(ProcessNode),
                    CreateMenuBuilder<Anim_PlayBlendSpaceNode>(ProcessNode)
                }
            });

            MenuBuilders.Add(new MenuBuilder { Text = "Create StateMachine", Event = (Sender) => { ProcessNode(new Anim_FSMNode()); } });
            MenuBuilders.Add(CreateMenuBuilder<Anim_SlotNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_GetPoseNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SavePoseNode>(ProcessNode));

            return MenuBuilders;
        }

        public MenuBuilder CreateMenuBuilder<T>(Action<Node> ProcessNode) where T : Anim_FlowNode, new()
        {
            MenuBuilder MenuBuilder = new MenuBuilder();

            // Needs node's types follow this pattern
            string type_name = typeof(T).FullName;
            string[] names = type_name.Split("_");
            string raw_name = names[names.Length - 1];

            MenuBuilder.Text = "Create " + raw_name;
            MenuBuilder.Event = (Sender) => { ProcessNode(new T()); };

            return MenuBuilder;
        }

        public List<MenuBuilder> BuildParamMenu(Action<Node> ProcessNode)
        {
            List<MenuBuilder> MenuBuilders = new List<MenuBuilder>();

            foreach (AnimParameterData Param in Property.Params)
            {
                MenuBuilders.Add(new MenuBuilder
                {
                    Text = string.Format("Create {0}<{1}>", Param.Name, Param.Type),
                    Event = (Sender) =>
                    {
                        Anim_ParamImplNode ParamImplNode = new Anim_ParamImplNode();
                        ParamImplNode.ParamName = Param.Name;
                        ParamImplNode.ParamType = (SlotSubType)Enum.Parse(typeof(SlotSubType), Param.Type);
                        ProcessNode(ParamImplNode);
                    }
                });
            }

            return MenuBuilders;
        }

        public List<MenuBuilder> BuildTransitionRuleParamMenu(Action<Node> ProcessNode)
        {
            List<MenuBuilder> MenuBuilders = new List<MenuBuilder>();

            foreach (AnimParameterData Param in Property.Params)
            {
                MenuBuilders.Add(new MenuBuilder
                {
                    Text = string.Format("Create {0}<{1}>", Param.Name, Param.Type),
                    Event = (Sender) =>
                    {
                        FlowNode_AnimParam ParamImplNode = new FlowNode_AnimParam(Param.Name);
                        ProcessNode(ParamImplNode);
                    }
                });
            }

            return MenuBuilders;
        }

        public override SlotConnectionResponse CanCreateConnection(Slot SlotA, Slot SlotB)
        {
            SlotConnectionResponse Response = base.CanCreateConnection(SlotA, SlotB);
            if (Response.Condition == ConnectCondition.Allow)
            {
                Slot InSlot = SlotA.bOutput ? SlotB : SlotA;
                if ((InSlot.SlotType == SlotType.LocalPoseLink || InSlot.SlotType == SlotType.RootPoseLink || InSlot.SlotType == SlotType.ParamImplLink) &&
                    InSlot.GetConnections().Count != 0)
                {
                    Response = new SlotConnectionResponse("Animator link can only accept input from one node", ConnectCondition.NeedBreakInSlot);
                }
            }
            return Response;
        }

        #endregion

        #region Save

        static void OrderingSavePoseNodes_Internal(Anim_FlowNode RootNode, List<Anim_SavePoseNode> OrderedSavePoseNodes)
        {
            List<Anim_FlowNode> LinkedNodes = new List<Anim_FlowNode>();
            // Get Linked Nodes
            foreach (var Slot in RootNode.GetInSlots())
            {
                foreach (var Link in Slot.GetConnections())
                {
                    var LinkNode = Link.OutSlot.Node;
                    if (LinkNode != null && LinkNode is Anim_FlowNode)
                    {
                        LinkedNodes.Add(LinkNode as Anim_FlowNode);
                    }
                }
            }

            foreach (var LinkedNode in LinkedNodes)
            {
                if (LinkedNode is Anim_GetPoseNode)
                {
                    Anim_SavePoseNode SavePoseNode = (LinkedNode as Anim_GetPoseNode).GetSavePoseNode();
                    if (SavePoseNode != null)
                    {
                        // Requeue the node we found
                        OrderedSavePoseNodes.Remove(SavePoseNode);
                        OrderedSavePoseNodes.Add(SavePoseNode);
                    }
                }
                else if (LinkedNode is Anim_FSMNode)
                {
                    // Visit state machine
                    Anim_FSMNode FSMNode = LinkedNode as Anim_FSMNode;
                    foreach (var NodeInFSM in FSMNode.StateMachineGraph.Nodes)
                    {
                        if (NodeInFSM is StateNode)
                        {
                            StateNode State = NodeInFSM as StateNode;
                            var StateRootNode = State.StateGraph.OutputNode;

                            OrderingSavePoseNodes_Internal(StateRootNode, OrderedSavePoseNodes);
                        }
                    }
                }
                else
                {
                    OrderingSavePoseNodes_Internal(LinkedNode, OrderedSavePoseNodes);
                }
            }
        }

        public void OrderingSavePoseNodes(Anim_FlowNode RootNode, List<Anim_SavePoseNode> OrderedSavePoseNodes, List<Anim_FlowNode> VisitedNodes)
        {
            Anim_SavePoseNode RootSaveNode = RootNode as Anim_SavePoseNode;
            string RootName = (RootSaveNode == null) ? RootNode.NodeName : RootSaveNode.NodeName;

            VisitedNodes.Add(RootNode);
            List<Anim_SavePoseNode> InternalOrderedNodes = new List<Anim_SavePoseNode>();

            OrderingSavePoseNodes_Internal(RootNode, InternalOrderedNodes);

            foreach (var SavePoseNode in InternalOrderedNodes)
            {
                if (VisitedNodes.Contains(SavePoseNode))
                {
                    CommonDialogUI.ShowSimpleOKDialog(UIManager.GetActiveUIManager(), "Error", string.Format("Infinite recursion detected with SavePose {0} and {1}", RootName, SavePoseNode.Name));
                }
                else
                {
                    // Requeue the node we found
                    OrderedSavePoseNodes.Remove(SavePoseNode);
                    OrderedSavePoseNodes.Add(SavePoseNode);

                    OrderingSavePoseNodes(SavePoseNode, OrderedSavePoseNodes, VisitedNodes.Clone());
                }
            }
        }

        public static NodeGraphData ModelToData(NodeGraphModel Model)
        {
            NodeGraphData Data = new NodeGraphData();
            Data.Name = "NodeGraph";
            Data.Nodes = new List<NodeData>();
            foreach (Node Node in Model.Nodes)
            {
                Data.Nodes.Add(NodeToData(Node));
            }
            Data.Links = new List<LinkData>();
            foreach (Connection Connection in Model.Connections)
            {
                Data.Links.Add(LinkTodata(Connection));
            }
            return Data;
        }

        public static NodeData NodeToData(Node Node)
        {
            NodeData Data;
            if (Node is Anim_FlowNode)
            {
                Data = (Node as Anim_FlowNode).ToData();
            }
            else
            {
                Data = new NodeData();
                Data.Name = Node.ID.ToString();
                Data.Type = Node.NodeType.ToString();
            }
            Data.PosX = Node.X;
            Data.PosY = Node.Y;
            return Data;
        }

        public static LinkData LinkTodata(Connection Link)
        {
            LinkData Data = new LinkData();
            Data.Name = Link.ID.ToString();

            // For AnimGraph ParamLink
            if (Link.InSlot.SlotType == SlotType.ParamImplLink)
            {
                Data.Type = string.Format("{0}<{1}>", Link.InSlot.SlotType.ToString(), Link.InSlot.SlotSubType.GetSlotSubTypeDesc());
            }
            else
            {
                Data.Type = Link.InSlot.SlotType.ToString();
            }
            Data.TargetNode = Link.OutSlot.Node.ID.ToString();
            Data.TargetSlot = Link.OutSlot.Index;
            Data.SourceNode = Link.InSlot.Node.ID.ToString();
            Data.SourceSlot = Link.InSlot.Index;
            return Data;
        }

        public void ArrangeByID()
        {
            Nodes.Sort((Node a, Node b) => { return a.ID.CompareTo(b.ID); });
            Connections.Sort((Connection a, Connection b) => { return a.ID.CompareTo(b.ID); });
        }

        public void GetResourceReferenceGUID(ref HashSet<string> outRef)
        {
            foreach (var node in Nodes)
            {
                if (node is Anim_FlowNode)
                {
                    (node as Anim_FlowNode).GetResourceReferenceGUID(ref outRef);
                }
            }
        }


        private void SaveToXml()
        {
            // Save to stb file
            XmlScript Xml = new XmlScript();
            Record RootRecord = Xml.GetRootRecord();

            Record RecordNodeGraph = RootRecord.AddChild();
            SaveToRecord(RecordNodeGraph);

            Record RecordProperty = RootRecord.AddChild();
            Property.SaveToXml(RecordProperty);

            Xml.Save(FilePath);
        }

        private void SaveToNda()
        {
            // Save to nda file
            List<Anim_SavePoseNode> OrderedSavePoseNodes = new List<Anim_SavePoseNode>();
            List<Anim_FlowNode> VisitedNodes = new List<Anim_FlowNode>();

            ArrangeByID();
            OrderingSavePoseNodes(RootNode, OrderedSavePoseNodes, VisitedNodes);
            foreach (Anim_SavePoseNode SavePoseNode in OrderedSavePoseNodes)
            {
                Nodes.Remove(SavePoseNode);
                Nodes.Add(SavePoseNode);
            }

            NodeGraphData Data = ModelToData(this);
            AnimatorResourceData StbData = new AnimatorResourceData();
            StbData.Nodes = Data.Nodes;
            StbData.Links = Data.Links;
            StbData.RootMotionMode = Property.RootMotionMode;
            StbData.Parameters = Property.Params;
            StbData.Name = Name;
            string JsonStr = JsonConvert.SerializeObject(StbData, Formatting.Indented);
            var hashString = new UniqueString(JsonStr);
            StbData.HashName = hashString.GetHash64().GetHashHigh().ToString() + hashString.GetHash64().GetHashLow().ToString();

            if (HashName != StbData.HashName)
            {
                GameScene.GetInstance().DebugAnimatorEntity = null;
                Property.DebugEntity = null;
                InspectorUI InspectorUI = InspectorUI.GetInstance();
                if (InspectorUI.GetObjectInspected() == Property)
                {
                    InspectorUI.InspectObject();
                }
            }
            HashName = StbData.HashName;

            JsonStr = JsonConvert.SerializeObject(StbData, Formatting.Indented);

            HashSet<string> ResourceRef = new HashSet<string>();
            GetResourceReferenceGUID(ref ResourceRef);
            string ResourceRefJson = JsonConvert.SerializeObject(ResourceRef, Formatting.Indented);

            string outputPath = FilePath.EndsWith(".nda") ? FilePath : FilePath + ".nda";

            var AnimatorPtr = Clicross.AnimationEditorUtil.AnimationCreateAnimator(JsonStr, ResourceRefJson);
            Clicross.ResourceUtil.ResourceSaveToFile(AnimatorPtr, outputPath);
            AnimatorPtr.Dispose();
        }

        public void SaveToFile()
        {
            if (FilePath != "")
            {
                SaveToNda();

                if (IsLoadedFromXml)
                    SaveToXml();

                // Refresh resource
                ProjectUI.GetInstance().RefreshListView(false);
            }
        }

        public override void SaveToRecord(Record RecordNodeGraph)
        {
            base.SaveToRecord(RecordNodeGraph);
            RecordNodeGraph.SetString("HashName", HashName);
        }

        public override void SaveNodeToRecord(Node NodeToSave, Record RecordNode)
        {
            base.SaveNodeToRecord(NodeToSave, RecordNode);

            if (NodeToSave is Anim_FlowNode)
            {
                string JsonStr = JsonConvert.SerializeObject((NodeToSave as Anim_FlowNode).ToData(), Formatting.Indented);
                RecordNode.SetString("NodeJsonData", JsonStr);
            }
        }

        #endregion

        #region Load

        public void LoadFromFile(string FilePath)
        {
            if (File.Exists(FilePath) == false)
            {
                return;
            }
            this.FilePath = FilePath;
            this.Name = PathHelper.GetNameOfPath(FilePath);

            string Content = FileHelper.ReadTextFile(FilePath);
            if (Content == "")
            {
                return;
            }
            string Extension = PathHelper.GetExtension(FilePath);
            if (StringHelper.IgnoreCaseEqual(Extension, ".stb"))
            {
                LoadFromXmlFile();
            }
            else if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
            {
                LoadFromNdaFile();

            }
        }

        private void LoadFromNdaFile()
        {
            IsLoadedFromXml = false;

            var animtorRes = Resource.Get(FilePath) as AnimatorResource;

            Property.Params = animtorRes.Parameters;
            Property.RootMotionMode = animtorRes.RootMotionMode;

            Name = animtorRes.NodeGraphData.Name;
            FromData(animtorRes.NodeGraphData);

            EditorLogger.Log(LogMessageType.Debug, "LoadFromNdabFile, is not implemented! ");
        }

        private void LoadFromXmlFile()
        {
            IsLoadedFromXml = true;

            XmlScript Xml = new XmlScript();
            Xml.Open(FilePath);
            Record RootRecord = Xml.GetRootRecord();

            Record RecordNodeGraph = RootRecord.FindByTypeString("NodeGraph");
            LoadFromRecord(RecordNodeGraph);

            Record RecordProperty = RootRecord.FindByTypeString("StbProperty");
            Property.LoadFromXml(RecordProperty);
        }

        public override void LoadFromRecord(Record RecordNodeGraph)
        {
            HashName = RecordNodeGraph.GetString("HashName");
            base.LoadFromRecord(RecordNodeGraph);

            RootNode = GetNodesOf<Anim_RootNode>()[0];
        }

        public override Node LoadNodeFromRecord(Record RecordNode)
        {
            Node Node = base.LoadNodeFromRecord(RecordNode);
            if (Node is Anim_FlowNode)
            {
                (Node as Anim_FlowNode).FromJsonData(RecordNode.GetString("NodeJsonData"));
            }
            return Node;
        }

        #endregion

        #region Load Old File

        public void LoadFromFile_Old(string FilePath)
        {
            this.FilePath = FilePath;
            this.Name = PathHelper.GetNameOfPath(FilePath);

            XmlScript Xml = new XmlScript();
            Xml.Open(FilePath);
            Record RootRecord = Xml.GetRootRecord();

            Record RecordNodeGraph = RootRecord.FindByTypeString("NodeGraph");
            LoadFromRecord_Old(RecordNodeGraph);

            Record RecordProperty = RootRecord.FindByTypeString("StbProperty");
            Property.LoadFromXml(RecordProperty);
        }

        public void LoadFromRecord_Old(Record RecordNodeGraph)
        {
            Nodes.Clear();

            NodeID = RecordNodeGraph.GetInt("NodeID");
            ConnectionID = RecordNodeGraph.GetInt("ConnectionID");

            int Count = RecordNodeGraph.GetChildCount();
            for (int i = 0; i < Count; i++)
            {
                Record RecordChild = RecordNodeGraph.GetChild(i);
                string TypeString = RecordChild.GetTypeString();
                if (TypeString == "Connection")
                {
                    LoadConnectionFromRecord_Old(RecordChild);
                }
                else
                {
                    LoadNodeFromRecord_Old(RecordChild);
                }
            }
        }

        public void LoadConnectionFromRecord_Old(Record RecordConnection)
        {
            Connection Connection = new Connection();

            Connection.ID = RecordConnection.GetInt("ID");

            int OutSlotNodeID = RecordConnection.GetInt("OutSlotNodeID");
            string OutSlotName = RecordConnection.GetString("OutSlotName");
            Slot OutSlot = FindNodeByID(OutSlotNodeID).FindOutSlot(OutSlotName);
            Connection.BindOutSlot(OutSlot);

            int InSlotNodeID = RecordConnection.GetInt("InSlotNodeID");
            string InSlotName = RecordConnection.GetString("InSlotName");
            Slot InSlot = FindNodeByID(InSlotNodeID).FindInSlot(InSlotName);
            Connection.BindInSlot(InSlot);

            AddConnection(Connection);
        }

        public void LoadNodeFromRecord_Old(Record RecordNode)
        {
            string TypeString = RecordNode.GetTypeString();
            Node Node = CreateNode(TypeString);
            if (Node == null)
            {
                DebugHelper.Assert(false);
            }
            else
            {
                Type NodeType = Node.GetType();
                PropertyInfo[] Properties = NodeType.GetProperties();
                foreach (PropertyInfo Info in Properties)
                {
                    PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(Info);
                    if (PropertyInfoAttribute.bHide == false)
                    {
                        object Value = RecordLoad(RecordNode, Info.PropertyType, Info.Name);
                        if (Value != null)
                        {
                            Info.SetValue(Node, Value);
                        }
                    }
                }
            }

            Node.ID = RecordNode.GetInt("ID");
            Node.X = RecordNode.GetInt("X");
            Node.Y = RecordNode.GetInt("Y");
            Node.SetOwner(this);

            if (RecordNode.GetChildCount() > 0 && Node.bHasSubGraph)
            {
                Record RecordSubGraph = RecordNode.FindByTypeString("StateMachine");
                if (RecordSubGraph != null)
                {
                    (Node.SubGraph as StateMachineGraph).LoadFromRecord_Old(RecordSubGraph);
                }
            }

            AddNode(Node);

            if (Node is Anim_FlowNode)
            {
                (Node as Anim_FlowNode).FromJsonData(RecordNode.GetString("NodeJsonData"));
            }

        }

        #endregion

        public bool CheckAnimParam(AnimParameterData Data)
        {
            foreach (AnimParameterData Param in Property.Params)
            {
                if (Param.Name == Data.Name && Param.Type == Data.Type)
                {
                    return true;
                }
            }

            return false;
        }

        public void RefreshDebugData()
        {
            Entity DebugEntity = GameScene.GetInstance().DebugAnimatorEntity;
            if (DebugEntity != null)
            {
                DebugData.DataItems.Clear();
                //CEEditorRuntime.AnimStbDebug_GetDebugItem(DebugEntity.World.GetNativePointer(), DebugEntity.EntityID, DebugData);
                UpdateDebugStatus(DebugData.DataItems);
            }
        }

        public void UpdateDebugStatus(vector_cross_anim_StbVisitRecord stbVisitRecords)
        {
            foreach (var item in stbVisitRecords)
            {
                string linkName = item.LinkName.GetCString();
                string toNodeName = item.ToNodeName.GetCString();
                string fromNodeName = item.FromNodeName.GetCString();
                int toNodeID = -1, linkID = -1, fromNodeID = -1;

                if (Int32.TryParse(toNodeName, out toNodeID))
                {
                    var node = FindNodeByID(toNodeID);
                    if (node != null)
                    {
                        node.SetDebugStatus();
                    }
                }
                if (Int32.TryParse(fromNodeName, out fromNodeID))
                {
                    var node = FindNodeByID(fromNodeID);
                    if (node != null)
                    {
                        node.bDebugged = true;
                        if (node is Anim_FSMNode)
                        {
                            (node as Anim_FSMNode).StateMachineGraph.UpdateDebugStatus(item.SubDataItems);
                        }
                    }
                }
                if (Int32.TryParse(linkName, out linkID))
                {
                    var link = FindConnectionByID(linkID);
                    if (link != null)
                    {
                        link.SetDebugStatus(item.Weight);
                    }
                }
            }
        }


        public void AddNodeFromData(NodeData NodeData)
        {
            string typeName = "Anim_" + NodeData.Type;
            var Node = CreateNode(typeName) as Anim_FlowNode;
            Node.SetOwner(this);
            Node.FromData(NodeData);
            AddNode(Node);
            NodeID = Math.Max(NodeID, Node.ID + 1);
        }


        public void FromData(NodeGraphData NodeGraphData)
        {
            Name = NodeGraphData.Name;

            Nodes.Clear();
            foreach (NodeData nodeData in NodeGraphData.Nodes)
            {
                AddNodeFromData(nodeData);
            }
            RootNode = GetNodesOf<Anim_RootNode>()[0];

            foreach (LinkData linkData in NodeGraphData.Links)
            {
                AddConnectionFromData(linkData);
            }
        }
    }
}
