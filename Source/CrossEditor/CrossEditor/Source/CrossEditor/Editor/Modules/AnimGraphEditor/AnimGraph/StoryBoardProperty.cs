using CEngine;
using EditorUI;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public enum RootMotionExtractionMode
    {
        DoNotExtract,
        ExtractFromEverything,
        ExtractFromSlotOnly
    }
    public enum RootMotionApplyMode
    {
        Apply,
        Ignore
    }

    public enum AnimGraphType
    {
        StoryBoard,
        StateGraph
    }


    public enum AnimSyncMethod
    {
        // Don't sync ever
        DoNotSync,

        // Use a named sync group
        SyncGroup,
    }

    public enum AnimSyncGroupRole
    {
        // This node can be the leader, as long as it has a higher blend weight than the previous best leader.
        CanBeLeader,

        // This node will always be a follower (unless there are only followers, in which case the first one ticked wins).
        AlwaysFollower,

        // This node will always be a leader (if more than one node is AlwaysLeader, the last one ticked wins). 
        AlwaysLeader,

        // This node will be excluded from the sync group while blending in. Once blended in it will be the sync group leader until blended out
        TransitionLeader,

        // This node will be excluded from the sync group while blending in. Once blended in it will be a follower until blended out
        TransitionFollower
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class RootMotionModeData
    {
        [JsonProperty("ExtractMode")]
        public string ExtractMode = "ExtractFromEverything";
        [JsonProperty("ApplyMode")]
        public string ApplyMode = "Apply";
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class AnimParameterData
    {
        string _Name = "DefaultParam";
        string _Type;

        public AnimParameterData()
        {
            _Type = GetType().Name.ToString().Replace("AnimParameter_", "");
        }
        public AnimParameterData(string name, string type)
        {
            _Name = name;
            _Type = type;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter Type", bReadOnly = true), JsonProperty("Type")]
        public string Type
        {
            get => _Type;
            set => _Type = value;
        }
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter name"), JsonProperty("Name")]
        public string Name
        {
            get => _Name;
            set => _Name = value;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class AnimParameter_Bool : AnimParameterData
    {
        [JsonProperty("Value")]
        public bool b = false;
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter value")]
        public bool Value
        {
            get => b;
            set => b = value;
        }
    }
    [JsonObject(MemberSerialization.OptIn)]
    public class AnimParameter_Int : AnimParameterData
    {
        [JsonProperty("Value")]
        public int i = 0;
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter value")]
        public int Value
        {
            get => i;
            set => i = value;
        }
    }
    [JsonObject(MemberSerialization.OptIn)]
    public class AnimParameter_Float : AnimParameterData
    {
        [JsonProperty("Value")]
        public float f = 0.1f;
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter value")]
        public float Value
        {
            get => f;
            set => f = value;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class AnimParameter_String : AnimParameterData
    {
        [JsonProperty("Value")]
        public string s = "";
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter value")]
        public string Value
        {
            get => s;
            set => s = value;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class AnimParameter_Vector2 : AnimParameterData
    {
        [JsonProperty("Value")]
        public Vector2f v = new Vector2f(0, 0);
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter value")]
        public Vector2f Value
        {
            get => v;
            set => v = value;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    class AnimParameter_Vector3 : AnimParameterData
    {
        [JsonProperty("Value")]
        public Vector3f v = new Vector3f(0, 0, 0);
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter value")]
        public Vector3f Value
        {
            get => v;
            set => v = value;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    class AnimParameter_Vector4 : AnimParameterData
    {
        [JsonProperty("Value")]
        public Vector4f v = new Vector4f(0, 0, 0, 1);
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter value")]
        public Vector4f Value
        {
            get => v;
            set => v = value;
        }
    }
    [JsonObject(MemberSerialization.OptIn)]
    class AnimParameter_Transform : AnimParameterData
    {
        [JsonProperty("Value")]
        public RTSData v = new RTSData();
        [PropertyInfo(PropertyType = "Struct", ToolTips = "Parameter value")]
        public RTSData Value
        {
            get => v;
            set => v = value;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    class AnimParameter_Customized : AnimParameterData
    {
        [JsonProperty("Value")]
        public string v = "";

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Parameter value,customized value can only be set by script", bReadOnly = true)]
        public string Value
        {
            get => v;
        }
    }

    public class RootMotionMode
    {
        [JsonConverter(typeof(StringEnumConverter))]
        [JsonProperty("ExtractMode")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Apply Mode")]
        public RootMotionExtractionMode ExtractionMode { get; set; } = RootMotionExtractionMode.ExtractFromEverything;

        [JsonConverter(typeof(StringEnumConverter))]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Extraction Mode")]
        public RootMotionApplyMode ApplyMode { get; set; } = RootMotionApplyMode.Apply;

        public RootMotionModeData ToData()
        {
            RootMotionModeData data = new RootMotionModeData();
            data.ExtractMode = ExtractionMode.ToString();
            data.ApplyMode = ApplyMode.ToString();
            return data;
        }
        public void SaveToXml(Record recordNode)
        {
            recordNode.SetUnsignedInt("ApplyMode", (uint)ApplyMode);
            recordNode.SetUnsignedInt("ExtractionMode", (uint)ExtractionMode);
        }

        public void LoadFromXml(Record recordNode)
        {
            uint v = recordNode.GetUnsignedInt("ApplyMode");
            ApplyMode = (RootMotionApplyMode)v;
            v = recordNode.GetUnsignedInt("ExtractionMode");
            ExtractionMode = (RootMotionExtractionMode)v;
        }
    }

    class StoryBoardProperty : NodeGraphProperty
    {
        RootMotionMode _RootMotionMode = new RootMotionMode();

        List<AnimParameterData> _Param = new List<AnimParameterData>();

        string _DebugEntity = "";

        [PropertyInfo(PropertyType = "StbDebugEntity", ToolTips = "Select PIE Entity to debug graph")]
        public string DebugEntity
        {
            get { return _DebugEntity; }
            set
            {
                _DebugEntity = value;
            }
        }

        [PropertyInfo(PropertyType = "ParamList", ChildPropertyType = "Struct", ToolTips = "Param.")]
        public List<AnimParameterData> Params
        {
            get => _Param; set => _Param = value;
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "RootMotionMode")]
        public RootMotionMode RootMotionMode
        {
            get => _RootMotionMode; set => _RootMotionMode = value;
        }
        public void SaveToXml(Record recordNode)
        {
            recordNode.SetTypeString("StbProperty");

            _RootMotionMode.SaveToXml(recordNode);
            foreach (var param in Params)
            {
                var paramNode = recordNode.AddChild();
                paramNode.SetTypeString("Param");
                paramNode.SetString("ParamType", param.GetType().FullName);

                string jStr = JsonConvert.SerializeObject(param, param.GetType(), Formatting.Indented, new JsonSerializerSettings());
                paramNode.SetString("ParamJson", jStr);
            }
        }

        public void LoadFromXml(Record recordNode)
        {
            _RootMotionMode.LoadFromXml(recordNode);
            _Param.Clear();

            var NumParams = recordNode.GetChildCount();
            for (int i = 0; i < NumParams; i++)
            {
                var paramNode = recordNode.GetChild(i);
                string type = paramNode.GetString("ParamType");
                string jStr = paramNode.GetString("ParamJson");
                var obj = JsonConvert.DeserializeObject(jStr, Type.GetType(type));
                _Param.Add((AnimParameterData)(obj));
            }
        }
    }
}
