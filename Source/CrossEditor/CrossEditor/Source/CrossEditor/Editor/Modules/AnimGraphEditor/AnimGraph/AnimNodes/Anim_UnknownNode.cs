using CEngine;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_UnknownNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> PoseLinks = new List<string>();
        [JsonProperty("InParamLinks")]
        public List<string> ParamLinks = new List<string>();
        [JsonProperty("InParamTypes")]
        public List<SlotSubType> InParamTypes = new List<SlotSubType>();
    }

    class Anim_UnknownNode : Anim_FlowNode
    {
        private Anim_UnknownNodeData _Data => NodeData as Anim_UnknownNodeData;
        private int _PoseLinkCount = 0;
        private int _ParamLinkCount = 0;
        public Anim_UnknownNode() : base()
        {
            NodeData = new Anim_UnknownNodeData();
            AddOutSlot("Output", SlotType.LocalPoseLink);
            InPoseCount = 0;
            InParams = new List<SlotSubType>();
        }

        public int InPoseCount
        {
            get { return _PoseLinkCount;  }
            set
            {
                int Count = Math.Clamp(value, 0, 10);
                SetPoseCount(Count);
            }
        }
        private void SetPoseCount(int Count)
        {
            while (_PoseLinkCount > Count)
            {
                RemoveInSlot(_PoseLinkCount - 1);
                _PoseLinkCount--;
            }
            while (_PoseLinkCount < Count)
            {
                string SlotName = string.Format("Pose {0}", _PoseLinkCount);
                InsertInSlot(_PoseLinkCount, SlotName, SlotType.LocalPoseLink);
                _PoseLinkCount++;
            }
        }
        
        [PropertyInfo(PropertyType = "List")]
        public List<SlotSubType> InParams
        {
            get { return _Data.InParamTypes; }
            set
            {
                _Data.InParamTypes = value;
                int Count = _Data.InParamTypes.Count;
                SetParamCount(Count);
                for (int i = 0; i < Count; i++)
                {
                    var Slot = GetInSlot(i + _PoseLinkCount);
                    Slot.SlotSubType = _Data.InParamTypes[i];
                }
            }
        }
        private void SetParamCount(int Count)
        {
            while (_ParamLinkCount > Count)
            {
                RemoveLastInSlot();
                _ParamLinkCount--;
            }
            while (_ParamLinkCount < Count)
            {
                string SlotName = string.Format("Param {0}", _ParamLinkCount);
                AddInSlot(SlotName, SlotType.ParamImplLink);
                _ParamLinkCount++;
            }
        }

        public override void FromData(NodeData NewNodeData)
        {
            base.FromData(NewNodeData);
            InParams = _Data.InParamTypes;
            InPoseCount = _Data.PoseLinks.Count;
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_UnknownNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.PoseLinks = new List<string>();
            Data.ParamLinks = new List<string>();

            foreach (var slot in InSlots)
            {
                string inputNodeID = "";
                if (slot.GetConnections().Count > 0)
                    inputNodeID = slot.GetConnections()[0].ID.ToString();

                if (slot.SlotType == SlotType.LocalPoseLink)
                    Data.PoseLinks.Add(inputNodeID);
                else
                    Data.ParamLinks.Add(inputNodeID);
            }

            return Data;
        }

    }
}