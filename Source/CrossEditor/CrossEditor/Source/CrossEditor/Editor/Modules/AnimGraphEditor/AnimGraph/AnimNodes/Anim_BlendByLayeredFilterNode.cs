using CEngine;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_BlendByLayeredFilter_FilterData
    {
        [JsonProperty("BoneName")]
        public string _BoneName = "";
        [JsonProperty("Depth")]
        public int _Depth = 1;
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Filter from which bone")]
        public string BoneName
        {
            get => _BoneName; set => _BoneName = value;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "depth > 0 for setting and  depth < 0 for resetting. Abs(depth) for weight increasing speed.")]
        public int Depth
        {
            get => _Depth; set => _Depth = value;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_BlendByLayeredFilter_LayerData
    {
        [JsonProperty("Filters")]
        public List<Anim_BlendByLayeredFilter_FilterData> _Filters = new List<Anim_BlendByLayeredFilter_FilterData>();
        [JsonProperty("Weight")]
        public float _Weight = 1.0f;

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Filters.")]
        public List<Anim_BlendByLayeredFilter_FilterData> Filters
        {
            get => _Filters; set => _Filters = value;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Layer blend weight")]
        public float Weight
        {
            get => _Weight; set => _Weight = value;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_BlendByLayeredFilterNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> Links = new List<string>();
        [JsonProperty("Layers")]
        public List<Anim_BlendByLayeredFilter_LayerData> Layers = new List<Anim_BlendByLayeredFilter_LayerData>();
    }

    public class Anim_BlendByLayeredFilterNode : Anim_FlowNode
    {
        //Anim_BlendByLayeredFilterNodeData _data;

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Layers.")]
        public List<Anim_BlendByLayeredFilter_LayerData> Layers
        {
            get
            {
                return ((Anim_BlendByLayeredFilterNodeData)NodeData).Layers;
            }
            set
            {
                ((Anim_BlendByLayeredFilterNodeData)NodeData).Layers = value;
                SetLayerNum(((Anim_BlendByLayeredFilterNodeData)NodeData).Layers.Count);
            }
        }

        private const string BASELAYER = "Base Layer";
        public Anim_BlendByLayeredFilterNode() : base()
        {
            NodeData = new Anim_BlendByLayeredFilterNodeData();
            AddOutSlot("Result", SlotType.LocalPoseLink);

            AddInSlot(BASELAYER, SlotType.LocalPoseLink);
            Layers = new List<Anim_BlendByLayeredFilter_LayerData> { new Anim_BlendByLayeredFilter_LayerData() };
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 80;
            ContentHeight = 15;
        }

        void SetLayerNum(int inLayerNum)
        {
            int CurNumPose = InSlots.Count - 1;
            if (CurNumPose > inLayerNum)
            {
                while (InSlots.Count - 1 > 0)
                {
                    RemoveLastInSlot();
                }
            }

            for (int i = InSlots.Count - 1; i < inLayerNum; i++)
            {
                string SlotName = string.Format("Layer {0}", i);
                AddInSlot(SlotName, SlotType.LocalPoseLink);
            }
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_BlendByLayeredFilterNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.Links = new List<string>();

            var BaseSlot = FindInSlot(BASELAYER);
            foreach (var BaseLink in BaseSlot.GetConnections())
            {
                Data.Links.Add(BaseLink.ID.ToString());
            }

            var PoseSlots = GetInSlots();
            foreach (var PoseSlot in PoseSlots)
            {
                if (PoseSlot.Name != BASELAYER)
                {
                    foreach (var Connection in PoseSlot.GetConnections())
                    {
                        Data.Links.Add(Connection.ID.ToString());
                    }
                }
            }
            return Data;
        }

        public override void FromData(NodeData NodeData)
        {
            base.FromData(NodeData);
            SetLayerNum(Layers.Count);
        }
    }
}