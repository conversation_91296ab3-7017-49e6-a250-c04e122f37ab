using CEngine;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_BlendPosesNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> PoseLinks = new List<string>();
        [JsonProperty("InParamLinks")]
        public List<string> ParamLinks = new List<string>();
    }

    public class Anim_BlendPosesNode : Anim_FlowNode
    {
        private int _PoseLinkNum = 0;
        private Anim_BlendPosesNodeData Data => NodeData as Anim_BlendPosesNodeData;
        public Anim_BlendPosesNode() : base()
        {
            NodeData = new Anim_BlendPosesNodeData();
            AddOutSlot("Output", SlotType.LocalPoseLink);

            SetPoseLinkNum(2);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Pose Link Num")]
        public int PoseLinkNum
        {
            get { return _PoseLinkNum; }

            set
            {
                int ClampedValue = Math.Min(value, 50);
                ClampedValue = Math.Max(ClampedValue, 2);
                SetPoseLinkNum(ClampedValue);
            }
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 60;
            ContentHeight = 15;
        }

        void SetPoseLinkNum(int inPoseLinkNum)
        {
            if (inPoseLinkNum <= 0)
                return;

            // remove slots when reduce pose num
            while (_PoseLinkNum > inPoseLinkNum)
            {
                // remove param slot from the end of the list first
                RemoveInSlot(_PoseLinkNum * 2 - 1);
                // remove pose slot at given index
                RemoveInSlot(_PoseLinkNum - 1);

                _PoseLinkNum--;
            }

            // add slots when increase pose num
            for (; _PoseLinkNum < inPoseLinkNum; _PoseLinkNum++)
            {
                // insert pose slot first
                string PoseSlotName = string.Format("Pose {0}", _PoseLinkNum);
                Slot PoseSlot = new Slot();
                PoseSlot.Name = PoseSlotName;
                PoseSlot.SlotType = SlotType.LocalPoseLink;
                PoseSlot.SlotSubType = SlotSubType.Default;
                PoseSlot.bOutput = false;
                PoseSlot.Node = this;
                PoseSlot.Index = _PoseLinkNum;

                InSlots.Insert(_PoseLinkNum, PoseSlot);

                // add param slot into the end of the list
                string ParamSlotName = string.Format("BlendWeight {0}", _PoseLinkNum);
                AddInSlot(ParamSlotName, SlotType.ParamImplLink, SlotSubType.Float);
            }

            RefreshSlotsIndex(InSlots);
        }

        public override void FromData(NodeData NewNodeData)
        {
            base.FromData(NewNodeData);
            PoseLinkNum = Data.PoseLinks.Count;
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_BlendPosesNodeData;
            Data.Name = ID.ToString();
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.PoseLinks = new List<string>();
            Data.ParamLinks = new List<string>();

            // PoseLinks
            for (int Index = 0; Index < _PoseLinkNum; ++Index)
            {
                var PoseSlot = GetInSlot(Index);

                foreach (var Link in PoseSlot.GetConnections())
                {
                    Data.PoseLinks.Add(Link.ID.ToString());
                }
            }

            // ParamLinks
            for (int Index = 0; Index < _PoseLinkNum; ++Index)
            {
                var ParamSlot = GetInSlot(Index + _PoseLinkNum);

                foreach (var Link in ParamSlot.GetConnections())
                {
                    Data.ParamLinks.Add(Link.ID.ToString());
                }
            }

            return Data;
        }
    }
}