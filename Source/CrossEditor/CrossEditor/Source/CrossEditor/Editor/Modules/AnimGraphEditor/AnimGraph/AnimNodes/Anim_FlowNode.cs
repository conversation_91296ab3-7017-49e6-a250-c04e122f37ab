using CEngine;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class NodeData
    {
        [JsonProperty()]
        public int PosX = 0;
        [JsonProperty()]
        public int PosY = 0;
        [JsonProperty("Name")]
        public string Name = "BaseNode";
        [JsonProperty("Type")]
        public string Type = "BaseNodeType";
    }

    public abstract class Anim_FlowNode : Node
    {
        public NodeData NodeData;
        public const int SpanX = 3;
        public const int SpanY = 3;
        public const int OffsetY = 2;
        public const int DefaultOneLineRectWidth = 80;
        public const int DefaultOneLineRectHeight = 30;

        [PropertyInfo(bHide = true)]
        public new string NodeName
        {
            get => Name;
        }

        public Anim_FlowNode() : base()
        {
            NodeType = NodeType.AnimNode;
            Name = TrimAnimNodeType(GetType().Name);
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = DefaultOneLineRectWidth;
            ContentHeight = DefaultOneLineRectHeight;
        }

        static public string TrimAnimNodeType(string TypeString)
        {
            const string Prefix = "Anim_";
            return TypeString.Replace(Prefix, "");
        }

        public virtual void FromData(NodeData NewNodeData)
        {
            if (NewNodeData != null && NodeData.GetType() == NewNodeData.GetType())
            {
                NodeData = NewNodeData;
                ID = int.Parse(NodeData.Name);

                if (NodeData.PosX != 0 || NodeData.PosY != 0)
                {
                    UpdatePosFromNodeData();
                }
            }
        }

        public void UpdatePosFromNodeData()
        {
            X = NodeData.PosX;
            Y = NodeData.PosY;
        }

        public virtual void FromJsonData(string JsonData)
        {
            var RealType = NodeData.GetType();
            NodeData newNodeData = JsonConvert.DeserializeObject(JsonData, RealType, new AnimNodeDataConverter()) as NodeData;
            FromData(newNodeData);
        }

        public virtual NodeData ToData()
        {
            NodeData Data = new NodeData();
            Data.Name = ID.ToString();
            Data.Type = NodeType.ToString();
            return Data;
        }

        public virtual void DoErrorCheck() { }
        public virtual void GetResourceReferenceGUID(ref HashSet<string> outRef) { }
    }
}
