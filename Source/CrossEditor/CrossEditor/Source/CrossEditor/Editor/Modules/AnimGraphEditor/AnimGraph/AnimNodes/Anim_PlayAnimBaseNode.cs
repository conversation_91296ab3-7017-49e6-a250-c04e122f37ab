using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    abstract class Anim_PlayAnimBaseNode : Anim_FlowNode
    {
        protected List<string> _UsedAnims;
        public Anim_PlayAnimBaseNode() : base()
        {
            _UsedAnims = new List<string>();
        }
        public abstract void UpdateUsedAnims();
        public List<string> GetUsedAnims() => _UsedAnims;

        public override void GetResourceReferenceGUID(ref HashSet<string> outRef)
        {
            foreach (var res in GetUsedAnims())
            {
                var guid = ResourceManager.Instance().GetGuidByPath(res);
                if (guid != null && guid.Length > 0)
                    outRef.Add(guid);
            }
        }
    }
}
