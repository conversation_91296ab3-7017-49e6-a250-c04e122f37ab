using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_SpaceLocalToRootNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> Links;
    }
    public class Anim_SpaceLocalToRootNode : Anim_FlowNode
    {
        public Anim_SpaceLocalToRootNode() : base()
        {
            NodeData = new Anim_SpaceLocalToRootNodeData();
            AddInSlot("Local Pose", SlotType.LocalPoseLink);
            AddOutSlot("Root Pose", SlotType.RootPoseLink);
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 40;
            ContentHeight = 15;
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_SpaceLocalToRootNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.Links = new List<string>();

            var ResultSlot = FindInSlot("Local Pose");
            if (ResultSlot != null)
            {
                foreach (var Connection in ResultSlot.GetConnections())
                {
                    Data.Links.Add(Connection.ID.ToString());
                }
            }
            return Data;
        }
    }
}