using System.Collections.Generic;

namespace CrossEditor
{
    public class Anim_SwitchPosesByBoolNode : Anim_FlowNode
    {
        private const string INDEX = "Active Value";

        public Anim_SwitchPosesByBoolNode() : base()
        {
            NodeData = new Anim_SwitchPosesNodeData();
            AddOutSlot("Result", SlotType.LocalPoseLink);

            AddInSlot(INDEX, SlotType.ParamImplLink, SlotSubType.Bool);
            AddInSlot("True Pose", SlotType.LocalPoseLink);
            AddInSlot("False Pose", SlotType.LocalPoseLink);
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 65;
            ContentHeight = 15;
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_SwitchPosesNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.PoseLinks = new List<string>();
            Data.ParamLinks = new List<string>();

            var IndexSlot = FindInSlot(INDEX);
            foreach (var Connection in IndexSlot.GetConnections())
            {
                Data.ParamLinks.Add(Connection.ID.ToString());
            }

            var PoseSlots = GetInSlots();
            foreach (var PoseSlot in PoseSlots)
            {
                if (PoseSlot.Name != INDEX)
                {
                    foreach (var Connection in PoseSlot.GetConnections())
                    {
                        Data.PoseLinks.Add(Connection.ID.ToString());
                    }
                }
            }

            return Data;
        }
    }
}