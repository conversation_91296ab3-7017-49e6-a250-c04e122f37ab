using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{

    [JsonObject(MemberSerialization.OptIn)]
    public class SyncParams
    {
        [JsonProperty("SyncMethod")]
        public string _SyncMethod = AnimSyncMethod.DoNotSync.ToString();

        [JsonProperty("GroupName")]
        public string _GroupName = "";

        [JsonProperty("GroupRole")]
        public string _GroupRole = AnimSyncGroupRole.CanBeLeader.ToString();

        [PropertyInfo(PropertyType = "Auto", ToolTips = "SyncMethod")]
        public AnimSyncMethod SyncMethod
        {
            get { return (AnimSyncMethod)Enum.Parse(typeof(AnimSyncMethod), _SyncMethod); }
            set { _SyncMethod = value.ToString(); }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "SyncMethod")]
        public string GroupName
        {
            get { return _GroupName; }
            set { _GroupName = value; }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "GroupRole")]
        public AnimSyncGroupRole GroupRole
        {
            get { return (AnimSyncGroupRole)Enum.Parse(typeof(AnimSyncGroupRole), _GroupRole); }
            set { _GroupRole = value.ToString(); }
        }

        public void SaveToXml(Record RecordNode)
        {
            RecordNode.SetTypeString("SyncParams");
            RecordNode.SetString("GroupName", GroupName);
            RecordNode.SetUnsignedInt("AnimSyncMethod", (uint)SyncMethod);
            RecordNode.SetUnsignedInt("AnimSyncGroupRole", (uint)GroupRole);
        }

        public void LoadFromXml(Record RecordNode)
        {
            if (RecordNode.HasAttribute("GroupName"))
            {
                GroupName = RecordNode.GetString("GroupName");
            }
            if (RecordNode.HasAttribute("AnimSyncMethod"))
            {
                SyncMethod = (AnimSyncMethod)RecordNode.GetUnsignedInt("AnimSyncMethod");
            }
            if (RecordNode.HasAttribute("AnimSyncGroupRole"))
            {
                GroupRole = (AnimSyncGroupRole)RecordNode.GetUnsignedInt("AnimSyncGroupRole");
            }
        }
    };

    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_PlayCompositeNodeData : NodeData
    {
        [JsonProperty("CompositePath")]
        public string _CompositePath = "";

        [JsonProperty("SyncParams")]
        public SyncParams _SyncParams = new SyncParams();

        [JsonProperty("Loop")]
        public bool Loop = true;

        [JsonProperty("InParamLinks")]
        public List<string> ParamLinks = new List<string>();
    }

    class Anim_PlayCompositeNode : Anim_PlayAnimBaseNode
    {
        public Anim_PlayCompositeNode() : base()
        {
            AddOutSlot("Output", SlotType.LocalPoseLink);
            AddInSlot("PlayRate", SlotType.ParamImplLink, SlotSubType.Float);
            NodeData = new Anim_PlayCompositeNodeData();
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_PlayCompositeNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.ParamLinks = new List<string>();

            var PlayRateSlot = FindInSlot("PlayRate");
            var PlayRateConnections = PlayRateSlot.GetConnections();
            Data.ParamLinks.Clear();
            if (PlayRateConnections.Count == 0)
            {

                Data.ParamLinks.Add("");
            }
            else
            {
                Data.ParamLinks.Add(PlayRateConnections[0].ID.ToString());
            }
            return Data;
        }

        public string GetStringContent()
        {
            string Content = AnimUtil.TrimAnimPath(PathHelper.GetFileName(CompositePath));
            return Content.Length == 0 ? "Anim" : Content;
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = GetStringContent();
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, GetStringContent(), Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }

        public override void UpdateUsedAnims()
        {
            _UsedAnims.Clear();
            _UsedAnims.Add(CompositePath);
        }

        // Move these two properties into anim_node, level up ui
        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Animation Composite", FileTypeDescriptor = "Animation Assets#nda", ObjectClassID1 = ClassIDType.CLASS_AnimCompositeRes)]
        public string CompositePath
        {
            get { return ResourceManager.Instance().ConvertGuidToPath(((Anim_PlayCompositeNodeData)NodeData)._CompositePath); }
            set
            {
                ((Anim_PlayCompositeNodeData)NodeData)._CompositePath = value;
                UpdateUsedAnims();
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "SyncParams")]
        public SyncParams SyncParams
        {
            get { return ((Anim_PlayCompositeNodeData)NodeData)._SyncParams; }
            set { ((Anim_PlayCompositeNodeData)NodeData)._SyncParams = value; }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Loop")]
        public bool Loop
        {
            get { return ((Anim_PlayCompositeNodeData)NodeData).Loop; }
            set { ((Anim_PlayCompositeNodeData)NodeData).Loop = value; }
        }
    }
}
