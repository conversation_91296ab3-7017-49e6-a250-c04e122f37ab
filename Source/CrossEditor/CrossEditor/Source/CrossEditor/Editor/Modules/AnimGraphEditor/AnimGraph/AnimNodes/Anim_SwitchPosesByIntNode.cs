using CEngine;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_SwitchPosesNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> PoseLinks = new List<string>();
        [JsonProperty("InParamLinks")]
        public List<string> ParamLinks = new List<string>();
    }

    public class Anim_SwitchPosesByIntNode : Anim_FlowNode
    {
        private int _PoseLinkNum = 0;
        private const string INDEX = "Active Index";

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Pose Num")]
        public int NumPose
        {
            get { return _PoseLinkNum; }

            set
            {
                int ClampedValue = Math.Min(value, 50);
                ClampedValue = Math.Max(ClampedValue, 1);
                SetPoseNum(ClampedValue);
            }
        }

        public Anim_SwitchPosesByIntNode() : base()
        {
            NodeData = new Anim_SwitchPosesNodeData();
            AddOutSlot("Result", SlotType.LocalPoseLink);
            AddInSlot(INDEX, SlotType.ParamImplLink, SlotSubType.Int);

            NumPose = 2;
        }

        private void SetPoseNum(int PoseNum)
        {
            if (PoseNum <= 0)
                return;

            // remove slots when reduce pose num
            while (_PoseLinkNum > PoseNum)
            {
                RemoveLastInSlot();

                _PoseLinkNum--;
            }

            // add slots when increase pose num
            for (; _PoseLinkNum < PoseNum; _PoseLinkNum++)
            {
                string SlotName = string.Format("Pose {0}", _PoseLinkNum);
                AddInSlot(SlotName, SlotType.LocalPoseLink);
            }
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 60;
            ContentHeight = 15;
        }


        public override void FromData(NodeData NewData)
        {
            base.FromData(NewData);
            var Data = NewData as Anim_SwitchPosesNodeData;
            NumPose = Data.PoseLinks.Count;
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_SwitchPosesNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.PoseLinks = new List<string>();
            Data.ParamLinks = new List<string>();

            var IndexSlot = FindInSlot(INDEX);
            foreach (var Connection in IndexSlot.GetConnections())
            {
                Data.ParamLinks.Add(Connection.ID.ToString());
            }

            var PoseSlots = GetInSlots();
            foreach (var PoseSlot in PoseSlots)
            {
                if (PoseSlot.Name != INDEX)
                {
                    foreach (var Connection in PoseSlot.GetConnections())
                    {
                        Data.PoseLinks.Add(Connection.ID.ToString());
                    }
                }
            }

            return Data;
        }
    }
}