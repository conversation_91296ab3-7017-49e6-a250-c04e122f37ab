using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_MirrorNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> Links;
        [JsonProperty("Axis")]
        public string Axis = "X";
    }
    public enum MirrorAxis
    {
        X,
        Y,
        Z
    }

    public class Anim_MirrorNode : Anim_FlowNode
    {
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Mirror axis. e.g, X means mirror around YZ-plane")]
        public MirrorAxis Axis
        {
            get => (MirrorAxis)Enum.Parse(typeof(MirrorAxis), ((Anim_MirrorNodeData)NodeData).Axis);
            set => ((Anim_MirrorNodeData)NodeData).Axis = value.ToString();
        }

        public Anim_MirrorNode() : base()
        {
            NodeData = new Anim_MirrorNodeData();
            AddInSlot("Input", SlotType.LocalPoseLink);
            AddOutSlot("Result", SlotType.RootPoseLink);
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = DefaultOneLineRectWidth / 2;
            ContentHeight = DefaultOneLineRectHeight;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, Axis.ToString(), Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_MirrorNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.Links = new List<string>();

            var ResultSlot = FindInSlot("Input");
            if (ResultSlot != null)
            {
                foreach (var Connection in ResultSlot.GetConnections())
                {
                    Data.Links.Add(Connection.ID.ToString());
                }
            }
            return Data;
        }
    }
}