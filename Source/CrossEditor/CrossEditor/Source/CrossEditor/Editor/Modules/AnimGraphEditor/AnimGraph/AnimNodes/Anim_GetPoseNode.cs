using CEngine;
using EditorUI;
using Newtonsoft.Json;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_GetPoseNodeData : NodeData
    {
        [JsonProperty("PoseName")]
        public string PoseName = "DefaultPose";
    }

    public class Anim_GetPoseNode : Anim_FlowNode
    {
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Saved Pose Name")]
        public string PoseName
        {
            get => ((Anim_GetPoseNodeData)NodeData).PoseName;
            set => ((Anim_GetPoseNodeData)NodeData).PoseName = value;
        }

        public Anim_GetPoseNode() : base()
        {
            NodeData = new Anim_GetPoseNodeData();
            AddOutSlot("Result", SlotType.LocalPoseLink);
        }

        public Anim_SavePoseNode GetSavePoseNode()
        {
            var SavePoseNodes = Owner.GetNodesOf<Anim_SavePoseNode>();
            foreach (var SavePoseNode in SavePoseNodes)
            {
                if (SavePoseNode.PoseName == PoseName)
                {
                    return SavePoseNode;
                }
            }
            return null;
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = PoseName;
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, PoseName, Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_GetPoseNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            return Data;
        }
    }
}