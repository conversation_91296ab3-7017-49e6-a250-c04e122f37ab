using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_FSMNodeData : NodeData
    {
        [JsonProperty("FSM")]
        public StateMachineData StateMachineData;
    }
    public class Anim_FSMNode : Anim_FlowNode
    {
        string _FSMName = "DefaultFSM";

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Saved Pose Name")]
        public string FSMName
        {
            get => _FSMName;
            set
            {
                _FSMName = value;
                StateMachineGraph.Name = value;
            }
        }

        [PropertyInfo(bHide = true)]
        public StateMachineGraph StateMachineGraph
        {
            get => SubGraph as StateMachineGraph;
        }

        public Anim_FSMNode() : base()
        {
            AddOutSlot("Output", SlotType.LocalPoseLink);

            SetSubGraph(new StateMachineGraph());

            RenameEvent = () =>
            {
                // Do nothing.
            };

            NodeData = new Anim_FSMNodeData();
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = _FSMName;
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, _FSMName, Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_FSMNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.StateMachineData = StateMachineGraph.ToData();
            return Data;
        }

        public override void FromData(NodeData NewNodeData)
        {
            base.FromData(NewNodeData);

            if (NewNodeData is Anim_FSMNodeData FSMNodeData)
            {
                if (StateMachineGraph != null && FSMNodeData != null && FSMNodeData.StateMachineData != null)
                {
                    StateMachineGraph.FromData(FSMNodeData.StateMachineData);
                    _FSMName = FSMNodeData.StateMachineData.Name;
                }
            }
        }

        public override void GetResourceReferenceGUID(ref HashSet<string> outRef)
        {
            StateMachineGraph.GetResourceReferenceGUID(ref outRef);
        }
    }
}