using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    class Anim_SlotNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> PoseLinks;
        [JsonProperty("SlotName")]
        public string SlotName = "DefaultSlot";
    }

    class Anim_SlotNode : Anim_FlowNode
    {
        private string _GroupName = "DefaultGroup";

        public Anim_SlotNode() : base()
        {
            NodeData = new Anim_SlotNodeData();
            AddInSlot("Source", SlotType.LocalPoseLink);
            AddOutSlot("Result", SlotType.LocalPoseLink);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Slot name")]
        public string SlotName
        {
            get => ((Anim_SlotNodeData)NodeData).SlotName;
            set => ((Anim_SlotNodeData)NodeData).SlotName = value;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Group name")]
        public string GroupName
        {
            get
            {
                return _GroupName;
            }
            set
            {
                _GroupName = value;
            }
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_SlotNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.PoseLinks = new List<string>();

            var SourceSlot = FindInSlot("Source");
            if (SourceSlot != null)
            {
                if (SourceSlot.GetConnections().Count > 0)
                {
                    Data.PoseLinks.Add(SourceSlot.GetConnections()[0].ID.ToString());
                }
            }

            Data.SlotName = SlotName;

            return Data;
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = _GroupName + "." + SlotName;
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            string ShowStr = _GroupName + "." + SlotName;
            GraphicsHelper.DrawString(UIManager, null, ShowStr, Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }
    }
}
