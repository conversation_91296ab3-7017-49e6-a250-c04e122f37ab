using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_SavePoseNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> Links;
        [JsonProperty("PoseName")]
        public string PoseName = "DefaultPose";

    }
    public class Anim_SavePoseNode : Anim_FlowNode
    {
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Saved Pose Name")]
        public string PoseName
        {
            get => ((Anim_SavePoseNodeData)NodeData).PoseName;
            set => ((Anim_SavePoseNodeData)NodeData).PoseName = value;
        }
        public Anim_SavePoseNode() : base()
        {
            NodeData = new Anim_SavePoseNodeData();
            AddInSlot("Save Pose", SlotType.LocalPoseLink);
        }
        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = PoseName;
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, PoseName, Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_SavePoseNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.Links = new List<string>();

            var ResultSlot = FindInSlot("Save Pose");
            if (ResultSlot != null)
            {
                foreach (var Connection in ResultSlot.GetConnections())
                {
                    Data.Links.Add(Connection.ID.ToString());
                }
            }
            return Data;
        }
    }
}