using Newtonsoft.Json;
using System.Collections.Generic;
namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_MotionRecordNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> PoseLinks;
    }

    class Anim_MotionRecordNode : Anim_FlowNode
    {
        private const string InPose = "InPose";

        public Anim_MotionRecordNode() : base()
        {
            AddOutSlot("Result", SlotType.LocalPoseLink);
            AddInSlot(InPose, SlotType.LocalPoseLink);
            NodeData = new Anim_MotionRecordNodeData();
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = 40;
            ContentHeight = 20;
        }

        public override NodeData ToData()
        {
            var Data = new Anim_MotionRecordNodeData();
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.PoseLinks = new List<string>();

            var PoseSlots = GetInSlots();
            foreach (var PoseSlot in PoseSlots)
            {
                foreach (var Connection in PoseSlot.GetConnections())
                {
                    Data.PoseLinks.Add(Connection.ID.ToString());
                }
            }

            return Data;
        }
    }
}
