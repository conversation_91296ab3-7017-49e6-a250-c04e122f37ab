using EditorUI;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    class StoryBoardUI
    {
        static List<StoryBoardUI> InstanceList = new List<StoryBoardUI> { new StoryBoardUI("StoryBoard") };

        AnimGraph AnimGraph;
        NodeGraphView View;

        public static StoryBoardUI GetInstance()
        {
            return InstanceList[0];
        }

        public static StoryBoardUI CreateInstance()
        {
            InstanceList.Add(new StoryBoardUI());
            return InstanceList.Last();
        }

        public StoryBoardUI()
        {
            AnimGraph = new AnimGraph();
            View = new NodeGraphView();
            View.BindModel(AnimGraph);
        }

        public StoryBoardUI(string Name) : this()
        {
            AnimGraph.Name = Name;
            View.UpdateDockingCardText();
            View.RefreshNavigator();
        }

        public void Initialize()
        {
            View.SaveEvent += OnSave;
            View.InspectPropertyEvent += OnInspectPreoperty;
            View.DoubleClickEvent += OnDoubleClick;
            // For state machine
            View.BeginConnectEvent += OnBeginConnect;
            View.ConnectingEvent += OnConnecting;
            View.HoverEvent += OnHover;
        }

        public DockingCard GetDockingCard() => View.GetDockingCard();

        public NodeGraphView GetGraphView() => View;

        #region Event

        public void OnSave()
        {
            AnimGraph.SaveToFile();
            View.ClearModified();
        }

        public void OnInspectPreoperty()
        {
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            if (InspectorUI.GetObjectInspected() != AnimGraph.Property)
            {
                InspectorUI.SetObjectInspected(AnimGraph.Property);
                InspectorUI.InspectObject();
            }
        }

        public void OnDoubleClick(object Context)
        {
            if (Context is Node)
            {
                Node Node = Context as Node;
                if (Node.bHasSubGraph)
                {
                    Node.SubGraph.Camera.RecoverToOriZoom();
                    View.BindModel(Node.SubGraph);
                }
            }
        }

        public void OnBeginConnect(object Context)
        {
            if (Context is SlotWrapper)
            {
                View.SlotA = ((SlotWrapper)Context).OutSlot;
                View.CurrentConnection = new Transition();
                View.CurrentConnection.OutSlot = View.SlotA;
            }
        }

        public void OnConnecting(object Context)
        {
            if (Context is SlotWrapper)
            {
                View.SetHoverSlot(((SlotWrapper)Context).InSlot);
            }
        }

        public void OnHover(object HoverObject)
        {
            if (HoverObject is SlotWrapper)
            {
                View.SetHoverSlot(((SlotWrapper)HoverObject).InSlot);
                View.SetCursor(SystemCursor.Cross);
            }
        }

        #endregion

        public void OpenStoryBoard(string FilePath)
        {
            // For old stb file
            //AnimGraph.LoadFromFile_Old(FilePath);
            AnimGraph.LoadFromFile(FilePath);
            View.UpdateDockingCardText();
            View.RefreshNavigator();
            View.RefreshZoom();

            GetDockingCard().SetTagString1(FilePath);
        }

        public void RefreshDebugData()
        {
            foreach (DockingCard Card in MainUI.GetInstance().FindDockingCardsByFileSuffix(".stb"))
            {
                NodeGraphView CardView = Card.GetTagObject() as NodeGraphView;
                NodeGraphModel GraphModel = CardView.GetModel();
                while (GraphModel != null && GraphModel.GetOwner() != null)
                {
                    GraphModel = GraphModel.GetOwner().GetOwner();
                }
                if (GraphModel is AnimGraph AnimGraph)
                    AnimGraph.RefreshDebugData();
            }
        }
    }
}
