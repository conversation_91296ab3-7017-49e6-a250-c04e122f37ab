using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    class CaptureComparerUI : DockingUI
    {
        const string Const_InvalidSizeText = "";

        static CaptureComparerUI _Instance = new CaptureComparerUI();

        public Panel _Container;

        Button _ButtonPageCaptures;
        Button _ButtonPageLeftCapture;
        Button _ButtonPageDifference;
        Button _ButtonPageRightCapture;
        Button _ButtonPageSlide;

        Panel _PanelPageCaptures;
        Panel _PanelPageLeftCapture;
        Panel _PanelPageDifference;
        Panel _PanelPageRightCapture;
        Panel _PanelPageSlide;

        ComboBox _ComboBoxCaptureName;
        //ComboBox _ComboBoxCaptureBookmark;
        ComboBox _ComboBoxCaptureCamera;
        ComboBox _ComboBoxCaptureVisualize;
        ComboBox _ComboBoxCaptureSize;
        ComboBox _ComboBoxCaptureFramesToSkip;
        Button _ButtonCapture;
        ProgressBar _ProgressBarCapture;
        But<PERSON> _ButtonCompare;
        Label _LabelStatistics;
        Button _ButtonCopyStatistics;
        Tree _TreeLeftCapture;
        Tree _TreeRightCapture;
        Label _LabelLeftCaptureSize;
        Label _LabelRightCaptureSize;
        Panel _PanelLeftCaptureImage;
        Panel _PanelDifferenceImage;
        Panel _PanelRightCaptureImage;

        Panel _PanelLeftCaptureImageBig;
        Panel _PanelRightCaptureImageBig;
        Panel _PanelDifferenceImageBig;
        ComparePanel _ComparePanelDifferenceImageBig;

        Texture _TextureTreeItemFoldedFolder;
        Texture _TextureTreeItemExpandedFolder;
        Texture _TextureTreeItemFile;

        Tree _CurrentTreeCapture;

        bool _LeftMouseDown;
        int _SavedMouseX;
        int _SavedMouseY;
        float _SavedOffsetX;
        float _SavedOffsetY;

        float _OffsetX;
        float _OffsetY;
        float _ScaleX;
        float _ScaleY;

        public static CaptureComparerUI GetInstance()
        {
            return _Instance;
        }

        public CaptureComparerUI()
        {
            _LeftMouseDown = false;
            _SavedMouseX = 0;
            _SavedMouseY = 0;
            _SavedOffsetX = 0.0f;
            _SavedOffsetY = 0.0f;

            ResetOffsetAndScale();
        }

        public bool Initialize()
        {
            _Container = new Panel();
            _Container.Initialize();
            _Container.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _Container.ApplicationActivateEvent += OnContainerApplicationActivate;

            _ButtonPageCaptures = CreatePageButton(18, "Captures");
            _ButtonPageLeftCapture = CreatePageButton(18 + 103, "Left");
            _ButtonPageDifference = CreatePageButton(18 + 103 * 2, "Difference");
            _ButtonPageRightCapture = CreatePageButton(18 + 103 * 3, "Right");
            _ButtonPageSlide = CreatePageButton(18 + 103 * 4, "Slide");

            _PanelPageCaptures = CreatePagePanel(false);
            _PanelPageLeftCapture = CreatePagePanel(true);
            _PanelPageDifference = CreatePagePanel(true);
            _PanelPageRightCapture = CreatePagePanel(true);
            _PanelPageSlide = CreatePagePanel(true);

            EnablePageScrolling(_PanelPageLeftCapture);
            EnablePageScrolling(_PanelPageDifference);
            EnablePageScrolling(_PanelPageRightCapture);
            EnablePageScrolling(_PanelPageSlide);

            _ButtonPageCaptures.SetTagObject(_PanelPageCaptures);
            _ButtonPageLeftCapture.SetTagObject(_PanelPageLeftCapture);
            _ButtonPageDifference.SetTagObject(_PanelPageDifference);
            _ButtonPageRightCapture.SetTagObject(_PanelPageRightCapture);
            _ButtonPageSlide.SetTagObject(_PanelPageSlide);

            _ComboBoxCaptureName = new ComboBox();
            _ComboBoxCaptureName.Initialize();
            _ComboBoxCaptureName.AddItem("Default");
            _ComboBoxCaptureName.SetPosition(18, 18, 100, 16);
            _ComboBoxCaptureName.SetSelectedItemByText("Default");
            _ComboBoxCaptureName.DropDownEvent += OnComboBoxCaptureNameDropDown;
            _PanelPageCaptures.AddChild(_ComboBoxCaptureName);
            _ComboBoxCaptureName.GetValueEdit().SetReadOnly(false);
            _ComboBoxCaptureName.GetValueEdit().SetEnable(true);

            // Combined camera and bookmark combo box
            _ComboBoxCaptureCamera = new ComboBox();
            _ComboBoxCaptureCamera.Initialize();
            _ComboBoxCaptureCamera.AddItem("EditorCamera");
            _ComboBoxCaptureCamera.SetPosition(138, 18, 220, 16); // Wider to accommodate both options
            _ComboBoxCaptureCamera.SetSelectedItemByText("EditorCamera");
            _ComboBoxCaptureCamera.DropDownEvent += ********************************;
            _PanelPageCaptures.AddChild(_ComboBoxCaptureCamera);

            _ComboBoxCaptureVisualize = new ComboBox();
            _ComboBoxCaptureVisualize.Initialize();

            var VisulizeEnums = Enum.GetNames(typeof(ViewModeVisualizeType));
            foreach (var name in VisulizeEnums)
            {
                _ComboBoxCaptureVisualize.AddItem(name);
            }

            _ComboBoxCaptureVisualize.SetSelectedItemIndex(0);
            _ComboBoxCaptureVisualize.SetPosition(378, 18, 100, 16);
            _PanelPageCaptures.AddChild(_ComboBoxCaptureVisualize);
            
            _ComboBoxCaptureSize = new ComboBox();
            _ComboBoxCaptureSize.Initialize();
            _ComboBoxCaptureSize.AddItem("1280x720");
            _ComboBoxCaptureSize.AddItem("1920x1080");
            _ComboBoxCaptureSize.AddItem("2560x1440");
            _ComboBoxCaptureSize.AddItem("3840x2160");
            _ComboBoxCaptureSize.AddItem("7680x4320");
            _ComboBoxCaptureSize.AddItem("Custom");

            _ComboBoxCaptureSize.SetPosition(498, 18, 100, 16);
            _ComboBoxCaptureSize.SetSelectedItemByText("3840x2160");
            _ComboBoxCaptureSize.GetValueEdit().SetReadOnly(false);
            _ComboBoxCaptureSize.GetValueEdit().SetEnable(true);
            _ComboBoxCaptureSize.DropDownEvent += OnComboBoxCaptureSizeDropDown;
            _PanelPageCaptures.AddChild(_ComboBoxCaptureSize);

            _ComboBoxCaptureFramesToSkip = new ComboBox();
            _ComboBoxCaptureFramesToSkip.Initialize();
            _ComboBoxCaptureFramesToSkip.AddItem("Wait 1 frame");
            _ComboBoxCaptureFramesToSkip.AddItem("Wait 10 frames");
            _ComboBoxCaptureFramesToSkip.AddItem("Wait 20 frames");
            _ComboBoxCaptureFramesToSkip.AddItem("Wait 50 frames");
            _ComboBoxCaptureFramesToSkip.AddItem("Wait 100 frames");
            _ComboBoxCaptureFramesToSkip.SetPosition(618, 18, 100, 16);
            _ComboBoxCaptureFramesToSkip.SetSelectedItemByText("Wait 50 frames");
            _PanelPageCaptures.AddChild(_ComboBoxCaptureFramesToSkip);

            _ProgressBarCapture = new ProgressBar();
            _ProgressBarCapture.SetPosition(738, 16, 100, 20);
            _ProgressBarCapture.SetProgress(0.0f);
            _ProgressBarCapture.SetBackgroundColor(Color.FromRGBA(255, 255, 255, 0));
            _ProgressBarCapture.SetBarColor(Color.FromRGBA(34, 111, 255, 128));
            _PanelPageCaptures.AddChild(_ProgressBarCapture);

            _ButtonCapture = new Button();
            _ButtonCapture.Initialize();
            _ButtonCapture.SetFontSize(16);
            _ButtonCapture.SetText("Capture");
            _ButtonCapture.SetTextOffsetY(2);
            _ButtonCapture.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonCapture.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            _ButtonCapture.SetPosition(858, 16, 100, 20);
            _ButtonCapture.ClickedEvent += OnButtonCaptureClicked;
            _PanelPageCaptures.AddChild(_ButtonCapture);

            _ButtonCompare = new Button();
            _ButtonCompare.Initialize();
            _ButtonCompare.SetFontSize(16);
            _ButtonCompare.SetText("Compare");
            _ButtonCompare.SetTextOffsetY(2);
            _ButtonCompare.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonCompare.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            _ButtonCompare.SetPosition(454, 50, 100, 20);
            _ButtonCompare.ClickedEvent += OnButtonCompareClicked;
            _PanelPageCaptures.AddChild(_ButtonCompare);

            _LabelStatistics = new Label();
            _LabelStatistics.SetPosition(440, 120, 140, 100);
            _LabelStatistics.SetMultiLine(true);
            _LabelStatistics.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelStatistics.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _PanelPageCaptures.AddChild(_LabelStatistics);

            _ButtonCopyStatistics = new Button();
            _ButtonCopyStatistics.Initialize();
            _ButtonCopyStatistics.SetFontSize(16);
            _ButtonCopyStatistics.SetText("Copy");
            _ButtonCopyStatistics.SetTextOffsetY(2);
            _ButtonCopyStatistics.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonCopyStatistics.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            _ButtonCopyStatistics.SetPosition(454, 370, 100, 20);
            _ButtonCopyStatistics.ClickedEvent += OnButtonCopyStatisticsClicked;
            _PanelPageCaptures.AddChild(_ButtonCopyStatistics);

            _TreeLeftCapture = new Tree();
            _TreeLeftCapture.Initialize();
            _TreeLeftCapture.SetPosition(18, 50, 390, 340);
            _TreeLeftCapture.SetBackgroundColor(Color.EDITOR_UI_EDIT_BACK_COLOR);
            _PanelPageCaptures.AddChild(_TreeLeftCapture);
            _TreeLeftCapture.ItemSelectedEvent += OnTreeLeftCaptureItemSelected;
            _TreeLeftCapture.RightMouseUpEvent += OnTreeCaptureXRightMouseUp;

            _TreeRightCapture = new Tree();
            _TreeRightCapture.Initialize();
            _TreeRightCapture.SetPosition(605, 50, 390, 340);
            _TreeRightCapture.SetBackgroundColor(Color.EDITOR_UI_EDIT_BACK_COLOR);
            _PanelPageCaptures.AddChild(_TreeRightCapture);
            _TreeRightCapture.ItemSelectedEvent += OnTreeRightCaptureItemSelected;
            _TreeRightCapture.RightMouseUpEvent += OnTreeCaptureXRightMouseUp;

            _TextureTreeItemFoldedFolder = UIManager.LoadUIImage("Editor/Tree/Project/FoldedFolder.png");
            _TextureTreeItemExpandedFolder = UIManager.LoadUIImage("Editor/Tree/Project/Folder.png");
            _TextureTreeItemFile = UIManager.LoadUIImage("Editor/Tree/Project/File.png");

            UpdateCaptureTrees();

            _LabelLeftCaptureSize = new Label();
            _LabelLeftCaptureSize.SetPosition(185, 400, 177, 20);
            _LabelLeftCaptureSize.SetTextAlign(TextAlign.CenterCenter);
            _LabelLeftCaptureSize.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelLeftCaptureSize.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _LabelLeftCaptureSize.SetText(Const_InvalidSizeText);
            _PanelPageCaptures.AddChild(_LabelLeftCaptureSize);

            _LabelRightCaptureSize = new Label();
            _LabelRightCaptureSize.SetPosition(647, 400, 177, 20);
            _LabelRightCaptureSize.SetTextAlign(TextAlign.CenterCenter);
            _LabelRightCaptureSize.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelRightCaptureSize.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _LabelRightCaptureSize.SetText(Const_InvalidSizeText);
            _PanelPageCaptures.AddChild(_LabelRightCaptureSize);

            _PanelLeftCaptureImage = CreateImagePanel(185, 423, _PanelPageCaptures);
            _PanelLeftCaptureImage.LeftMouseDoubleClickedEvent += OnPanelLeftCaptureLeftMouseDoubleClicked;

            _PanelDifferenceImage = CreateImagePanel(417, 423, _PanelPageCaptures);
            _PanelDifferenceImage.LeftMouseDoubleClickedEvent += OnPanelDifferenceLeftMouseDoubleClicked;

            _PanelRightCaptureImage = CreateImagePanel(647, 423, _PanelPageCaptures);
            _PanelRightCaptureImage.LeftMouseDoubleClickedEvent += OnPanelRightCaptureLeftMouseDoubleClicked;

            _PanelLeftCaptureImageBig = CreateImagePanel(0, 0, _PanelPageLeftCapture);

            _PanelRightCaptureImageBig = CreateImagePanel(0, 0, _PanelPageRightCapture);

            _PanelDifferenceImageBig = CreateImagePanel(0, 0, _PanelPageDifference);

            _ComparePanelDifferenceImageBig = CreateImageComparePanel(0, 0, _PanelPageSlide);

            SwitchToPage(_ButtonPageCaptures);

            base.Initialize("Capture", _Container);

            UpdateLayout();

            return true;
        }

        public Button CreatePageButton(int X, string Text)
        {
            Button PageButton = new Button();
            PageButton.Initialize();
            PageButton.SetFontSize(16);
            PageButton.SetText(Text);
            PageButton.SetTextOffsetY(2);
            PageButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            PageButton.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            PageButton.SetPosition(X, 18, 100, 20);
            PageButton.ClickedEvent += OnButtonPageXClicked;
            _Container.AddChild(PageButton);
            return PageButton;
        }

        Panel CreatePagePanel(bool bHasBorder)
        {
            Panel PanelPage = new Panel();
            PanelPage.Initialize();
            PanelPage.SetPosition(0, 38, 1024, 1024);
            if (bHasBorder)
            {
                PanelPage.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            }
            _Container.AddChild(PanelPage);
            return PanelPage;
        }

        void ActivatePageButton(Button PageButton)
        {
            PageButton.SetBorderColor(Color.EDITOR_UI_DOCKING_CARD_HILIGHT_TITLE_COLOR);
            PageButton.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
        }

        void DeactivatePageButton(Button PageButton)
        {
            PageButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            PageButton.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
        }

        void SwitchToPage(Button PageButton)
        {
            DeactivatePageButton(_ButtonPageCaptures);
            DeactivatePageButton(_ButtonPageLeftCapture);
            DeactivatePageButton(_ButtonPageRightCapture);
            DeactivatePageButton(_ButtonPageDifference);
            DeactivatePageButton(_ButtonPageSlide);

            ActivatePageButton(PageButton);

            _PanelPageCaptures.SetVisible(false);
            _PanelPageLeftCapture.SetVisible(false);
            _PanelPageRightCapture.SetVisible(false);
            _PanelPageDifference.SetVisible(false);
            _PanelPageSlide.SetVisible(false);

            Panel PagePanel = (Panel)PageButton.GetTagObject();
            PagePanel.SetVisible(true);
        }

        Panel CreateImagePanel(int X, int Y, Control Parent)
        {
            Panel PanelImage = new Panel();
            PanelImage.Initialize();
            PanelImage.SetPosition(X, Y, 177, 100);
            PanelImage.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            Parent.AddChild(PanelImage);
            return PanelImage;
        }

        ComparePanel CreateImageComparePanel(int X, int Y, Control Parent)
        {
            ComparePanel ComparePanelImage = new ComparePanel();
            ComparePanelImage.Initialize();
            ComparePanelImage.SetPosition(X, Y, 177, 100);
            ComparePanelImage.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            Parent.AddChild(ComparePanelImage);
            return ComparePanelImage;
        }

        void OnButtonPageXClicked(Button Sender)
        {
            Button PageButton = Sender;
            SwitchToPage(PageButton);
        }

        public void UpdateCaptureTrees()
        {
            string CaptureDirectory = SceneCapturer.GetInstance().GetCaptureDirectory();
            UpdateCaptureTree(_TreeLeftCapture, CaptureDirectory);
            UpdateCaptureTree(_TreeRightCapture, CaptureDirectory);
        }

        void UpdateCaptureTree(Tree CaptureTree, string CaptureDirectory)
        {
            TreeState TreeState = CaptureTree.SaveState();
            TreeItem RootItem = CaptureTree.GetRootItem();
            RootItem.ClearChildren();
            ShowDirectory(CaptureTree, RootItem, CaptureDirectory);
            CaptureTree.LoadState(TreeState);
            RootItem.SetExpanded(true);
        }

        void ShowDirectory(Tree CaptureTree, TreeItem TreeItem, string DirectoryPath)
        {
            DirectoryPath = PathHelper.ToStandardForm(DirectoryPath);
            string DirectoryName = PathHelper.GetFileName(DirectoryPath);
            if (DirectoryPath == "")
            {
                DirectoryName = "Empty";
            }
            TreeItem.SetFolder(true);
            TreeItem.SetExpanded(false);
            TreeItem.SetText(DirectoryName);
            TreeItem.SetImageFolded(_TextureTreeItemFoldedFolder);
            TreeItem.SetImageExpanded(_TextureTreeItemExpandedFolder);
            TreeItem.SetTagString(DirectoryPath);
            if (DirectoryHelper.IsDirectoryExists(DirectoryPath))
            {
                DirectoryInfo[] SubDirectoryInfos = DirectoryHelper.GetSubDirectories(DirectoryPath);
                int Count = SubDirectoryInfos.Length;
                for (int i = 0; i < Count; i++)
                {
                    DirectoryInfo SubDirectoryInfo = SubDirectoryInfos[i];
                    TreeItem TreeItemDirectory = CaptureTree.CreateItem();
                    TreeItem.AddChild(TreeItemDirectory);
                    ShowDirectory(CaptureTree, TreeItemDirectory, SubDirectoryInfo.FullName);
                }
                if (Count == 0)
                {
                    TreeItem.SetImageExpanded(_TextureTreeItemFoldedFolder);
                }
                FileInfo[] FileInfos = DirectoryHelper.GetFilesInDirectory(DirectoryPath);
                Count = FileInfos.Length;
                for (int i = 0; i < Count; i++)
                {
                    FileInfo FileInfo = FileInfos[i];
                    if (FileInfo.FullName.Contains("Difference") == false)
                    {
                        TreeItem TreeItemFile = CaptureTree.CreateItem();
                        TreeItem.AddChild(TreeItemFile);
                        ShowFile(TreeItemFile, FileInfo.FullName);
                    }
                }
            }
        }

        void ShowFile(TreeItem TreeItem, string FilePath)
        {
            FilePath = PathHelper.ToStandardForm(FilePath);
            string FileName = PathHelper.GetFileName(FilePath);
            if (FileName == "")
            {
                FileName = "Empty";
            }
            TreeItem.SetFolder(false);
            TreeItem.SetExpanded(false);
            TreeItem.SetText(FileName);
            TreeItem.SetImageFolded(_TextureTreeItemFile);
            TreeItem.SetImageExpanded(_TextureTreeItemFile);
            TreeItem.SetTagString(FilePath);
        }

        void CollectCaptureNameFromTree(List<string> CaptureNameList, SortedSet<string> CaptureNameSet, TreeItem TreeItem)
        {
            if (TreeItem.GetFolder() == false)
            {
                string Path = TreeItem.GetTagString();
                string FileName = PathHelper.GetFileName(Path);
                int IndexBookmark = FileName.IndexOf("_Bookmark");
                int IndexEditorCamera = FileName.IndexOf("_EditorCamera");
                int Index = Math.Max(IndexBookmark, IndexEditorCamera);
                int DateStringLength = 12;
                if (Index > DateStringLength)
                {
                    string CaptureName = FileName.Substring(DateStringLength + 1, Index - DateStringLength - 1);
                    if (CaptureNameSet.Contains(CaptureName) == false)
                    {
                        CaptureNameList.Add(CaptureName);
                        CaptureNameSet.Add(CaptureName);
                    }
                }
            }
            int Count = TreeItem.GetChildCount();
            for (int i = 0; i < Count; i++)
            {
                TreeItem Child = TreeItem.GetChild(i);
                CollectCaptureNameFromTree(CaptureNameList, CaptureNameSet, Child);
            }
        }

        void OnComboBoxCaptureNameDropDown(ComboBox Sender)
        {
            List<string> CaptureNameList = new List<string>();
            SortedSet<string> CaptureNameSet = new SortedSet<string>();
            CollectCaptureNameFromTree(CaptureNameList, CaptureNameSet, _TreeLeftCapture.GetRootItem());
            _ComboBoxCaptureName.ClearItems();
            foreach (string CaptureName in CaptureNameList)
            {
                _ComboBoxCaptureName.AddItem(CaptureName);
            }
        }

        void OnComboBoxCaptureSizeDropDown(ComboBox Sender)
        {
            string currentText = _ComboBoxCaptureSize.GetValueEdit().GetText();
            if (currentText == "Custom")
            {
                // Use the combo box's edit control directly for input
                Edit editControl = _ComboBoxCaptureSize.GetValueEdit();
                editControl.SetText("");
                editControl.SetFocus();
                
                // Show hint message
                string message = "Please input size in WIDTHxHEIGHT format (e.g. 800x600)";
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Custom Size", message);
            }
        }

        void ********************************(ComboBox Sender)
        {
            _ComboBoxCaptureCamera.ClearItems();
            
            // Add all cameras in the scene
            EditorScene EditorScene = EditorScene.GetInstance();
            if (EditorScene != null && EditorScene.GetRoot() != null)
            {
                // Recursively find all entities with Camera component
                List<Entity> cameraEntities = new List<Entity>();
                FindEntitiesWithComponent(EditorScene.GetRoot(), typeof(Camera), cameraEntities);
                
                // Use HashSet to avoid duplicate names
                HashSet<string> cameraNames = new HashSet<string>();
                
                // Add all unique camera names to combo box
                foreach (var entity in cameraEntities)
                {
                    string cameraName = entity.GetName();
                    if (!string.IsNullOrEmpty(cameraName))
                    {
                        cameraNames.Add(cameraName);
                    }
                }
                
                // Add default EditorCamera if not already in the list
                if (!cameraNames.Contains("EditorCamera"))
                {
                    cameraNames.Add("EditorCamera");
                }
                
                foreach (string name in cameraNames)
                {
                    _ComboBoxCaptureCamera.AddItem(name);
                }
            }
            else
            {
                // Fallback to default EditorCamera if no scene
                _ComboBoxCaptureCamera.AddItem("EditorCamera");
            }

            // Add bookmarks
            BookmarkList BookmarkList = BookmarkUI.GetCurrentBookmarkList();
            for (int BookmarkIndex = 0; BookmarkIndex <= 9; BookmarkIndex++)
            {
                if (BookmarkList.IsBookmarkEnable(BookmarkIndex))
                {
                    _ComboBoxCaptureCamera.AddItem($"Bookmark{BookmarkIndex}");
                }
            }
        }

        void OnButtonCaptureClicked(Button Sender)
        {
            string CaptureName = _ComboBoxCaptureName.GetSelectedItemText();
            if (EditorUtilities.CheckFileOrDirectoryName(CaptureName) == false)
            {
                string Tips = string.Format("Capture name: {0} contains illegal char/chars.", CaptureName);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }

            string selectedCameraBookmark = _ComboBoxCaptureCamera.GetSelectedItemText();
            CaptureLocation CaptureLocation = selectedCameraBookmark.StartsWith("Bookmark") ? 
                Enum.Parse<CaptureLocation>(selectedCameraBookmark) : 
                CaptureLocation.EditorCamera;

            string CaptureVisualizeText = _ComboBoxCaptureVisualize.GetSelectedItemText();
            ViewModeVisualizeType CaptureVisualize = Enum.Parse<ViewModeVisualizeType>(CaptureVisualizeText);

            string CaptureSizeText = _ComboBoxCaptureSize.GetValueEdit().GetText();
            string[] CaptureSizeStrings = CaptureSizeText.Split('x');
            if (CaptureSizeStrings.Length != 2)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Error", "Invalid size format. Please use WIDTHxHEIGHT (e.g. 800x600)");
                return;
            }
            
            int Width = MathHelper.ParseInt(CaptureSizeStrings[0]);
            int Height = MathHelper.ParseInt(CaptureSizeStrings[1]);
            
            if (Width <= 0 || Height <= 0)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Error", "Width and height must be positive numbers");
                return;
            }

            string CaptureFramesToSkipText = _ComboBoxCaptureFramesToSkip.GetSelectedItemText();
            CaptureFramesToSkipText = CaptureFramesToSkipText.Replace("Wait ", "");
            CaptureFramesToSkipText = CaptureFramesToSkipText.Replace(" frames", "");
            CaptureFramesToSkipText = CaptureFramesToSkipText.Replace(" frame", "");
            int FramesToSkip = MathHelper.ParseInt(CaptureFramesToSkipText);

            _ProgressBarCapture.SetProgress(0.0f);

            string SelectedCamera = _ComboBoxCaptureCamera.GetSelectedItemText();
            SceneCapturer.GetInstance().StartCaptureScene(CaptureName, CaptureLocation, CaptureVisualize, Width, Height, FramesToSkip, OnCaptureProgress, OnCaptureEnd, SelectedCamera);

            _ButtonCapture.SetEnable(false);
        }

        void OnCaptureProgress(float Progress)
        {
            _ProgressBarCapture.SetProgress(Progress);
        }

        void OnCaptureEnd()
        {
            UpdateCaptureTrees();
            _ButtonCapture.SetEnable(true);
            _ProgressBarCapture.SetProgress(0.0f);
        }

        void OnButtonCompareClicked(Button Sender)
        {
            TreeItem TreeItemLeft = _TreeLeftCapture.GetSelectedItem();
            TreeItem TreeItemRight = _TreeRightCapture.GetSelectedItem();
            if (TreeItemLeft == null || TreeItemRight == null || TreeItemLeft.GetFolder() || TreeItemRight.GetFolder())
            {
                string Tips = string.Format("Please select two capture images.");
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string PathLeft = TreeItemLeft.GetTagString();
            if (PathLeft.Contains(".nda") == false)
            {
                PathLeft = PathLeft.Replace(".png", ".nda");
            }
            string PathRight = TreeItemRight.GetTagString();
            if (PathRight.Contains(".nda") == false)
            {
                PathRight = PathRight.Replace(".png", ".nda");
            }
            if (FileHelper.IsFileExists(PathLeft) == false || FileHelper.IsFileExists(PathLeft) == false)
            {
                string Tips = string.Format("Corresponding .nda not exists.");
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string PathDifferenceNDA;
            string ErrorMessage;
            double MSE;
            double PSNR;
            double SSIM;
            bool bSuccess = CaptureComparer.GetInstance().CompareCaptures(PathLeft, PathRight, out PathDifferenceNDA, out ErrorMessage, out MSE, out PSNR, out SSIM);
            if (!bSuccess)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", ErrorMessage);
                return;
            }
            Texture TextureDifference = UIManager.LoadUIImage(PathDifferenceNDA);
            int Width = TextureDifference.GetWidth();
            int Height = TextureDifference.GetHeight();
            _PanelDifferenceImage.SetImage(TextureDifference);
            _PanelDifferenceImageBig.SetImage(TextureDifference);
            _PanelDifferenceImageBig.SetSize(Width, Height);
            string StatisticsText = string.Format("MSE: {0}\r\nPSNR: {1}dB\r\nSSIM: {2}", MSE.ToString("0.000"), PSNR.ToString("0.000"), SSIM.ToString("0.000"));
            _LabelStatistics.SetText(StatisticsText);
        }

        // Helper method to recursively find entities with specific component
        void FindEntitiesWithComponent(Entity entity, Type componentType, List<Entity> result)
        {
            if (entity.HasComponent(componentType))
            {
                result.Add(entity);
            }
            
            foreach (Entity child in entity.Children)
            {
                FindEntitiesWithComponent(child, componentType, result);
            }
        }

        void OnButtonCopyStatisticsClicked(Button Sender)
        {
            string Text = _LabelStatistics.GetText();
            if (Text != "")
            {
                GetDevice().SetClipboardText(Text);
            }
        }

        void OnTreeLeftCaptureItemSelected(Tree Sender, TreeItem TreeItem)
        {
            SelectCaptureItem(_PanelLeftCaptureImage, _PanelLeftCaptureImageBig, _LabelLeftCaptureSize, TreeItem);
        }

        void OnTreeRightCaptureItemSelected(Tree Sender, TreeItem TreeItem)
        {
            SelectCaptureItem(_PanelRightCaptureImage, _PanelRightCaptureImageBig, _LabelRightCaptureSize, TreeItem);
        }

        void SelectCaptureItem(Panel PanelCaptureImage, Panel PanelCaptureImageBig, Label LabelCaptureSize, TreeItem TreeItem)
        {
            if (TreeItem.GetFolder() == false)
            {
                string Path = TreeItem.GetTagString();
                string NdaPath = TreeItem.GetTagString();
                if (Path.Contains(".png") && Path.Contains(".nda") == false)
                {
                    NdaPath = Path.Replace(".png", ".nda");
                    if (FileHelper.IsFileExists(NdaPath) == false)
                    {
                        SceneCapturer.GetInstance().ImportCapture(Path);
                        OperationQueue.GetInstance().AddOperation(() =>
                        {
                            UpdateCaptureTrees();
                        });
                    }
                }
                if (FileHelper.IsFileExists(NdaPath))
                {
                    Texture TextureCapture = UIManager.LoadUIImage(NdaPath);
                    int Width = TextureCapture.GetWidth();
                    int Height = TextureCapture.GetHeight();
                    PanelCaptureImage.SetImage(TextureCapture);
                    PanelCaptureImageBig.SetImage(TextureCapture);
                    string SizeText = string.Format("{0}x{1}", Width, Height);
                    LabelCaptureSize.SetText(SizeText);
                    if (PanelCaptureImageBig == _PanelLeftCaptureImageBig)
                    {
                        _ComparePanelDifferenceImageBig.SetImage(TextureCapture);
                    }
                    else if (PanelCaptureImageBig == _PanelRightCaptureImageBig)
                    {
                        _ComparePanelDifferenceImageBig.SetImage2(TextureCapture);
                    }
                }
            }
        }

        void OnTreeCaptureXRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY) == false)
            {
                return;
            }
            _CurrentTreeCapture = (Tree)Sender;
            TreeItemHitTest HitTest = _CurrentTreeCapture.HitTest(MouseX, MouseY);
            if (HitTest.HitResult != TreeItemHitResult.Nothing)
            {
                ShowContextMenu(MouseX, MouseY);
            }
            bContinue = false;
        }

        void ShowContextMenu(int MouseX, int MouseY)
        {
            MenuItem MenuItem_Delete = new MenuItem();
            MenuItem_Delete.SetText("Delete");
            MenuItem_Delete.SetImage(UIManager.LoadUIImage("Editor/Icons/Context/Delete.png"));
            MenuItem_Delete.ClickedEvent += OnMenuItemDeleteClicked;

            MenuItem MenuItem_ShowInExplorer = new MenuItem();
            MenuItem_ShowInExplorer.SetText("Show In Explorer");
            MenuItem_ShowInExplorer.ClickedEvent += OnMenuItemShowInExplorerClicked;

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            MenuContextMenu.AddMenuItem(MenuItem_Delete);
            MenuContextMenu.AddMenuItem(MenuItem_ShowInExplorer);
            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, MouseX, MouseY);
        }

        void OnMenuItemDeleteClicked(MenuItem Sender)
        {
            if (_CurrentTreeCapture != null)
            {
                TreeItem TreeItem = _CurrentTreeCapture.GetSelectedItem();
                string Path = TreeItem.GetTagString();
                if (TreeItem.GetFolder())
                {
                    if (DirectoryHelper.IsDirectoryExists(Path))
                    {
                        DirectoryHelper.DeleteDirectory(Path);
                    }
                }
                else
                {
                    if (FileHelper.IsFileExists(Path))
                    {
                        FileHelper.DeleteFile(Path);
                    }
                }
                ClearDeletedPanelImage(_PanelLeftCaptureImage, _PanelLeftCaptureImageBig, _LabelLeftCaptureSize);
                ClearDeletedPanelImage(_PanelRightCaptureImage, _PanelRightCaptureImageBig, _LabelRightCaptureSize);
                UpdateCaptureTrees();
            }
        }

        void OnMenuItemShowInExplorerClicked(MenuItem MenuItem)
        {
            TreeItem TreeItem = _CurrentTreeCapture.GetSelectedItem();
            string Path = TreeItem.GetTagString();
            ProcessHelper.OpenContainingFolder(Path);
        }

        void ClearDeletedPanelImage(Panel PanelCaptureImage, Panel PanelCaptureImageBig, Label LabelCaptureSize)
        {
            Texture CaptureImage = PanelCaptureImage.GetImage();
            if (CaptureImage != null)
            {
                string CaptureFilename = CaptureImage.GetFilename();
                if (FileHelper.IsFileExists(CaptureFilename) == false)
                {
                    PanelCaptureImage.SetImage(null);
                    PanelCaptureImageBig.SetImage(null);
                }
            }
            LabelCaptureSize.SetText(Const_InvalidSizeText);
        }

        void OnPanelLeftCaptureLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            SwitchToPage(_ButtonPageLeftCapture);
        }

        void OnPanelDifferenceLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            SwitchToPage(_ButtonPageDifference);
        }

        void OnPanelRightCaptureLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            SwitchToPage(_ButtonPageRightCapture);
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            UpdateLayout();
        }

        public void UpdateLayout()
        {
            int ContainerWidth = _Container.GetWidth();
            int ContainerHeight = _Container.GetHeight();
            ContainerWidth = Math.Max(ContainerWidth, 740);
            ContainerHeight = Math.Max(ContainerHeight, 450);

            int PageY = 38;
            int PageX = 18;
            int PageWidth = ContainerWidth - 18 * 2;
            int PageHeight = ContainerHeight - PageY - 8;
            int PageCenterX = PageWidth / 2;

            _PanelPageCaptures.SetPosition(PageX, PageY, PageWidth, PageHeight);
            _PanelPageLeftCapture.Follow(_PanelPageCaptures);
            _PanelPageDifference.Follow(_PanelPageCaptures);
            _PanelPageRightCapture.Follow(_PanelPageCaptures);
            _PanelPageSlide.Follow(_PanelPageCaptures);

            // Adjust positions for all combo boxes
            //int comboBoxSpacing = 120;
            _ComboBoxCaptureName.SetPosition(18, 18, 100, 16);
            _ComboBoxCaptureCamera.SetPosition(138, 18, 220, 16); // Wider combo box
            _ComboBoxCaptureVisualize.SetPosition(378, 18, 100, 16);
            _ComboBoxCaptureSize.SetPosition(498, 18, 100, 16);
            _ComboBoxCaptureFramesToSkip.SetPosition(618, 18, 100, 16);
            
            _ButtonCapture.SetX(PageWidth - _ButtonCapture.GetWidth());

            _ProgressBarCapture.Follow(_ButtonCapture);

            int ButtonCompareX = PageCenterX - _ButtonCapture.GetWidth() / 2;
            _ButtonCompare.SetX(ButtonCompareX);

            int LabelStatisticsX = PageCenterX - _LabelStatistics.GetWidth() / 2;
            _LabelStatistics.SetX(LabelStatisticsX);

            int ButtonCopyStatisticsX = PageCenterX - _ButtonCopyStatistics.GetWidth() / 2;
            _ButtonCopyStatistics.SetX(ButtonCopyStatisticsX);

            int TreeLeftCaptureX = 0;
            int TreeLeftCaptureWidth = LabelStatisticsX - 8 - TreeLeftCaptureX;
            _TreeLeftCapture.SetX(TreeLeftCaptureX);
            _TreeLeftCapture.SetWidth(TreeLeftCaptureWidth);

            int TreeRightCaptureX = PageWidth - TreeLeftCaptureWidth;
            int TreeRightCaptureWidth = TreeLeftCaptureWidth;
            _TreeRightCapture.SetX(TreeRightCaptureX);
            _TreeRightCapture.SetWidth(TreeRightCaptureWidth);

            int ImageSpanX = 50;
            int PanelDifferenceImageX = PageCenterX - _PanelDifferenceImage.GetWidth() / 2;
            int PanelLeftCaptureImageX = PanelDifferenceImageX - _PanelLeftCaptureImage.GetWidth() - ImageSpanX;
            int PanelRightCaptureImageX = PanelDifferenceImageX + _PanelDifferenceImage.GetWidth() + ImageSpanX;

            _PanelLeftCaptureImage.SetX(PanelLeftCaptureImageX);
            _PanelDifferenceImage.SetX(PanelDifferenceImageX);
            _PanelRightCaptureImage.SetX(PanelRightCaptureImageX);

            int PanelImageY = PageHeight - _PanelLeftCaptureImage.GetHeight();
            _PanelLeftCaptureImage.SetY(PanelImageY);
            _PanelDifferenceImage.SetY(PanelImageY);
            _PanelRightCaptureImage.SetY(PanelImageY);

            int TreeCaptureEndY = PanelImageY - 30;

            int TreeCaptureHeight = TreeCaptureEndY - _TreeLeftCapture.GetY();
            _TreeLeftCapture.SetHeight(TreeCaptureHeight);
            _TreeRightCapture.SetHeight(TreeCaptureHeight);

            int ButtonCopyStatisticsY = TreeCaptureEndY - _ButtonCopyStatistics.GetHeight();
            _ButtonCopyStatistics.SetY(ButtonCopyStatisticsY);

            int LabelCaptureSizeY = PanelImageY - 22;
            _LabelLeftCaptureSize.SetX(PanelLeftCaptureImageX);
            _LabelLeftCaptureSize.SetY(LabelCaptureSizeY);
            _LabelRightCaptureSize.SetX(PanelRightCaptureImageX);
            _LabelRightCaptureSize.SetY(LabelCaptureSizeY);
        }

        void EnablePageScrolling(Panel PanelPage)
        {
            PanelPage.SetText("Double click left mouse to reset view location.");
            PanelPage.SetFontSize(16);
            PanelPage.SetTextColor(Color.FromRGB(64, 64, 64));
            PanelPage.SetTextAlign(TextAlign.CenterCenter);

            PanelPage.LeftMouseDoubleClickedEvent += OnPanelPageLeftMouseDoubleClicked;
            PanelPage.RightMouseDownEvent += OnPanelPageRightMouseDown;
            PanelPage.MouseMoveEvent += OnPanelPageMouseMove;
            PanelPage.RightMouseUpEvent += OnPanelPageRightMouseUp;
            PanelPage.MouseWheelEvent += OnPanelPageMouseWheel;
        }

        void OnPanelPageLeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            ResetOffsetAndScale();
        }

        void ResetOffsetAndScale()
        {
            _OffsetX = 0.0f;
            _OffsetY = 0.0f;
            _ScaleX = 1.0f;
            _ScaleY = 1.0f;
        }

        void OnPanelPageRightMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Sender.CaptureMouse();
            _LeftMouseDown = true;
            _SavedMouseX = MouseX;
            _SavedMouseY = MouseY;
            _SavedOffsetX = _OffsetX;
            _SavedOffsetY = _OffsetY;
        }

        void OnPanelPageMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_LeftMouseDown)
            {
                _OffsetX = _SavedOffsetX + (MouseX - _SavedMouseX);
                _OffsetY = _SavedOffsetY + (MouseY - _SavedMouseY);
            }
        }

        void OnPanelPageRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_LeftMouseDown)
            {
                Sender.ReleaseMouse();
                _LeftMouseDown = false;
                bContinue = false;
            }
        }

        void OnPanelPageMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            float Scale = 1.01f;
            int Count = Math.Abs(MouseDeltaZ);
            if (MouseDeltaZ > 0)
            {
                for (int i = 0; i < Count; i++)
                {
                    _ScaleX *= Scale;
                    _ScaleY *= Scale;
                }
            }
            else if (MouseDeltaZ < 0)
            {
                for (int i = 0; i < Count; i++)
                {
                    _ScaleX /= Scale;
                    _ScaleY /= Scale;
                }
            }
            float MinimalScale = 0.01f;
            if (_ScaleX < MinimalScale)
            {
                _ScaleX = MinimalScale;
            }
            if (_ScaleY < MinimalScale)
            {
                _ScaleY = MinimalScale;
            }
        }

        public override void Update(long TimeElapsed)
        {
            UpdateBigImagesPosition();
        }

        void UpdateBigImagesPosition()
        {
            UpdateBigImagePosition(_PanelLeftCaptureImageBig);
            UpdateBigImagePosition(_PanelRightCaptureImageBig);
            UpdateBigImagePosition(_PanelDifferenceImageBig);
            UpdateBigImagePosition(_ComparePanelDifferenceImageBig);
        }

        void UpdateBigImagePosition(Panel PanelImageBig)
        {
            Texture Texture1 = _PanelLeftCaptureImageBig.GetImage();
            Texture Texture2 = _PanelRightCaptureImageBig.GetImage();
            int Width1 = 1280;
            int Height1 = 720;
            if (Texture1 != null)
            {
                Width1 = Texture1.GetWidth();
                Height1 = Texture1.GetHeight();
            }
            int Width2 = 1280;
            int Height2 = 720;
            if (Texture2 != null)
            {
                Width2 = Texture2.GetWidth();
                Height2 = Texture2.GetHeight();
            }
            int Width = Math.Max(Width1, Width2);
            int Height = Math.Max(Height1, Height2);
            int X3 = (int)_OffsetX;
            int Y3 = (int)_OffsetY;
            int Width3 = (int)(Width * _ScaleX);
            int Height3 = (int)(Height * _ScaleY);
            PanelImageBig.SetPosition(X3, Y3, Width3, Height3);
        }

        public void OnContainerApplicationActivate(Control Sender, bool bApplicationActivated)
        {
            if (bApplicationActivated)
            {
                UpdateCaptureTrees();
            }
        }

        public void SaveUserConfig(Record RootRecord)
        {
            Record RecordCaptureCompareUI = RootRecord.AddChild();
            RecordCaptureCompareUI.SetTypeString("CaptureCompareUI");

            Record RecordCaptureCompareSettings = RecordCaptureCompareUI.AddChild();
            RecordCaptureCompareSettings.SetTypeString("CaptureCompareSettings");
            RecordCaptureCompareSettings.SetString("CaptureName", _ComboBoxCaptureName.GetValueEdit().GetText());
            RecordCaptureCompareSettings.SetString("CaptureVisualize", _ComboBoxCaptureVisualize.GetSelectedItemText());
            RecordCaptureCompareSettings.SetString("CaptureSize", _ComboBoxCaptureSize.GetSelectedItemText());
            RecordCaptureCompareSettings.SetString("CaptureFramesToSkip", _ComboBoxCaptureFramesToSkip.GetSelectedItemText());
        }

        public void LoadUserConfig(Record RootRecord)
        {
            Record RecordCaptureCompareUI = RootRecord.FindByTypeString("CaptureCompareUI");
            if (RecordCaptureCompareUI != null)
            {
                Record RecordCaptureCompareSettings = RecordCaptureCompareUI.FindByTypeString("CaptureCompareSettings");
                if (RecordCaptureCompareSettings != null)
                {
                    _ComboBoxCaptureName.GetValueEdit().SetText(RecordCaptureCompareSettings.GetString("CaptureName"));
                    _ComboBoxCaptureVisualize.SetSelectedItemByText(RecordCaptureCompareSettings.GetString("CaptureVisualize"));
                    _ComboBoxCaptureSize.SetSelectedItemByText(RecordCaptureCompareSettings.GetString("CaptureSize"));
                    _ComboBoxCaptureFramesToSkip.SetSelectedItemByText(RecordCaptureCompareSettings.GetString("CaptureFramesToSkip"));
                }
            }
        }
    }
}