using EditorUI;

namespace CrossEditor
{
    class CaptureComparer
    {
        int _Index;

        static CaptureComparer _Instance = new CaptureComparer();

        public static CaptureComparer GetInstance()
        {
            return _Instance;
        }

        CaptureComparer()
        {
            _Index = 0;
        }

        public bool CompareCaptures(string PathLeft, string PathRight, out string PathDifferenceNDA, out string ErrorMessage, out double MSE, out double PSNR, out double SSIM)
        {
            string CaptureDirectory = SceneCapturer.GetInstance().GetCaptureDirectory();
            string PathDifference = CaptureDirectory + "/Difference.png";
            double mse = 0;
            double psnr = 0;
            double ssim = 0;
            bool bSuccess = Clicross.ResourceUtil.CompareTexture(PathDifference, PathLeft, PathRight, mse, psnr, ssim);
            MSE = mse;
            PSNR = psnr;
            SSIM = ssim;
            if (!bSuccess)
            {
                ErrorMessage = "Failed to compare captures, such as sizes of captures not match.";
                PathDifferenceNDA = "";
                return false;
            }
            DebugHelper.Assert(FileHelper.IsFileExists(PathDifference));
            ErrorMessage = "";
            PathDifferenceNDA = PathDifference.Replace(".png", _Index.ToString() + ".nda");
            _Index++;
            FileHelper.DeleteFile(PathDifferenceNDA);
            SceneCapturer.GetInstance().ImportCapture(PathDifferenceNDA, PathDifference);
            return true;
        }
    }
}
