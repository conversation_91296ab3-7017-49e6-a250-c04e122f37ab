using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Threading;

namespace CrossEditor
{
    delegate void SceneCaptureCaptureProgressEventHandler(float Progress);
    delegate void SceneCaptureCaptureEndEventHandler();

    class SceneCapturer
    {
        const string CaptureCameraName = "CaptureCamera";

        int _FramesToSkip;

        static SceneCapturer _Instance = new SceneCapturer();

        public static SceneCapturer GetInstance()
        {
            return _Instance;
        }

        ViewModeVisualizeType _SavedViewModeVisualizeType;
        Double3 _SavedPostion;
        double _SavedDirX;
        double _SavedDirY;

        string _CaptureName;
        string _SceneName;
        string _DateString;
        ViewModeVisualizeType _CaptureVisualize;
        int _Width;
        int _Height;

        SceneCaptureCaptureProgressEventHandler _ProgressCallback;
        int _TotalTasks;

        List<string> _CapturePathList;

        SceneCapturer()
        {
            _SavedViewModeVisualizeType = ViewModeVisualizeType.Lit;
            _CapturePathList = new List<string>();
            _FramesToSkip = 0;
        }

        public string GetCaptureDirectory()
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string CaptureDirectory = string.Format("{0}/Intermediate/Capture", ProjectDirectory);
            return CaptureDirectory;
        }

        public void StartCaptureScene(string CaptureName, CaptureLocation CaptureLocation, ViewModeVisualizeType CaptureVisualize, int Width, int Height, int FramesToSkip,
            SceneCaptureCaptureProgressEventHandler ProgressCallback, SceneCaptureCaptureEndEventHandler Callback, string CameraName = "EditorCamera")
        {
            _CapturePathList.Clear();

            EditorScene EditorScene = EditorScene.GetInstance();
            string SceneFilename = EditorScene.GetCurrentSceneFilename();
            string SceneName = PathHelper.GetNameOfPath(SceneFilename);
            if (SceneName == "")
            {
                SceneName = "NewScene";
            }

            DateTime DateTime = DateTime.Now;
            string DateString = string.Format(
                "{0}{1}{2}_{3}{4}{5}",
                DateTime.Year.ToString("0000"),
                DateTime.Month.ToString("00"),
                DateTime.Day.ToString("00"),
                DateTime.Hour.ToString("00"),
                DateTime.Minute.ToString("00"),
                DateTime.Second.ToString("00"));

            _CaptureName = CaptureName;
            _SceneName = SceneName;
            _DateString = DateString;
            _CaptureVisualize = CaptureVisualize;
            _Width = Width;
            _Height = Height;
            _FramesToSkip = FramesToSkip;

            _ProgressCallback = ProgressCallback;
            _TotalTasks = 0;

            BeginCapture();

            BookmarkList BookmarkList = BookmarkUI.GetCurrentBookmarkList();

            if (CaptureLocation == CaptureLocation.EditorCamera ||
                CaptureLocation == CaptureLocation.All)
            {
                AddCaptureSceneOperations(CaptureLocation.EditorCamera, CameraName);
            }

            for (int BookmarkIndex = 0; BookmarkIndex <= 9; BookmarkIndex++)
            {
                CaptureLocation CurrentCaptureLocation = CaptureLocation.Bookmark0 + BookmarkIndex;
                if (CaptureLocation == CaptureLocation.All ||
                    CaptureLocation == CurrentCaptureLocation)
                {
                    bool BookmarkEnable = BookmarkList.IsBookmarkEnable(BookmarkIndex);
                    if (BookmarkEnable)
                    {
                        AddJumpBookmark(BookmarkIndex);
                        AddCaptureSceneOperations(CurrentCaptureLocation, CameraName);
                    }
                }
            }

            FrameExecutor FrameExecutor = FrameExecutor.GetInstance();
            for (int i = 0; i < 10; i++)
            {
                int TaskIndex = _TotalTasks;
                FrameExecutor.AddOperation(() =>
                {
                    TriggerProcessCallback(TaskIndex);
                });
                _TotalTasks++;
            }

            FrameExecutor.AddOperation(() =>
            {
                ImportCaptures();
                CleanUp();
                Callback();
            });
        }

        void BeginCapture()
        {
            EditorSceneUI EditorSceneUI = EditorSceneUI.GetInstance();
            _SavedViewModeVisualizeType = EditorSceneUI.GetViewModeVisualizeType();

            EditorScene EditorScene = EditorScene.GetInstance();

            FPSCamera FPSCamera = EditorScene.GetFPSCamera();
            _SavedPostion = FPSCamera.GetPosition();
            _SavedDirX = FPSCamera._DirX;
            _SavedDirY = FPSCamera._DirY;

            EditorScene.SetForceVisible(true);
            EditorSceneUI.SetForceWorldEnable(true);
        }

        public void AddJumpBookmark(int BookmarkIndex)
        {
            FrameExecutor FrameExecutor = FrameExecutor.GetInstance();
            FrameExecutor.AddOperation(() =>
            {
                BookmarkUI.JumpBookmark(BookmarkIndex);
            });
        }

        public void AddCaptureSceneOperations(CaptureLocation CaptureLocation, string CameraName = "EditorCamera")
        {
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Lit, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Unlit, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Wireframe, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.DetailLighting, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.LightingOnly, CameraName);

            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.BaseColor, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Depth, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.WorldNormal, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.DiffuseColor, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.MaterialAO, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Metallic, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Opacity, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Roughness, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Specular, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.SpecularColor, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.SubsurfaceColor, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.ShadingModel, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.MotionVector, CameraName);

            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.AmbientOcclusion, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.BentNormal, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.Reflections, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.SeparateTranslucencyRGB, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.GlobalIllumination, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.SceneColor, CameraName);

            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.GILighting, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.DebugColor, CameraName);
            AddCaptureSceneOperation(CaptureLocation, ViewModeVisualizeType.EmissiveColor, CameraName);
        }

        void TriggerProcessCallback(int TaskIndex)
        {
            if (_ProgressCallback != null)
            {
                float Progress = (TaskIndex + 1) / (float)_TotalTasks;
                _ProgressCallback(Progress);
            }
        }

        public void AddCaptureSceneOperation(CaptureLocation CaptureLocation, ViewModeVisualizeType ViewModeVisualizeType, string CameraName = "EditorCamera")
        {
            if (_CaptureVisualize == ViewModeVisualizeType)
            {
                int TaskIndex1 = _TotalTasks;
                FrameExecutor FrameExecutor = FrameExecutor.GetInstance();
                FrameExecutor.AddOperation(() =>
                {
                    SetupScene(CaptureLocation, ViewModeVisualizeType, CameraName);
                    TriggerProcessCallback(TaskIndex1);
                });
                _TotalTasks++;

                for (int i = 0; i < _FramesToSkip; i++)
                {
                    int TaskIndex2 = _TotalTasks;
                    FrameExecutor.AddOperation(() =>
                    {
                        TriggerProcessCallback(TaskIndex2);
                    });
                    _TotalTasks++;
                }

                int TaskIndex3 = _TotalTasks;
                FrameExecutor.AddOperation(() =>
                {
                    CaptureScene(CaptureLocation, ViewModeVisualizeType, CameraName);
                    TriggerProcessCallback(TaskIndex3);
                });
                _TotalTasks++;
            }
        }

        void SetupScene(CaptureLocation CaptureLocation, ViewModeVisualizeType ViewModeVisualizeType, string CameraName = "EditorCamera")
        {
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();

            World World = EditorScene.GetInstance().GetWorld();
            Entity Root = World.Root;

            Entity SourceCameraEntity = Root.FindChildByName(CameraName);
            if (SourceCameraEntity == null)
            {
                SourceCameraEntity = Root.FindChildByName("EditorCamera");
            }

            Entity CaptureCameraEntity = Root.FindChildByName(CaptureCameraName);
            if (CaptureCameraEntity != null)
            {
                CaptureCameraEntity.Parent.RemoveChildEntity(CaptureCameraEntity);
                CaptureCameraEntity.RuntimeRemove();
            }

            CaptureCameraEntity = HierarchyUI.InnerDuplicateItem(SourceCameraEntity, CaptureCameraName);
            HierarchyUI.SelectEntity(null);

            RenderTextureInfo RenderTextureInfo = new RenderTextureInfo()
            {
                Name = "CaptureCameraRenderTexture",
                Dimension = TextureDimension.Tex2D,
                Format = RenderTextureFormat.R8G8B8A8_UNorm,
                Width = (uint)_Width,
                Height = (uint)_Height,
            };

            RenderTexture RenderTexture = new RenderTexture(RenderTextureInfo);

            Camera CaptureCamera = (Camera)CaptureCameraEntity.GetComponent(typeof(Camera));
            CaptureCamera.SetRenderTexture(RenderTexture);

            float Aspect = _Width / (float)_Height;
            CaptureCamera.SensorWidth = CaptureCamera.SensorHeight * Aspect;

            EditorSceneUI EditorSceneUI = EditorSceneUI.GetInstance();
            EditorSceneUI.SetViewModeVisualizeType(ViewModeVisualizeType);
        }

        void CaptureScene(CaptureLocation CaptureLocation, ViewModeVisualizeType ViewModeVisualizeType, string CameraName = "EditorCamera")
        {
            World World = EditorScene.GetInstance().GetWorld();
            Entity Root = World.Root;

            Entity CaptureCameraEntity = Root.FindChildByName(CaptureCameraName);

            string CaptureDirectory = GetCaptureDirectory();
            // Ensure CameraName is valid, fallback to "EditorCamera" if empty
            string validCameraName = string.IsNullOrEmpty(CameraName) ? "EditorCamera" : CameraName;
            string suffix = CaptureLocation == CaptureLocation.EditorCamera ? 
                validCameraName : 
                CaptureLocation.ToString();
            string CapturePath = string.Format("{0}/{1}/{2}_{3}_{4}.png", CaptureDirectory, _SceneName, _DateString, _CaptureName, suffix);
            string Directory = PathHelper.GetDirectoryName(CapturePath);
            DirectoryHelper.CreateDirectory(Directory);

            ulong ImageSaveType = 0; //png
            Clicross.RenderTargetUtil.Camera_SaveRenderTarget_Simple_Async(World._WorldInterface, CaptureCameraEntity.EntityID, CapturePath, (uint)_Width, (uint)_Height, true, (uint)ImageSaveType);
            _CapturePathList.Add(CapturePath);
        }

        public void ImportCapture(string Path)
        {
            string NDAPath = Path.Replace(".png", ".nda");
            ImportCapture(NDAPath, Path);
        }

        public void ImportCapture(string NDAPath, string Path)
        {
            if (FileHelper.IsFileExists(NDAPath) == false)
            {
                TextureImportSetting Settings = new TextureImportSetting();
                Settings.ColorSpace = ImportColorSpace.Linear;
                Settings.Compression = TextureCompression.Uncompressed;
                Settings.GenerateMipmap = false;
                Settings.SetEngineImportSetting();
                AssetImporterManager.Instance().ImportAsset(Path, NDAPath);
            }
        }

        void ImportCaptures()
        {
            foreach (string CapturePath in _CapturePathList)
            {
                while (FileHelper.IsFileExists(CapturePath) == false)
                {
                    Thread.Sleep(100);
                }
                ImportCapture(CapturePath);
            }
        }

        void CleanUp()
        {
            EditorSceneUI EditorSceneUI = EditorSceneUI.GetInstance();
            EditorScene EditorScene = EditorScene.GetInstance();

            EditorSceneUI.SetViewModeVisualizeType(_SavedViewModeVisualizeType);

            FPSCamera FPSCamera = EditorScene.GetFPSCamera();
            FPSCamera.SetPosition(_SavedPostion);
            FPSCamera._DirX = _SavedDirX;
            FPSCamera._DirY = _SavedDirY;

            EditorSceneUI.SetForceWorldEnable(false);
            EditorScene.SetForceVisible(true);

            World World = EditorScene.GetInstance().GetWorld();
            Entity Root = World.Root;

            Entity CaptureCameraEntity = Root.FindChildByName(CaptureCameraName);
            if (CaptureCameraEntity != null)
            {
                CaptureCameraEntity.Parent.RemoveChildEntity(CaptureCameraEntity);
                CaptureCameraEntity.RuntimeRemove();
            }
        }
    }
}
