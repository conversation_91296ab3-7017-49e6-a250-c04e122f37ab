using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{


    internal class Inspector_AnimatorStateLink : Inspector
    {
        protected AnimatorStatePanel _AnimStatePanelSource = null;
        protected AnimatorStatePanel _AnimStatePanelTarget = null;
        protected AnimatorContext _Context = null;
        protected AnimatorStateLink _Link = null;

        protected Button _ButtonRemoveLink;
        protected Edit _EditName;
        protected Panel _ParamsSeparator;
        protected Button _ButtonAddParam;

        List<Inspector> _LinkProperties = new List<Inspector>();
        List<Inspector> _ParamProperties = new List<Inspector>();

        public Inspector_AnimatorStateLink()
        {
        }

        public override void InspectObject(object Object1, object Tag = null)
        {
            Control Container = _SelfContainer;

            var parameter = Object1 as AnimatorStateLink.InspectorParameter;
            _Link = parameter.Data;
            _Context = parameter.Context;

            _AnimStatePanelSource = _Link.Source;
            _AnimStatePanelTarget = _Link.Target;

            // Link Head Ui
            string title = _AnimStatePanelSource.Name() + " -> " + _AnimStatePanelTarget.Name();

            _ButtonRemoveLink = new Button();
            _ButtonRemoveLink.Initialize();
            _ButtonRemoveLink.SetText("-");
            _ButtonRemoveLink.SetFontSize(16);
            _ButtonRemoveLink.SetTextAlign(TextAlign.CenterCenter);
            _ButtonRemoveLink.ClickedEvent += OnRemoveButtonClicked;
            _ButtonRemoveLink.SetToolTips("Remove Param");
            Container.AddChild(_ButtonRemoveLink);

            _EditName = new Edit();
            _EditName.SetFontSize(16);
            _EditName.Initialize(EditMode.Simple_SingleLine);
            _EditName.LoadSource("");
            _EditName.SetSize(100, 13);
            _EditName.SetText(title);
            _EditName.SetEnable(false);
            Container.AddChild(_EditName);

            _ParamsSeparator = new Panel();
            _ParamsSeparator.Initialize();
            _ParamsSeparator.SetBackgroundColor(Color.EDITOR_UI_SEPARATOR);
            Container.AddChild(_ParamsSeparator);

            _ButtonAddParam = new Button();
            _ButtonAddParam.Initialize();
            _ButtonAddParam.SetText("Add Parameter");
            _ButtonAddParam.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonAddParam.SetFontSize(18);
            _ButtonAddParam.SetTextOffsetY(2);
            _ButtonAddParam.ClickedEvent += OnButtonAddParamClicked;
            Container.AddChild(_ButtonAddParam);

            // Link Property -- Start Blend Num
            Inspector property = CreateStateProperty(Container, typeof(int), false, true, "Start Blend", "",
                (Object, PropertyName, ValueExtraProperty) => { return _Link.LinkDesc.StartBlend; },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                {
                    _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) =>
                    { simpleLink.StartBlend = Convert.ToInt32(PropertyValue); return false; });
                });
            _LinkProperties.Add(property);

            // Link Property -- End Blend Num
            property = CreateStateProperty(Container, typeof(int), false, true, "End Blend", "",
                (Object, PropertyName, ValueExtraProperty) => { return _Link.LinkDesc.EndBlend; },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                {
                    _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) =>
                    { simpleLink.EndBlend = Convert.ToInt32(PropertyValue); return false; });
                });
            _LinkProperties.Add(property);

            // Link Property -- Delta Frame Num
            property = CreateStateProperty(Container, typeof(int), false, true, "Delta Frame", "",
                (Object, PropertyName, ValueExtraProperty) => { return _Link.LinkDesc.DeltaFrame; },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                {
                    _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) =>
                    { simpleLink.DeltaFrame = Convert.ToInt32(PropertyValue); ; return false; });
                });
            _LinkProperties.Add(property);

            // Link Property -- Transit Model
            property = CreateStateProperty(Container, typeof(StateTransitMode), true, true, "Transit Mode", "",
                (Object, PropertyName, ValueExtraProperty) => { return _Link.LinkDesc.TransitMode; },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                {
                    _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) => { simpleLink.TransitMode = (StateTransitMode)PropertyValue; return false; });
                });
            _LinkProperties.Add(property);

            // Link Property -- Params
            for (int i = 0; i < _Link.LinkDesc.GetParamsForLink().Count; ++i)
            {
                IAnimatorParam param = _Link.LinkDesc.GetParamsForLink()[i];
                int indexForParam = i;

                property = CreateParamProperty(Container,
                    (Object, PropertyName, ValueExtraProperty) => { return param.Name; },
                    (Object, PropertyName, PropertyValue, SubProperty) =>
                    {
                        _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) =>
                        {
                            IAnimatorParam globalParam = _Context.FindPram(PropertyValue.ToString());

                            Type[] elementTypes = globalParam.GetType().GetGenericArguments();

                            Type paramType = typeof(AnimatorParam<>);
                            Type genericType = paramType.MakeGenericType(elementTypes);

                            IAnimatorParam createdParam = (IAnimatorParam)Activator.CreateInstance(genericType, new object[] { globalParam });
                            simpleLink.GetParamsForLink()[indexForParam] = createdParam;
                            return true;
                        });
                    },
                    (Object, PropertyName, ValueExtraProperty) => { return _Link.LinkDesc.GetParamsForLink()[indexForParam].Operate; },
                    (Object, PropertyName, PropertyValue, SubProperty) =>
                    {
                        _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) =>
                        {
                            simpleLink.GetParamsForLink()[indexForParam].Operate = (StateCompareOperate)PropertyValue;
                            return true;
                        });
                    },
                    (Object, PropertyName, ValueExtraProperty) => { return _Link.LinkDesc.GetParamsForLink()[indexForParam].Value; },
                    (Object, PropertyName, PropertyValue, SubProperty) =>
                    {
                        IAnimatorParam curParam = _Link.LinkDesc.GetParamsForLink()[indexForParam];
                        object value = Convert.ChangeType(PropertyValue as string, curParam.ToType());

                        _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) =>
                        {
                            simpleLink.GetParamsForLink()[indexForParam].Value = value;
                            return false;
                        });
                    },
                    (Object, PropertyName, PropertyValue, SubProperty) =>
                    {
                        IAnimatorParam curParam = _Link.LinkDesc.GetParamsForLink()[indexForParam];

                        _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) =>
                        {
                            simpleLink.GetParamsForLink().RemoveAt(indexForParam);
                            return true;
                        });
                    });
                _ParamProperties.Add(property);
            }

            ReadValue();
        }

        public override void ReadValue()
        {
            foreach (Inspector Inspector in _ParamProperties)
            {
                Inspector.ReadValue();
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            Width = Math.Max(Width, 260);
            int SpanX = 5;
            _ButtonRemoveLink.SetPosition(SpanX, Y + 3, 20, 20);

            int EditNameX = _ButtonRemoveLink.GetEndX() + SpanX;
            int EditNameWidth = Math.Max(Width - SpanX - EditNameX, 100);
            _EditName.SetPosition(EditNameX, Y + 7, EditNameWidth, 13);

            Y += 28;
            foreach (Inspector_Property Inspector in _LinkProperties)
                Inspector.UpdateLayout(Width, ref Y);

            Y += 5;
            _ParamsSeparator.SetPosition(SpanX, Y, Width, 1);

            Y += 5;
            foreach (Inspector_Property Inspector in _ParamProperties)
                Inspector.UpdateLayout(Width, ref Y);

            Y += 2;
            _ButtonAddParam.SetPosition(SpanX, Y + 5, Width - SpanX * 2, 30);

            Y += _ButtonAddParam.GetHeight() + 10;
            base.UpdateLayout(Width, ref Y);
        }

        protected Inspector CreateStateProperty(Control Container,
            Type PropertyType,
            bool IsEnum,
            bool IsEditValid,
            string Name,
            object Value,
            GetPropertyValueFunction getPropertyValueFunction,
            SetPropertyValueFunction setPropertyValueFunction)
        {
            Inspector inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyType.ToString(), IsEnum);

            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = Value;
            ObjectProperty.Name = Name;
            ObjectProperty.Type = PropertyType;
            ObjectProperty.ReadOnly = !IsEditValid;
            ObjectProperty.GetPropertyValueFunction = getPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = setPropertyValueFunction;

            inspector.InspectProperty(ObjectProperty);
            return inspector;
        }

        protected Inspector CreateParamProperty(Control Container,
            GetPropertyValueFunction getPropertyParamValueFunction,
            SetPropertyValueFunction setPropertyParamValueFunction,
            GetPropertyValueFunction getPropertyOperateValueFunction,
            SetPropertyValueFunction setPropertyOperateValueFunction,
            GetPropertyValueFunction getPropertyEditorValueFunction,
            SetPropertyValueFunction setPropertyEditorValueFunction,
            SetPropertyValueFunction removeParamFunction)
        {
            Inspector_AnimatorProperty_ParamGeneral inspector = new Inspector_AnimatorProperty_ParamGeneral();
            inspector.GetOperateValueFunction = getPropertyOperateValueFunction;
            inspector.SetOperateValueFunction = setPropertyOperateValueFunction;

            inspector.GetEditorValueFunction = getPropertyEditorValueFunction;
            inspector.SetEditorValueFunction = setPropertyEditorValueFunction;

            inspector.RemoveParamFunction = removeParamFunction;

            ObjectProperty property = new ObjectProperty();
            property.Object = StateCompareOperate.Equal;
            property.Name = typeof(StateCompareOperate).ToString();
            property.Type = typeof(StateCompareOperate);
            property.ReadOnly = false;
            property.GetPropertyValueFunction = getPropertyParamValueFunction;
            property.SetPropertyValueFunction = setPropertyParamValueFunction;

            for (int i = 0; i < _Context.ParamCount(); ++i)
            {
                IAnimatorParam param = _Context.GetParam(i);
                inspector.animParams.Add(param);
            }

            inspector.InspectProperty(property);
            return inspector;
        }

        protected void OnRemoveButtonClicked(Button Sender)
        {
            _Context.RemoveStatesLink(_Link.LinkDesc);
        }

        protected void OnButtonAddParamClicked(Button Sender)
        {
            int paramNumber = _Context.ParamCount();
            if (paramNumber == 0)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Need at least one param.");
                return;
            }

            Type[] elementTypes = _Context.GetParam(0).GetType().GetGenericArguments();

            Type paramType = typeof(AnimatorParam<>);
            Type genericType = paramType.MakeGenericType(elementTypes);

            IAnimatorParam param = (IAnimatorParam)Activator.CreateInstance(genericType, new object[] { _Context.GetParam(0) });
            _Context.ChangeStateLinkProperty(_Link.LinkDesc, (simpleLink) => { simpleLink.AddNewParamForLink(param); return true; });
        }


    }
}
