using EditorUI;
using System;

namespace CrossEditor
{
    internal class AnimatorStateLink : IComparable<AnimatorStateLink>, IComparable<IAnimatorStateLink>
    {
        #region Inspector Parameter

        internal class InspectorParameter
        {
            internal AnimatorStateLink Data;
            internal AnimatorContext Context;

            internal InspectorParameter(AnimatorStateLink InData, AnimatorContext InContext)
            {
                Data = InData;
                Context = InContext;
            }
        }

        #endregion

        protected AnimatorStatePanel _AnimStatePanelSource;

        protected AnimatorStatePanel _AnimStatePanelTarget;

        protected IAnimatorStateLink _LinkDesc;

        protected Vector2f _AnimStatePanelStartPos = new Vector2f(0, 0);

        protected Vector2f _AnimStatePanelEndPos = new Vector2f(0, 0);

        protected bool _IsSelected = false;

        protected readonly Color SelectColor = Color.FromRGB(120, 120, 250);

        protected readonly Color UnselectColor = Color.White;

        public AnimatorStatePanel Source
        {
            get { return _AnimStatePanelSource; }
        }

        public AnimatorStatePanel Target
        {
            get { return _AnimStatePanelTarget; }

            protected set { _AnimStatePanelTarget = value; }
        }

        public IAnimatorStateLink LinkDesc
        {
            get { return _LinkDesc; }
        }

        public AnimatorStateLink(AnimatorStatePanel Src)
        {
            _AnimStatePanelSource = Src;
            _AnimStatePanelTarget = null;
            _LinkDesc = null;
        }

        public bool IsStartState(AnimatorStatePanel Panel)
        {
            return Source == Panel;
        }

        public void Link(AnimatorStatePanel Target, IAnimatorStateLink Desc)
        {
            _AnimStatePanelTarget = Target;
            _LinkDesc = Desc;

            _AnimStatePanelSource.OnStatePanelBeSourceCreateLink(_AnimStatePanelTarget, this);
            _AnimStatePanelTarget.OnStatePanelBeTargetCreateLink(_AnimStatePanelSource, this);
        }

        public void OnAnimStatePanelPaint(IAnimatorUiLayerDesc Sender)
        {
            if (Target == null)
                OnAnimStatePanelPaint_Unfocus(Sender);
            else
                OnAnimStatePanelPaint_Linked(Sender);
        }

        public void OnAnimStatePanelPaint_Unfocus(IAnimatorUiLayerDesc Sender)
        {
            Vector2i start = new Vector2i(_AnimStatePanelSource.GetX() + (int)(_AnimStatePanelSource.GetWidth() * 0.5f),
                _AnimStatePanelSource.GetY() + (int)(_AnimStatePanelSource.GetHeight() * 0.5f));
            Vector2i end = new Vector2i(0, 0);

            AnimatorStatePanel focusTarget = Sender.TemporaryTargetPanel;

            int offsetSum = 0;
            if (focusTarget == null)
            {
                Device device = Source.GetDevice();
                int mouseX = device.GetMouseX();
                int mouseY = device.GetMouseY();
                AnimatorUI.ConvertToLocalCoordinate(_AnimStatePanelSource.GetParent(), mouseX, mouseY, ref end.X, ref end.Y);
            }
            else
            {
                float offsetGap = focusTarget.GetWidth() / 8.0f;

                int linkedNum = Sender.StatesLinkedNumber(Source, focusTarget);
                offsetSum = linkedNum % 2 > 0 ? 1 : -1;
                offsetSum *= (int)offsetGap * (int)Math.Floor((linkedNum / 2.0) + 0.5);

                end = new Vector2i(focusTarget.GetX() + (int)(focusTarget.GetWidth() * 0.5f),
                    focusTarget.GetY() + (int)(focusTarget.GetHeight() * 0.5f));
            }

            _AnimStatePanelStartPos.X = start.X * 1.0f;
            _AnimStatePanelStartPos.Y = start.Y * 1.0f;

            _AnimStatePanelEndPos.X = end.X * 1.0f;
            _AnimStatePanelEndPos.Y = end.Y * 1.0f;

            Vector2f direct = (_AnimStatePanelEndPos - _AnimStatePanelStartPos).Normalize();
            Vector2f normal = new Vector2f(direct.Y, direct.X * -1.0f);

            if (Line.Orient(
                _AnimStatePanelStartPos,
                _AnimStatePanelEndPos,
                _AnimStatePanelEndPos + normal * (float)_AnimStatePanelSource.GetWidth()) == Line.Orientation.Clockwise)
                normal = normal * -1.0f;

            Vector2f offsetF = normal * offsetSum;
            Vector2i offsetI = new Vector2i((int)offsetF.X, (int)offsetF.Y);

            AnimatorUI.GetInstance().DrawLink(start + offsetI, end + offsetI, 3.0f, 0.15f, Color.White);
        }

        public void OnAnimStatePanelPaint_Linked(IAnimatorUiLayerDesc Sender)
        {
            Vector2i start = new Vector2i(_AnimStatePanelSource.GetX() + (int)(_AnimStatePanelSource.GetWidth() * 0.5f),
                _AnimStatePanelSource.GetY() + (int)(_AnimStatePanelSource.GetHeight() * 0.5f));
            Vector2i end = new Vector2i(_AnimStatePanelTarget.GetX() + (int)(_AnimStatePanelTarget.GetWidth() * 0.5f),
                _AnimStatePanelTarget.GetY() + (int)(_AnimStatePanelTarget.GetHeight() * 0.5f));

            _AnimStatePanelStartPos.X = start.X * 1.0f;
            _AnimStatePanelStartPos.Y = start.Y * 1.0f;

            _AnimStatePanelEndPos.X = end.X * 1.0f;
            _AnimStatePanelEndPos.Y = end.Y * 1.0f;

            Vector2f direct = (_AnimStatePanelEndPos - _AnimStatePanelStartPos).Normalize();
            Vector2f normal = new Vector2f(direct.Y, direct.X * -1.0f);

            if (Line.Orient(
                _AnimStatePanelStartPos,
                _AnimStatePanelEndPos,
                _AnimStatePanelEndPos + normal * (float)_AnimStatePanelSource.GetWidth()) == Line.Orientation.Clockwise)
                normal = normal * -1.0f;

            int paramIndex = Sender.StatesLinkedIndex(this);
            float offsetGap = _AnimStatePanelTarget.GetWidth() / 8.0f;
            int offsetSum = paramIndex % 2 > 0 ? 1 : -1;
            offsetSum *= (int)offsetGap * (int)Math.Floor((paramIndex / 2.0) + 0.5);

            Vector2f offsetF = normal * offsetSum;
            _AnimStatePanelStartPos += offsetF;
            _AnimStatePanelEndPos += offsetF;

            Vector2i offsetI = new Vector2i((int)offsetF.X, (int)offsetF.Y);
            AnimatorUI.GetInstance().DrawLink(start + offsetI, end + offsetI, 3.0f, 0.15f, _IsSelected ? SelectColor : UnselectColor);
        }

        public bool TrySelect(int MouseX, int MouseY)
        {
            AnimatorUI ui = AnimatorUI.GetInstance();

            Vector2i screenStart = ui.ConvertStatePanelToScreenCoordinate((int)_AnimStatePanelStartPos.X, (int)_AnimStatePanelStartPos.Y);
            Vector2i screenEnd = ui.ConvertStatePanelToScreenCoordinate((int)_AnimStatePanelEndPos.X, (int)_AnimStatePanelEndPos.Y);

            _IsSelected = Line.OnSegment(new Vector2f(screenStart.X * 1.0f, screenStart.Y * 1.0f),
                new Vector2f(screenEnd.X * 1.0f, screenEnd.Y * 1.0f),
                new Vector2f(MouseX, MouseY),
                10.0f);

            return _IsSelected;
        }

        public void UnSelect()
        {
            _IsSelected = false;
        }

        public int CompareTo(AnimatorStateLink other)
        {
            return (_AnimStatePanelSource == other.Source &&
                _AnimStatePanelTarget == other.Target &&
                _LinkDesc == other.LinkDesc) ? 0 : -1;
        }

        public int CompareTo(IAnimatorStateLink other)
        {
            return _LinkDesc == other ? 0 : -1;
        }
    }
}
