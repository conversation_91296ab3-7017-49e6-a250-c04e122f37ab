using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{

    internal class AnimatorUiLayersController
    {
        protected class Layer
        {
            public IAnimatorUiLayerDesc Desc;
            public IAnimatorUiLayerCtrl Ctrl;

            public Layer(AnimatorUiBasicLayer layer)
            {
                Desc = layer;
                Ctrl = layer;
            }
        }

        List<Layer> _Layers = new List<Layer>();

        public IAnimatorUiLayerDesc GetCurrentLayerDesc() { return _Layers[_Layers.Count - 1].Desc; }

        public IAnimatorUiLayerCtrl GetCurrentLayerCtrl() { return _Layers[_Layers.Count - 1].Ctrl; }

        public AnimatorUiLayersController(Panel Container)
        {
            _Layers.Add(new Layer(new AnimatorUiBasicLayer(Container)));
        }
    }
}
