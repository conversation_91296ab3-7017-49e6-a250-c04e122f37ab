using EditorUI;
using System;

namespace CrossEditor
{
    internal class Inspector_Anim_General : Inspector_Property_General
    {
        protected Button _ButtonParameter;

        protected AnimatorContext _AnimContext;

        protected EditAnimParamName _EditName;

        public Inspector_Anim_General(AnimatorContext Context)
        {
            _AnimContext = Context;
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = _SelfContainer;

            // Hide Label Name
            string title = _LabelName.GetText();
            Container.RemoveChild(_LabelName);

            // Replace by btn which could pop menu
            _ButtonParameter = new Button();
            _ButtonParameter.SetText(title);
            _ButtonParameter.SetFontSize(PROPERTY_FONT_SIZE);
            _ButtonParameter.SetTextAlign(TextAlign.CenterLeft);
            _ButtonParameter.ClickedEvent += OnParameterButtonClicked;
            Container.AddChild(_ButtonParameter);

            _EditName = new EditAnimParamName(_AnimContext, this);
            _EditName.SetFontSize(PROPERTY_FONT_SIZE);
            _EditName.Initialize(EditMode.Simple_SingleLine);
            _EditName.LoadSource("");
            _EditName.SetText(title);
            _EditName.SetVisible(false);
            Container.AddChild(_EditName);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int LabelNameWidth = GetNameWidth();
            int LabelNameHeight = 20;

            _ButtonParameter.SetPosition(SPAN_X, _EditValue.GetY(), LabelNameWidth, LabelNameHeight);
            _EditName.SetPosition(SPAN_X, _EditValue.GetY(), LabelNameWidth, LabelNameHeight);
        }

        protected void OnParameterButtonClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            BulidParamsMenuItems(MenuContextMenu);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        protected void BulidParamsMenuItems(Menu Menu)
        {
            MenuItem MenuItem_Remove = new MenuItem();
            MenuItem_Remove.SetText("Remove");
            MenuItem_Remove.ClickedEvent += OnMenuItemRemoveParam;

            MenuItem MenuItem_Rename = new MenuItem();
            MenuItem_Rename.SetText("Rename");
            MenuItem_Rename.ClickedEvent += OnMenuItemRenameParam;

            Menu.AddMenuItem(MenuItem_Remove);
            Menu.AddMenuItem(MenuItem_Rename);
        }

        protected void OnMenuItemRemoveParam(MenuItem Item)
        {
            _AnimContext.RemoveParam(_ObjectProperty.Name);
        }

        protected void OnMenuItemRenameParam(MenuItem Item)
        {
            _EditName.SetVisible(true);
            _EditName.SetFocus();
        }
    }

    internal class Inspector_Anim_Int : Inspector_Anim_General
    {
        public Inspector_Anim_Int(AnimatorContext Context) : base(Context) { }

        public override void WriteValue()
        {
            base.WriteValue();

            string ValueString = _EditValue.GetText();
            object NewValue = null;

            NewValue = MathHelper.ParseInt(ValueString);
            SetPropertyValue(NewValue);
            _EditValue.SetText(NewValue.ToString());
        }
    }

    internal class Inspector_Anim_Float : Inspector_Anim_General
    {
        public Inspector_Anim_Float(AnimatorContext Context) : base(Context) { }

        public override void WriteValue()
        {
            base.WriteValue();

            string ValueString = _EditValue.GetText();
            object NewValue = null;

            if (ValueString.Length != 0 &&
                ValueString[ValueString.Length - 1] != '.')
            {
                NewValue = MathHelper.ParseFloat(ValueString);
                SetPropertyValue(NewValue);
                _EditValue.SetText(NewValue.ToString());
            }
            else
                _EditValue.SetText(ValueString);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = "";
            if (PropertyValue != null)
                PropertyValueString = PropertyValue.ToString();
            else
                PropertyValueString = "<null>";

            float value = MathHelper.ParseFloat(PropertyValue.ToString());
            if ((value - MathF.Floor(value)) < 0.00001f)
                PropertyValueString += ".0";

            _EditValue.SetText(PropertyValueString);
        }
    }
}
