using EditorUI;

namespace CrossEditor
{
    internal class Inspector_Anim_Bool : Inspector_Property_Bool
    {
        protected Button _ButtonParameter;

        protected AnimatorContext _AnimContext;

        protected EditAnimParamName _EditName;

        public Inspector_Anim_Bool(AnimatorContext Context)
        {
            _AnimContext = Context;
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = _SelfContainer;

            // Hide Label Name
            string title = _LabelName.GetText();
            Container.RemoveChild(_LabelName);

            // Replace by btn which could pop menu
            _ButtonParameter = new Button();
            _ButtonParameter.SetText(title);
            _ButtonParameter.SetFontSize(PROPERTY_FONT_SIZE);
            _ButtonParameter.SetTextAlign(TextAlign.CenterLeft);
            _ButtonParameter.ClickedEvent += OnParameterButtonClicked;
            Container.AddChild(_ButtonParameter);

            _EditName = new EditAnimParamName(_AnimContext, this);
            _EditName.SetFontSize(PROPERTY_FONT_SIZE);
            _EditName.Initialize(EditMode.Simple_SingleLine);
            _EditName.LoadSource("");
            _EditName.SetText(title);
            _EditName.SetVisible(false);
            Container.AddChild(_EditName);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int LabelNameWidth = GetNameWidth();
            int LabelNameHeight = 20;

            _ButtonParameter.SetPosition(SPAN_X, _CheckValue.GetY(), LabelNameWidth, LabelNameHeight);
            _EditName.SetPosition(SPAN_X, _CheckValue.GetY(), LabelNameWidth, LabelNameHeight);
        }

        protected void OnParameterButtonClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            BulidParamsMenuItems(MenuContextMenu);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        protected void BulidParamsMenuItems(Menu Menu)
        {
            MenuItem MenuItem_Remove = new MenuItem();
            MenuItem_Remove.SetText("Remove");
            MenuItem_Remove.ClickedEvent += OnMenuItemRemoveParam;

            MenuItem MenuItem_Rename = new MenuItem();
            MenuItem_Rename.SetText("Rename");
            MenuItem_Rename.ClickedEvent += OnMenuItemRenameParam;

            Menu.AddMenuItem(MenuItem_Remove);
            Menu.AddMenuItem(MenuItem_Rename);
        }

        protected void OnMenuItemRemoveParam(MenuItem Item)
        {
            _AnimContext.RemoveParam(_ObjectProperty.Name);
        }

        protected void OnMenuItemRenameParam(MenuItem Item)
        {
            _EditName.SetVisible(true);
            _EditName.SetFocus();
        }
    }
}
