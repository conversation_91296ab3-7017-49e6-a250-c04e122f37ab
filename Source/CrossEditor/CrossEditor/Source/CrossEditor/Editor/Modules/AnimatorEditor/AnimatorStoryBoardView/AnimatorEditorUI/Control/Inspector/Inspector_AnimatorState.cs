using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    internal class Inspector_AnimatorState : Inspector
    {
        // Ui -- Descriptor
        IAnimatorState _StateData;
        AnimatorContext _Context;

        // Ui -- State Name
        Panel _StateHeadIcon;
        Edit _StateHeadNameEdit;

        List<Inspector> _StateProperties = new List<Inspector>();

        public Inspector_AnimatorState()
        {
        }

        public override void InspectObject(object Object1, object Tag = null)
        {
            var parameter = Object1 as AnimatorStatePanel.InspectorParameter;
            Control Container = _SelfContainer;
            _StateData = parameter.Data;
            _Context = parameter.Context;

            // State Head Ui
            _StateHeadIcon = new Panel();
            _StateHeadIcon.Initialize();
            _StateHeadIcon.SetImage(UIManager.LoadUIImage("Editor/Game/GameObject.png"));
            Container.AddChild(_StateHeadIcon);

            _StateHeadNameEdit = new Edit();
            _StateHeadNameEdit.SetFontSize(16);
            _StateHeadNameEdit.Initialize(EditMode.Simple_SingleLine);
            _StateHeadNameEdit.LoadSource("");
            Container.AddChild(_StateHeadNameEdit);
            EditContextUI.GetInstance().RegisterEdit(_StateHeadNameEdit);
            _StateHeadNameEdit.SetSize(100, 13);
            _StateHeadNameEdit.SetText(_StateData.NameProperty);
            _StateHeadNameEdit.SelfFocusChangedEvent += OnStateNameEditorFocusChanged;

            // State Property -- Animation Path
            Inspector Property = CreateStateProperty(Container, typeof(string), false, true, "Animation Path", "Need a Animation Path",
                (Object, PropertyName, ValueExtraProperty) => { return _StateData.PathProperty; },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                {
                    _Context.ChangeStateProperty(_StateData.NameProperty, (s) => { s.PathProperty = PropertyValue as string; return false; });
                });
            _StateProperties.Add(Property);

            // State Property -- Animation Start Num
            Property = CreateStateProperty(Container, typeof(uint), false, true, "Start Num", "",
                (Object, PropertyName, ValueExtraProperty) => { return _StateData.StartNumProperty; },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                {
                    _Context.ChangeStateProperty(_StateData.NameProperty, (s) => { s.StartNumProperty = Convert.ToInt32(PropertyValue); return false; });
                });
            _StateProperties.Add(Property);

            // State Property -- Animation End Num
            Property = CreateStateProperty(Container, typeof(uint), false, true, "End Num", "",
                (Object, PropertyName, ValueExtraProperty) => { return _StateData.EndNumProperty; },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                {
                    _Context.ChangeStateProperty(_StateData.NameProperty, (s) => { s.EndNumProperty = Convert.ToInt32(PropertyValue); return false; });
                });
            _StateProperties.Add(Property);

            // State Property -- Animation Loop Property
            Property = CreateStateProperty(Container, typeof(bool), false, true, "Loop", "",
                (Object, PropertyName, ValueExtraProperty) => { return _StateData.LoopProperty; },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                {
                    _Context.ChangeStateProperty(_StateData.NameProperty, (s) => { s.LoopProperty = Convert.ToBoolean(PropertyValue); return false; });
                });
            _StateProperties.Add(Property);

            // State Property -- Slot Disabled
            Property = CreateStateProperty(Container, typeof(bool), false, false, "Slot Enable", "",
                (Object, PropertyName, ValueExtraProperty) => { return _StateData.IsSlotLayerValid(); },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                { });
            _StateProperties.Add(Property);

            // State Property -- Sub Layer Disabled
            Property = CreateStateProperty(Container, typeof(bool), false, false, "Sublayer Enable", "",
                (Object, PropertyName, ValueExtraProperty) => { return _StateData.IsStateSubLayerValid(); },
                (Object, PropertyName, PropertyValue, SubProperty) =>
                { });
            _StateProperties.Add(Property);

        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            Width = Math.Max(Width, 260);
            int SpanX = 5;
            _StateHeadIcon.SetPosition(SpanX, Y + 3, 20, 20);

            int EditNameX = _StateHeadIcon.GetEndX() + SpanX;
            int EditNameWidth = Math.Max(Width - SpanX - EditNameX, 100);
            _StateHeadNameEdit.SetPosition(EditNameX, Y + 7, EditNameWidth, 13);

            Y += 28;
            foreach (Inspector_Property Inspector in _StateProperties)
                Inspector.UpdateLayout(Width, ref Y);

            base.UpdateLayout(Width, ref Y);
        }

        protected Inspector CreateStateProperty(Control Container,
            Type PropertyType,
            bool IsEnum,
            bool IsEditValid,
            string Name,
            object Value,
            GetPropertyValueFunction GetPropertyValueFunction,
            SetPropertyValueFunction SetPropertyValueFunction)
        {
            Inspector Inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyType.ToString(), IsEnum);

            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = Value;
            ObjectProperty.Name = Name;
            ObjectProperty.Type = PropertyType;
            ObjectProperty.ReadOnly = !IsEditValid;
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;

            Inspector.InspectProperty(ObjectProperty);
            return Inspector;
        }

        #region Porperty modify callbacks

        protected void OnStateNameEditorFocusChanged(Control Sender)
        {
            if (Sender.IsFocused())
                return;

            string name = _StateHeadNameEdit.GetText();
            if (_Context.IsStateNameExist(name))
            {
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Warning, "New State Name existed!! please change a new one");
                _StateHeadNameEdit.SetText(_StateData.NameProperty);
                return;
            }

            _Context.ChangeStateProperty(_StateData.NameProperty, (state) =>
            {
                state.NameProperty = name;
                return true;
            });
        }

        #endregion

    }
}
