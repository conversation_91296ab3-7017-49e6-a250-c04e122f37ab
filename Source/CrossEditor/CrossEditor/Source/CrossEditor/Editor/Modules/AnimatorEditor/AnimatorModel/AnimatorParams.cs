using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    enum StateCompareOperate
    {
        Less,
        Greater,
        Equal,
        NotEqual
    }

    internal interface IAnimatorParam
    {
        string ToValue();
        System.Type ToType();

        string Name { get; set; }
        object Value { get; set; }

        StateCompareOperate Operate { get; set; }
    }

    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorParam<T> : IAnimatorParam where T : IComparable<T>
    {
        public T _Value;

        public string _Name;

        public StateCompareOperate _Operate;

        public virtual System.Type ToType() { return _Value.GetType(); }

        public virtual string ToValue() { return _Value.ToString(); }

        #region Serialize Into Json

        [JsonProperty("Type")]
        private string SerializeType
        {
            get { return ToType().ToString(); }
        }

        [JsonProperty("Name")]
        public virtual string Name
        {
            get { return _Name; }
            set { _Name = value; }
        }

        [JsonProperty("Value")]
        public virtual object Value
        {
            get { return _Value; }
            set { _Value = (T)Convert.ChangeType(value, typeof(T)); }
        }

        [JsonProperty("Operate")]
        [JsonConverter(typeof(StringEnumConverter))]
        public virtual StateCompareOperate Operate
        {
            get { return _Operate; }
            set { _Operate = value; }
        }

        #endregion

        public AnimatorParam() { }

        public AnimatorParam(T v, string n) { _Value = v; _Name = n; }

        public AnimatorParam(T v, string n, StateCompareOperate op) { _Value = v; _Name = n; _Operate = op; }

        public AnimatorParam(IAnimatorParam Other)
        {
            _Value = (T)Other.Value;
            _Name = Other.Name;
            _Operate = Other.Operate;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorParams
    {
        [JsonProperty("Parameters")]
        protected List<IAnimatorParam> _Parameters = new List<IAnimatorParam>();

        public void AddParam<T>(T v, string name) where T : IComparable<T>
        {
            if (FindPram(name) == null)
                _Parameters.Add(new AnimatorParam<T>(v, name));
        }

        public IAnimatorParam FindPram(string name)
        {
            return _Parameters.Find(item => item.Name == name);
        }

        public bool RemoveParam(string name)
        {
            IAnimatorParam param = _Parameters.Find(item => item.Name == name);
            if (param != null)
                return _Parameters.Remove(param);
            return false;
        }

        public IAnimatorParam GetParam(int index) { return _Parameters[index]; }

        public int Count() { return _Parameters.Count; }

        public void OnDeserializeFinished() { }
    }
}
