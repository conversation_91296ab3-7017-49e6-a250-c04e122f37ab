using EditorUI;

namespace CrossEditor
{
    internal class AnimatorStateEntry : AnimatorStatePanel
    {
        protected IAnimatorEntry _Entry;

        public AnimatorStateEntry(AnimatorContext Context) : base(Context.GetBasicLayer().GetEntryState().GetState(), Context)
        {
            _Entry = Context.GetBasicLayer().GetEntryState();
            SetPosition((int)_Data.GetUiControl().UiPosition.X,
                (int)_Data.GetUiControl().UiPosition.Y,
                (int)_Data.GetUiControl().UiSize.X,
                (int)_Data.GetUiControl().UiSize.Y);

            _UnSelectColor = Color.FromRGBA(60, 210, 60, 255);
            _SelectColor = Color.FromRGBA(90, 240, 90, 255);

            SetBackgroundColor(_UnSelectColor);
        }

        public override void OnStatePanelBeSourceCreateLink(AnimatorStatePanel Target, AnimatorStateLink UiLink)
        {
            _Links.Clear();
            _Links.Add(UiLink);
        }
    }
}
