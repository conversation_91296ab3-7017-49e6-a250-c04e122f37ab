using EditorUI;

namespace CrossEditor
{
    internal class EditAnimParamName : Edit
    {
        protected AnimatorContext _AnimContext;

        protected Inspector_Property _Property;

        public EditAnimParamName(AnimatorContext Context, Inspector_Property Property)
        {
            _AnimContext = Context;
            _Property = Property;
        }

        public override void OnFocusChanged()
        {
            string content = GetText();
            base.OnFocusChanged();

            if (!this.IsFocused() && content != "" && _Property.GetPropertyName() != content)
                _AnimContext.RenameParam(_Property.GetPropertyName(), content == "" ? "Need a Name" : content);

            if (!this.IsFocused())
                this.SetVisible(false);
        }

    }
}
