using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{

    #region Version0

    internal partial class AnimatorContext
    {
        public AnimatorContext(AnimatorData.Version0.Warrrper Warrper)
        {
            // Creata Parameters
            foreach (AnimatorData.Version0.Attribute attribute in Warrper.AnimatorAttributes.Parameters)
            {
                if (attribute.Type.ToLower().Contains("int32"))
                    this.AddParam(Convert.ToInt32(attribute.Value), attribute.Name);
                else if (attribute.Type.ToLower().Contains("bool"))
                    this.AddParam(Convert.ToBoolean(attribute.Value), attribute.Name);
                else if (attribute.Type.ToLower().Contains("single"))
                    this.AddParam(Convert.ToSingle(attribute.Value), attribute.Name);
            }

            // Create States by Traverse 
            foreach (AnimatorData.Version0.State state in Warrper.BasicLayer.States)
            {
                _BasicLayer.AddAnimatorState(
                    state.Name, (int)state.UiPosition.X, (int)state.UiPosition.Y, (int)state.UiSize.X, (int)state.UiSize.Y);

                AnimatorDescSimpleNode simpleNode = _BasicLayer.FindAnimatorState(state.Name) as AnimatorDescSimpleNode;
                simpleNode.LoopProperty = state.Loop;
                simpleNode.StartNumProperty = state.StartNum;
                simpleNode.EndNumProperty = state.EndNum;
                simpleNode.PathProperty = state.Path;

                // Create State links
                foreach (AnimatorData.Version0.Link link in state.Links)
                {
                    IAnimatorStateLink iLink = simpleNode.AnimAddLink(_BasicLayer.FindAnimatorState(link.To));

                    AnimatorData.Version0.Link tempLink = link;
                    ChangeStateLinkProperty(iLink, (simpleLink) =>
                    {
                        simpleLink.StartBlend = tempLink.StartBlend;
                        simpleLink.EndBlend = tempLink.EndBlend;
                        simpleLink.DeltaFrame = tempLink.DeltaFrame;
                        simpleLink.TransitMode = tempLink.TransitMode.ToLower().Contains("zero") ? StateTransitMode.Zero : StateTransitMode.Match;

                        // Create State link parameters
                        foreach (AnimatorData.Version0.Attribute attribute in tempLink.Parameters)
                        {
                            StateCompareOperate operate = StateCompareOperate.Equal;
                            if (attribute.Operate.ToLower().Contains("less"))
                                operate = StateCompareOperate.Less;
                            else if (attribute.Operate.ToLower().Contains("notequal"))
                                operate = StateCompareOperate.NotEqual;
                            else if (attribute.Operate.ToLower().Contains("equal"))
                                operate = StateCompareOperate.Equal;
                            else if (attribute.Operate.ToLower().Contains("great"))
                                operate = StateCompareOperate.Greater;

                            if (attribute.Type.ToLower().Contains("int32"))
                                simpleLink.AddNewParamForLink(Convert.ToInt32(attribute.Value), attribute.Name, operate);
                            else if (attribute.Type.ToLower().Contains("bool"))
                                simpleLink.AddNewParamForLink(Convert.ToBoolean(attribute.Value), attribute.Name, operate);
                            else if (attribute.Type.ToLower().Contains("single"))
                                simpleLink.AddNewParamForLink(Convert.ToSingle(attribute.Value), attribute.Name, operate);
                        }

                        return false;
                    });
                }
            }
        }
    }

    namespace AnimatorData
    {
        namespace Version0
        {
            internal class Warrrper
            {
                [JsonProperty]
                public Attributes AnimatorAttributes { get; set; }

                [JsonProperty]
                public BasicLayer BasicLayer { get; set; }

                public AnimatorContext Context()
                {
                    AnimatorContext context = new AnimatorContext(this);
                    return context;
                }
            }

            [JsonObject(MemberSerialization.OptIn)]
            internal class Attribute
            {
                [JsonProperty]
                public string Type { get; set; }

                [JsonProperty]
                public string Name { get; set; }

                [JsonProperty]
                public object Value { get; set; }

                [JsonProperty]
                public string Operate { get; set; }
            }

            [JsonObject(MemberSerialization.OptIn)]
            internal class Attributes
            {
                [JsonProperty]
                public List<Attribute> Parameters { get; set; }
            }

            [JsonObject(MemberSerialization.OptIn)]
            internal class Link
            {
                [JsonProperty]
                public List<Attribute> Parameters { get; set; }

                [JsonProperty]
                public string From { get; set; }

                [JsonProperty]
                public string To { get; set; }

                [JsonProperty]
                public int StartBlend { get; set; }

                [JsonProperty]
                public int EndBlend { get; set; }

                [JsonProperty]
                public int DeltaFrame { get; set; }

                [JsonProperty]
                public string TransitMode { get; set; }
            }

            [JsonObject(MemberSerialization.OptIn)]
            internal class State
            {
                [JsonProperty]
                public List<Link> Links { get; set; }

                [JsonProperty]
                public string Name { get; set; }

                [JsonProperty]
                public string Path { get; set; }

                [JsonProperty]
                public int StartNum { get; set; }

                [JsonProperty]
                public int EndNum { get; set; }

                [JsonProperty]
                public bool Loop { get; set; }

                [JsonProperty]
                public Vector2f UiPosition { get; set; }

                [JsonProperty]
                public Vector2f UiSize { get; set; }
            }

            [JsonObject(MemberSerialization.OptIn)]
            internal class BasicLayer
            {
                [JsonProperty]
                public List<State> States { get; set; }

                [JsonProperty]
                public State Entry { get; set; }

                [JsonProperty]
                public State Any { get; set; }
            }
        }
    }

    #endregion

    internal class AnimatorSerializer
    {

        [JsonObject(MemberSerialization.OptIn)]
        private class AnimatorVersionWarrper
        {
            AnimatorVersion _Version = new AnimatorVersion();

            [JsonProperty("AnimatorVersion")]
            public AnimatorVersion Version { get { return _Version; } set { _Version = value; } }
        }

        public void Serialize(string Path, AnimatorContext Context)
        {
            JsonSerializerSettings jsetting = new JsonSerializerSettings();
            jsetting.NullValueHandling = NullValueHandling.Include;
            jsetting.TypeNameHandling = TypeNameHandling.Auto;

            string json = JsonConvert.SerializeObject(Context, Formatting.Indented, jsetting);

            if (!File.Exists(Path))
                File.Create(Path).Dispose();

            try
            {
                TextWriter tw = new StreamWriter(Path, false);
                tw.Write(json);
                tw.Close();
            }
            catch (Exception e)
            {
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Error, e.ToString());
            }
        }

        public AnimatorContext Deserialize(string Path)
        {
            if (!File.Exists(Path))
                return null;

            string jsonStr = File.ReadAllText(Path, System.Text.Encoding.ASCII);
            AnimatorVersionWarrper versionWarrper = JsonConvert.DeserializeObject<AnimatorVersionWarrper>(jsonStr);
            if (versionWarrper.Version.VersionId < AnimatorVersion.SUPPORT_MIN_ANIMATOR_FILE_VERSION_ID ||
                versionWarrper.Version.VersionId > AnimatorVersion.CURRENT_ANIMATOR_FILE_VERSION_ID)
                CommonDialogUI.ShowSimpleOKDialog(UIManager.GetMainUIManager(), "Tips", "Animator file is broken.");

            if (versionWarrper.Version.VersionId ==
                AnimatorVersion.CURRENT_ANIMATOR_FILE_VERSION_ID)
                return Deserialize_CurrentVersion(jsonStr);

            if (versionWarrper.Version.VersionId == 0)
                return Deserialize_Version0(jsonStr);

            return null;
        }

        protected AnimatorContext Deserialize_CurrentVersion(string JsonStr)
        {
            JsonSerializerSettings jsetting = new JsonSerializerSettings();
            jsetting.NullValueHandling = NullValueHandling.Include;
            jsetting.TypeNameHandling = TypeNameHandling.Auto;

            AnimatorContext context = JsonConvert.DeserializeObject<AnimatorContext>(JsonStr, jsetting);
            return context;
        }

        protected AnimatorContext Deserialize_Version0(string JsonStr)
        {
            JsonSerializerSettings jsetting = new JsonSerializerSettings();
            jsetting.NullValueHandling = NullValueHandling.Include;

            AnimatorData.Version0.Warrrper warrper =
                JsonConvert.DeserializeObject<AnimatorData.Version0.Warrrper>(JsonStr, jsetting);

            return warrper.Context();
        }

    }
}
