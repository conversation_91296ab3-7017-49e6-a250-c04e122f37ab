using EditorUI;

namespace CrossEditor
{
    internal class AnimatorStateAny : AnimatorStatePanel
    {
        public AnimatorStateAny(AnimatorContext Context) : base(Context.GetBasicLayer().GetAnyState(), Context)
        {
            SetPosition((int)_Data.GetUiControl().UiPosition.X,
                (int)_Data.GetUiControl().UiPosition.Y,
                (int)_Data.GetUiControl().UiSize.X,
                (int)_Data.GetUiControl().UiSize.Y);

            _UnSelectColor = Color.FromRGBA(110, 200, 200, 255);
            _SelectColor = Color.FromRGBA(140, 240, 240, 255);

            SetBackgroundColor(Color.FromRGBA(110, 200, 200, 255));
        }

    }
}
