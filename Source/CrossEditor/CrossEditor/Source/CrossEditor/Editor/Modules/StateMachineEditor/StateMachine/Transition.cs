using CEngine;
using EditorUI;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text.Json.Nodes;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class TransitionData
    {
        [JsonProperty("Name")]
        public string Name;
        [JsonProperty("From")]
        public string From;
        [JsonProperty("To")]
        public string To;
        [JsonProperty("Rule")]
        public string Rule;
        [JsonProperty("BlendTime")]
        public float BlendTime;
        [JsonProperty("BlendMode")]
        public string BlendMode;
        [JsonProperty("RuleGraphs")]
        public List<TransitionRuleGraphData> RuleGraphs;
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class TransitionRuleGraphData
    {
        [JsonProperty("Nodes")]
        public List<JObject> Nodes = new List<JObject>();
        [JsonProperty("Links")]
        public List<LinkData> Links = new List<LinkData>();
    }
    public enum BlendMode
    {
        CrossFade,
        Inertialization
    }

    public class Transition : Connection
    {
        public Vector2f Start;

        List<TransitionRule> Rules;

        public const int Margin = 2;
        public bool bSelected = false;

        float _BlendTime = 0.2F;
        BlendMode _BlendMode = BlendMode.CrossFade;

        [PropertyInfo(bHide = true)]
        public Vector2f End
        {
            get => TempEnd;
            set => TempEnd = value;
        }

        public Transition() : base()
        {
            Start = new Vector2f();
            End = new Vector2f();
            Rules = new List<TransitionRule>();
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Transition Blend Time")]
        public float BlendTime
        {
            get => _BlendTime; set => _BlendTime = value;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Transition Blend Mode")]
        public BlendMode TransitionBlendMode
        {
            get => _BlendMode; set => _BlendMode = value;
        }

        public void AddRule(TransitionRule Rule)
        {
            if (Rules.Contains(Rule) == false)
            {
                Rule.SetTransition(this);
                Rules.Add(Rule);
            }
        }

        public void RemoveRule(TransitionRule Rule)
        {
            if (Rules.Contains(Rule) == true)
            {
                Rules.Remove(Rule);
            }
        }

        public List<TransitionRule> GetRules()
        {
            return Rules;
        }

        public StateGraph GetInStateGraph()
        {
            if (InSlot.Node is StateNode)
            {
                return (InSlot.Node as StateNode).StateGraph;
            }
            else
            {
                return null;
            }
        }

        public StateGraph GetOutStateGraph()
        {
            if (OutSlot.Node is StateNode)
            {
                return (OutSlot.Node as StateNode).StateGraph;
            }
            else
            {
                return null;
            }
        }

        public override void DoLayout()
        {
            if (InSlot == null)
            {
                Node StateNode = OutSlot.Node;
                Start = CalculatePosition(StateNode, End);
            }
            else
            {
                Node OutputNode = OutSlot.Node;
                Node InputNode = InSlot.Node;

                Vector2f LineCenter = (new Vector2f(OutputNode.X + OutputNode.Width / 2, OutputNode.Y + OutputNode.Height / 2) +
                    new Vector2f(InputNode.X + InputNode.Width / 2, InputNode.Y + InputNode.Height / 2)) * 0.5F;
                Start = CalculatePosition(OutputNode, LineCenter);
                End = CalculatePosition(InputNode, LineCenter);
                // make offset
                Vector2f Direction = (Start - End).Normalize();
                // rotate PI/2;
                Vector2f Offset = new Vector2f(-Direction.Y, Direction.X) * 3;
                Start = Start + Offset;
                End = End + Offset;

                DoRulesLayout();
            }
        }

        void DoRulesLayout()
        {
            Vector2f Center = (Start + End) * 0.5F;
            Vector2f Direction = (Start - End).Normalize();
            Vector2f Offset = new Vector2f(-Direction.Y, Direction.X) * 20;

            float TotalLength = (TransitionRule.Size + TransitionRule.Interval) * (Rules.Count - 1);
            Vector2f BasePoint = Center + Offset + Direction * (TotalLength / 2);

            for (int i = 0; i < Rules.Count; i++)
            {
                Vector2f Point = BasePoint - Direction * (TransitionRule.Size + TransitionRule.Interval) * i;
                Rules[i].CenterX = Convert.ToInt32(Point.X);
                Rules[i].X = Rules[i].CenterX - TransitionRule.Size / 2;
                Rules[i].CenterY = Convert.ToInt32(Point.Y);
                Rules[i].Y = Rules[i].CenterY - TransitionRule.Size / 2;
            }
        }

        Vector2f CalculatePosition(Node RefNode, Vector2f RefPoint)
        {
            Vector2f Point = new Vector2f();
            // X
            if (RefPoint.X < RefNode.X - Margin)
            {
                Point.X = RefNode.X - Margin;
            }
            else if (RefPoint.X > RefNode.X + RefNode.Width + Margin + 1)
            {
                Point.X = RefNode.X + RefNode.Width + Margin + 1;
            }
            else
            {
                Point.X = RefPoint.X;
            }
            // Y
            if (RefPoint.Y < RefNode.Y - Margin)
            {
                Point.Y = RefNode.Y - Margin;
            }
            else if (RefPoint.Y > RefNode.Y + RefNode.Height + Margin + 1)
            {
                Point.Y = RefNode.Y + RefNode.Height + Margin;
            }
            else
            {
                Point.Y = RefPoint.Y;
            }
            return Point;
        }

        public override void Draw(UIManager UIManager)
        {
            // draw line
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Color Color = bSelected ? Color.FromRGB(255, 204, 0) : Color.FromRGB(245, 245, 245);
            Color = bDebugged ? Color.EDITOR_UI_HILIGHT_COLOR_BLUE : Color;

            float lineWidth = bSelected ? 2.0F : 1.5F;
            lineWidth = bDebugged ? 3.5F : lineWidth;
            GraphicsHelper.DrawLine(UIManager, Color, Start.X, Start.Y, End.X, End.Y, lineWidth);

            Vector2f Direction = (Start - End).Normalize() * 10;
            // use for rotation matrix
            float Theta = MathF.PI / 8;
            float Cos = MathF.Cos(Theta);
            float Sin = MathF.Sin(Theta);
            // rotate PI/8
            Vector2f Point1;
            Point1.X = Direction.X * Cos - Direction.Y * Sin;
            Point1.Y = Direction.X * Sin + Direction.Y * Cos;
            Point1 = End + Point1;
            // rotate -PI/8
            Vector2f Point2;
            Point2.X = Direction.X * Cos + Direction.Y * Sin;
            Point2.Y = -Direction.X * Sin + Direction.Y * Cos;
            Point2 = End + Point2;
            // draw arrow
            GraphicsHelper.DrawLine(UIManager, Color, End.X, End.Y, Point1.X, Point1.Y, lineWidth);
            GraphicsHelper.DrawLine(UIManager, Color, End.X, End.Y, Point2.X, Point2.Y, lineWidth);
            if (!(OutSlot.Node is StateEntryNode))
            {
                DrawRules(UIManager);
            }

            bDebugged = false;
        }

        void DrawRules(UIManager UIManager)
        {
            foreach (TransitionRule Rule in Rules)
            {
                Rule.Draw(UIManager);
            }
        }

        new public TransitionData ToData()
        {
            TransitionData data = new TransitionData();
            data.Name = ID.ToString();
            data.To = InSlot.Node.ID.ToString();
            data.From = OutSlot.Node.ID.ToString();
            data.Rule = string.Format("CanTransition = ({0}) ", Rules[0].ToExpression());
            for (int i = 1; i < Rules.Count; i++)
            {
                data.Rule = string.Format("{0} or ({1})", data.Rule, Rules[i].ToExpression());
            }

            data.BlendTime = BlendTime;
            data.BlendMode = TransitionBlendMode.ToString();

            data.RuleGraphs = new List<TransitionRuleGraphData>();
            foreach (var Rule in Rules)
            {
                var GraphData = Rule.TransitionRuleGraph.ToData();
                data.RuleGraphs.Add(GraphData);
            }
            return data;
        }

        public void FromData(TransitionData Data, StateMachineGraph Graph)
        {
            ID = int.Parse(Data.Name);
            int OutSlotID = int.Parse(Data.From);
            int InSlotID = int.Parse(Data.To);
            OutSlot = Graph.FindNodeByID(OutSlotID).GetOutSlot(0);
            InSlot = Graph.FindNodeByID(InSlotID).GetInSlot(0);

            BindInSlot(InSlot);
            BindOutSlot(OutSlot);

            BlendTime = Data.BlendTime;
            TransitionBlendMode = Enum.Parse<BlendMode>(Data.BlendMode);

            foreach (var RuleData in Data.RuleGraphs)
            {
                TransitionRule Rule = new TransitionRule();
                Rule.SetOwner(Graph);
                Rule.TransitionRuleGraph.FromData(RuleData);

                AddRule(Rule);
            }
        }

        public override void CloneTo(ref Connection Target)
        {
            Transition Transition = Target as Transition;

            Transition.BlendTime = BlendTime;
            Transition.TransitionBlendMode = TransitionBlendMode;

            Transition.Rules = new List<TransitionRule>();

            foreach (TransitionRule Rule in Rules)
            {
                Node NewRule = new TransitionRule();
                NewRule.SetOwner(Rule.GetOwner());
                Rule.CloneTo(ref NewRule);
                Transition.AddRule(NewRule as TransitionRule);
            }
        }
    }
}
