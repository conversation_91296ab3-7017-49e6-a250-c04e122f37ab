using CEngine;
using EditorUI;

namespace CrossEditor
{
    public struct SlotWrapper
    {
        public Slot InSlot;
        public Slot OutSlot;
    }

    public class StateNodeBase : Node
    {
        const int PinMargin = 12;
        const int SpanX = 3;
        const int SpanY = 3;
        const int OffsetY = 2;

        [PropertyInfo(bHide = true)]
        public Slot InSlot
        {
            get => InSlots[0];
            set => InSlots[0] = value;
        }

        [PropertyInfo(bHide = true)]
        public Slot OutSlot
        {
            get => OutSlots[0];
            set => OutSlots[0] = value;
        }

        public StateNodeBase() : base()
        {
            AddInSlot("In", SlotType.StateLink);
            AddOutSlot("Out", SlotType.StateLink);

            ContentX = 0;
            ContentY = 0;
            ContentWidth = 0;
            ContentHeight = 0;
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(Name) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGB(105, 105, 105);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, Name, Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }

        public override void DoLayout()
        {
            GetContentSize(ref ContentWidth, ref ContentHeight);
            ContentX = PinMargin;
            ContentY = PinMargin;
            Width = PinMargin + ContentWidth + PinMargin;
            Height = PinMargin + ContentHeight + PinMargin;
        }

        public override void DoLayoutWithConnections()
        {
            DoLayout();
            OutSlot.DoLayout_Connections();
            InSlot.DoLayout_Connections();
        }

        public override void Draw(UIManager UIManager)
        {
            if (!GetVisibility())
            {
                return;
            }
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color = Color.FromRGBA(0, 0, 0, 160);
            if (InSlot.bSelected | OutSlot.bSelected)
            {
                Color = Color.FromRGBA(255, 255, 0, 160);
            }
            GraphicsHelper.FillRectangle(UIManager, Color, X, Y, Width, Height);

            if (bSelected && bError)
            {
                GraphicsHelper.DrawRectangle(UIManager, Color.FromRGB(255, 102, 0), X, Y, Width, Height);
            }
            else if (bSelected)
            {
                GraphicsHelper.DrawRectangle(UIManager, Color.FromRGB(255, 204, 0), X, Y, Width, Height);
            }
            else if (bError)
            {
                GraphicsHelper.DrawRectangle(UIManager, Color.FromRGB(255, 0, 0), X, Y, Width, Height);
            }
            else
            {
                GraphicsHelper.DrawRectangle(UIManager, Color.FromRGB(0, 0, 0), X, Y, Width, Height);
            }

            if (bDebugged)
            {
                GraphicsHelper.DrawRectangleLines(UIManager, Color.EDITOR_UI_HILIGHT_COLOR_BLUE, X - 2, Y - 2, Width + 4, Height + 4, 4.0f);
                bDebugged = false;
            }

            DrawContent(UIManager, X + ContentX, Y + ContentY, ContentWidth, ContentHeight);
        }

        public override object HitTest(int WorldX, int WorldY)
        {
            if (UIManager.PointInRect(WorldX, WorldY, X, Y, Width, Height))
            {
                if (UIManager.PointInRect(WorldX, WorldY, X + ContentX, Y + ContentY, ContentWidth, ContentHeight))
                {
                    return this;
                }
                SlotWrapper Pins = new SlotWrapper();
                Pins.InSlot = InSlot;
                Pins.OutSlot = OutSlot;
                return Pins;
            }
            return null;
        }

        public override void CloneTo(ref Node Target)
        {
            base.CloneTo(ref Target);
        }
    }
}
