using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class StateMachineData
    {
        [JsonProperty("Name")]
        public string Name = "StateMachine";
        [JsonProperty("InitState")]
        public string InitState;
        [JsonProperty("States")]
        public List<StateNodeData> StateNodes;
        [JsonProperty("Conditions")]
        public List<ConduitNodeData> ConduitNodes;
        [JsonProperty("Transitions")]
        public List<TransitionData> Transitions;
    }

    public class StateMachineGraph : NodeGraphModel
    {
        public string FilePath;
        public StateEntryNode Entry;
        public bool bRunning;
        public StateNodeBase CurrentState;

        public StateMachineGraph() : base()
        {
            Type = NodeGraphType.StateMachineGraph;
            FilePath = "";
            CreateStateEntryNode();
            bRunning = false;
            CurrentState = null;
        }

        public void CreateStateEntryNode()
        {
            Entry = new StateEntryNode();
            Entry.X = 0;
            Entry.Y = 0;

            ImportNode(Entry);
        }

        public override object HitTest(int WorldX, int WorldY)
        {
            object HitObject = base.HitTest(WorldX, WorldY);
            if (HitObject != null)
            {
                return HitObject;
            }

            foreach (Transition Transition in Connections)
            {
                foreach (TransitionRule Rule in Transition.GetRules())
                {
                    HitObject = Rule.HitTest(WorldX, WorldY);
                    if (HitObject != null)
                    {
                        return HitObject;
                    }
                }

            }

            return null;
        }

        public override List<Node> BoxSelect(int X, int Y, int Width, int Height)
        {
            List<Node> SelectedNodes = base.BoxSelect(X, Y, Width, Height);

            foreach (Transition Transition in Connections)
            {
                if (Transition.OutSlot.Node is StateEntryNode)
                {
                    continue;
                }
                foreach (TransitionRule Rule in Transition.GetRules())
                {
                    if (Rule.RectInRect(X, Y, Width, Height))
                    {
                        SelectedNodes.Add(Rule);
                    }
                }
            }

            return SelectedNodes;
        }

        public override List<MenuBuilder> BuildMenu(object Context)
        {
            if (Context is Node)
            {
                if (Context is TransitionRule)
                {
                    // Transition Rule can only delete
                    return new List<MenuBuilder> { new MenuBuilder
                    {
                        Text = "Delete Rule",
                        Event = (Sender) =>
                        {
                            TransitionRule Rule = Context as TransitionRule;
                            EditOperation_RemoveTransitionRule EditOperation = new EditOperation_RemoveTransitionRule(this, Rule.Transition, new List<TransitionRule> { Rule });
                            EditOperationManager.GetInstance().AddOperation(EditOperation);
                            EditOperation.Redo();
                            View.SetModified();
                        }
                    } };
                }
                else
                {
                    return View.BuildNodeActionMenu();
                }
            }
            else if (Context is SlotWrapper)
            {
                return View.BuildSlotActionMenu(((SlotWrapper)Context).OutSlot);
            }
            else if (Context is Transition)
            {
                return BuildNodeMenu(Node =>
                {
                    Transition Transition = Context as Transition;
                    int MoveStep = NodeGraphView.MoveStep;
                    Node.X = Convert.ToInt32(Transition.TempEnd.X) / MoveStep * MoveStep;
                    Node.Y = Convert.ToInt32(Transition.TempEnd.Y) / MoveStep * MoveStep;

                    Slot SlotA = View.SlotA;
                    Slot SlotB = Node.GetInSlot(0);
                    SlotConnectionResponse Response = CanCreateConnection(SlotA, SlotB);
                    if (Response.Condition == ConnectCondition.Allow)
                    {
                        Connection NewTransition = CreateConnection(SlotA, SlotB);
                        View.ImportNodeWithConnection(Node, NewTransition);
                    }
                    else if (Response.Condition == ConnectCondition.NeedBreakOutSlot)
                    {
                        Connection NewTransition = CreateConnection(SlotA, SlotB);
                        View.ImportNodeWithConnection(Node, NewTransition, SlotA.GetConnections());
                    }
                    ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information, Response.Message);
                });
            }
            else
            {
                List<MenuBuilder> MenuBuilders = new List<MenuBuilder>();
                MenuBuilders.AddRange(View.BuildContextActionMenu());
                MenuBuilders.Add(new MenuBuilder { bIsSeperator = true });
                MenuBuilders.AddRange(View.GetModel().BuildNodeMenu(View.ImportNode));
                return MenuBuilders;
            }
        }

        public override List<MenuBuilder> BuildNodeMenu(Action<Node> ProcessNode)
        {
            List<MenuBuilder> MenuBuilders = new List<MenuBuilder>();

            MenuBuilders.Add(new MenuBuilder { Text = "State Node", Event = (Sender) => { ProcessNode(new StateNode()); } });
            MenuBuilders.Add(new MenuBuilder { Text = "Conduit Node", Event = (Sender) => { ProcessNode(new ConduitNode()); } });

            return MenuBuilders;
        }

        public override SlotConnectionResponse CanCreateConnection(Slot SlotA, Slot SlotB)
        {
            if (SlotA.Node == SlotB.Node)
            {
                return new SlotConnectionResponse("Both are on the same node", ConnectCondition.NotAllow);
            }

            if (SlotB.Node is StateEntryNode)
            {
                return new SlotConnectionResponse("Can not transition to Entry Node", ConnectCondition.NotAllow);
            }

            if (SlotA.Node is StateEntryNode && SlotA.GetConnections().Count != 0)
            {
                return new SlotConnectionResponse("Entry Node should only transition to one node", ConnectCondition.NeedBreakOutSlot);
            }

            return new SlotConnectionResponse("Create a transition", ConnectCondition.Allow);
        }

        public override void TryConnect(Slot SlotA, Slot SlotB)
        {
            SlotConnectionResponse Response = CanCreateConnection(SlotA, SlotB);

            if (Response.Condition == ConnectCondition.Allow)
            {
                Transition NewTransition = CreateConnection(SlotA, SlotB) as Transition;
                AddConnection(NewTransition);
                EditOperation_AddTransitionRule EditOperation = new EditOperation_AddTransitionRule(this, NewTransition, NewTransition.GetRules().Last());
                EditOperationManager.GetInstance().AddOperation(EditOperation);
                View?.SetModified();
            }
            else if (Response.Condition == ConnectCondition.NeedBreakOutSlot)
            {
                Connection NewConnection = CreateConnection(SlotA, SlotB);
                View?.ImportConnection(NewConnection, (SlotA.bOutput ? SlotA : SlotB).GetConnections());
            }
        }

        public override Connection CreateConnection(Slot SlotA, Slot SlotB)
        {
            int Index = Connections.FindIndex(Item =>
            {
                if (SlotA.bOutput)
                {
                    return Item.InSlot == SlotB && Item.OutSlot == SlotA;
                }
                else
                {
                    return Item.InSlot == SlotA && Item.OutSlot == SlotB;
                }
            });
            Transition Transition;

            if (Index == -1)
            {
                Transition = new Transition();

                Transition.ID = GenerateConnectionID();
                Transition.BindInSlot(SlotA.bOutput ? SlotB : SlotA);
                Transition.BindOutSlot(SlotA.bOutput ? SlotA : SlotB);
            }
            else
            {
                Transition = Connections[Index] as Transition;
            }

            TransitionRule TransitionRule = new TransitionRule();
            TransitionRule.SetOwner(this);
            Transition.AddRule(TransitionRule);

            return Transition;
        }

        #region Save

        public StateMachineData ToData()
        {
            StateMachineData data = new StateMachineData();
            data.Name = Name;

            if (Entry.OutSlot.GetConnections().Count > 0)
            {
                data.InitState = (Entry.OutSlot.GetConnections()[0] as Transition).InSlot.Node.ID.ToString();

                // Transitions
                data.Transitions = new List<TransitionData>();
                foreach (Transition Transition in Connections)
                {
                    if (Transition.OutSlot.Node is not StateEntryNode)
                    {
                        data.Transitions.Add(Transition.ToData());
                    }
                }
                // States
                data.StateNodes = new List<StateNodeData>();
                data.ConduitNodes = new List<ConduitNodeData>();
                foreach (StateNodeBase CurNode in Nodes)
                {
                    if (CurNode is StateNode stateNode)
                    {
                        data.StateNodes.Add(stateNode.ToData());
                    }
                    if (CurNode is ConduitNode conduitNode)
                    {
                        data.ConduitNodes.Add(conduitNode.ToData());
                    }
                }
            }
            return data;
        }

        public override void SaveConnectionToRecord(Connection ConnectionToSave, Record RecordConnection)
        {
            SaveTransitionToRecord(ConnectionToSave as Transition, RecordConnection);
        }

        public void SaveTransitionToRecord(Transition Transition, Record RecordTransition)
        {
            RecordTransition.SetTypeString("Transition");
            RecordTransition.SetInt("ID", Transition.ID);
            RecordTransition.SetInt("OutSlotNodeID", Transition.OutSlot.Node.ID);
            RecordTransition.SetInt("InSlotNodeID", Transition.InSlot.Node.ID);
            RecordTransition.SetFloat("BlendTime", Transition.BlendTime);
            RecordTransition.SetString("BlendMode", Transition.TransitionBlendMode.ToString());

            foreach (TransitionRule Rule in Transition.GetRules())
            {
                Record RecordRule = RecordTransition.AddChild();
                RecordRule.SetTypeString("TransitionRule");
                RecordRule.SetInt("Priority", Rule.Priority);
                Rule.SubGraph.SaveToRecord(RecordRule.AddChild());
            }
        }

        #endregion

        #region Load

        public override void LoadFromRecord(Record RecordNodeGraph)
        {
            Nodes.Clear();

            Name = RecordNodeGraph.GetString("Name");
            NodeID = RecordNodeGraph.GetInt("NodeID");
            ConnectionID = RecordNodeGraph.GetInt("ConnectionID");

            int Count = RecordNodeGraph.GetChildCount();
            for (int i = 0; i < Count; i++)
            {
                Record RecordChild = RecordNodeGraph.GetChild(i);
                string TypeString = RecordChild.GetTypeString();
                if (TypeString == "Transition")
                {
                    Record RecordConnection = RecordChild;
                    LoadConnectionFromRecord(RecordConnection);
                }
                else if (TypeString == "Camera")
                {
                    Record RecordCamera = RecordChild;
                    Camera.LoadFromXml(RecordCamera);
                }
                else
                {
                    Record RecordNode = RecordChild;
                    LoadNodeFromRecord(RecordNode);
                }
            }

            Entry = GetNodesOf<StateEntryNode>()[0];
        }

        public override void LoadConnectionFromRecord(Record RecordConnection)
        {
            LoadTransitoinFromRecord(RecordConnection);
        }

        public void LoadTransitoinFromRecord(Record RecordTransition)
        {
            Transition Transition = new Transition();

            Transition.ID = RecordTransition.GetInt("ID");

            int OutSlotNodeID = RecordTransition.GetInt("OutSlotNodeID");
            Slot OutSlot = FindNodeByID(OutSlotNodeID).GetOutSlot(0);
            Transition.BindOutSlot(OutSlot);

            int InSlotNodeID = RecordTransition.GetInt("InSlotNodeID");
            Slot InSlot = FindNodeByID(InSlotNodeID).GetInSlot(0);
            Transition.BindInSlot(InSlot);

            Transition.BlendTime = RecordTransition.GetFloat("BlendTime");
            Transition.TransitionBlendMode = Enum.Parse<BlendMode>(RecordTransition.GetString("BlendMode"));

            int Count = RecordTransition.GetChildCount();
            for (int i = 0; i < Count; ++i)
            {
                Record RecordRule = RecordTransition.GetChild(i);
                TransitionRule Rule = new TransitionRule();
                Rule.Priority = RecordRule.GetInt("Priority");
                Rule.SubGraph.LoadFromRecord(RecordRule.GetChild(0));
                Rule.SetOwner(this);
                Transition.AddRule(Rule);
            }

            AddConnection(Transition);
        }

        #endregion

        #region Load Old

        public void LoadFromRecord_Old(Record RecordNodeGraph)
        {
            Nodes.Clear();

            NodeID = RecordNodeGraph.GetInt("NodeID");
            ConnectionID = RecordNodeGraph.GetInt("ConnectionID");

            int Count = RecordNodeGraph.GetChildCount();

            for (int i = 0; i < Count; i++)
            {
                Record RecordChild = RecordNodeGraph.GetChild(i);
                string TypeString = RecordChild.GetTypeString();
                if (TypeString == "Transition")
                {
                    Record RecordConnection = RecordChild;
                    LoadTransitionFromRecord_Old(RecordConnection);
                }
                else
                {
                    Record RecordNode = RecordChild;
                    LoadNodeFromRecord_Old(RecordNode);
                }
            }

            Entry = GetNodesOf<StateEntryNode>()[0];
        }

        void LoadTransitionFromRecord_Old(Record RecordTransition)
        {
            Transition Transition = new Transition();

            Transition.ID = RecordTransition.GetInt("ID");

            int OutSlotNodeID = RecordTransition.GetInt("OutputNodeID");
            Slot OutSlot = FindNodeByID(OutSlotNodeID).GetOutSlot(0);
            Transition.BindOutSlot(OutSlot);

            int InSlotNodeID = RecordTransition.GetInt("InputNodeID");
            Slot InSlot = FindNodeByID(InSlotNodeID).GetInSlot(0);
            Transition.BindInSlot(InSlot);

            Transition.BlendTime = RecordTransition.GetFloat("BlendTime");
            Transition.TransitionBlendMode = Enum.Parse<BlendMode>(RecordTransition.GetString("BlendMode"));

            int Count = RecordTransition.GetChildCount();
            for (int i = 0; i < Count; ++i)
            {
                Record RecordRule = RecordTransition.GetChild(i);
                TransitionRule Rule = new TransitionRule();
                Rule.Priority = RecordRule.GetInt("Priority");
                (Rule.SubGraph as TransitionRuleGraph).LoadFromRecord_Old(RecordRule.GetChild(0));
                Rule.SetOwner(this);
                Transition.AddRule(Rule);
            }

            AddConnection(Transition);
        }

        void LoadNodeFromRecord_Old(Record RecordNode)
        {
            string TypeString = RecordNode.GetTypeString();
            Node Node = CreateNode(TypeString);
            if (Node == null)
            {
                DebugHelper.Assert(false);
            }
            else
            {
                Type NodeType = Node.GetType();
                PropertyInfo[] Properties = NodeType.GetProperties();
                foreach (PropertyInfo Info in Properties)
                {
                    PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(Info);
                    if (PropertyInfoAttribute.bHide == false)
                    {
                        object Value = RecordLoad(RecordNode, Info.PropertyType, Info.Name);
                        if (Value != null)
                        {
                            Info.SetValue(Node, Value);
                        }
                    }
                }
            }

            Node.Name = RecordNode.GetString("Name");
            Node.ID = RecordNode.GetInt("ID");
            Node.X = RecordNode.GetInt("X");
            Node.Y = RecordNode.GetInt("Y");
            Node.SetOwner(this);

            if (RecordNode.GetChildCount() > 0 && Node.bHasSubGraph)
            {
                Record RecordSubGraph = RecordNode.FindByTypeString("NodeGraph");
                if (RecordSubGraph != null)
                {
                    (Node.SubGraph as StateGraph).LoadFromRecord_Old(RecordSubGraph);
                }
            }

            AddNode(Node);
        }

        #endregion

        public void UpdateDebugStatus(vector_cross_anim_StbVisitRecord stbVisitRecords)
        {
            foreach (var item in stbVisitRecords)
            {
                string linkName = item.LinkName.GetCString();
                string toNodeName = item.ToNodeName.GetCString();
                string fromNodeName = item.FromNodeName.GetCString();
                int toNodeID = -1, linkID = -1, fromNodeID = -1;

                if (Int32.TryParse(toNodeName, out toNodeID))
                {
                    var node = FindNodeByID(toNodeID);
                    if (node != null)
                    {
                        node.bDebugged = true;
                        if (node is StateNode && item.IsStateNode)
                        {
                            (node as StateNode).StateGraph.UpdateDebugStatus(item.SubDataItems);
                        }
                    }
                }
                if (Int32.TryParse(fromNodeName, out fromNodeID))
                {
                    var node = FindNodeByID(fromNodeID);
                    if (node != null)
                    {
                        node.bDebugged = true;
                    }
                }
                if (Int32.TryParse(linkName, out linkID))
                {
                    var link = FindConnectionByID(linkID);
                    if (link != null)
                    {
                        link.bDebugged = true;
                    }
                }
            }
        }

        public virtual void Start()
        {
            if (Entry.OutSlot.GetConnections().Count > 0)
            {
                CurrentState = (Entry.OutSlot.GetConnections()[0] as Transition).OutSlot.Node as StateNodeBase;

                if (CurrentState is ConduitNode)
                {
                    Entry.CommitError("Can not start with conduit node.");
                }
                else
                {
                    bRunning = true;
                }
            }
            else
            {
                Entry.CommitError("Entry is not connect to a state.");
            }
        }

        public virtual void Stop()
        {
            CurrentState = null;
            bRunning = false;
        }

        public virtual void Tick()
        {
            if (bRunning)
            {
                StateNodeBase NextNode;
                while (true)
                {
                    NextNode = GetNextNode(CurrentState);
                    if (NextNode == null)
                    {
                        // can not find next state
                        break;
                    }
                    else if (NextNode is StateNode)
                    {
                        // change state and break
                        CurrentState = NextNode;
                        break;
                    }
                    else if (NextNode is ConduitNode)
                    {
                        // if next node is conduit,
                        // contine find next node until null or state
                        continue;
                    }
                }
            }
        }

        public StateNodeBase GetNextNode(StateNodeBase CurrentNode)
        {
            if (CurrentNode is ConduitNode)
            {
                object Result = ((CurrentNode as ConduitNode).SubGraph as TransitionRuleGraph).ResultNode.Eval(0);
                if (!(Result is bool && (bool)Result))
                {
                    return null;
                }
            }
            // get all rules on output pin
            List<TransitionRule> Rules = new List<TransitionRule>();
            foreach (Transition Transition in CurrentNode.OutSlot.GetConnections())
            {
                foreach (TransitionRule Rule in Transition.GetRules())
                {
                    Rules.Add(Rule);
                }
            }
            // sort by priority and ID
            Rules.Sort((left, right) =>
            {
                if (left.Priority != right.Priority)
                {
                    return left.Priority.CompareTo(right.Priority);
                }
                else
                {
                    return left.ID.CompareTo(right.ID);
                }
            });
            // change current state if transition rule's evaluation is true
            foreach (TransitionRule Rule in Rules)
            {
                object Result = Rule.TransitionRuleGraph.ResultNode.Eval(0);
                if (Result is bool && (bool)Result)
                {
                    return Rule.Transition.InSlot.Node as StateNodeBase;
                }
            }
            return null;
        }

        public void GetResourceReferenceGUID(ref HashSet<string> outRef)
        {
            foreach (var node in Nodes)
            {
                if (node is StateNode)
                {
                    (node as StateNode).GetResourceReferenceGUID(ref outRef);
                }
            }
        }

        #region Load from json
        public void AddStateNodeFromData(StateNodeData NodeData)
        {
            StateNode Node = new StateNode();
            Node.SetOwner(this);
            Node.FromData(NodeData);
            AddNode(Node);
            NodeID = Math.Max(Node.ID + 1, NodeID);
        }

        public void AddConduitNodeFromData(ConduitNodeData NodeData)
        {
            ConduitNode Node = new ConduitNode();
            Node.SetOwner(this);
            Node.FromData(NodeData);
            AddNode(Node);
            NodeID = Math.Max(Node.ID + 1, NodeID);
        }

        public void AddTransitionFromData(TransitionData TransitionData)
        {
            try
            {
                Transition Transition = new Transition();
                Transition.FromData(TransitionData, this);
                AddConnection(Transition);
                ConnectionID = Math.Max(Transition.ID + 1, ConnectionID);
            }
            catch (Exception)
            {
            }
        }

        public void FromData(StateMachineData StateMachineData)
        {
            Name = StateMachineData.Name;
            if (!GetTopGrap().IsLoadedFromXml)
            {
                Nodes.Clear();
                Nodes.Add(Entry);

                if (StateMachineData.ConduitNodes != null)
                {
                    foreach (var ConduitNodeData in StateMachineData.ConduitNodes)
                    {
                        AddConduitNodeFromData(ConduitNodeData);
                    }
                }

                if (StateMachineData.StateNodes != null)
                {
                    foreach (var StateNode in StateMachineData.StateNodes)
                    {
                        AddStateNodeFromData(StateNode);
                    }

                    foreach (var Transition in StateMachineData.Transitions)
                    {
                        AddTransitionFromData(Transition);
                    }

                    int.TryParse(StateMachineData.InitState, out int InitStateId);
                    if (StateMachineData.StateNodes.Count > 0 && InitStateId != 0)
                    {
                        var InitStateNode = FindNodeByID(InitStateId);
                        TryConnect(Entry.GetOutSlot(0), InitStateNode.GetInSlot(0));
                        //TryConnect(InitStateNode.GetOutSlot(0), Entry.GetInSlot(0));
                    }
                }
            }
        }

        #endregion
    }
}
