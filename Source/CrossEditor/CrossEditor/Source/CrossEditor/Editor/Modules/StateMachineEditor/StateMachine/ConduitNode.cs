using CEngine;
using Newtonsoft.Json;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class ConduitNodeData
    {
        [JsonProperty()]
        public int PosX = 0;
        [JsonProperty()]
        public int PosY = 0;
        [JsonProperty()]
        public string Name = "";
        [JsonProperty()]
        public string DisplayName = "";
        [JsonProperty()]
        public TransitionRuleGraphData RuleGraph;
    }

    public class ConduitNode : StateNodeBase
    {
        public ConduitNode()
        {
            Name = "Conduit";
            SetSubGraph(new TransitionRuleGraph());

            RenameEvent = () =>
            {
                SubGraph.Name = Name + "(Rule)";
            };
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Conduit Default Result")]
        public bool DefaultResult
        {
            get
            {
                return (SubGraph as TransitionRuleGraph).ResultNode.DefaultResult;
            }
            set
            {
                (SubGraph as TransitionRuleGraph).ResultNode.DefaultResult = value;
            }
        }

        public ConduitNodeData ToData()
        {
            ConduitNodeData Data = new ConduitNodeData();
            Data.PosX = X;
            Data.PosY = Y;
            Data.Name = ID.ToString();
            Data.DisplayName = Name;
            Data.RuleGraph = (SubGraph as TransitionRuleGraph).ToData();
            return Data;
        }

        public void FromData(ConduitNodeData Data)
        {
            var TransitionRuleGraph = (SubGraph as TransitionRuleGraph);
            ID = int.Parse(Data.Name);
            X = Data.PosX;
            Y = Data.PosY;
            TransitionRuleGraph.FromData(Data.RuleGraph);
        }
    }
}
