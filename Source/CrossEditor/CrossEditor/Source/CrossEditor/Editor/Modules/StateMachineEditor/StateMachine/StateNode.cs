using CEngine;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class StateNodeData
    {
        [JsonProperty()]
        public string DisplayName = "";
        [JsonProperty()]
        public int PosX = 0;
        [JsonProperty()]
        public int PosY = 0;
        [JsonProperty()]
        public string Name = "BaseNode";
        [JsonProperty()]
        public NodeGraphData StateGraph;
    }

    public class StateNode : StateNodeBase
    {
        public StateNode()
        {
            Name = "State";
            SetSubGraph(new StateGraph());

            RenameEvent = () =>
            {
                SubGraph.Name = Name + "(state)";
            };
        }

        [PropertyInfo(bHide = true)]
        public StateGraph StateGraph
        {
            get => SubGraph as StateGraph;
        }

        public StateNodeData ToData()
        {
            StateNodeData Data = new StateNodeData();
            Data.PosX = X;
            Data.PosY = Y;
            Data.DisplayName = Name;
            Data.Name = ID.ToString();
            Data.StateGraph = AnimGraph.ModelToData(StateGraph);
            return Data;
        }

        public void FromData(StateNodeData StateNodeData)
        {
            ID = int.Parse(StateNodeData.Name);
            Name = StateNodeData.DisplayName;
            X = StateNodeData.PosX;
            Y = StateNodeData.PosY;

            SubGraph = new StateGraph();
            SubGraph.Owner = this;
            StateGraph.FromData(StateNodeData.StateGraph);
        }

        public void GetResourceReferenceGUID(ref HashSet<string> outRef)
        {
            StateGraph.GetResourceReferenceGUID(ref outRef);
        }
    }
}
