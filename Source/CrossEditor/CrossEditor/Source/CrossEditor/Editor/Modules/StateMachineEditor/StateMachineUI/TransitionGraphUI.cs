namespace CrossEditor
{
    class TransitionGraphUI
    {
        /*        static NodeGraphBaseUI _Instance = new NodeGraphBaseUI(NodeGraphType.TransitionRuleGraph);

                public static NodeGraphBaseUI GetInstance()
                {
                    return _Instance;
                }

                public static void CustomMenuItem(ref Menu MenuContextMenu,
                    MenuItemClickedEventHandler OnMenuItemCompileAndRunClicked,
                    MenuItemClickedEventHandler OnMenuItemSaveClicked,
                    MenuItemClickedEventHandler OnMenuItemDuplicateClicked,
                    MenuItemClickedEventHandler OnMenuItemDeleteClicked,
                    NodeProcessFunc AddNode
                    )
                {
                    MenuItem MenuItem_Save = new MenuItem();
                    MenuItem_Save.SetText("Save");
                    MenuItem_Save.ClickedEvent += StoryBoardUI.OnSaveStoryBoard;

                    MenuItem MenuItem_Duplicate = new MenuItem();
                    MenuItem_Duplicate.SetText("Duplicate");
                    MenuItem_Duplicate.ClickedEvent += OnMenuItemDuplicateClicked;

                    MenuItem MenuItem_Delete = new MenuItem();
                    MenuItem_Delete.SetText("Delete");
                    MenuItem_Delete.ClickedEvent += OnMenuItemDeleteClicked;

                    Menu Menu_Node_LogicOp = new Menu(GetUIManager());
                    Menu_Node_LogicOp.Initialize();
                    MenuItem MenuItem_Node_LogicOp = new MenuItem();
                    MenuItem_Node_LogicOp.SetText("LogicOp");
                    MenuItem_Node_LogicOp.SetMenu(Menu_Node_LogicOp);

                    MenuItem MenuItem_Node_LogicOp_And = new MenuItem();
                    MenuItem_Node_LogicOp_And.SetText("And");
                    MenuItem_Node_LogicOp_And.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_BinaryLogicOp(BinaryLogicOp.And)); };
                    Menu_Node_LogicOp.AddMenuItem(MenuItem_Node_LogicOp_And);

                    MenuItem MenuItem_Node_LogicOp_Or = new MenuItem();
                    MenuItem_Node_LogicOp_Or.SetText("Or");
                    MenuItem_Node_LogicOp_Or.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_BinaryLogicOp(BinaryLogicOp.Or)); };
                    Menu_Node_LogicOp.AddMenuItem(MenuItem_Node_LogicOp_Or);

                    MenuItem MenuItem_Node_LogicOp_Xor = new MenuItem();
                    MenuItem_Node_LogicOp_Xor.SetText("Xor");
                    MenuItem_Node_LogicOp_Xor.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_BinaryLogicOp(BinaryLogicOp.Xor)); };
                    Menu_Node_LogicOp.AddMenuItem(MenuItem_Node_LogicOp_Xor);

                    Menu_Node_LogicOp.AddSeperator();

                    MenuItem MenuItem_Node_LogicOp_Not = new MenuItem();
                    MenuItem_Node_LogicOp_Not.SetText("Not");
                    MenuItem_Node_LogicOp_Not.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_UnaryLogicOp(UnaryLogicOp.Not)); };
                    Menu_Node_LogicOp.AddMenuItem(MenuItem_Node_LogicOp_Not);

                    Menu Menu_Node_Compare = new Menu(GetUIManager());
                    Menu_Node_Compare.Initialize();
                    MenuItem MenuItem_Node_Compare = new MenuItem();
                    MenuItem_Node_Compare.SetText("Compare");
                    MenuItem_Node_Compare.SetMenu(Menu_Node_Compare);

                    MenuItem MenuItem_Node_Compare_Equal = new MenuItem();
                    MenuItem_Node_Compare_Equal.SetText("==");
                    MenuItem_Node_Compare_Equal.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Compare(Relation.EqualTo)); };
                    Menu_Node_Compare.AddMenuItem(MenuItem_Node_Compare_Equal);

                    MenuItem MenuItem_Node_Compare_Inequal = new MenuItem();
                    MenuItem_Node_Compare_Inequal.SetText("!=");
                    MenuItem_Node_Compare_Inequal.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Compare(Relation.InequalTo)); };
                    Menu_Node_Compare.AddMenuItem(MenuItem_Node_Compare_Inequal);

                    MenuItem MenuItem_Node_Compare_LowerTo = new MenuItem();
                    MenuItem_Node_Compare_LowerTo.SetText("<");
                    MenuItem_Node_Compare_LowerTo.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Compare(Relation.LowerTo)); };
                    Menu_Node_Compare.AddMenuItem(MenuItem_Node_Compare_LowerTo);

                    MenuItem MenuItem_Node_Compare_LowerEqual = new MenuItem();
                    MenuItem_Node_Compare_LowerEqual.SetText("<=");
                    MenuItem_Node_Compare_LowerEqual.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Compare(Relation.LowerEqualTo)); };
                    Menu_Node_Compare.AddMenuItem(MenuItem_Node_Compare_LowerEqual);

                    MenuItem MenuItem_Node_Compare_GreaterTo = new MenuItem();
                    MenuItem_Node_Compare_GreaterTo.SetText(">");
                    MenuItem_Node_Compare_GreaterTo.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Compare(Relation.GreaterTo)); };
                    Menu_Node_Compare.AddMenuItem(MenuItem_Node_Compare_GreaterTo);

                    MenuItem MenuItem_Node_Compare_GreaterEqual = new MenuItem();
                    MenuItem_Node_Compare_GreaterEqual.SetText(">=");
                    MenuItem_Node_Compare_GreaterEqual.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Compare(Relation.GreaterEqualTo)); };
                    Menu_Node_Compare.AddMenuItem(MenuItem_Node_Compare_GreaterEqual);

                    Menu Menu_Node_ArithOp = new Menu(GetUIManager());
                    Menu_Node_ArithOp.Initialize();
                    MenuItem MenuItem_Node_ArithOp = new MenuItem();
                    MenuItem_Node_ArithOp.SetText("ArithOp");
                    MenuItem_Node_ArithOp.SetMenu(Menu_Node_ArithOp);

                    MenuItem MenuItem_Node_ArithOp_Add = new MenuItem();
                    MenuItem_Node_ArithOp_Add.SetText("+");
                    MenuItem_Node_ArithOp_Add.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_BinaryArithOp(BinaryArithOp.Add)); };
                    Menu_Node_ArithOp.AddMenuItem(MenuItem_Node_ArithOp_Add);

                    MenuItem MenuItem_Node_ArithOp_Substract = new MenuItem();
                    MenuItem_Node_ArithOp_Substract.SetText("- ");
                    MenuItem_Node_ArithOp_Substract.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_BinaryArithOp(BinaryArithOp.Substract)); };
                    Menu_Node_ArithOp.AddMenuItem(MenuItem_Node_ArithOp_Substract);

                    MenuItem MenuItem_Node_ArithOp_Multiply = new MenuItem();
                    MenuItem_Node_ArithOp_Multiply.SetText("*");
                    MenuItem_Node_ArithOp_Multiply.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_BinaryArithOp(BinaryArithOp.Multiply)); };
                    Menu_Node_ArithOp.AddMenuItem(MenuItem_Node_ArithOp_Multiply);

                    MenuItem MenuItem_Node_ArithOp_Divide = new MenuItem();
                    MenuItem_Node_ArithOp_Divide.SetText("/");
                    MenuItem_Node_ArithOp_Divide.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_BinaryArithOp(BinaryArithOp.Divide)); };
                    Menu_Node_ArithOp.AddMenuItem(MenuItem_Node_ArithOp_Divide);

                    MenuItem MenuItem_Node_ArithOp_Modulo = new MenuItem();
                    MenuItem_Node_ArithOp_Modulo.SetText("%");
                    MenuItem_Node_ArithOp_Modulo.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_BinaryArithOp(BinaryArithOp.Modulo)); };
                    Menu_Node_ArithOp.AddMenuItem(MenuItem_Node_ArithOp_Modulo);

                    Menu_Node_ArithOp.AddSeperator();

                    MenuItem MenuItem_Node_ArithOp_Neg = new MenuItem();
                    MenuItem_Node_ArithOp_Neg.SetText("- ");
                    MenuItem_Node_ArithOp_Neg.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_UnaryArithOp(UnaryArithOp.Negative)); };
                    Menu_Node_ArithOp.AddMenuItem(MenuItem_Node_ArithOp_Neg);

                    Menu Menu_Node_Constant = new Menu(GetUIManager());
                    Menu_Node_Constant.Initialize();
                    MenuItem MenuItem_Node_Constant = new MenuItem();
                    MenuItem_Node_Constant.SetText("Constant");
                    MenuItem_Node_Constant.SetMenu(Menu_Node_Constant);

                    MenuItem MenuItem_Node_Constant_True = new MenuItem();
                    MenuItem_Node_Constant_True.SetText("True");
                    MenuItem_Node_Constant_True.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Bool(true)); };
                    Menu_Node_Constant.AddMenuItem(MenuItem_Node_Constant_True);

                    MenuItem MenuItem_Node_Constant_False = new MenuItem();
                    MenuItem_Node_Constant_False.SetText("False");
                    MenuItem_Node_Constant_False.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Bool(false)); };
                    Menu_Node_Constant.AddMenuItem(MenuItem_Node_Constant_False);

                    MenuItem MenuItem_Node_Constant_Integer = new MenuItem();
                    MenuItem_Node_Constant_Integer.SetText("Integer");
                    MenuItem_Node_Constant_Integer.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Integer(0)); };
                    Menu_Node_Constant.AddMenuItem(MenuItem_Node_Constant_Integer);

                    MenuItem MenuItem_Node_Constant_Float = new MenuItem();
                    MenuItem_Node_Constant_Float.SetText("Float");
                    MenuItem_Node_Constant_Float.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_Float(0.0f)); };
                    Menu_Node_Constant.AddMenuItem(MenuItem_Node_Constant_Float);

                    Menu Menu_Node_Convert = new Menu(GetUIManager());
                    Menu_Node_Convert.Initialize();
                    MenuItem MenuItem_Node_Convert = new MenuItem();
                    MenuItem_Node_Convert.SetText("Convert");
                    MenuItem_Node_Convert.SetMenu(Menu_Node_Convert);

                    MenuItem MenuItem_Node_Convert_ToInt = new MenuItem();
                    MenuItem_Node_Convert_ToInt.SetText("ToInt");
                    MenuItem_Node_Convert_ToInt.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_ToInt()); };
                    Menu_Node_Convert.AddMenuItem(MenuItem_Node_Convert_ToInt);

                    MenuItem MenuItem_Node_Convert_ToFloat = new MenuItem();
                    MenuItem_Node_Convert_ToFloat.SetText("ToFloat");
                    MenuItem_Node_Convert_ToFloat.ClickedEvent += (MenuItem Sender) => { AddNode(new FlowNode_ToFloat()); };
                    Menu_Node_Convert.AddMenuItem(MenuItem_Node_Convert_ToFloat);

                    MenuContextMenu.AddMenuItem(MenuItem_Save);
                    MenuContextMenu.AddMenuItem(MenuItem_Duplicate);
                    MenuContextMenu.AddMenuItem(MenuItem_Delete);
                    MenuContextMenu.AddSeperator();
                    MenuContextMenu.AddMenuItem(MenuItem_Node_LogicOp);
                    MenuContextMenu.AddMenuItem(MenuItem_Node_Compare);
                    MenuContextMenu.AddMenuItem(MenuItem_Node_ArithOp);
                    MenuContextMenu.AddMenuItem(MenuItem_Node_Constant);
                    MenuContextMenu.AddMenuItem(MenuItem_Node_Convert);
                    MenuContextMenu.AddSeperator();

                    Menu Menu_Node_Param = new Menu(GetUIManager());
                    Menu_Node_Param.Initialize();
                    MenuItem MenuItem_Node_Param = new MenuItem();
                    MenuItem_Node_Param.SetText("Param Nodes");
                    MenuItem_Node_Param.SetMenu(Menu_Node_Param);

                    var StbProperty = StoryBoardUI.GetInstance().GetProperty() as StoryBoardProperty;
                    foreach (var param in StbProperty.Params)
                    {
                        Menu_Node_Param.AddMenuItem(CreateParamNodeMenu(param, AddNode));
                    }
                    MenuContextMenu.AddMenuItem(MenuItem_Node_Param);

                    CreateTimeRemainFuncNodeMenu(ref MenuContextMenu, AddNode);
                }

                static public void CreateTimeRemainFuncNodeMenu(ref Menu parantMenu, NodeProcessFunc AddNode)
                {
                    Menu Menu_Node_Func = new Menu(GetUIManager());
                    Menu_Node_Func.Initialize();
                    MenuItem MenuItem_Node_Func = new MenuItem();
                    MenuItem_Node_Func.SetText("AnimTimeRemaining for ");
                    MenuItem_Node_Func.SetMenu(Menu_Node_Func);

                    var transitionGraph = GetInstance().GetNodeGraph() as TransitionRuleGraph;
                    DebugHelper.Assert(transitionGraph != null);
                    var transitionRule = transitionGraph.GetOwner();
                    DebugHelper.Assert(transitionRule != null);
                    var transition = transitionRule.GetOwner();
                    DebugHelper.Assert(transition != null);
                    var stateGraph = transition.GetFromStateGraph();
                    DebugHelper.Assert(stateGraph != null);
                    var animNodes = stateGraph.GetNodesOf<Anim_PlayAnimBaseNode>();
                    foreach (var node in animNodes)
                    {
                        var animNode = node as Anim_PlayAnimBaseNode;
                        DebugHelper.Assert(animNode != null);
                        foreach (var usedAnim in animNode.GetUsedAnims())
                        {
                            Menu_Node_Func.AddMenuItem(CreateTimeRemainNode(usedAnim, AddNode));
                        }
                    }
                    parantMenu.AddMenuItem(MenuItem_Node_Func);
                }

                static public MenuItem CreateParamNodeMenu(AnimParameterData param, NodeProcessFunc AddNode)
                {
                    string name = param.Name;
                    string type = param.Type;
                    MenuItem MenuItemRet = new MenuItem();

                    MenuItemRet.SetText(string.Format("Create {0}<{1}>", name, type));
                    MenuItemRet.ClickedEvent += (MenuItem sender) =>
                    {
                        var node = new FlowNode_AnimParam(name);
                        AddNode(node);
                    };
                    return MenuItemRet;
                }

                static public MenuItem CreateTimeRemainNode(string name, NodeProcessFunc AddNode)
                {
                    MenuItem MenuItemRet = new MenuItem();

                    MenuItemRet.SetText(AnimUtil.TrimAnimPath(PathHelper.GetFileName(name)));
                    MenuItemRet.ClickedEvent += (MenuItem sender) =>
                    {
                        var node = new FlowNode_AnimTimeRemaining(name);
                        AddNode(node);
                    };
                    return MenuItemRet;
                }*/
    }
}
