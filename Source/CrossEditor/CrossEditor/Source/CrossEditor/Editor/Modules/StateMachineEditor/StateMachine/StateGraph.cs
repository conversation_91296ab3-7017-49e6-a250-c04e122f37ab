using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class StateGraph : NodeGraphModel
    {
        public Anim_RootNode OutputNode;

        public StateGraph() : base()
        {
            Type = NodeGraphType.StateGraph;
            CreateOutputNode();
        }

        public void CreateOutputNode()
        {
            OutputNode = new Anim_RootNode();
            OutputNode.X = 200;
            OutputNode.Y = 200;

            ImportNode(OutputNode);
        }

        #region Save

        public override void SaveNodeToRecord(Node NodeToSave, Record RecordNode)
        {
            base.SaveNodeToRecord(NodeToSave, RecordNode);

            if (NodeToSave is Anim_FlowNode)
            {
                string JsonStr = JsonConvert.SerializeObject((NodeToSave as Anim_FlowNode).ToData(), Formatting.Indented);
                RecordNode.SetString("NodeJsonData", JsonStr);
            }
        }

        #endregion

        #region Load

        public override void LoadFromRecord(Record RecordNodeGraph)
        {
            base.LoadFromRecord(RecordNodeGraph);

            OutputNode = GetNodesOf<Anim_RootNode>()[0];
        }

        public override Node LoadNodeFromRecord(Record RecordNode)
        {
            Node Node = base.LoadNodeFromRecord(RecordNode);
            if (Node is Anim_FlowNode)
            {
                (Node as Anim_FlowNode).FromJsonData(RecordNode.GetString("NodeJsonData"));
            }
            return Node;
        }


        public void AddNodeFromData(NodeData NodeData)
        {
            string typeName = "Anim_" + NodeData.Type;
            var Node = CreateNode(typeName) as Anim_FlowNode;
            Node.SetOwner(this);
            Node.FromData(NodeData);
            AddNode(Node);
            NodeID = Math.Max(NodeID, Node.ID + 1);
        }

        public void FromData(NodeGraphData NodeGraphData)
        {
            Name = NodeGraphData.Name;

            Nodes.Clear();
            foreach (NodeData nodeData in NodeGraphData.Nodes)
            {
                AddNodeFromData(nodeData);
            }
            OutputNode = GetNodesOf<Anim_RootNode>()[0];

            foreach (LinkData linkData in NodeGraphData.Links)
            {
                AddConnectionFromData(linkData);
            }
        }
        #endregion

        #region Load Old

        public void LoadFromRecord_Old(Record RecordNodeGraph)
        {
            Nodes.Clear();

            NodeID = RecordNodeGraph.GetInt("NodeID");
            ConnectionID = RecordNodeGraph.GetInt("ConnectionID");

            int Count = RecordNodeGraph.GetChildCount();
            for (int i = 0; i < Count; i++)
            {
                Record RecordChild = RecordNodeGraph.GetChild(i);
                string TypeString = RecordChild.GetTypeString();
                if (TypeString == "Connection")
                {
                    LoadConnectionFromRecord_Old(RecordChild);
                }
                else
                {
                    LoadNodeFromRecord_Old(RecordChild);
                }
            }

            OutputNode = GetNodesOf<Anim_RootNode>()[0];
        }

        public void LoadConnectionFromRecord_Old(Record RecordConnection)
        {
            Connection Connection = new Connection();

            Connection.ID = RecordConnection.GetInt("ID");

            int OutSlotNodeID = RecordConnection.GetInt("OutSlotNodeID");
            string OutSlotName = RecordConnection.GetString("OutSlotName");
            Slot OutSlot = FindNodeByID(OutSlotNodeID).FindOutSlot(OutSlotName);
            Connection.BindOutSlot(OutSlot);

            int InSlotNodeID = RecordConnection.GetInt("InSlotNodeID");
            string InSlotName = RecordConnection.GetString("InSlotName");
            Slot InSlot = FindNodeByID(InSlotNodeID).FindInSlot(InSlotName);
            Connection.BindInSlot(InSlot);

            AddConnection(Connection);
        }

        public void LoadNodeFromRecord_Old(Record RecordNode)
        {
            string TypeString = RecordNode.GetTypeString();
            Node Node = CreateNode(TypeString);
            if (Node == null)
            {
                DebugHelper.Assert(false);
            }
            else
            {
                Type NodeType = Node.GetType();
                PropertyInfo[] Properties = NodeType.GetProperties();
                foreach (PropertyInfo Info in Properties)
                {
                    PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(Info);
                    if (PropertyInfoAttribute.bHide == false && Info.Name != "ParamType" && Info.Name != "NodeName")
                    {
                        object Value = RecordLoad(RecordNode, Info.PropertyType, Info.Name);
                        if (Value != null)
                        {
                            Info.SetValue(Node, Value);
                        }
                    }
                }
            }

            Node.ID = RecordNode.GetInt("ID");
            Node.X = RecordNode.GetInt("X");
            Node.Y = RecordNode.GetInt("Y");
            Node.SetOwner(this);

            AddNode(Node);

            if (Node is Anim_FlowNode)
            {
                (Node as Anim_FlowNode).FromJsonData(RecordNode.GetString("NodeJsonData"));
            }

        }

        #endregion

        public override List<MenuBuilder> BuildNodeMenu(Action<Node> ProcessNode)
        {
            List<MenuBuilder> MenuBuilders = new List<MenuBuilder>();

            MenuBuilders.Add(CreateMenuBuilder<Anim_AccumulatePoseNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SwitchPosesByIntNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SwitchPosesByBoolNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SpaceRootToLocalNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_SpaceLocalToRootNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_MirrorNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_BlendByLayeredFilterNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_BlendPosesNode>(ProcessNode));

            {
                NodeGraphModel Graph = this;
                while (Graph.GetOwner() != null)
                {
                    Graph = Graph.GetOwner().GetOwner();
                }

                if (Graph != null && Graph is AnimGraph)
                {
                    AnimGraph AnimGraph = Graph as AnimGraph;
                    MenuBuilders.Add(new MenuBuilder
                    {
                        Text = "Param Nodes",
                        bHasChild = true,
                        Children = AnimGraph.BuildParamMenu(ProcessNode)
                    });
                }
            }

            MenuBuilders.Add(new MenuBuilder
            {
                Text = "Motion Match",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    CreateMenuBuilder<Anim_AdvanceMMNode>(ProcessNode),
                    CreateMenuBuilder<Anim_PoseMatchingNode>(ProcessNode),
                    CreateMenuBuilder<Anim_MotionRecordNode>(ProcessNode)
                }
            });
            MenuBuilders.Add(new MenuBuilder
            {
                Text = "Play Animation",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    CreateMenuBuilder<Anim_EvaluateSequenceNode>(ProcessNode),
                    CreateMenuBuilder<Anim_PlaySequenceNode>(ProcessNode),
                    CreateMenuBuilder<Anim_PlayCompositeNode>(ProcessNode),
                    CreateMenuBuilder<Anim_PlayBlendSpaceNode>(ProcessNode)
                }
            });

            MenuBuilders.Add(new MenuBuilder { Text = "Create StateMachine", Event = (Sender) => { ProcessNode(new Anim_FSMNode()); } });
            MenuBuilders.Add(CreateMenuBuilder<Anim_SlotNode>(ProcessNode));
            MenuBuilders.Add(CreateMenuBuilder<Anim_GetPoseNode>(ProcessNode));

            return MenuBuilders;
        }

        public MenuBuilder CreateMenuBuilder<T>(Action<Node> ProcessNode) where T : Anim_FlowNode, new()
        {
            MenuBuilder MenuBuilder = new MenuBuilder();

            // Needs node's types follow this pattern
            string type_name = typeof(T).FullName;
            string[] names = type_name.Split("_");
            string raw_name = names[names.Length - 1];

            MenuBuilder.Text = "Create " + raw_name;
            MenuBuilder.Event = (Sender) => { ProcessNode(new T()); };

            return MenuBuilder;
        }

        public void UpdateDebugStatus(vector_cross_anim_StbVisitRecord stbVisitRecords)
        {
            foreach (var item in stbVisitRecords)
            {
                string linkName = item.LinkName.GetCString();
                string toNodeName = item.ToNodeName.GetCString();
                string fromNodeName = item.FromNodeName.GetCString();
                int toNodeID = -1, linkID = -1, fromNodeID = -1;

                if (Int32.TryParse(toNodeName, out toNodeID))
                {
                    var node = FindNodeByID(toNodeID);
                    if (node != null)
                    {
                        node.SetDebugStatus();
                    }
                }
                if (Int32.TryParse(fromNodeName, out fromNodeID))
                {
                    var node = FindNodeByID(fromNodeID);
                    if (node != null)
                    {
                        node.bDebugged = true;
                        if (node is Anim_FSMNode)
                        {
                            (node as Anim_FSMNode).StateMachineGraph.UpdateDebugStatus(item.SubDataItems);
                        }
                    }
                }
                if (Int32.TryParse(linkName, out linkID))
                {
                    var link = FindConnectionByID(linkID);
                    if (link != null)
                    {
                        link.SetDebugStatus(item.Weight);
                    }
                }
            }
        }
        public void GetResourceReferenceGUID(ref HashSet<string> outRef)
        {
            foreach (var node in Nodes)
            {
                if (node is Anim_FlowNode)
                {
                    (node as Anim_FlowNode).GetResourceReferenceGUID(ref outRef);
                }
            }
        }
    }
}
