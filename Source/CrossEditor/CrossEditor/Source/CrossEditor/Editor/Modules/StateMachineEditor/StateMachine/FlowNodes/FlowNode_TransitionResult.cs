using CEngine;

namespace CrossEditor
{
    public class FlowNode_TransitionResult : FlowNode
    {
        bool _bDefalutResult;

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Default Transition Result")]
        public bool DefaultResult
        {
            get
            {
                return _bDefalutResult;
            }
            set
            {
                _bDefalutResult = value;
            }
        }

        public FlowNode_TransitionResult()
        {
            Name = "Result";
            NodeType = NodeType.Statement;
            TemplateExpression = "{0}";

            bOperable = false;
            _bDefalutResult = false;

            AddInSlot("Can Enter Transition", SlotType.DataFlow);
        }

        public override object Eval(int OutSlotIndex)
        {
            int OutSlotIndex1;
            FlowNode InNode1 = GetInputNode(0, out OutSlotIndex1) as FlowNode;

            if (InNode1 == null)
            {
                return _bDefalutResult;
            }

            object Value1 = InNode1.Eval(OutSlotIndex1);
            if (Value1 is bool)
            {
                return Value1;
            }
            else
            {
                CommitError("In slots type missmatch");
                return null;
            }
        }

        public override string ToExpression()
        {
            string Expr = "false";

            int OutSlotIndex1;
            FlowNode InNode1 = GetInputNode(0, out OutSlotIndex1) as FlowNode;

            if (InNode1 != null)
            {
                Expr = InNode1.ToExpression();
            }
            return string.Format(TemplateExpression, Expr);
        }
    }
}
