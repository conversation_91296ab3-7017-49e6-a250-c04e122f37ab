using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace CrossEditor
{
    class TextureCacheItem
    {
        public long ModifyTime;
        public int UsedCount = 0;
        public Texture Texture;
        public long LatestUsedFrame = 0;
        public string TextureName;
    }

    class ThumbnailHelper
    {
        static ThumbnailHelper _Instance = new ThumbnailHelper();

        Texture _TexturePanelFile1;
        Texture _TexturePanelFileFailed;
        const int _MaxTextureCacheCount = 1024; // 1024 * 256 /1024 = 256MB Thumbnail cache
        Dictionary<string, TextureCacheItem> _TextureCache;
        Dictionary<Texture, TextureCacheItem> _mFastTextureQuery;
        SortedDictionary<long, HashSet<string>> _TextureCacheLFU; // key: used count, value: HashSet of texture path
        private long mCurrentFrame = 0;


        public static ThumbnailHelper GetInstance()
        {
            return _Instance;
        }

        ThumbnailHelper()
        {
            _TexturePanelFile1 = UIManager.LoadUIImage("Editor/Others/PanelFile1.png");
            _TexturePanelFileFailed = UIManager.LoadUIImage("Editor/Others/PanelFileFailed.png");
            _TextureCache = new Dictionary<string, TextureCacheItem>();
            _TextureCacheLFU = new SortedDictionary<long, HashSet<string>>();
            _mFastTextureQuery = new Dictionary<Texture, TextureCacheItem>();
        }

        public void EnableThumbnail(Panel PanelResource)
        {
            PanelResource.PaintEvent -= OnPanelResourcePaint;
            PanelResource.PaintEvent += OnPanelResourcePaint;
        }

        void OnPanelResourcePaint(Control Sender)
        {
            Control Parent = Sender.GetParent();
            if (Sender.GetY() + Sender.GetHeight() < 0 ||
                Sender.GetY() > Parent.GetHeight())
            {
                return;
            }

            Panel PanelResource = (Panel)Sender;
            Texture TextureResource = null;
            string StandardFilename = PanelResource.GetTagString1();
            if (StandardFilename != null && StandardFilename != "")
            {
                TextureResource = GetThumbnailTexture(StandardFilename);
            }
            PanelResource.SetImage(TextureResource);
            PanelResource.PaintThis();
            PanelResource.PaintChildren();
            PanelResource.PaintThisAfter();
        }


        public void EndFrame()
        {
            mCurrentFrame++;

            // begin release

            if (_TextureCache.Count >= _MaxTextureCacheCount)
            {
                HashSet<string> deletionList = new HashSet<string>();
                foreach (var kvp in _TextureCacheLFU)
                {
                    // do not clear the cache too close
                    if(kvp.Key + 60 >= mCurrentFrame) break;

                    deletionList.UnionWith(kvp.Value);
                }

                foreach (var pathToBeReplaced in deletionList)
                {
                    TextureCacheItem TextureCacheItem = null;
                    if (_TextureCache.TryGetValue(pathToBeReplaced, out TextureCacheItem))
                    {
                        RemoveCache(TextureCacheItem);
                        TextureCacheItem.Texture.SetInternalResourceValid(false);
                        UIManager.GetMainUIManager().GetEditorUICanvas().ReleaseUIImage_UserDefined(pathToBeReplaced, new UserParam());
                    }
                }
            }
        }

        public Texture GetTexture_Cache(string Path, long ModifyTime)
        {
            TextureCacheItem TextureCacheItem = null;
            if (_TextureCache.TryGetValue(Path, out TextureCacheItem))
            {
                if (TextureCacheItem.ModifyTime == ModifyTime)
                {
                    
                    UpdateCacheUsage(TextureCacheItem);
                   
                    return TextureCacheItem.Texture;
                }
                else // force update, erase old texture
                {
                    RemoveCache(TextureCacheItem);
                }
            }
            else
            {
                TextureCacheItem = new TextureCacheItem();
            }
            //Console.WriteLine("Loading: {0}", Path);
            TextureCacheItem.ModifyTime = ModifyTime;
            TextureCacheItem.Texture = null;
            if (FileHelper.IsFileExists(Path))
            {
                ClassIDType ObjectClassID1 = Resource.GetResourceTypeStatic(Path);
                if (ObjectClassID1 == ClassIDType.CLASS_Texture2D)
                {
                    TextureCacheItem.Texture = UIManager.LoadUIImage_UserDefined(Path, new UserParam());
                    TextureCacheItem.TextureName = Path;
                }
            }


            TextureCacheItem.TextureName = Path;
            
            UpdateCacheUsage(TextureCacheItem);

            return TextureCacheItem.Texture;
        }

        public void RemoveCache(TextureCacheItem cache)
        {
            _mFastTextureQuery.Remove(cache.Texture);
            _TextureCache.Remove(cache.TextureName);

            HashSet<string> pathSet = null;
            if (_TextureCacheLFU.TryGetValue(cache.LatestUsedFrame, out pathSet))
            {
                pathSet.Remove(cache.TextureName);
                if (pathSet.Count == 0)
                {
                    _TextureCacheLFU.Remove(cache.LatestUsedFrame);
                }
            }
        }

        public void UpdateCacheUsage(TextureCacheItem cache)
        {

            HashSet<string> pathSet = null;
            if (_TextureCacheLFU.TryGetValue(cache.LatestUsedFrame, out pathSet))
            {
                pathSet.Remove(cache.TextureName);
                if (pathSet.Count == 0)
                {
                    _TextureCacheLFU.Remove(cache.LatestUsedFrame);
                }
            }

            cache.LatestUsedFrame = mCurrentFrame;

            if (_TextureCacheLFU.TryGetValue(cache.LatestUsedFrame, out pathSet))
            {
                pathSet.Add(cache.TextureName);
            }
            else
            {
                HashSet<string> newPathSet = new HashSet<string>();
                newPathSet.Add(cache.TextureName);
                _TextureCacheLFU.Add(cache.LatestUsedFrame, newPathSet);
            }

            _mFastTextureQuery[cache.Texture] = cache;
            _TextureCache[cache.TextureName] = cache;
        }


        public bool UpdateThumbnailTextureUsage(Texture texture)
        {
            TextureCacheItem cache = null;
            if (_mFastTextureQuery.TryGetValue(texture, out cache))
            {
                UpdateCacheUsage(cache);

                return true;
            }

            return false;
        }

        public Texture GetThumbnailTexture(string Path)
        {
            string Extension = PathHelper.GetExtension(Path);
            if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
            {
                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                Background.SuspendBackgroundThreads();
                ClassIDType ObjectClassID = ResourceTypeCache.GetInstance().GetResourceType_Cache(Path);
                Background.ResumeBackgroundThreads();
                if (ObjectClassID == ClassIDType.CLASS_Material ||
                    ObjectClassID == ClassIDType.CLASS_Fx ||
                    ObjectClassID == ClassIDType.CLASS_MeshAssetDataResource ||
                    ObjectClassID == ClassIDType.CLASS_Texture2D ||
                    ObjectClassID == ClassIDType.CLASS_TextureCube ||
                    ObjectClassID == ClassIDType.CLASS_Texture2DVirtual ||
                    ObjectClassID == ClassIDType.CLASS_SkeletonResource)
                {
                    string ThumbnailPath = ThumbnailScene.GetThumbnailPath(Path1);
                    if (ThumbnailScene.NeedGenerateThumbnail(Path1, ThumbnailPath) == false)
                    {
                        long ThumbnailPathModifyTime = FileHelper.GetFileModifyTime(ThumbnailPath);
                        Texture TextureThumbnail = GetTexture_Cache(ThumbnailPath, ThumbnailPathModifyTime);
                        return TextureThumbnail;
                    }
                    else
                    {
                        ThumbnailHost ThumbnailHost = ThumbnailHost.GetInstance();
                        if (ThumbnailHost.IsThumbnailTaskFailed(Path1) == false)
                        {
                            ThumbnailHost.AddThumbnailTask(Path1);
                        }
                        else
                        {
                            return _TexturePanelFileFailed;
                        }
                    }
                }
                else
                {
                    return _TexturePanelFile1;
                }
            }
            return null;
        }

        // No additional heavy file IO
        public Texture GetThumbnailTexture_CacheOnly(string Path)
        {
            string Extension = PathHelper.GetExtension(Path);
            if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
            {
                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                ClassIDType ObjectClassID = ResourceTypeCache.GetInstance().GetResourceType_CacheOnly(Path);
                if (ObjectClassID == ClassIDType.CLASS_Material ||
                    ObjectClassID == ClassIDType.CLASS_MeshAssetDataResource ||
                    ObjectClassID == ClassIDType.CLASS_Texture2D ||
                    ObjectClassID == ClassIDType.CLASS_TextureCube ||
                    ObjectClassID == ClassIDType.CLASS_SkeletonResource)
                {
                    string ThumbnailPath = ThumbnailScene.GetThumbnailPath(Path1);
                    if (ThumbnailScene.NeedGenerateThumbnail(Path1, ThumbnailPath) == false)
                    {
                        long ThumbnailPathModifyTime = FileHelper.GetFileModifyTime(ThumbnailPath);
                        Texture TextureThumbnail = GetTexture_Cache(ThumbnailPath, ThumbnailPathModifyTime);
                        return TextureThumbnail;
                    }
                    else
                    {
                        ThumbnailHost ThumbnailHost = ThumbnailHost.GetInstance();
                        if (ThumbnailHost.IsThumbnailTaskFailed(Path1) == false)
                        {
                            ThumbnailHost.AddThumbnailTask(Path1);
                        }
                        else
                        {
                            return _TexturePanelFileFailed;
                        }
                    }
                }
                else
                {
                    return _TexturePanelFile1;
                }
            }
            return null;
        }
    }
}
