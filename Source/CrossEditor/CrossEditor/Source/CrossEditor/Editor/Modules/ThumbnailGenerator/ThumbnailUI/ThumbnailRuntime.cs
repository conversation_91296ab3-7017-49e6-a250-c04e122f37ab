using EditorUI;
using System;

namespace CrossEditor
{
    class ThumbnailRuntime
    {
        static ThumbnailRuntime _Instance = new ThumbnailRuntime();

        CrossEngine _CrossEngine;
        IntPtr _RenderWindowHandle;
        Clicross.IRenderWindow _RenderWindow;

        public static ThumbnailRuntime GetInstance()
        {
            return _Instance;
        }

        ThumbnailRuntime()
        {
        }

        public UIManager GetUIManager()
        {
            return ThumbnailUI.GetInstance().GetUIManager();
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        static void ThumbnailLogHandler(int LogType, string LogMessage)
        {
        }

        public void Initialize(string ProjectDirectory, string ResourceDirectory)
        {
            Device Device = GetDevice();

            EditorUICanvas EditorUICanvas = new EditorUICanvas();
            Device.SetEditorUICanvas(EditorUICanvas);

            _CrossEngine = SceneRuntime.GetInstance().GetCrossEngine();
            //_CrossEngine.SetLogHandler(new CrossEngineLogDelegate(ThumbnailLogHandler));
            //string[] args = {"exe"};
            // _CrossEngine.Initialize(AppStartUpType.ThumbnailProcessor, ProjectDirectory, ResourceDirectory, true, args);
            float DPR = 1.0f;
            string Title = "Thumbnail Generator";
            _RenderWindowHandle = Device.GetNativeWindowPointer();

            int DeviceWidth = Device.GetWidth();
            int DeviceHeight = Device.GetHeight();

            _RenderWindow = _CrossEngine.CreateRenderWindow(_RenderWindowHandle, 0, 0, DeviceWidth, DeviceHeight, DPR, Title, !ThumbnailUI.bShowThumbnailWindow);

            EditorUICanvas.Initialize(OnEditorUI, _RenderWindow);
            EditorUICanvas.SetSize(DeviceWidth, DeviceHeight);
            ThumbnailScene ThumbnailScene = ThumbnailScene.GetInstance();
            ThumbnailScene.Initialize(_CrossEngine, _RenderWindow);
        }


        public void OnEditorUI()
        {
            SceneRuntime.GetInstance().OnEditorUI();
            ThumbnailScene.GetInstance().DrawScene();

            ThumbnailHelper.GetInstance().EndFrame();
        }
    }
}
