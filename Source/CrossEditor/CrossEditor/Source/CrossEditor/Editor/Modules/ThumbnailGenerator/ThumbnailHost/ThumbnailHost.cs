using EditorUI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;

namespace CrossEditor
{
    public class ThumbnailHost
    {
        static ThumbnailHost _Instance = new ThumbnailHost();

        public const string Filename_FileToGenerateThumbnail = "Intermediate/Thumb/FileToGenerateThumbnail.xml";
        public const string Filename_FileThumbnailGenerated = "Intermediate/Thumb/FileThumbnailGenerated.xml";

        Mutex _MutexThumbnail;

        Process _ThumbnailProcess;
        ManualResetEvent _ThumbnailHostThreadEvent;
        Thread _ThumbnailHostThread;

        List<string> _ThumbnailTasks;
        object _ThumbnailTasksLock;

        HashSet<string> _ThumbnailFailedTasks;
        object _ThumbnailFailedTasksLock;

        public static ThumbnailHost GetInstance()
        {
            return _Instance;
        }

        ThumbnailHost()
        {
            _ThumbnailHostThreadEvent = new ManualResetEvent(true);

            _ThumbnailTasks = new List<string>();
            _ThumbnailTasksLock = new object();
            _ThumbnailFailedTasks = new HashSet<string>();
            _ThumbnailFailedTasksLock = new object();
        }

        public bool CreateThumbnailMutex()
        {
            _MutexThumbnail = new Mutex(false, "Thumbnail");
            try
            {
                _MutexThumbnail.WaitOne(0);
                return true;
            }
            catch (AbandonedMutexException)
            {
                return false;
            }
        }

        public void ReleaseThumbnailMutex()
        {
            _MutexThumbnail.ReleaseMutex();
        }


        public void CreateGenerateThumbnailProcess()
        {
            string DotNetProgramPath = EditorUtilities.GetDotNetProgramPath();
            string ExecutableDirectory = DirectoryHelper.GetExecutableDirectory();
            string EditorProgramPath = string.Format("\"{0}/CrossEditor.dll\"", ExecutableDirectory);
            string ThumbnailProject = string.Format("ThumbnailProject=\"{0}\"", MainUI.GetInstance().GetProjectDirectory());
            string Parameter = EditorProgramPath + " " + ThumbnailProject;
            _ThumbnailProcess = ProcessHelper.Execute(DotNetProgramPath, Parameter);
        }

        public void TeminateGenerateThumbnailProcess()
        {
            if (_ThumbnailProcess != null)
            {
                if (_ThumbnailProcess.HasExited == false)
                {
                    _ThumbnailProcess.Kill();
                    _ThumbnailProcess = null;
                }
            }
        }

        public void MakeSureThumbnailProcessAlive()
        {
            if (_ThumbnailProcess == null || _ThumbnailProcess.HasExited)
            {
                CreateGenerateThumbnailProcess();
            }
        }

        public bool IsThumbnailTaskFailed(string ThumbnailTask)
        {
            lock (_ThumbnailFailedTasksLock)
            {
                if (_ThumbnailFailedTasks.Contains(ThumbnailTask))
                {
                    return true;
                }
            }
            return false;
        }

        public void ClearThumbnailTasks()
        {
            lock (_ThumbnailTasksLock)
            {
                _ThumbnailTasks.Clear();
            }
        }

        public void AddThumbnailTask(string ThumbnailTask)
        {
            lock (_ThumbnailTasksLock)
            {
                if (_ThumbnailTasks.IndexOf(ThumbnailTask) == -1)
                {
                    _ThumbnailTasks.Insert(0, ThumbnailTask);
                }
            }
        }

        public void RemoveThumbnailTask(string ThumbnailTask)
        {
            lock (_ThumbnailTasksLock)
            {
                _ThumbnailTasks.Remove(ThumbnailTask);
            }
        }

        public void AddFailedThumbnailTask(string ThumbnailTask)
        {
            lock (_ThumbnailFailedTasksLock)
            {
                _ThumbnailFailedTasks.Add(ThumbnailTask);
            }
        }

        public void RemoveFailedThumbnailTask(string ThumbnailTask)
        {
            lock (_ThumbnailFailedTasksLock)
            {
                _ThumbnailFailedTasks.Remove(ThumbnailTask);
            }
        }

        public void CreateThumbnailHostThread()
        {
            _ThumbnailHostThread = new Thread(ThumbnailHostThreadStart);
            _ThumbnailHostThread.Start();
        }

        public void SuspendThumbnailHostThread()
        {
            _ThumbnailHostThreadEvent.Reset();
        }

        public void ResumeThumbnailHostThread()
        {
            _ThumbnailHostThreadEvent.Set();
        }

        public void ThumbnailHostThreadStart()
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string FileToGenerateThumbnail = ProjectDirectory + "/" + Filename_FileToGenerateThumbnail;
            string FileToGenerateThumbnailTemp = FileToGenerateThumbnail + ".tmp";
            string FileThumbnailGenerated = ProjectDirectory + "/" + Filename_FileThumbnailGenerated;
            while (true)
            {
                _ThumbnailHostThreadEvent.WaitOne();

                string ThumbnailTask = "";
                lock (_ThumbnailTasksLock)
                {
                    if (_ThumbnailTasks.Count > 0)
                    {
                        ThumbnailTask = _ThumbnailTasks[0];
                        _ThumbnailTasks.RemoveAt(0);
                    }
                }
                if (ThumbnailTask != "")
                {
                    if (ThumbnailUI.bShowThumbnailLog)
                    {
                        Console.WriteLine("ThumbnailHost: {0} start.", ThumbnailTask);
                    }
                    string ThumbnailPath = ThumbnailScene.GetThumbnailPath(ThumbnailTask);
                    if (ThumbnailScene.NeedGenerateThumbnail(ThumbnailTask, ThumbnailPath))
                    {
                        if (ThumbnailTask.Contains("Intermediate/Thumb") == false)
                        {
                            // MakeSureThumbnailProcessAlive();
                            FileHelper.DeleteFile(ThumbnailPath);
                            FileHelper.DeleteFile(FileToGenerateThumbnail);
                            FileHelper.DeleteFile(FileToGenerateThumbnailTemp);
                            FileHelper.DeleteFile(FileThumbnailGenerated);
                            FileHelper.WriteTextFile(FileToGenerateThumbnailTemp, ThumbnailTask);
                            FileHelper.RenameFile(FileToGenerateThumbnailTemp, FileToGenerateThumbnail);
                            while (FileHelper.IsFileExists(FileThumbnailGenerated) == false)
                            {
                            }
                            FileHelper.DeleteFile(FileToGenerateThumbnail);
                            FileHelper.DeleteFile(FileToGenerateThumbnail);
                            FileHelper.DeleteFile(FileToGenerateThumbnailTemp);
                            if (FileHelper.IsFileExists(ThumbnailPath) == false)
                            {
                                AddFailedThumbnailTask(ThumbnailTask);
                            }
                        }
                    }
                }
            }
        }
    }
}
