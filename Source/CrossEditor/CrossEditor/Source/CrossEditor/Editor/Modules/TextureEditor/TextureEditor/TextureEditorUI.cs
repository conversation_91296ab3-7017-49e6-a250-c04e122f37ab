using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    class TextureEditorUI : DockingUI
    {
        protected Panel _PanelPreviewUI;
        protected Panel _PanelPreview;
        protected Panel _PanelImage;
        protected Panel _PanelZoomIn;
        //protected Panel _TestPanel;

        OperationBarUI _OperationBar;
        Button _Button_R;
        Button _Button_G;
        Button _Button_B;
        Button _Button_A;

        List<Button> ButtonList = new List<Button>();
        TrackBar _TrackBarMipMap;
        Label _TextMipMap;
        TrackBar _TrackBarZoom;
        Label _TextZoom;
        Label _TextScale;
        Label left;
        Label right;

        UserParam UserParam = new UserParam();
        int DisplayValue = 0;
        int X;
        int Y;
        string NdaFilePath = "";
        float Scale = 1.0f;
        const float FixedScale = 16.0f;
        float MaxDisplayScale = 16.0f;
        float MinDisplayScale = 0.1f;
        int ZoomInWidth = 128;
        float DefaultTrackBarValue = 1 / 16.0f;

        int CenterX = 0;
        int CenterY = 0;
        int ImageWidth = 0;
        int ImageHeight = 0;
        int LeftUpperX = 0;
        int LeftUpperY = 0;
        int RightLowX = 0;
        int RightLowY = 0;
        int SpaceX = 0;
        int SpaceY = 0;
        const int LabelHeight = 20;
        const int Margin = 1;
        const int OPTION_FONT_SIZE = 15;
        const int ScreenImageWidth = 512;
        const int ScreenImageHeight = 512;
        int LeftX = 0;
        int LeftY = 0;
        int Width = 0;
        int Height = 0;
        float AdaptScale = 1.0f;
        bool IsAdapt = true;
        int RightMouseDownX = 0;
        int RightMouseDownY = 0;
        int ScaleWidth = 0;
        int ScaleHeight = 0;
        bool bRightMouseDown = false;
        bool bPreviewRightMouseDown = false;
        float _OldScale = 1.0f;

        private TextureResource _CurrentTextureResourceData = null;

        List<string> CESuffix = new List<string> { "_BaseColor.", "_Normal.", "_MRO." };

        static TextureEditorUI Instance = new TextureEditorUI();

        public TextureEditorUI()
        {
        }

        public static TextureEditorUI GetInstance()
        {
            return new TextureEditorUI();
        }

        public void Initialize()
        {
            _OperationBar = new OperationBarUI();
            _OperationBar.Initialize();
            _OperationBar.GetPanelBar().SetEnable(true);
            _OperationBar.GetPanelBar().SetVisible(true);

            _Button_R = OperationBarUI.CreateTextButton("R");
            _Button_R.SetTagObject(true);
            _Button_R.ClickedEvent += OnButtonRGBAClicked;
            _OperationBar.AddLeft(_Button_R);
            ButtonList.Add(_Button_R);

            _Button_G = OperationBarUI.CreateTextButton("G");
            _Button_G.SetTagObject(true);
            _Button_G.ClickedEvent += OnButtonRGBAClicked;
            _OperationBar.AddLeft(_Button_G);
            ButtonList.Add(_Button_G);

            _Button_B = OperationBarUI.CreateTextButton("B");
            _Button_B.SetTagObject(true);
            _Button_B.ClickedEvent += OnButtonRGBAClicked;
            _OperationBar.AddLeft(_Button_B);
            ButtonList.Add(_Button_B);

            _Button_A = OperationBarUI.CreateTextButton("A");
            _Button_A.SetTagObject(true);
            //_Button_A.SetEnable(false);
            _Button_A.ClickedEvent += OnButtonRGBAClicked;
            _OperationBar.AddLeft(_Button_A);
            ButtonList.Add(_Button_A);

            _TextZoom = new Label();
            _TextZoom.Initialize();
            _TextZoom.SetText("Zoom");
            _TextZoom.SetFontSize(16);
            _TextZoom.SetPosition(0, 6, _TextZoom.CalculateTextWidth() + 10, 16);
            _OperationBar.AddRight(_TextZoom);

            _TrackBarZoom = new TrackBar();
            _TrackBarZoom.Initialize();
            _TrackBarZoom.SetValue(DefaultTrackBarValue);
            _TrackBarZoom.ValueChangedEvent += OnZoomTrackBarValueChanged;
            _TrackBarZoom.SetPosition(5, 5, 160, 14);
            _TrackBarZoom.SetBackgroundColor(Color.FromRGB(50, 50, 50));
            _OperationBar.AddRight(_TrackBarZoom);

            _TextScale = new Label();
            _TextScale.Initialize();
            _TextScale.SetText(_TrackBarZoom.GetValue() * 1600 + "%");
            _TextScale.SetFontSize(16);
            _TextScale.SetPosition(0, 6, _TextScale.CalculateTextWidth() + 20, 16);
            _OperationBar.AddRight(_TextScale);

            _TrackBarMipMap = new TrackBar();
            _TrackBarMipMap.Initialize();
            _TrackBarMipMap.SetValue(0.0f);
            _TrackBarMipMap.ValueChangedEvent += OnMipmapTrackBarValueChanged;
            _TrackBarMipMap.SetPosition(5, 5, 80, 14);
            _TrackBarMipMap.SetBackgroundColor(Color.FromRGB(50, 50, 50));
            _OperationBar.AddRight(_TrackBarMipMap);

            _TextMipMap = new Label();
            _TextMipMap.Initialize();
            _TextMipMap.SetText("MipMap 0");
            _TextMipMap.SetFontSize(16);
            _TextMipMap.SetPosition(0, 6, _TextMipMap.CalculateTextWidth() + 10, 16);
            _OperationBar.AddRight(_TextMipMap);

            _OperationBar.Refresh();

            _PanelPreview = new Panel();
            _PanelPreview.Initialize();
            _PanelPreview.SetEnable(true);
            _PanelPreview.SetVisible(true);
            _PanelPreview.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _PanelPreview.MouseMoveEvent += OnPanelPreviewMouseMoveEvent;
            _PanelPreview.RightMouseDownEvent += OnPanelPreviewRightMouseDownEvent;
            _PanelPreview.RightMouseUpEvent += OnPanelPreviewRightMouseUpEvent;

            //_TestPanel = new Panel();
            //_TestPanel.Initialize();
            //_TestPanel.SetEnable(true);
            //_TestPanel.SetVisible(true);
            //_TestPanel.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            //_PanelPreview.AddChild(_TestPanel);

            _PanelImage = new Panel();
            _PanelImage.Initialize();
            _PanelImage.SetEnable(true);
            _PanelImage.SetVisible(true);
            _PanelImage.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            _PanelImage.SetTagString1(NdaFilePath);
            _PanelImage.MouseMoveEvent += OnPanelImageMouseMoveEvent;
            _PanelImage.LeftMouseDownEvent += OnPanelImageLeftDownEvent;
            _PanelImage.RightMouseDownEvent += OnPanelImageRightMouseDownEvent;
            _PanelImage.RightMouseUpEvent += OnPanelImageRightMouseUpEvent;
            _PanelPreview.AddChild(_PanelImage);

            _PanelZoomIn = new Panel();
            _PanelZoomIn.Initialize();
            _PanelZoomIn.SetEnable(true);
            _PanelZoomIn.SetVisible(true);
            _PanelZoomIn.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            _PanelZoomIn.SetTagString1(NdaFilePath);
            //_PanelPreview.AddChild(_PanelZoomIn);

            left = new Label();
            right = new Label();
            left.Initialize();
            left.SetTextColor(Color.EDITOR_UI_TEST_COLOR_GREEN);
            left.SetText("RGB: ");
            left.SetFontSize(OPTION_FONT_SIZE);
            left.SetTextOffsetY(2);
            left.SetPosition(Margin, OPTION_FONT_SIZE, left.CalculateTextWidth(), LabelHeight);
            //_PanelPreview.AddChild(left);

            right.Initialize();
            right.SetTextColor(Color.EDITOR_UI_TEST_COLOR_GREEN);
            right.SetTagString1("R: 0, G:0, B:0");
            right.SetText("R: 0, G:0, B:0");
            right.SetFontSize(OPTION_FONT_SIZE);
            right.SetTextOffsetY(2);
            right.SetPosition(left.GetWidth() + Margin, OPTION_FONT_SIZE, right.CalculateTextWidth(), LabelHeight);
            //_PanelPreview.AddChild(right);

            _PanelPreviewUI = new Panel();
            _PanelPreviewUI.Initialize();
            _PanelPreviewUI.MouseWheelEvent += OnPanelPreviewMouseWheel;
            _PanelPreviewUI.AddChild(_OperationBar.GetPanelBar());
            _PanelPreviewUI.AddChild(_PanelPreview);

            //Scale = 1.0f;
            SetButtonBorderColor();
            base.Initialize(NdaFilePath.Length == 0 ? "TextureEditor" :
                PathHelper.GetFileName(NdaFilePath), _PanelPreviewUI);
        }

        private void OnPanelPreviewRightMouseUpEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            bPreviewRightMouseDown = false;
            Sender.ReleaseMouse();
        }

        private void OnPanelPreviewRightMouseDownEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            bPreviewRightMouseDown = true;
            RightMouseDownX = MouseX;
            RightMouseDownY = MouseY;
            Sender.CaptureMouse();
        }

        private void OnPanelPreviewMouseMoveEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_PanelImage.IsPointIn(MouseX, MouseY))
            {
                if (bPreviewRightMouseDown)
                {
                }
            }
        }

        private void OnPanelImageRightMouseUpEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            bRightMouseDown = false;
            Sender.ReleaseMouse();
        }

        private void OnPanelImageRightMouseDownEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_PanelImage.IsPointIn(MouseX, MouseY))
            {
                bRightMouseDown = true;
                RightMouseDownX = MouseX;
                RightMouseDownY = MouseY;
                X = _PanelImage.GetX();
                Y = _PanelImage.GetY();
                Sender.CaptureMouse();
            }
        }

        private void OnPanelImageLeftDownEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            //if (_PanelImage.IsPointIn(MouseX, MouseY))
            //{
            //    bool flag = CEResource.DoesTexture2DLookLikelyToBeANormalMap(_CurrentTextureResourceData.ResourcePtr, NdaFilePath);
            //    bool a = flag;
            //}
        }

        private void OnPanelImageMouseMoveEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_PanelImage.IsPointIn(MouseX, MouseY))
            {
                int ImageX = MouseX - _PanelImage.GetScreenX() + SpaceX;
                int ImageY = MouseY - _PanelImage.GetScreenY() + SpaceY;

                int X1 = ImageX;
                int Y1 = ImageY;
                int X2 = X1 + ZoomInWidth;
                int Y2 = Y1 + ZoomInWidth;
                GetRectangle(ref X1, ref Y1, ref X2, ref Y2);

                LeftX = (int)((ImageX - X) / Scale / AdaptScale);
                LeftY = (int)((ImageY - Y) / Scale / AdaptScale);
                if (IsAdapt && Scale > 1.0f)
                {
                    Width = (int)((X2 - X1) <= ZoomInWidth ? (X2 - X1) : ZoomInWidth);
                    Height = (int)((Y2 - Y1) <= ZoomInWidth ? (Y2 - Y1) : ZoomInWidth);
                    int ActualWidth = LeftX + Width;
                    int ActualHeight = LeftY + Height;
                    if (ActualWidth > (int)((RightLowX - X) / Scale / AdaptScale))
                    {
                        Width = (int)((RightLowX - X) / Scale / AdaptScale) - LeftX;
                    }
                    if (ActualHeight > (int)((RightLowY - Y) / Scale / AdaptScale))
                    {
                        Height = (int)((RightLowY - Y) / Scale / AdaptScale) - LeftY;
                    }
                }
                else
                {
                    Width = (int)((X2 - X1) / Scale / AdaptScale <= ZoomInWidth ? (X2 - X1) / Scale / AdaptScale : ZoomInWidth);
                    Height = (int)((Y2 - Y1) / Scale / AdaptScale <= ZoomInWidth ? (Y2 - Y1) / Scale / AdaptScale : ZoomInWidth);
                }
                if (bRightMouseDown)
                {
                    if (ScaleWidth > _PanelPreviewUI.GetWidth() || ScaleHeight > _PanelPreviewUI.GetHeight())
                    {
                        int DeltaX = (int)(MouseX - RightMouseDownX);
                        int DeltaY = (int)(MouseY - RightMouseDownY);
                        _PanelImage.SetPosition(X + DeltaX, Y + DeltaY, ScaleWidth, ScaleHeight);
                    }
                }
            }
            else
            {
                bRightMouseDown = false;
            }
        }

        void SetButtonBorderColor()
        {
            foreach (Button n in ButtonList)
            {
                if ((bool)n.GetTagObject())
                {
                    if (n.GetText() == "R")
                    {
                        n.SetNormalColor(Color.FromRGB(255, 0, 0));
                        n.SetHoverColor(Color.FromRGB(255, 0, 0));
                    }
                    else if (n.GetText() == "G")
                    {
                        n.SetNormalColor(Color.FromRGB(0, 255, 0));
                        n.SetHoverColor(Color.FromRGB(0, 255, 0));
                    }
                    else if (n.GetText() == "B")
                    {
                        n.SetNormalColor(Color.FromRGB(0, 0, 255));
                        n.SetHoverColor(Color.FromRGB(0, 0, 255));
                    }
                    else
                    {
                        n.SetNormalColor(Color.FromRGB(180, 180, 180));
                        n.SetHoverColor(Color.FromRGB(180, 180, 180));
                    }
                }
                else
                {
                    n.SetNormalColor(Color.FromRGB(82, 82, 82));
                    n.SetHoverColor(Color.FromRGB(82, 82, 82));
                }
            }
        }

        void OnButtonRGBAClicked(Button Sender)
        {
            Sender.SetTagObject(!(bool)Sender.GetTagObject());
            SetButtonBorderColor();
            UserParam.buttonR = (bool)_Button_R.GetTagObject();
            UserParam.buttonG = (bool)_Button_G.GetTagObject();
            UserParam.buttonB = (bool)_Button_B.GetTagObject();
            UserParam.buttonA = (bool)_Button_A.GetTagObject();
            UserParam.isRGBA = true;
            if (!(UserParam.buttonR && UserParam.buttonG && UserParam.buttonB && UserParam.buttonA))
            {
                UserParam.isSRGB = false;
            }
            else
            {
                if (_CurrentTextureResourceData.TextureResourceInfo.ColorSpace == ColorSpace.SRGB)
                {
                    UserParam.isSRGB = true;
                }
            }
            UserParam.MipLevel = (sbyte)DisplayValue;
            _PanelImage.SetImage(UIManager.LoadUIImage_UserDefined(NdaFilePath, UserParam));
        }

        void OnMipmapTrackBarValueChanged(TrackBar Sender)
        {

            float Value = Sender.GetValue();
            DisplayValue = (int)(Math.Round((Value / (1.0f / _CurrentTextureResourceData.TextureResourceInfo.MipCount) - 1), 0));
            DisplayValue = DisplayValue < 0 ? 0 : DisplayValue;
            _TextMipMap.SetText("MipMap " + DisplayValue);
            UserParam.isMipmap = true;
            UserParam.MipLevel = (sbyte)DisplayValue;
            _PanelImage.SetImage(UIManager.LoadUIImage_UserDefined(NdaFilePath, UserParam));
        }

        void OnZoomTrackBarValueChanged(TrackBar Sender)
        {
            float Value = Sender.GetValue();
            int ZoomDisplayValue = 100;
            ZoomDisplayValue = (int)(Value * FixedScale * 100);
            _TextScale.SetText(ZoomDisplayValue + "%");
            Scale = Value * FixedScale;
            if (Scale == 0.0f)
            {
                Scale = 0.1f;
            }
            PanelSetPosition(Scale, false);
            _OldScale = Scale;
            //OnPanelImageZoom(Scale, CenterX, CenterY);
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            _PanelPreviewUI = (Panel)Sender;

            _OperationBar.GetPanelBar().SetPosition(0, 0, _PanelPreviewUI.GetWidth(), 20);

            Width = _PanelPreviewUI.GetWidth() / 2;
            Height = _PanelPreviewUI.GetHeight() / 2;

            _PanelZoomIn.SetPosition(_PanelPreviewUI.GetWidth() - ZoomInWidth, 0, ZoomInWidth, ZoomInWidth);
            _PanelPreview.SetPosition(0, 25, _PanelPreviewUI.GetWidth(), _PanelPreviewUI.GetHeight());

            CenterX = _PanelPreviewUI.GetWidth() / 2;
            CenterY = _PanelPreviewUI.GetHeight() / 2;
            SpaceX = CenterX - ImageWidth / 2;
            SpaceY = CenterY - ImageHeight / 2;
            LeftUpperX = CenterX - ImageWidth / 2;
            LeftUpperY = CenterY - ImageHeight / 2;
            RightLowX = CenterX + ImageWidth / 2;
            RightLowY = CenterY + ImageHeight / 2;
            PanelSetPosition(Scale, true);
        }

        public void OnPanelPreviewMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            if (_PanelPreviewUI.IsPointIn(MouseX, MouseY))
            {
                Scale *= 1f + MouseDeltaZ * 0.1f;
                if (Scale >= MaxDisplayScale)
                {
                    Scale = MaxDisplayScale;
                }
                else if (Scale <= MinDisplayScale)
                {
                    Scale = MinDisplayScale;
                }
                if (Scale == MinDisplayScale && MouseDeltaZ < 0)
                {
                    _TextScale.SetText("0%");
                    _TrackBarZoom.SetValue(0);
                    return;
                }
                if (ScaleWidth > _PanelPreviewUI.GetWidth() || ScaleHeight > _PanelPreviewUI.GetHeight())
                {
                    OnPanelImageZoom(Scale, MouseX, MouseY);
                }
                else
                {
                    PanelSetPosition(Scale, false);
                }
                float TempScale = ((Scale - MinDisplayScale) / (MaxDisplayScale - MinDisplayScale) * 100);
                _TextScale.SetText((int)(TempScale * MaxDisplayScale) + "%");
                _TrackBarZoom.SetValue((TempScale / 100.0f));
            }
        }

        public void OnPanelImageZoom(float ScaleParam, int MouseX = 0, int MouseY = 0)
        {
            int ValueX = MouseX;
            int ValueY = MouseY;
            int MouseXValue = ValueX - _PanelPreview.GetScreenX();
            int MouseYValue = ValueY - _PanelPreview.GetScreenY();
            int ImageX = _PanelImage.GetX();
            int ImageY = _PanelImage.GetY();
            int DeltaX = (int)((MouseXValue - ImageX) * (ScaleParam / _OldScale));
            int DeltaY = (int)((MouseYValue - ImageY) * (ScaleParam / _OldScale));
            ScaleWidth = (int)(ImageWidth * ScaleParam);
            ScaleHeight = (int)(ImageHeight * ScaleParam);
            X = MouseXValue - DeltaX;
            Y = MouseYValue - DeltaY;
            _PanelImage.SetPosition(X, Y, ScaleWidth, ScaleHeight);
            _OldScale = ScaleParam;
        }

        public bool OpenTexture(string FilePath, bool IsReload = true)
        {
            TextureEditorManager.GetInstance().AddTextureEditorUI(this);
            TextureResource TextureResourceData = (TextureResource)Resource.Get(EditorUtilities.EditorFilenameToStandardFilename(FilePath), IsReload);
            if (TextureResourceData != null)
            {
                if (TextureResourceData.TextureResourceInfo.VirtualTextureStreaming)
                {
                    Initialize();
                    UpdateInspector(FilePath);
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Virtual Texture Not Support Preview yet");
                    return false;
                }
                _CurrentTextureResourceData = TextureResourceData;
                NdaFilePath = FilePath;
                ImageWidth = (int)_CurrentTextureResourceData.TextureResourceInfo.Width;
                ImageHeight = (int)_CurrentTextureResourceData.TextureResourceInfo.Height;
                AdaptScale = 1.0f;
                IsAdapt = true;

                Scale = AdaptScale;
                DefaultTrackBarValue *= AdaptScale;
                Initialize();
                GetDockingCard().SetTagString1(FilePath);
                GetDockingCard().SetText(PathHelper.GetFileName(FilePath));
                if (_CurrentTextureResourceData.TextureResourceInfo.ColorSpace == ColorSpace.Linear ||
                    _CurrentTextureResourceData.TextureResourceInfo.ColorSpace == ColorSpace.Count)
                {

                }
                else if (_CurrentTextureResourceData.TextureResourceInfo.ColorSpace == ColorSpace.SRGB)
                {
                    UserParam.isSRGB = true;
                }
                if (_CurrentTextureResourceData.TextureResourceInfo.Format == TextureFormat.BC5)
                {
                    UserParam.isUnPack = true;
                }
                UserParam.isMipmap = false;
                UserParam.MipLevel = (sbyte)0;
                _PanelImage.SetImage(UIManager.LoadUIImage_UserDefined(FilePath, UserParam));
                return true;
            }
            return false;
        }

        public void UpdateInspector(string FilePath)
        {
            TextureResource TextureResourceData = (TextureResource)Resource.Get(EditorUtilities.EditorFilenameToStandardFilename(FilePath), false);
            _CurrentTextureResourceData = TextureResourceData;
            ResourceInspectorUI.Inspect(TextureResourceData);
        }

        void PanelSetPosition(float ScaleParam, bool IsSetXY)
        {
            LeftUpperX = (int)(CenterX - ImageWidth * ScaleParam / 2);
            LeftUpperY = (int)(CenterY - ImageHeight * ScaleParam / 2);
            RightLowX = (int)(CenterX + ImageWidth * ScaleParam / 2);
            RightLowY = (int)(CenterY + ImageHeight * ScaleParam / 2);

            ScaleWidth = (int)(ImageWidth * ScaleParam);
            ScaleHeight = (int)(ImageHeight * ScaleParam);
            SpaceX = CenterX - ScaleWidth / 2;
            SpaceY = CenterY - ScaleHeight / 2;

            X = CenterX - ScaleWidth / 2;
            Y = CenterY - ScaleHeight / 2;
            _PanelImage.SetPosition(X, Y, ScaleWidth, ScaleHeight);

            _OldScale = ScaleParam;
        }

        void GetRectangle(ref int X1, ref int Y1, ref int X2, ref int Y2)
        {

            if (X1 >= LeftUpperX && Y1 < LeftUpperY)
            {
                Y1 = LeftUpperY;
            }
            else if (X1 < LeftUpperX && Y1 >= LeftUpperY)
            {
                X1 = LeftUpperX;
            }
            else if (X1 < LeftUpperX && Y1 < LeftUpperY)
            {
                X1 = LeftUpperX;
                Y1 = LeftUpperY;
            }

            if (X2 > RightLowX && Y2 > RightLowY)
            {
                X2 = RightLowX;
                Y2 = RightLowY;
            }
            else if (X2 > RightLowX && Y2 <= RightLowY)
            {
                X2 = RightLowX;
            }
            else if (X2 <= RightLowX && Y2 > RightLowY)
            {
                Y2 = RightLowY;
            }
        }

        #region Texture recognition CE
        //Texture recognition CE standard  _BaseColor、_Normal、_MRO
        public Dictionary<string, List<string>> AutoRecognition(ref List<string> Textures)
        {
            Dictionary<string, List<string>> Dictionary = new Dictionary<string, List<string>>();

            // judge accord with nanhang
            bool IsBelong = true;
            foreach (var Texture in Textures)
            {
                if (!Texture.Contains(CESuffix[0]))
                {
                    IsBelong = false;
                    break;
                }
            }
            if (!IsBelong)
            {
                return new Dictionary<string, List<string>>();
            }

            foreach (var Texture in Textures)
            {
                if (Dictionary.ContainsKey(Texture))
                {
                    continue;
                }
                string Suffix = "";
                if (!Texture.Contains(CESuffix[0])) continue;

                string Name = ProcessSuffix(Texture, ref Suffix);
                if (Name != "")
                {
                    List<string> MoreFile = QueryMoreFile(Texture, Name);
                    if (MoreFile.Count != 0)
                    {
                        Dictionary.Add(Name, MoreFile);
                        continue;
                    }
                }
            }

            foreach (var TextureGroup in Dictionary)
            {
                if (TextureGroup.Value.Count == 3)
                {
                    Textures.Clear();
                    break;
                }
            }
            return Dictionary;
        }

        public string ProcessSuffix(string path, ref string Suffix)
        {
            string FileName = Path.GetFileName(path);
            foreach (var suffix in CESuffix)
            {
                if (FileName.Contains(suffix))
                {
                    Suffix = suffix;
                    return FileName.Substring(0, FileName.IndexOf(suffix));
                }
            }
            return "";
        }

        public List<string> QueryMoreFile(string Texture, string PrefixName)
        {
            List<string> QueryFiles = new List<string>();
            string Directory = Path.GetDirectoryName(Texture);
            DirectoryInfo Dir = new DirectoryInfo(Directory);
            FileInfo[] files = Dir.GetFiles();
            foreach (var f in files)
            {
                string name = Path.GetFileName(f.FullName.ToString());

                foreach (var Suffix in CESuffix)
                {
                    if (name.StartsWith(PrefixName + Suffix))
                    {
                        QueryFiles.Add(f.FullName.ToString());
                    }
                }
            }

            if (QueryFiles.Count < 3)
            {
                return QueryFiles;
            }

            if (QueryFiles[1] != "" && !QueryFiles[1].Contains(CESuffix[1]))
            {
                string Swap = QueryFiles[1];
                QueryFiles[1] = QueryFiles[2];
                QueryFiles[2] = Swap;
            }
            return QueryFiles;
        }
        #endregion

        #region Texture Relate Enum
        public List<string> GetTextureUesdEnum()
        {
            string[] Arr = { "RGBA32", "BC1", "BC3", "BC5", "BC6H", "BC7", "Linear", "SRGB" };
            List<string> List = new List<string>(Arr);
            return List;
        }
        #endregion
    }
}