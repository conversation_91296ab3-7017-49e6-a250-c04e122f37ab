using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class TextureEditorManager
    {
        static TextureEditorManager Instance = new TextureEditorManager();

        List<TextureEditorUI> TextureEditorUIList = new List<TextureEditorUI>();

        public TextureEditorManager() { }

        public static TextureEditorManager GetInstance() { return Instance; }

        public void AddTextureEditorUI(TextureEditorUI TextureEditorUI)
        {
            TextureEditorUIList.Add(TextureEditorUI);
        }

        public void RemoveTextureEditorUI(TextureEditorUI TextureEditorUI)
        {
            TextureEditorUIList.Remove(TextureEditorUI);
        }

        public DockingUI FindDockingUIByFilePath(String Path)
        {
            DockingUI DockingUI = null;
            foreach (var UI in TextureEditorUIList)
            {
                if (UI.GetDockingCard().GetTagString1() == Path && UI.IsDockingCardDisplay())
                {
                    DockingUI = UI;
                    break;
                }
            }
            return DockingUI;
        }
    }
}
