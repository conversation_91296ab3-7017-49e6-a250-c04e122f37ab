using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class DataAssetEditorUIManager
    {
        public static DataAssetEditorUIManager Instance { get; } = new DataAssetEditorUIManager();
        private Dictionary<string, DataAssetEditorUI> mDataAssetEditors = new Dictionary<string, DataAssetEditorUI>();

        DataAssetEditorUIManager() { }

        public void Update(long TimeElapsed)
        {
        }

        public void OpenDataAsset(string FilePath)
        {
            DockingCard dockingCard = null;
            if (!mDataAssetEditors.ContainsKey(FilePath))
            {
                var editor = new DataAssetEditorUI(FilePath);
                string fileName = FilePath.Substring(FilePath.LastIndexOf('/') + 1);
                editor.Initialize(fileName);
                dockingCard = editor.GetDockingCard();
                dockingCard.SetDocument(true);
                mDataAssetEditors.Add(FilePath, editor);
                dockingCard.CloseEvent += (DockingCard Sender, ref bool bNotToClose) =>
                {
                    mDataAssetEditors.Remove(FilePath);
                };
            }
            else
            {
                dockingCard = mDataAssetEditors[FilePath].GetDockingCard();
            }
            MainUI.GetInstance().ActivateDockingCard_WorkflowEditor(dockingCard);
        }
    }
}