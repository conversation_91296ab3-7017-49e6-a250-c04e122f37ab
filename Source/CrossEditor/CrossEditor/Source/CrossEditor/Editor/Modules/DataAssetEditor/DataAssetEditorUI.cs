using Clicross;
using EditorUI;

namespace CrossEditor
{
    class DataAssetEditorUI : DockingUI
    {
        VContainer mMainContainer = new VContainer();
        Button mSaveBtn = OperationBarUI.CreateTextButton("Save");
        DataAssetEditorContext mContext;
        private DataAssetEditorContent mContentView = new DataAssetEditorContent();
        private DataAssetEditorInfoPanel mInfoPanel = new DataAssetEditorInfoPanel();
        HSplitter mHSplitter;

        public DataAssetEditorUI(string FilePath)
        {
            mContext = new DataAssetEditorContext(FilePath);
            mHSplitter = new HSplitter();
        }

        public void Initialize(string fileName)
        {
            mMainContainer.Initialize();
            mMainContainer.SetSize(1, 1);

            // OperationBar
            {
                OperationBarUI operationBar = new OperationBarUI();
                operationBar.Initialize();

                mSaveBtn.ClickedEvent += sender =>
                {
                    mContext.Save();
                };
                operationBar.AddLeft(mSaveBtn);

                mMainContainer.AddFixedChild(operationBar.GetPanelBar());
            }

            // Data Items
            VContainer ContentContainer = new VContainer();
            {
                ContentContainer.Initialize();
                ContentContainer.SetSize(1, 1);

                mContentView.BindProperty(mContext);
                mContentView.OnAddToParent(ContentContainer);
            }

            // Info Panel
            VContainer InfoContainer = new VContainer();
            {
                InfoContainer.Initialize();
                InfoContainer.SetSize(1, 1);

                mInfoPanel.BindProperty(mContext);
                mInfoPanel.OnAddToParent(InfoContainer);
            }

            mHSplitter.Initialize();
            mHSplitter.AddChild(ContentContainer);
            mHSplitter.AddChild(InfoContainer);
            mMainContainer.AddSizableChild(mHSplitter, 1);

            base.Initialize(fileName, mMainContainer);

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
        }

        protected void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }

            mContentView.OnDragDropManagerDragEnd(Sender, UIManager, MouseX, MouseY, ref bContinue);
        }

        public override void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            mContext.Dispose();
        }
    }
}