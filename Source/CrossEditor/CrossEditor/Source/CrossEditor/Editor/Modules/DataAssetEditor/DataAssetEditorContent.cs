using CEngine;
using Clicross;
using Clicross.resource;
using EditorUI;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Net;
using System.Reflection;
using ClassIDType = CEngine.ClassIDType;

namespace CrossEditor
{
    public class DataAssetEditorContent
    {
        //int _RowHeight = 30;
        Inspector _RootInspector = new Inspector();
        OperationBarUI _OperationBarUI = new OperationBarUI();
        SearchUI _SearchUI = new SearchUI();
        ScrollView _ScrollView = new ScrollView();
        Panel _ScrollPanel;
        private DataAssetEditorContext _mContext;

        public DataAssetEditorContent()
        {
            _OperationBarUI.Initialize();
            _OperationBarUI.GetPanelBar().SetText("Details");
            _OperationBarUI.GetPanelBar().SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI.GetPanelBar().SetFontSize(20);
            _OperationBarUI.GetPanelBar().SetTextOffsetX(10);

            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 275, 25);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);

            _ScrollView.Initialize();
            _ScrollView.SetBackgroundColor(Color.FromRGBA(36, 36, 36, (int)byte.MaxValue));
            _ScrollView.GetHScroll().SetEnable(true);
            _ScrollView.GetVScroll().SetEnable(true);
            _ScrollView.PositionChangedEvent += OnScrollViewChanged;
            _ScrollPanel = _ScrollView.GetScrollPanel();
        }

        public void BindProperty(DataAssetEditorContext context)
        {
            _mContext = context;
            RegenerateDataItem();
        }
        public void OnAddToParent(VContainer mContainer)
        {
            mContainer.AddFixedChild(_OperationBarUI.GetPanelBar());
            mContainer.AddSizableChild(_ScrollView, 1);
            _RootInspector.SetContainer(_ScrollPanel);
        }

        void RegenerateDataItem()
        {
            _RootInspector.ClearChildInspectors();
            _ScrollPanel.ClearChildren();
            for (var it = _mContext.mDataItems.Begin(); it != _mContext.mDataItems.End(); ++ it)
            {
                DataAssetItem assetItem = it.Value;
                string name = assetItem.Name;

                Inspector ins = new Inspector();
                switch (assetItem.Category)
                {
                    case DataAssetItemTypeCategory.Asset:
                        ins = CreateAssetInspector(assetItem, name, assetItem.DefaultValue);
                        break;
                    case DataAssetItemTypeCategory.Struct:
                        ins = CreateStructInspector(assetItem, name);
                        break;
                    case DataAssetItemTypeCategory.Color:
                        ins = CreateColorInspector(assetItem, name);
                        break;
                    default:        //DataAssetItemTypeCategory.BasicType:
                        if (assetItem.Type == DataAssetItemType.Integer)
                        {
                            ins = CreateIntInspector(assetItem, name);
                        }
                        else if (assetItem.Type == DataAssetItemType.String)
                        {
                            ins = CreateStringInspector(assetItem, name);
                        }
                        else if (assetItem.Type == DataAssetItemType.Real)
                        {
                            ins = CreateRealInspector(assetItem, name);
                        }
                        else if (assetItem.Type == DataAssetItemType.Bool)
                        {
                            ins = CreateBoolInspector(assetItem, name);
                        }
                        break;
                }
                _RootInspector.AddChildInspector(ins);
            }
            _SearchUI.TriggerSearchEvent();     //Refresh Inspector UI
        }
        Inspector CreateBoolInspector(Object dataAssetItemObj, string name)
        {
            PropertyInfoAttribute PropertyInfoAttribute = new PropertyInfoAttribute();
            Inspector inspector;
            ObjectProperty ObjectProperty = new ObjectProperty();
            InspectorHandler inspectorHandler = new InspectorHandler();
            ObjectProperty.Object = dataAssetItemObj;
            ObjectProperty.Name = "Value";
            ObjectProperty.DisplayName = name;
            ObjectProperty.Type = name.GetType();
            PropertyInfoAttribute.PropertyType = typeof(bool).ToString();
            BindObjectFunction_Bool(ref ObjectProperty);
            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;

            inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyInfoAttribute.PropertyType, ObjectProperty.Type.IsEnum);
            inspector.InspectProperty(ObjectProperty);
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);
            return inspector;
        }

        Inspector CreateRealInspector(Object dataAssetItemObj, string name)
        {
            PropertyInfoAttribute PropertyInfoAttribute = new PropertyInfoAttribute();
            Inspector inspector;
            ObjectProperty ObjectProperty = new ObjectProperty();
            InspectorHandler inspectorHandler = new InspectorHandler();
            ObjectProperty.Object = dataAssetItemObj;
            ObjectProperty.Name = "Value";
            ObjectProperty.DisplayName = name;
            ObjectProperty.Type = name.GetType();
            PropertyInfoAttribute.PropertyType = typeof(float).ToString();
            BindObjectFunction_Real(ref ObjectProperty);
            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;

            inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyInfoAttribute.PropertyType, ObjectProperty.Type.IsEnum);
            inspector.InspectProperty(ObjectProperty);
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);
            return inspector;
        }

        Inspector CreateStringInspector(Object dataAssetItemObj, string name)
        {
            PropertyInfoAttribute PropertyInfoAttribute = new PropertyInfoAttribute();
            Inspector inspector;
            ObjectProperty ObjectProperty = new ObjectProperty();
            InspectorHandler inspectorHandler = new InspectorHandler();
            ObjectProperty.Object = dataAssetItemObj;
            ObjectProperty.Name = "Value";
            ObjectProperty.DisplayName = name;
            ObjectProperty.Type = name.GetType();
            PropertyInfoAttribute.PropertyType = typeof(string).ToString();
            BindObjectFunction_String(ref ObjectProperty);
            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;

            inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyInfoAttribute.PropertyType, ObjectProperty.Type.IsEnum);
            inspector.InspectProperty(ObjectProperty);
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);
            return inspector;
        }

        Inspector CreateIntInspector(Object dataAssetItemObj, string name)
        {
            PropertyInfoAttribute PropertyInfoAttribute = new PropertyInfoAttribute();
            Inspector inspector;
            ObjectProperty ObjectProperty = new ObjectProperty();
            InspectorHandler inspectorHandler = new InspectorHandler();
            ObjectProperty.Object = dataAssetItemObj;
            ObjectProperty.Name = "Value";
            ObjectProperty.DisplayName = name;
            ObjectProperty.Type = name.GetType();
            PropertyInfoAttribute.PropertyType = typeof(int).ToString();
            BindObjectFunction_Int(ref ObjectProperty);
            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;

            inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyInfoAttribute.PropertyType, ObjectProperty.Type.IsEnum);
            inspector.InspectProperty(ObjectProperty);
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);
            return inspector;
        }

        Inspector CreateAssetInspector(Object dataAssetItemObj, string name, string defaultValue)
        {
            PropertyInfoAttribute PropertyInfoAttribute = new PropertyInfoAttribute();
            Inspector inspector;
            ObjectProperty ObjectProperty = new ObjectProperty();
            InspectorHandler inspectorHandler = new InspectorHandler();
            ObjectProperty.Object = dataAssetItemObj;
            ObjectProperty.Name = "Value";
            ObjectProperty.DisplayName = name;
            ObjectProperty.Type = name.GetType();
            PropertyInfoAttribute.PropertyType = "StringAsResource";
            PropertyInfoAttribute.DefaultValue = defaultValue;

            //Test Data
            /*PropertyInfoAttribute.FileTypeDescriptor = "Material Assets#nda";
            PropertyInfoAttribute.ObjectClassID1 = ClassIDType.CLASS_Material;
            PropertyInfoAttribute.ObjectClassID2 = ClassIDType.CLASS_Fx;
            PropertyInfoAttribute.ObjectClassID3 = ClassIDType.CLASS_InputActionMappingResource;*/

            BindObjectFunction_Asset(ref ObjectProperty);

            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;

            inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyInfoAttribute.PropertyType, ObjectProperty.Type.IsEnum);
            inspector.InspectProperty(ObjectProperty);
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);
            return inspector;
        }

        Inspector CreateStructInspector(DataAssetItem DAItem, string name)
        {
            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = (object)DAItem;
            ObjectProperty.Name = "Value";
            ObjectProperty.DisplayName = name;
            ObjectProperty.Type = name.GetType();

            Inspector_DataAssetExpandItem inspector = new Inspector_DataAssetExpandItem();
            inspector.Initialize();
            inspector.InspectProperty(ObjectProperty);
            for (var it = DAItem.Child.Begin(); it != DAItem.Child.End(); ++ it)
            {
                DataAssetItem childItem = it.Value;
                switch (childItem.Category)
                {
                    case DataAssetItemTypeCategory.Asset:
                        inspector.AddChildInspector(CreateAssetInspector(childItem, childItem.Name, childItem.DefaultValue));
                        break;
                    case DataAssetItemTypeCategory.Color:
                        inspector.AddChildInspector(CreateColorInspector(childItem, childItem.Name));
                        break;
                    case DataAssetItemTypeCategory.Struct:
                        inspector.AddChildInspector(CreateStructInspector(childItem, childItem.Name));
                        break;
                    default:
                        if (childItem.Type == DataAssetItemType.Bool)
                        {
                            inspector.AddChildInspector(CreateBoolInspector(childItem, childItem.Name));
                        }
                        else if (childItem.Type == DataAssetItemType.Integer)
                        {
                            inspector.AddChildInspector(CreateIntInspector(childItem, childItem.Name));
                        }
                        else if (childItem.Type == DataAssetItemType.String)
                        {
                            inspector.AddChildInspector(CreateStringInspector(childItem, childItem.Name));
                        }
                        else if (childItem.Type == DataAssetItemType.Real)
                        {
                            inspector.AddChildInspector(CreateRealInspector(childItem, childItem.Name));
                        }
                        break;
                }
            }

            InspectorHandler inspectorHandler = new InspectorHandler();
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);

            return inspector;
        }

        Inspector CreateColorInspector(Object dataAssetItemObj, string name)
        {
            PropertyInfoAttribute PropertyInfoAttribute = new PropertyInfoAttribute();
            Inspector inspector;
            ObjectProperty ObjectProperty = new ObjectProperty();
            InspectorHandler inspectorHandler = new InspectorHandler();
            ObjectProperty.Object = dataAssetItemObj;
            ObjectProperty.Name = "Child";
            ObjectProperty.DisplayName = name;
            ObjectProperty.Type = name.GetType();
            PropertyInfoAttribute.PropertyType = typeof(Color).ToString();
            BindObjectFunction_Color(ref ObjectProperty);
            ObjectProperty.ReadOnly = PropertyInfoAttribute.bReadOnly;
            ObjectProperty.Advanced = PropertyInfoAttribute.bAdvanced;
            ObjectProperty.ValueMin = Convert.ToDecimal(PropertyInfoAttribute.ValueMin);
            ObjectProperty.ValueMax = Convert.ToDecimal(PropertyInfoAttribute.ValueMax);
            ObjectProperty.ValueStep = Convert.ToDecimal(PropertyInfoAttribute.ValueStep);
            ObjectProperty.DefaultValue = PropertyInfoAttribute.DefaultValue;
            ObjectProperty.KeyFrame = PropertyInfoAttribute.bKeyFrame;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;

            inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyInfoAttribute.PropertyType, ObjectProperty.Type.IsEnum);
            inspector.InspectProperty(ObjectProperty);
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);
            return inspector;
        }
        private void BindObjectFunction_Asset(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    return PropertyInfo.GetValue(Object);
                }
                return null;
            };
            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(Object, PropertyValue);
                }
            };
        }
        private void BindObjectFunction_Bool(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    object res = (string)PropertyInfo.GetValue(Object) == "true";
                    return res;
                }
                return null;
            };
            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    if ((bool)PropertyValue)
                    {
                        PropertyInfo.SetValue(Object, "true");
                    }
                    else
                    {
                        PropertyInfo.SetValue(Object, "false");
                    }
                }
            };
        }

        private void BindObjectFunction_Int(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    int.TryParse((string)PropertyInfo.GetValue(Object), out int res);
                    return res;
                }
                return null;
            };
            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    if (double.TryParse((string)PropertyValue, out double numericValue))
                    {
                        int result = (int)Math.Truncate(numericValue);
                        PropertyInfo.SetValue(Object, result.ToString());
                    }
                }
            };
        }

        private void BindObjectFunction_Real(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    float.TryParse((string)PropertyInfo.GetValue(Object), out float res);
                    return res;
                }
                return null;
            };
            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(Object, PropertyValue.ToString());
                }
            };
        }

        private void BindObjectFunction_String(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    return PropertyInfo.GetValue(Object);
                }
                return null;
            };
            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                Type Type = Object.GetType();
                PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(Object, PropertyValue);
                }
            };
        }

        private void BindObjectFunction_Color(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
            {
                DataAssetItem originItem = (DataAssetItem)Object;
                float R = 1f, G = 1f, B = 1f, A = 1f;
                for (var it = originItem.Child.Begin(); it != originItem.Child.End(); ++it)
                {
                    DataAssetItem color = it.Value;
                    if (color.Name == "r")
                    {
                        float.TryParse(color.Value, out R);
                    }
                    else if (color.Name == "g")
                    {
                        float.TryParse(color.Value, out G);
                    }
                    else if (color.Name == "b")
                    {
                        float.TryParse(color.Value, out B);
                    }
                    else if (color.Name == "a")
                    {
                        float.TryParse(color.Value, out A);
                    }
                }
                return new Color(R, G, B, A);
            };
            ObjectProperty.SetPropertyValueFunction = (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) =>
            {
                DataAssetItem originItem = (DataAssetItem)Object;
                Color newColor = (Color)PropertyValue;
                for (var it = originItem.Child.Begin(); it != originItem.Child.End(); ++it)
                {
                    DataAssetItem color = it.Value;
                    if (color.Name == "r")
                    {
                        //float.TryParse(color.Value, out R);
                        color.Value = (newColor.R).ToString();
                    }
                    else if (color.Name == "g")
                    {
                        color.Value = (newColor.G).ToString();
                    }
                    else if (color.Name == "b")
                    {
                        color.Value = (newColor.B).ToString();
                    }
                    else if (color.Name == "a")
                    {
                        color.Value = (newColor.A).ToString();
                    }
                }
            };
        }

        private void RefreshInspectorUI()
        {
            int ScrollPanelWidth = _ScrollPanel.GetWidth();
            int Y = 0;
            _RootInspector.UpdateLayout(ScrollPanelWidth, ref Y);
            _ScrollPanel.SetHeight(Math.Max(_ScrollPanel.GetHeight(), Y + 50));
            _ScrollView.UpdateScrollBar();
        }
        public void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            if (_RootInspector != null)
            {
                List<Inspector> SearchResult = new List<Inspector>();
                _RootInspector.ForEach((Inspector) =>
                {
                    if (Inspector == _RootInspector)
                    {
                        return;
                    }
                    // Traverse all controls
                    Queue<Control> Controls = new Queue<Control>();
                    Controls.Enqueue(Inspector.GetSelfContainer());
                    while (Controls.Count > 0)
                    {
                        Control Control = Controls.Dequeue();
                        for (int i = 0; i < Control.GetChildCount(); ++i)
                        {
                            Controls.Enqueue(Control.GetChild(i));
                        }
                        // Try to match label text
                        if (Control is Label)
                        {
                            Label Label = Control as Label;
                            if (StringHelper.IgnoreCaseContains(Label.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                        // Try to match edit text
                        else if (Control is Edit)
                        {
                            Edit Edit = Control as Edit;
                            if (StringHelper.IgnoreCaseContains(Edit.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                    }
                });
                // Hide all inspectors
                _RootInspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(false);
                });
                // Show search result
                foreach (Inspector Result in SearchResult)
                {
                    Inspector This = Result;
                    while (This != null)
                    {
                        This.SetVisible(true);
                        This = This.GetParentInspector();
                    }

                    Result.ForEach((Inspector) =>
                    {
                        Inspector.SetVisible(true);
                    });
                }
                if (SearchResult.Count == 0)
                {
                    _RootInspector.SetVisible(true);
                }
                RefreshInspectorUI();
            }
        }

        public void OnSearchUICancel(SearchUI Sender)
        {
            if (_RootInspector != null)
            {
                _RootInspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(true);
                });

                RefreshInspectorUI();
            }
        }
        
        public void OnScrollViewChanged(Control sender, bool poschange, bool sizechange)
        {
            _SearchUI.TriggerSearchEvent();
        }
        public void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_ScrollView.GetVisible_Recursively() && _ScrollView.IsPointIn_Recursively(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (ProjectUI.IsPathesDragging())
                {
                    List<string> PathesDragged = ProjectUI.GetPathesDragged();
                    DropPathOnInspector(_RootInspector, MouseX, MouseY, PathesDragged);
                }
            }
        }
        bool DropPathOnInspector(Inspector Inspector, int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (Inspector == null)
            {
                return false;
            }
            List<Inspector> ChildInspectors = Inspector.GetChildInspectors();
            foreach (Inspector ChildInspector in ChildInspectors)
            {
                if (DropPathOnInspector(ChildInspector, MouseX, MouseY, PathesDragged))
                {
                    return true;
                }
            }
            return Inspector.OnDropPathes(MouseX, MouseY, PathesDragged);
        }
    }
}