using CEngine;
namespace CrossEditor
{
    class SculptTerrainConfigs
    {
        static SculptTerrainConfigs _Instance = new SculptTerrainConfigs();

        public static SculptTerrainConfigs GetInstance()
        {
            return _Instance;
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Terrain brush radius")]
        public float Radius
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationRadius;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationRadius = value;
            }
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Terrain brush strength")]
        public float Strength
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationStrength;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationStrength = value;
            }
        }

        [PropertyInfo(PropertyType = "EnumWithTab", ToolTips = "Terrain falloff type")]
        public TerrainFalloffType TerrainFalloffType
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainFalloffType;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainFalloffType = value;
            }
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Terrain brush falloff")]
        public float Falloff
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationFalloff;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationFalloff = value;
            }
        }
    }
}
