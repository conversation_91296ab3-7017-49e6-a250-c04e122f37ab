using EditorUI;
using System;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_Property_EnumWithTab : Inspector_Property
    {
        Panel _ButtonContainer;
        Button _SelectButton;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _ButtonContainer = new Panel();
            _ButtonContainer.Initialize();
            Container.AddChild(_ButtonContainer);

            Type Type = _ObjectProperty.Type;
            string[] EnumNames = Type.GetEnumNames();
            foreach (string EnumName in EnumNames)
            {
                FieldInfo FieldInfo = Type.GetField(EnumName);
                TerrainEnumInfoAttribute TerrainEnumInfo = TerrainEnumInfoAttribute.GetTerrainEnumInfo(FieldInfo);

                Button EnumButton = new Button();
                EnumButton.Initialize();
                EnumButton.SetSize(30, 30);
                EnumButton.SetTagString1(EnumName);
                EnumButton.SetToolTips(TerrainEnumInfo.DisplayName);
                EnumButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                Texture Icon = UIManager.LoadUIImage(TerrainEnumInfo.TexturePath);
                EnumButton.SetImage(Icon);
                EnumButton.ClickedEvent += OnEnumButtonClicked;

                _ButtonContainer.AddChild(EnumButton);
            }

            ReadValue();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int SPAN_X = 2;

            int Height = 30 + 2 * SPAN_Y;
            Y += Height - DEFAULT_HEIGHT;

            _Separator.SetY(Y - 1);

            _SelfContainer.SetHeight(Height);
            _Splitter.UpdateLayout(_Splitter.GetWidth(), Height);
            if (_ButtonRevert != null)
            {
                _ButtonRevert.SetY((Height - DEFAULT_HEIGHT) / 2);
            }

            _LabelName.SetY((Height - DEFAULT_HEIGHT) / 2);
            if (_ButtonToolTips != null)
            {
                _ButtonToolTips.SetY((Height - DEFAULT_HEIGHT) / 2);
            }

            int ButtonContainerWidth = _ButtonContainer.GetChildCount() * (30 + SPAN_X);
            _ButtonContainer.SetPosition(0, SPAN_Y, ButtonContainerWidth, 30);
            GetValueContainer().FloatToLeft(_ButtonContainer);
            for (int i = 0; i < _ButtonContainer.GetChildCount(); i++)
            {
                _ButtonContainer.GetChild(i).SetPos(i * (30 + SPAN_X), 0);
            }
        }

        private void OnEnumButtonClicked(Button Sender)
        {
            for (int i = 0; i < _ButtonContainer.GetChildCount(); i++)
            {
                Button EnumButton = _ButtonContainer.GetChild(i) as Button;

                if (EnumButton == Sender)
                {
                    EnumButton.SetBorderChecked(true);
                    EnumButton.SetNormalColor(Color.EDITOR_UI_ACTIVE_TOOL_OR_MENU);
                    _SelectButton = EnumButton;
                }
                else
                {
                    EnumButton.SetBorderChecked(false);
                    EnumButton.SetNormalColor(Color.FromRGBA(0, 0, 0, 0));
                }
            }

            RecordAndWriteValue();
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = PropertyValue.ToString();
            Button EnumButton = _ButtonContainer.SearchChildByTagString1(PropertyValueString) as Button;
            OnEnumButtonClicked(EnumButton);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            Type Type = _ObjectProperty.Type;
            string ValueString = _SelectButton.GetTagString1();
            object NewValue = Enum.Parse(Type, ValueString);
            SetPropertyValue(NewValue);
        }
    }
}
