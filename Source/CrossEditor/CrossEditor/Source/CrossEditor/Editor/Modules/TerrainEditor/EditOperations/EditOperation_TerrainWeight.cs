using CEngine;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    unsafe class TerrainWeightData
    {
        public byte* Pointer;
        public byte OldHeight;
        public byte NewHeight;
    }

    class EditOperation_TerrainWeight : EditOperation_TerrainBase
    {
        public Dictionary<IntPtr, TerrainWeightData> _TerrainWeightDataList;

        public EditOperation_TerrainWeight()
        {
            _TerrainWeightDataList = new Dictionary<IntPtr, TerrainWeightData>();
        }

        public unsafe void AddTerrainWeightData(IntPtr Pointer)
        {
            if (_TerrainWeightDataList.ContainsKey(Pointer) == false)
            {
                byte Weight = *(byte*)Pointer;
                TerrainWeightData TerrainWeightData = new TerrainWeightData();
                TerrainWeightData.Pointer = (byte*)Pointer;
                TerrainWeightData.OldHeight = Weight;
                TerrainWeightData.NewHeight = Weight;
                _TerrainWeightDataList[Pointer] = TerrainWeightData;
            }
        }

        public unsafe void UpdateTerrainWeightData(IntPtr Pointer)
        {
            byte Weight = *(byte*)Pointer;
            TerrainWeightData TerrainWeightData = _TerrainWeightDataList[Pointer];
            TerrainWeightData.NewHeight = Weight;
        }

        public override unsafe void Undo()
        {
            foreach (var Pair in _TerrainWeightDataList)
            {
                TerrainWeightData TerrainWeightData = Pair.Value;
                (*TerrainWeightData.Pointer) = TerrainWeightData.OldHeight;
            }
            MarkTilesDirty(TileDirtyType.WeightTexture);
        }

        public override unsafe void Redo()
        {
            foreach (var Pair in _TerrainWeightDataList)
            {
                TerrainWeightData TerrainWeightData = Pair.Value;
                (*TerrainWeightData.Pointer) = TerrainWeightData.NewHeight;
            }
            MarkTilesDirty(TileDirtyType.WeightTexture);
        }
    }
}
