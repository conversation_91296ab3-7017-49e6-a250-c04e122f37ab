using CEngine;
namespace CrossEditor
{
    public enum SurfaceType
    {
        Flat,
        Spherical,
        WGS84,
    }

    public class CreateTerrainConfigs
    {
        static CreateTerrainConfigs _Instance = new CreateTerrainConfigs();

        int _GridSizeX;
        int _GridSizeY;
        TerrainBlockSizes _BlockSize;
        TerrainTileSizes _TileSize;

        public static CreateTerrainConfigs GetInstance()
        {
            return _Instance;
        }

        public CreateTerrainConfigs()
        {
            _GridSizeX = 1;
            _GridSizeY = 1;
            _BlockSize = TerrainBlockSizes.__2;
            _TileSize = TerrainTileSizes.__256;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Grid count in X.")]
        public int GridSizeX
        {
            get
            {
                return _GridSizeX;
            }
            set
            {
                _GridSizeX = value;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Grid count in Y.")]
        public int GridSizeY
        {
            get
            {
                return _GridSizeY;
            }
            set
            {
                _GridSizeY = value;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Block count per grid in X/Y.")]
        public TerrainBlockSizes BlockSize
        {
            get
            {
                return _BlockSize;
            }
            set
            {
                _BlockSize = value;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Tile count per block in X/Y.")]
        public TerrainTileSizes TileSize
        {
            get
            {
                return _TileSize;
            }
            set
            {
                _TileSize = value;
            }
        }
    }
}
