using EditorUI;
using System;
using System.Reflection;

namespace CrossEditor
{
    [AttributeUsage(AttributeTargets.Field)]
    public class TerrainEnumInfoAttribute : System.Attribute
    {
        public string DisplayName;
        public string TexturePath;

        public TerrainEnumInfoAttribute()
        {
            DisplayName = "";
            TexturePath = "";
        }

        public static TerrainEnumInfoAttribute GetTerrainEnumInfo(FieldInfo FieldInfo)
        {
            TerrainEnumInfoAttribute TerrainEnumInfo = new TerrainEnumInfoAttribute();
            AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(FieldInfo);
            if (AttributeList != null)
            {
                AttributeData AttributeData = AttributeList.GetAttributeData("TerrainEnumInfo");
                if (AttributeData != null)
                {
                    string DisplayName = AttributeData.GetNamedAttribute("DisplayName") as string;
                    if (DisplayName != null && DisplayName != "")
                    {
                        TerrainEnumInfo.DisplayName = DisplayName;
                    }

                    string TexturePath = AttributeData.GetNamedAttribute("TexturePath") as String;
                    if (TexturePath != null && TexturePath != "")
                    {
                        TerrainEnumInfo.TexturePath = TexturePath;
                    }
                }
            }
            return TerrainEnumInfo;
        }
    }
}
