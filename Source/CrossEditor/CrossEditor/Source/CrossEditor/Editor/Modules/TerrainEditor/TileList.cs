using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    class TileList
    {
        public List<TileIndex> _TileList;

        public TileList()
        {
            _TileList = new List<TileIndex>();
        }

        static bool TileIndexEqual(TileIndex TileIndex1, TileIndex TileIndex2)
        {
            if (TileIndex1.mBlockX == TileIndex2.mBlockX &&
                TileIndex1.mBlockY == TileIndex2.mBlockY &&
                TileIndex1.mLevel == TileIndex2.mLevel &&
                TileIndex1.mTileX == TileIndex2.mTileX &&
                TileIndex1.mTileY == TileIndex2.mTileY)
            {
                return true;
            }
            return false;
        }

        public int FindTileIndex(TileIndex TileIndex)
        {
            int Count = _TileList.Count;
            for (int i = 0; i < Count; i++)
            {
                TileIndex TileIndex1 = _TileList[i];
                if (TileIndexEqual(TileIndex1, TileIndex))
                {
                    return i;
                }
            }
            return -1;
        }

        public void AddTileIndex(TileIndex TileIndex)
        {
            if (FindTileIndex(TileIndex) == -1)
            {
                _TileList.Add(TileIndex);
            }
        }

        public void ClearTiles()
        {
            _TileList.Clear();
        }
    }
}
