using EditorUI;

namespace CrossEditor
{
    class Inspector_TerrainConfigs : Inspector_Struct_With_Property
    {
        const int BUTTON_FONT_SIZE = 18;
        const int BUTTON_HEIGHT = 30;
        Button _ButtonCreate;

        public override void InspectObject(object Object, object Tag = null)
        {
            base.InspectObject(Object, Tag);

            _ButtonCreate = new Button();
            _ButtonCreate.Initialize();
            _ButtonCreate.SetText("Create");
            _ButtonCreate.SetBorderColor(Color.FromRGBA(81, 82, 84, 255));
            _ButtonCreate.SetFontSize(BUTTON_FONT_SIZE);
            _ButtonCreate.SetTextOffsetY(2);
            _ButtonCreate.ClickedEvent += OnButtonCreateClicked;
            _ChildContainer.AddChild(_ButtonCreate);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int Y1 = _ChildContainer.GetHeight();
            int AvailableWidth = Width - 2 * SPAN_X;

            if (_Object is CreateTerrainConfigs ||
                _Object is ImportTerrainConfigs)
            {
                _ButtonCreate.SetVisible(true);
                _ButtonCreate.SetPosition(SPAN_X, Y1 + 5, AvailableWidth, BUTTON_HEIGHT);
                Y1 += BUTTON_HEIGHT + 5;
                if (_Object is ImportTerrainConfigs)
                {
                    _ButtonCreate.SetText("Import");
                }
                else
                {
                    _ButtonCreate.SetText("Create");
                }
            }
            else
            {
                _ButtonCreate.SetVisible(false);
            }

            _ChildContainer.SetHeight(Y1);
            Y += 2 * BUTTON_HEIGHT + 3 * 5;
        }

        public void OnButtonCreateClicked(Button Sender)
        {
            if (_Object is CreateTerrainConfigs)
            {
                TerrainEditorUI.GetInstance().CreateTerrain();
            }
            else if (_Object is ImportTerrainConfigs)
            {
                TerrainEditorUI.GetInstance().ImportTerrain();
            }
        }
    }
}