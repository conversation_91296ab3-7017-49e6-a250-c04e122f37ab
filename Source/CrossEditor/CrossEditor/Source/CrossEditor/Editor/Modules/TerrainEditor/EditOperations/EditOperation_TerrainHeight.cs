using CEngine;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    unsafe class TerrainHeightData
    {
        public float* Pointer;
        public float OldHeight;
        public float NewHeight;
    }

    class EditOperation_TerrainHeight : EditOperation_TerrainBase
    {
        public Dictionary<IntPtr, TerrainHeightData> _TerrainHeightDataList;

        public EditOperation_TerrainHeight()
        {
            _TerrainHeightDataList = new Dictionary<IntPtr, TerrainHeightData>();
        }

        public unsafe void AddTerrainHeightData(IntPtr Pointer)
        {
            if (_TerrainHeightDataList.ContainsKey(Pointer) == false)
            {
                float Height = *(float*)Pointer;
                TerrainHeightData TerrainHeightData = new TerrainHeightData();
                TerrainHeightData.Pointer = (float*)Pointer;
                TerrainHeightData.OldHeight = Height;
                TerrainHeightData.NewHeight = Height;
                _TerrainHeightDataList[Pointer] = TerrainHeightData;
            }
        }

        public unsafe void UpdateTerrainHeightData(IntPtr Pointer)
        {
            float Height = *(float*)Pointer;
            TerrainHeightData TerrainHeightData = _TerrainHeightDataList[Pointer];
            TerrainHeightData.NewHeight = Height;
        }

        public override unsafe void Undo()
        {
            foreach (var Pair in _TerrainHeightDataList)
            {
                TerrainHeightData TerrainHeightData = Pair.Value;
                (*TerrainHeightData.Pointer) = TerrainHeightData.OldHeight;
            }
            MarkTilesDirty(TileDirtyType.Heightmap);
        }

        public override unsafe void Redo()
        {
            foreach (var Pair in _TerrainHeightDataList)
            {
                TerrainHeightData TerrainHeightData = Pair.Value;
                (*TerrainHeightData.Pointer) = TerrainHeightData.NewHeight;
            }
            MarkTilesDirty(TileDirtyType.Heightmap);
        }
    }
}
