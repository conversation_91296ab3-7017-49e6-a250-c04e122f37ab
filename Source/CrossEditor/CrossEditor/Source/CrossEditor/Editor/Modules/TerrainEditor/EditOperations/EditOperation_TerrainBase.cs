using CEngine;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class EditOperation_TerrainBase : EditOperation
    {
        public Entity _TerrainEntity;
        public List<TileIndex> _TerrainTiles;

        public EditOperation_TerrainBase()
        {
            _TerrainEntity = null;
            _TerrainTiles = new List<TileIndex>();
        }

        public void AddTerrainTile(TileIndex TileIndex)
        {
            bool bFound = false;
            foreach (TileIndex TileIndex1 in _TerrainTiles)
            {
                if (TileIndex.mBlockX == TileIndex1.mBlockX &&
                    TileIndex.mBlockY == TileIndex1.mBlockY &&
                    TileIndex.mLevel == TileIndex1.mLevel &&
                    TileIndex.mTileX == TileIndex1.mTileX &&
                    TileIndex.mTileY == TileIndex1.mTileY)
                {
                    bFound = true;
                    break;
                }
            }
            if (bFound == false)
            {
                _TerrainTiles.Add(TileIndex);
            }
        }

        protected void MarkTilesDirty(TileDirtyType TileDirtyType)
        {
            EditorScene EditorScene = EditorScene.GetInstance();
            IntPtr WorldPtr = EditorScene.GetWorld().GetNativePointer();
            ulong TerrainEntityID = _TerrainEntity.EntityID;
            foreach (TileIndex TileIndex in _TerrainTiles)
            {
                TerrainSystemG.MarkTileDirty(WorldPtr, TerrainEntityID, TileIndex, TileDirtyType);
            }
        }
    }
}
