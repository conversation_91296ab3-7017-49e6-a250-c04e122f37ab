using EditorUI;

namespace CrossEditor
{
    class Inspector_Property_SelectionList : Inspector_Property_List
    {
        static Color SelectedColor = Color.FromRGBA(50, 50, 50, 255);
        int _SelectIndex;

        public Inspector_Property_SelectionList()
        {
            _SelectIndex = 0;
        }

        public int GetSelectIndex()
        {
            return _SelectIndex;
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);

            if (_ChildInspectors.Count > 0)
            {
                bool bContinue = false;
                OnSelfContainerLeftMouseDown(_ChildInspectors[_SelectIndex].GetSelfContainer(), 0, 0, ref bContinue);
            }
        }

        protected override void AddChildInspectors()
        {
            base.AddChildInspectors();

            foreach (Inspector Child in _ChildInspectors)
            {
                Child.GetSelfContainer().SetTagObject(this);
                Child.GetSelfContainer().LeftMouseDownEvent += OnSelfContainerLeftMouseDown;
                Child.GetChildContainer().SetTagObject(this);
                Child.GetChildContainer().LeftMouseDownEvent += OnChildContainerLeftMouseDown;
            }
        }

        private void SetInspectorChecked(Inspector_Property Inspector, bool bCheck)
        {
            Inspector.GetNameContainer().SetBackgroundColor(bCheck ? SelectedColor : Color.EDITOR_UI_GENERAL_BACK_COLOR);
            Inspector.GetValueContainer().SetBackgroundColor(bCheck ? SelectedColor : Color.EDITOR_UI_GENERAL_BACK_COLOR);
            foreach (Inspector Child in Inspector.GetChildInspectors())
            {
                Inspector_Property ChildProperty = Child as Inspector_Property;
                if (ChildProperty != null)
                {
                    SetInspectorChecked(ChildProperty, bCheck);
                }
            }
        }

        private void OnSelfContainerLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            int Index = 0;
            foreach (Inspector Child in _ChildInspectors)
            {
                Inspector_Property ChildProperty = Child as Inspector_Property;
                if (ChildProperty == null)
                {
                    continue;
                }
                Panel SelfContainer = Child.GetSelfContainer();
                if (Sender == SelfContainer)
                {
                    _SelectIndex = Index;
                    SetInspectorChecked(ChildProperty, true);
                }
                else
                {
                    SetInspectorChecked(ChildProperty, false);
                }

                Index++;
            }
        }

        private void OnChildContainerLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            int Index = 0;
            foreach (Inspector Child in _ChildInspectors)
            {
                Inspector_Property ChildProperty = Child as Inspector_Property;
                if (ChildProperty == null)
                {
                    continue;
                }
                Panel ChildContainer = Child.GetChildContainer();
                if (Sender == ChildContainer)
                {
                    _SelectIndex = Index;
                    SetInspectorChecked(ChildProperty, true);
                }
                else
                {
                    SetInspectorChecked(ChildProperty, false);
                }

                Index++;
            }
        }

        public static void ChildControlHandleLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Control Parent = Sender.GetParent();
            while (Parent != null)
            {
                object TagObject = Parent.GetTagObject();
                if (TagObject is Inspector_Property_SelectionList)
                {
                    break;
                }
                Parent = Parent.GetParent();
            }
            if (Parent != null)
            {
                Parent.TriggerLeftMouseDownEvent(MouseX, MouseY, ref bContinue);
            }
        }
    }
}
