using CEngine;
namespace CrossEditor
{
    class DigTerrainConfig
    {
        static DigTerrainConfig _Instance = new DigTerrainConfig();

        public static DigTerrainConfig GetInstance()
        {
            return _Instance;
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Terrain brush radius")]
        public float Radius
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationRadius;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationRadius = value;
            }
        }
    }
}
