using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class TerrainEditor
    {
        const float HoleHeight = -512.0f;
        public const float RadiusScale = 300.0f;

        static TerrainEditor _Instance = new TerrainEditor();

        public static TerrainEditor GetInstance()
        {
            return _Instance;
        }

        bool _bEnableTerrainOperation;
        EditOperation_TerrainHeight _EditOperation_TerrainHeight;
        EditOperation_TerrainWeight _EditOperation_TerrainWeight;

        public TerrainOperationType _TerrainOperationType;
        public float _TerrainOperationRadius;
        public float _TerrainOperationStrength;
        public bool _TerrainOperationEnableTargetWeight;
        public float _TerrainOperationTargetWeightValue;
        public TerrainFalloffType _TerrainFalloffType;
        public float _TerrainOperationFalloff;
        public float _TerrainOperationKernelSize;
        public int _TerrainOperationLayerIndex;

        Entity _LastTerrainEntity;
        public List<TerrainLayer> _TerrainLayers;

        bool _bTerrainDirty;
        TileList _DirtyTileList_Height;
        TileList _DirtyTileList_Weight;

        Random _Random;

        List<IntPtr> _WeightDataList;

        List<TerrainHeight> _TerrainHeightList;
        Dictionary<long, TerrainHeight> _TerrainHeightMap;
        List<TileIndex> _DirtyTileList;

        TerrainEditor()
        {
            _bEnableTerrainOperation = false;
            _TerrainOperationType = TerrainOperationType.CreateTerrain;
            _TerrainOperationRadius = 0.2f;
            _TerrainOperationStrength = 0.25f;
            _TerrainOperationEnableTargetWeight = false;
            _TerrainOperationTargetWeightValue = 0.5f;
            _TerrainFalloffType = TerrainFalloffType.Smooth;
            _TerrainOperationFalloff = 0.5f;
            _TerrainOperationKernelSize = 0.2f;
            _TerrainOperationLayerIndex = 0;
            _TerrainLayers = new List<TerrainLayer>();
            _bTerrainDirty = false;
            _DirtyTileList_Height = new TileList();
            _DirtyTileList_Weight = new TileList();
            _Random = new Random();
            _WeightDataList = new List<IntPtr>();
            _TerrainHeightList = new List<TerrainHeight>();
            _TerrainHeightMap = new Dictionary<long, TerrainHeight>();
            _DirtyTileList = new List<TileIndex>();
        }

        public static bool GetTileBoundingBox(IntPtr WorldPtr, ulong TerrainEntityID, TileIndex TileIndex, out Double3 OutCenter, out Double3 OutExtent)
        {
            Float3 OutCenterF = new Float3();
            Float3 OutExtentF = new Float3();
            bool bReturn = TerrainSystemG.GetTileBoundingBox(WorldPtr, TerrainEntityID, TileIndex, OutCenterF, OutExtentF);
            OutCenter = new Double3(OutCenterF);
            OutExtent = new Double3(OutExtentF);
            return bReturn;
        }

        public void SetTerrainDirty()
        {
            EditorScene.GetInstance().SetDirty();
            _bTerrainDirty = true;
        }

        public void ResetTerrainDirty()
        {
            _bTerrainDirty = false;
            _DirtyTileList_Height.ClearTiles();
            _DirtyTileList_Weight.ClearTiles();
        }

        public bool GetTerrainDirty()
        {
            return _bTerrainDirty;
        }

        public void Update(float TimeElapsedS)
        {
            UpdatePaintSphereMaterial();
            DoTerrainOperation(TimeElapsedS);
        }

        void UpdatePaintSphereMaterial()
        {
            Device Device = UIManager.GetActiveUIManager().GetDevice();
            if (Device.IsShiftDown())
            {
                EditorSceneUI.GetInstance().SetPaintSphereMaterial(false);
            }
            else
            {
                EditorSceneUI.GetInstance().SetPaintSphereMaterial(true);
            }
        }

        public void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            BeginTerrainOperation();
        }

        public void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
        }

        public void OnPanelLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            EndTerrainOperation();
        }

        void ClearDirectory(string Directory)
        {
            if (System.IO.Directory.Exists(Directory))
            {
                System.IO.Directory.Delete(Directory, true);
            }
            System.IO.Directory.CreateDirectory(Directory);
        }

        unsafe public void SaveTerrain()
        {
            if (GetTerrainDirty())
            {
                _bTerrainDirty = false;

                Entity TerrainEntity = SearchTerrainEntity();
                if (TerrainEntity == null)
                {
                    return;
                }
                ulong TerrainEntityID = TerrainEntity.EntityID;

                TerrainComponent TerrainComponent = TerrainEntity.GetTerrainComponent();
                if (TerrainComponent == null)
                {
                    return;
                }

                string TerrainPath = GetTerrainPath(TerrainComponent);
                string UpdateHMDirectory = GetUpdateDirectory(TerrainPath) + "/UpdateHM";
                string UpdateWTDirectory = GetUpdateDirectory(TerrainPath) + "/UpdateWT";

                ClearDirectory(UpdateHMDirectory);
                ClearDirectory(UpdateWTDirectory);

                EditorScene EditorScene = EditorScene.GetInstance();
                IntPtr WorldPtr = EditorScene.GetWorld().GetNativePointer();
                TerrainInfo TerrainInfo = TerrainSystemG.GetTerrainInfo(WorldPtr, TerrainEntityID);

                List<TileIndex> DirtyTileList_Height = _DirtyTileList_Height._TileList;
                foreach (TileIndex TileIndex in DirtyTileList_Height)
                {
                    TerrainSystemG.SaveTileHeightmapDataAsPng(WorldPtr, TerrainEntityID, TileIndex, UpdateHMDirectory);
                }

                List<TileIndex> DirtyTileList_Weight = _DirtyTileList_Weight._TileList;
                foreach (TileIndex TileIndex in DirtyTileList_Weight)
                {
                    TerrainSystemG.SaveTileWeightTextureAsPng(WorldPtr, TerrainEntityID, TileIndex, UpdateWTDirectory);
                }

                if (DirtyTileList_Height.Count > 0)
                {
                    CEAssetPipeline.UpdateTerrainHeightmap(TerrainPath, UpdateHMDirectory);
                }
                if (DirtyTileList_Weight.Count > 0)
                {
                    CEAssetPipeline.UpdateTerrainWeightTexture(TerrainPath, UpdateWTDirectory);
                }
                TerrainSystemG.SaveLayers(WorldPtr, TerrainEntityID);

                DirectoryHelper.DeleteDirectory(UpdateHMDirectory);
                DirectoryHelper.DeleteDirectory(UpdateWTDirectory);

                ResetTerrainDirty();
            }
        }

        double CalculateFalloff_Smooth(double Distance, double Radius, double Falloff)
        {
            double Y = CalculateFalloff_Linear(Distance, Radius, Falloff);
            return Y * Y * (3.0 - 2.0 * Y);
        }

        double CalculateFalloff_Linear(double Distance, double Radius, double Falloff)
        {
            if (Distance < Radius)
            {
                return 1.0;
            }
            if (Falloff > 0.0)
            {
                return Math.Max(0.0, 1.0 - (Distance - Radius) / Falloff);
            }
            return 0.0;
        }

        double CalculateFalloff_Spherical(double Distance, double Radius, double Falloff)
        {
            if (Distance < Radius)
            {
                return 1.0;
            }
            if (Distance > Radius + Falloff)
            {
                return 0.0;
            }
            double X = (Distance - Radius) / Falloff;
            return Math.Sqrt(1.0 - X * X);
        }

        double CalculateFalloff_Tip(double Distance, double Radius, double Falloff)
        {
            if (Distance < Radius)
            {
                return 1.0;
            }
            if (Distance > Radius + Falloff)
            {
                return 0.0;
            }
            double X = (Falloff + Radius - Distance) / Falloff;
            return 1.0 - Math.Sqrt(1.0f - X * X);
        }

        double CalculateFalloff(TerrainFalloffType TerrainFalloffType, double Distance, double Radius, double Falloff)
        {
            switch (TerrainFalloffType)
            {
                case TerrainFalloffType.Smooth:
                    return CalculateFalloff_Smooth(Distance, Radius, Falloff);
                case TerrainFalloffType.Linear:
                    return CalculateFalloff_Linear(Distance, Radius, Falloff);
                case TerrainFalloffType.Spherical:
                    return CalculateFalloff_Spherical(Distance, Radius, Falloff);
                case TerrainFalloffType.Tip:
                    return CalculateFalloff_Tip(Distance, Radius, Falloff);
                default:
                    return 0.0f;
            }
        }

        public Entity SearchTerrainEntity()
        {
            EditorScene EditorScene = EditorScene.GetInstance();
            Entity Root = EditorScene.GetRoot();
            if (Root == null)
            {
                return null;
            }
            return Root.SearchChildByComponentType(typeof(TerrainComponent));
        }

        string GetTerrainPath(TerrainComponent TerrainComponent)
        {
            string TerrainPath_GUID = TerrainComponent.TerrainPath;
            string TerrainPath = ResourceManager.Instance().ConvertGuidToPath(TerrainPath_GUID);
            return TerrainPath;
        }

        string GetUpdateDirectory(string TerrainPath)
        {
            string TerrainPathAbs = EditorUtilities.StandardFilenameToEditorFilename(TerrainPath);
            string UpdateDirectory = PathHelper.GetDirectoryName(TerrainPathAbs);
            return UpdateDirectory;
        }

        public void FetchTerrainLayers()
        {
            Entity TerrainEntity = SearchTerrainEntity();
            if (TerrainEntity != _LastTerrainEntity)
            {
                _TerrainLayers = new List<TerrainLayer>();
                if (TerrainEntity != null)
                {
                    TerrainComponent TerrainComponent = TerrainEntity.GetTerrainComponent();
                    if (TerrainComponent != null && TerrainComponent.TerrainPath != "")
                    {
                        EditorScene EditorScene = EditorScene.GetInstance();
                        IntPtr WorldPtr = EditorScene.GetWorld().GetNativePointer();
                        ulong TerrainEntityID = TerrainEntity.EntityID;
                        vector_cross_TerrainLayerTexture TerrainLayerTextures = TerrainSystemG.GetBlendLayers(WorldPtr, TerrainEntityID);
                        int TerrainLayerTextureCount = TerrainLayerTextures.Count;
                        for (int i1 = 0; i1 < TerrainLayerTextureCount; i1++)
                        {
                            TerrainLayerTexture TerrainLayerTexture = TerrainLayerTextures[i1];
                            TerrainLayer TerrainLayer = new TerrainLayer();
                            TerrainLayer._BaseColorTexture = TerrainLayerTexture.mBaseColorTexture;
                            TerrainLayer._NormalTexture = TerrainLayerTexture.mNormalTexture;
                            TerrainLayer._HMRATexture = TerrainLayerTexture.mHMRATexture;
                            _TerrainLayers.Add(TerrainLayer);
                        }
                        if (TerrainLayerTextureCount > 0)
                        {
                            TerrainInfo TerrainInfo = TerrainSystemG.GetTerrainInfo(WorldPtr, TerrainEntityID);
                            if (TerrainInfo.mSurfaceType == TerrainSurfaceType.Flat)
                            {
                                TraverseTerrainTiles(TerrainComponent);
                            }
                        }
                        _LastTerrainEntity = TerrainEntity;
                    }
                }
                else
                {
                    _LastTerrainEntity = TerrainEntity;
                }
            }
        }

        public void UpdateTerrainLayers()
        {
            if (_LastTerrainEntity == null)
            {
                return;
            }
            EditorScene EditorScene = EditorScene.GetInstance();
            IntPtr WorldPtr = EditorScene.GetWorld().GetNativePointer();
            ulong TerrainEntityID = _LastTerrainEntity.EntityID;

            vector_cross_TerrainLayerTexture TerrainLayerTextures = new vector_cross_TerrainLayerTexture();
            int TerrainLayerCount = _TerrainLayers.Count;
            TerrainLayerCount = Math.Min(TerrainLayerCount, TerrainEditorUI.MaxLayersSupported);
            for (int i1 = 0; i1 < TerrainLayerCount; i1++)
            {
                TerrainLayer TerrainLayer = _TerrainLayers[i1];
                TerrainLayerTexture TerrainLayerTexture = new TerrainLayerTexture();
                TerrainLayerTexture.mBaseColorTexture = TerrainLayer._BaseColorTexture;
                TerrainLayerTexture.mNormalTexture = TerrainLayer._NormalTexture;
                TerrainLayerTexture.mHMRATexture = TerrainLayer._HMRATexture;
                TerrainLayerTextures.Add(TerrainLayerTexture);
            }

            TerrainSystemG.SetBlendLayers(WorldPtr, TerrainEntityID, TerrainLayerTextures);
        }

        void TraverseTerrainTiles(TerrainComponent TerrainComponent)
        {
            Entity TerrainEntity = TerrainComponent.Entity;
            ulong TerrainEntityID = TerrainEntity.EntityID;
            EditorScene EditorScene = EditorScene.GetInstance();
            IntPtr WorldPtr = EditorScene.GetWorld().GetNativePointer();
            TerrainInfo TerrainInfo = TerrainSystemG.GetTerrainInfo(WorldPtr, TerrainEntityID);
            uint GridSizeX = TerrainInfo.mGridSizeX;
            uint GridSizeY = TerrainInfo.mGridSizeY;
            uint BlockSize = TerrainInfo.mBlockSize;
            for (uint GridX = 0; GridX < GridSizeX; GridX++)
            {
                for (uint GridY = 0; GridY < GridSizeY; GridY++)
                {
                    for (uint TileX = 0; TileX < BlockSize; TileX++)
                    {
                        for (uint TileY = 0; TileY < BlockSize; TileY++)
                        {
                            uint Level = 0;
                            TileIndex TileIndex = new TileIndex(GridX, GridY, Level, TileX, TileY);

                            Float3 OutCenter = new Float3();
                            Float3 OutExtent = new Float3();
                            TerrainSystemG.GetTileBoundingBox(WorldPtr, TerrainEntityID, TileIndex, OutCenter, OutExtent);
                        }
                    }
                }
            }
        }

        void BeginTerrainOperation()
        {
            _bEnableTerrainOperation = true;
            Entity TerrainEntity = SearchTerrainEntity();
            _EditOperation_TerrainHeight = new EditOperation_TerrainHeight();
            _EditOperation_TerrainHeight._TerrainEntity = TerrainEntity;
            _EditOperation_TerrainWeight = new EditOperation_TerrainWeight();
            _EditOperation_TerrainWeight._TerrainEntity = TerrainEntity;
        }

        float CalculateHeightPerWeight(float TileSizeHeight, float TileSizeWeight)
        {
            return TileSizeHeight / TileSizeWeight;
        }

        double CalculateTerrainScalingAverage(Vector3d TerrainScaling)
        {
            double TerrainScalingX = TerrainScaling.X;
            double TerrainScalingY = TerrainScaling.Y;
            double TerrainScalingZ = TerrainScaling.Z;
            return (TerrainScalingX + TerrainScalingY + TerrainScalingZ) / 3.0;
        }

        double CalculateTerrainScalingAverage()
        {
            Entity TerrainEntity = SearchTerrainEntity();
            if (TerrainEntity == null)
            {
                return 1.0f;
            }
            Transform Transform = TerrainEntity.GetTransformComponent();
            Matrix4x4d WorldMatrix = new Matrix4x4d();
            Transform.GetWorldMatrix(ref WorldMatrix);
            Vector3d TerrainScaling;
            WorldMatrix.GetScale(out TerrainScaling);
            return CalculateTerrainScalingAverage(TerrainScaling);
        }

        public double CalculatePaintSphereScale()
        {
            double TerrainScalingAverage = CalculateTerrainScalingAverage();
            return TerrainScalingAverage * _TerrainOperationRadius * RadiusScale / 50.0;
        }

        unsafe void DoTerrainOperation(float TimeElapsedS)
        {
            if (_bEnableTerrainOperation == false)
            {
                return;
            }

            if (_TerrainOperationType == TerrainOperationType.CreateTerrain ||
                _TerrainOperationType == TerrainOperationType.ImportTerrain)
            {
                return;
            }

            Entity TerrainEntity = SearchTerrainEntity();
            if (TerrainEntity == null)
            {
                return;
            }
            ulong TerrainEntityID = TerrainEntity.EntityID;

            TerrainComponent TerrainComponent = TerrainEntity.GetTerrainComponent();
            if (TerrainComponent == null)
            {
                return;
            }

            if (TerrainComponent.Enable == false)
            {
                return;
            }

            RayPickResult RayPickStruct = EditorSceneUI.GetInstance()._RayPickResult;

            if (RayPickStruct == null || RayPickStruct.HitEntity == null || RayPickStruct.HitEntity.EntityID != TerrainEntityID)
            {
                return;
            }

            Device Device = UIManager.GetActiveUIManager().GetDevice();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            if (bAlt)
            {
                return;
            }

            SetTerrainDirty();

            Transform Transform = TerrainEntity.GetTransformComponent();

            Matrix4x4d WorldMatrix = new Matrix4x4d();
            Transform.GetWorldMatrix(ref WorldMatrix);

            Vector3d TerrainPosition = new Vector3d();
            WorldMatrix.GetPosition(out TerrainPosition);
            double TerrainPositionX = TerrainPosition.X;
            double TerrainPositionY = TerrainPosition.Y;
            double TerrainPositionZ = TerrainPosition.Z;

            Vector3d TerrainScaling = new Vector3d();
            WorldMatrix.GetScale(out TerrainScaling);
            double TerrainScalingX = TerrainScaling.X;
            double TerrainScalingY = TerrainScaling.Y;
            double TerrainScalingZ = TerrainScaling.Z;

            double TerrainScalingAverage = CalculateTerrainScalingAverage(TerrainScaling);

            Vector3d HitPoint = RayPickStruct.HitPoint;
            double HitPointX = HitPoint.X;
            double HitPointY = HitPoint.Y;
            double HitPointZ = HitPoint.Z;
            double TerrainOperationTotalRadius = Math.Max(_TerrainOperationRadius, 0.0f) * RadiusScale * TerrainScalingAverage;
            double TerrainOperationFalloff = Math.Max(Math.Min(_TerrainOperationFalloff, 1.0f), 0.0f);
            double TerrainOperationInnerRadius = TerrainOperationTotalRadius * TerrainOperationFalloff;
            double TerrainOperationFalloffRadius = TerrainOperationTotalRadius - TerrainOperationInnerRadius;
            double TerrainOperationStrength = Math.Min(Math.Max(_TerrainOperationStrength, 0.0f), 1.0f);
            double Radius = TerrainOperationTotalRadius;
            double RadiusSquare = Radius * Radius;
            double MinX = HitPointX - Radius;
            double MaxX = HitPointX + Radius;
            double MinZ = HitPointZ - Radius;
            double MaxZ = HitPointZ + Radius;

            double HitPointTerrainHeight = (HitPointY - TerrainPositionY) / TerrainScalingY;

            EditorScene EditorScene = EditorScene.GetInstance();
            IntPtr WorldPtr = EditorScene.GetWorld().GetNativePointer();
            TerrainInfo TerrainInfo = TerrainSystemG.GetTerrainInfo(WorldPtr, TerrainEntityID);
            vector_unsigned_int HeightmapTriangleIndices = TerrainSystemG.GetHeightmapTriangleIndices(WorldPtr, TerrainEntityID);

            uint GridSizeX = TerrainInfo.mGridSizeX;
            uint GridSizeY = TerrainInfo.mGridSizeY;
            uint BlockSize = TerrainInfo.mBlockSize;
            uint TileSize = TerrainInfo.mTileSize;
            uint TileSize1 = TileSize + 1;
            uint TileHeightmapDataSize = TileSize1 * TileSize1;

            if (_TerrainOperationType == TerrainOperationType.SmoothHeight)
            {
                int KernelSize = 2 + (int)(Math.Max(0.0f, _TerrainOperationKernelSize) * 15);
                double KernelRadius = (KernelSize + 1) * TerrainScalingAverage;
                double Range = Radius + KernelRadius;
                _TerrainHeightList.Clear();
                _TerrainHeightMap.Clear();
                _DirtyTileList.Clear();
                for (uint GridX = 0; GridX < GridSizeX; GridX++)
                {
                    for (uint GridY = 0; GridY < GridSizeY; GridY++)
                    {
                        for (uint TileX = 0; TileX < BlockSize; TileX++)
                        {
                            for (uint TileY = 0; TileY < BlockSize; TileY++)
                            {
                                uint Level = 0;
                                TileIndex TileIndex = new TileIndex(GridX, GridY, Level, TileX, TileY);

                                Double3 OutCenter;
                                Double3 OutExtent;
                                GetTileBoundingBox(WorldPtr, TerrainEntityID, TileIndex, out OutCenter, out OutExtent);
                                OutCenter.x = TerrainPositionX + OutCenter.x * TerrainScalingX;
                                OutCenter.z = TerrainPositionZ + OutCenter.z * TerrainScalingZ;
                                OutExtent.x *= TerrainScalingX;
                                OutExtent.z *= TerrainScalingZ;

                                if (MaxX > OutCenter.x - OutExtent.x &&
                                    MinX < OutCenter.x + OutExtent.x &&
                                    MaxZ > OutCenter.z - OutExtent.z &&
                                    MinZ < OutCenter.z + OutExtent.z)
                                {
                                    _EditOperation_TerrainHeight.AddTerrainTile(TileIndex);
                                    _DirtyTileList_Height.AddTileIndex(TileIndex);
                                    _DirtyTileList.Add(TileIndex);

                                    IntPtr TileHeightmapData = TerrainSystemG.GetTileHeightmapData(WorldPtr, TerrainEntityID, TileIndex);
                                    float* TileHeightmapPointer = (float*)TileHeightmapData;

                                    for (uint VertexIndex = 0; VertexIndex < TileHeightmapDataSize; VertexIndex++)
                                    {
                                        double X = (GridX * BlockSize + TileX) * TileSize + VertexIndex % TileSize1;
                                        double Z = (GridY * BlockSize + TileY) * TileSize + VertexIndex / TileSize1;

                                        X = TerrainPositionX + TerrainScalingX * X;
                                        Z = TerrainPositionZ + TerrainScalingZ * Z;

                                        double DistanceX = Math.Abs(X - HitPointX);
                                        double DistanceZ = Math.Abs(Z - HitPointZ);
                                        if (DistanceX <= Range && DistanceZ <= Range)
                                        {
                                            float* HeightmapPointer = TileHeightmapPointer + VertexIndex;
                                            float Height = *HeightmapPointer;

                                            long Key = ((long)(X * 100) << 32) + (long)(Z * 100);
                                            TerrainHeight TerrainHeight = null;
                                            if (_TerrainHeightMap.TryGetValue(Key, out TerrainHeight) == false)
                                            {
                                                TerrainHeight = new TerrainHeight();
                                                TerrainHeight._X = X;
                                                TerrainHeight._Z = Z;
                                                TerrainHeight._YOld = Height;
                                                TerrainHeight._YNew = Height;
                                                _TerrainHeightMap[Key] = TerrainHeight;
                                                _TerrainHeightList.Add(TerrainHeight);
                                                TerrainHeight._FirstYPointer = (IntPtr)HeightmapPointer;
                                            }
                                            else
                                            {
                                                if (TerrainHeight._YPointerList == null)
                                                {
                                                    TerrainHeight._YPointerList = new List<IntPtr>();
                                                }
                                                TerrainHeight._YPointerList.Add((IntPtr)HeightmapPointer);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if (_TerrainHeightList.Count == 0)
                {
                    return;
                }
                _TerrainHeightList.Sort((TerrainHeight TerrainHeight1, TerrainHeight TerrainHeight2) =>
                {
                    int Comp = TerrainHeight1._Z.CompareTo(TerrainHeight2._Z);
                    if (Comp != 0)
                    {
                        return Comp;
                    }
                    return TerrainHeight1._X.CompareTo(TerrainHeight2._X);
                });
                double FirstZ = _TerrainHeightList[0]._Z;
                int FirstZCount = 0;
                foreach (TerrainHeight TerrainHeight in _TerrainHeightList)
                {
                    if (Math.Abs(TerrainHeight._Z - FirstZ) < 0.01f)
                    {
                        FirstZCount++;
                    }
                    else
                    {
                        break;
                    }
                }
                int Count = _TerrainHeightList.Count;
                int Width1 = FirstZCount;
                int Height1 = Count / Width1;
                double Coefficient1 = 0.0f;
                double Coefficient2 = (1.0f - Coefficient1) / 4.0f;
                DebugHelper.Assert(Width1 * Height1 == Count);
                for (int i = KernelSize; i < Height1 - KernelSize; i++)
                {
                    for (int j = KernelSize; j < Width1 - KernelSize; j++)
                    {
                        int YOldCount = 0;
                        float YOldTotal = 0;
                        for (int Kernel1 = -KernelSize; Kernel1 <= KernelSize; Kernel1++)
                        {
                            for (int Kernel2 = -KernelSize; Kernel2 <= KernelSize; Kernel2++)
                            {
                                int i2 = (i + Kernel1) * Width1 + (j + Kernel2);
                                float YOld = _TerrainHeightList[i2]._YOld;
                                YOldTotal += YOld;
                                YOldCount++;
                            }
                        }

                        int i1 = i * Width1 + j;
                        TerrainHeight TerrainHeight = _TerrainHeightList[i1];
                        TerrainHeight._YNew = YOldTotal / YOldCount;
                    }
                }
                foreach (TerrainHeight TerrainHeight in _TerrainHeightList)
                {
                    double DistanceX = TerrainHeight._X - HitPointX;
                    double DistanceZ = TerrainHeight._Z - HitPointZ;
                    double DistanceSquare = DistanceX * DistanceX + DistanceZ * DistanceZ;
                    if (DistanceSquare <= RadiusSquare)
                    {
                        double Distance = Math.Sqrt(DistanceSquare);
                        double Strength = CalculateFalloff(_TerrainFalloffType, Distance, TerrainOperationInnerRadius, TerrainOperationFalloffRadius);
                        double Strength1 = Strength * TerrainOperationStrength;
                        double DeltaHeight = Strength1 * 10.0f * (TerrainHeight._YNew - TerrainHeight._YOld) * TimeElapsedS;
                        if (bShift)
                        {
                            DeltaHeight = -DeltaHeight;
                        }
                        double Height = TerrainHeight._YOld + DeltaHeight;
                        {
                            IntPtr YPointer1 = TerrainHeight._FirstYPointer;
                            float* YPointer = (float*)YPointer1;
                            _EditOperation_TerrainHeight.AddTerrainHeightData(YPointer1);
                            *YPointer = (float)Height;
                            _EditOperation_TerrainHeight.UpdateTerrainHeightData(YPointer1);
                        }
                        if (TerrainHeight._YPointerList != null)
                        {
                            foreach (IntPtr YPointer1 in TerrainHeight._YPointerList)
                            {
                                float* YPointer = (float*)YPointer1;
                                _EditOperation_TerrainHeight.AddTerrainHeightData(YPointer1);
                                *YPointer = (float)Height;
                                _EditOperation_TerrainHeight.UpdateTerrainHeightData(YPointer1);
                            }
                        }
                    }
                }
                foreach (TileIndex TileIndex in _DirtyTileList)
                {
                    TerrainSystemG.MarkTileDirty(WorldPtr, TerrainEntityID, TileIndex, TileDirtyType.Heightmap);
                }
                EditorScene.SetDirty();
                return;
            }

            for (uint GridX = 0; GridX < GridSizeX; GridX++)
            {
                for (uint GridY = 0; GridY < GridSizeY; GridY++)
                {
                    for (uint TileX = 0; TileX < BlockSize; TileX++)
                    {
                        for (uint TileY = 0; TileY < BlockSize; TileY++)
                        {
                            uint Level = 0;
                            TileIndex TileIndex = new TileIndex(GridX, GridY, Level, TileX, TileY);

                            Double3 OutCenter;
                            Double3 OutExtent;
                            GetTileBoundingBox(WorldPtr, TerrainEntityID, TileIndex, out OutCenter, out OutExtent);
                            OutCenter.x = TerrainPositionX + OutCenter.x * TerrainScalingX;
                            OutCenter.z = TerrainPositionZ + OutCenter.z * TerrainScalingZ;
                            OutExtent.x *= TerrainScalingX;
                            OutExtent.z *= TerrainScalingZ;

                            if (MaxX > OutCenter.x - OutExtent.x &&
                                MinX < OutCenter.x + OutExtent.x &&
                                MaxZ > OutCenter.z - OutExtent.z &&
                                MinZ < OutCenter.z + OutExtent.z)
                            {
                                if (_TerrainOperationType == TerrainOperationType.PaintWeight)
                                {
                                    _EditOperation_TerrainWeight.AddTerrainTile(TileIndex);
                                    _DirtyTileList_Weight.AddTileIndex(TileIndex);

                                    uint WeightmapTileSize1 = TerrainSystemG.GetTileLayerWeightSize(WorldPtr, TerrainEntityID, TileIndex);
                                    uint TileWeightmapDataSize = WeightmapTileSize1 * WeightmapTileSize1;
                                    float HeightPerWeight = CalculateHeightPerWeight(TileSize1, WeightmapTileSize1);

                                    _WeightDataList.Clear();
                                    int LayerIndex1 = 0;
                                    while (true)
                                    {
                                        IntPtr IntPointer = TerrainSystemG.GetTileLayerWeightData(WorldPtr, TerrainEntityID, TileIndex, (uint)LayerIndex1);
                                        if (IntPointer == IntPtr.Zero)
                                        {
                                            break;
                                        }
                                        _WeightDataList.Add(IntPointer);
                                        LayerIndex1++;
                                    }
                                    IntPtr Data = _WeightDataList[_TerrainOperationLayerIndex];
                                    byte* TileWeightDataPointer = (byte*)Data;
                                    if (TileWeightDataPointer != null)
                                    {
                                        bool bTileModified = false;

                                        for (uint i = 0; i < TileWeightmapDataSize; i++)
                                        {
                                            double X = (GridX * BlockSize + TileX) * TileSize + HeightPerWeight * (float)(i % WeightmapTileSize1);
                                            double Z = (GridY * BlockSize + TileY) * TileSize + HeightPerWeight * (float)(i / WeightmapTileSize1);

                                            X = TerrainPositionX + TerrainScalingX * X;
                                            Z = TerrainPositionZ + TerrainScalingZ * Z;

                                            double DistanceX = X - HitPointX;
                                            double DistanceZ = Z - HitPointZ;
                                            double DistanceSquare = DistanceX * DistanceX + DistanceZ * DistanceZ;
                                            if (DistanceSquare <= RadiusSquare)
                                            {
                                                double Distance = Math.Sqrt(DistanceSquare);
                                                double Strength = CalculateFalloff(_TerrainFalloffType, Distance, TerrainOperationInnerRadius, TerrainOperationFalloffRadius);
                                                double Strength1 = Strength * TerrainOperationStrength;
                                                byte* WeightmapPointer = TileWeightDataPointer + i;
                                                byte MixtureWeight = *WeightmapPointer;
                                                double MixtureWeightF = MixtureWeight / 255.0;

                                                IntPtr WeightmapPointer1 = (IntPtr)WeightmapPointer;
                                                _EditOperation_TerrainWeight.AddTerrainWeightData(WeightmapPointer1);

                                                double DeltaMixtureWeight = 0.0f;
                                                if (_TerrainOperationType == TerrainOperationType.PaintWeight)
                                                {
                                                    if (_TerrainOperationEnableTargetWeight)
                                                    {
                                                        DeltaMixtureWeight = (_TerrainOperationTargetWeightValue - MixtureWeightF) * 5.0f * Strength1 * TimeElapsedS;
                                                    }
                                                    else
                                                    {
                                                        DeltaMixtureWeight = Strength1 * 2.0f * TimeElapsedS;
                                                        if (bShift)
                                                        {
                                                            DeltaMixtureWeight = -DeltaMixtureWeight;
                                                        }
                                                    }
                                                }

                                                MixtureWeightF += DeltaMixtureWeight;
                                                MixtureWeightF = Math.Max(MixtureWeightF, 0.0f);
                                                MixtureWeightF = Math.Min(MixtureWeightF, 1.0f);
                                                MixtureWeight = (byte)(MixtureWeightF * 255.0f);
                                                *WeightmapPointer = MixtureWeight;

                                                _EditOperation_TerrainWeight.UpdateTerrainWeightData(WeightmapPointer1);

                                                int WeightLeft = 255 - MixtureWeight;
                                                int WeightOthers = 0;
                                                for (int LayerIndex2 = 0; LayerIndex2 < _WeightDataList.Count; LayerIndex2++)
                                                {
                                                    if (LayerIndex2 != _TerrainOperationLayerIndex)
                                                    {
                                                        IntPtr Data2 = _WeightDataList[LayerIndex2];
                                                        byte* TileWeightDataPointer2 = (byte*)Data2;
                                                        WeightOthers += TileWeightDataPointer2[i];
                                                    }
                                                }

                                                if (WeightOthers > 0)
                                                {
                                                    double F = WeightLeft / (float)WeightOthers;
                                                    for (int LayerIndex2 = 0; LayerIndex2 < _WeightDataList.Count; LayerIndex2++)
                                                    {
                                                        if (LayerIndex2 != _TerrainOperationLayerIndex)
                                                        {
                                                            IntPtr Data2 = _WeightDataList[LayerIndex2];
                                                            byte* WeightmapPointer2 = (byte*)Data2 + i;
                                                            IntPtr WeightmapPointer3 = (IntPtr)WeightmapPointer2;

                                                            _EditOperation_TerrainWeight.AddTerrainWeightData(WeightmapPointer3);

                                                            byte MixtureWeight3 = *WeightmapPointer2;
                                                            *WeightmapPointer2 = (byte)(MixtureWeight3 * F);

                                                            _EditOperation_TerrainWeight.UpdateTerrainWeightData(WeightmapPointer3);
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    if (_TerrainOperationLayerIndex == 0)
                                                    {
                                                        *WeightmapPointer = 255;
                                                        _EditOperation_TerrainWeight.UpdateTerrainWeightData(WeightmapPointer1);
                                                    }
                                                    else
                                                    {
                                                        for (int LayerIndex2 = 0; LayerIndex2 < _WeightDataList.Count; LayerIndex2++)
                                                        {
                                                            if (LayerIndex2 != _TerrainOperationLayerIndex)
                                                            {
                                                                IntPtr Data2 = _WeightDataList[LayerIndex2];
                                                                byte* WeightmapPointer2 = (byte*)Data2 + i;
                                                                IntPtr WeightmapPointer3 = (IntPtr)WeightmapPointer2;

                                                                _EditOperation_TerrainWeight.AddTerrainWeightData(WeightmapPointer3);

                                                                if (DeltaMixtureWeight >= 0.0f)
                                                                {
                                                                    *WeightmapPointer2 = 0;
                                                                }
                                                                else
                                                                {
                                                                    if (LayerIndex2 == 0)
                                                                    {
                                                                        *WeightmapPointer2 = (byte)WeightLeft;
                                                                    }
                                                                }

                                                                _EditOperation_TerrainWeight.UpdateTerrainWeightData(WeightmapPointer3);
                                                            }
                                                        }
                                                    }
                                                }

                                                bTileModified = true;
                                            }
                                        }

                                        if (bTileModified)
                                        {
                                            TerrainSystemG.MarkTileDirty(WorldPtr, TerrainEntityID, TileIndex, TileDirtyType.WeightTexture);
                                            EditorScene.SetDirty();
                                        }
                                    }
                                }
                                else
                                {
                                    _EditOperation_TerrainHeight.AddTerrainTile(TileIndex);
                                    _DirtyTileList_Height.AddTileIndex(TileIndex);

                                    IntPtr TileHeightmapData = TerrainSystemG.GetTileHeightmapData(WorldPtr, TerrainEntityID, TileIndex);
                                    float* TileHeightmapPointer = (float*)TileHeightmapData;

                                    bool bTileModified = false;

                                    for (uint VertexIndex = 0; VertexIndex < TileHeightmapDataSize; VertexIndex++)
                                    {
                                        double X = (GridX * BlockSize + TileX) * TileSize + VertexIndex % TileSize1;
                                        double Z = (GridY * BlockSize + TileY) * TileSize + VertexIndex / TileSize1;

                                        X = TerrainPositionX + TerrainScalingX * X;
                                        Z = TerrainPositionZ + TerrainScalingZ * Z;

                                        //Float3 WorldTranslation = new Float3(0.0f, 0.0f, 0.0f);
                                        //Float3 WorldScale = new Float3(1.0f, 1.0f, 1.0f);
                                        //Float3 TileVertexWorldPosition = TerrainSystemG.GetTileVertexWorldPosition(WorldPtr, TerrainEntityID, WorldTranslation, WorldScale, TileIndex, VertexIndex, TileHeightmapData);
                                        //float X = TileVertexWorldPosition.x;
                                        //float Z = TileVertexWorldPosition.z;

                                        double DistanceX = X - HitPointX;
                                        double DistanceZ = Z - HitPointZ;
                                        double DistanceSquare = DistanceX * DistanceX + DistanceZ * DistanceZ;
                                        if (DistanceSquare <= RadiusSquare)
                                        {
                                            double Distance = Math.Sqrt(DistanceSquare);
                                            double Strength = CalculateFalloff(_TerrainFalloffType, Distance, TerrainOperationInnerRadius, TerrainOperationFalloffRadius);
                                            double Strength1 = Strength * TerrainOperationStrength;
                                            double DeltaHeight = 0.0f;
                                            float* HeightmapPointer = TileHeightmapPointer + VertexIndex;
                                            double Height = *HeightmapPointer;

                                            IntPtr HeightmapPointer1 = (IntPtr)HeightmapPointer;
                                            _EditOperation_TerrainHeight.AddTerrainHeightData(HeightmapPointer1);

                                            if (_TerrainOperationType == TerrainOperationType.SculptHeight)
                                            {
                                                DeltaHeight = Strength1 * 50.0f * TimeElapsedS;
                                                if (bShift)
                                                {
                                                    DeltaHeight = -DeltaHeight;
                                                }
                                            }
                                            else if (_TerrainOperationType == TerrainOperationType.NoiseHeight)
                                            {
                                                float OffsetY = ((float)_Random.NextDouble() - 0.5f) * 30.0f;
                                                DeltaHeight = Strength1 * OffsetY * TimeElapsedS;
                                            }
                                            else if (_TerrainOperationType == TerrainOperationType.FlattenHeight)
                                            {
                                                DeltaHeight = -(Strength1 * 2.0f * (Height - HitPointTerrainHeight) * TimeElapsedS);
                                                if (bShift)
                                                {
                                                    DeltaHeight = -DeltaHeight;
                                                }
                                            }
                                            else if (_TerrainOperationType == TerrainOperationType.ClearHeight)
                                            {
                                                DeltaHeight = -Height;
                                            }
                                            else if (_TerrainOperationType == TerrainOperationType.DigHole)
                                            {
                                                DeltaHeight = -Height + HoleHeight;   // Dig holes
                                            }
                                            else
                                            {
                                                DebugHelper.Assert(false);
                                            }

                                            if (_TerrainOperationType == TerrainOperationType.DigHole || Height > HoleHeight)
                                            {
                                                Height += DeltaHeight;
                                            }

                                            TileHeightmapPointer[VertexIndex] = (float)Height;

                                            _EditOperation_TerrainHeight.UpdateTerrainHeightData(HeightmapPointer1);

                                            bTileModified = true;
                                        }
                                    }

                                    if (bTileModified)
                                    {
                                        TerrainSystemG.MarkTileDirty(WorldPtr, TerrainEntityID, TileIndex, TileDirtyType.Heightmap);
                                        EditorScene.SetDirty();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        void EndTerrainOperation()
        {
            if (_EditOperation_TerrainHeight != null &&
                _EditOperation_TerrainHeight._TerrainHeightDataList.Count > 0)
            {
                EditOperationManager.GetInstance().AddOperation(_EditOperation_TerrainHeight);
                _EditOperation_TerrainHeight = null;
            }
            if (_EditOperation_TerrainWeight != null &&
                _EditOperation_TerrainWeight._TerrainWeightDataList.Count > 0)
            {
                EditOperationManager.GetInstance().AddOperation(_EditOperation_TerrainWeight);
                _EditOperation_TerrainWeight = null;
            }
            _bEnableTerrainOperation = false;
        }
    }
}
