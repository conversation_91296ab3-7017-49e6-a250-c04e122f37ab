using CEngine;
namespace CrossEditor
{
    class SmoothTerrainConfigs : SculptTerrainConfigs
    {
        static SmoothTerrainConfigs _Instance = new SmoothTerrainConfigs();

        public static new SmoothTerrainConfigs GetInstance()
        {
            return _Instance;
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Terrain smooth brush kernel size")]
        public float KernelSize
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationKernelSize;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationKernelSize = value;
            }
        }
    }
}
