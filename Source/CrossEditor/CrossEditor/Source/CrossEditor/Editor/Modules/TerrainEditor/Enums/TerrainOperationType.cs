namespace CrossEditor
{
    enum TerrainOperationType
    {
        [TerrainEnumInfo(DisplayName = "Create", TexturePath = "EngineResource/Editor/Icons/Terrain/CreateTerrain.nda")]
        CreateTerrain,

        [TerrainEnumInfo(DisplayName = "Import", TexturePath = "EngineResource/Editor/Icons/Terrain/CreateTerrain.nda")]
        ImportTerrain,

        [TerrainEnumInfo(DisplayName = "Sculpt", TexturePath = "EngineResource/Editor/Icons/Terrain/SculptHeight.nda")]
        SculptHeight,

        [TerrainEnumInfo(DisplayName = "NoiseH", TexturePath = "EngineResource/Editor/Icons/Terrain/NoiseHeight.nda")]
        NoiseHeight,

        [TerrainEnumInfo(DisplayName = "FlattenH", TexturePath = "EngineResource/Editor/Icons/Terrain/FlattenHeight.nda")]
        FlattenHeight,

        [TerrainEnumInfo(DisplayName = "SmoothH", TexturePath = "EngineResource/Editor/Icons/Terrain/FlattenHeight.nda")]
        SmoothHeight,

        [TerrainEnumInfo(DisplayName = "Clear", TexturePath = "EngineResource/Editor/Icons/Terrain/ClearHeight.nda")]
        ClearHeight,

        [TerrainEnumInfo(DisplayName = "Dig", TexturePath = "EngineResource/Editor/Icons/Terrain/DigHole.nda")]
        DigHole,

        [TerrainEnumInfo(DisplayName = "Paint", TexturePath = "EngineResource/Editor/Icons/Terrain/PaintWeight.nda")]
        PaintWeight,
    }
}
