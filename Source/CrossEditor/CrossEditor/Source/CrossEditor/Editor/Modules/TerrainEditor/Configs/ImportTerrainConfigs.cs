using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    public class ImportTerrainConfigs
    {
        static ImportTerrainConfigs _Instance = new ImportTerrainConfigs();

        TerrainBlockSizes _BlockSize;
        TerrainTileSizes _TileSize;
        string _Heightmap;
        List<string> _Weightmap;

        public static ImportTerrainConfigs GetInstance()
        {
            return _Instance;
        }

        public ImportTerrainConfigs()
        {
            _BlockSize = TerrainBlockSizes.__2;
            _TileSize = TerrainTileSizes.__256;
            _Heightmap = "";
            _Weightmap = new List<string>();
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Block count per grid in X/Y.")]
        public TerrainBlockSizes BlockSize
        {
            get
            {
                return _BlockSize;
            }
            set
            {
                _BlockSize = value;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Tile count per block in X/Y.")]
        public TerrainTileSizes TileSize
        {
            get
            {
                return _TileSize;
            }
            set
            {
                _TileSize = value;
            }
        }

        [PropertyInfo(PropertyType = "StringAsFile", ToolTips = "Height map path.", FileTypeDescriptor = "Heightmap Files#png")]
        public string Heightmap
        {
            get
            {
                return _Heightmap;
            }
            set
            {
                _Heightmap = value;
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "StringAsFile", ToolTips = "Weight map path.", FileTypeDescriptor = "Heightmap Files#png")]
        public List<string> WeightmapList
        {
            get
            {
                return _Weightmap;
            }
            set
            {
                _Weightmap = value;
            }
        }
    }
}
