using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

namespace CrossEditor
{
    class TerrainEditorUI : DockingUI
    {
        const uint DefaultNormal = 0x80800000;
        const uint DefaultHeight = 0x00008000;
        const uint MinimalHeight = 0x00000001;
        const uint HoleHeight = 0x00000000;

        public const int LayersPerWeightTexture = 3;
        public const int MaxLayersSupported = LayersPerWeightTexture * 2;
        public const float DefaultScale = 1.0f;  //100.0f

        const uint DefaultNormalAndDefaultHeight = (DefaultNormal | DefaultHeight);

        static TerrainEditorUI _Instance = new TerrainEditorUI();
        static int SPAN_X = 5;

        public Panel _Container;

        public AlignedPanel _ButtonContainer;
        public List<Button> _OperationButtons;

        ScrollView _ScrollView;
        Panel _ScrollPanel;

        Inspector _Inspector;
        InspectorHandler _InspectorHandler;
        object _ObjectToInspect;

        public static TerrainEditorUI GetInstance()
        {
            return _Instance;
        }

        public TerrainEditorUI()
        {
            _OperationButtons = new List<Button>();
            _ObjectToInspect = null;
        }

        bool IsTerrainTest()
        {
            return FileHelper.IsFileExists("TerrainTest.txt");
        }

        public bool Initialize()
        {
            _Container = new Panel();
            _Container.Initialize();
            _Container.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);

            base.Initialize("Terrain", _Container);

            _ButtonContainer = new AlignedPanel(SPAN_X);
            _Container.AddChild(_ButtonContainer);

            InitializeOperationButtons<TerrainOperationType>();
            UpdateBorderCheckedStates();

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.GetHScroll().SetEnable(false);
            _Container.AddChild(_ScrollView);
            _ScrollView.SetPosition(10, 40, 630, 420);

            _ScrollPanel = _ScrollView.GetScrollPanel();

            if (IsTerrainTest())
            {
                InitTestImportTerrainConfigs();
            }

            _ObjectToInspect = CreateTerrainConfigs.GetInstance();

            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_ObjectToInspect); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            _Inspector = new Inspector_TerrainConfigs();
            _Inspector.InspectObject(_ObjectToInspect);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);
            UpdateLayout();

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;

            return true;
        }

        void InitTestImportTerrainConfigs()
        {
            ImportTerrainConfigs ImportTerrainConfigs = ImportTerrainConfigs.GetInstance();
            ImportTerrainConfigs.BlockSize = TerrainBlockSizes.__1;
            ImportTerrainConfigs.Heightmap = "Contents/GreatWallTerrainResource/Heightmap.png";
            List<string> WeightmapList = new List<string>();
            WeightmapList.Add("Contents/GreatWallTerrainResource/0_GrassBigLayer.png");
            WeightmapList.Add("Contents/GreatWallTerrainResource/1_RockLayer.png");
            WeightmapList.Add("Contents/GreatWallTerrainResource/2_RockALayer.png");
            WeightmapList.Add("Contents/GreatWallTerrainResource/3_GroundLayer.png");
            WeightmapList.Add("Contents/GreatWallTerrainResource/4_GroundWetLayer.png");
            WeightmapList.Add("Contents/GreatWallTerrainResource/5_RockSuishi_Layer.png");
            ImportTerrainConfigs.WeightmapList = WeightmapList;
        }

        public void UpdateInspector()
        {
            _Inspector.GetInspectorHandler().InspectObject();
            _Inspector.GetInspectorHandler().UpdateLayout();
        }

        public override void Update(long TimeElapsed)
        {
            CheckKeys(TimeElapsed);
            TerrainEditor.GetInstance().FetchTerrainLayers();
            Inspector Inspector = _Inspector.FindChildInspectorByPropertyName("Layers");
            if (Inspector != null)
            {
                Inspector_Property_SelectionList Inspector_Property_SelectionList = (Inspector_Property_SelectionList)Inspector;
                int LayerIndex = Inspector_Property_SelectionList.GetSelectIndex();
                TerrainEditor.GetInstance()._TerrainOperationLayerIndex = LayerIndex;
            }
        }

        void CheckKeys(long TimeElapsed)
        {
            EditorMode EditorMode = EditorSceneUI.GetInstance().GetEditorMode();
            if (EditorMode == EditorMode.Terrain_Mode)
            {
                TerrainEditor TerrainEditor = TerrainEditor.GetInstance();
                Device Device = GetDevice();
                bool bOpenBracketDown = Device.IsKeyDown(Key.OpenBracket);
                bool bCloseBracketDown = Device.IsKeyDown(Key.CloseBracket);
                if (bOpenBracketDown || bCloseBracketDown)
                {
                    float Strength = 0.3f;
                    float TimeElapsedF = TimeElapsed / 1000.0f;
                    float Radius = TerrainEditor._TerrainOperationRadius;
                    if (bOpenBracketDown)
                    {
                        Radius -= Strength * TimeElapsedF;
                        Radius = Math.Max(0.0f, Radius);
                    }
                    else if (bCloseBracketDown)
                    {
                        Radius += Strength * TimeElapsedF;
                        Radius = Math.Min(1.0f, Radius);
                    }
                    TerrainEditor.GetInstance()._TerrainOperationRadius = Radius;
                    EditorSceneUI.GetInstance().SetPaintSphereDirty();
                }
            }
        }

        void InitializeOperationButtons<EnumType>() where EnumType : Enum
        {
            _ButtonContainer.ClearChildren();
            _OperationButtons.Clear();

            int Index = 0;
            foreach (EnumType Enum in Enum.GetValues(typeof(EnumType)))
            {
                FieldInfo FieldInfo = Enum.GetType().GetField(Enum.ToString());
                TerrainEnumInfoAttribute TerrainEnumInfo = TerrainEnumInfoAttribute.GetTerrainEnumInfo(FieldInfo);

                Button ButtonOperation = new Button();
                ButtonOperation.Initialize();
                ButtonOperation.SetSize(64, 64);
                ButtonOperation.SetTagInt1(Index);
                ButtonOperation.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                ButtonOperation.ClickedEvent += OnButtonOperationClicked;
                ButtonOperation.SetVisible(true);

                Panel IconPanel = new Panel();
                IconPanel.Initialize();
                IconPanel.SetPosition(12, 0, 40, 40);
                Texture Icon = UIManager.LoadUIImage(TerrainEnumInfo.TexturePath);
                IconPanel.SetImage(Icon);
                ButtonOperation.AddChild(IconPanel);

                Label ButtonLabel = new Label();
                ButtonLabel.Initialize();
                ButtonLabel.SetText(TerrainEnumInfo.DisplayName);
                ButtonLabel.SetTextAlign(TextAlign.CenterCenter);
                ButtonLabel.SetPosition(2, 40, 60, 24);
                ButtonLabel.SetFontSize(12);
                ButtonOperation.AddChild(ButtonLabel);

                _ButtonContainer.AddChild(ButtonOperation);
                _OperationButtons.Add(ButtonOperation);
                Index++;
            }
        }

        void OnButtonOperationClicked(Control Sender)
        {
            TerrainOperationType TerrainOperationType = (TerrainOperationType)Sender.GetTagInt1();
            TerrainEditor.GetInstance()._TerrainOperationType = TerrainOperationType;
            UpdateBorderCheckedStates();
            switch (TerrainOperationType)
            {
                case TerrainOperationType.CreateTerrain:
                    _ObjectToInspect = CreateTerrainConfigs.GetInstance();
                    break;
                case TerrainOperationType.ImportTerrain:
                    _ObjectToInspect = ImportTerrainConfigs.GetInstance();
                    break;
                case TerrainOperationType.SculptHeight:
                case TerrainOperationType.NoiseHeight:
                case TerrainOperationType.FlattenHeight:
                    _ObjectToInspect = SculptTerrainConfigs.GetInstance();
                    break;
                case TerrainOperationType.SmoothHeight:
                    _ObjectToInspect = SmoothTerrainConfigs.GetInstance();
                    break;
                case TerrainOperationType.ClearHeight:
                case TerrainOperationType.DigHole:
                    _ObjectToInspect = DigTerrainConfig.GetInstance();
                    break;
                case TerrainOperationType.PaintWeight:
                    _ObjectToInspect = PaintTerrainConfigs.GetInstance();
                    break;
            }
            UpdateInspector();
            if (TerrainOperationType != TerrainOperationType.CreateTerrain &&
                TerrainOperationType != TerrainOperationType.ImportTerrain)
            {
                GameToolBarUI.GetInstance().ComboBoxModeSetEditorMode(EditorMode.Terrain_Mode);
            }
        }

        void UpdateBorderCheckedStates()
        {
            TerrainOperationType CurrentTerrainOperationType = TerrainEditor.GetInstance()._TerrainOperationType;
            foreach (Button ButtonOperation in _OperationButtons)
            {
                TerrainOperationType TerrainOperationType = (TerrainOperationType)ButtonOperation.GetTagInt1();
                if (TerrainOperationType == CurrentTerrainOperationType)
                {
                    ButtonOperation.SetBorderChecked(true);
                    ButtonOperation.SetNormalColor(Color.EDITOR_UI_ACTIVE_TOOL_OR_MENU);
                }
                else
                {
                    ButtonOperation.SetBorderChecked(false);
                    ButtonOperation.SetNormalColor(Color.FromRGBA(0, 0, 0, 0));
                }
            }
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bSizeChanged)
            {
                int SPAN_Y = 8;
                int Width = _Container.GetWidth();
                int Height = _Container.GetHeight();
                int Y = SPAN_Y;
                UpdateButtonContainer(Width, ref Y);
                Y += SPAN_Y;
                int AvaliableWidth = Width - SPAN_X * 2;
                int Height1 = Height - Y - SPAN_Y;
                _ScrollView.SetPosition(SPAN_X, Y, AvaliableWidth, Height1);
                UpdateLayout();
            }
        }

        public void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }

        public void UpdateButtonContainer(int Width, ref int Y)
        {
            int Line = 0;
            foreach (Button Button in _OperationButtons)
            {
                _ButtonContainer.FloatToLeft(Button);
                Button.SetY(Line * 70 + 3);
                if (Button.GetEndX() > Width - SPAN_X)
                {
                    _ButtonContainer.ClearLastLayout();
                    _ButtonContainer.FloatToLeft(Button);
                    Line++;
                    Button.SetY(Line * 70 + 3);
                }
            }

            int ContainerHeight = (Line + 1) * 70;
            _ButtonContainer.SetPosition(0, Y, Width, ContainerHeight);
            Y += ContainerHeight;

            _ButtonContainer.ClearLastLayout();
        }

        void ShowTerrainDirectoryExistsTipDialog(string TerrainDirectory)
        {
            string Tips = string.Format("Target terrain directory already exists: \"{0}\".", TerrainDirectory);
            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
        }

        string CalculateTerrainDirectory()
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string SceneFilename = EditorScene.GetInstance().GetCurrentSceneFilename();
            if (SceneFilename == "")
            {
                SceneFilename = ProjectDirectory + "/Contents/NewScene.world";
            }
            string TerrainDirectory = SceneFilename.Replace(".world", "") + "/Terrain";
            return TerrainDirectory;
        }

        string CalculateTerrainAssetDirectory(string TerrainDirectory)
        {
            string TerrainFilename = TerrainDirectory + "/TerrainAssets";
            return TerrainFilename;
        }

        string CalculateTerrainFilename(string TerrainDirectory)
        {
            string TerrainFilename = TerrainDirectory + "/Terrain.terrain";
            return TerrainFilename;
        }

        string CalculateTerrainNdaFilename(string TerrainDirectory)
        {
            string TerrainNdaFilename = TerrainDirectory + "/Terrain.nda";
            return TerrainNdaFilename;
        }

        Entity CreateTerrainEntity(string TerrainNdaFilename)
        {
            Entity TerrainEntity = HierarchyUI.GetInstance().CreateTerrainEntity(true);
            Transform TransformComponent = TerrainEntity.GetTransformComponent();
            double DefaultScaleD = DefaultScale;
            TransformComponent.Scale = new Double3(DefaultScaleD, DefaultScaleD, DefaultScaleD);
            TerrainComponent TerrainComponent = TerrainEntity.GetTerrainComponent();
            string TerrainNdaFilename1 = EditorUtilities.EditorFilenameToStandardFilename(TerrainNdaFilename);
            string TerrainNdaGuid = ResourceManager.Instance().ConvertPathToGuid(TerrainNdaFilename1);
            TerrainComponent.TerrainPath = TerrainNdaGuid;
            return TerrainEntity;
        }

        void RemoveTerrainAssetsDirectory(string TerrainAssetsDirectory)
        {
            DirectoryHelper.DeleteDirectory(TerrainAssetsDirectory);
        }

        void DeleteTerrainFile(string TerrainFilename)
        {
            FileHelper.DeleteFile(TerrainFilename);
        }

        public void CreateTerrain()
        {
            Entity CurrentTerrainEntity = TerrainEditor.GetInstance().SearchTerrainEntity();
            if (CurrentTerrainEntity != null)
            {
                string Tips = string.Format("There is already a terrain entity: \"{0}\".", CurrentTerrainEntity.GetCascadeName());
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string TerrainDirectory = CalculateTerrainDirectory();
            string TerrainAssetsDirectory = CalculateTerrainAssetDirectory(TerrainDirectory);
            string TerrainFilename = CalculateTerrainFilename(TerrainDirectory);
            string TerrainNdaFilename = CalculateTerrainNdaFilename(TerrainDirectory);
            CreateTerrainConfigs CreateTerrainConfigs = CreateTerrainConfigs.GetInstance();
            if (CreateTerrainConfigs.GridSizeX < 1 || CreateTerrainConfigs.GridSizeY < 1)
            {
                string Tips = string.Format("Grid size X/Y have to greater than 1.");
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            if (TerrainDirectory.Contains("/NewScene/Terrain") || IsTerrainTest())
            {
                DirectoryHelper.DeleteDirectory(TerrainDirectory);
            }
            if (DirectoryHelper.IsDirectoryExists(TerrainDirectory))
            {
                ShowTerrainDirectoryExistsTipDialog(TerrainDirectory);
                return;
            }
            CreateTerrain(TerrainAssetsDirectory, TerrainFilename, CreateTerrainConfigs);
            ProjectUI.GetInstance().ImportTerrain(TerrainFilename, TerrainNdaFilename);
            RemoveTerrainAssetsDirectory(TerrainAssetsDirectory);
            DeleteTerrainFile(TerrainFilename);
            CreateTerrainEntity(TerrainNdaFilename);
        }

        int TerrainBlockSizesToInt(TerrainBlockSizes TerrainBlockSizes)
        {
            return MathHelper.ParseInt(TerrainBlockSizes.ToString().Substring(2));
        }

        int TerrainTileSizesToInt(TerrainTileSizes TerrainTileSizes)
        {
            return MathHelper.ParseInt(TerrainTileSizes.ToString().Substring(2));
        }

        void CreateTerrainJson(string TerrainFilename, string TerrainAssetsDirectory, int GridSizeX, int GridSizeY, int BlockSize, int TileSize)
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string TerrainConfig = "";
            TerrainConfig += string.Format("{{\r\n");
            TerrainConfig += string.Format("  \"SurfaceType\": {0},\r\n", (int)SurfaceType.Flat);
            TerrainConfig += string.Format("  \"GridSizeX\": {0},\r\n", GridSizeX);
            TerrainConfig += string.Format("  \"GridSizeY\": {0},\r\n", GridSizeY);
            TerrainConfig += string.Format("  \"BlockSize\": {0},\r\n", BlockSize);
            TerrainConfig += string.Format("  \"TileSize\": {0},\r\n", TileSize);
            TerrainConfig += string.Format("  \"TileRootPath\": \"{0}/\",\r\n", TerrainAssetsDirectory);
            TerrainConfig += string.Format("  \"AlbedoTexturePrefix\": \"AT/AT\",\r\n");
            TerrainConfig += string.Format("  \"HeightmapPrefix\": \"HM/HM\",\r\n");
            TerrainConfig += string.Format("  \"WeightTexturePrefix\": \"WT/WT\",\r\n");
            TerrainConfig += string.Format("  \"HasCollision\": true,\r\n");
            if (IsTerrainTest())
            {
                string TerrainResourceDirectory = EditorUtilities.GetResourceDirectory() + "/EngineResource/Terrain/Textures";
                TerrainConfig += string.Format("  \"BaseColorTextures\": [\r\n");
                TerrainConfig += string.Format("      \"{0}/Layer0_Grass_Albedo.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer1_Rock_Albedo.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer2_RockA_Albedo.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer3_Ground_Albedo.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer4_GroundWet_Albedo.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer5_RockSuishi_Albedo.nda\"\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("  ],\r\n");
                TerrainConfig += string.Format("  \"NormalTextures\": [\r\n");
                TerrainConfig += string.Format("      \"{0}/Layer0_Grass_Normal.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer1_Rock_Normal.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer2_RockA_Normal.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer3_Ground_Normal.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer4_GroundWet_Normal.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/Layer5_RockSuishi_Normal.nda\"\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("  ],\r\n");
                TerrainConfig += string.Format("  \"HMRATextures\": [\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\"\r\n");
                TerrainConfig += string.Format("  ]\r\n");
            }
            else
            {
                string TerrainResourceDirectory = EditorUtilities.GetResourceDirectory() + "/EngineResource/Terrain";
                TerrainConfig += string.Format("  \"BaseColorTextures\": [\r\n");
                TerrainConfig += string.Format("      \"{0}/BaseColorTexture0.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/BaseColorTexture1.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/BaseColorTexture0.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/BaseColorTexture1.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/BaseColorTexture0.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/BaseColorTexture1.nda\"\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("  ],\r\n");
                TerrainConfig += string.Format("  \"NormalTextures\": [\r\n");
                TerrainConfig += string.Format("      \"{0}/NormalTexture0.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/NormalTexture1.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/NormalTexture0.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/NormalTexture1.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/NormalTexture0.nda\",\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("      \"{0}/NormalTexture1.nda\"\r\n", TerrainResourceDirectory);
                TerrainConfig += string.Format("  ],\r\n");
                TerrainConfig += string.Format("  \"HMRATextures\": [\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\",\r\n");
                TerrainConfig += string.Format("      \"\"\r\n");
                TerrainConfig += string.Format("  ]\r\n");
            }
            TerrainConfig += string.Format("}}\r\n");
            File.WriteAllText(TerrainFilename, TerrainConfig);
        }

        void UpdateProjectUI()
        {
            ProjectUI.GetInstance().UpdateAll();
        }

        void CreateTerrain(string TerrainAssetsDirectory, string TerrainFilename, CreateTerrainConfigs CreateTerrainConfigs)
        {
            DirectoryHelper.CreateDirectory(TerrainAssetsDirectory);
            DirectoryHelper.CreateDirectory(TerrainAssetsDirectory + "/HM");
            DirectoryHelper.CreateDirectory(TerrainAssetsDirectory + "/WT");

            int GridSizeX = CreateTerrainConfigs.GridSizeX;
            int GridSizeY = CreateTerrainConfigs.GridSizeY;
            int BlockSize = TerrainBlockSizesToInt(CreateTerrainConfigs.BlockSize);
            int TileSize = TerrainTileSizesToInt(CreateTerrainConfigs.TileSize);

            int TileSize1 = TileSize + 1;

            int Level = 0;
            for (int GridX = 0; GridX < GridSizeX; GridX++)
            {
                for (int GridY = 0; GridY < GridSizeY; GridY++)
                {
                    for (int TileX = 0; TileX < BlockSize; TileX++)
                    {
                        for (int TileY = 0; TileY < BlockSize; TileY++)
                        {
                            string HeightmapFilename = string.Format("{0}/HM/HM_{1}_{2}_{3}_{4}_{5}.png", TerrainAssetsDirectory, GridX, GridY, Level, TileX, TileY);
                            Clicross.ResourceUtil.Editor_CreateImage(HeightmapFilename, TileSize1, TileSize1, DefaultNormalAndDefaultHeight);

                            string Weightmap0Filename = string.Format("{0}/WT/WT0_{1}_{2}_{3}_{4}_{5}.png", TerrainAssetsDirectory, GridX, GridY, Level, TileX, TileY);
                            Clicross.ResourceUtil.Editor_CreateImage(Weightmap0Filename, TileSize1, TileSize1, 0xFF0000FF);

                            string Weightmap1Filename = string.Format("{0}/WT/WT1_{1}_{2}_{3}_{4}_{5}.png", TerrainAssetsDirectory, GridX, GridY, Level, TileX, TileY);
                            Clicross.ResourceUtil.Editor_CreateImage(Weightmap1Filename, TileSize1, TileSize1, 0xFF000000);
                        }
                    }
                }
            }

            CreateTerrainJson(TerrainFilename, TerrainAssetsDirectory, GridSizeX, GridSizeY, BlockSize, TileSize);

            UpdateProjectUI();
        }

        public string ResourceGuidToEditorFilename(string ResourceGuid)
        {
            if (ResourceGuid == "")
            {
                return "";
            }
            string Filename = ResourceManager.Instance().ConvertGuidToPath(ResourceGuid);
            if (Filename == "")
            {
                return "";
            }
            string Filename1 = EditorUtilities.StandardFilenameToEditorFilename(Filename);
            return Filename1;
        }

        public void ImportTerrain()
        {
            Entity CurrentTerrainEntity = TerrainEditor.GetInstance().SearchTerrainEntity();
            if (CurrentTerrainEntity != null)
            {
                string Tips = string.Format("There is already a terrain entity: \"{0}\".", CurrentTerrainEntity.GetCascadeName());
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string TerrainDirectory = CalculateTerrainDirectory();
            string TerrainAssetsDirectory = CalculateTerrainAssetDirectory(TerrainDirectory);
            string TerrainFilename = CalculateTerrainFilename(TerrainDirectory);
            string TerrainNdaFilename = CalculateTerrainNdaFilename(TerrainDirectory);
            ImportTerrainConfigs ImportTerrainConfigs = ImportTerrainConfigs.GetInstance();
            if (TerrainDirectory.Contains("/NewScene/Terrain") || IsTerrainTest())
            {
                DirectoryHelper.DeleteDirectory(TerrainDirectory);
            }
            if (DirectoryHelper.IsDirectoryExists(TerrainDirectory))
            {
                ShowTerrainDirectoryExistsTipDialog(TerrainDirectory);
                return;
            }
            string Heightmap = ResourceGuidToEditorFilename(ImportTerrainConfigs.Heightmap);
            if (Heightmap == "")
            {
                string Tips = string.Format("Heightmap not specified.");
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            if (FileHelper.IsFileExists(Heightmap) == false)
            {
                string Tips = string.Format("Heightmap \"{0}\" not exists.", Heightmap);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            List<string> WeightmapList = ImportTerrainConfigs.WeightmapList;
            if (WeightmapList.Count > MaxLayersSupported)
            {
                string Tips = string.Format("Weightmap count must < 8.");
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            foreach (string Weightmap1 in WeightmapList)
            {
                string Weightmap = ResourceGuidToEditorFilename(Weightmap1);
                if (Weightmap == "" || FileHelper.IsFileExists(Heightmap) == false)
                {
                    string Tips = string.Format("Weightmap \"{0}\" not exists.", Weightmap);
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                    return;
                }
            }
            ImportTerrain(TerrainAssetsDirectory, TerrainFilename, ImportTerrainConfigs);
            ProjectUI.GetInstance().ImportTerrain(TerrainFilename, TerrainNdaFilename);
            RemoveTerrainAssetsDirectory(TerrainAssetsDirectory);
            DeleteTerrainFile(TerrainFilename);
            Entity TerrainEntity = CreateTerrainEntity(TerrainNdaFilename);
            if (IsTerrainTest() && ImportTerrainConfigs.Heightmap == "Contents/GreatWallTerrainResource/Heightmap.png")
            {
                Transform Transform = TerrainEntity.GetTransformComponent();
                Transform.Translation = new Double3(-228950, 10180, -189450);
                Transform.Scale = new Double3(92.0, 82.43, 92.0);
            }
        }

        int CalculateBlockCount(int TotalSize, int BlockSize)
        {
            int BlockCount = TotalSize / BlockSize;
            if ((TotalSize % BlockSize) != 0)
            {
                BlockCount++;
            }
            return BlockCount;
        }

        unsafe uint* GetWeightmapPointer(List<IntPtr> WeightmapDataList, int WeightTextureIndex, int SubIndex)
        {
            int Index = WeightTextureIndex * LayersPerWeightTexture + SubIndex;
            if (Index < WeightmapDataList.Count)
            {
                return (uint*)WeightmapDataList[Index];
            }
            return null;
        }

        unsafe uint GetWeightOfWeightmap(uint* WeightmapPointer, int Index, uint DefaultWeight)
        {
            if (WeightmapPointer != null)
            {
                return WeightmapPointer[Index] & 0x000000FF;
            }
            return DefaultWeight;
        }

        unsafe void ImportTerrain(string TerrainAssetsDirectory, string TerrainFilename, ImportTerrainConfigs ImportTerrainConfigs)
        {
            DirectoryHelper.CreateDirectory(TerrainAssetsDirectory);
            DirectoryHelper.CreateDirectory(TerrainAssetsDirectory + "/HM");
            DirectoryHelper.CreateDirectory(TerrainAssetsDirectory + "/WT");

            int BlockSize = TerrainBlockSizesToInt(ImportTerrainConfigs.BlockSize);
            int TileSize = TerrainTileSizesToInt(ImportTerrainConfigs.TileSize);

            string Heightmap = ResourceGuidToEditorFilename(ImportTerrainConfigs.Heightmap);

            string Heightmap1 = Heightmap + ".png";
            bool bSuccess = CEAssetPipeline.ConvertHeightmap(Heightmap, Heightmap1);
            if (!bSuccess)
            {
                return;
            }
            int Width1 = 0;
            int Height1 = 0;
            Clicross.ImageInfo image = Clicross.ResourceUtil.Editor_LoadImage(Heightmap1);
            IntPtr HeightmapData = image.Data;
            Width1 = image.Width;
            Height1 = image.Height;
            FileHelper.DeleteFile(Heightmap1);
            uint* HeightmapPointer = (uint*)HeightmapData;

            List<string> WeightmapList = ImportTerrainConfigs.WeightmapList;
            List<IntPtr> WeightmapDataList = new List<IntPtr>();
            foreach (string Weightmap1 in WeightmapList)
            {
                string Weightmap = ResourceGuidToEditorFilename(Weightmap1);
                int Width2 = 0;
                int Height2 = 0;
                Clicross.ImageInfo image2 = Clicross.ResourceUtil.Editor_LoadImage(Heightmap1);
                Width2 = image2.Width;
                Height2 = image2.Height;
                IntPtr WeightmapData = image2.Data;
                DebugHelper.Assert(Width2 == Width1);
                DebugHelper.Assert(Height2 == Height1);
                WeightmapDataList.Add(WeightmapData);
            }

            int TileSize1 = TileSize + 1;
            int Width = Width1 - 1;
            int Height = Height1 - 1;
            int BlockSize1 = BlockSize * TileSize;
            int GridSizeX = CalculateBlockCount(Width, BlockSize1);
            int GridSizeY = CalculateBlockCount(Height, BlockSize1);

            IntPtr TileImageData = Clicross.ResourceUtil.Editor_CreateImageBuffer(TileSize1, TileSize1);
            uint* TileImageDataPointer = (uint*)TileImageData;

            int Level = 0;
            for (int GridX = 0; GridX < GridSizeX; GridX++)
            {
                for (int GridY = 0; GridY < GridSizeY; GridY++)
                {
                    for (int TileX = 0; TileX < BlockSize; TileX++)
                    {
                        for (int TileY = 0; TileY < BlockSize; TileY++)
                        {
                            int TileIndexX = GridX * BlockSize + TileX;
                            int TileIndexY = GridY * BlockSize + TileY;
                            int X1 = TileIndexX * TileSize;
                            int Y1 = TileIndexY * TileSize;

                            string HeightmapFilename = string.Format("{0}/HM/HM_{1}_{2}_{3}_{4}_{5}.png", TerrainAssetsDirectory, GridX, GridY, Level, TileX, TileY);
                            if (HeightmapPointer != null)
                            {
                                for (int i = 0; i < TileSize1; i++)
                                {
                                    for (int j = 0; j < TileSize1; j++)
                                    {
                                        int IndexInTile = i * TileSize1 + j;
                                        int X2 = X1 + j;
                                        int Y2 = Y1 + i;
                                        if (X2 < Height1 && Y2 < Width1)
                                        {
                                            int Index = (Height1 - Y2 - 1) * Width1 + X2;
                                            //int Index = X2 * Width1 + Y2;
                                            uint TerrainHeight = HeightmapPointer[Index];
                                            byte* TerrainHeightPointer = (byte*)&TerrainHeight;
                                            byte Blue = TerrainHeightPointer[2];
                                            byte Alpha = TerrainHeightPointer[3];
                                            float NormalX = 2.0f / 255.0f * Blue - 1.0f;
                                            float NormalZ = 2.0f / 255.0f * Alpha - 1.0f;
                                            //NormalX *= -1;
                                            NormalZ *= -1;
                                            byte Blue1 = (byte)(Math.Clamp(127.5f * (NormalX + 1.0f) + 0.5f, 0.0f, 255.0f));
                                            byte Alpha1 = (byte)(Math.Clamp(127.5f * (NormalZ + 1.0f) + 0.5f, 0.0f, 255.0f));
                                            TerrainHeightPointer[2] = Blue1;
                                            TerrainHeightPointer[3] = Alpha1;
                                            TileImageDataPointer[IndexInTile] = TerrainHeight;
                                        }
                                        else
                                        {
                                            TileImageDataPointer[IndexInTile] = HoleHeight;
                                        }
                                    }
                                }
                                Clicross.ResourceUtil.Editor_SaveImage(HeightmapFilename, TileSize1, TileSize1, TileImageData);
                            }

                            if (WeightmapDataList.Count > 0)
                            {
                                int WeightTextureCount = CalculateBlockCount(WeightmapDataList.Count, LayersPerWeightTexture);
                                for (int WeightTextureIndex = 0; WeightTextureIndex < WeightTextureCount; WeightTextureIndex++)
                                {
                                    uint* WeightmapPointer0 = GetWeightmapPointer(WeightmapDataList, WeightTextureIndex, 0);
                                    uint* WeightmapPointer1 = GetWeightmapPointer(WeightmapDataList, WeightTextureIndex, 1);
                                    uint* WeightmapPointer2 = GetWeightmapPointer(WeightmapDataList, WeightTextureIndex, 2);
                                    uint DefaultWeightR = 0x00000000;
                                    if (WeightTextureIndex == 0)
                                    {
                                        DefaultWeightR = 0x000000FF;
                                    }
                                    string WeightmapFilename = string.Format("{0}/WT/WT{1}_{2}_{3}_{4}_{5}_{6}.png", TerrainAssetsDirectory, WeightTextureIndex, GridX, GridY, Level, TileX, TileY);
                                    for (int i = 0; i < TileSize1; i++)
                                    {
                                        for (int j = 0; j < TileSize1; j++)
                                        {
                                            int IndexInTile = i * TileSize1 + j;
                                            int X2 = X1 + j;
                                            int Y2 = Y1 + i;
                                            if (X2 < Height1 && Y2 < Width1)
                                            {
                                                int Index = (Height1 - Y2 - 1) * Width1 + X2;
                                                //int Index = X2 * Width1 + Y2;
                                                uint R = GetWeightOfWeightmap(WeightmapPointer0, Index, DefaultWeightR);
                                                uint G = GetWeightOfWeightmap(WeightmapPointer1, Index, 0x00000000);
                                                uint B = GetWeightOfWeightmap(WeightmapPointer2, Index, 0x00000000);
                                                uint A = 0x000000FF;
                                                TileImageDataPointer[IndexInTile] = R | (G << 8) | (B << 16) | (A << 24);
                                            }
                                            else
                                            {
                                                TileImageDataPointer[IndexInTile] = 0xFF00000;
                                            }
                                        }
                                    }
                                    Clicross.ResourceUtil.Editor_SaveImage(WeightmapFilename, TileSize1, TileSize1, TileImageData);
                                }
                            }
                            else
                            {
                                string Weightmap0Filename = string.Format("{0}/WT/WT0_{1}_{2}_{3}_{4}_{5}.png", TerrainAssetsDirectory, GridX, GridY, Level, TileX, TileY);
                                Clicross.ResourceUtil.Editor_CreateImage(Weightmap0Filename, TileSize1, TileSize1, 0xFF0000FF);

                                string Weightmap1Filename = string.Format("{0}/WT/WT1_{1}_{2}_{3}_{4}_{5}.png", TerrainAssetsDirectory, GridX, GridY, Level, TileX, TileY);
                                Clicross.ResourceUtil.Editor_CreateImage(Weightmap1Filename, TileSize1, TileSize1, 0xFF000000);
                            }
                        }
                    }
                }
            }

            foreach (IntPtr WeightmapData in WeightmapDataList)
            {
                Clicross.ResourceUtil.Editor_FreeImage(WeightmapData);
            }
            Clicross.ResourceUtil.Editor_FreeImage(TileImageData);
            Clicross.ResourceUtil.Editor_FreeImage(HeightmapData);

            CreateTerrainJson(TerrainFilename, TerrainAssetsDirectory, GridSizeX, GridSizeY, BlockSize, TileSize);

            UpdateProjectUI();
        }

        void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (_ScrollView.GetVisible_Recursively() && _ScrollView.IsPointIn_Recursively(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (ProjectUI.IsPathesDragging())
                {
                    List<string> PathesDragged = ProjectUI.GetPathesDragged();
                    DropPathOnInspector(_Inspector, MouseX, MouseY, PathesDragged);
                }
            }
        }

        bool DropPathOnInspector(Inspector Inspector, int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (Inspector == null)
            {
                return false;
            }
            List<Inspector> ChildInspectors = Inspector.GetChildInspectors();
            foreach (Inspector ChildInspector in ChildInspectors)
            {
                if (DropPathOnInspector(ChildInspector, MouseX, MouseY, PathesDragged))
                {
                    return true;
                }
            }
            return Inspector.OnDropPathes(MouseX, MouseY, PathesDragged);
        }
    }
}
