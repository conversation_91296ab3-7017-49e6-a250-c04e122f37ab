using Clicross;
using EditorUI;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class InputKeyModifier : InputKeyImplement
    {
        // Excluded properties will not be displayed in InputActionKeyDrawer
        private readonly List<string> _ExcludedProperties = ["type", "ModifierName"];

        public InputKeyModifier(InputKeyItem parentKey, int idx, Control parentContainer) : base(parentKey, idx, parentContainer)
        {
            Init();
        }

        public override void Init()
        {
            InputKeyModifierItem modifier = _ParentKeyItem.GetModifier(_VectorIndex);
            string displayName = modifier.GetType().Name;
            _NameBar.SetText(displayName);

            Childs.ForEach(child => { _ParentScrollView.RemoveChild(child.Panel); });
            Childs.Clear();

            var properties = modifier.GetType().GetProperties().Where(e => !_ExcludedProperties.Contains(e.Name));
            foreach (var property in properties)
            {
                InputActionKeyDrawer drawer = new(property.Name, property.PropertyType, _ParentScrollView);
                drawer.BindDrawerObj(modifier);
                Childs.Add(drawer);
            }

            InitExpand();
        }
    }
}