
using EditorUI;
using System;
using System.ComponentModel;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{

    public enum KeyDrawerType
    {
        Int,
        Real,
        String,
        Bool,
        Enum
    }

    public class InputActionKeyDrawer
    {
        private object mBindObj;
        private KeyDrawerType mType;
        private Type mBindType;
        string Name;
        private Label NameLabel;
        private Control ControlBox;
        public Panel Panel;
        private PropertyInfo propertyInfo;
        private const int BAR_HEIGHT = 24;

        public InputActionKeyDrawer(string name, Type type, Control parentPanel)
        {
            mBindType = type;
            mType = GetDrawerType(type);
            Name = name;
            Init(name);

            parentPanel.AddChild(Panel);
        }

        public void Init(string name)
        {
            Panel = new Panel();
            Panel.SetSize(500, 24);
            Panel.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);

            NameLabel = new Label();
            NameLabel.Initialize();
            NameLabel.SetText(name);
            NameLabel.SetFontSize(20);
            NameLabel.SetTextAlign(TextAlign.CenterCenter);
            NameLabel.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            NameLabel.SetBorderColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            NameLabel.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            int nameLen = NameLabel.CalculateTextWidth() + 10;
            NameLabel.SetPosition(0, 0, nameLen, BAR_HEIGHT);

            switch (mType)
            {
                case KeyDrawerType.Int:
                case KeyDrawerType.Real:
                case KeyDrawerType.String:
                case KeyDrawerType.Enum: // TODO(hendrikwang): Enum type should use a dropdown
                    ControlBox = new Edit();
                    (ControlBox as Edit).Initialize(EditMode.Simple_SingleLine);
                    ControlBox.SetBackgroundColor(Color.FromRGB(50, 50, 50));
                    ControlBox.CharInputEvent += (Control Sender, char Char, ref bool bContinue) =>
                    {
                        if (Char == '\r')
                            SyncBindObj();
                    };
                    ControlBox.FocusChangedEvent += (Control Sender) =>
                    {
                        SyncBindObj();
                    };
                    break;
                case KeyDrawerType.Bool:
                    ControlBox = new Check();
                    Check _CheckValue = (Check)ControlBox;
                    _CheckValue.Initialize();
                    _CheckValue.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
                    _CheckValue.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
                    _CheckValue.SetAutoCheck(true);
                    _CheckValue.ClickedEvent += OnCheckValueClicked;
                    break;
            }

            int controlBoxStartX = nameLen + 15;
            ControlBox.SetPosition(controlBoxStartX, 2, 300, BAR_HEIGHT - 4);

            Panel.AddChild(NameLabel);
            Panel.AddChild(ControlBox);
        }

        public void BindDrawerObj(object bindObj)
        {
            mBindObj = bindObj;
            Type objType = mBindObj.GetType();
            propertyInfo = objType.GetProperty(Name);

            UpdateControlBox();
        }

        private void UpdateControlBox()
        {
            ControlBox.SetText(propertyInfo.GetValue(mBindObj).ToString());
        }

        private void SyncBindObj()
        {
            try
            {
                switch (mType)
                {
                    case KeyDrawerType.Int:
                        propertyInfo.SetValue(mBindObj, int.Parse(ControlBox.GetText()));
                        break;
                    case KeyDrawerType.Real:
                        propertyInfo.SetValue(mBindObj, float.Parse(ControlBox.GetText()));
                        break;
                    case KeyDrawerType.String:
                        propertyInfo.SetValue(mBindObj, ControlBox.GetText());
                        break;
                    case KeyDrawerType.Bool:
                        propertyInfo.SetValue(mBindObj, bool.Parse(ControlBox.GetText()));
                        break;
                    case KeyDrawerType.Enum:
                        propertyInfo.SetValue(mBindObj, Enum.Parse(mBindType, ControlBox.GetText()));
                        break;
                }
            }
            catch { }
        }

        void OnCheckValueClicked(Check Sender)
        {
            NameLabel.SetText("OK!");
        }

        KeyDrawerType GetDrawerType(Type type)
        {
            if (type == typeof(int) || type == typeof(short) || type == typeof(long) || type == typeof(uint) || type == typeof(ushort) || type == typeof(ulong))
            {
                return KeyDrawerType.Int;
            }
            else if (type == typeof(float) || type == typeof(double))
            {
                return KeyDrawerType.Real;
            }
            else if (type == typeof(string) || type == typeof(char))
            {
                return KeyDrawerType.String;
            }
            else if (type == typeof(bool))
            {
                return KeyDrawerType.Bool;
            }
            else if (type.IsEnum)
            {
                return KeyDrawerType.Enum;
            }
            else
            {
                EditorLogger.Log(LogMessageType.Warning, $"Unsupported mapping of type {type} to KeyDrawerType!");
                return KeyDrawerType.String;
            }
        }
    }
}
