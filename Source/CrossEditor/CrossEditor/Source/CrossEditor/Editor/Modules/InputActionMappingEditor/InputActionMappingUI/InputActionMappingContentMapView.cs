
using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{

    public class InputActionMappingContentMapView
    {
        class InputKeyButton
        {
            public Button key;
            public Button delete;
            public Check check;
            public InputKeyModifierManager modifier;
            //public bool mExpand = false;    //modifier expand
            public InputKeyTriggerManager trigger;
        }
        class MixedButton
        {
            public Button left;
            public Button middle;
            public Button right;
            public List<InputKeyButton> childs = new List<InputKeyButton>();
            public bool expand = false;
        }

        int _RowHeight = 30;
        private VContainer _ParentContainer;
        OperationBarUI _OperationBarUI = new OperationBarUI();
        SearchUI _SearchUI = new SearchUI();
        ScrollView _ScrollView = new ScrollView();
        Panel _ScrollPanel;
        Button _ButtonAdd;
        object _InputMap;
        object _AllKeys;
        Type _DictionaryType;
        PropertyInfo _PropertyInfo_Count;
        MethodInfo _MethodInfo_GetMethod;
        MethodInfo _MethodInfo_InsertMethod;
        MethodInfo _MethodInfo_EraseMethod;
        MethodInfo _MethodInfo_ContainsMethod;
        MethodInfo _MethodInfo_GetKeys;
        Type _ItemType;

        List<MixedButton> _InputActions = new List<MixedButton>();
        //InputMapItem _InputMapItem;

        private List<string> _ActionValueTypeList = new List<string> { "Bool", "Axis1D", "Axis2D", "Axis3D" };

        public InputActionMappingContentMapView()
        {
            _OperationBarUI.Initialize();
            _OperationBarUI.GetPanelBar().SetText("Details");
            _OperationBarUI.GetPanelBar().SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI.GetPanelBar().SetFontSize(20);
            _OperationBarUI.GetPanelBar().SetTextOffsetX(10);

            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 275, 25);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);

            _ButtonAdd = new Button();
            _ButtonAdd.Initialize();
            _ButtonAdd.SetFontSize(20);
            _ButtonAdd.SetText("+");
            _ButtonAdd.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonAdd.SetToolTips("Add Item");
            _ButtonAdd.SetPosition(0, 2, _RowHeight, 20);
            _ButtonAdd.ClickedEvent += CreateNewInputAction;
            _OperationBarUI.AddRight(_ButtonAdd);

            _ScrollView.Initialize();
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            //_ScrollView.SetPosition(5, 28, ScrollView.SCROLL_BAR_SIZE, 367);
            _ScrollView.GetHScroll().SetEnable(true);
            _ScrollView.GetVScroll().SetEnable(true);
            _ScrollView.PositionChangedEvent += OnScrollViewChanged;
            _ScrollPanel = _ScrollView.GetScrollPanel();
        }

        public void BindProperty(object inputMapping, object allKeys, string DictName)
        {
            _OperationBarUI.GetPanelBar().SetText(DictName);
            _InputMap = inputMapping;
            _AllKeys = allKeys;
            _DictionaryType = _InputMap.GetType();        //std::map<std::string, InputMapItem>
            _PropertyInfo_Count = _DictionaryType.GetProperty("Count");
            _MethodInfo_GetMethod = _DictionaryType.GetMethod("get_Item");
            _MethodInfo_InsertMethod = _DictionaryType.GetMethods().Single(x => x.Name == "Insert" && x.GetParameters().Length == 2);
            _MethodInfo_EraseMethod = _DictionaryType.GetMethods().Single(x => x.Name == "Erase" && x.GetParameters().Length == 1 && x.GetParameters()[0].ParameterType == typeof(string));
            _MethodInfo_ContainsMethod = _DictionaryType.GetMethod("ContainsKey");
            _MethodInfo_GetKeys = _DictionaryType.GetMethod("Keys");
            _ItemType = _MethodInfo_InsertMethod.GetParameters()[1].ParameterType;  //InputMapItem  -> std::vector<std::string> InputKeys;
            RegenerateInputActions();
        }

        public void OnAddToParent(VContainer mContainer)
        {
            _ParentContainer = mContainer;
            _ParentContainer.AddFixedChild(_OperationBarUI.GetPanelBar());
            _ParentContainer.AddSizableChild(_ScrollView, 1);
        }

        protected int GetDictionaryCount(object Dictionary)
        {
            return (int)_PropertyInfo_Count.GetValue(Dictionary);
        }

        object GetDictionaryItem(object Dictionary, object key)
        {
            return _MethodInfo_GetMethod.Invoke(Dictionary, new object[] { key });
        }

        void InsertDictionaryItem(object Dictionary, object key, object value)
        {
            _MethodInfo_InsertMethod.Invoke(Dictionary, new object[] { key, value });
        }

        void RemoveDictionayItem(object Dictionary, object key)
        {
            _MethodInfo_EraseMethod.Invoke(Dictionary, new object[] { key });
        }

        bool ContainsDictionaryKey(object Dictionary, object key)
        {
            return (bool)_MethodInfo_ContainsMethod.Invoke(Dictionary, new object[] { key });
        }

        void RegenerateInputActions()
        {
            HashSet<string> expandSet = new HashSet<string>();
            foreach (var IA in _InputActions)
            {
                if (IA.expand)
                    expandSet.Add(IA.left.GetText());
            }
            _InputActions.Clear();

            var actions = (List<string>)_MethodInfo_GetKeys.Invoke(_InputMap, null);
            _ScrollPanel.ClearChildren();
            foreach (var actionName in actions)
            {
                MixedButton ItemMixedButton = new MixedButton();

                //Input Action Button
                Button ItemSelectButton = new Button();
                ItemSelectButton.Initialize();
                ItemSelectButton.SetTagString1(actionName);
                ItemSelectButton.SetFontSize(25);
                ItemSelectButton.SetText(actionName);
                ItemSelectButton.SetTextOffsetY(1);
                ItemSelectButton.SetTextOffsetX(30);
                ItemSelectButton.SetTextAlign(TextAlign.CenterLeft);
                ItemSelectButton.SetBorderColor(Color.FromRGBA(135, 135, 135, (int)byte.MaxValue));
                ItemSelectButton.ClickedEvent += ExpandInputAction;
                ItemMixedButton.left = ItemSelectButton;
                _ScrollPanel.AddChild(ItemMixedButton.left);

                //Value Type
                Button ValueTypeButton = new Button();
                ValueTypeButton.Initialize();
                ValueTypeButton.SetTagString1(actionName);
                ValueTypeButton.SetFontSize(25);
                InputMapItem item = (InputMapItem)GetDictionaryItem(_InputMap, actionName);
                ValueTypeButton.SetText(item.ValueType);
                ValueTypeButton.SetToolTips("Set Input Action Value Type");
                ValueTypeButton.SetTextOffsetY(1);
                ValueTypeButton.SetTextOffsetX(30);
                ValueTypeButton.SetTextAlign(TextAlign.CenterLeft);
                ValueTypeButton.SetBorderColor(Color.EDITOR_UI_GRAY_DRAW_COLOR);
                ValueTypeButton.ClickedEvent += CreateValueTypeMenu;
                ItemMixedButton.middle = ValueTypeButton;
                _ScrollPanel.AddChild(ItemMixedButton.middle);

                //Menu Button
                Button ItemMenuButton = new Button();
                ItemMenuButton.Initialize();
                ItemMenuButton.SetTagString1(actionName);
                ItemMenuButton.SetFontSize(20);
                ItemMenuButton.SetText("...");
                ItemMenuButton.SetToolTips("Operations");
                ItemMenuButton.SetTextOffsetY(1);
                ItemMenuButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                ItemMenuButton.ClickedEvent += OpenInputActionItemMenu;
                ItemMixedButton.right = ItemMenuButton;
                _ScrollPanel.AddChild(ItemMixedButton.right);

                //Input Keys Button
                InputMapItem mapItem = (InputMapItem)GetDictionaryItem(_InputMap, actionName);
                for (var it = mapItem.InputKeys.Begin(); it != mapItem.InputKeys.End(); ++it)
                {
                    InputKeyItem keyItem = it.Value;
                    InputKeyButton keyButton = new InputKeyButton();
                    keyButton.key = new Button();
                    keyButton.key.Initialize();
                    keyButton.key.SetTagString1(actionName);
                    keyButton.key.SetFontSize(20);
                    keyButton.key.SetText(keyItem.KeyName);
                    keyButton.key.SetTextColor(Color.EDITOR_UI_LIGHT_TEXT_COLOR);
                    keyButton.key.SetTextOffsetY(1);
                    keyButton.key.SetBorderColor(Color.FromRGBA(135, 135, 135, (int)byte.MaxValue));
                    keyButton.key.SetNormalColor(Color.FromRGB(70, 70, 70));
                    keyButton.key.ClickedEvent += ChooseInputKey;

                    keyButton.modifier = new InputKeyModifierManager();
                    keyButton.modifier.InputMappingRefreshEvent += RefreshInspectorUI;
                    keyButton.modifier.InputMappingReGenEvent += RegenerateInputActions;
                    keyButton.modifier.OnAddToParent(_ScrollPanel);
                    keyButton.modifier.Init(keyItem);

                    keyButton.trigger = new InputKeyTriggerManager();
                    keyButton.trigger.InputMappingRefreshEvent += RefreshInspectorUI;
                    keyButton.trigger.InputMappingReGenEvent += RegenerateInputActions;
                    keyButton.trigger.OnAddToParent(_ScrollPanel);
                    keyButton.trigger.Init(keyItem);

                    keyButton.check = new Check();
                    keyButton.check.Initialize();
                    keyButton.check.SetImageUnchecked(UIManager.LoadUIImage("Editor/Tree/Common/Folded.png"));
                    keyButton.check.SetImageChecked(UIManager.LoadUIImage("Editor/Tree/Common/NotFolded.png"));
                    keyButton.check.SetAutoCheck(true);
                    keyButton.check.SetChecked(true);
                    keyButton.check.ClickedEvent += (Check sender) =>
                    {
                        keyButton.check?.SetChecked(sender.GetChecked());
                        keyButton.modifier.SetVisible(sender.GetChecked());
                        keyButton.trigger.SetVisible(sender.GetChecked());
                        //keyButton.trigger.SetVisible(sender.GetChecked());

                        RefreshInspectorUI();
                    };

                    keyButton.delete = new Button();
                    keyButton.delete.Initialize();
                    keyButton.delete.SetTagString1(actionName);
                    keyButton.delete.SetName(keyItem.KeyName);
                    keyButton.delete.SetFontSize(20);
                    keyButton.delete.SetText("Delete");
                    keyButton.delete.SetTextColor(Color.EDITOR_UI_LIGHT_TEXT_COLOR);
                    keyButton.delete.SetTextOffsetY(1);
                    keyButton.delete.SetBorderColor(Color.FromRGBA(135, 135, 135, (int)byte.MaxValue));
                    keyButton.delete.SetNormalColor(Color.FromRGB(70, 70, 70));
                    keyButton.delete.ClickedEvent += DeleteInputKey;

                    ItemMixedButton.childs.Add(keyButton);
                    _ScrollPanel.AddChild(keyButton.key);
                    _ScrollPanel.AddChild(keyButton.check);
                    _ScrollPanel.AddChild(keyButton.delete);
                }

                if (expandSet.Contains(actionName))
                    ItemMixedButton.expand = true;

                _InputActions.Add(ItemMixedButton);
            }
            _SearchUI.TriggerSearchEvent();     //Refresh Inspector UI
        }

        void ExpandInputAction(Button Sender)
        {
            string inputActionName = Sender.GetTagString1();

            if (_InputActions != null)
            {
                _InputActions.ForEach((IAButton) =>
                {
                    if (IAButton.left.GetText() == inputActionName)
                    {
                        IAButton.expand = !IAButton.expand;
                    }
                });
            }
            RegenerateInputActions();
        }

        void CreateValueTypeMenu(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuItem mainContainerItem = new MenuItem();
            Panel Container = new Panel();
            Tree keyTree = new Tree();

            MenuContextMenu.Initialize();
            keyTree.Initialize();
            keyTree.SetTextAlign(TextAlign.TopLeft);
            keyTree.GetRootItem().SetExpanded(true);
            keyTree.SetPosition(2, 2, 300, 400);
            keyTree.ItemSelectedEvent += (tree, treeItem) =>
            {
                if (treeItem.GetText() != "")
                {
                    string inputActionName = Sender.GetTagString1();
                    InputMapItem item = (InputMapItem)GetDictionaryItem(_InputMap, inputActionName);
                    item.ValueType = treeItem.GetText();
                    RegenerateInputActions();
                }
                GetUIManager().GetContextMenu().HideMenu();
            };
            TreeItem treeRoot = keyTree.GetRootItem();
            treeRoot.ClearChildren();
            foreach (string category in _ActionValueTypeList)
            {
                TreeItem treeItem = keyTree.CreateItem();
                treeRoot.AddChild(treeItem);
                treeItem.SetFolder(true);
                treeItem.SetText(category);
            }
            Container.AddChild(keyTree);

            Container.SetSize(304, 400);
            mainContainerItem.SetControl(Container);
            MenuContextMenu.AddMenuItem(mainContainerItem);
            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        void OpenInputActionItemMenu(Button Sender)
        {
            string inputActionName = Sender.GetTagString1();
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuItem MenuItem_DeleteItem = new MenuItem();
            MenuItem_DeleteItem.SetText("Delete");
            MenuItem_DeleteItem.SetTagString(inputActionName);
            MenuItem_DeleteItem.ClickedEvent += DeleteInputAction;

            MenuItem MenuItem_Rename = new MenuItem();
            MenuItem_Rename.SetText("Rename");
            MenuItem_Rename.SetTagString(inputActionName);
            MenuItem_Rename.ClickedEvent += RenameInputAction;

            MenuItem MenuItem_Add = new MenuItem();
            MenuItem_Add.SetText("Add New Key");
            MenuItem_Add.SetTagString(inputActionName);
            MenuItem_Add.ClickedEvent += AddInputActionNewKey;

            MenuContextMenu.AddMenuItem(MenuItem_DeleteItem);
            MenuContextMenu.AddMenuItem(MenuItem_Rename);
            MenuContextMenu.AddMenuItem(MenuItem_Add);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        void DeleteInputAction(MenuItem MenuItem)
        {
            string inputActionName = MenuItem.GetTagString();
            RemoveDictionayItem(_InputMap, inputActionName);
            RegenerateInputActions();
        }

        void RenameInputAction(MenuItem MenuItem)
        {
            string inputActionName = MenuItem.GetTagString();
            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Rename Item", "Please input a New Name:", inputActionName);
            string pre_item_key = inputActionName;        //ѡ�е�key string
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                if (StringInputed == "")
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Item name can not be empty!");
                    return;
                }
                if (ContainsDictionaryKey(_InputMap, StringInputed))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Duplicated Item name: " + StringInputed);
                    return;
                }
                var newItem = NewItem();
                // newItem is ref of the value in dictionary, because the elements of dictionary is reference type
                newItem = GetDictionaryItem(_InputMap, pre_item_key);
                InsertDictionaryItem(_InputMap, StringInputed, newItem);
                RemoveDictionayItem(_InputMap, pre_item_key);
                CancelSelect();
                RegenerateInputActions();
            };
            TextInputUI.ShowDialog();
        }

        void AddInputActionNewKey(MenuItem MenuItem)
        {
            string inputActionName = MenuItem.GetTagString();
            InputMapItem item = (InputMapItem)GetDictionaryItem(_InputMap, inputActionName);
            item.InputKeys.Add(new InputKeyItem("null"));

            //Expand IA
            if (_InputActions != null)
            {
                _InputActions.ForEach((IAButton) =>
                {
                    if (IAButton.left.GetText() == inputActionName)
                    {
                        IAButton.expand = true;
                    }
                });
            }
            RegenerateInputActions();
        }

        void ChooseInputKey(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuItem mainContainerItem = new MenuItem();
            Panel Container = new Panel();
            Tree keyTree = new Tree();
            Edit searchBox = new Edit();

            MenuContextMenu.Initialize();
            keyTree.Initialize();
            keyTree.SetTextAlign(TextAlign.TopLeft);
            keyTree.GetRootItem().SetExpanded(true);
            keyTree.SetPosition(2, 2, 300, 400);
            keyTree.ItemSelectedEvent += (tree, treeItem) =>
            {
                if (treeItem.GetParent() != keyTree.GetRootItem())
                {
                    if (treeItem.GetText() != "")
                    {
                        string inputActionName = Sender.GetTagString1();
                        InputMapItem item = (InputMapItem)GetDictionaryItem(_InputMap, inputActionName);
                        for (int i = 0; i < item.InputKeys.Count; ++i)
                        {
                            if (item.InputKeys[i].KeyName == Sender.GetText())
                            {
                                item.InputKeys[i].KeyName = treeItem.GetText();
                                break;
                            }
                        }
                        RegenerateInputActions();
                    }
                }
                GetUIManager().GetContextMenu().HideMenu();
            };
            CreateTreeByVector("", keyTree);
            Container.AddChild(keyTree);

            int FontSize = 16;
            searchBox.SetBackgroundColor(Color.FromRGBA(30, 50, 76, 255));
            searchBox.SetFontSize(FontSize);
            searchBox.Initialize(EditMode.Simple_SingleLine);
            searchBox.SetWidth(300);
            searchBox.SetHeight(FontSize + 4);
            searchBox.SetPos(2, 2);
            searchBox.SetFocus();
            searchBox.TextChangedEvent += (Sender) =>
            {
                if (searchBox.GetText() == "")
                    CreateTreeByVector("", keyTree);
                else
                    CreateTreeByVector(searchBox.GetText(), keyTree);
            };
            Container.AddChild(searchBox);

            Container.SetSize(304, 400);
            mainContainerItem.SetControl(Container);
            MenuContextMenu.AddMenuItem(mainContainerItem);
            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);

        }

        void CreateTreeByVector(string searchKey, Tree nodeTree)
        {
            TreeItem treeRoot = nodeTree.GetRootItem();
            treeRoot.ClearChildren();
            var keyCategory = (List<string>)_AllKeys.GetType().GetMethod("Keys").Invoke(_AllKeys, null);
            foreach (string category in keyCategory)
            {
                TreeItem treeItem = nodeTree.CreateItem();
                treeItem.SetFolder(true);
                treeItem.SetText(category);
                InputMapItem keys = (InputMapItem)_AllKeys.GetType().GetMethod("get_Item").Invoke(_AllKeys, new object[] { category });
                bool hasChild = keys.InputKeys.Count > 0;
                for (int i = 0; i < keys.InputKeys.Count; ++i)
                {
                    TreeItem leafItem = nodeTree.CreateItem();
                    treeItem.AddChild(leafItem);
                    leafItem.SetFolder(true);
                    leafItem.SetText(keys.InputKeys[i].KeyName);
                }

                if (searchKey != "" && hasChild)
                {
                    treeItem.SetExpanded(true);
                }

                if (hasChild)
                {
                    treeRoot.AddChild(treeItem);
                }
            }
        }

        void DeleteInputKey(Button Sender)
        {
            string inputActionName = Sender.GetTagString1();
            string senderKey = Sender.GetName();
            InputMapItem item = (InputMapItem)GetDictionaryItem(_InputMap, inputActionName);
            for (int i = 0; i < item.InputKeys.Count; ++i)
            {
                if (item.InputKeys[i].KeyName == senderKey)
                {
                    item.InputKeys.Erase(i);
                    break;
                }
            }
            RegenerateInputActions();
        }

        UIManager GetUIManager()
        {
            return _OperationBarUI.GetPanelBar().GetUIManager();
        }

        object NewItem()
        {
            var obj = Activator.CreateInstance(_ItemType);
            return obj;
        }

        protected void CreateNewInputAction(Button Sender)
        {
            Sender.CloseToolTips();
            CancelSelect();
            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Create Input Action", "Please input IA Name:", "");
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                if (StringInputed == "")
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Item name can not be empty!");
                    return;
                }
                if (ContainsDictionaryKey(_InputMap, StringInputed))
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Invalid Name", "Duplicated Item name: " + StringInputed);
                    return;
                }
                var Item = NewItem();
                InsertDictionaryItem(_InputMap, StringInputed, Item);
                RegenerateInputActions();
            };
            TextInputUI.ShowDialog();
        }

        /*Adjust the location of UI item*/
        private void RefreshInspectorUI()
        {
            int ScrollPanelWidth = _ScrollPanel.GetWidth();
            int Y = 0;
            if (_InputActions != null && _InputMap != null)
            {
                foreach (var button in _InputActions)
                {
                    if (button.left.GetVisible())
                    {
                        Y += _RowHeight;
                    }
                }
                if (Y > _ScrollPanel.GetHeight())
                {
                    ScrollPanelWidth = _ScrollPanel.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                }
                Y = 0;
                foreach (var button in _InputActions)
                {
                    if (button.left.GetVisible())
                    {
                        int operatorLen = 50;
                        int valueTypeLen = 150;
                        int actionLen = ScrollPanelWidth - valueTypeLen - operatorLen - 2;
                        button.left.SetPosition(0, Y, actionLen, _RowHeight);
                        button.middle.SetPosition(actionLen, Y, valueTypeLen, _RowHeight);
                        button.right.SetPosition(ScrollPanelWidth - operatorLen - 2, Y, operatorLen, _RowHeight);
                        Y += (_RowHeight + 2);
                        if (button.expand)
                        {
                            foreach (var keyButton in button.childs)
                            {
                                keyButton.key.SetVisible(true);
                                keyButton.delete.SetVisible(true);

                                int keyLength = Math.Max(actionLen, keyButton.key.CalculateTextWidth());
                                keyLength = Math.Min(keyLength, ScrollPanelWidth - 2);
                                int checkLength = 30;
                                int interval = 30;
                                int deleteLen = keyButton.delete.CalculateTextWidth() * 2;
                                //int startPositionX = (ScrollPanelWidth - 2 - keyLength - deleteLen) / 2;

                                keyButton.check.SetPosition(0, Y, checkLength, _RowHeight);
                                keyButton.key.SetPosition(checkLength, Y, keyLength, _RowHeight);
                                keyButton.delete.SetPosition(keyLength + checkLength, Y, deleteLen, _RowHeight);
                                Y += (_RowHeight + 2);

                                keyButton.modifier.Refresh(interval + interval, ref Y, _RowHeight);
                                keyButton.trigger.Refresh(interval + interval, ref Y, _RowHeight);
                            }
                        }
                        else
                        {
                            foreach (var keyButton in button.childs)
                            {
                                keyButton.key.SetVisible(false);
                                keyButton.check.SetVisible(false);
                                keyButton.modifier.SetVisible(false);
                                keyButton.trigger.SetVisible(false);
                                keyButton.delete.SetVisible(false);
                            }
                        }
                    }
                    else    //It works when searching
                    {
                        foreach (var keyButton in button.childs)
                        {
                            keyButton.key.SetVisible(false);
                            keyButton.check.SetVisible(false);
                            keyButton.modifier.SetVisible(false);
                            keyButton.trigger.SetVisible(false);
                            keyButton.delete.SetVisible(false);
                        }
                    }
                }
            }
            _ScrollPanel.SetHeight(Math.Max(_ScrollPanel.GetHeight(), Y + 50));
            _ScrollView.UpdateScrollBar();
        }
        public void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            if (_InputActions != null)
            {
                string InputActionName = null;
                _InputActions.ForEach((OneButton) =>
                {
                    // Traverse all controls
                    if (StringHelper.IgnoreCaseContains(OneButton.left.GetText(), Pattern))
                    {
                        OneButton.left.SetVisible(true);
                        OneButton.middle.SetVisible(true);
                        OneButton.right.SetVisible(true);
                        InputActionName = OneButton.left.GetTagString1();
                    }
                    else
                    {
                        OneButton.left.SetVisible(false);
                        OneButton.middle.SetVisible(false);
                        OneButton.right.SetVisible(false);
                    }
                });

                RefreshInspectorUI();
            }
        }

        public void OnSearchUICancel(SearchUI Sender)
        {
            if (_InputActions != null)
            {
                _InputActions.ForEach((OneButton) =>
                {
                    OneButton.left.SetVisible(true);
                    OneButton.middle.SetVisible(true);
                    OneButton.right.SetVisible(true);
                });

                RefreshInspectorUI();
            }
        }

        void CancelSelect()
        {
            GetUIManager().SetFocusControl(null);
        }

        public void OnScrollViewChanged(Control sender, bool poschange, bool sizechange)
        {
            _SearchUI.TriggerSearchEvent();
        }
    }
}
