using Clicross;
using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public abstract class InputKeyImplement
    {
        public delegate void RefreshEventHandler();

        //private int _ControllerWidth = 300;
        private const int BAR_HEIGHT = 24;
        private Color BAR_COLOR = Color.FromRGB(55, 55, 55);
        private Color ButtonColor = Color.FromRGB(70, 70, 70);

        protected InputKeyItem _ParentKeyItem;
        protected int _VectorIndex;
        protected List<InputActionKeyDrawer> Childs = new List<InputActionKeyDrawer>();
        protected Panel _NameBar;
        public Button _DeleteButton;
        public Button _ChooseTypeButton;
        protected Control _ParentScrollView;
        protected Check _CheckExpand;

        public event RefreshEventHandler InputMappingReGenEvent;
        public event RefreshEventHandler InputMappingRefreshEvent;

        public InputKeyImplement(InputKeyItem parentKey, int idx, Control parentContainer)
        {
            _ParentKeyItem = parentKey;
            _VectorIndex = idx;
            _ParentScrollView = parentContainer;

            _NameBar = new Panel();
            _NameBar.SetSize(500, BAR_HEIGHT);
            _NameBar.SetBackgroundColor(BAR_COLOR);
            _NameBar.SetBorderColor(Color.FromRGBA(135, 135, 135, (int)byte.MaxValue));
            _NameBar.SetTextAlign(TextAlign.CenterLeft);
            _NameBar.SetFontSize(20);
            _NameBar.SetTextOffsetX(10);

            _DeleteButton = new Button();
            _DeleteButton.Initialize();
            _DeleteButton.SetFontSize(15);
            _DeleteButton.SetText("Delete");
            _DeleteButton.SetBorderColor(Color.FromRGBA(135, 135, 135, (int)byte.MaxValue));
            _DeleteButton.SetToolTips("Delete this");
            int deleteLen = _DeleteButton.CalculateTextWidth() + 10;
            _DeleteButton.SetPosition(_NameBar.GetWidth() - deleteLen - 2, 2, deleteLen, 20);
            _DeleteButton.SetNormalColor(ButtonColor);
            _DeleteButton.SetTagInt1(_VectorIndex);

            _ChooseTypeButton = new Button();
            _ChooseTypeButton.Initialize();
            _ChooseTypeButton.SetFontSize(15);
            _ChooseTypeButton.SetText("Modify");
            _ChooseTypeButton.SetBorderColor(Color.FromRGBA(135, 135, 135, (int)byte.MaxValue));
            _ChooseTypeButton.SetToolTips("Modify the Type");
            int chooseLen = _ChooseTypeButton.CalculateTextWidth() + 10;
            _ChooseTypeButton.SetPosition(_NameBar.GetWidth() - chooseLen - deleteLen - 10, 2, chooseLen, 20);
            _ChooseTypeButton.SetNormalColor(ButtonColor);
            _ChooseTypeButton.SetTagInt1(_VectorIndex);

            _NameBar.AddChild(_DeleteButton);
            _NameBar.AddChild(_ChooseTypeButton);
            _ParentScrollView.AddChild(_NameBar);
        }

        public abstract void Init();

        protected void InitExpand()
        {
            _CheckExpand = new Check();
            _CheckExpand.Initialize();
            _CheckExpand.SetImageUnchecked(UIManager.LoadUIImage("Editor/Tree/Common/Folded.png"));
            _CheckExpand.SetImageChecked(UIManager.LoadUIImage("Editor/Tree/Common/NotFolded.png"));
            _CheckExpand.SetAutoCheck(true);
            _CheckExpand.SetChecked(true);
            _CheckExpand.ClickedEvent += (Check sender) =>
            {
                for (int i = 0; i < Childs.Count; i++)
                {
                    Childs[i].Panel.SetVisible(sender.GetChecked());
                }
                RefreshInputMapping();
            };

            _NameBar.AddChild(_CheckExpand);
            _NameBar.SetTextOffsetX(30);
            _CheckExpand.SetPosition(0, 0, 30, 30);
        }

        //Refresh Controlled by InputActionMappingContentMapView
        public void Refresh(int X, ref int Y)
        {
            int interval = 30;
            if (_NameBar.GetVisible())
            {
                _NameBar.SetPos(X + interval, Y);
                Y += (BAR_HEIGHT + 2);
            }

            interval += 30;
            for (int i = 0; i < Childs.Count; i++)
            {
                if (Childs[i].Panel.GetVisible())
                {
                    Childs[i].Panel.SetPosition(X + interval, Y, Childs[i].Panel.GetWidth(), BAR_HEIGHT);
                    Y += (BAR_HEIGHT + 2);
                }
            }
        }

        public void SetVisible(bool flag)
        {
            _NameBar.SetVisible(flag);
            for (int i = 0; i < Childs.Count; i++)
            {
                Childs[i].Panel.SetVisible(flag);
            }
        }

        protected void RefreshInputMapping()
        {
            InputMappingRefreshEvent();
        }

        protected void RegenInputMapping()
        {
            InputMappingReGenEvent();
        }
    }
}