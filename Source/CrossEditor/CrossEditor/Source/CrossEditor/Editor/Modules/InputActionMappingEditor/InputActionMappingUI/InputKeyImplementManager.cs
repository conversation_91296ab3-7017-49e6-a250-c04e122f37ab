using Clicross;
using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public abstract class InputKeyImplementManager
    {
        public delegate void RefreshEventHandler();

        int _RowHeight = 30;
        public const int BAR_HEIGHT = 24;
        public static Color BAR_COLOR = Color.FromRGB(100, 100, 100);

        Button _ButtonAdd;
        protected Check _CheckExpand;
        protected Panel _OperationBar;
        public List<InputKeyImplement> Implements = new List<InputKeyImplement>();     //the order of Modifiers should be same with CPP
        protected InputKeyItem _ParentKeyItem;
        protected Control _ParentScrollView;

        public event RefreshEventHandler InputMappingReGenEvent;
        public event RefreshEventHandler InputMappingRefreshEvent;

        public InputKeyImplementManager(string name)
        {
            _OperationBar = new Panel();
            _OperationBar.SetSize(600, BAR_HEIGHT);
            _OperationBar.SetBackgroundColor(BAR_COLOR);
            _OperationBar.SetText(name);
            _OperationBar.SetTextAlign(TextAlign.CenterCenter);
            _OperationBar.SetFontSize(20);
            _OperationBar.SetTextOffsetX(10);

            _ButtonAdd = new Button();
            _ButtonAdd.Initialize();
            _ButtonAdd.SetFontSize(20);
            _ButtonAdd.SetText("+");
            _ButtonAdd.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonAdd.SetToolTips("Add " + name);
            _ButtonAdd.SetPosition(_OperationBar.GetWidth() - _RowHeight - 2, 2, _RowHeight, 20);
            _ButtonAdd.ClickedEvent += CreateImplement;

            _CheckExpand = new Check();
            _CheckExpand.Initialize();
            _CheckExpand.SetImageUnchecked(UIManager.LoadUIImage("Editor/Tree/Common/Folded.png"));
            _CheckExpand.SetImageChecked(UIManager.LoadUIImage("Editor/Tree/Common/NotFolded.png"));
            _CheckExpand.SetAutoCheck(true);
            _CheckExpand.SetChecked(true);
            _CheckExpand.ClickedEvent += ExpandImplements;

            _OperationBar.AddChild(_ButtonAdd);
            _OperationBar.AddChild(_CheckExpand);
        }

        public abstract void Init(InputKeyItem parentKeyItem);
        protected abstract void CreateImplement(Button Sender);

        protected void ExpandImplements(Check Sender)
        {
            bool flag = Sender.GetChecked();
            _CheckExpand?.SetChecked(flag);
            for (int i = 0; i < Implements.Count; i++)
            {
                Implements[i].SetVisible(flag);
            }

            RefreshInputMapping();
        }

        public void OnAddToParent(Control mContainer)
        {
            _ParentScrollView = mContainer;
            _ParentScrollView.AddChild(_OperationBar);
        }

        //Refresh Controlled by InputActionMappingContentMapView
        public void Refresh(int X, ref int Y, int RowHeight)
        {
            if (_OperationBar.GetVisible())
            {
                _OperationBar.SetPos(X, Y);
                Y += (BAR_HEIGHT + 2);
            }
            for (int i = 0; i < Implements.Count; i++)
            {
                Implements[i].Refresh(X, ref Y);
            }
        }

        public void SetVisible(bool flag)
        {
            _OperationBar.SetVisible(flag);
            for (int i = 0; i < Implements.Count; i++)
            {
                Implements[i].SetVisible(flag);
            }
        }

        protected void RefreshInputMapping()
        {
            InputMappingRefreshEvent();
        }

        protected void RegenInputMapping()
        {
            InputMappingReGenEvent();
        }
    }
}
