using Clicross;
using Clicegf;
using EditorUI;
using System.Linq;
using System;

namespace CrossEditor
{
    public class InputKeyTriggerManager : InputKeyImplementManager
    {
        public InputKeyTriggerManager() : base("Trigger")
        { }

        public override void Init(InputKeyItem parentKeyItem)
        {
            _ParentKeyItem = parentKeyItem;
            int triggerCount = _ParentKeyItem.GetTriggerCount();

            //Initialize all triggers
            for (int i = 0; i < triggerCount; i++)
            {
                GenTrigger_Editor(i);
            }
        }

        protected override void CreateImplement(Button Sender)
        {
            _ParentKeyItem.CreateTrigger(InputActionMappingRegistry.Instance().DefaultInputTrigger);
            GenTrigger_Editor(_ParentKeyItem.GetTriggerCount() - 1);
            RegenInputMapping();

            /*SetVisible(true);  
            InputMappingRefreshEvent();*/
        }

        private void DeleteTrigger(Button Sender)
        {
            int idx = Sender.GetTagInt1();
            _ParentKeyItem.DeleteTrigger(idx);
            Implements.RemoveAt(idx);
            RegenInputMapping();
        }

        private void ChangeTriggerType(Button Sender)
        {
            Menu MenuContextMenu = new Menu(_OperationBar.GetUIManager());
            MenuItem mainContainerItem = new MenuItem();
            Panel Container = new Panel();
            Tree keyTree = new Tree();

            MenuContextMenu.Initialize();
            keyTree.Initialize();
            keyTree.SetTextAlign(TextAlign.TopLeft);
            keyTree.GetRootItem().SetExpanded(true);
            keyTree.SetPosition(2, 2, 300, 400);
            keyTree.ItemSelectedEvent += (tree, treeItem) =>
            {
                if (treeItem.GetText() != "")
                {
                    int idx = Sender.GetTagInt1();
                    string fullName = InputActionMappingRegistry.Instance().Triggers.FirstOrDefault(e => e.Contains(treeItem.GetText()));
                    _ParentKeyItem.ChangeTriggerType(fullName, idx);
                    Implements[idx].Init();
                    RefreshInputMapping();
                }
                _OperationBar.GetUIManager().GetContextMenu().HideMenu();
            };
            TreeItem treeRoot = keyTree.GetRootItem();
            treeRoot.ClearChildren();
            foreach (string name in InputActionMappingRegistry.Instance().Triggers)
            {
                TreeItem treeItem = keyTree.CreateItem();
                treeRoot.AddChild(treeItem);
                treeItem.SetFolder(true);
                string displayName = name.Split("::").LastOrDefault();
                treeItem.SetText(displayName);
            }
            Container.AddChild(keyTree);

            Container.SetSize(304, 400);
            mainContainerItem.SetControl(Container);
            MenuContextMenu.AddMenuItem(mainContainerItem);
            _OperationBar.GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        private void GenTrigger_Editor(int idx)
        {
            InputKeyTrigger trigger_CS = new InputKeyTrigger(_ParentKeyItem, idx, _ParentScrollView);
            trigger_CS.InputMappingRefreshEvent += RefreshInputMapping;
            trigger_CS.InputMappingReGenEvent += RegenInputMapping;
            trigger_CS._DeleteButton.ClickedEvent += DeleteTrigger;
            trigger_CS._ChooseTypeButton.ClickedEvent += ChangeTriggerType;
            Implements.Add(trigger_CS);
        }
    }
}
