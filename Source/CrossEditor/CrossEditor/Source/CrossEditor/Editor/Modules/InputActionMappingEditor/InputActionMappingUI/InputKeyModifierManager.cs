
using Clicross;
using Clicegf;
using EditorUI;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class InputKeyModifierManager : InputKeyImplementManager
    {
        public InputKeyModifierManager() : base("Modifier")
        { }

        public override void Init(InputKeyItem parentKeyItem)
        {
            _ParentKeyItem = parentKeyItem;
            int modifierCount = _ParentKeyItem.GetModifierCount();

            //Initialize all modifiers
            for (int i = 0; i < modifierCount; i++)
            {
                GenModifier_Editor(i);
            }
        }

        protected override void CreateImplement(Button Sender)
        {
            _ParentKeyItem.CreateModifier(InputActionMappingRegistry.Instance().DefaultInputModifier);
            GenModifier_Editor(_ParentKeyItem.GetModifierCount() - 1);
            RegenInputMapping();
            /*SetVisible(true); 
            InputMappingRefreshEvent();*/
        }

        private void DeleteModifier(Button Sender)
        {
            int idx = Sender.GetTagInt1();
            _ParentKeyItem.DeleteModifier(idx);
            Implements.RemoveAt(idx);
            RegenInputMapping();
        }

        private void ChangeModifierType(Button Sender)
        {
            Menu MenuContextMenu = new Menu(_OperationBar.GetUIManager());
            MenuItem mainContainerItem = new MenuItem();
            Panel Container = new Panel();
            Tree keyTree = new Tree();

            MenuContextMenu.Initialize();
            keyTree.Initialize();
            keyTree.SetTextAlign(TextAlign.TopLeft);
            keyTree.GetRootItem().SetExpanded(true);
            keyTree.SetPosition(2, 2, 300, 400);
            keyTree.ItemSelectedEvent += (tree, treeItem) =>
            {
                if (treeItem.GetText() != "")
                {
                    int idx = Sender.GetTagInt1();
                    string fullName = InputActionMappingRegistry.Instance().Modifiers.FirstOrDefault(e => e.Contains(treeItem.GetText()));
                    _ParentKeyItem.ChangeModifierType(fullName, idx);
                    Implements[idx].Init();
                    RefreshInputMapping();
                }
                _OperationBar.GetUIManager().GetContextMenu().HideMenu();
            };
            TreeItem treeRoot = keyTree.GetRootItem();
            treeRoot.ClearChildren();
            foreach (string name in InputActionMappingRegistry.Instance().Modifiers)
            {
                TreeItem treeItem = keyTree.CreateItem();
                treeRoot.AddChild(treeItem);
                treeItem.SetFolder(true);
                string displayName = name.Split("::").LastOrDefault();
                treeItem.SetText(displayName);
            }
            Container.AddChild(keyTree);

            Container.SetSize(304, 400);
            mainContainerItem.SetControl(Container);
            MenuContextMenu.AddMenuItem(mainContainerItem);
            _OperationBar.GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        private void GenModifier_Editor(int idx)
        {
            InputKeyModifier modifier_CS = new InputKeyModifier(_ParentKeyItem, idx, _ParentScrollView);
            modifier_CS.InputMappingRefreshEvent += RefreshInputMapping;
            modifier_CS.InputMappingReGenEvent += RegenInputMapping;
            modifier_CS._DeleteButton.ClickedEvent += DeleteModifier;
            modifier_CS._ChooseTypeButton.ClickedEvent += ChangeModifierType;
            Implements.Add(modifier_CS);
        }
    }
}
