using Clicross;
using EditorUI;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class InputKeyTrigger : InputKeyImplement
    {
        // Excluded properties will not be displayed in InputActionKeyDrawer
        private readonly List<string> _ExcludedProperties = [ "type", "TriggerName" ];

        public InputKeyTrigger(InputKeyItem parentKey, int idx, Control parentContainer) : base(parentKey, idx, parentContainer)
        {
            Init();
        }

        public override void Init()
        {
            InputKeyTriggerItem trigger = _ParentKeyItem.GetTrigger(_VectorIndex);
            string displayName = trigger.GetType().Name;
            _NameBar.SetText(displayName);

            Childs.ForEach(child => { _ParentScrollView.RemoveChild(child.Panel); });
            Childs.Clear();

            var properties = trigger.GetType().GetProperties().Where(e => !_ExcludedProperties.Contains(e.Name));
            foreach (var property in properties)
            {
                InputActionKeyDrawer drawer = new(property.Name, property.PropertyType, _ParentScrollView);
                drawer.BindDrawerObj(trigger);
                Childs.Add(drawer);
            }

            InitExpand();
        }
    }
}