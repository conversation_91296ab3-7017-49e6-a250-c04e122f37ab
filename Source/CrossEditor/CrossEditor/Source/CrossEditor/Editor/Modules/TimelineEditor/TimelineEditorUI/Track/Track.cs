using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public delegate void PostKeyFrameAddEventHander(Track Sender, KeyFrame NewKeyFrame);
    public delegate void PostTrackAddEventHandler(Track Sender, Track NewTrack);
    public delegate void TrackItemClickedEventHandler(Track Sender, Key MouseType, int MouseX, int MouseY);
    public delegate void TrackExpanedEventHandler(Track Sender, bool bExpaned);
    public delegate bool GetCurvesFilter(Track inTrack);

    public abstract class Track
    {
        public static readonly int INDENT_WIDTH = 20;
        public static readonly float OriginKeyValue = -1000f;

        protected Panel _Panel;
        protected ScaleUI _ScaleUI;

        public TrackTree ParentTree;
        protected Track ParentTrack;
        protected List<Track> Children;

        protected int ItemHeight;
        protected int ButtonHeight;
        protected int FontSize;
        protected int SpanX;

        protected Button _ButtonBar;
        protected Check _CheckExpand;
        protected Label _LabelName;
        protected Button _ButtonAddTrack;

        protected object TagObject;

        protected CurveManager Curve;
        protected List<KeyFrame> KeyFrames;

        protected decimal StartKey, EndKey;
        protected bool bInfinite;
        protected bool bVisible;
        protected bool bSelected;
        protected bool bFixed;

        protected Color UnSelectedColor;
        protected Color SelectedColor;

        public bool CanBeDeleted = true;
        public bool CanBeEdit = true;

        protected EditWithProgress _EditWithProgress;

        public event PostKeyFrameAddEventHander PostKeyFrameAddEvent;
        public event PostTrackAddEventHandler PostTrackAddEvent;
        public event TrackItemClickedEventHandler TrackItemClickedEvent;
        public event TrackExpanedEventHandler TrackExpanedEvent;

        public Entity LevelSequenceEntity = null;

        public Track()
        {
            ItemHeight = 24;
            ButtonHeight = 20;
            FontSize = 14;
            SpanX = 2;

            StartKey = EndKey = 0;
            bInfinite = true;
            bVisible = true;
            bSelected = false;
            bFixed = false;

            UnSelectedColor = Color.EDITOR_UI_HILIGHT_COLOR_GRAY;
            SelectedColor = Color.EDITOR_UI_HILIGHT_COLOR_BLUE;
        }

        public virtual void Initialize(ScaleUI ScaleUI, Track Parent, string Name, object TagObject = null)
        {
            _ScaleUI = ScaleUI;
            _Panel = ScaleUI.GetPanel();
            ParentTrack = Parent;
            Children = new List<Track>();
            Curve = new CurveManager();
            Curve.Name = Name;
            KeyFrames = new List<KeyFrame>();

            SetTagObject(TagObject);
            //var Previewing = TagObject as Entity;
            //if (Previewing != null && Previewing.GetEntityIdStruct() == EntityIDStruct.InvalidHandle()) 
            //{
            //    SetIsInvalidState(false);
            //}

            _ButtonBar = new Button();
            _ButtonBar.Initialize();
            _ButtonBar.SetSize(0, 0);
            _ButtonBar.LeftMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            { OnTrackItemClicked(Sender, MouseX, MouseY, ref bContinue, Key.LeftButton); };
            _ButtonBar.RightMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            { OnTrackItemClicked(Sender, MouseX, MouseY, ref bContinue, Key.RightButton); };
            _ButtonBar.PaintEvent += OnTrackItemPaint;

            _CheckExpand = new Check();
            _CheckExpand.Initialize();
            _CheckExpand.SetSize(0, 0);
            _CheckExpand.SetImageUnchecked(UIManager.LoadUIImage("Editor/Tree/Common/Folded.png"));
            _CheckExpand.SetImageChecked(UIManager.LoadUIImage("Editor/Tree/Common/NotFolded.png"));
            _CheckExpand.SetAutoCheck(true);
            _CheckExpand.SetChecked(true);
            _CheckExpand.ClickedEvent += (Sender) => { TrackExpanedEvent?.Invoke(this, _CheckExpand.GetChecked()); };
            _ButtonBar.AddChild(_CheckExpand);

            _LabelName = new Label();
            _LabelName.SetText(Name);
            _LabelName.SetFontSize(FontSize);
            _ButtonBar.AddChild(_LabelName);

            _ButtonAddTrack = new Button();
            _ButtonAddTrack.Initialize();
            _ButtonAddTrack.SetText("+ Track");
            _ButtonAddTrack.SetFontSize(FontSize);
            _ButtonAddTrack.ClickedEvent += OnButtonAddTrackClicked;
            _ButtonBar.AddChild(_ButtonAddTrack);
        }
        public UIManager GetUIManager()
        {
            if (_Panel != null)
            {
                return _Panel.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        private void OnEditTextChanged(Control Sender)
        {
            AddNewTrack(this);
        }

        protected void OnTrackItemClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue, Key code)
        {
            TrackItemClickedEvent?.Invoke(this, code, MouseX, MouseY);
        }

        public virtual RectangleF GetBound()
        {
            int X;
            int Y = GetTrackItem().GetScreenY();
            int Width;
            int Height = GetTrackItem().GetHeight();

            if (Y < _Panel.GetScreenY() + ScaleUI.PAINT_HEIGHT) return new RectangleF();

            if (bInfinite)
            {
                X = _Panel.GetScreenX();
                Width = _Panel.GetWidth();
            }
            else
            {
                X = (int)_ScaleUI.ValueToScreenX(StartKey);
                Width = (int)_ScaleUI.ValueToScreenX(EndKey) - X;
            }

            return new RectangleF(X, Y, Width, Height);
        }

        public List<Track> GetChildList() { return Children; }

        public CurveManager GetCurve() { return Curve; }

        public List<CurveManager> GetCurves(GetCurvesFilter filter = null)
        {
            List<CurveManager> Curves = new List<CurveManager>();
            if (Curve != null && (filter == null || filter(this)))
                Curves.Add(Curve);
            foreach (Track Track in Children)
            {
                Curves.AddRange(Track.GetCurves(filter));
            }
            return Curves;
        }

        public bool GetExpanded() { return _CheckExpand.GetChecked(); }

        public void SetExpanded() { _CheckExpand.SetChecked(!_CheckExpand.GetChecked()); }

        public bool GetIsFixed() { return bFixed; }

        public bool GetIsSelected() { return bSelected; }
        public virtual void SetIsSelected(bool IsSelected)
        {
            bSelected = IsSelected;
            if (bSelected)
            {
                _ButtonBar.SetNormalColor(Color.EDITOR_UI_ACTIVE_TOOL_OR_MENU);
                _ButtonBar.SetHoverColor(Color.EDITOR_UI_ACTIVE_TOOL_OR_MENU);

            }
            else
            {
                _ButtonBar.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
                _ButtonBar.SetHoverColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            }
        }

        public bool GetIsVisible() { return bVisible; }

        public List<KeyFrame> GetKeyFrames() { return KeyFrames; }

        public string GetName() { return Curve.Name; }

        public ScaleUI GetScaleUI() { return _ScaleUI; }

        public object GetTagObject() { return TagObject; }
        public void SetTagObject(object Obj) { TagObject = Obj; }

        public Track GetParentTrack()
        {
            return ParentTrack;
        }

        public Control GetTrackItem() { return _ButtonBar; }

        public virtual void AddKey(KeyFrame Key)
        {
            Key.MoveEvent += OnKeyFrameMove;
            KeyFrames.Add(Key);
            KeyFrames.Sort();

            PostKeyFrameAdd(Key);
        }

        public virtual void AddNewKey(decimal Key, object Value)
        {
            KeyFrame NewKeyFrame = new SimpleKeyFrame(Value, this, Key);
            NewKeyFrame.MoveEvent += OnKeyFrameMove;
            KeyFrames.Add(NewKeyFrame);
            KeyFrames.Sort();

            PostKeyFrameAdd(NewKeyFrame);
        }

        public virtual KeyFrame RemoveKeyFrame(KeyFrame KeyFrame)
        {
            return KeyFrames.Remove(KeyFrame) ? KeyFrame : null;
        }

        public virtual void ClearKeyFrames()
        {
            var Tmp = KeyFrames.Clone();
            foreach (var KeyFrame in Tmp)
                RemoveKeyFrame(KeyFrame);
        }

        public int GetKeyFrameIndex(KeyFrame key)
        {
            return KeyFrames.IndexOf(key);
        }

        public virtual Track CreateNewTrack(string Name) { return null; }

        public virtual void AddTrack(Track NewTrack)
        {
            Children.Add(NewTrack);
            PostAddTrack(NewTrack);
        }

        public virtual void AddNewTrack(Track NewTrack)
        {

        }

        public virtual void AddTrackOnTop(Track NewTrack)
        {
            Children.Insert(0, NewTrack);
            PostAddTrack(NewTrack);
        }

        public virtual void ClearTrack()
        {
            if (ParentTree != null)
            {
                foreach (var child in Children)
                {
                    ParentTree.UnbindTrack(child);
                }
            }
            Children.Clear();
        }

        public virtual bool RemoveChildTrack(Track Track)
        {
            return Children.Remove(Track);
        }

        public abstract object GetValue(float Key);

        public virtual void UpdateValue(float Key)
        {
            foreach (var Child in Children)
            {
                Child.UpdateValue(Key);
            }
        }

        public virtual void Update()
        {
            foreach (var Child in Children)
                Child.Update();
        }

        public virtual void UpdateLayout(bool IsVisible, int Width, int Indent, ref int Y)
        {
            bVisible = IsVisible;

            int ButtonBarHeight = ItemHeight;
            _ButtonBar.SetVisible(IsVisible);
            _ButtonBar.SetPosition(0, Y, Width, ButtonBarHeight);

            int CheckExpandY = (ButtonBarHeight - INDENT_WIDTH) / 2;
            _CheckExpand.SetVisible(Children.Count > 0);
            _CheckExpand.SetPosition(SpanX + Indent * INDENT_WIDTH, CheckExpandY, INDENT_WIDTH, INDENT_WIDTH);

            int LabelNameWidth = _LabelName.CalculateTextWidth();
            int LabelNameHeight = ButtonHeight;
            int LabelNameY = (ButtonBarHeight - LabelNameHeight) / 2;
            _LabelName.SetPosition(SpanX + _CheckExpand.GetX() + _CheckExpand.GetWidth(), LabelNameY, LabelNameWidth, LabelNameHeight);

            int ButtonAddTrackWidth = _ButtonAddTrack.CalculateTextWidth();
            int ButtonAddTrackHeight = ButtonHeight;
            int ButtonAddTrackX = Width - ButtonAddTrackWidth - SpanX;
            int BUttonAddTrackY = (ButtonBarHeight - ButtonAddTrackHeight) / 2;
            _ButtonAddTrack.SetPosition(ButtonAddTrackX, BUttonAddTrackY, ButtonAddTrackWidth, ButtonAddTrackHeight);

            if (IsVisible)
            {
                Y += ItemHeight;
            }
            bool ChildVisibility = IsVisible && _CheckExpand.GetChecked();
            foreach (var Child in Children)
            {
                Child.UpdateLayout(ChildVisibility, Width, Indent + 1, ref Y);
            }
        }

        public virtual void Refresh()
        {
            foreach (var KeyFrame in KeyFrames)
                KeyFrame.Refresh();
        }

        public abstract void Draw(UIManager UIManager);

        public virtual void DrawKeyFrames()
        {
            if (KeyFrames.Count == 0)
                return;

            UIManager UIManager = GetUIManager();
            KeyFrame first = KeyFrames[0];
            if (first.GetType() == typeof(PointKeyFrame))
            {
                PointKeyFrame.DrawBatched(UIManager, KeyFrames);
            }
            else if (first.GetType() == typeof(VectorKeyFrame))
            {
                VectorKeyFrame.DrawBatched(UIManager, KeyFrames);
            }
            else
            {
                foreach (KeyFrame KeyFrame in KeyFrames)
                {
                    KeyFrame.Draw();
                }
            }
        }

        public virtual bool HitTest(int MouseX, int MouseY)
        {
            return bVisible && GetBound().Contains(MouseX, MouseY);
        }

        public virtual KeyFrame HitOnKeyFrame(int MouseX, int MouseY)
        {
            foreach (var KeyFrame in KeyFrames)
            {
                if (KeyFrame.HitTest(MouseX, MouseY)) return KeyFrame;
            }

            return null;
        }

        public virtual List<KeyFrame> ContainKeyFrame(RectangleF Bound)
        {
            List<KeyFrame> KeyFrameList = new List<KeyFrame>();
            foreach (var KeyFrame in KeyFrames)
            {
                if (Bound.Contains(KeyFrame.GetBound()))
                {
                    KeyFrame.bSelected = true;
                    KeyFrameList.Add(KeyFrame);
                }
            }
            return KeyFrameList;
        }

        public List<KeyFrame> GetCurHeadKeyFrames(decimal PlayHead)
        {
            var SelectedKeyFrames = KeyFrames.Where(k =>
            {
                if (Math.Abs(k.GetKeyValue() - PlayHead) < 0.1m)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            });
            return SelectedKeyFrames.ToList();
        }


        public virtual void PreTraverse(Action<Track> Action)
        {
            Action?.Invoke(this);
            foreach (var Child in Children) Child.PreTraverse(Action);
        }

        public virtual void PostTraverse(Action<Track> Action)
        {
            foreach (var Child in Children) Child.PostTraverse(Action);
            Action?.Invoke(this);
        }

        protected virtual void OnButtonAddTrackClicked(Button Sender)
        {
            ShowAddTrackMenu();
            //ShowAddCompTrackMenu();
        }

        protected virtual void OnKeyFrameMove(object Sender, MoveEvnetArgs Args) { }

        protected virtual void OnTrackItemPaint(Control Sender)
        {
            Sender.PaintThis();
            Sender.PaintChildren();
        }

        public void PostKeyFrameAdd(KeyFrame NewKeyFrame)
        {
            PostKeyFrameAddEvent?.Invoke(this, NewKeyFrame);
        }

        public void PostAddTrack(Track NewTrack)
        {
            PostTrackAddEvent?.Invoke(this, NewTrack);
        }

        protected virtual void ShowAddTrackMenu() { }
        public virtual List<MenuItem> GetMenuItems() { return new List<MenuItem>(); }
        public virtual void ShowMenu(int MouseX, int MouseY)
        {
            Menu Menu = new Menu(GetUIManager());
            Menu.Initialize();
            GetMenuItems();
            if (Menu.GetMenuItemCount() > 0)
                GetUIManager().GetContextMenu().ShowMenu(Menu, MouseX, MouseY);
        }

        public virtual void GetFrameSpherePoints(ref List<Vector2f> Selected, ref List<Vector2f> UnSelected)
        {

        }
        public EditWithProgress GetEditWithPrcogress()
        {
            return _EditWithProgress;
        }

        public string GetLabelName()
        {
            return _LabelName.GetText();
        }

        public void SetIsInvalidState(bool flag)
        {
            List<Control> ListControl = new List<Control>() { _ButtonBar, _CheckExpand, _LabelName, _ButtonAddTrack };
            Color DisplayColor = Color.EDITOR_UI_COLOR_WHITE;
            if (!flag)
            {
                DisplayColor = Color.EDITOR_UI_TEST_COLOR_RED;
            }
            foreach (var Value in ListControl)
            {
                Value.SetBackgroundColor(DisplayColor);
            }
        }

        public decimal GetKeyFrameBegin()
        {
            if (KeyFrames.Count > 1)
            {
                return KeyFrames[0].GetKeyValue() == -1000 ? KeyFrames[1].GetKeyValue() : KeyFrames[0].GetKeyValue();
            }
            return decimal.MinValue;
        }
    }
}
