using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{

    public enum ShowMode
    {
        ShowTime = 0,
        ShowFPS = 1,
    }
    public class ScaleUI : IMovable
    {
        public static readonly int PAINT_HEIGHT = 30;
        public static readonly int GRAD_HEIGHT = 20;
        public static readonly int GRAD_SMALL_HEIGHT = 10;
        public static readonly int FONT_SIZE = 16;

        public static readonly decimal MIN_UNIT = 1e-9m;
        public static readonly decimal MAX_UNIT = 1e9m;
        public static readonly int MIN_UNIT_WIDTH = 50;
        public static readonly int MAX_UNIT_WIDTH = 100;

        Panel _Panel;
        TimelineEditorUI _CurrentUI;

        decimal Start;
        decimal End;
        decimal Unit;
        decimal UnitWidth;
        decimal Fps;
        int Segment;
        int ScaleBase;

        CurveGraphicsHelper CurveGraphicsHelper;
        Axis Axis;
        ShowMode CurrentMode = ShowMode.ShowTime;

        List<decimal> GradList;

        public event MoveHandler MoveEvent;

        public UIManager GetUIManager()
        {
            if (_Panel != null)
            {
                return _Panel.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public void Initialize(decimal Start, decimal Unit, decimal UnitWidth, decimal Fps, int Segment, Panel Panel, TimelineEditorUI CurrentUI)
        {
            ScaleBase = 0;

            this.Start = Start;
            this.Unit = Unit;
            this.UnitWidth = UnitWidth;
            this.Fps = Fps;
            this.Segment = Segment;
            _Panel = Panel;
            //_Panel.SetBackgroundColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _CurrentUI = CurrentUI;

            GradList = new List<decimal>();

            Axis = new Axis();
            Axis.Initialize(Start, 0, Unit / Segment, 1, UnitWidth / Segment, 1);
            CurveGraphicsHelper = new CurveGraphicsHelper();
            CurveGraphicsHelper.Initialize(Axis, Panel);
            Axis.UpdateEndValue(CurveGraphicsHelper);
        }

        public Panel GetPanel() { return _Panel; }
        public TimelineEditorUI GetCurrentUI() { return _CurrentUI; }
        public CurveGraphicsHelper GetCurveGraphicsHelper() { return CurveGraphicsHelper; }
        public Axis GetAxis() { return Axis; }
        public decimal GetStart() { return Start; }
        public void SetStart(decimal Value) { Start = Value; }
        public decimal GetEnd() { return End; }
        public int GetWidth() { return _Panel.GetWidth(); }
        public int GerHeight() { return _Panel.GetHeight(); }
        public decimal GetFps() { return Fps; }
        public void SetFps(decimal Fps) { this.Fps = Fps; }
        public void SetShowMode(ShowMode ShowMode)
        {
            this.CurrentMode = ShowMode;
        }
        public ShowMode GetShowMode()
        {
            return this.CurrentMode;
        }

        public decimal GetUnit()
        {
            return Unit;
        }

        public void Draw()
        {
            int ScreenX = _Panel.GetScreenX();
            int ScreenY = _Panel.GetScreenY();
            int Width = _Panel.GetWidth();
            int Height = PAINT_HEIGHT;

            //Color BackgroundColor = Color.FromRGB(0, 0, 0);
            //EditorUICanvas.GetInstance().DrawLine(ScreenX, ScreenY + Height, ScreenX + Width, ScreenY + Height, ref BackgroundColor);

            End = Width / UnitWidth * Unit + Start;
            GradList.Clear();

            decimal GradToAdd;
            if (Start % Unit == 0)
            {
                GradToAdd = Start;
            }
            else
            {
                GradToAdd = CurveMathHelper.FindNextInUnit(Start, Unit) - Unit;
            }

            while (GradToAdd < End)
            {
                GradList.Add(GradToAdd);
                GradToAdd += Unit;
            }

            UIManager UIManager = GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();

            Color GradColor = Color.White;
            float Size = 1f;
            Vector2m StartPoint;
            Vector2m EndPoint;
            foreach (decimal Grad in GradList)
            {
                decimal GradScreenX = ValueToScreenX(Grad);
                StartPoint = new Vector2m(GradScreenX, ScreenY + Height);
                EndPoint = new Vector2m(GradScreenX, ScreenY + Height - GRAD_HEIGHT);
                EditorUICanvas.DrawLineF(StartPoint, EndPoint, Size, ref GradColor);

                EditorUICanvas.DrawLineF(StartPoint, new Vector2m(GradScreenX, ScreenY + _Panel.GetHeight()), 1f, ref Color.EDITOR_UI_GRAY_DRAW_COLOR);
            }

            decimal SmallUnit = Unit / Segment;
            foreach (decimal Grad in GradList)
            {
                for (int i = 1; i < Segment; ++i)
                {
                    decimal GradScreenX = ValueToScreenX(Grad + i * SmallUnit);
                    StartPoint = new Vector2m(GradScreenX, ScreenY + Height);
                    EndPoint = new Vector2m(GradScreenX, ScreenY + Height - GRAD_SMALL_HEIGHT);
                    EditorUICanvas.DrawLineF(StartPoint, EndPoint, Size, ref GradColor);
                }
            }

            Color FontColor = Color.FromRGB(128, 128, 128);
            Font AxisStringFont = UIManager.GetDefaultFont(FONT_SIZE);
            foreach (decimal Grad in GradList)
            {
                decimal GradScreenX = ValueToScreenX(Grad);
                if (CurrentMode == ShowMode.ShowFPS)
                {
                    decimal FPSTime = Grad * new decimal(CrossEngineApi.GetSettingManager().GetMaxTickRates());
                    AxisStringFont.DrawString(UIManager, string.Format("{0:0000}", FPSTime), ref FontColor, (int)GradScreenX, ScreenY + Height - FONT_SIZE, 1.0f);
                }
                else
                {
                    AxisStringFont.DrawString(UIManager, string.Format("{0:0.####}", Grad), ref FontColor, (int)GradScreenX, ScreenY + Height - FONT_SIZE, 1.0f);
                }
            }
        }

        public void ScaleToGrad(decimal Grad, float Percent)
        {
            decimal OldScreenX = ValueToScreenX(Grad);
            Scale(Percent);
            decimal NewScreenX = ValueToScreenX(Grad);
            decimal DeltaX = (OldScreenX - NewScreenX);
            Move(DeltaX, 0, GetCurveGraphicsHelper());
        }

        public void Scale(float Percent)
        {
            if (Percent > 1.0 && Unit <= MIN_UNIT)
            {
                Unit = MIN_UNIT;
                UnitWidth *= (decimal)Percent;
                return;
            }
            if (Percent < 1.0 && Unit >= MAX_UNIT)
                return;

            UnitWidth *= (decimal)Percent;

            while (UnitWidth < MIN_UNIT_WIDTH)
            {
                Unit *= Axis.ScaleFactorList[ScaleBase];
                UnitWidth *= Axis.ScaleFactorList[ScaleBase];
                ScaleBase = (ScaleBase + 1) % Axis.ScaleBaseList.Length;
                Segment = Axis.ScaleBaseList[ScaleBase];
            }
            while (UnitWidth > MAX_UNIT_WIDTH)
            {
                if (Unit <= MIN_UNIT) return;
                if (CurrentMode == ShowMode.ShowFPS && Unit == 1) return;

                ScaleBase = ScaleBase == 0 ? Axis.ScaleBaseList.Length - 1 : ScaleBase - 1;
                Unit /= Axis.ScaleFactorList[ScaleBase];
                UnitWidth /= Axis.ScaleFactorList[ScaleBase];
                Segment = Axis.ScaleBaseList[ScaleBase];
            }
        }

        public decimal ValueToScreenX(decimal Value)
        {
            decimal DistanceX = _Panel.GetWidth() / UnitWidth * Unit;
            decimal X = (Value - Start) / DistanceX * _Panel.GetWidth() + _Panel.GetScreenX();
            if(X > Int32.MaxValue)
            {
                return Int32.MaxValue;
            }
            return X;
        }

        public decimal ScreenXToValue(decimal ScreenX)
        {
            decimal WidthRatio = (ScreenX - _Panel.GetScreenX()) / _Panel.GetWidth();
            decimal Value = (End - Start) * WidthRatio + Start;
            if (Value > Int32.MaxValue)
            {
                return Int32.MaxValue;
            }
            return Value;
        }

        public decimal FrameToSecond(decimal Frame) { return Frame / Fps; }
        public decimal SecondToFrame(decimal Second) { return Math.Round(Second * Fps); }
        public decimal SecondToFrame(float Second) { return (decimal)Second * Fps; }

        public decimal GetNearestValue(decimal Value)
        {
            decimal NextValue = CurveMathHelper.FindNextInUnit(Value, Unit / Segment);
            decimal LastValue = NextValue - Unit / Segment;

            return NextValue - Value < Value - LastValue ? NextValue : LastValue;
        }

        public RectangleF GetBound()
        {
            return new RectangleF(_Panel.GetScreenX(), _Panel.GetScreenY(), _Panel.GetWidth(), PAINT_HEIGHT);
        }

        public bool HitTest(int MouseX, int MouseY)
        {
            return GetBound().Contains(MouseX, MouseY);
        }

        public void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper GraphicsHelper)
        {
            Start -= (decimal)DeltaX * Unit / UnitWidth;

            Axis.StartX = Start;
            Axis.UpdateEndValue(CurveGraphicsHelper);
            MoveEvent?.Invoke(this, new MoveEvnetArgs());
        }

        public void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper GraphicsHelper)
        {
        }

        public void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper GraphicsHelper)
        {
        }

        public void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper GraphicsHelper)
        {
        }
    }
}
