using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public delegate void HoldingObjectMoveEventHandler(object Sender, object HodingObject);

    public class TimelineEditorUI : DockingUI
    {
        public static readonly string DockingCardName = "Timeline";

        static readonly TimelineEditorUI _Instance = new TimelineEditorUI();

        public static TimelineEditorUI GetInstance()
        {
            return _Instance;
        }

        #region Control

        protected VContainer _MainContainer;

        protected HSplitter _MainSplitter;
        TimelineControlUI _TimelineControl;

        protected Panel _Panel;
        protected Panel _EditPanel;
        protected ScaleUI _ScaleUI;

        protected VContainer _ViewContainer;
        protected OperationBarUI _OperationBarUI;
        protected OperationBarUI _OperationBarUI1;
        protected SearchUI _SearchUI;
        TrackTree _TrackTree;
        Button _ButtonAddTrack;

        #endregion Control

        protected MoveHead PlayHead;
        protected StartHead StartHead;
        protected EndHead EndHead;
        protected decimal CachedEndHeadPosition;

        List<Track> SelectedTracks;

        public IEnumerable<Track> GetSelectedTracks() { return SelectedTracks; }

        protected static int DISTANCE = 30;
        protected static decimal Interval = 2;
        protected List<KeyFrame> SelectedKeyFrames;

        protected bool IsLeftMouseDown;
        protected bool IsRightMouseDown;

        protected IMovable HoldingObject;
        protected int LastMouseLocationX;
        protected int LastMouseLocationY;
        protected int CumulatedMovement;

        protected bool bShouldUpdateValue;
        protected bool bFramesCopied = false;
        protected List<KeyFrame> TempFramesCopied;
        bool bFrameLockOn;

        bool bModified;

        public string Name { get; set; }

        public Resource Resource { get; set; }

        public Button ButtonAddTrack { get => _ButtonAddTrack; }
        public ScaleUI ScaleUI { get => _ScaleUI; }
        public TimelineControlUI TimelineControl { get => _TimelineControl; }
        public TrackTree TrackTree { get => _TrackTree; }

        protected SelectRectangle SelectRectangle;

        public event HoldingObjectMoveEventHandler HoldingObjectMoveEvent;

        public TimelineEditorUI()
        {
            IsLeftMouseDown = false;
            IsRightMouseDown = false;
            HoldingObject = null;
            LastMouseLocationX = 0;
            LastMouseLocationY = 0;
            CumulatedMovement = 0;

            bShouldUpdateValue = false;
            bFrameLockOn = true;

            Name = DockingCardName;
            Resource = null;
        }

        public virtual bool Initialize()
        {
            #region Initialize UI

            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.SetBackgroundColor(Color.EDITOR_UI_TREE_BACK_COLOR);

            _Panel.PaintEvent += OnPanelPaint;
            _Panel.LeftMouseDownEvent += OnPanelLeftMouseDown;
            _Panel.LeftMouseDoubleClickedEvent += OnPanelLeftMouseDoubleClick;
            _Panel.LeftMouseUpEvent += OnPanelLeftMouseUp;
            _Panel.RightMouseDownEvent += OnPanelRightMouseDown;
            _Panel.RightMouseUpEvent += OnPanelRightMouseUp;
            _Panel.MouseMoveEvent += OnPanelMouseMove;
            _Panel.MouseWheelEvent += OnPanelMouseWheel;

            _EditPanel = new Panel();
            _EditPanel.Initialize();
            _EditPanel.SetBackgroundColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);

            _ScaleUI = new ScaleUI();
            _ScaleUI.Initialize(0m, 1m, 50m, 15, 1, _Panel, this);

            PlayHead = new MoveHead();
            PlayHead.Initialize(_ScaleUI, 0m);

            StartHead = new StartHead();
            StartHead.Initialize(_ScaleUI, 0m);

            EndHead = new EndHead();
            EndHead.Initialize(_ScaleUI, 30m);

            _OperationBarUI = new OperationBarUI();
            _OperationBarUI.Initialize();

            _ButtonAddTrack = OperationBarUI.CreateTextButton("+ Track");
            _ButtonAddTrack.ClickedEvent += OnButtonAddTrackClicked;

            _SearchUI = new SearchUI();
            _SearchUI.Initialize();
            _SearchUI.GetPanelBack().SetPos(0, 2);
            _SearchUI.GetPanelBack().SetWidth(130);

            _OperationBarUI.AddLeft(_ButtonAddTrack);
            _OperationBarUI.AddRight(_SearchUI.GetPanelBack());

            var PanelBar = _OperationBarUI.GetPanelBar();
            PanelBar.SetHeight(ScaleUI.PAINT_HEIGHT);
            for (int i = 0; i < PanelBar.GetChildCount(); ++i)
            {
                PanelBar.GetChild(i).SetHeight(ScaleUI.PAINT_HEIGHT);
            }

            _TrackTree = new TrackTree();
            _TrackTree.Initialize(_Panel);

            Track NotifyTrackHolder = new NotifyTrackHolder();
            NotifyTrackHolder.Initialize(_ScaleUI, null, "Notifies");

            Track CurveTrackHolder = new CurveTrackHolder();
            CurveTrackHolder.Initialize(_ScaleUI, null, "Curves");

            _TrackTree.AddTrack(NotifyTrackHolder);
            _TrackTree.AddTrack(CurveTrackHolder);

            _ViewContainer = new VContainer();
            _ViewContainer.Initialize();

            Panel dummyGap = new Panel();
            dummyGap.SetWidth(1);
            dummyGap.SetHeight(2);

            _ViewContainer.AddFixedChild(dummyGap);
            _ViewContainer.AddFixedChild(_OperationBarUI.GetPanelBar());
            _ViewContainer.AddSizableChild(_TrackTree.GetScrollView(), 1f);

            _MainSplitter = new HSplitter();
            _MainSplitter.Initialize();

            _MainSplitter.AddChild(_ViewContainer);
            _MainSplitter.AddChild(_Panel);

            _TimelineControl = new TimelineControlUI();
            _TimelineControl.Initialize(StartHead, EndHead, PlayHead);

            _MainContainer = new VContainer();
            _MainContainer.Initialize();
            _MainContainer.AddSizableChild(_MainSplitter, 1f);
            _MainContainer.AddFixedChild(_TimelineControl.GetPanel());

            Initialize(Name, _MainContainer);
            _DockingCard.KeyUpEvent += OnDockingCardKeyUp;
            _DockingCard.KeyDownEvent += OnDockingCardKeyDown;

            #endregion Initialize UI

            SceneRuntime.GetInstance().EditorGlobalUpdateEvent += OnEditorGlobalUpdate;

            SelectedTracks = new List<Track>();
            SelectedKeyFrames = new List<KeyFrame>();
            TempFramesCopied = new List<KeyFrame>();

            SelectRectangle = null;

            SetIsFrameLockOn(true);

            return true;
        }

        public bool GetIsFrameLockOn() { return bFrameLockOn; }
        public void SetIsFrameLockOn(bool bFrameLockOn)
        {
            this.bFrameLockOn = bFrameLockOn;
            PlayHead.bFrameLockOn = bFrameLockOn;
            StartHead.bFrameLockOn = bFrameLockOn;
            EndHead.bFrameLockOn = bFrameLockOn;
        }

        public decimal GetHeadLocation() { return PlayHead.Value; }
        public void SetHeadLocation(decimal Value) { PlayHead.Value = Value; }
        public decimal GetStartLocation() { return StartHead.Value; }
        public void SetStartLocation(decimal Value) { StartHead.Value = Value; }
        public decimal GetEndLocation() { return EndHead.Value; }
        public void SetEndLocation(decimal Value) { EndHead.Value = Value; }
        public decimal GetLength() => GetEndLocation() - GetStartLocation();

        public virtual void ClearModified()
        {
            bModified = false;
            GetDockingCard().SetText(Name);
        }
        public bool GetIsModified() => bModified;
        public virtual void SetModified()
        {
            bModified = true;
            GetDockingCard().SetText(Name + "*");
        }

        public Track AddEntityTrack(Entity Entity)
        {
            Track NewTrack = new EntityTrack();
            NewTrack.Initialize(_ScaleUI, null, Entity.GetName());
            NewTrack.SetTagObject(Entity);
            _TrackTree.AddTrack(NewTrack);

            return NewTrack;
        }

        public void ResetTimelineControl()
        {
            PlayHead.Value = 0;
            _TimelineControl.SetIsPlaying(true);
            _TimelineControl.SetIsLoop(true);
        }

        public virtual void DoSave()
        {
            if (Resource != null)
            {
                Resource.Save();
                ClearModified();
            }
        }

        protected virtual void OnEditorGlobalUpdate(Device Sender, long TimeElapsed)
        {
            if (_TimelineControl.GetIsPlaying())
            {
                decimal NewValue = PlayHead.Value + TimeElapsed * _ScaleUI.GetFps() / 1000m;
                if (NewValue >= EndHead.Value)
                {
                    if (_TimelineControl.GetIsLoop())
                    {
                        NewValue = StartHead.Value;
                    }
                    else
                    {
                        NewValue = EndHead.Value;
                        _TimelineControl.StopPlay();
                    }
                }
                PlayHead.Value = NewValue;
                bShouldUpdateValue = true;
            }

            if (bShouldUpdateValue)
            {
                _TrackTree.UpdateValue((float)PlayHead.Value);
                bShouldUpdateValue = false;
            }
        }

        #region Panel Event

        void OnPanelPaint(Control Sender)
        {
            UIManager UIManager = Sender.GetUIManager();

            _Panel.PaintBackground(_Panel.GetScreenX(), DISTANCE + _Panel.GetScreenY());

            _EditPanel.SetUIManager_Recursively(UIManager);

            decimal Distance = _ScaleUI.ValueToScreenX(EndHead.Value) - _ScaleUI.ValueToScreenX(StartHead.Value);
            int EditSpaceDistance = 0;
            if (Distance < new decimal(Int32.MaxValue))
            {
                EditSpaceDistance = (int)Distance;
            }
            else
            {
                EditSpaceDistance = _ScaleUI.GetWidth();
            }

            _EditPanel.SetPosition((int)Math.Clamp(_ScaleUI.ValueToScreenX(StartHead.Value), int.MinValue, int.MaxValue), DISTANCE + _Panel.GetScreenY(),
                EditSpaceDistance, _Panel.GetHeight());
            _EditPanel.PaintBackground(_EditPanel.GetScreenX(), _EditPanel.GetScreenY());

            _ScaleUI.Draw();

            _TrackTree.Update();
            _TrackTree.UpdateLayout();
            _TrackTree.Draw(UIManager);

            StartHead.Draw();
            EndHead.Draw();
            PlayHead.Draw();
            if (SelectRectangle != null)
            {
                SelectRectangle.Draw(UIManager);
            }
        }

        public virtual void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (IsRightMouseDown) return;

            IsLeftMouseDown = true;
            LastMouseLocationX = MouseX;
            LastMouseLocationY = MouseY;
            CumulatedMovement = 0;

            Sender.CaptureMouse();

            HoldingObject = TryHit(MouseX, MouseY);

            if (HoldingObject is EndHead)
            {
                CachedEndHeadPosition = EndHead.Value;
            }
            HoldingObject?.MoveBegin(new Vector2f(MouseX, MouseY), null);
        }

        public virtual void OnPanelLeftMouseDoubleClick(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {

        }

        protected virtual void OnPanelLeftMouseUpImpl(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (IsLeftMouseDown)
            {
                HoldingObject?.MoveEnd(new Vector2f(MouseX, MouseY), null);

                IsLeftMouseDown = false;
                HoldingObject = null;
                Sender.ReleaseMouse();
                bContinue = false;
            }
        }

        void OnPanelLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            OnPanelLeftMouseUpImpl(Sender, MouseX, MouseY, ref bContinue);
        }

        void OnPanelRightMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (IsLeftMouseDown) return;

            Sender.CaptureMouse();

            IsRightMouseDown = true;
            LastMouseLocationX = MouseX;
            LastMouseLocationY = MouseY;
            CumulatedMovement = 0;

            HoldingObject = _ScaleUI;
            HoldingObject?.MoveBegin(new Vector2f(MouseX, MouseY), null);
        }

        void OnPanelRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (IsRightMouseDown)
            {
                HoldingObject = TryHit(MouseX, MouseY);
                HoldingObject?.MoveEnd(new Vector2f(MouseX, MouseY), null);

                if (CumulatedMovement < 2)
                {
                    ShowMenu(MouseX, MouseY);
                }

                IsRightMouseDown = false;
                HoldingObject = null;
                Sender.ReleaseMouse();
                bContinue = false;
            }
        }

        void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            int DeltaX = MouseX - LastMouseLocationX;
            int DeltaY = MouseY - LastMouseLocationY;
            CumulatedMovement += Math.Abs(DeltaX) + Math.Abs(DeltaY);

            if (HoldingObject is KeyFrameMover)
            {
                ProcessRotateKeyFrameMover(((KeyFrameMover)HoldingObject).KeyFrames);
            }

            HoldingObject?.Move(DeltaX, DeltaY, null);

            if (HoldingObject == PlayHead)
                bShouldUpdateValue = true;

            if (HoldingObject == EndHead)
            {
                if (EndHead.Value < StartHead.Value)
                {
                    EndHead.Value = StartHead.Value;
                }
            }

            LastMouseLocationX = MouseX;
            LastMouseLocationY = MouseY;

            HoldingObjectMoveEvent?.Invoke(this, HoldingObject);
        }

        void OnPanelMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            Device Device = GetDevice();
            if (Device.IsControlDownOnly())
            {
                _ScaleUI.ScaleToGrad(PlayHead.Value, 1f + MouseDeltaZ * 0.1f);
            }
        }

        #endregion Panel Event

        protected virtual void OnDockingCardKeyUp(Control Sender, Key Key, ref bool bContinue)
        {
            if (GetDevice().IsNoneModifiersDown() && Key == Key.Space)
            {
                _TimelineControl.SwitchPlay();
            }
        }
        protected virtual void OnDockingCardKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            if (Key == Key.Home)
            {
                CinematicUI.GetInstance().SetTimeProgress(PlayHead, GetStartLocation());
                FitOnRange();
                return;
            }
            else if (Key == Key.F)
            {
                FocusOnPlayHead();
                return;
            }
            else if (Key == Key.End)
            {
                CinematicUI.GetInstance().SetTimeProgress(PlayHead, GetEndLocation());
                FitOnRange();
                return;
            }

            Device Device = GetDevice();
            bool IsControlDown = Device.IsControlDown();
            bool IsShiftDown = Device.IsShiftDown();
            bool IsAltDown = Device.IsAltDown();
            if (Key == Key.Left)
            {
                if (IsControlDown)
                {
                    SetHeadLocation(Math.Max(PlayHead.Value - 1, StartHead.Value));
                }
                else
                {
                    decimal frame = _ScaleUI.SecondToFrame(PlayHead.Value);
                    SetHeadLocation(Math.Max(_ScaleUI.FrameToSecond(frame - 1), GetStartLocation()));
                }
                bShouldUpdateValue = true;
                TimelineControl.StopPlay();
            }
            else if (Key == Key.Right)
            {
                if (IsControlDown)
                {
                    SetHeadLocation(Math.Min(PlayHead.Value + 1, EndHead.Value));
                }
                else
                {
                    decimal frame = _ScaleUI.SecondToFrame(PlayHead.Value);
                    SetHeadLocation(Math.Min(_ScaleUI.FrameToSecond(frame + 1), GetEndLocation()));
                }
                bShouldUpdateValue = true;
                TimelineControl.StopPlay();
            }
        }

        public void OnButtonAddTrackClicked(Button Sender)
        {
            Menu AddTrackMenu = new Menu(GetUIManager());
            AddTrackMenu.Initialize();

            World World = EditorScene.GetInstance().GetWorld();
            if (World.Root != null)
            {
                foreach (Entity Child in World.Root.Children)
                {
                    MenuItem NewItem = new MenuItem();
                    NewItem.SetText(Child.GetName());
                    NewItem.ClickedEvent += (ItemSender) =>
                    {
                        AddEntityTrack(Child);
                    };

                    AddTrackMenu.AddMenuItem(NewItem);
                }
            }

            GetUIManager().GetContextMenu().ShowMenu(AddTrackMenu, Sender.GetScreenX(), Sender.GetScreenY() + Sender.GetHeight());
        }

        public void FitOnRange()
        {
            decimal StartX = _ScaleUI.ValueToScreenX(GetStartLocation() - Interval);
            decimal EndX = _ScaleUI.ValueToScreenX(GetEndLocation() + Interval);
            decimal OriginWidth = _ScaleUI.GetWidth();
            decimal TargetWidth = EndX - StartX;
            decimal Percent = 0m;
            if (TargetWidth != 0m)
            {
                Percent = OriginWidth / TargetWidth;
                _ScaleUI.Scale((float)Percent);
            }
            _ScaleUI.SetStart(GetStartLocation() - Interval * Percent);
        }
        public void FocusOnPlayHead()
        {
            decimal width = GetEndLocation() - GetStartLocation();

            decimal StartX = _ScaleUI.ValueToScreenX(PlayHead.Value - width/2 - Interval);
            decimal EndX = _ScaleUI.ValueToScreenX(PlayHead.Value + width/2 + Interval);
            decimal OriginWidth = _ScaleUI.GetWidth();
            decimal TargetWidth = EndX - StartX;
            decimal Percent = 0m;
            if (TargetWidth != 0m)
            {
                Percent = OriginWidth / TargetWidth;
                _ScaleUI.Scale((float)Percent);
            }
            _ScaleUI.SetStart(PlayHead.Value - width / 2 - Interval * Percent);
        }
        #region Select

        protected bool TrySelect(int MouseX, int MouseY, out KeyFrame HitKeyFrame, out Track HitTrack)
        {
            bool bHitTestSuccess = false;

            HitKeyFrame = null;
            HitTrack = null;

            bool IsControlDownOnly = GetDevice().IsControlDownOnly();

            if (SelectedKeyFrames.Count != 0)
            {
                bool IsHit = false;
                foreach (KeyFrame KeyFrame in SelectedKeyFrames)
                {
                    if (KeyFrame.GetBound().Contains(MouseX, MouseY))
                    {
                        IsHit = true;
                        break;
                    }
                }
                if (IsHit)
                {
                    HoldingObject = new KeyFrameMover(_ScaleUI, SelectedKeyFrames, bFrameLockOn);
                    bHitTestSuccess = true;
                    ClearTrackSelect();
                }
                //SelectedKeyFrames.Clear();
            }
            else
            {
                foreach (Track Track in _TrackTree.GetTracks())
                {
                    if (Track.HitTest(MouseX, MouseY))
                    {
                        HitKeyFrame = Track.HitOnKeyFrame(MouseX, MouseY);
                        if (HitKeyFrame != null)
                        {
                            if (IsControlDownOnly)
                                ChangeSelectOnKeyFrame(HitKeyFrame);
                            else if (!SelectedKeyFrames.Contains(HitKeyFrame))
                                SelectKeyFrame(HitKeyFrame);

                            HoldingObject = new KeyFrameMover(_ScaleUI, SelectedKeyFrames, bFrameLockOn);
                            bHitTestSuccess = true;
                            ClearTrackSelect();
                            PlayHead.Value = HitKeyFrame.GetKeyValue();
                            break;
                        }
                    }
                }
            }

            if (SelectedKeyFrames.Count == 1)
            {
                HitKeyFrame = SelectedKeyFrames[0];
            }

            if (!bHitTestSuccess)
            {
                ClearKeyFrameSelect();
                //ClearTrackSelect();
            }

            return bHitTestSuccess;
        }

        void SelectKeyFrame(KeyFrame KeyFrame)
        {
            ClearKeyFrameSelect();
            KeyFrame.bSelected = true;
            SelectedKeyFrames.Add(KeyFrame);
        }

        void ChangeSelectOnKeyFrame(KeyFrame KeyFrame)
        {
            if (SelectedKeyFrames.Count <= 0)
            {
                SelectKeyFrame(KeyFrame);
            }
            else if (SelectedKeyFrames.Contains(KeyFrame))
            {
                KeyFrame.bSelected = false;
                SelectedKeyFrames.Remove(KeyFrame);
            }
            else if (SelectedKeyFrames[0].GetType() == KeyFrame.GetType())
            {
                KeyFrame.bSelected = true;
                SelectedKeyFrames.Add(KeyFrame);
            }
            else
            {
                SelectKeyFrame(KeyFrame);
            }

            ClearTrackSelect();
        }

        protected void SelectTrack(Track Track)
        {
            ClearTrackSelect();
            Track.SetIsSelected(true);

            SelectedTracks.Add(Track);
            CurveEditorUI.GetInstance().LoadFromCurves(Track.GetCurves((Track inTrack) => { return inTrack.CanBeEdit; }));
        }

        protected void SelectInRectangleTrack(Track Track)
        {
            Track.SetIsSelected(true);
            SelectedTracks.Add(Track);
            CurveEditorUI.GetInstance().LoadFromCurves(Track.GetCurves((Track inTrack) => { return inTrack.CanBeEdit; }));
        }

        public void ClearSelect()
        {
            ClearKeyFrameSelect();
            //ClearTrackSelect();
        }

        void ClearKeyFrameSelect()
        {
            var ObjectInspected = InspectorUI.GetInstance().GetObjectInspected() as KeyFrame;
            if (ObjectInspected != null && SelectedKeyFrames.Contains(ObjectInspected))
            {
                InspectorUI.GetInstance().SetObjectInspected(null);
                InspectorUI.GetInstance().InspectObject();
            }

            foreach (var KeyFrame in SelectedKeyFrames)
            {
                KeyFrame.bSelected = false;
            }
            SelectedKeyFrames.Clear();
        }

        void ClearTrackSelect()
        {
            foreach (var Track in _TrackTree.GetTracks())
            {
                Track.SetIsSelected(false);
            }
            SelectedTracks.Clear();
        }

        public void RemoveSelectedKeyFrames(List<KeyFrame> keyFrames)
        {
            foreach (var KeyFrame in keyFrames)
            {
                if (KeyFrame is null) continue;
                if (KeyFrame.BindObject is Point Point)
                {
                    var OwerCurve = Point.OwnerCurve;
                    if (OwerCurve == null)
                    {
                        break;
                    }
                    KeyFrame.OwnerTrack.RemoveKeyFrame(KeyFrame);
                    OwerCurve.DeletePoint(Point);
                    OwerCurve.PostModifiedCurve();
                }
                else
                {
                    KeyFrame.OwnerTrack.RemoveKeyFrame(KeyFrame);
                }
            }
        }

        public void AddSelectedKeyFrames(List<KeyFrame> CurKeyFrames, List<CurveManager> OwnerCurves)
        {
            for (int i = 0; i < CurKeyFrames.Count; i++)
            {
                if (CurKeyFrames[i].BindObject is Point Point)
                {
                    OwnerCurves[i].AddPoint(Point);
                    OwnerCurves[i].PostModifiedCurve();
                }
                else
                {
                    CurKeyFrames[i].OwnerTrack.AddNewKey(CurKeyFrames[i].GetKeyValue(), CurKeyFrames[i].BindObject);
                }
            }
        }

        public void AddSelectedKeyFrames(List<KeyFrame> CurKeyFrames)
        {
            foreach (var KeyFrame in CurKeyFrames)
            {
                if (KeyFrame.BindObject is Point Point)
                {
                    if (Point.OwnerCurve == null) continue;
                    Point.OwnerCurve.AddPoint(Point);
                    Point.OwnerCurve.PostModifiedCurve();
                }
                else
                {
                    KeyFrame.OwnerTrack.AddNewKey(KeyFrame.GetKeyValue(), KeyFrame.BindObject);
                }
            }
        }

        #endregion Select

        protected virtual IMovable TryHit(int MouseX, int MouseY)
        {
            if (PlayHead.HitTest(MouseX, MouseY))
            {
                return PlayHead;
            }
            else if (EndHead.HitTest(MouseX, MouseY))
            {
                return EndHead;
            }
            else if (StartHead.HitTest(MouseX, MouseY))
            {
                return StartHead;
            }
            else if (_ScaleUI.HitTest(MouseX, MouseY))
            {
                PlayHead.MoveTo(MouseX, MouseY, null);

                _TimelineControl.StopPlay();
                bShouldUpdateValue = true;

                return null;
            }
            else
            {
                TrySelect(MouseX, MouseY, out var KeyFrame, out var _);
                if (KeyFrame != null)
                {
                    InspectorUI.GetInstance().SetObjectInspected(KeyFrame);
                    InspectorUI.GetInstance().InspectObject();
                }
                return HoldingObject;
            }
        }

        public void AddPropertyItemToMenu(Menu menu, IPropertyEditorUI properityEditorUI)
        {
            var MenuItem_Properties = new MenuItem();
            MenuItem_Properties.SetText("Properties");
            MenuItem_Properties.SetMenu(properityEditorUI.GetMenu());

            var MenuItem_FramesProperity = new MenuItem();
            MenuItem_FramesProperity.SetControl(properityEditorUI.GetScrollPanel());

            properityEditorUI.GetMenu().AddMenuItem(MenuItem_FramesProperity);

            menu.AddMenuItem(MenuItem_Properties);
        }

        public virtual void ShowMenu(int MouseX, int MouseY)
        {
            Menu Menu = new Menu(GetUIManager());
            Menu.Initialize();

            bool bHitTestSuccess = false;
            bHitTestSuccess = TrySelect(MouseX, MouseY, out var HitKeyFrame, out var HitTrack);

            // Add FramePropertyEditor Item
            if (HitKeyFrame is SeqAnimSectionKeyFrame)
            {
                var ProperityEditorUI = new AnimSectionPrpertyEditorUI();
                ProperityEditorUI.Initialize(SelectedKeyFrames);
                ProperityEditorUI.SetMenu(new Menu(GetUIManager()));
                AddPropertyItemToMenu(Menu, ProperityEditorUI);
            }
            else if (HitKeyFrame is SubSeqKeyFrame)
            {
                var SubSeqEditorUI = new SubSeqSectionPropertyEditorUI();
                SubSeqEditorUI.Initialize(SelectedKeyFrames);
                SubSeqEditorUI.SetMenu(new Menu(GetUIManager()));
                AddPropertyItemToMenu(Menu, SubSeqEditorUI);
            }
            else
            {
                FramesPropertyEditorUI FramesProperityEditorUI = new FramesPropertyEditorUI();
                List<string> TrackNames = new List<string>();
                List<KeyFrame> NewKeyFrames = FramesProperityEditorUI.JudgePropertyDisplay(SelectedKeyFrames, ref TrackNames);
                if (NewKeyFrames.Count > 0)
                {
                    FramesProperityEditorUI.Initialize(NewKeyFrames, TrackNames);
                    FramesProperityEditorUI.SetMenu(new Menu(GetUIManager()));
                    AddPropertyItemToMenu(Menu, FramesProperityEditorUI);
                }
            }

            MenuItem MenuItem_RemoveKeyFrame = new MenuItem();
            MenuItem_RemoveKeyFrame.SetText("Remove KeyFrames");
            MenuItem_RemoveKeyFrame.SetEnable(SelectedKeyFrames.Count > 0);
            MenuItem_RemoveKeyFrame.ClickedEvent += (Sender) =>
            {
                EditOperation_RemoveKeyFrames operation = new EditOperation_RemoveKeyFrames(SelectedKeyFrames);
                EditOperationManager.GetInstance().AddOperation(operation);
                List<KeyFrame> CurKeyFrames = SelectedKeyFrames.Clone();
                RemoveSelectedKeyFrames(CurKeyFrames);
            };

            if (HitKeyFrame != null)
            {
                // Only show RemoveKeyFrame menu when hit a key frame object
                var KeyFrameMenuItems = HitKeyFrame.GetMenuItems();
                TimelineEditorCommon.AddMenuItemsSeperately(Menu, KeyFrameMenuItems);
                if (HitKeyFrame.OwnerTrack != null)
                {
                    var TrackMenuItems = HitKeyFrame.OwnerTrack.GetMenuItems();
                    TimelineEditorCommon.AddMenuItemsSeperately(Menu, TrackMenuItems);
                }
            }
            else if (HitTrack != null)
            {
                TimelineEditorCommon.AddMenuItemsSeperately(Menu, HitTrack.GetMenuItems());
            }

            MenuItem MenuItem_CopyKeyFrame = new MenuItem();
            MenuItem_CopyKeyFrame.SetText("Copy KeyFrames");
            MenuItem_CopyKeyFrame.SetEnable(SelectedKeyFrames.Count > 0);
            MenuItem_CopyKeyFrame.ClickedEvent += (Sender) =>
            {
                TempFramesCopied.Clear();
                bFramesCopied = true;
                foreach (var Frame in SelectedKeyFrames)
                {
                    TempFramesCopied.Add(Frame);
                }
                ProcessRotateKeyFrameMover(TempFramesCopied);
            };

            if (SelectedKeyFrames.Count > 0)
            {
                Menu.AddMenuItem(MenuItem_RemoveKeyFrame);
                Menu.AddMenuItem(MenuItem_CopyKeyFrame);
            }

            MenuItem MenuItem_PasteKeyFrame = new MenuItem();
            MenuItem_PasteKeyFrame.SetText("Paste KeyFrames");
            MenuItem_PasteKeyFrame.SetEnable(bFramesCopied);
            MenuItem_PasteKeyFrame.ClickedEvent += (Sender) =>
            {
                List<KeyFrame> Temp = TempFramesCopied;
                if (Temp[0].GetKeyValue() != PlayHead.Value)
                {
                    bFramesCopied = false;
                    PasteKeyFrames(Temp);
                    EditOperation_PasteKeyFrames operation = new EditOperation_PasteKeyFrames(SelectedKeyFrames.Clone());
                    EditOperationManager.GetInstance().AddOperation(operation);
                    TempFramesCopied.Clear();
                    SetModified();
                }
            };

            if (bFramesCopied)
            {
                Menu.AddMenuItem(MenuItem_PasteKeyFrame);
            }

            if (Menu.GetMenuItemCount() > 0)
                GetUIManager().GetContextMenu().ShowMenu(Menu, MouseX, MouseY);
        }

        public MoveHead GetHead()
        {
            return PlayHead;
        }

        protected void ProcessRotateKeyFrameMover(List<KeyFrame> ImportKeyFrames)
        {
            List<KeyFrame> NewKeyFrames = new List<KeyFrame>();
            List<KeyFrame> DeleteFrames = new List<KeyFrame>();
            foreach (var KeyFrame in ImportKeyFrames)
            {
                var ComponnetTrack = KeyFrame.OwnerTrack.GetParentTrack() as ComponentTrack;
                if (ComponnetTrack is null)
                    continue;

                if (ComponnetTrack.GetDisplayPropertyName() == "Rotation")
                {
                    bool IsExist = true;

                    foreach (var ComKeyFrame in ComponnetTrack.GetKeyFrames())
                    {
                        List<KeyFrame> PointKeyFrames = ComKeyFrame.BindObject as List<KeyFrame>;

                        if (PointKeyFrames.Contains(KeyFrame))
                        {
                            foreach (var PointFrame in PointKeyFrames)
                            {
                                if (!ImportKeyFrames.Contains(PointFrame))
                                {
                                    IsExist = false;
                                    break;
                                }
                            }
                            if (IsExist) break;
                            foreach (var PointFrame in PointKeyFrames)
                            {
                                if (!NewKeyFrames.Contains(PointFrame))
                                    NewKeyFrames.Add(PointFrame);
                            }
                        }
                    }
                    if (IsExist) break;
                    DeleteFrames.Add(KeyFrame);
                }
            }
            foreach (var Frame in DeleteFrames)
            {
                ImportKeyFrames.Remove(Frame);
            }
            ImportKeyFrames.AddRange(NewKeyFrames);
        }

        public void PasteKeyFrames(List<KeyFrame> KeyFrames)
        {
            if (KeyFrames.Count == 0) return;
            decimal Interval = PlayHead.Value - KeyFrames[0].GetKeyValue();
            foreach (var KeyFrame in KeyFrames)
            {
                Track OwnerTrack = KeyFrame.OwnerTrack;
                KeyFrame NewKeyFrame = null;
                if (OwnerTrack is NumericTrack)
                {
                    double Value = (double)((Point)(KeyFrame.BindObject)).ValueY;
                    ((NumericTrack)OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetKeyValue() + Interval, Value);
                    NewKeyFrame = ((NumericTrack)OwnerTrack).FindByKeyValue(KeyFrame.GetKeyValue() + Interval);
                }
                else if (OwnerTrack is EventTrack)
                {
                    List<string> StringList = new List<string>();
                    var SplitList = ((SmoothPoint)KeyFrame.BindObject).EventString.Split("|");
                    foreach (var Split in SplitList)
                    {
                        if (Split != "")
                        {
                            StringList.Add(Split);
                        }
                    }
                    ((EventTrack)OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetKeyValue() + Interval, StringList);
                    NewKeyFrame = ((EventTrack)OwnerTrack).FindByKeyValue(KeyFrame.GetKeyValue() + Interval);
                }
                if (NewKeyFrame != null)
                {
                    SelectedKeyFrames.Add(NewKeyFrame);
                }
            }
        }

        public void UndoPasteKeyFrames(List<KeyFrame> KeyFrames)
        {
            foreach (var KeyFrame in KeyFrames)
            {
                if (!(KeyFrame is PointKeyFrame)) continue;
                Track OwnerTrack = KeyFrame.OwnerTrack;
                if (OwnerTrack is EventTrack)
                {
                    ((EventTrack)OwnerTrack).RemoveComponentValueFromSelected(KeyFrames);
                }
                else if (KeyFrame.OwnerTrack.GetParentTrack() is ComponentTrack)
                {
                    ((ComponentTrack)KeyFrame.OwnerTrack.GetParentTrack()).RemoveComponentValueFromSelected(KeyFrames);
                }
            }
        }

        public void RedoPasteKeyFrames(List<KeyFrame> KeyFrames)
        {
            if (KeyFrames.Count == 0) return;
            foreach (var KeyFrame in KeyFrames)
            {
                Track OwnerTrack = KeyFrame.OwnerTrack;
                if (OwnerTrack is NumericTrack)
                {
                    double Value = (double)((Point)(KeyFrame.BindObject)).ValueY;
                    ((NumericTrack)OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetKeyValue(), Value);
                }
                else if (OwnerTrack is EventTrack)
                {
                    List<string> StringList = new List<string>();
                    var SplitList = ((SmoothPoint)KeyFrame.BindObject).EventString.Split("|");
                    foreach (var Split in SplitList)
                    {
                        if (Split != "")
                        {
                            StringList.Add(Split);
                        }
                    }
                    ((EventTrack)OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetKeyValue(), StringList);
                }
            }
        }
    }
}
