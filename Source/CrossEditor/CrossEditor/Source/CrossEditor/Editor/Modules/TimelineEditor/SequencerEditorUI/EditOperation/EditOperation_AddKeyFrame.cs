namespace CrossEditor
{
    public class EditOperation_AddKeyFrame : EditOperation
    {
        public delegate void OnAddKeyFrameOperationUndo();
        protected OnAddKeyFrameOperationUndo onOperationUndo;

        public delegate void OnAddKeyFrameOperationRedo();
        protected OnAddKeyFrameOperationRedo onOperationRedo;

        public EditOperation_AddKeyFrame(OnAddKeyFrameOperationUndo undoFunc, OnAddKeyFrameOperationRedo redoFunc)
        {
            onOperationUndo = undoFunc;
            onOperationRedo = redoFunc;
        }

        public override void Undo()
        {
            onOperationUndo();
            CinematicUI.GetInstance().SetModified();
        }

        public override void Redo()
        {
            onOperationRedo();
            CinematicUI.GetInstance().SetModified();
        }
    }
}
