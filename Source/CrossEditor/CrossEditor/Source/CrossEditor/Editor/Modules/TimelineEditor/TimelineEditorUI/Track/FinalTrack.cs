using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public abstract class FinalTrack : Track
    {
        protected Button _ButtonAddKeyFrame;

        public override void Initialize(ScaleUI ScaleUI, Track Parent, string Name, object TagObject = null)
        {
            base.Initialize(ScaleUI, Parent, Name, TagObject);

            // Only holder track may create new track, FinalTrack does not need this button
            _ButtonAddTrack.SetVisible(false);

            _ButtonAddKeyFrame = new Button();
            _ButtonAddKeyFrame.Initialize();
            _ButtonAddKeyFrame.SetText("+ Key");
            _ButtonAddKeyFrame.SetBackgroundColor(Color.FromRGB(255, 0, 0));
            _ButtonAddKeyFrame.SetSize(0, 0);
            _ButtonAddKeyFrame.SetFontSize(FontSize);
            _ButtonAddKeyFrame.ClickedEvent += OnButtonAddKeyFrameClicked;

            //_TrackWithEdit = new TrackWithEdit(GetTrackItem(), this);
            //_TrackWithEdit.SetPosition(0, 0, 0, 0);
            //_TrackWithEdit.SetRange(decimal.MinValue, decimal.MaxValue);
            //_TrackWithEdit.TextChangedEvent += OnEditTextChanged;

            _EditWithProgress = new EditWithProgress(GetTrackItem());
            _EditWithProgress.SetPosition(0, 0, 0, 0);
            _EditWithProgress.SetRange(decimal.MinValue, decimal.MaxValue);
            _EditWithProgress.TextChangedEvent += OnEditTextChanged;



            if (Parent is ComponentTrack && ((ComponentTrack)Parent).PropertyStr == "Rotation")
            {
                _EditWithProgress.SetReadOnly(true);
            }

            if (Parent is MaterialTrack || this is NotifyTrack)
            {
                GetTrackItem().AddChild(_ButtonAddKeyFrame);
            }
        }

        public override void UpdateLayout(bool IsVisible, int Width, int Indent, ref int Y)
        {
            int ButtonAddKeyFrameWidth = _ButtonAddKeyFrame.CalculateTextWidth();
            int ButtonAddKeyFrameHeight = ButtonHeight;
            int ButtonAddKeyFrameX = Width - ButtonAddKeyFrameWidth - SpanX;
            int ButtonAddKeyFrameY = (ItemHeight - ButtonAddKeyFrameHeight) / 2;
            _ButtonAddKeyFrame.SetPosition(ButtonAddKeyFrameX, ButtonAddKeyFrameY, ButtonAddKeyFrameWidth, ButtonAddKeyFrameHeight);

            int EditX = ButtonAddKeyFrameX - ButtonAddKeyFrameWidth * 2 - SpanX * 2;
            _EditWithProgress.SetPosition(EditX, ButtonAddKeyFrameY, ButtonAddKeyFrameWidth * 20, ButtonAddKeyFrameHeight);
            base.UpdateLayout(IsVisible, Width, Indent, ref Y);
        }

        protected virtual void OnButtonAddKeyFrameClicked(Button Sender)
        {
            decimal Key = _ScaleUI.GetCurrentUI().GetHeadLocation();
            if (KeyFrames.BinarySearch(new SimpleKeyFrame(null, this, Key)) >= 0) return;
            object Value = GetValue((float)Key);
            AddNewKey(Key, Value);
        }

        #region Relate to 
        public void OnEditTextChanged(Control Sender)
        {
            EditOperation_ModifyCinematicEdit EditOperation = new EditOperation_ModifyCinematicEdit(this, _EditWithProgress.GetOldText(), _EditWithProgress.GetEditValue().GetText());
            EditOperationManager.GetInstance().AddOperation_AutoCombine(EditOperation);
            AddNewTrack(this);
            CinematicUI.GetInstance().ForceSetHeadLocation(CinematicUI.GetInstance().GetHeadLocation());
            CinematicUI.GetInstance().SetModified();
        }

        public override void AddNewTrack(Track _Track)
        {
            ComponentTrack ComponentTrack = (ComponentTrack)_Track.GetParentTrack();
            Component Component = ComponentTrack.GetComponent();
            if (ComponentTrack != null && (ComponentTrack is MaterialTrack || Component is CrossEditor.Light))
            {
                NumericTrack NumericTrack = (NumericTrack)_Track;
                if (NumericTrack != null)
                {
                    ((NumericTrack)_Track).PropertyAddKeyFrame(_Track.GetScaleUI().GetCurrentUI().GetHeadLocation(), float.Parse(_EditWithProgress.GetEditValue().GetText()));
                }
            }
            else if (ComponentTrack != null)
            {
                PropertyInfo Info = ComponentTrack.PreviewingProperty;
                object NewValue = ExtractProperty(ComponentTrack);
                if (Component is PostProcessVolumeComponent || Component is SkyAtmosphereComponent || Component is SkyLightComponent || Component is TODLightComponent)
                {
                    string[] SplitString = ComponentTrack.GetDisplayPropertyName().Split(".");
                    List<String> StringList = new List<String>(SplitString);
                    if (Component is PostProcessVolumeComponent)
                    {
                        Type DataType = ((PostProcessVolumeComponent)Component).AddComponentData.PostProcessVolumeSettings.GetType();
                        PropertyInfo PropertyInfo1 = DataType.GetProperty(SplitString[1]);
                        object Obj = PropertyInfo1.GetValue(((PostProcessVolumeComponent)Component).AddComponentData.PostProcessVolumeSettings);
                        RecursiveGetValue(Obj, StringList, 2, NewValue, this);
                        ((PostProcessVolumeComponent)Component).OnComponentDataChanged();
                    }
                    if (Component is SkyAtmosphereComponent)
                    {
                        SkyAtmosphereComponent SkyAtmosphereComponent = (SkyAtmosphereComponent)Component;
                        PropertyInfo PropertyInfo1 = SkyAtmosphereComponent.GetType().GetProperty(StringList[0]);
                        object Obj = PropertyInfo1.GetValue(SkyAtmosphereComponent);
                        RecursiveGetValue(Obj, StringList, 1, NewValue, this);
                    }
                    if (Component is TODLightComponent)
                    {
                        TODLightComponent TODLightComponent = (TODLightComponent)Component;
                        PropertyInfo PropertyInfo1 = TODLightComponent.GetType().GetProperty(StringList[0]);
                        object Obj = PropertyInfo1.GetValue(TODLightComponent);
                        RecursiveGetValue(Obj, StringList, 1, NewValue, this);
                    }
                    if (Component is SkyLightComponent)
                    {
                        SkyLightComponent SkyLightComponent = (SkyLightComponent)Component;
                        PropertyInfo PropertyInfo1 = SkyLightComponent.GetType().GetProperty(StringList[0]);
                        object Obj = PropertyInfo1.GetValue(SkyLightComponent);
                        RecursiveGetValue(Obj, StringList, 1, NewValue, this);
                    }
                }
                else
                {
                    if (Info != null)
                    {
                        Info.SetValue(Component, (object)NewValue);
                    }
                }
                Entity Entity = ComponentTrack.GetPrevieEntity();
                Entity.SyncDataFromEngine();
                NumericTrack NumericTrack = (NumericTrack)_Track;
                if (NumericTrack != null)
                {
                    ((NumericTrack)_Track).AutoAddKeyFrame();
                }
            }
        }

        void RecursiveGetValue(Object Obj, List<String> SplitString, int index, Object NewValue, Track _Track)
        {
            Type Type = Obj.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(SplitString[index]);
            if (index == SplitString.Count - 1)
            {
                if (Obj.GetType().Name == "TODLightConfig")
                {
                    float ClampedValue = Math.Max(0.0f, (float)NewValue);
                    uint Value = (uint)ClampedValue;
                    PropertyInfo.SetValue(Obj, (object)UInt32.Parse(Value.ToString()));
                    ((TODLightComponent)((ComponentTrack)_Track.GetParentTrack()).GetComponent()).Config = (TODLightConfig)Obj;
                    return;
                }

                PropertyInfo.SetValue(Obj, (object)NewValue);
                if (Obj.GetType().Name == "SkyAtmosphereConfig")
                {
                    ((SkyAtmosphereComponent)((ComponentTrack)_Track.GetParentTrack()).GetComponent()).Config = (SkyAtmosphereConfig)Obj;
                }
                else if (Obj.GetType().Name == "SkyAtmosphereOuterParam")
                {
                    ((SkyAtmosphereComponent)((ComponentTrack)_Track.GetParentTrack()).GetComponent()).OuterParam = (SkyAtmosphereOuterParam)Obj;
                }
                else if (Obj.GetType().Name == "SkyLightComponentG")
                {
                    ((SkyLightComponent)((ComponentTrack)_Track.GetParentTrack()).GetComponent()).SkyLight = (SkyLightComponentG)Obj;
                }
            }
            else
            {
                object SubObj = PropertyInfo.GetValue(Obj);
                RecursiveGetValue(SubObj, SplitString, ++index, NewValue, _Track);
            }
        }

        protected object ExtractProperty(ComponentTrack ComponentTrack)
        {
            List<Track> Children = ComponentTrack.GetChildList();
            if (Children.Count == 3)
            {
                if (ColorList.Exists(t => t == ComponentTrack.PropertyStr))
                {
                    Float3 Color = new Float3();
                    Color.x = float.Parse(((FinalTrack)Children[0]).GetEditWithPrcogress().GetEditValue().GetText());
                    Color.y = float.Parse(((FinalTrack)Children[1]).GetEditWithPrcogress().GetEditValue().GetText());
                    Color.z = float.Parse(((FinalTrack)Children[2]).GetEditWithPrcogress().GetEditValue().GetText());
                    return (object)Color;
                }
                Double3 Value = new Double3();
                Value.x = double.Parse(((FinalTrack)Children[0]).GetEditWithPrcogress().GetEditValue().GetText());
                Value.y = double.Parse(((FinalTrack)Children[1]).GetEditWithPrcogress().GetEditValue().GetText());
                Value.z = double.Parse(((FinalTrack)Children[2]).GetEditWithPrcogress().GetEditValue().GetText());
                return (object)Value;
            }
            else if (Children.Count == 1)
            {
                float Value = float.Parse(((FinalTrack)Children[0]).GetEditWithPrcogress().GetEditValue().GetText());
                return (object)Value;
            }
            else if (Children.Count == 4)
            {
                if (ComponentTrack.PropertyStr == "Rotation")
                {
                    Quaternion64 Value = new Quaternion64();
                    Value.x = double.Parse(((FinalTrack)Children[0]).GetEditWithPrcogress().GetEditValue().GetText());
                    Value.y = double.Parse(((FinalTrack)Children[1]).GetEditWithPrcogress().GetEditValue().GetText());
                    Value.z = double.Parse(((FinalTrack)Children[2]).GetEditWithPrcogress().GetEditValue().GetText());
                    Value.w = double.Parse(((FinalTrack)Children[2]).GetEditWithPrcogress().GetEditValue().GetText());
                    return (object)Quaternion64.Quaternion64ToEuler(new Quaternion64(Value)).ToDegree();
                }
                else
                {
                    Float4 Value = new Float4();
                    Value.x = float.Parse(((FinalTrack)Children[0]).GetEditWithPrcogress().GetEditValue().GetText());
                    Value.y = float.Parse(((FinalTrack)Children[1]).GetEditWithPrcogress().GetEditValue().GetText());
                    Value.z = float.Parse(((FinalTrack)Children[2]).GetEditWithPrcogress().GetEditValue().GetText());
                    Value.w = float.Parse(((FinalTrack)Children[2]).GetEditWithPrcogress().GetEditValue().GetText());
                    return (object)Value;
                }
            }
            return null;
        }

        protected List<string> ColorList = new List<string>()
        {
            "Color","MieScattCoeff","AbsorptiCoeff","MieAbsorCoeff","RayScattCoeff","GroundAlbedo3",
            "DirectionalInscatter", "LightColor", "Inscatter", "Saturation", "Contrast", "Gamma", "Gain", "Offset",
            "OverlayColor", "OverlayScale", "BloomTint", "DirectionalInscatter", "Albedo", "ColorNear", "ColorMid", "ColorFar",
            "BloomTint",
        };
        #endregion
    }
}
