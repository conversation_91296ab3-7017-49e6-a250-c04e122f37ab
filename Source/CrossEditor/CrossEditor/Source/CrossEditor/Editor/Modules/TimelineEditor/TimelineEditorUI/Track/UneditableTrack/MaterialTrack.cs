using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    public class MaterialTrack : ComponentTrack
    {
        protected static Dictionary<Type, string> PropertyTypeToCurveType = new Dictionary<Type, string>() {
            { typeof(Vector3f), "Float3" },
            { typeof(Float3), "Float3" },
            { typeof(Vector2f), "Float2" },
            { typeof(float), "Float1" },
            { typeof(double), "Float1" },
            { typeof(Double3), "Float3" },
            { typeof(Float4), "Float4" },
            { typeof(Vector4f), "Float4" }
        };


        public Material _Material = null;

        protected object _Context;
        private MaterialCurveContext Context { get { return _Context as MaterialCurveContext; } set { _Context = value; } }

        /// <summary>
        /// Model's index, valid after settled
        /// </summary>
        public int ModelIndex { get { return Context != null ? Context.ModelIndex : -1; } }

        /// <summary>
        /// SubModel's index, valid after settled 
        /// </summary>
        public int SubModelIndex { get { return Context != null ? Context.SubModelIndex : -1; } }

        /// <summary>
        /// Material's particular float parameter
        /// </summary>
        public override string PropertyStr { get { return Context != null ? Context.ParamName.GetCString() : ""; } }

        /// <summary>
        /// Always reflect float type
        /// </summary>
        /// <returns></returns>
        public override bool IsPropertyInitialized { get { return Context.ParamName.GetCString().Length > 0 && Context.ModelIndex >= 0 && Context.SubModelIndex >= 0; } }

        /// <summary>
        /// Whether to change the mother material, or just this material instance
        /// </summary>
        public virtual bool MotherMaterial { get { return Context.MotherMaterial; } set { Context.MotherMaterial = value; } }

        public override Entity Previewing
        {
            get { return _Previewing; }
            set { _Previewing = value; OnEntitySelected(); }
        }

        #region 

        public ComboBox _ParamSelect = null;

        protected Edit _ParamLabel = null;

        protected Check _MotherMaterialCheck = null;

        protected Edit _MotherMaterialLabel = null;

        #endregion
        public MaterialTrack() : base()
        {
            _Context = new MaterialCurveContext();
        }

        public override void Initialize(ScaleUI ScaleUI, Track Parent, string Name, object TagObject = null)
        {
            base.Initialize(ScaleUI, Parent, Name, TagObject);

            // Component's property Settled ui items Create

            _ComponentLabel.SetText("Main");

            _PropertyLabel.SetText("Sub");

            _ParamLabel = new Edit();
            _ParamLabel.Initialize(EditMode.Simple_SingleLine);
            _ParamLabel.SetText("Para");
            _ParamLabel.SetTextAlign(TextAlign.CenterCenter);
            _ParamLabel.SetFontSize(FontSize);
            _ParamLabel.SetReadOnly(true);
            _ParamLabel.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GREEN);

            //_ButtonBar.AddChild(_ParamLabel);

            // Component's property not Settled ui items Create

            _ParamSelect = new ComboBox();
            _ParamSelect.Initialize();
            _ParamSelect.ItemSelectedEvent += OnParameterItemSelect;
            _ParamSelect.SetFontSize(FontSize);

            _ButtonBar.AddChild(_ParamSelect);

            _MotherMaterialLabel = new Edit();
            _MotherMaterialLabel.Initialize(EditMode.Simple_SingleLine);
            _MotherMaterialLabel.SetText("Parent");
            _MotherMaterialLabel.SetTextAlign(TextAlign.CenterCenter);
            _MotherMaterialLabel.SetSize(0, 0);
            _MotherMaterialLabel.SetFontSize(FontSize);
            _MotherMaterialLabel.SetTextColor(Color.FromRGB(204, 255, 204));
            _MotherMaterialLabel.SetBackgroundColor(Color.EDITOR_UI_COLOR_KEY);
            _MotherMaterialLabel.SetReadOnly(true);
            _MotherMaterialLabel.SetEnable(false);

            _ButtonBar.AddChild(_MotherMaterialLabel);

            _MotherMaterialCheck = new Check();
            _MotherMaterialCheck.Initialize();
            _MotherMaterialCheck.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _MotherMaterialCheck.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _MotherMaterialCheck.SetAutoCheck(true);
            _MotherMaterialCheck.SetEnable(true);
            _MotherMaterialCheck.SetChecked(false);
            _MotherMaterialCheck.ClickedEvent += OnMotherMaterialCheckClicked;

            _ButtonBar.AddChild(_MotherMaterialCheck);
        }

        protected virtual void OnMotherMaterialCheckClicked(Check Sender)
        {
            MotherMaterial = Sender.GetChecked();
            ControllableUnitSystemG.SetMotherMaterial(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, _Component.Entity.GetEntityIdStruct());
            if (MotherMaterial)
            {
                var mat = _Material.ResourcePtr as Clicross.resource.Material;
                mat.RemoveProperty(new Clicross.NameID(PropertyStr));
            }
            CinematicUI.GetInstance().ForceSetHeadLocation(CinematicUI.GetInstance().GetHeadLocation());
        }

        protected Material SelectMaterial(Entity entity, int mainModelIndex, int subModelIndex)
        {
            var comp = entity.GetComponent(typeof(ModelComponent)) as ModelComponent;

            var models = comp.Models;
            if (mainModelIndex >= models.Count)
                return null;
            var model = models[mainModelIndex];

            if (subModelIndex >= model.LODProperties.Count)
                return null;
            var subModel = model.LODProperties[0].SubModels[subModelIndex];
            var materialPath = subModel.MaterialPath;

            var materialPtr = Clicross.ModelSystemG.Model_GetMaterialResource(entity.World._WorldInterface, entity.EntityID, mainModelIndex, subModelIndex);

            if (materialPtr == null)
                return null;

            var material = new Material(materialPtr);
            material.Reload();
            material.SetPath(ResourceManager.Instance().ConvertGuidToPath(materialPath));

            return material;
        }

        public virtual string GetDisplayName()
        {
            return string.Format("({0}).({1}).{2}",
                    _ComponentSelect.GetItemText(Context.ModelIndex),
                    _PropertySelect.GetItemText(Context.SubModelIndex),
                    PropertyStr);
        }

        public override void UpdateLayout(bool IsVisible, int Width, int Indent, ref int Y)
        {
            bVisible = IsVisible;

            int singleLayerHeight = ItemHeight;
            // property is null process
            //if (!IsPropertyInitialized)
            //{
            //    foreach (var Child in Children)
            //        Child.UpdateLayout(bVisible && _CheckExpand.GetChecked(), Width, Indent + 1, ref Y);
            //    return;
            //}

            GetTrackItem().SetVisible(IsVisible);
            if (IsVisible)
            {
                GetTrackItem().SetPosition(0, Y, Width, singleLayerHeight);
            }

            // modify visible by property validate
            bool showSettled = IsPropertyInitialized && bVisible;

            /* Settled view items */
            _ComponentLabel.SetVisible(showSettled);
            _MotherMaterialLabel.SetVisible(showSettled);
            _MotherMaterialCheck.SetVisible(showSettled);
            _PropertyLabel.SetVisible(false);
            //_ParamLabel.SetVisible(false);
            _CheckExpand.SetVisible(showSettled);

            if (showSettled)
            {
                int currentWidth = HieraWidth;

                int CheckExpandY = (singleLayerHeight - INDENT_WIDTH) / 2;
                _CheckExpand.SetVisible(Children.Count > 0);
                _CheckExpand.SetPosition(currentWidth + SpanX, CheckExpandY, INDENT_WIDTH, INDENT_WIDTH);
                currentWidth += SpanX + INDENT_WIDTH;

                int leftItemCount = 3;

                int LabelNameWeight = (Width - currentWidth - SpanX * leftItemCount) / leftItemCount;
                int LabelNameHeight = singleLayerHeight - 4;
                int LabelNameY = (singleLayerHeight - LabelNameHeight) / 2;

                _ComponentLabel.SetText(GetDisplayName());
                _ComponentLabel.SetPosition(currentWidth + SpanX, LabelNameY, Math.Max(_ComponentLabel.CalculateTextWidth(), 150), LabelNameHeight);
                currentWidth += SpanX + LabelNameWeight;

                _MotherMaterialLabel.SetText("Parent");
                int MMtlWidth = 60;
                int MMtlHeight = ButtonHeight;
                int MMtlX = Width - MMtlWidth;
                int MMtlY = (ItemHeight - MMtlHeight) / 2;
                _MotherMaterialLabel.SetPosition(MMtlX, MMtlY, MMtlWidth, MMtlHeight);

                _MotherMaterialCheck.SetPosition(MMtlX - 20, MMtlY, 20, MMtlHeight);
                _MotherMaterialCheck.SetChecked(MotherMaterial);
            }

            /* Not settled view items */
            _ComponentSelect.SetVisible(!showSettled && bVisible);
            _PropertySelect.SetVisible(!showSettled && bVisible);
            _ParamSelect.SetVisible(!showSettled && bVisible);

            if (!showSettled && bVisible)
            {
                int currentWidth = 0;

                int Span = 10;
                int leftItemCount = 3;

                int ComboWeight = (Width - Span * leftItemCount) / leftItemCount;
                int ComboHeight = singleLayerHeight - 4;
                int ComboY = (singleLayerHeight - ComboHeight) / 2;

                _ComponentSelect.SetPosition(currentWidth + Span, ComboY, ComboWeight, ComboHeight);
                currentWidth += Span + ComboWeight;

                _PropertySelect.SetPosition(currentWidth + Span, ComboY, ComboWeight, ComboHeight);
                currentWidth += Span + ComboWeight;

                _ParamSelect.SetPosition(currentWidth + Span, ComboY, ComboWeight, ComboHeight);
            }

            if (bVisible)
                Y += singleLayerHeight;

            bool ChildVisibility = bVisible && _CheckExpand.GetChecked();
            foreach (var Child in Children)
                Child.UpdateLayout(ChildVisibility, Width, Indent + 1, ref Y);
        }

        public override void ExtractComponentValueToSelected(decimal axisPos)
        {
            if (IsPropertyInitialized == false)
                return;

            var selectedKeys = GetSelectedKeys();
            object propertyValue = _Material.GetPropertyValue(Context.ParamName.GetCString());

            if (selectedKeys.Count != 0)
            {
                foreach (var keyframe in selectedKeys)
                {
                    var bindObject = keyframe.BindObject as ICollection<KeyFrame>;

                    // handle all track got validate keys
                    foreach (var bindKey in bindObject)
                    {
                        (bindKey.OwnerTrack as NumericTrack).ModifyValue(bindKey, propertyValue);
                        axisPos = bindKey.GetKeyValue();
                    }

                    // handle all track no near key found
                    var notExistTracks = Children.Where(t =>
                    {
                        return bindObject.FirstOrDefault(b => b.OwnerTrack == t) == null;
                    }).ToList();

                    foreach (var track in notExistTracks)
                    {
                        track.AddNewKey(axisPos, propertyValue);
                    }
                }
            }
            else
            {
                foreach (var track in Children)
                {
                    track.AddNewKey(axisPos, propertyValue);
                }
            }
        }

        public override void ExtractComponentValueToEdit(decimal axisPos)
        {
            if (IsPropertyInitialized == false)
                return;
            object propertyValue = null;
            var mat = _Material.ResourcePtr as Clicross.resource.Material;
            if (Children.Count == 1)
            {
                float f1 = 0f;
                f1 = mat.GetPropertyFloat(new Clicross.NameID(Context.ParamName.GetCString()));
                propertyValue = f1;
            }
            else if (Children.Count == 3)
            {
                var v = mat.GetPropertyFloat3(new Clicross.NameID(Context.ParamName.GetCString()));
                Vector3f v3 = new Vector3f(v.x, v.y, v.z);
                propertyValue = v3;
            }
            else if (Children.Count == 4)
            {
                var v = mat.GetPropertyFloat4(new Clicross.NameID(Context.ParamName.GetCString()));
                Vector4f v4 = new Vector4f(v.x, v.y, v.z, v.w);
                propertyValue = v4;
            }

            foreach (var track in Children)
            {
                var item = (NumericTrack)track;
                item.WriteValue(propertyValue);
            }
        }

        public override bool SetComponentAndProperty(Entity PreviewingEntity, UniqueString ComponentName, UniqueString PropertyName)
        {
            if (PreviewingEntity == null)
                return false;
            Previewing = PreviewingEntity;

            var comp = _Previewing.GetComponent(typeof(ModelComponent));
            if (comp == null)
                return false;
            _Component = comp;

            return true;
        }

        public bool SetMaterialCurveContext(MaterialCurveContext inContext)
        {
            return SetModelIndexAndParamName(inContext.ModelIndex, inContext.SubModelIndex, inContext.ParamName);
        }
        public bool SetModelIndexAndParamName(int ModelIndex, int SubModelIndex, UniqueString ParamName)
        {
            if (Previewing == null || _Component == null)
                return false;

            _ComponentCandidates = BuildEnumerableComponents();
            Context.ModelIndex = ModelIndex;
            BuildEnumerableSubmodelInfos();

            Context.SubModelIndex = SubModelIndex;
            _Material = SelectMaterial(_Previewing, Context.ModelIndex, Context.SubModelIndex);

            if (_Material != null)
            {
                Context.ParamName = ParamName;
                BuildEnumerablePropertyInfos(null);
                return true;
            }
            else
            {
                return false;
            }
        }

        public bool IsSameMaterialCurveContext(int ModelIndex, int SubModelIndex, string ParamName)
        {
            return this.ModelIndex == ModelIndex && this.SubModelIndex == SubModelIndex && this.PropertyStr == ParamName;
        }

        protected override void ShowAddTrackMenu() { }

        public override void Update()
        {
            base.Update();

            // handle vector track
            if (Children.Count > 0)
            {
                KeyFrames.Clear();

                foreach (var Child in Children)
                {
                    foreach (var KeyFrame in Child.GetKeyFrames())
                    {
                        int Index = KeyFrames.BinarySearch(KeyFrame);
                        if (Index < 0)
                        {
                            VectorKeyFrame NewKeyFrame = new VectorKeyFrame(new List<KeyFrame>() { KeyFrame }, this, KeyFrame.GetKeyValue());
                            NewKeyFrame.SolidFrameCount = Children.Count;
                            KeyFrames.Add(NewKeyFrame);
                        }
                        else
                            (KeyFrames[Index].BindObject as ICollection<KeyFrame>).Add(KeyFrame);
                    }
                }

                KeyFrames.Sort();
            }
            // handle float track
            else
            { }
        }

        public override void Draw(UIManager UIManager)
        {
            RectangleF Bound = GetBound();
            if (Bound.Y == 0)
                return;

            var holder = ParentTrack as ComponentTrackHolder;

            // remove closest points in the very beginning
            if (Children.Count > 0)
            {
                float preKeyScreenX = -1.0f;
                var filtered = KeyFrames.Where(k =>
                {
                    float curKeyScreenX = (float)_ScaleUI.ValueToScreenX(k.GetKeyValue());

                    if (preKeyScreenX > 0 && Math.Abs(preKeyScreenX - curKeyScreenX) < 2)
                        return false;

                    preKeyScreenX = curKeyScreenX;
                    return true;
                });

                // draw the left keys
                foreach (var key in filtered)
                {
                    // auto select by cinematic ui's cursor
                    if (bSelected)
                    {
                        var distance = holder.GetScreenDistance(key);
                        if (distance < 5)
                            key.bSelected = true;
                    }

                    key.Draw();
                }
            }
        }

        // Main model index handle here
        protected override void OnComponentItemSelected(ComboBox Sender)
        {
            var selected = Sender.GetSelectedItemText();
            var mainIndex = Sender.GetSelectedItemIndex();

            var comp = _Component as ModelComponent;
            var models = comp.Models;

            if (mainIndex < models.Count)
            {
                Context.ModelIndex = mainIndex;
                BuildEnumerableSubmodelInfos();
            }
        }

        // Main model select candidates
        protected override IEnumerable<Type> BuildEnumerableComponents()
        {
            var comp = _Component as ModelComponent;
            var models = comp.Models;

            var candidates = new List<Type>();

            _ComponentSelect.ClearItems();
            for (int i = 0; i < models.Count; i++)
            {
                Model Model = models[i];
                string ModelPath = ResourceManager.Instance().ConvertGuidToPath(Model.ModelPath);
                string ItemName = string.Format("Model[{0}] {1}", i.ToString(), ModelPath.Split('/').Last());
                _ComponentSelect.AddItem(ItemName);
                candidates.Add(typeof(string));
            }
            /*foreach (var model in models)
            {
                string modelPath = model.ModelPath;
                modelPath = ResourceManager.Instance().ConvertGuidToPath(modelPath);
                _ComponentSelect.AddItem(modelPath.Substring(modelPath.LastIndexOf('/') + 1));
                candidates.Add(typeof(string));
            }*/

            return candidates;
        }

        // Sub model index handle here
        protected override void OnPropertyItemSelected(ComboBox Sender)
        {
            var selected = Sender.GetSelectedItemText();
            var subModelIndex = Sender.GetSelectedItemIndex();

            var comp = _Component as ModelComponent;
            var models = comp.Models;
            var model = models[Context.ModelIndex];

            if (subModelIndex < model.LODProperties.Count)
            {
                Context.SubModelIndex = subModelIndex;
                _Material = SelectMaterial(_Previewing, Context.ModelIndex, Context.SubModelIndex);

                if (_Material != null)
                    BuildEnumerablePropertyInfos(null);
            }
        }

        // Sub model select candidates
        protected virtual void BuildEnumerableSubmodelInfos()
        {
            var comp = _Component as ModelComponent;
            var models = comp.Models;
            var model = models[Context.ModelIndex];

            _PropertySelect.SetEnable(true);
            _PropertySelect.ClearItems();
            for (int i = 0; i < model.LODProperties.Count; i++)
            {
                SubModelProperty SubModel = model.LODProperties[0].SubModels[i];
                string MaterialPath = ResourceManager.Instance().ConvertGuidToPath(SubModel.MaterialPath);
                string ItemName = string.Format("Element[{0}] {1}", i.ToString(), MaterialPath.Split('/').Last());
                _PropertySelect.AddItem(ItemName);
            }
            /*foreach (var subModel in model.SubModelProperties)
            {
                string materialPath = subModel.MaterialPath;
                materialPath = ResourceManager.Instance().ConvertGuidToPath(materialPath);
                _PropertySelect.AddItem(materialPath.Substring(materialPath.LastIndexOf('/') + 1));
            }*/
        }

        private Type GetMatParamType(Material.Property Property)
        {
            return Property.Value.GetType();
            //return typeof(float);
        }

        // Parameter name handle here
        protected virtual void OnParameterItemSelect(ComboBox Sender)
        {
            var selected = Sender.GetSelectedItemText();
            AddParameterItem(selected);
        }

        public void AddParameterItem(string Params)
        {
            Context.ParamName = new UniqueString(Params);

            ComponentTrackHolder holder = ParentTrack as ComponentTrackHolder;
            if (holder.IsAleadyExisting(this))
            {
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information,
                    string.Format("failed assignment for repeat element"));
                return;
            }
            var property = _Material.Properties.Where(p =>
               p.Name.Equals(Params)).ToList()[0];

            UniqueString CurveType = null;
            CurveType = new UniqueString("");
            if (property != null)
            {
                Type PropertyType = property.Value.GetType();
                if (PropertyTypeToCurveType.ContainsKey(PropertyType))
                {
                    CurveType = new UniqueString(PropertyTypeToCurveType[PropertyType]);
                }
                else
                {
                    CurveType = new UniqueString("Float1");
                }
            }
            ControllableUnitSystemG.CreateCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, CurveType, _Component.Entity.GetEntityIdStruct());
            FloatCurveListInfo info = GetFloatCurveListInfo(property);
            ControllableUnitSystemG.SetCurveControllerInfo(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, info, _Component.Entity.GetEntityIdStruct());

            RefreshTrackList();
            // Set default key at the init position.
            //ExtractComponentValueToSelected(0);
            ExtractComponentValueToEdit(0);

            if (_ScaleUI.GetCurrentUI() is CinematicUI)
            {
                CinematicUI CinematicUI = _ScaleUI.GetCurrentUI() as CinematicUI;
                if (CinematicUI.CheckIsPreviewing(_Component.Entity))
                {
                    CinematicUI.SetModified();
                }
            }
        }

        // Parameter select candidates
        protected override IEnumerable<PropertyInfo> BuildEnumerablePropertyInfos(Type componentType)
        {
            if (_Material == null)
                return new List<PropertyInfo>();

            var properties = _Material.Properties;
            properties = properties.Where(p =>
               p.Value.GetType().Equals(typeof(Vector4f)) ||
               p.Value.GetType().Equals(typeof(Vector3f)) ||
               p.Value.GetType().Equals(typeof(Vector2f)) ||
               p.Value.GetType().Equals(typeof(float))).ToList();

            _ParamSelect.SetEnable(true);
            _ParamSelect.ClearItems();
            foreach (var property in properties)
            {
                bool bAlreadyExist = false;
                if (ParentTrack is ComponentTrackHolder Holder)
                {
                    foreach (var Track in Holder.GetChildList())
                    {
                        if (Track != this && Track is MaterialTrack Child)
                        {
                            if (Child.ModelIndex == ModelIndex &&
                                Child.SubModelIndex == SubModelIndex &&
                                Child.PropertyStr == property.Name)
                            {
                                bAlreadyExist = true;
                                break;
                            }
                        }
                    }
                }
                if (!bAlreadyExist)
                {
                    _ParamSelect.AddItem(property.Name);
                }
            }

            RefreshTrackList();
            return new List<PropertyInfo>();
        }

        public override void RefreshTrackList()
        {
            ClearTrack();
            if (_Previewing == null)
            {
                return;
            }

            FloatCurveList list = new FloatCurveList();
            ControllableUnitSystemG.GetCurveControllerList(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, list, _Previewing.GetEntityIdStruct());
            int index = 0;
            Entity tempPreviewingEntity = _Previewing;
            foreach (var item in list.mTracks)
            {
                var track = new NumericTrack();
                track.Initialize(_ScaleUI, this, item.Name.GetCString(), index);
                track.GetCurve().RuntimeCurve = item;
                track.CanBeDeleted = false;
                track.CanBeEdit = PropertyStr != "Rotation";

                track.GetCurve().PostModifiedCurveEvent += () =>
                {
                    bool ret = ControllableUnitSystemG.SetCurveControllerTrack(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, track.GetCurve().RuntimeCurve.Name, track.GetCurve().RuntimeCurve, tempPreviewingEntity.GetEntityIdStruct());
                    if (_ScaleUI.GetCurrentUI() is CinematicUI)
                    {
                        CinematicUI CinematicUI = _ScaleUI.GetCurrentUI() as CinematicUI;
                        if (ret && CinematicUI.CheckIsPreviewing(_Component.Entity))
                        {
                            CinematicUI.SetModified();
                        }
                    }
                };

                Color trackColor = Color.White;
                if (_TrackNameToColor.ContainsKey(item.Name.GetCString()))
                {
                    trackColor = _TrackNameToColor[item.Name.GetCString()];
                }
                track.GetCurve().UnselectedColor = trackColor;

                AddTrack(track);
                index++;
            }
        }

        protected override void OnEntitySelected()
        {
            var comp = _Previewing.GetComponent(typeof(ModelComponent));
            if (comp == null)
                return;
            _Component = comp;

            _ComponentCandidates = BuildEnumerableComponents();
        }

        public override List<MenuItem> GetMenuItems()
        {
            var MenuItems = new List<MenuItem>();

            if (IsPropertyInitialized && _Property != null && _Property.PropertyType.Equals(typeof(float)))
            {
                MenuItem MenuItem_EditCurve = new MenuItem();
                MenuItem_EditCurve.SetText("Edit Curve");
                MenuItem_EditCurve.ClickedEvent += (Sender) =>
                {
                    CurveEditorUI.GetInstance().LoadFromCurves(new[] { Curve });
                    MainUI.GetInstance().ActivateDockingCard_CurveEditor();
                };
                MenuItems.Add(MenuItem_EditCurve);
            }

            return MenuItems;
        }

        public MaterialCurveContext GetContext()
        {
            return Context;
        }

        public FloatCurveListInfo GetFloatCurveListInfo(Material.Property Property)
        {
            FloatCurveListInfo info = new FloatCurveListInfo();
            if (GetMatParamType(Property) == typeof(Vector4f))
            {
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items[0].CurveName = new UniqueString("X");
                info.Items[1].CurveName = new UniqueString("Y");
                info.Items[2].CurveName = new UniqueString("Z");
                info.Items[3].CurveName = new UniqueString("W");
            }
            else if (GetMatParamType(Property) == typeof(Vector3f))
            {
                if (Context.ParamName.GetCString() == "Rotation")
                {
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items[0].CurveName = new UniqueString("Pitch");
                    info.Items[1].CurveName = new UniqueString("Yaw");
                    info.Items[2].CurveName = new UniqueString("Roll");
                }
                else
                {
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items[0].CurveName = new UniqueString("X");
                    info.Items[1].CurveName = new UniqueString("Y");
                    info.Items[2].CurveName = new UniqueString("Z");
                }
            }
            else if (GetMatParamType(Property) == typeof(Vector2f))
            {
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items[0].CurveName = new UniqueString("X");
                info.Items[1].CurveName = new UniqueString("Y");
            }
            else if (GetMatParamType(Property) == typeof(float))
            {
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items[0].CurveName = Context.ParamName;
            }
            return info;
        }

        #region Fault tolerance
        public virtual bool Material_IsProperty()
        {
            foreach (var Property in _Material.Properties)
            {
                if (Property.Name == Context.ParamName.GetCString())
                {
                    return true;
                }
            }

            return false;
        }
        #endregion
    }
}
