using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    public class ParticleMaterialTrack : MaterialTrack
    {
        private ParticleMaterialCurveContext Context { get { return _Context as ParticleMaterialCurveContext; } set { _Context = value; } }

        public int EmitterIndex { get { return Context != null ? Context.EmitterIndex : -1; } }
        public int MaterialIndex { get { return Context != null ? Context.MaterialIndex : -1; } }
        public override string PropertyStr { get { return Context != null ? Context.ParamName.GetCString() : ""; } }
        public override bool MotherMaterial { get { return Context.MotherMaterial; } set { Context.MotherMaterial = value; } }
        public override bool IsPropertyInitialized
        {
            get
            {
                return Context != null && Context.ParamName.GetCString().Length > 0 && Context.EmitterIndex >= 0 && Context.MaterialIndex >= 0;
            }
        }

        public ParticleMaterialTrack() : base()
        {
            _Context = new ParticleMaterialCurveContext();
        }

        protected override void OnMotherMaterialCheckClicked(Check Sender)
        {
            MotherMaterial = Sender.GetChecked();
            ControllableUnitSystemG.SetMotherMaterial(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, _Component.Entity.GetEntityIdStruct());
            if (MotherMaterial)
            {
                var mat = _Material.ResourcePtr as Clicross.resource.Material;
                mat.RemoveProperty(new Clicross.NameID(PropertyStr));
            }
            CinematicUI.GetInstance().ForceSetHeadLocation(CinematicUI.GetInstance().GetHeadLocation());
        }

        protected Material GetMaterial(int EmitterIndex, int MaterialIndex)
        {
            var EmitterInfos = GetParticleSystemInfo().EmitterInfos;
            var EmitterInfo = EmitterInfos[EmitterIndex];

            string MaterialPath = "";
            bool Valid = false;
            if (EmitterInfo.RendererType == ParticleRendererType.Sprite)
            {
                Valid = MaterialIndex == 0;
                if (Valid) MaterialPath = EmitterInfo.SpriteRenderer.MaterialPath;
            }
            else if (EmitterInfo.RendererType == ParticleRendererType.Mesh)
            {
                if (EmitterInfo.MeshRenderer.MaterialSlots.Count == 0)
                {
                    Valid = MaterialIndex == 0;
                    if (Valid) MaterialPath = EmitterInfo.MeshRenderer.MaterialPath;
                }
                else
                {
                    Valid = MaterialIndex < EmitterInfo.MeshRenderer.MaterialSlots.Count;
                    if (Valid) MaterialPath = EmitterInfo.MeshRenderer.MaterialSlots[MaterialIndex].MaterialPath;
                }
            }

            if (Valid)
            {
                var MaterialPtr = Clicross.ParticleSimulationSystemG.FX_GetParticleEmitterMaterialPtr(Previewing.World._WorldInterface, Previewing.EntityID, EmitterIndex, MaterialIndex);
                if (MaterialPtr.GetPointer() != IntPtr.Zero)
                {
                    Material Material = new Material(MaterialPtr);
                    Material.Reload();
                    Material.SetPath(ResourceManager.Instance().ConvertPathToGuid(MaterialPath));

                    return Material;
                }
            }
            return null;
        }

        public override string GetDisplayName()
        {
            return string.Format("({0}).({1}).{2}",
                    _ComponentSelect.GetItemText(Context.EmitterIndex),
                    _PropertySelect.GetItemText(Context.MaterialIndex),
                    PropertyStr);
        }

        public override void ExtractComponentValueToSelected(decimal axisPos)
        {
            if (IsPropertyInitialized == false)
                return;

            var selectedKeys = GetSelectedKeys();
            object propertyValue = _Material.GetPropertyValue(PropertyStr);

            if (selectedKeys.Count != 0)
            {
                foreach (var keyframe in selectedKeys)
                {
                    var bindObject = keyframe.BindObject as ICollection<KeyFrame>;

                    // handle all track got validate keys
                    foreach (var bindKey in bindObject)
                    {
                        (bindKey.OwnerTrack as NumericTrack).ModifyValue(bindKey, propertyValue);
                        axisPos = bindKey.GetKeyValue();
                    }

                    // handle all track no near key found
                    var notExistTracks = Children.Where(t =>
                    {
                        return bindObject.FirstOrDefault(b => b.OwnerTrack == t) == null;
                    }).ToList();

                    foreach (var track in notExistTracks)
                    {
                        track.AddNewKey(axisPos, propertyValue);
                    }
                }
            }
            else
            {
                foreach (var track in Children)
                {
                    track.AddNewKey(axisPos, propertyValue);
                }
            }
        }

        public override void ExtractComponentValueToEdit(decimal axisPos)
        {
            if (IsPropertyInitialized == false)
                return;
            object propertyValue = null;
            var mat = _Material.ResourcePtr as Clicross.resource.Material;
            if (Children.Count == 1)
            {
                float f1 = mat.GetPropertyFloat(new Clicross.NameID(Context.ParamName.GetCString()));
                propertyValue = f1;
            }
            else if (Children.Count == 3)
            {
                var v = mat.GetPropertyFloat3(new Clicross.NameID(Context.ParamName.GetCString()));
                Vector3f v3 = new Vector3f(v.x, v.y, v.z);
                propertyValue = v3;
            }
            else if (Children.Count == 4)
            {
                var v = mat.GetPropertyFloat4(new Clicross.NameID(Context.ParamName.GetCString()));
                Vector4f v4 = new Vector4f(v.x, v.y, v.z, v.w);
                propertyValue = v4;
            }

            foreach (var track in Children)
            {
                var item = (NumericTrack)track;
                item.WriteValue(propertyValue);
            }
        }

        public override bool SetComponentAndProperty(Entity PreviewingEntity, UniqueString ComponentName, UniqueString PropertyName)
        {
            if (PreviewingEntity == null)
                return false;
            Previewing = PreviewingEntity;

            var comp = _Previewing.GetComponent(typeof(ParticleSystemComponent));
            if (comp == null)
                return false;
            _Component = comp;

            return true;
        }

        public bool SetParticleMaterialCurveContext(ParticleMaterialCurveContext inContext)
        {
            return SetEmitterIndexAndParamName(inContext.EmitterIndex, inContext.MaterialIndex, inContext.ParamName);
        }

        public bool SetEmitterIndexAndParamName(int EmitterIndex, int MaterialIndex, UniqueString ParamName)
        {
            if (Previewing == null || _Component == null)
                return false;

            _ComponentCandidates = BuildEnumerableComponents();
            Context.EmitterIndex = EmitterIndex;
            BuildEnumerableSubmodelInfos();

            Context.MaterialIndex = MaterialIndex;
            _Material = GetMaterial(EmitterIndex, MaterialIndex);

            if (_Material != null)
            {
                Context.ParamName = ParamName;
                BuildEnumerablePropertyInfos(null);
                return true;
            }
            else
            {
                return false;
            }
        }

        protected override void OnEntitySelected()
        {
            var comp = _Previewing.GetComponent(typeof(ParticleSystemComponent));
            if (comp == null)
                return;
            _Component = comp;

            _ComponentCandidates = BuildEnumerableComponents();
        }

        protected ParticleSystemInfo GetParticleSystemInfo()
        {
            ParticleSystemComponent Component = _Component as ParticleSystemComponent;
            ParticleSystemResource SystemResource = Resource.Get(Component.SystemInfo.ParticleSystemPath) as ParticleSystemResource;
            return SystemResource.SystemInfo;
        }

        protected override IEnumerable<Type> BuildEnumerableComponents()
        {
            var EmitterInfos = GetParticleSystemInfo().EmitterInfos;
            var Candidates = new List<Type>();

            _ComponentSelect.ClearItems();
            for (int i = 0; i < EmitterInfos.Count; i++)
            {
                ParticleEmitterInfo Emitter = EmitterInfos[i];
                string ItemName = string.Format("Emitter[{0}] {1}", i.ToString(), Emitter.EmitterName);
                _ComponentSelect.AddItem(ItemName);
                Candidates.Add(typeof(string));
            }

            return Candidates;
        }

        protected override void OnComponentItemSelected(ComboBox Sender)
        {
            var EmitterIndex = Sender.GetSelectedItemIndex();
            var EmitterInfos = GetParticleSystemInfo().EmitterInfos;

            if (EmitterIndex < EmitterInfos.Count)
            {
                Context.EmitterIndex = EmitterIndex;
                BuildEnumerableSubmodelInfos();
            }
        }

        protected override void BuildEnumerableSubmodelInfos()
        {
            var EmitterInfos = GetParticleSystemInfo().EmitterInfos;
            var EmitterInfo = EmitterInfos[EmitterIndex];

            _PropertySelect.SetEnable(true);
            _PropertySelect.ClearItems();

            if (EmitterInfo.RendererType == ParticleRendererType.Sprite)
            {
                string MaterialPath = ResourceManager.Instance().ConvertGuidToPath(EmitterInfo.SpriteRenderer.MaterialPath);
                string ItemName = string.Format("Material[{0}] {1}", 0, MaterialPath.Split('/').Last());
                _PropertySelect.AddItem(ItemName);
            }
            else if (EmitterInfo.RendererType == ParticleRendererType.Mesh)
            {
                if (EmitterInfo.MeshRenderer.MaterialSlots.Count == 0)
                {
                    string MaterialPath = ResourceManager.Instance().ConvertGuidToPath(EmitterInfo.MeshRenderer.MaterialPath);
                    string ItemName = string.Format("Material[{0}] {1}", 0, MaterialPath.Split('/').Last());
                    _PropertySelect.AddItem(ItemName);
                }
                else
                {
                    for (int i = 0; i < EmitterInfo.MeshRenderer.MaterialSlots.Count; i++)
                    {
                        var MaterialSlot = EmitterInfo.MeshRenderer.MaterialSlots[i];
                        string MaterialPath = ResourceManager.Instance().ConvertGuidToPath(MaterialSlot.MaterialPath);
                        string ItemName = string.Format("Element[{0}] {1}", i.ToString(), MaterialPath.Split('/').Last());
                        _PropertySelect.AddItem(ItemName);
                    }
                }
            }
        }

        protected override void OnPropertyItemSelected(ComboBox Sender)
        {
            var MaterialIndex = Sender.GetSelectedItemIndex();

            _Material = GetMaterial(EmitterIndex, MaterialIndex);
            if (_Material != null)
            {
                Context.MaterialIndex = MaterialIndex;
                BuildEnumerablePropertyInfos(null);
            }
        }

        protected override IEnumerable<PropertyInfo> BuildEnumerablePropertyInfos(Type componentType)
        {
            if (_Material == null)
                return new List<PropertyInfo>();

            var properties = _Material.Properties;
            properties = properties.Where(p =>
               p.Value.GetType().Equals(typeof(Vector4f)) ||
               p.Value.GetType().Equals(typeof(Vector3f)) ||
               p.Value.GetType().Equals(typeof(Vector2f)) ||
               p.Value.GetType().Equals(typeof(float))).ToList();

            _ParamSelect.SetEnable(true);
            _ParamSelect.ClearItems();
            foreach (var property in properties)
            {
                bool bAlreadyExist = false;
                if (ParentTrack is ComponentTrackHolder Holder)
                {
                    foreach (var Track in Holder.GetChildList())
                    {
                        if (Track != this && Track is ParticleMaterialTrack Child)
                        {
                            if (Child.EmitterIndex == EmitterIndex &&
                                Child.MaterialIndex == MaterialIndex &&
                                Child.PropertyStr == property.Name)
                            {
                                bAlreadyExist = true;
                                break;
                            }
                        }
                    }
                }
                if (!bAlreadyExist)
                {
                    _ParamSelect.AddItem(property.Name);
                }
            }

            RefreshTrackList();
            return new List<PropertyInfo>();
        }

        protected override void OnParameterItemSelect(ComboBox Sender)
        {
            string Selected = Sender.GetSelectedItemText();
            Context.ParamName = new UniqueString(Selected);
            Material.Property Property = _Material.Properties.Find(Item => Item.Name == Selected);

            if (ParentTrack is ComponentTrackHolder Holder && Holder.IsAleadyExisting(this) || Property == null)
            {
                return;
            }

            UniqueString CurveType = new UniqueString("");
            Type PropertyType = Property.Value.GetType();
            if (PropertyTypeToCurveType.ContainsKey(PropertyType))
            {
                CurveType = new UniqueString(PropertyTypeToCurveType[PropertyType]);
            }
            else
            {
                CurveType = new UniqueString("Float1");
            }

            ControllableUnitSystemG.CreateCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, CurveType, _Component.Entity.GetEntityIdStruct());
            FloatCurveListInfo Info = new FloatCurveListInfo();

            if (PropertyType == typeof(Vector4f))
            {
                Info.Items.Add(new FloatCurveListInfoItem());
                Info.Items.Add(new FloatCurveListInfoItem());
                Info.Items.Add(new FloatCurveListInfoItem());
                Info.Items.Add(new FloatCurveListInfoItem());
                Info.Items[0].CurveName = new UniqueString("X");
                Info.Items[1].CurveName = new UniqueString("Y");
                Info.Items[2].CurveName = new UniqueString("Z");
                Info.Items[3].CurveName = new UniqueString("W");
            }
            else if (PropertyType == typeof(Vector3f))
            {
                if (Context.ParamName.GetCString() == "Rotation")
                {
                    Info.Items.Add(new FloatCurveListInfoItem());
                    Info.Items.Add(new FloatCurveListInfoItem());
                    Info.Items.Add(new FloatCurveListInfoItem());
                    Info.Items[0].CurveName = new UniqueString("Pitch");
                    Info.Items[1].CurveName = new UniqueString("Yaw");
                    Info.Items[2].CurveName = new UniqueString("Roll");
                }
                else
                {
                    Info.Items.Add(new FloatCurveListInfoItem());
                    Info.Items.Add(new FloatCurveListInfoItem());
                    Info.Items.Add(new FloatCurveListInfoItem());
                    Info.Items[0].CurveName = new UniqueString("X");
                    Info.Items[1].CurveName = new UniqueString("Y");
                    Info.Items[2].CurveName = new UniqueString("Z");
                }
            }
            else if (PropertyType == typeof(Vector2f))
            {
                Info.Items.Add(new FloatCurveListInfoItem());
                Info.Items.Add(new FloatCurveListInfoItem());
                Info.Items[0].CurveName = new UniqueString("X");
                Info.Items[1].CurveName = new UniqueString("Y");
            }
            else if (PropertyType == typeof(float))
            {
                Info.Items.Add(new FloatCurveListInfoItem());
                Info.Items[0].CurveName = Context.ParamName;
            }
            ControllableUnitSystemG.SetCurveControllerInfo(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, Info, _Component.Entity.GetEntityIdStruct());

            RefreshTrackList();
            // Set default key at the init position.
            //ExtractComponentValueToSelected(0);
            ExtractComponentValueToEdit(0);

            if (_ScaleUI.GetCurrentUI() is CinematicUI)
            {
                CinematicUI CinematicUI = _ScaleUI.GetCurrentUI() as CinematicUI;
                if (CinematicUI.CheckIsPreviewing(_Component.Entity))
                {
                    CinematicUI.SetModified();
                }
            }
        }

        public override void RefreshTrackList()
        {
            ClearTrack();
            if (_Previewing == null)
            {
                return;
            }

            FloatCurveList list = new FloatCurveList();
            ControllableUnitSystemG.GetCurveControllerList(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, list, _Previewing.GetEntityIdStruct());
            int index = 0;
            Entity tempPreviewingEntity = _Previewing;
            foreach (var item in list.mTracks)
            {
                var track = new NumericTrack();
                track.Initialize(_ScaleUI, this, item.Name.GetCString(), index);
                track.GetCurve().RuntimeCurve = item;
                track.CanBeDeleted = false;
                track.CanBeEdit = base.PropertyStr != "Rotation";

                track.GetCurve().PostModifiedCurveEvent += () =>
                {
                    bool ret = ControllableUnitSystemG.SetCurveControllerTrack(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, track.GetCurve().RuntimeCurve.Name, track.GetCurve().RuntimeCurve, tempPreviewingEntity.GetEntityIdStruct());
                    if (_ScaleUI.GetCurrentUI() is CinematicUI)
                    {
                        CinematicUI CinematicUI = _ScaleUI.GetCurrentUI() as CinematicUI;
                        if (ret && CinematicUI.CheckIsPreviewing(_Component.Entity))
                        {
                            CinematicUI.SetModified();
                        }
                    }
                };

                Color trackColor = Color.White;
                if (_TrackNameToColor.ContainsKey(item.Name.GetCString()))
                {
                    trackColor = _TrackNameToColor[item.Name.GetCString()];
                }
                track.GetCurve().UnselectedColor = trackColor;

                AddTrack(track);
                index++;
            }
        }

        public new ParticleMaterialCurveContext GetContext()
        {
            return Context;
        }

        public override bool Material_IsProperty()
        {
            foreach (var Property in _Material.Properties)
            {
                if (Property.Name == Context.ParamName.GetCString())
                {
                    return true;
                }
            }

            return false;
        }
    }
}
