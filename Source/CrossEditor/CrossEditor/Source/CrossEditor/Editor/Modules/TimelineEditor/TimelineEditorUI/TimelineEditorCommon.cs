using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public delegate void ExtandedRenamerRenameEventHandler(ExtendedRenamer Sender, string NewName, string OldName);
    public delegate void ExtandedRenamerCloseEventHandler(ExtendedRenamer Sender);

    public class ExtendedRenamer
    {
        public Edit _Edit;
        private string _OldName;

        public event ExtandedRenamerRenameEventHandler RenameEvent;
        public event ExtandedRenamerCloseEventHandler CloseEvent;

        public ExtendedRenamer(UIManager UIManager)
        {
            _Edit = new Edit();
            _Edit.Initialize(EditMode.Simple_SingleLine);
            _Edit.SetBaseX(1);
            _Edit.SetTextAlign(TextAlign.CenterLeft);
            _Edit.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _Edit.SetVisible(false);
            UIManager.GetRoot().AddChild(_Edit);
            EditContextUI.GetInstance().RegisterEdit(_Edit);

            _Edit.PaintEvent += (Sender) =>
            {
                Sender.PaintThis();
                Sender.PaintBorder(Sender.GetScreenX(), Sender.GetScreenY());
            };
            _Edit.SelfFocusChangedEvent += OnEditFocusChanged;
            _Edit.KeyDownEvent += OnEditKeyDown;
            _Edit.TextChangedEvent += (Sender) =>
            {
                ClearError();
            };

            _OldName = "";
        }

        public UIManager GetUIManager()
        {
            if (_Edit != null)
            {
                return _Edit.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public void SetError()
        {
            _Edit.SetBorderColor(Color.FromRGB(255, 0, 0));
        }

        public void ClearError()
        {
            _Edit.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
        }

        public void Rename(string OldName, int X, int Y, int Width, int Height, int FontSize,
            ExtandedRenamerRenameEventHandler RenameEvent, ExtandedRenamerCloseEventHandler CloseEvent)
        {
            _Edit.LoadSource(OldName);
            _Edit.SetFontSize(FontSize);
            _Edit.SetPosition(X, Y, Width, Height);
            GetUIManager().GetRoot().AddChild(_Edit);
            EditContextUI.GetInstance().RegisterEdit(_Edit);
            _Edit.SetVisible(true);
            _Edit.SetFocus();

            this._OldName = OldName;

            this.RenameEvent += RenameEvent;
            this.CloseEvent += CloseEvent;
        }

        public void Close()
        {
            RenameEvent = null;
            CloseEvent = null;

            OperationQueue.GetInstance().AddOperation(() => GetUIManager().GetRoot().RemoveChild(_Edit));
        }

        private void OnEditFocusChanged(Control Sender)
        {
            if (!Sender.IsFocused())
                CloseEvent?.Invoke(this);
        }

        private void OnEditKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            bool bNoneModifiedKey = GetDevice().IsNoneModifiersDown();
            if (bNoneModifiedKey)
            {
                if (Key == Key.Escape)
                    CloseEvent?.Invoke(this);
                else if (Key == Key.Enter)
                    RenameEvent?.Invoke(this, _Edit.GetText(), _OldName);
            }
        }
    }

    ///<summary>
    /// !!! Only use as a stub
    ///</summary>
    class SeperatorMenuItem : MenuItem { }

    static class TimelineEditorCommon
    {
        public static void AddMenuItemsSeperately(Menu Menu, ICollection<MenuItem> MenuItems)
        {
            if (Menu.GetMenuItemCount() > 0 && MenuItems.Count > 0)
                Menu.AddSeperator();

            foreach (var Item in MenuItems)
            {
                if (Item is SeperatorMenuItem)
                    Menu.AddSeperator();
                else
                    Menu.AddMenuItem(Item);
            }
        }
    }
}
