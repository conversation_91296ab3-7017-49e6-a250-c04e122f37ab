using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using Clicegf;
using Newtonsoft.Json.Linq;

namespace CrossEditor
{
    class TODSetting_InspectorHelper
    {
        public static string internal_tod_name = "cegf::TODLightComponent";
        private Clicross.TODLightConfig _ConfigDefault;
        private int _TimeZone;
        private SceneBase _CachedScene = null;
        private ulong _CachedEntity;

        public TODSetting_InspectorHelper()
        {
            _ConfigDefault = new Clicross.TODLightConfig();
            _ConfigDefault.Year = 9999;
            _ConfigDefault.Month = 9999;
            _ConfigDefault.Day = 9999;
            _ConfigDefault.Hour = 9999;
            _ConfigDefault.Minute = 9999;
            _ConfigDefault.Second = 9999;

            _TimeZone = 0;
            ClearCachedValue();
        }

        private void ClearCachedValue()
        {
            _CachedEntity = ulong.MaxValue;
            _CachedScene = null;
        }

        private Clicegf.GameObject CheckAndGetCachedTODObject()
        {
            var Scene = HierarchyUI.GetInstance().GetScene();
            if (Scene == null || Scene != _CachedScene)
            {
                return null;
            }
            var World = Scene.GetWorld();
            if (World == null)
            {
                return null;
            }

            GOContext GoContext = new GOContext(World._WorldInterface);
            
            return GoContext.GetGameObject(_CachedEntity);
        }

        private Clicegf.GameObject QueryGameObjectWithTODComponent()
        {
            //var cachedObject = CheckAndGetCachedTODObject();
            //if (cachedObject != null)
            //{
            //    return cachedObject;
            //}

            ClearCachedValue();

            var Scene = HierarchyUI.GetInstance().GetScene();
            if (Scene == null)
            {
                return null;
            }

            _CachedScene = Scene;

            var World = Scene.GetWorld();
            if (World == null)
            {
                return null;
            }

            GOContext GoContext = new GOContext(World._WorldInterface);

            var gos = GoContext.GetGameObjectsWithComponent(internal_tod_name).holder;
            if (gos.Count == 0)
            {
                return null;
            }

            _CachedEntity = gos[0].GetObjectEntityID().GetValue();

            return gos[0];
        }

        private void OnChangeConfig(Clicegf.TODLightComponent comp)
        {
            comp.OnChangeConfig(false);
            comp.SyncAllTODLightComponents();
            
            TODSettingUI.GetInstance().SetTODDirty();

            var Scene = HierarchyUI.GetInstance().GetScene();
            if (Scene != null)
            {
                var World = Scene.GetWorld();
                Clicross.GameWorldInterface.World_TriggerRealTimeProbeCaptureByScript(World._WorldInterface);
            }

            float SolarEleAngle = comp.ComputeSolarElevationAngleAtCameraPosition();
            TODLightComponent.UpdateCinematicUI(SolarEleAngle);
        }

        private Clicegf.TODLightComponent QueryTODLightComponent()
        {
            var go = QueryGameObjectWithTODComponent();
            if (go == null)
                return null;

            var comp = go.GetComponentByMetaClassName(internal_tod_name);
            if (comp == null)
                return null;

            var tod_comp = comp as Clicegf.TODLightComponent;
            if (tod_comp == null)
                return null;

            return tod_comp;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "TODLight configuration", bKeyFrame = false, bReadOnly = true)]
        public string CurrentScene
        {
            get
            {
                string _None = "None";
                var Scene = HierarchyUI.GetInstance().GetScene();
                if (Scene == null)
                {
                    return _None;
                }

                return Scene.GetType().Name;
            }
            set
            {
            }
        }


        [PropertyInfo(PropertyType = "Struct", ToolTips = "TODLight configuration", bKeyFrame = false)]
        public Clicross.TODLightConfig Config
        {
            get
            {
                var tod_comp = QueryTODLightComponent();

                if (tod_comp == null)
                    return _ConfigDefault;

                return tod_comp.GetTODLightConfig();
            }
            set
            {
                var tod_comp = QueryTODLightComponent();

                if (tod_comp != null)
                {
                    tod_comp.SetTODLightConfig(value);
                    OnChangeConfig(tod_comp);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "TODLight TimeZone setting")]
        public int TimeZone
        {
            get
            {
                var tod_comp = QueryTODLightComponent();

                if (tod_comp == null)
                    return _TimeZone;

                return tod_comp.GetTODLightTimeZone();
            }
            set
            {
                var tod_comp = QueryTODLightComponent();

                if (tod_comp != null)
                {
                    tod_comp.SetTODLightTimeZone(value);
                    OnChangeConfig(tod_comp);
                }
            }
        }


        [PropertyInfo(PropertyType = "Auto", ToolTips = "Inspector UI Property Refresh Rate", bKeyFrame = false, bReadOnly = true)]
        public string PropertyRefreshRate
        {
            get
            {
                return "0.5s";
            }
        }
    }

    class TODSettingUI : DockingUI
    {
        static TODSettingUI _Instance = new TODSettingUI();
        static int SPAN_X = 5;

        public Panel _Container;

        public AlignedPanel _ButtonContainer;
        ScrollView _ScrollView;
        Panel _ScrollPanel;

        Inspector _Inspector;
        InspectorHandler _InspectorHandler;
        object _ObjectToInspect;

        private bool _TODDirty = false;
        private long _RefreshTime = 500;
        private long _CurrentRefreshTime = 0;

        public static TODSettingUI GetInstance()
        {
            return _Instance;
        }

        public TODSettingUI()
        {
            _ObjectToInspect = null;
        }

        public void SetTODDirty(bool dirty = true)
        {
            _TODDirty = dirty;
        }

        public bool Initialize()
        {
            _Container = new Panel();
            _Container.Initialize();
            _Container.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);

            base.Initialize("TODSetting", _Container);

            _ButtonContainer = new AlignedPanel(SPAN_X);
            _Container.AddChild(_ButtonContainer);

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.GetHScroll().SetEnable(false);
            _Container.AddChild(_ScrollView);
            _ScrollView.SetPosition(10, 40, 630, 420);

            _ScrollPanel = _ScrollView.GetScrollPanel();
            
            _ObjectToInspect = new TODSetting_InspectorHelper();

            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_ObjectToInspect); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            _Inspector = new Inspector_Struct_With_Property();
            _Inspector.InspectObject(_ObjectToInspect);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);
            UpdateLayout();

            return true;
        }

        public void UpdateInspector()
        {
            _Inspector.GetInspectorHandler().InspectObject();
            _Inspector.GetInspectorHandler().UpdateLayout();
        }

        public override void Update(long TimeElapsed)
        {
            if (IsDockingCardActive() && IsDockingCardDisplay())
            {
                if (!IsFocused())
                {
                    UpdateInspector();
                }

                if (_TODDirty)
                {
                    _CurrentRefreshTime += TimeElapsed;
                    if (_CurrentRefreshTime >= _RefreshTime)
                    {
                        InspectorUI.GetInstance().InspectObject();
                        _TODDirty = false;
                        _CurrentRefreshTime = 0;
                    }
                }
                else
                {
                    _CurrentRefreshTime = 0;
                }
            }
        }

        protected override void OnEnter()
        {
            UpdateInspector();
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bSizeChanged)
            {
                int SPAN_Y = 8;
                int Width = _Container.GetWidth();
                int Height = _Container.GetHeight();
                int Y = SPAN_Y;
                Y += SPAN_Y;
                int AvaliableWidth = Width - SPAN_X * 2;
                int Height1 = Height - Y - SPAN_Y;
                _ScrollView.SetPosition(SPAN_X, Y, AvaliableWidth, Height1);
                UpdateLayout();
            }
        }

        public void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }

    }
}
