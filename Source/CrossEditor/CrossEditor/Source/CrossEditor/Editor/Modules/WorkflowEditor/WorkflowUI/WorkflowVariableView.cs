using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class WorkflowVariableView : WorkflowContentMapView
    {
        public delegate void OnVarCategoryChangedEvent(string key, GbfValueFieldCategory category);

        public event OnVarCategoryChangedEvent OnVarCategoryChanged = null;
        private string RemoveNameSpace(string inName)
        {
            const string colon = "::";
            var index = inName.LastIndexOf(colon);
            if (index == -1)
            {
                return inName;
            }
            else
            {
                return inName.Substring(index + colon.Length);
            }
        }

        protected override string GetItemNameFromKey(string Key)
        {
            var variableDesc = GetDictionaryItem(_Dictionary, Key) as WorkflowVariableDesc;
            string className = variableDesc.Type.ToString();
            if (variableDesc.Type == GbfValueFieldType.UserObject)
            {
                className = RemoveNameSpace(variableDesc.GetUserObjectClassName());
            }
            return className + " " + Key;
        }
        protected override void BuildItemMenu(Menu MenuContextMenu)
        {
            MenuItem MenuItem_Type = new MenuItem();
            MenuItem_Type.SetText("Type");
            {
                Menu Menu_Type = new Menu(MenuContextMenu.GetUIManager());

                // find all basic types
                List<string> BasicTypes = new List<string>();
                GbfValueFieldType[] values = (GbfValueFieldType[])Enum.GetValues(typeof(GbfValueFieldType));

                for (int i = 0; i < values.Length - 1; i++)
                {
                    GbfValueFieldType value = values[i];
                    BasicTypes.Add(value.ToString());
                }
                // add sub types
                Menu_Type.AddMenuItem(BuildSubTypeItem("BasicTypes", GbfValueFieldCategory.BasicType, MenuContextMenu.GetUIManager(), BasicTypes));
                Menu_Type.AddMenuItem(BuildSubTypeItem("GameObjectRef", GbfValueFieldCategory.ClassRef, MenuContextMenu.GetUIManager(), GameFrameworkTools.Instance.GOContext.GOTypeNames));
                Menu_Type.AddMenuItem(BuildSubTypeItem("ComponentRef", GbfValueFieldCategory.ClassRef, MenuContextMenu.GetUIManager(), GameFrameworkTools.Instance.GOContext.GCompTypeNames));
                Menu_Type.AddMenuItem(BuildSubTypeItem("ResourceRef", GbfValueFieldCategory.ClassRef, MenuContextMenu.GetUIManager(), GameFrameworkTools.Instance.GOContext.ResourceTypeNames));

                Menu_Type.AddMenuItem(BuildSubTypeItem("Struct", GbfValueFieldCategory.Struct, MenuContextMenu.GetUIManager(), GameFrameworkTools.Instance.GOContext.StructTypeNames));

                MenuItem_Type.SetMenu(Menu_Type);
            }
            MenuContextMenu.AddMenuItem(MenuItem_Type);


            // tempClassList = GameFrameworkTools.Instance.GOContext.GCompTypeNames;
            base.BuildItemMenu(MenuContextMenu);
        }

        private MenuItem BuildSubTypeItem(string item_name, GbfValueFieldCategory cat, UIManager ui_manager, IEnumerable<string> classes)
        {
            int TotalClassNum = classes.Count();
            var BuildOnePage = (string PageName, int Start, int End) =>
            {
                MenuItem Item_Page = new MenuItem();
                Item_Page.SetText(PageName);

                Menu Menu_Page = new Menu(ui_manager);

                if (End > TotalClassNum)
                {
                    End = TotalClassNum;
                }

                for(int i = Start; i < End; i++)
                {
                    var tempClass = classes.ElementAt(i);
                    string name = RemoveNameSpace(tempClass);
                    MenuItem PageItem_Obj = new MenuItem();

                    PageItem_Obj.SetText(name);
                    PageItem_Obj.ClickedEvent += (Sender) =>
                    {
                        OnVarCategoryChanged(_ItemKey, cat);
                        TriggerMapViewChanged(MapViewChangeEvent.ItemUpdate, _ItemKey, tempClass);

                        RefreshButtons();
                    };

                    Menu_Page.AddMenuItem(PageItem_Obj);
                }
                Item_Page.SetMenu(Menu_Page);
                return Item_Page;
            };

            int page_size = 15;

            if (TotalClassNum <= page_size)
            {
                return BuildOnePage(item_name, 0, TotalClassNum);
            }

            MenuItem Item_Subtype = new MenuItem();
            Item_Subtype.SetText(item_name);
            Menu Menu_Subtype = new Menu(ui_manager);

            int totalPage = (TotalClassNum + page_size - 1) / page_size;
            for (int i = 0; i < totalPage; i++)
            {
                int start = i * page_size;
                int end = start + page_size;
                MenuItem MenuItem_Obj = BuildOnePage("Page" + (i + 1), start, end);
                Menu_Subtype.AddMenuItem(MenuItem_Obj);
            }
            Item_Subtype.SetMenu(Menu_Subtype);
            return Item_Subtype;
        }

        private void MenuItem_Obj_ClickedEvent(MenuItem Sender)
        {
            throw new NotImplementedException();
        }
    }
}
