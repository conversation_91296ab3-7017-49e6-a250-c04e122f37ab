using CEngine;
using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using static CrossEditor.WorkflowContentMapView;

namespace CrossEditor
{
    public class WorkflowArgDescWrapper
    {
        [PropertyInfo(PropertyType = "Auto")]
        public string Name { get; set; }
        [PropertyInfo(PropertyType = "Auto")]
        public string Description { get; set; }
        [PropertyInfo(PropertyType = "Auto")]
        public GbfValueFieldType Type { get; set; }
        [PropertyInfo(PropertyType = "Auto")]
        public string Value { get; set; }

    };

    public class WorkflowVarDescWrapper
    {
        //public WorkflowVariableDesc Native;
        [PropertyInfo(PropertyType = "Auto", bReadOnly = true)]
        public string Name { get; set; }
        [PropertyInfo(PropertyType = "Auto")]
        public string Description { get; set; }

        [PropertyInfo(PropertyType = "Auto", bReadOnly = true, ToolTips = "Internal type, read only.")]
        public GbfValueFieldType Type { get; set; }

        [PropertyInfo(PropertyType = "Auto", bReadOnly = true, ToolTips = "Internal meta class, read only.")]
        public string ObjectClassName { get; set; }

        [PropertyInfo(PropertyType = "Struct", DisplayName = "Default Value")]
        public Cligbf.logic.VarInspectorHelper VarInspectorHelper { get; set; }

        public void FromNative(WorkflowVariableDesc CurVar)
        {
            Name = CurVar.GetName();
            Description = CurVar.Description;
            Type = CurVar.Type;
            ObjectClassName = CurVar.GetUserObjectClassName();

            VarInspectorHelper = CurVar.GetInspectorHelper();
            VarInspectorHelper.SyncFromRuntime();
        }

        public void ToNative(WorkflowVariableDesc CurVar)
        {
            if (CurVar == null)
            {
                return;
            }
            CurVar.Description = Description;
            CurVar.Type = Type;
            CurVar.SetUserObjectClassName(ObjectClassName);
            VarInspectorHelper.UpdateToRuntime();
        }
    };

    public class WorkflowFunctionDescWrapper
    {
        public WorkflowFunctionDesc Native;
        public string Name;
        [PropertyInfo(PropertyType = "Auto")]
        public string Description { get; set; }
        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct")]
        public List<WorkflowArgDescWrapper> Args { get; set; }
        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct")]
        public List<WorkflowArgDescWrapper> Returns { get; set; }

        public WorkflowFunctionDescWrapper()
        {
            Args = new List<WorkflowArgDescWrapper>();
            Returns = new List<WorkflowArgDescWrapper>();
        }
        public void FromNative(WorkflowFunctionDesc CurFunc)
        {
            Native = CurFunc;
            Name = CurFunc.GetName();
            Description = CurFunc.Description;
            Args.Clear();
            foreach (var one_arg in CurFunc.Args)
            {
                WorkflowArgDescWrapper temp_new_arg = new WorkflowArgDescWrapper();
                temp_new_arg.Name = one_arg.Name;
                temp_new_arg.Description = one_arg.Description;
                temp_new_arg.Value = one_arg.Value;
                temp_new_arg.Type = one_arg.Type;
                Args.Add(temp_new_arg);
            }
            Returns.Clear();
            foreach (var one_arg in CurFunc.Returns)
            {
                WorkflowArgDescWrapper temp_new_arg = new WorkflowArgDescWrapper();
                temp_new_arg.Name = one_arg.Name;
                temp_new_arg.Description = one_arg.Description;
                temp_new_arg.Value = one_arg.Value;
                temp_new_arg.Type = one_arg.Type;
                Returns.Add(temp_new_arg);
            }
        }

        public void ToNative()
        {
            if (Native == null)
            {
                return;
            }
            Native.Description = Description;
            Native.Args.Clear();
            foreach (var one_arg in Args)
            {
                WorkflowArgDesc temp_new_arg = new WorkflowArgDesc();
                temp_new_arg.Name = one_arg.Name;
                temp_new_arg.Description = one_arg.Description;
                temp_new_arg.Value = one_arg.Value;
                temp_new_arg.Type = one_arg.Type;
                Native.Args.Add(temp_new_arg);
            }
            Native.Returns.Clear();
            foreach (var one_arg in Returns)
            {
                WorkflowArgDesc temp_new_arg = new WorkflowArgDesc();
                temp_new_arg.Name = one_arg.Name;
                temp_new_arg.Description = one_arg.Description;
                temp_new_arg.Value = one_arg.Value;
                temp_new_arg.Type = one_arg.Type;
                Native.Returns.Add(temp_new_arg);
            }
        }
    };

    public class WorkflowEventDescWrapper
    {
        public WorkflowEventDesc Native;
        public string Name;
        [PropertyInfo(PropertyType = "Auto")]
        public string Description { set; get; }
        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct")]
        public List<WorkflowArgDescWrapper> Args { set; get; }

        public WorkflowEventDescWrapper()
        {
            Args = new List<WorkflowArgDescWrapper>();
        }
        public void FromNative(WorkflowEventDesc CurEvent)
        {
            Native = CurEvent;
            Name = CurEvent.GetName();
            Description = CurEvent.Description;
            Args.Clear();
            foreach (var one_arg in CurEvent.Args)
            {
                WorkflowArgDescWrapper temp_new_arg = new WorkflowArgDescWrapper();
                temp_new_arg.Name = one_arg.Name;
                temp_new_arg.Description = one_arg.Description;
                temp_new_arg.Value = one_arg.Value;
                temp_new_arg.Type = one_arg.Type;
                Args.Add(temp_new_arg);
            }
        }

        public void ToNative()
        {
            if (Native == null)
            {
                return;
            }
            Native.Description = Description;
            Native.Args.Clear();
            foreach (var one_arg in Args)
            {
                WorkflowArgDesc temp_new_arg = new WorkflowArgDesc();
                temp_new_arg.Name = one_arg.Name;
                temp_new_arg.Description = one_arg.Description;
                temp_new_arg.Value = one_arg.Value;
                temp_new_arg.Type = one_arg.Type;
                Native.Args.Add(temp_new_arg);
            }
        }
    };

    public class WorkflowEditorUI : DockingUI
    {
        enum WorkflowMapType
        {
            invalid,
            graph,
            func,
            variable,
            events
        }
        public static string PreviewContextStr = "Preview Context";
        public static string WorkflowStr = "Workflow";

        WorkflowEditorContext mContext;
        ImGuiPanel mImGuiPanel;

        string mFilePath;
        string mSelectItemName;
        WorkflowMapType mSelectMapType = WorkflowMapType.invalid;

        WorkflowVarDescWrapper mVarDescWrapper = new WorkflowVarDescWrapper();
        WorkflowEventDescWrapper mEventDescWrapper = new WorkflowEventDescWrapper();

        WorkflowFunctionDescWrapper mFunctionDescWrapper = new WorkflowFunctionDescWrapper();

        VContainer mMainContainer = new VContainer();
        Button mSaveBtn = OperationBarUI.CreateTextButton("Save");
        Button mPlayBtn = OperationBarUI.CreateTextButton("Play");
        HSplitter HSplitter = new HSplitter();

        VSplitter VSplitter = new VSplitter();
        Panel PagePanel;
        Panel mViewPortPanel;
        WorkflowPreview mPreview;
        Button _ButtonEventGraph;
        Button _ButtonPreview;
        WorkflowContentMapView GraphView = new WorkflowContentMapView();
        WorkflowContentMapView FunctionView = new WorkflowContentMapView();
        WorkflowVariableView VariableView = new WorkflowVariableView();
        WorkflowContentMapView EventsView = new WorkflowContentMapView();

        WorkflowDetailView mDetailView;

        class GraphOperation : EditOperation
        {
            public WorkflowEditorContext Context { get; set; }

            public override void Undo()
            {
                Context.Undo();
            }

            public override void Redo()
            {
                Context.Redo();
            }
        }

        public class NativeListener : WorkflowEditorCallback
        {
            List<ExpressionCreateGroupInfo> menuInfoList;
            ExpressionCreateMenuInfo menuCategoryInfo;

            public WorkflowEditorUI Editor { get; set; }

            public override void OnOperation()
            {
                EditOperationManager.GetInstance().AddOperation(new GraphOperation { Context = Editor.mContext });
            }

            public override void OnSetCursor(int cursor)
            {
                switch (cursor)
                {
                    case 0: Editor.mImGuiPanel.Cursor = SystemCursor.Arrow; break;
                    case 1: Editor.mImGuiPanel.Cursor = SystemCursor.Edit; break;
                    case 2: Editor.mImGuiPanel.Cursor = SystemCursor.Dragging; break;
                    case 3: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNS; break;
                    case 4: Editor.mImGuiPanel.Cursor = SystemCursor.SizeWE; break;
                    case 5: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNESW; break;
                    case 6: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNWSE; break;
                    case 7: Editor.mImGuiPanel.Cursor = SystemCursor.Hand; break;
                    default: break;
                }
            }

            public override void OnSetInputScreenPosition(int x, int y)
            {
                Editor.GetUIManager().GetDevice().SetCaret(true, x + Editor.mImGuiPanel.Panel.GetScreenX(), y + Editor.mImGuiPanel.Panel.GetScreenY(), 1, 1);
            }
            public void CreateTreeByStringArray(string keyword, List<ExpressionCreateGroupInfo> groupInfos, Tree nodeTree, bool firstCreate)
            {
                TreeItem treeRoot = nodeTree.GetRootItem();
                treeRoot.ClearChildren();
                for (int index = 0; index < groupInfos.Count; index++)
                {
                    string categoryName = groupInfos.ElementAt(index).categoryName;
                    TreeItem treeItem = nodeTree.CreateItem();
                    treeRoot.AddChild(treeItem);
                    treeItem.SetFolder(true);
                    if (firstCreate)
                        treeItem.SetExpanded(false);
                    else
                        treeItem.SetExpanded(true);
                    treeItem.SetText(categoryName);
                    for (int i = 0; i < groupInfos.ElementAt(index).ExpressionInfos.Count; i++)
                    {
                        ExpressionCreateNodeInfo expressionCreateNodeInfo = groupInfos.ElementAt(index).ExpressionInfos.ElementAt(i);
                        TreeItem leafTreeItem = nodeTree.CreateItem();
                        treeItem.AddChild(leafTreeItem);
                        leafTreeItem.SetFolder(true);
                        leafTreeItem.SetExpanded(true);
                        leafTreeItem.SetText(expressionCreateNodeInfo.menuName);
                        leafTreeItem.SetTagString(expressionCreateNodeInfo.className);
                    }
                }

                if (keyword != "")
                {
                    TreeItems treeItemAll = new TreeItems();
                    treeItemAll.ClearItems();
                    treeItemAll.CollectVisibleItems(nodeTree.GetRootItem());
                    TreeItem treeItemSelected = nodeTree.GetRootItem();
                    for (int i = 0; i < treeItemAll.GetItemCount(); i++)
                    {
                        TreeItem treeItemInList = treeItemAll.GetItem(i);
                        if (treeItemInList.GetChildCount() == 0)
                        {
                            if (treeItemSelected == nodeTree.GetRootItem())
                            {
                                treeItemSelected = treeItemInList;
                            }
                            else
                            {
                                if (treeItemInList.GetText().IndexOf(keyword) < treeItemSelected.GetText().IndexOf(keyword))
                                {
                                    treeItemSelected = treeItemInList;
                                }
                            }
                        }
                    }
                    nodeTree.SelectItemNoEvent(treeItemSelected);
                }
            }

            public List<ExpressionCreateGroupInfo> ModifyMenuInfoByKeyword(List<ExpressionCreateGroupInfo> menuInfo, string keyword)
            {
                if (keyword == "")
                {
                    return menuInfo;
                }
                List<ExpressionCreateGroupInfo> menuInfoModified = new List<ExpressionCreateGroupInfo>();
                foreach (ExpressionCreateGroupInfo groupInfo in menuInfo)
                {
                    ExpressionCreateGroupInfo expressionCreateGroupInfo = new ExpressionCreateGroupInfo();
                    expressionCreateGroupInfo.categoryName = groupInfo.categoryName;
                    List<ExpressionCreateNodeInfo> expressionCreateNodeInfo = groupInfo.ExpressionInfos.ToList();
                    foreach (ExpressionCreateNodeInfo item in expressionCreateNodeInfo)
                    {
                        if (item.menuName.ToLower().Contains(keyword.ToLower()))
                        {
                            expressionCreateGroupInfo.ExpressionInfos.Add(item);
                        }
                    }
                    if (expressionCreateGroupInfo.ExpressionInfos.Count > 0)
                    {
                        menuInfoModified.Add(expressionCreateGroupInfo);
                    }
                }
                return menuInfoModified;
            }


            public override void OnCreateMenuAtPosition(int position_x, int position_y, ExpressionCreateMenuInfo menuCategories)
            {
                Menu MenuContextMenu = new Menu(Editor.GetUIManager());
                MenuItem testMenuItem = new MenuItem();
                Panel Container = new Panel();
                Tree nodeTree = new Tree();
                Edit editBox = new Edit();
                //prevent memory crash 
                menuCategoryInfo = new ExpressionCreateMenuInfo(menuCategories);
                menuInfoList = menuCategoryInfo.Groups.ToList();
                menuInfoList.Sort((x, y) => x.categoryName.CompareTo(y.categoryName));
                MenuContextMenu.Initialize();
                nodeTree.Initialize();
                nodeTree.SetTextAlign(TextAlign.TopLeft);
                nodeTree.GetRootItem().SetExpanded(true);
                nodeTree.SetPosition(2, 2, 300, 400);
                nodeTree.ItemSelectedEvent += (Sender, TreeItem) =>
                {
                    if (TreeItem.GetParent() != nodeTree.GetRootItem())
                    {
                        if (TreeItem.GetText() != "")
                        {
                            string selectItemInfo = TreeItem.GetTagString();
                            Editor.mContext.CreateNode(selectItemInfo, position_x, position_y);
                            Editor.GetUIManager().GetContextMenu().HideMenu();
                        }
                    }
                };

                nodeTree.KeyDownEvent += (Control Sender, Key Key, ref bool bContinue) =>
                {
                    bContinue = false;
                    if (Key == Key.Up)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        TreeItem rootItem = nodeTree.GetRootItem();
                        TreeItems treeItems = new TreeItems();
                        treeItems.ClearItems();
                        if (editBox.GetText() == "")
                            treeItems.CollectVisibleItems(rootItem);
                        else
                        {
                            TreeItems treeItemAll = new TreeItems();
                            treeItemAll.ClearItems();
                            treeItemAll.CollectVisibleItems(rootItem);
                            for (int i = 0; i < treeItemAll.GetItemCount(); i++)
                            {
                                TreeItem tempTreeItem = treeItemAll.GetItem(i);
                                if (tempTreeItem != nodeTree.GetRootItem() && tempTreeItem.GetParent() != nodeTree.GetRootItem())
                                {
                                    treeItems.CollectItems(tempTreeItem);
                                }
                            }
                        }
                        int num = treeItems.FindItem(selectedItem);
                        if (num > 0)
                        {
                            TreeItem item = treeItems.GetItem(num - 1);
                            nodeTree.SelectItemNoEvent(item);
                        }
                    }
                    else if (Key == Key.Down)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        TreeItem rootItem = nodeTree.GetRootItem();
                        TreeItems treeItems = new TreeItems();
                        treeItems.ClearItems();
                        if (editBox.GetText() == "")
                            treeItems.CollectVisibleItems(rootItem);
                        else
                        {
                            TreeItems treeItemAll = new TreeItems();
                            treeItemAll.ClearItems();
                            treeItemAll.CollectVisibleItems(rootItem);
                            for (int i = 0; i < treeItemAll.GetItemCount(); i++)
                            {
                                TreeItem tempTreeItem = treeItemAll.GetItem(i);
                                if (tempTreeItem != nodeTree.GetRootItem() && tempTreeItem.GetParent() != nodeTree.GetRootItem())
                                {
                                    treeItems.CollectItems(tempTreeItem);
                                }
                            }
                        }
                        int num = treeItems.FindItem(selectedItem);
                        if (num < treeItems.GetItemCount() - 1)
                        {
                            TreeItem item = treeItems.GetItem(num + 1);
                            nodeTree.SelectItemNoEvent(item);
                        }
                    }
                    else if (Key == Key.Enter)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        nodeTree.SelectItem(selectedItem);
                    }
                    else if (Key == Key.Left)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        if (selectedItem.GetExpanded() && selectedItem != nodeTree.GetRootItem())
                        {
                            selectedItem.SetExpanded(false);
                        }
                    }
                    else if (Key == Key.Right)
                    {
                        TreeItem selectedItem = nodeTree.GetSelectedItem();
                        if (!selectedItem.GetExpanded() && selectedItem != nodeTree.GetRootItem())
                        {
                            selectedItem.SetExpanded(true);
                        }
                    }
                };
                nodeTree.KeyUpEvent += (Control Sender, Key Key, ref bool bContinue) =>
                {
                    if (Key == Key.Up)
                    {

                    }
                    else if (Key == Key.Down)
                    {
                        bContinue = false;
                    }
                    else if (Key == Key.Enter)
                    {

                    }
                    else if (Key == Key.Left)
                    {

                    }
                    else if (Key == Key.Right)
                    {

                    }
                };
                int FontSize = 16;
                CreateTreeByStringArray("", menuInfoList, nodeTree, true);
                Container.AddChild(nodeTree);
                editBox.SetBackgroundColor(Color.FromRGBA(30, 50, 76, 255));
                editBox.SetFontSize(FontSize);
                editBox.Initialize(EditMode.Simple_SingleLine);
                editBox.SetWidth(300);
                editBox.SetHeight(FontSize + 4);
                editBox.SetPos(2, 2);
                editBox.SetFocus();
                editBox.KeyDownEvent += (Control Sender, Key Key, ref bool bContinue) =>
                {
                    if (Key == Key.Left || Key == Key.Right || Key == Key.Down || Key == Key.Up || Key == Key.Enter)
                    {
                        bool keyDownContinue = false;
                        nodeTree.OnKeyDown(Key, ref keyDownContinue);
                    }
                };
                editBox.TextChangedEvent += (Sender) =>
                {
                    if (editBox.GetText() == "")
                        CreateTreeByStringArray("", menuInfoList, nodeTree, true);
                    else
                        CreateTreeByStringArray(editBox.GetText(), ModifyMenuInfoByKeyword(menuInfoList, editBox.GetText()), nodeTree, false);
                };
                Container.AddChild(editBox);
                Container.SetSize(304, 400);
                testMenuItem.SetControl(Container);
                MenuContextMenu.AddMenuItem(testMenuItem);
                Editor.GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, position_x + Editor.mImGuiPanel.Panel.GetScreenX(), position_y + Editor.mImGuiPanel.Panel.GetScreenY());
            }
        }

        NativeListener mListener;

        Button PageButton(int X, string Text)
        {
            Button PageButton = new Button();
            PageButton.Initialize();
            PageButton.SetFontSize(16);
            PageButton.SetText(Text);
            PageButton.SetTextOffsetY(2);
            PageButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            PageButton.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            PageButton.SetPosition(X, 4, PageButton.CalculateTextWidth() + 8, 30);
            PageButton.ClickedEvent += OnButtonPageXClicked;
            return PageButton;
        }
        public WorkflowEditorUI(string FilePath)
        {
            mFilePath = FilePath;
            mListener = new NativeListener { Editor = this };
            mContext = new WorkflowEditorContext(FilePath, mListener);

            mImGuiPanel = new ImGuiPanel(mContext);
            mViewPortPanel = new Panel();
            mDetailView = new WorkflowDetailView(mContext);
            _ButtonEventGraph = PageButton(4, "EventGraph");
            _ButtonPreview = PageButton(4 + 103, "ViewPort");
            PagePanel = new Panel();
            PagePanel.Initialize();
            mPreview = new WorkflowPreview(mContext);
            PagePanel.SetPosition(4, 4, 1024, 10);
            PagePanel.AddChild(_ButtonEventGraph);
            PagePanel.AddChild(_ButtonPreview);
            PagePanel.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            mContext.SetWorkflowPreview(mPreview);
        }
        public virtual void DoSave()
        {
            mContext.Apply();
        }

        void ActivatePageButton(Button PageButton)
        {
            PageButton.SetBorderColor(Color.EDITOR_UI_DOCKING_CARD_HILIGHT_TITLE_COLOR);
            PageButton.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
        }

        void DeactivatePageButton(Button PageButton)
        {
            PageButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            PageButton.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
        }
        void OnButtonPageXClicked(Button Sender)
        {
            Button PageButton = Sender;
            DeactivatePageButton(_ButtonPreview);
            DeactivatePageButton(_ButtonEventGraph);
            ActivatePageButton(PageButton);
            mImGuiPanel.Panel.SetVisible(false);
            mViewPortPanel.SetVisible(false);
            Panel PagePanel = (Panel)PageButton.GetTagObject();
            PagePanel.SetVisible(true);
        }
        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            mViewPortPanel.Follow(mImGuiPanel.Panel);
        }

        public void Initialize()
        {
            mMainContainer.Initialize();
            mMainContainer.SetSize(1, 1);
            _ButtonEventGraph.SetTagObject(mImGuiPanel.Panel);
            _ButtonPreview.SetTagObject(mViewPortPanel);
            mViewPortPanel.Initialize();
            // OperationBar
            {
                OperationBarUI operationBar = new OperationBarUI();
                operationBar.Initialize();
                mSaveBtn.SetCanFocus(true);
                mSaveBtn.ClickedEvent += sender =>
                {
                    DoSave();
                    // clear all inspectors and re-inspect
                    //mDetailView.Inspect(null);
                    //GetUIManager().SetFocusControl(mSaveBtn);
                    //mSelectItemName = null;
                };
                operationBar.AddLeft(mSaveBtn);

                mPlayBtn.ClickedEvent += sender =>
                {
                    mPreview.Play();
                };
                operationBar.AddLeft(mPlayBtn);

                mMainContainer.AddFixedChild(operationBar.GetPanelBar());
            }

            // Left
            VContainer leftContainer = new VContainer();
            {
                leftContainer.Initialize();
                leftContainer.SetSize(1, 3);

                // Splitter
                VSplitter.Initialize();

                VSplitter VSplitter2 = new VSplitter();
                VSplitter2.Initialize();

                // Graphs
                VContainer GraphContainer = new VContainer();
                {
                    GraphContainer.Initialize();
                    GraphContainer.SetSize(1, 1);

                    GraphView.BindProperty(mContext.m_Graphs, "Graphs");
                    GraphView.OnMapViewItemSelect += (string key) =>
                    {
                        mSelectItemName = key;
                        mSelectMapType = WorkflowMapType.graph;
                        EditorLogger.Log(LogMessageType.Information, string.Format("key {0} type {1}", mSelectItemName, mSelectMapType));
                        mDetailView.AfterInspectObjectChange(string.Format("{0}::{1}", mSelectMapType, mSelectItemName));
                        mContext.SwitchGraph(WorkflowGraphType.Sequence, mSelectItemName);
                    };
                    GraphView.OnMapViewChanged += (MapViewChangeEvent Event, string key, string other_key) =>
                        {
                            mContext.OnGraphChange(key, other_key);
                        };
                    GraphView.OnAddToParent(GraphContainer);
                    VSplitter2.AddChild(GraphContainer);
                }

                // Functions
                VContainer FunctionContainer = new VContainer();
                {
                    FunctionContainer.Initialize();
                    FunctionContainer.SetSize(1, 1);

                    FunctionView.BindProperty(mContext.m_Functions, "Functions");
                    FunctionView.OnMapViewItemSelect += (string key) =>
                    {
                        mSelectItemName = key;
                        mSelectMapType = WorkflowMapType.func;
                        EditorLogger.Log(LogMessageType.Information, string.Format("key {0} type {1}", mSelectItemName, mSelectMapType));
                        mDetailView.AfterInspectObjectChange(string.Format("{0}::{1}", mSelectMapType, mSelectItemName));
                        mContext.SwitchGraph(WorkflowGraphType.Function, mSelectItemName);
                    };

                    FunctionView.OnMapViewChanged += (MapViewChangeEvent Event, string key, string other_key) =>
                    {
                        mContext.OnFunctionChange(key, other_key);
                    };
                    FunctionView.OnAddToParent(FunctionContainer);
                    VSplitter2.AddChild(FunctionContainer);
                }

                VSplitter VSplitter3 = new VSplitter();
                VSplitter3.Initialize();



                VContainer VariableContainer = new VContainer();
                {
                    VariableContainer.Initialize();
                    VariableContainer.SetSize(1, 1);
                    VariableView.BindProperty(mContext.m_NamedVariables, "Variables");
                    VariableView.OnVarCategoryChanged += (key, category) =>
                    {
                        if (mContext.m_NamedVariables.ContainsKey(key))
                        {
                            var CurVar = mContext.m_NamedVariables[key];
                            // to category
                            CurVar.Category = category;
                        }
                    };

                    VariableView.OnMapViewItemSelect += (string key) =>
                    {
                        mSelectItemName = key;
                        mSelectMapType = WorkflowMapType.variable;
                        EditorLogger.Log(LogMessageType.Information, string.Format("key {0} type {1}", mSelectItemName, mSelectMapType));
                        mDetailView.AfterInspectObjectChange(string.Format("{0}::{1}", mSelectMapType, mSelectItemName));
                    };
                    VariableView.OnMapViewChanged += (MapViewChangeEvent Event, string key, string other_key) =>
                    {
                        if (Event == MapViewChangeEvent.Rename)
                        {
                            mContext.OnNameVariableChange(key, other_key, VariableChangedType.Rename);
                        }
                        else if (Event == MapViewChangeEvent.ItemUpdate && mContext.m_NamedVariables.ContainsKey(key)) // change var type
                        {
                            GbfValueFieldType type;
                            if (Enum.TryParse(other_key, out type)) // is GbfValueFieldType
                            {
                                var CurVar = mContext.m_NamedVariables[key];
                                CurVar.Type = type;
                                CurVar.SetUserObjectClassName("");
                                mContext.OnNameVariableChange(key, "", VariableChangedType.TypeChanged);
                            }
                            else // is struct / object / component
                            {
                                var CurVar = mContext.m_NamedVariables[key];
                                CurVar.Type = GbfValueFieldType.UserObject;
                                CurVar.SetUserObjectClassName(other_key);
                                mContext.OnNameVariableChange(key, "", VariableChangedType.TypeChanged);
                            }
                        }
                        else if (Event == MapViewChangeEvent.Add) // Add
                        {
                            mContext.OnNameVariableChange(key, other_key, VariableChangedType.Add);
                        }
                        else if (Event == MapViewChangeEvent.Delete) // delete
                        {
                            mContext.OnNameVariableChange(key, other_key, VariableChangedType.Delete);
                        }

                        if (mSelectMapType == WorkflowMapType.variable && mSelectItemName != null)
                        {
                            mDetailView.Inspect(null);
                            if (mContext.m_NamedVariables.ContainsKey(mSelectItemName))
                            {
                                var CurVar = mContext.m_NamedVariables[mSelectItemName];
                                mVarDescWrapper.FromNative(CurVar);
                                mDetailView.Inspect(mVarDescWrapper);
                            }
                            else
                            {
                                mSelectItemName = null;
                                mSelectMapType = WorkflowMapType.invalid;
                            }
                        }
                    };

                    VariableView.OnAddToParent(VariableContainer);
                    VSplitter3.AddChild(VariableContainer);
                }

                VContainer EventContainer = new VContainer();
                {
                    VariableContainer.Initialize();
                    VariableContainer.SetSize(1, 1);
                    EventsView.BindProperty(mContext.m_Events, "Events");
                    EventsView.OnMapViewItemSelect += (string key) =>
                    {
                        mSelectItemName = key;
                        mSelectMapType = WorkflowMapType.events;
                        EditorLogger.Log(LogMessageType.Information, string.Format("key {0} type {1}", mSelectItemName, mSelectMapType));
                        mDetailView.AfterInspectObjectChange(string.Format("{0}::{1}", mSelectMapType, mSelectItemName));
                    };
                    EventsView.OnMapViewChanged += (MapViewChangeEvent Event, string key, string other_key) =>
                    {
                        mContext.OnEventChange(key, other_key);
                    };

                    EventsView.OnAddToParent(EventContainer);
                    VSplitter3.AddChild(EventContainer);
                }


                VSplitter.AddChild(VSplitter2);
                VSplitter.AddChild(VSplitter3);



                leftContainer.AddSizableChild(VSplitter, 1.0f);
            }

            HSplitter HSplitter2 = new HSplitter();
            {
                Panel left = new Panel();
                {
                    left.Initialize();
                    left.AddChild(PagePanel);
                    left.AddChild(mImGuiPanel.Panel);
                    left.AddChild(mViewPortPanel);
                    mImGuiPanel.Panel.SetVisible(true);
                    mViewPortPanel.SetVisible(false);
                }
                left.PositionChangedEvent += (Sender, bPositionChanged, bSizeChanged) =>
                {
                    if (bSizeChanged)
                    {
                        PagePanel.SetPosition(4, 4, left.GetWidth() - 8, 30);
                        int Y1 = PagePanel.GetY() + PagePanel.GetHeight();
                        mImGuiPanel.Panel.SetPosition(3, Y1 + 3, left.GetWidth() - 6, left.GetHeight() - Y1 - 6);
                        mViewPortPanel.SetPosition(3, Y1 + 3, left.GetWidth() - 6, left.GetHeight() - Y1 - 6);
                    }
                };
                // Right
                VContainer rightContainer = new VContainer();
                {
                    rightContainer.Initialize();
                    rightContainer.SetSize(1, 1);
                    mDetailView.OnAddToParent(rightContainer);
                }
                HSplitter2.AddChild(left);
                HSplitter2.AddChild(rightContainer);
            }


            // Splitter
            HSplitter.Initialize();
            HSplitter.AddChild(leftContainer);
            HSplitter.AddChild(HSplitter2);

            mMainContainer.AddSizableChild(HSplitter, 1);

            base.Initialize("Workflow Editor", mMainContainer);
            ///Preview
            mViewPortPanel.LeftMouseDownEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.CaptureMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.LeftButton, true);
            };
            mViewPortPanel.LeftMouseUpEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.ReleaseMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.LeftButton, false);
            };
            mViewPortPanel.RightMouseDownEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.CaptureMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.RightButton, true);
            };
            mViewPortPanel.RightMouseUpEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.ReleaseMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.RightButton, false);
            };
            mViewPortPanel.MiddleMouseDownEvent += (Control sender, int x, int y, ref bool _) => mPreview.OnKeyEvent((EditorKey)EditorKey.MiddleButton, true);
            mViewPortPanel.MiddleMouseUpEvent += (Control sender, int x, int y, ref bool _) => mPreview.OnKeyEvent((EditorKey)EditorKey.MiddleButton, false);
            mViewPortPanel.MouseMoveEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) => mPreview.OnMouseMoveEvent(MouseX - mViewPortPanel.GetScreenX(), MouseY - mViewPortPanel.GetScreenY());
            mViewPortPanel.MouseWheelEvent += (Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue) => mPreview.OnMouseWheelEvent(MouseDeltaZ);

            mViewPortPanel.PositionChangedEvent += (_, posChanged, sizeChanged) =>
            {
                if (sizeChanged)
                {
                    mPreview.OnResize(new Clicross.Float2(mViewPortPanel.GetWidth(), mViewPortPanel.GetHeight()));
                }
            };
            mViewPortPanel.PaintEvent += sender =>
            {
                var texture = mPreview.GetTexture();
                var width = Clicross.EditorUICanvasInterface.Instance().GetImageWidth(texture);
                var height = Clicross.EditorUICanvasInterface.Instance().GetImageHeight(texture);
                var color = new Color(1, 1, 1, 1);
                (GetDevice().GetEditorUICanvas() as EditorUICanvas).DrawImage(texture, mViewPortPanel.GetScreenX(), mViewPortPanel.GetScreenY(), width, height, ref color);
            };
            // Set TagName
            GetDockingCard().SetTagString1(mFilePath);

            // Handle DragDrop Event
            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
        }

        public new void Update(long TimeElapsed)
        {
            base.Update(TimeElapsed);

            var pre_inspected_object = mDetailView.GetObjectInspected();
            if (mContext.GetSelectedNodesCount() > 0)
            {
                var node = mContext.GetSelectedNode(mContext.GetSelectedNodesCount() - 1);
                if (pre_inspected_object != null && pre_inspected_object.GetType() == typeof(WorkflowNode))
                {
                    var pre_node = pre_inspected_object as WorkflowNode;
                    if (pre_node.GetLogicNodeId() == node.GetLogicNodeId())
                    {
                        //return;
                    }
                }

                mDetailView.Inspect(node);
            }
            else if (mSelectItemName != null)
            {
                if (mSelectMapType == WorkflowMapType.variable)
                {
                    if (pre_inspected_object != null && pre_inspected_object.GetType() == typeof(WorkflowVarDescWrapper))
                    {
                        var pre_var = pre_inspected_object as WorkflowVarDescWrapper;
                        if (pre_var.Name == mSelectItemName)
                        {
                            if (!mContext.m_NamedVariables.ContainsKey(mSelectItemName))
                            {
                                mSelectItemName = null;
                                mDetailView.Inspect(null);
                            }
                            return;

                        }
                    }
                    if (mContext.m_NamedVariables.ContainsKey(mSelectItemName))
                    {
                        var CurVar = mContext.m_NamedVariables[mSelectItemName];
                        mVarDescWrapper.FromNative(CurVar);
                    }
                    mDetailView.Inspect(null);
                    mDetailView.Inspect(mVarDescWrapper);
                }
                else if (mSelectMapType == WorkflowMapType.func)
                {
                    if (pre_inspected_object != null && pre_inspected_object.GetType() == typeof(WorkflowFunctionDescWrapper))
                    {
                        var pre_func = pre_inspected_object as WorkflowFunctionDescWrapper;
                        if (pre_func.Name == mSelectItemName)
                        {
                            if (!mContext.m_Functions.ContainsKey(mSelectItemName))
                            {
                                mSelectItemName = null;
                                mDetailView.Inspect(null);
                            }
                            return;
                        }
                    }
                    var CurFunction = mContext.m_Functions[mSelectItemName];
                    mFunctionDescWrapper.FromNative(CurFunction);
                    mDetailView.Inspect(null);
                    mDetailView.Inspect(mFunctionDescWrapper);
                }
                else if (mSelectMapType == WorkflowMapType.events)
                {
                    if (pre_inspected_object != null && pre_inspected_object.GetType() == typeof(WorkflowEventDescWrapper))
                    {
                        var pre_event = pre_inspected_object as WorkflowEventDescWrapper;
                        if (pre_event.Name == mSelectItemName)
                        {
                            if (!mContext.m_Events.ContainsKey(mSelectItemName))
                            {
                                mSelectItemName = null;
                                mDetailView.Inspect(null);
                            }
                            return;
                        }
                    }
                    var CurEvent = mContext.m_Events[mSelectItemName];
                    mEventDescWrapper.FromNative(CurEvent);
                    mDetailView.Inspect(null);
                    mDetailView.Inspect(mEventDescWrapper);
                }
                else if (mSelectMapType == WorkflowMapType.graph)
                {
                    if (pre_inspected_object != null && pre_inspected_object.GetType() == typeof(WorkflowGraphDesc))
                    {
                        var pre_graph = pre_inspected_object as WorkflowGraphDesc;
                        if (pre_graph.GetName() == mSelectItemName)
                        {
                            if (!mContext.m_Graphs.ContainsKey(mSelectItemName))
                            {
                                mSelectItemName = null;
                                mDetailView.Inspect(null);
                            }
                            return;
                        }
                    }
                    mDetailView.Inspect(mContext.m_Graphs[mSelectItemName]);
                }
                else
                {
                    mDetailView.Inspect(null);
                }
            }
            else
            {
                mDetailView.Inspect(null);
            }
            if (mViewPortPanel.GetVisible())
            {
                mPreview.Tick();
            }
        }

        public override void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            base.OnClose(Sender, ref bNotToClose);
            mContext.Dispose();
            mPreview.Dispose();
        }

        protected override void OnEnter()
        {
            base.OnEnter();
            mContext.OnActivate(true);
            //mPreview.OnActivate(true);
        }

        protected override void OnLeave()
        {
            base.OnLeave();
            mContext.OnActivate(false);
            //mPreview.OnActivate(false);
        }

        protected void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }

            mDetailView.OnDragDropManagerDragEnd(Sender, UIManager, MouseX, MouseY, ref bContinue);
        }
    }
}
