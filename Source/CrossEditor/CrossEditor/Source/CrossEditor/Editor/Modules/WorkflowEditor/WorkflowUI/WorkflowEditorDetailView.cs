using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{

    public class WorkflowEditorDetailView
    {
        protected ScrollView _ScrollView = new ScrollView();
        Panel _ScrollPanel;
        object _ObjectInspected;
        object _ObjectTag;
        Inspector _Inspector;
        InspectorHandler _InspectorHandler = new InspectorHandler();

        WorkflowEditorContext _WorkflowEditorContext;

        public WorkflowEditorDetailView(WorkflowEditorContext context)
        {
            _WorkflowEditorContext = context;

            _ScrollView.Initialize();
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ScrollView.GetHScroll().SetEnable(false);
            _ScrollView.PositionChangedEvent += OnPositionChanged;

            _ScrollPanel = _ScrollView.GetScrollPanel();
            _ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ScrollPanel.PaintEvent += (Sender) =>
            {
                SetChildInnerEnable(Sender);
                Sender.PaintThis();
                Sender.PaintChildren();
            };

            _InspectorHandler.InspectObject = DoInspectObject;
            _InspectorHandler.UpdateLayout = DoUpdateLayout;
            _InspectorHandler.ReadValue = DoReadValue;
        }

        public virtual void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
        }

        public void InspectObject()
        {
            _InspectorHandler.InspectObject();
            _InspectorHandler.UpdateLayout();
        }

        public object GetObjectInspected()
        {
            return _ObjectInspected;
        }

        private void SetChildInnerEnable(Control Control)
        {
            for (int i = 0; i < Control.GetChildCount(); ++i)
            {
                Control Child = Control.GetChild(i);
                if (UIManager.RectInRect(Child.GetScreenX(), Child.GetScreenY(), Child.GetWidth(), Child.GetHeight(),
                    _ScrollView.GetScreenX(), _ScrollView.GetScreenY(), _ScrollView.GetWidth(), _ScrollView.GetHeight()))
                {
                    Child.SetInnerEnable(true);

                    if (Child.GetChildCount() != 0)
                    {
                        SetChildInnerEnable(Child);
                    }
                }
                else
                {
                    Child.SetInnerEnable(false);
                }
            }
        }


        private void DoInspectObject()
        {
            _ScrollPanel.ClearChildren();
            if (_ObjectInspected != null)
            {
                if (_ObjectInspected is WorkflowNode)
                {
                    var NodeInspector = new Inspector_WorkflowNode();
                    _Inspector = NodeInspector;

                    var TempNode = _ObjectInspected as WorkflowNode;

                    NodeInspector.OnWorkflowFieldUpdate += (string key) =>
                    {
                        _WorkflowEditorContext.OnNodeEditorFieldsChange(TempNode, key);
                    };
                }
                else if (_ObjectInspected is WorkflowVarDescWrapper)
                {
                    var StructInspector = new Inspector_Struct_With_Property();
                    _Inspector = StructInspector;
                    var TempVar = _ObjectInspected as WorkflowVarDescWrapper;
                    string SelectVarName = TempVar.Name;
                    //ObjectProperty ObjectProperty = new ObjectProperty();
                    //ObjectProperty.Object = TempVar;
                    //ObjectProperty.Type = TempVar.GetType();
                    //StructInspector.InspectProperty(ObjectProperty);
                    StructInspector.SetPropertyModifiedFunction((object PropertyOwner, PropertyInfo Property) =>
                    {
                        if (_WorkflowEditorContext.m_NamedVariables.ContainsKey(SelectVarName))
                        {
                            var CurVar = _WorkflowEditorContext.m_NamedVariables[SelectVarName];
                            TempVar.ToNative(CurVar);
                            _WorkflowEditorContext.OnNameVariableChange(TempVar.Name, new string(""), VariableChangedType.ValueUpdate);
                        }
                    });

                }
                else if (_ObjectInspected is WorkflowFunctionDescWrapper)
                {
                    var StructInspector = new Inspector_Struct_With_Property();
                    _Inspector = StructInspector;
                    var TempFunc = _ObjectInspected as WorkflowFunctionDescWrapper;
                    //ObjectProperty ObjectProperty = new ObjectProperty();
                    //ObjectProperty.Object = TempFunc;
                    //ObjectProperty.Type = TempFunc.GetType();
                    //StructInspector.InspectProperty(ObjectProperty);
                    StructInspector.SetPropertyModifiedFunction((object PropertyOwner, PropertyInfo Property) =>
                    {
                        TempFunc.ToNative();
                        _WorkflowEditorContext.OnFunctionChange(TempFunc.Name, new string(""));
                    });

                }
                else if (_ObjectInspected is WorkflowGraphDesc)
                {
                    var StructInspector = new Inspector_Struct_With_Property();
                    _Inspector = StructInspector;
                    var TempGraph = _ObjectInspected as WorkflowGraphDesc;
                    //ObjectProperty ObjectProperty = new ObjectProperty();
                    //ObjectProperty.Object = TempGraph;
                    //ObjectProperty.Type = TempGraph.GetType();
                    //StructInspector.InspectProperty(ObjectProperty);
                    StructInspector.SetPropertyModifiedFunction((object PropertyOwner, PropertyInfo Property) =>
                    {
                        _WorkflowEditorContext.OnGraphChange(TempGraph.GetName(), new string(""));
                    });

                }
                else if (_ObjectInspected is WorkflowEventDescWrapper)
                {
                    var StructInspector = new Inspector_Struct_With_Property();
                    _Inspector = StructInspector;
                    var TempEvent = _ObjectInspected as WorkflowEventDescWrapper;
                    //ObjectProperty ObjectProperty = new ObjectProperty();
                    //ObjectProperty.Object = TempGraph;
                    //ObjectProperty.Type = TempGraph.GetType();
                    //StructInspector.InspectProperty(ObjectProperty);
                    StructInspector.SetPropertyModifiedFunction((object PropertyOwner, PropertyInfo Property) =>
                    {
                        TempEvent.ToNative();
                        _WorkflowEditorContext.OnEventChange(TempEvent.Name, new string(""));
                    });
                }

                if (_Inspector != null)
                {
                    _Inspector.SetContainer(_ScrollPanel);
                    _Inspector.SetInspectorHandler(_InspectorHandler);
                    _Inspector.InspectObject(_ObjectInspected, _ObjectTag);
                    _Inspector.UpdateCheckExpand();
                }
                UpdateLayout();
            }
        }

        private void DoUpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_Inspector != null && _ObjectInspected != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }

        protected void UpdateLayout()
        {
            _InspectorHandler.UpdateLayout();
        }

        private void DoReadValue()
        {
            if (_Inspector != null)
            {
                _Inspector.ReadValue();
            }
        }

        public void Inspect(object ObjectInspected, Object Tag = null)
        {
            if (_ObjectInspected == null && ObjectInspected == null)
            {
                return;
            }
            if (_ObjectInspected != null && _ObjectInspected.Equals(ObjectInspected))
            {
                return;
            }
            SetObjectInspected(ObjectInspected);
            SetObjectTag(Tag);
            InspectObject();
        }

        public void SetObjectInspected(object ObjectInspected)
        {
            _ObjectInspected = ObjectInspected;
            if (_Inspector != null)
            {
                _Inspector.WriteValue();
            }

            if (_ObjectInspected == null)
            {
                _Inspector = null;
            }
        }

        public void SetObjectTag(Object Tag)
        {
            _ObjectTag = Tag;
        }

        public void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            if (_Inspector != null)
            {
                List<Inspector> SearchResult = new List<Inspector>();
                _Inspector.ForEach((Inspector) =>
                {
                    if (Inspector == _Inspector)
                    {
                        return;
                    }
                    // Traverse all controls
                    Queue<Control> Controls = new Queue<Control>();
                    Controls.Enqueue(Inspector.GetSelfContainer());
                    while (Controls.Count > 0)
                    {
                        Control Control = Controls.Dequeue();
                        for (int i = 0; i < Control.GetChildCount(); ++i)
                        {
                            Controls.Enqueue(Control.GetChild(i));
                        }
                        // Try to match label text
                        if (Control is Label)
                        {
                            Label Label = Control as Label;
                            if (StringHelper.IgnoreCaseContains(Label.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                        // Try to match edit text
                        else if (Control is Edit)
                        {
                            Edit Edit = Control as Edit;
                            if (StringHelper.IgnoreCaseContains(Edit.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                    }
                });
                // Hide all inspectors
                _Inspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(false);
                });
                // Show search result
                foreach (Inspector Result in SearchResult)
                {
                    Inspector This = Result;
                    while (This != null)
                    {
                        This.SetVisible(true);
                        This = This.GetParentInspector();
                    }

                    Result.ForEach((Inspector) =>
                    {
                        Inspector.SetVisible(true);
                    });
                }
                if (SearchResult.Count == 0)
                {
                    _Inspector.SetVisible(true);
                }
                UpdateLayout();
            }
        }

        public void OnSearchUICancel(SearchUI Sender)
        {
            if (_Inspector != null)
            {
                _Inspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(true);
                });

                UpdateLayout();
            }
        }

        public void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_ScrollView.GetVisible_Recursively() && _ScrollView.IsPointIn_Recursively(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (ProjectUI.IsPathesDragging())
                {
                    List<string> PathesDragged = ProjectUI.GetPathesDragged();
                    DropPathOnInspector(_Inspector, MouseX, MouseY, PathesDragged);
                }
            }
        }

        bool DropPathOnInspector(Inspector Inspector, int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (Inspector == null)
            {
                return false;
            }
            List<Inspector> ChildInspectors = Inspector.GetChildInspectors();
            foreach (Inspector ChildInspector in ChildInspectors)
            {
                if (DropPathOnInspector(ChildInspector, MouseX, MouseY, PathesDragged))
                {
                    return true;
                }
            }
            return Inspector.OnDropPathes(MouseX, MouseY, PathesDragged);
        }

        public Control Control => _ScrollView;
    }
}
