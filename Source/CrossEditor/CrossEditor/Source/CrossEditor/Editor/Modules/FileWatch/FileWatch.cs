using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace CrossEditor
{
    public enum FileWatchType
    {
        WatchSourceFile = 0,
        WatchProjectFile = 1,
        WatchEngineFile = 2,
    }

    public enum FileChangeType
    {
        ChangeSourceFile = 0,
        ChangeNDAFile = 1,
        ChangeShaderMuldleFile = 2,
        ChangeShaderFile = 3,
        RenameNDAFile = 4,
        DeleteNDAFile = 5,
    }

    public class FileWatch
    {
        private delegate void ImportShaderAsync();

        static FileWatch gInstance = new FileWatch();

        Dictionary<FileWatchType, bool> mEnableMap;
        Dictionary<FileWatchType, FileSystemWatcher> mFileWatcherMap;
        Dictionary<FileChangeType, HashSet<string>> mChangedFileMap;
        int mHandleCount = 5;

        public static FileWatch GetInsance()
        {
            return gInstance;
        }

        public FileWatch()
        {
            mEnableMap = new Dictionary<FileWatchType, bool>();
            mChangedFileMap = new Dictionary<FileChangeType, HashSet<string>>();
            mFileWatcherMap = new Dictionary<FileWatchType, FileSystemWatcher>();

            InitChangeFileMap();
            AddProjectWatcher();
            AddEngineWatcher();

            SceneRuntime.GetInstance().EditorGlobalUpdateEvent += OnEditorGlobalUpdate;
        }

        public void InitChangeFileMap()
        {
            mChangedFileMap.Add(FileChangeType.ChangeSourceFile, new HashSet<string>());
            mChangedFileMap.Add(FileChangeType.ChangeNDAFile, new HashSet<string>());
            mChangedFileMap.Add(FileChangeType.ChangeShaderMuldleFile, new HashSet<string>());
            mChangedFileMap.Add(FileChangeType.ChangeShaderFile, new HashSet<string>());
            mChangedFileMap.Add(FileChangeType.RenameNDAFile, new HashSet<string>());
            mChangedFileMap.Add(FileChangeType.DeleteNDAFile, new HashSet<string>());
        }

        public void AddSrcFileWatcher(string path)
        {
            AddWatcher(FileWatchType.WatchSourceFile, path, OnSrcFileChanged, null, null);
        }

        public void AddProjectWatcher()
        {
            string path = MainUI.GetInstance().GetProjectDirectory();
            AddWatcher(FileWatchType.WatchProjectFile, path, OnFileChanged, OnFileDeleted, OnFileRenamed);
        }

        public void AddEngineWatcher()
        {
            string path = EditorUtilities.GetResourceDirectory();
            AddWatcher(FileWatchType.WatchEngineFile, path, OnFileChanged, OnFileDeleted, OnFileRenamed);
        }

        public void AddWatcher(FileWatchType tp, string path, FileSystemEventHandler changeHandler, FileSystemEventHandler deleteHandler, RenamedEventHandler renameHandler)
        {
            if (path == "")
                return;

            if (mFileWatcherMap.ContainsKey(tp))
            {
                mFileWatcherMap[tp].Path = path;
            }
            else
            {
                FileSystemWatcher fsw = new FileSystemWatcher(path);
                fsw.NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size | NotifyFilters.FileName;
                fsw.Changed += changeHandler;
                fsw.Deleted += deleteHandler;
                fsw.Renamed += renameHandler;
                fsw.IncludeSubdirectories = true;
                fsw.EnableRaisingEvents = false;
                mFileWatcherMap[tp] = fsw;
                mEnableMap[tp] = false;
            }
        }

        public void Enable(FileWatchType tp, bool enable)
        {
            if (mFileWatcherMap.ContainsKey(tp))
            {
                mFileWatcherMap[tp].EnableRaisingEvents = enable;
                mEnableMap[tp] = enable;
            }
        }

        public bool IsEnable(FileWatchType tp)
        {
            if (mFileWatcherMap.ContainsKey(tp))
            {
                return mFileWatcherMap[tp].EnableRaisingEvents;
            }
            return false;
        }

        public void Suspend()
        {
            foreach (var item in mFileWatcherMap)
            {
                item.Value.EnableRaisingEvents = false;
            }
        }

        public void Resume()
        {
            foreach (var item in mFileWatcherMap)
            {
                item.Value.EnableRaisingEvents = mEnableMap[item.Key];
            }
        }

        public string GetSrcWatchingPath()
        {
            if (mFileWatcherMap.ContainsKey(FileWatchType.WatchSourceFile))
            {
                return mFileWatcherMap[FileWatchType.WatchSourceFile].Path;
            }
            return "";
        }

        public void UpdateAllFiles(string dir)
        {
            if (DirectoryHelper.IsDirectoryExists(dir))
            {
                DirectoryInfo DirInfo = new DirectoryInfo(dir);
                FileInfo[] files = DirInfo.GetFiles();
                foreach (FileInfo f in files)
                {
                    string NDAPath = GetNDAFilePath(f.FullName);
                    if (AssetImporterManager.Instance().CheckUpdateAsset(f.FullName, NDAPath))
                    {
                        AssetImporterManager.Instance().UpdateAsset(f.FullName, NDAPath);
                    }
                }
                DirectoryInfo[] subDirs = DirInfo.GetDirectories();
                foreach (DirectoryInfo subDir in subDirs)
                {
                    UpdateAllFiles(subDir.FullName);
                }
            }
        }

        private void OnSrcFileChanged(object sender, FileSystemEventArgs e)
        {
            if (e.ChangeType != WatcherChangeTypes.Changed)
                return;
            var filepath = PathHelper.ToStandardForm(e.FullPath);
            AssetType assetType = AssetImporterManager.Instance().GetAssetType(filepath);
            if (assetType != AssetType.Unknown && assetType != AssetType.Default)
            {
                lock (mChangedFileMap)
                {
                    mChangedFileMap[FileChangeType.ChangeSourceFile].Add(filepath);
                }
            }
        }

        private void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            if (e.ChangeType != WatcherChangeTypes.Changed)
                return;
            var filepath = PathHelper.ToStandardForm(e.FullPath);
            if (filepath.Contains("Intermediate") || filepath.Contains("TempFolder"))
                return;
            string buildDir = ((BuildConfig)EditorConfigManager.GetInstance().GetConfig<BuildConfig>()).Staging;
            if (filepath.StartsWith(buildDir))
                return;
            lock (mChangedFileMap)
            {
                if (ResourceManager.Instance().IsNDAFile(filepath))
                {
                    mChangedFileMap[FileChangeType.ChangeNDAFile].Add(filepath);
                }
                //else if (filepath.EndsWith(".h") || filepath.EndsWith(".hlsl"))
                //{
                //    mChangedFileMap[FileChangeType.ChangeShaderMuldleFile].Add(filepath);
                //}
                //else if (filepath.EndsWith(".shader") || filepath.EndsWith(".compute"))
                //{
                //    mChangedFileMap[FileChangeType.ChangeShaderFile].Add(filepath);
                //}
            }
        }

        private void OnFileRenamed(object sender, RenamedEventArgs e)
        {
            var filepath = PathHelper.ToStandardForm(e.FullPath);
            if (filepath.Contains("Intermediate") || filepath.Contains("TempFolder"))
                return;

            string buildDir = ((BuildConfig)EditorConfigManager.GetInstance().GetConfig<BuildConfig>()).Staging;
            if (filepath.StartsWith(buildDir))
                return;

            lock (mChangedFileMap)
            {
                if (ResourceManager.Instance().IsNDAFile(filepath))
                {
                    mChangedFileMap[FileChangeType.RenameNDAFile].Add(filepath);
                }
            }
        }

        private void OnFileDeleted(object sender, FileSystemEventArgs e)
        {
            var filepath = PathHelper.ToStandardForm(e.FullPath);
            if (filepath.Contains("Intermediate") || filepath.Contains("TempFolder"))
                return;

            string buildDir = ((BuildConfig)EditorConfigManager.GetInstance().GetConfig<BuildConfig>()).Staging;
            if (filepath.StartsWith(buildDir))
                return;

            lock (mChangedFileMap)
            {
                if (ResourceManager.Instance().IsNDAFile(filepath))
                {
                    mChangedFileMap[FileChangeType.DeleteNDAFile].Add(filepath);
                }
            }
        }

        private string GetNDAFilePath(string filepath)
        {
            AssetType assetType = AssetImporterManager.Instance().GetAssetType(filepath);
            if (assetType != AssetType.Unknown && assetType != AssetType.Default)
            {
                string watchPath = GetSrcWatchingPath();
                string fileDir = PathHelper.GetDirectoryName(filepath);
                string revalPath = fileDir.Length == watchPath.Length ? "" : fileDir.Substring(watchPath.Length + 1);
                string filename = PathHelper.GetNameOfPath(filepath);
                if (assetType == AssetType.ComputeShader)
                    revalPath = PathHelper.CombinePath(revalPath, filename + ".compute.nda");
                else if (assetType == AssetType.Shader)
                    revalPath = PathHelper.CombinePath(revalPath, filename + ".shader.nda");
                else
                    revalPath = PathHelper.CombinePath(revalPath, filename + ".nda");
                string ret = EditorUtilities.StandardFilenameToEditorFilename(revalPath);
                return PathHelper.ToStandardForm(ret);
            }
            return "";
        }

        void OnEditorGlobalUpdate(Device Sender, long TimeElapsed)
        {
            lock (mChangedFileMap)
            {
                if (mChangedFileMap[FileChangeType.ChangeSourceFile].Count > 0)
                {
                    int handleCount = 0;
                    List<string> files = new List<string>();
                    foreach (var filepath in mChangedFileMap[FileChangeType.ChangeSourceFile])
                    {
                        files.Add(filepath);
                        string NDAPath = GetNDAFilePath(filepath);
                        if (AssetImporterManager.Instance().UpdateAsset(filepath, NDAPath))
                        {
                            ResourceManager.Instance().TryReloadResource(NDAPath);
                        }
                        mChangedFileMap[FileChangeType.ChangeSourceFile].Remove(filepath);
                        if (++handleCount >= mHandleCount)
                            break;
                    }
                    foreach (var item in files)
                    {
                        mChangedFileMap[FileChangeType.ChangeSourceFile].Remove(item);
                    }
                }

                if (mChangedFileMap[FileChangeType.ChangeNDAFile].Count > 0)
                {
                    int handleCount = 0;
                    vector_string files = new vector_string();
                    foreach (var filepath in mChangedFileMap[FileChangeType.ChangeNDAFile])
                    {
                        files.Add(filepath);
                        ResourceManager.Instance().TryReloadResource(filepath);

                        ResourceInspectorUI.ForEach((ObjectInspected, Instance) =>
                        {
                            if (ObjectInspected is Resource resource)
                            {
                                string relPath = EditorUtilities.EditorFilenameToStandardFilename(filepath);
                                if (resource.Path == relPath)
                                {
                                    Instance.InspectObject();
                                }
                            }
                        });

                        MaterialEditorUIManager.Instance.NotifyResourceChange(filepath);

                        if (++handleCount >= mHandleCount)
                            break;
                    }
                    ResourceManager.Instance().AddNewFiles(files, false);
                    foreach (var item in files)
                    {
                        mChangedFileMap[FileChangeType.ChangeNDAFile].Remove(item);
                    }
                }

                if (mChangedFileMap[FileChangeType.ChangeShaderMuldleFile].Count > 0)
                {
                    foreach (var filepath in mChangedFileMap[FileChangeType.ChangeShaderMuldleFile])
                    {
                        vector_string outShaders = new vector_string();
                        AssetImporterManager.Instance().CheckShaderModuleFile(filepath, outShaders, ShaderFileOutdateLevelE.ALL);
                        foreach (var shader in outShaders)
                        {
                            mChangedFileMap[FileChangeType.ChangeShaderFile].Remove(shader);
                        }
                    }
                    mChangedFileMap[FileChangeType.ChangeShaderMuldleFile].Clear();
                }

                if (mChangedFileMap[FileChangeType.ChangeShaderFile].Count > 0)
                {
                    List<string> allShaders = new List<string>();
                    foreach (var item in mChangedFileMap[FileChangeType.ChangeShaderFile])
                    {
                        allShaders.Add(item);
                    }

                    ImportShaderAsync ImportShaderAsync = async () =>
                    {
                        List<string> ShaderImportSuccessNames = new List<string>();
                        List<string> ShaderImportFailNames = new List<string>();

                        for (int i = 0; i < allShaders.Count; i++)
                        {
                            AssetType assetType = AssetImporterManager.Instance().GetAssetType(allShaders[i]);
                            if (assetType == AssetType.Shader || assetType == AssetType.ComputeShader)
                            {

                                Func<string, Task<AssetImportResult>> ImportShader = async (string ShaderPath) =>
                                {
                                    return await ProjectUI.GetInstance().DoAssetImport(PathHelper.GetDirectoryName(ShaderPath), ShaderPath);
                                };

                                AssetImportResult Result = await ImportShader(allShaders[i]);

                                if (Result.bSuccess)
                                {
                                    ShaderImportSuccessNames.Push(allShaders[i]);
                                }
                                else
                                {
                                    ShaderImportFailNames.Push(allShaders[i]);
                                }
                            }
                        }
                        StringBuilder LogString = new StringBuilder();
                        if (ShaderImportSuccessNames.Count > 0)
                            LogString.AppendLine(string.Format("\n  Shader Compile Success:{0}", ShaderImportSuccessNames.Count));

                        if (ShaderImportFailNames.Count > 0)
                        {
                            LogString.AppendLine(string.Format("\n  Shader Compile Fail:{0}", ShaderImportFailNames.Count));

                            foreach (string ShaderImportFailName in ShaderImportFailNames)
                                LogString.AppendLine("    " + ShaderImportFailName);

                            EditorLogger.Log(LogMessageType.Error, LogString.ToString());
                        }
                        else
                        {
                            EditorLogger.Log(LogMessageType.Information, LogString.ToString());
                        }
                    };

                    ImportShaderAsync();
                    mChangedFileMap[FileChangeType.ChangeShaderFile].Clear();
                }

                if (mChangedFileMap[FileChangeType.RenameNDAFile].Count > 0)
                {
                    foreach (var filePath in mChangedFileMap[FileChangeType.RenameNDAFile])
                    {
                        MaterialEditorUIManager.Instance.NotifyResourceChange(filePath);
                    }

                    mChangedFileMap[FileChangeType.RenameNDAFile].Clear();
                }

                if (mChangedFileMap[FileChangeType.DeleteNDAFile].Count > 0)
                {
                    foreach (var filePath in mChangedFileMap[FileChangeType.DeleteNDAFile])
                    {
                        MaterialEditorUIManager.Instance.NotifyResourceChange(filePath);
                    }

                    mChangedFileMap[FileChangeType.DeleteNDAFile].Clear();
                }
            }
        }
    }
}