using CEngine;

namespace CrossEditor
{
    class FlowNode_Unknown : FlowNode_StringContent
    {
        public FlowNode_Unknown()
        {
            Name = "Unknown";
            NodeType = NodeType.Unknown;
            AddOutSlot("Value", SlotType.DataFlow);
        }

        public override object Eval(int OutSlotIndex)
        {
            return false;
        }

        public override string GetStringContent()
        {
            return "false";
        }
        public override string ToExpression()
        {
            return "false";
        }
    }
}
