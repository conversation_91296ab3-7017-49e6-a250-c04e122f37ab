using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace CrossEditor
{
    public class CurveEditorUI : DockingUI
    {
        static readonly CurveEditorUI _Instance = new CurveEditorUI();
        Dictionary<string, Color> ColorCurveMap = new Dictionary<string, Color>{
                                                  { "R", Color.FromRGB(255, 0, 0) },
                                                  { "G", Color.FromRGB(0, 255, 0) },
                                                  { "B", Color.FromRGB(0, 0, 255) },
                                                  { "A", Color.FromRGB(255, 255, 255) }
                                                  };

        public static CurveEditorUI GetInstance()
        {
            return _Instance;
        }

        protected bool bModified;

        protected Panel _Panel;
        protected ListView _ListView;
        protected HSplitter _HSplitter;
        protected CurveEditToolBarUI _ToolBarUI;
        protected Panel _PanelBack;

        protected string Filename;
        protected Axis Axis;
        public GradientColor GradientColor;
        protected CurveGraphicsHelper CurveGraphicsHelper;

        protected List<CurveManager> Curves;
        protected List<CurveManager> SelectedCurveList;
        protected List<Point> SelectedPointList;
        protected List<ListViewItem> CurveListItems = new List<ListViewItem>();

        protected SelectRectangle SelectRectangle;
        protected PointMoveTool MoveTool;
        protected PointEditTool EditTool;

        IMovable HoldingObject = null;
        int LastMouseLocationX;
        int LastMouseLocationY;
        float CumulatedMovement;
        bool bLeftMouseDown;
        bool bRightMouseDown;
        protected bool bSeqOpen;

        public bool Initialize()
        {
            bModified = false;
            bSeqOpen = false;

            _PanelBack = new Panel();
            _PanelBack.Initialize();

            #region Initialize Panel

            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.SetCanFocus(true);

            _Panel.PaintEvent += OnPanelPaint;

            _Panel.LeftMouseDownEvent += OnPanelLeftMouseDown;
            _Panel.LeftMouseUpEvent += OnPanelLeftMouseUp;

            _Panel.RightMouseDownEvent += OnPanelRightMouseDown;
            _Panel.RightMouseUpEvent += OnPanelRightMouseUp;

            _Panel.MouseMoveEvent += OnPanelMouseMove;
            _Panel.MouseWheelEvent += OnPanelMouseWheel;

            #endregion Initialize Panel

            _ListView = new ListView();
            _ListView.Initialize();
            _ListView.SetWidth(50);
            _ListView.SetListViewStyle(ListViewStyle.List);
            _ListView.SetEnableRename(true);

            Texture NewTexture = UIManager.CreateUIImage(16, 16, Color.White.ToDword());


            _ListView.SelectionChangedEvent += OnListViewSelectionChanged;
            _ListView.RightMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) => { ShowCurveManageContextMenu(MouseX, MouseY); };
            _ListView.KeyDownEvent += OnListViewKeyDown;
            _ListView.ItemRenameEvent += OnListViewItemRename;

            _HSplitter = new HSplitter();
            _HSplitter.Initialize();
            _HSplitter.AddChild(_ListView);
            _HSplitter.AddChild(_Panel);

            _ToolBarUI = new CurveEditToolBarUI();
            _ToolBarUI.Initialize(UIManager.GetMainUIManager());
            _ToolBarUI.InputEnterEvent += OnToolBarUIInputEnter;
            _ToolBarUI.ZoomToFitClickEvent += OnZoomToFitButtonClick;
            _ToolBarUI.SaveClickEvent += (Sender) => { DoSave(); };
            _ToolBarUI.ConstantClickEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Constant); };
            _ToolBarUI.LinearClickEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Linear); };
            _ToolBarUI.SmoothClickEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Smooth); };
            _ToolBarUI.RepeatTypeMenuClickedEvent += (Sender, Type) => { ChangeRepeatType(Sender.bLeaveType, Type); };
            _ToolBarUI.GenerateClickEvent += ToolBarUITestGeneClickEvent;
            _ToolBarUI._ButtonTool_Adjust.SetVisible(false);
            _ToolBarUI._ButtonTool_Generate.SetVisible(false);


            _PanelBack.AddChild(_ToolBarUI.GetPanel());
            _PanelBack.AddChild(_HSplitter);

            Filename = "";

            Axis = new Axis();
            Axis.Initialize(-0.5m, -0.5m, 0.1m, 0.1m, 100m, 50m);

            CurveGraphicsHelper = new CurveGraphicsHelper();
            CurveGraphicsHelper.Initialize(Axis, _Panel);

            Curves = new List<CurveManager>();
            SelectedCurveList = new List<CurveManager>();

            var CurveManager = new CurveManager();
            CurveManager.Name = "Curve_0";
            Curves.Add(CurveManager);
            SelectedCurveList.Add(CurveManager);

            ListViewItem NewItem = new ListViewItem() { Name = "Curve_0", Image = NewTexture, bIsDirectory = false };
            _ListView.AddItem(NewItem);
            _ListView.AddSelection(NewItem);

            SelectedPointList = new List<Point>();
            CurveListItems = new List<ListViewItem>();

            SelectRectangle = null;
            MoveTool = new PointMoveTool();
            MoveTool.Initialize(SelectedPointList, _ToolBarUI);
            EditTool = new PointEditTool();
            EditTool.Initialize(SelectedPointList, SelectedCurveList);

            Initialize("Curve", _PanelBack);
            _DockingCard.KeyDownEvent += OnDockingCardKeyDown;

            return true;
        }


        protected virtual void ToolBarUITestGeneClickEvent(Button Sender)
        {

        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bSizeChanged)
            {
                int Width = Sender.GetWidth();
                int Height = Sender.GetHeight();

                Panel PanelBar = _ToolBarUI.GetOperationBar().GetPanelBar();
                PanelBar.SetWidth(Width);
                int BarHeight = PanelBar.GetHeight();
                _HSplitter.SetPosition(0, BarHeight, Width, Height - BarHeight);
            }
        }

        public void Clear()
        {
            Curves.Clear();
            SelectedCurveList.Clear();
            SelectedPointList.Clear();
        }

        public void LoadFromCurves(IEnumerable<CurveManager> InCurves)
        {
            Clear();
            bSeqOpen = true;
            Curves.AddRange(InCurves);
            PostDeserialize();
        }

        public bool GetModified()
        {
            return bModified;
        }

        public void SetModified(bool value, CurveManager inCurve = null)
        {
            bModified = value;
            UpdateDockingCardText();
        }
        public void SetModified(bool value, IEnumerable<CurveManager> InCurves)
        {
            bModified = value;
            UpdateDockingCardText();

            foreach (var curve in InCurves)
            {
                curve.PostModifiedCurve();
            }
        }

        public void DoSave()
        {
            if (bSeqOpen)
                CinematicUI.GetInstance().DoSave();

            if (!File.Exists(Filename))
            {
                if (bSeqOpen) SetModified(false);
                return;
            }

            XmlScript Xml = new XmlScript();
            Record RootRecord = Xml.GetRootRecord();
            foreach (var Cur in Curves)
            {
                Record RecordCurveManager = new Record();
                Cur.SaveToXml(RecordCurveManager);
                RootRecord.AddChild(RecordCurveManager);
            }

            if (Xml.Save(Filename))
            {
                SetModified(false);
            }
        }

        public void OpenCurFile(string Filename)
        {
            if (File.Exists(this.Filename) && bModified)
            {
                CommonDialogUI SaveDialogUI = new CommonDialogUI();
                SaveDialogUI.Initialize(GetUIManager(), "Save", "Do you want to save changes?", CommonDialogType.OKCancel);
                SaveDialogUI.CloseEvent += (Sender, Result) =>
                {
                    if (Result == CommonDialogResult.OK)
                    {
                        DoSave();
                    }
                    DoOpenFile(Filename);
                };
                DialogUIManager.GetInstance().ShowDialogUI(SaveDialogUI);
            }
            else
            {
                DoOpenFile(Filename);
            }
        }

        void DoOpenFile(string Filename)
        {
            this.Filename = Filename;

            if (File.Exists(Filename) == false)
            {
                return;
            }

            string Content = FileHelper.ReadTextFile(Filename);
            if (Content == "")
            {
                Clear();
                PostDeserialize();
                return;
            }

            XmlScript Xml = new XmlScript();
            Xml.Open(Filename);
            Record RootRecord = Xml.GetRootRecord();

            Clear();

            int Count = RootRecord.GetChildCount();
            Curves.Resize(Count);
            for (int i = 0; i < Count; ++i)
            {
                CurveManager NewCurveManager = new CurveManager();
                Record RecordCurveManager = RootRecord.GetChild(i);
                NewCurveManager.LoadFromXml(RecordCurveManager);
                Curves[i] = NewCurveManager;
            }

            bSeqOpen = false;
            PostDeserialize();
        }

        void PostDeserialize()
        {
            EditOperationManager.GetInstance().ClearAll();
            SetModified(false);
            Curves.ForEach(Cur => Cur.PostDeserialize());
            UpdateListView();

            // All curves are selected by default
            if (SelectedCurveList.Count == 0 &&
                Curves.Count != 0 &&
                CurveListItems.Count != 0)
            {
                SelectedCurveList.AddRange(Curves);
                CurveListItems.ForEach(Item => _ListView.AddSelection(Item));
            }
        }

        #region Panel Event

        void OnPanelPaint(Control Sender)
        {
            UIManager UIManager = GetUIManager();

            UpdateToolBar();
            Axis.Draw(CurveGraphicsHelper, UIManager);

            foreach (var Cur in SelectedCurveList) Cur.Draw(CurveGraphicsHelper, UIManager);
            if (SelectRectangle != null) SelectRectangle.Draw(UIManager);

            Color Color = Color.FromRGB(255, 255, 255);
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawRectangle(_Panel.GetScreenX(), _Panel.GetScreenY(), _Panel.GetWidth(), _Panel.GetHeight(), ref Color.EDITOR_UI_BUTTON_BORDER_COLOR);
        }

        public void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            ForceUpdateToolBar();

            if (bRightMouseDown) return;

            LastMouseLocationX = MouseX;
            LastMouseLocationY = MouseY;
            CumulatedMovement = 0f;

            bLeftMouseDown = true;
            Sender.CaptureMouse();

            HoldingObject = SelectOnCurve(MouseX, MouseY);

            if (HoldingObject == null)
            {
                SelectRectangle = new SelectRectangle();
                HoldingObject = SelectRectangle;
            }

            if (HoldingObject != null)
            {
                HoldingObject.MoveBegin(new Vector2f(MouseX, MouseY), CurveGraphicsHelper);
            }
            else
            {
                ClearSelect();
            }
        }

        public void OnPanelLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (bLeftMouseDown)
            {
                if (HoldingObject != null)
                {
                    if (HoldingObject == SelectRectangle)
                    {
                        SelectInRectangle(SelectRectangle.GetBoundRectM(), CurveGraphicsHelper);
                    }
                    HoldingObject.MoveEnd(new Vector2f(MouseX, MouseY), CurveGraphicsHelper);
                }

                bLeftMouseDown = false;
                HoldingObject = null;
                SelectRectangle = null;

                Sender.ReleaseMouse();
                bContinue = false;
            }
        }

        public void OnPanelRightMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (bLeftMouseDown) return;

            HoldingObject = Axis;

            LastMouseLocationX = MouseX;
            LastMouseLocationY = MouseY;
            CumulatedMovement = 0f;

            bRightMouseDown = true;
            Sender.CaptureMouse();

            if (HoldingObject != null)
                HoldingObject.MoveBegin(new Vector2f(MouseX, MouseY), CurveGraphicsHelper);
        }

        public void OnPanelRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (bRightMouseDown)
            {
                bRightMouseDown = false;
                HoldingObject = null;

                Sender.ReleaseMouse();
                bContinue = false;

                if (HoldingObject != null)
                    HoldingObject.MoveEnd(new Vector2f(MouseX, MouseY), CurveGraphicsHelper);
                if (CumulatedMovement < 2f)
                {
                    if (this is LinearColorCurveEditorUI)
                    {
                        ShowCurveEditContextMenu(MouseX, MouseY, false);
                    }
                    else
                    {
                        ShowCurveEditContextMenu(MouseX, MouseY);
                    }
                }
            }
        }

        public void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (bRightMouseDown || bLeftMouseDown)
            {
                int DeltaX = MouseX - LastMouseLocationX;
                int DeltaY = MouseY - LastMouseLocationY;
                CumulatedMovement += Math.Abs(DeltaX) + Math.Abs(DeltaY);

                if (HoldingObject != null)
                {
                    HoldingObject.Move(DeltaX, DeltaY, CurveGraphicsHelper);
                    SelectedCurveList.ForEach(Cur => Cur.Points.Sort());
                    if (HoldingObject is Axis && this is LinearColorCurveEditorUI)
                    {
                        GradientColor.Move(DeltaX, DeltaY, CurveGraphicsHelper);
                    }
                }

                LastMouseLocationX = MouseX;
                LastMouseLocationY = MouseY;
            }
            else
            {
                bool HoverOnOne = false;

                foreach (var Cur in Enumerable.Reverse(SelectedCurveList))
                {
                    if (!HoverOnOne && Cur.HitTestOnCurve(MouseX, MouseY, CurveGraphicsHelper))
                    {
                        Cur.bSelected = true;
                        HoverOnOne = true;
                    }
                    else Cur.bSelected = false;
                }
            }
        }

        public void OnPanelMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            float Scale = 1f + MouseDeltaZ * 0.1f;
            Scale = Math.Clamp(Scale, 0.1f, 10.0f);
            Axis.ScaleToPoint(new Vector2m(MouseX, MouseY), Scale, CurveGraphicsHelper);

            if (this is LinearColorCurveEditorUI)
            {
                GradientColor.Scale(CurveGraphicsHelper);
            }
        }

        #endregion Panel Event

        public void OnDockingCardKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone)
            {
                switch (Key)
                {
                    case Key.Enter:
                        AddKeyOnMousePosition(PointType.Smooth);
                        break;
                    case Key.Delete:
                        EditTool.Delete(this is LinearColorCurveEditorUI);
                        if (GradientColor.PanelContainCoord(Device.GetMouseX(), Device.GetMouseY()))
                            GradientColor.DeleteStop();
                        break;
                    case Key.F:
                        OnZoomToFitButtonClick(null);
                        break;
                }
            }
            else if (Device.IsControlDownOnly())
            {
                switch (Key)
                {
                    case Key.S:
                        DoSave();
                        break;
                    case Key.A:
                        SelectAll();
                        break;
                }
            }
        }

        public void OnToolBarUIInputEnter(CurveEditToolBarUI Sender, float InputX, float InputY)
        {
            MoveTool.AssignNewLocation(InputX, (decimal)InputY);
            SelectedCurveList.ForEach(Cur => Cur.Points.Sort());
        }

        public void OnZoomToFitButtonClick(Control Sender)
        {
            HashSet<CurveManager> SelectedPointCurveSet = CollectOwnerCurves(SelectedPointList);
            List<CurveManager> SelectedPointCurveList = new List<CurveManager>(SelectedPointCurveSet);

            if (SelectedPointCurveList.Count != 0)
            {
                DoZoomToFit(SelectedPointCurveList);
            }
            else
            {
                DoZoomToFit(SelectedCurveList);
            }
        }

        public void DoZoomToFit(List<CurveManager> CurvesToFit)
        {
            if (CurvesToFit.Count == 0)
            {
                Axis.Initialize(-0.5m, -0.5m, 0.1m, 0.1m, 100m, 50m);
                return;
            }

            decimal MinX = decimal.MaxValue;
            decimal MinY = decimal.MaxValue;
            decimal MaxX = decimal.MinValue;
            decimal MaxY = decimal.MinValue;
            decimal CurrentWidth = Axis.EndX - Axis.StartX;
            decimal CurrentHeight = Axis.EndY - Axis.StartY;

            foreach (CurveManager CurveManager in CurvesToFit)
            {
                foreach (Point Point in CurveManager.Points)
                {
                    MinX = Math.Min((decimal)Point.ValueX, MinX);
                    MinY = Math.Min((decimal)Point.ValueY, MinY);
                    MaxX = Math.Max((decimal)Point.ValueX, MaxX);
                    MaxY = Math.Max((decimal)Point.ValueY, MaxY);
                }

                for (int i = 1; i < CurveManager.BaseCurves.Count - 1; ++i)
                {
                    BaseCurve Curve = CurveManager.BaseCurves[i];

                    if (Curve is CubicBezierCurve)
                    {
                        (Curve as CubicBezierCurve).GetLimitValue(ref MinY, ref MaxY);
                    }
                }
            }

            if (MinX == decimal.MaxValue || MaxX == decimal.MinValue ||
                MinY == decimal.MaxValue || MaxY == decimal.MinValue)
            {
                Axis.Initialize(-CurrentWidth / 2, -CurrentHeight / 2, Axis.UnitX, Axis.UnitY, Axis.UnitWidth, Axis.UnitHeight);
                return;
            }

            decimal Width = (decimal)(MaxX - MinX);
            decimal Height = (decimal)(MaxY - MinY);

            decimal StartX = MinX - Width * 0.2m;
            if (Width == 0)
            {
                StartX = MinX - CurrentWidth / 2;
            }

            decimal StartY = MinY - Height * 0.2m;
            if (Height == 0)
            {
                StartY = MinY - CurrentHeight / 2;
            }

            Axis.Initialize(StartX, StartY, Axis.UnitX, Axis.UnitY, Axis.UnitWidth, Axis.UnitHeight);
            if (Width != 0)
                Axis.ScaleX((float)(CurrentWidth / (Width * 1.4m)), false);
            if (Height != 0)
                Axis.ScaleY((float)(CurrentHeight / (Height * 1.4m)), false);
        }

        #region ListView Event

        public void OnListViewSelectionChanged(ListView Sender)
        {
            SelectedPointList.Clear();
            SelectedCurveList.Clear();

            var Items = Sender.GetSelectedItems();
            if (Items.Count > 0)
            {
                foreach (var Item in Items)
                {
                    SelectedCurveList.Add(Curves[Item.Index]);
                }
            }

            OnZoomToFitButtonClick(null);
        }

        public void OnListViewKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && this is CurveEditorUI)
            {
                switch (Key)
                {
                    case Key.F2:
                        if (!bSeqOpen && _ListView.GetSelectedItems().Count > 0)
                        {
                            _ListView.StartRename(_ListView.GetSelectedItem(0));
                        }
                        break;
                    case Key.Delete:
                        if (!bSeqOpen)
                        {
                            DeleteSelectedCurves();
                        }
                        break;
                }
            }
        }

        public void OnListViewItemRename(ListView Sender, string NewName, string OldName, ListViewItem ListViewItem)
        {
            if (NewName == OldName) return;

            var RenamedCurve = Curves[ListViewItem.Index];
            ListViewItem.Name = NewName;
            RenamedCurve.Name = NewName;

            var EditOperation = new EditOperation_RenameCurve(RenamedCurve, OldName, NewName);
            EditOperationManager.GetInstance().AddOperation(EditOperation);

            UpdateListView();
            SetModified(true, RenamedCurve);
        }

        void DeleteSelectedCurves()
        {
            var Items = _ListView.GetSelectedItems();
            List<CurveManager> CurvesToDelete = new List<CurveManager>();
            SortedDictionary<int, CurveManager> RemovedCurves = new SortedDictionary<int, CurveManager>();
            foreach (var Item in Items)
            {
                CurvesToDelete.Add(Curves[Item.Index]);
                RemovedCurves.Add(Item.Index, Curves[Item.Index]);
            }
            foreach (var Cur in CurvesToDelete)
            {
                Curves.Remove(Cur);
                SelectedCurveList.Remove(Cur);
            }

            if (RemovedCurves.Count > 0)
            {
                var EditOperation = new EditOperation_RemoveCurves(RemovedCurves, Curves, SelectedCurveList);
                EditOperationManager.GetInstance().AddOperation(EditOperation);

                UpdateListView();
                SetModified(true, CurvesToDelete);
            }
        }

        #endregion ListView Event

        #region Select Function

        IMovable SelectOnCurve(int MouseX, int MouseY)
        {
            IMovable Result = null;

            List<Point> HitResultList = new List<Point>();
            foreach (var Cur in Enumerable.Reverse(SelectedCurveList))
            {
                HitResultList.AddRange(Cur.HitTestOnPoint(MouseX, MouseY, CurveGraphicsHelper));
            }

            if (HitResultList.Count() > 0)
            {
                if (GetDevice().IsControlDown())
                {
                    ChangeSelect(MouseX, MouseY, CurveGraphicsHelper, HitResultList);
                }
                else
                {
                    SelectOne(MouseX, MouseY, CurveGraphicsHelper, HitResultList);
                }

                Result = MoveTool;
            }
            else
            {
                bool bHitOnCurve = false;
                foreach (CurveManager Cur in Enumerable.Reverse(SelectedCurveList))
                {
                    bHitOnCurve |= Cur.HitTestOnCurve(MouseX, MouseY, CurveGraphicsHelper);
                    if (bHitOnCurve)
                    {
                        if (GetDevice().IsControlDown())
                        {
                            SelectedPointList.FindAll(ControlPoint.IsSameType).ForEach(Point.Unselect);
                            foreach (var Pt in Cur.Points)
                            {
                                Pt.bSelected = !Pt.bSelected;
                                if (Pt.bSelected) SelectedPointList.Add(Pt);
                                else SelectedPointList.Remove(Pt);
                            }
                        }
                        else
                        {
                            SelectedPointList.FindAll(ControlPoint.IsSameType).ForEach(Point.Unselect);

                            ClearSelect();
                            foreach (var Pt in Cur.Points) Pt.bSelected = true;
                            SelectedPointList.AddRange(Cur.Points);
                        }

                        Result = MoveTool;
                        break;
                    }
                }
            }

            return Result;
        }

        void SelectOne(float X, float Y, CurveGraphicsHelper CurveGraphicsHelper, List<Point> HitResultList)
        {
            List<Vector2f> DispLocList = new List<Vector2f>();
            HitResultList.ForEach(Pt => DispLocList.Add(CurveGraphicsHelper.WorldToScreen(Pt)));

            int NearestPointIndex = CurveMathHelper.FindNearestPointIndex(X, Y, DispLocList);

            if (NearestPointIndex == -1)
                return;

            Point NearestPoint = HitResultList[NearestPointIndex];

            if (NearestPoint.bSelected)
            {
                return;
            }
            else
            {
                NearestPoint.bSelected = true;

                ClearSelect();
                SelectedPointList.Add(NearestPoint);
            }
        }

        void SelectInRectangle(RectangleM Bound, CurveGraphicsHelper CurveGraphicsHelper)
        {
            ClearSelect();

            foreach (CurveManager Cur in SelectedCurveList)
            {
                foreach (Point Pt in Cur.Points)
                {
                    if (Bound.Contains(CurveGraphicsHelper.WorldToScreen(Pt)))
                    {
                        Pt.bSelected = true;
                        SelectedPointList.Add(Pt);
                    }
                }
            }

            if (SelectedPointList.Count() == 0)
            {
                foreach (var Cur in SelectedCurveList)
                {
                    foreach (var BaseCur in Cur.BaseCurves)
                    {
                        if (BaseCur.IntersectWithRect(Bound, CurveGraphicsHelper))
                        {
                            foreach (var Pt in Cur.Points) Pt.bSelected = true;
                            SelectedPointList.AddRange(Cur.Points);
                            break;
                        }
                    }
                }
            }
        }

        void ChangeSelect(float X, float Y, CurveGraphicsHelper CurveGraphicsHelper, List<Point> HitResultList)
        {
            List<Vector2f> DispLocList = new List<Vector2f>();
            HitResultList.ForEach(Pt => DispLocList.Add(CurveGraphicsHelper.WorldToScreen(Pt)));

            int NearestPointIndex = CurveMathHelper.FindNearestPointIndex(X, Y, DispLocList);
            if (NearestPointIndex == -1) return;
            Point NearestPoint = HitResultList[NearestPointIndex];

            if (NearestPoint.PointType == PointType.Control)
            {
                SelectOne(X, Y, CurveGraphicsHelper, HitResultList);
                return;
            }
            else
            {
                List<Point> CPList = SelectedPointList.FindAll(ControlPoint.IsSameType);
                foreach (var Pt in CPList)
                {
                    ControlPoint CP = Pt as ControlPoint;
                    CP.bSelected = false;
                    CP.Parent.bSelected = true;
                    SelectedPointList.Remove(CP);
                    SelectedPointList.Add(CP.Parent);
                }
            }

            if (NearestPoint.bSelected)
            {
                NearestPoint.bSelected = false;
                SelectedPointList.Remove(NearestPoint);
            }
            else
            {
                NearestPoint.bSelected = true;
                SelectedPointList.Add(NearestPoint);
            }
        }

        void SelectAll()
        {
            ClearSelect();
            foreach (CurveManager Cur in SelectedCurveList)
            {
                foreach (Point Pt in Cur.Points) Pt.bSelected = true;
                SelectedPointList.AddRange(Cur.Points);
            }
        }

        void ClearSelect()
        {
            foreach (Point Pt in SelectedPointList) Pt.bSelected = false;
            SelectedPointList.Clear();
        }

        #endregion Select Function

        #region Edit Operation

        void AddKeyOnMousePosition(PointType Type)
        {
            Device Device = GetDevice();
            AddKeyOnScreenPostion(Device.GetMouseX(), Device.GetMouseY(), Type);
        }

        void AddKeyOnScreenPostion(int ScreenX, int ScreenY, PointType Type)
        {
            Vector2f Pos = new Vector2f(ScreenX, ScreenY);
            if (CurveGraphicsHelper.GetBound().Contains(Pos))
            {
                Vector2m Value = CurveGraphicsHelper.ScreenToWorld(Pos);

                if (_ToolBarUI.EnableSnappingX)
                {
                    Value.X = CurveGraphicsHelper.GetNearestX((decimal)Value.X, (decimal)_ToolBarUI.SnappingIntervalX);
                }
                if (_ToolBarUI.EnableSnappingY)
                {
                    Value.Y = CurveGraphicsHelper.GetNearestY(Value.Y, (decimal)_ToolBarUI.SnappingIntervalY);
                }

                EditTool.Add(Value.X, Value.Y, Type, CurveGraphicsHelper);
            }

            SetModified(true);
        }

        void Flatten()
        {
            const decimal FlattenTangent = 0;

            var SmoothPointList = SelectedPointList.FindAll((Pt) => SmoothPoint.IsSameType(Pt));
            List<SmoothPoint> ModifiedParent = new List<SmoothPoint>();
            List<ControlPoint> CPtList = new List<ControlPoint>();
            HashSet<CurveManager> ModifiedCurves = new HashSet<CurveManager>();
            if (SmoothPointList.Count > 0)
            {
                foreach (SmoothPoint Pt in SmoothPointList)
                {
                    if (Pt.Mode == KeySmoothMode.KSM_AUTO)
                    {
                        ModifiedParent.Add(Pt);
                        Pt.Mode = KeySmoothMode.KSM_FLAT;
                    }
                    CPtList.Add(Pt.ControlPointLeft);
                    CPtList.Add(Pt.ControlPointRight);
                    ModifiedCurves.Add(Pt.OwnerCurve);
                }
            }
            else if (SelectedPointList.Exists(ControlPoint.IsSameType))
            {
                foreach (ControlPoint CPt in SelectedPointList)
                {
                    SmoothPoint Parent = CPt.Parent as SmoothPoint;
                    if (Parent.Mode == KeySmoothMode.KSM_AUTO)
                    {
                        ModifiedParent.Add(Parent);
                        Parent.Mode = KeySmoothMode.KSM_FLAT;
                    }
                    CPtList.Add(Parent.ControlPointLeft);
                    CPtList.Add(Parent.ControlPointRight);
                }
            }

            List<ControlPoint> ModifiedPoints = new List<ControlPoint>();
            List<ControlAttributes> OldAttrs = new List<ControlAttributes>();
            List<ControlAttributes> NewAttrs = new List<ControlAttributes>();
            foreach (var CPt in CPtList)
            {
                if (CPt.Tangent != FlattenTangent)
                {
                    ModifiedPoints.Add(CPt);
                    OldAttrs.Add(new ControlAttributes(CPt.Weight, CPt.Tangent));
                    NewAttrs.Add(new ControlAttributes(CPt.Weight, FlattenTangent));
                    CPt.Tangent = FlattenTangent;
                }
            }

            if (ModifiedPoints.Count > 0 || ModifiedParent.Count > 0)
            {
                EditOperation NewOperation;
                if (ModifiedParent.Count > 0)
                {
                    List<KeySmoothMode> OldModes = new List<KeySmoothMode>(Enumerable.Repeat(KeySmoothMode.KSM_AUTO, ModifiedParent.Count));
                    List<KeySmoothMode> NewModes = new List<KeySmoothMode>(Enumerable.Repeat(KeySmoothMode.KSM_FLAT, ModifiedParent.Count));
                    var ChangeModeOperation = new EditOperation_ChangeSmoothMode(ModifiedParent, OldModes, NewModes);
                    NewOperation = new EditOperation_MoveControlPoints(ModifiedPoints, OldAttrs, NewAttrs, ChangeModeOperation);
                }
                else
                {
                    NewOperation = new EditOperation_MoveControlPoints(ModifiedPoints, OldAttrs, NewAttrs);
                }
                EditOperationManager.GetInstance().AddOperation(NewOperation);
            }

            SetModified(true, ModifiedCurves);
        }

        public void ChangeRepeatType(bool bLeaveType, CurveRepeatType Type)
        {
            HashSet<CurveManager> SelectedPointCurveSet = CollectOwnerCurves(SelectedPointList);

            List<CurveManager> SelectedPointCurveList = new List<CurveManager>(SelectedPointCurveSet);
            List<CurveRepeatType> OldTypes = new List<CurveRepeatType>();
            List<CurveRepeatType> NewTypes = new List<CurveRepeatType>();
            if (bLeaveType)
            {
                foreach (var Cur in SelectedPointCurveList)
                {
                    OldTypes.Add(Cur.LeaveRepeatType);
                    NewTypes.Add(Type);
                    Cur.LeaveRepeatType = Type;
                }
            }
            else
            {
                foreach (var Cur in SelectedPointCurveList)
                {
                    OldTypes.Add(Cur.EnterRepeatType);
                    NewTypes.Add(Type);
                    Cur.EnterRepeatType = Type;
                }
            }

            if (SelectedPointCurveList.Count > 0)
            {
                var EditOperation = new EditOperation_ChangeRepeatType(SelectedPointCurveList, OldTypes, NewTypes, bLeaveType);
                EditOperationManager.GetInstance().AddOperation(EditOperation);

                SetModified(true, SelectedPointCurveSet);
            }
        }

        #endregion Edit Operation

        #region Context Menu

        public void ShowCurveEditContextMenu(int MouseX, int MouseY, bool BHideAdd = true)
        {
            var _ContextMenu = new Menu(GetUIManager());
            _ContextMenu.Initialize();

            MenuItem MenuItem_AddKey = new MenuItem();
            MenuItem_AddKey.SetText("Add Key");
            MenuItem_AddKey.ClickedEvent += (Sender) => { AddKeyOnScreenPostion(LastMouseLocationX, LastMouseLocationY, PointType.Smooth); };
            MenuItem_AddKey.SetEnable(BHideAdd);

            bool IsSelectedPoint = SelectedPointList.Count > 0;

            MenuItem MenuItem_DeleteKey = new MenuItem();
            MenuItem_DeleteKey.SetText("Delete Key");
            MenuItem_DeleteKey.ClickedEvent += (Sender) => EditTool.Delete();
            MenuItem_DeleteKey.SetEnable(IsSelectedPoint);

            bool HasSmoothPoint = SelectedPointList.Exists(SmoothPoint.IsSameType);
            bool HasControlPoint = SelectedPointList.Exists(ControlPoint.IsSameType);

            MenuItem MenuItem_Flatten = new MenuItem();
            MenuItem_Flatten.SetText("Flatten");
            MenuItem_Flatten.ClickedEvent += (Sender) => { Flatten(); };
            MenuItem_Flatten.SetEnable(IsSelectedPoint && (HasSmoothPoint || HasControlPoint));

            MenuItem MenuItem_PointTypeConstant = new MenuItem();
            MenuItem_PointTypeConstant.SetText("Constant");
            MenuItem_PointTypeConstant.ClickedEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Constant); };
            MenuItem_PointTypeConstant.SetEnable(IsSelectedPoint);

            MenuItem MenuItem_PointTypeLinear = new MenuItem();
            MenuItem_PointTypeLinear.SetText("Linear");
            MenuItem_PointTypeLinear.ClickedEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Linear); };
            MenuItem_PointTypeLinear.SetEnable(IsSelectedPoint);

            MenuItem MenuItem_PointTypeSmooth = new MenuItem();
            MenuItem_PointTypeSmooth.SetText("Smooth");
            MenuItem_PointTypeSmooth.ClickedEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Smooth); };
            MenuItem_PointTypeSmooth.SetEnable(IsSelectedPoint);

            bool IsSameSmoothMode = true;
            bool IsSameAutoWeighted = true;

            KeySmoothMode LastMode = KeySmoothMode.KSM_AUTO;
            bool LastAutoWeighted = false;

            if (!HasSmoothPoint)
            {
                HasSmoothPoint = false;
                IsSameSmoothMode = false;
                IsSameAutoWeighted = false;
            }
            else
            {
                var SmoothPointList = SelectedPointList.FindAll((Pt) => SmoothPoint.IsSameType(Pt));
                LastMode = (SmoothPointList.First() as SmoothPoint).Mode;
                LastAutoWeighted = (SmoothPointList.First() as SmoothPoint).AutoWeighted;
                foreach (SmoothPoint Pt in SmoothPointList)
                {
                    if (Pt.Mode != LastMode)
                    {
                        IsSameSmoothMode = false;
                        break;
                    }
                }
                foreach (SmoothPoint Pt in SmoothPointList)
                {
                    if (Pt.AutoWeighted != LastAutoWeighted)
                    {
                        IsSameAutoWeighted = false;
                        break;
                    }
                }
            }

            Texture CheckedImage = UIManager.LoadUIImage("Editor/Icons/Special/CheckedMark.png");

            MenuItem MenuItem_SmoothModeAuto = new MenuItem();
            MenuItem_SmoothModeAuto.SetText("Auto");
            MenuItem_SmoothModeAuto.ClickedEvent += (Sender) => { EditTool.ChangeSmoothMode(KeySmoothMode.KSM_AUTO); };
            MenuItem_SmoothModeAuto.SetEnable(IsSelectedPoint && HasSmoothPoint);
            if (IsSameSmoothMode && LastMode == KeySmoothMode.KSM_AUTO)
                MenuItem_SmoothModeAuto.SetImage(CheckedImage);

            MenuItem MenuItem_SmoothModeFlat = new MenuItem();
            MenuItem_SmoothModeFlat.SetText("Flat");
            MenuItem_SmoothModeFlat.ClickedEvent += (Sender) => { EditTool.ChangeSmoothMode(KeySmoothMode.KSM_FLAT); };
            MenuItem_SmoothModeFlat.SetEnable(IsSelectedPoint && HasSmoothPoint);
            if (IsSameSmoothMode && LastMode == KeySmoothMode.KSM_FLAT)
                MenuItem_SmoothModeFlat.SetImage(CheckedImage);

            MenuItem MenuItem_SmoothModeBreak = new MenuItem();
            MenuItem_SmoothModeBreak.SetText("Break");
            MenuItem_SmoothModeBreak.ClickedEvent += (Sender) => { EditTool.ChangeSmoothMode(KeySmoothMode.KSM_BREAK); };
            MenuItem_SmoothModeBreak.SetEnable(IsSelectedPoint && HasSmoothPoint);
            if (IsSameSmoothMode && LastMode == KeySmoothMode.KSM_BREAK)
                MenuItem_SmoothModeBreak.SetImage(CheckedImage);

            MenuItem MenuItem_AutoWeighted = new MenuItem();
            MenuItem_AutoWeighted.SetText("Auto Weighted");
            MenuItem_AutoWeighted.ClickedEvent += (Sender) => { EditTool.SetAutoWeighted(!(IsSameAutoWeighted && LastAutoWeighted)); };
            MenuItem_AutoWeighted.SetEnable(IsSelectedPoint && HasSmoothPoint);
            if (IsSameAutoWeighted && LastAutoWeighted)
                MenuItem_AutoWeighted.SetImage(CheckedImage);

            _ContextMenu.AddMenuItem(MenuItem_AddKey);
            _ContextMenu.AddMenuItem(MenuItem_DeleteKey);
            _ContextMenu.AddMenuItem(MenuItem_Flatten);
            _ContextMenu.AddSeperator();
            _ContextMenu.AddMenuItem(MenuItem_PointTypeConstant);
            _ContextMenu.AddMenuItem(MenuItem_PointTypeLinear);
            _ContextMenu.AddMenuItem(MenuItem_PointTypeSmooth);
            _ContextMenu.AddSeperator();
            _ContextMenu.AddMenuItem(MenuItem_SmoothModeAuto);
            _ContextMenu.AddMenuItem(MenuItem_SmoothModeFlat);
            _ContextMenu.AddMenuItem(MenuItem_SmoothModeBreak);
            _ContextMenu.AddSeperator();
            _ContextMenu.AddMenuItem(MenuItem_AutoWeighted);

            GetUIManager().GetContextMenu().ShowMenu(_ContextMenu, MouseX, MouseY);
        }

        public void ShowCurveManageContextMenu(int MouseX, int MouseY)
        {
            var _ContextMenu = new Menu(GetUIManager());
            _ContextMenu.Initialize();
            int SelectedCount = _ListView.GetSelectedItems().Count;

            bool IsSelectedItem = false;
            if (bSeqOpen)
            {
                IsSelectedItem = false;
            }
            else
            {
                IsSelectedItem = _ListView.GetSelectedItems().Count > 0;
            }
            MenuItem MenuItem_NewCurve = new MenuItem();
            MenuItem_NewCurve.SetText("New");
            MenuItem_NewCurve.SetEnable(!bSeqOpen);
            MenuItem_NewCurve.ClickedEvent += (Sender) =>
            {
                CurveManager NewCurve = new CurveManager();
                NewCurve.Name = "NewCurve_" + Curves.Count.ToString();
                Curves.Add(NewCurve);
                SelectedCurveList.Add(NewCurve);

                var EditOperation = new EditOperation_AddNewCurve(NewCurve, Curves, SelectedCurveList);
                EditOperationManager.GetInstance().AddOperation(EditOperation);

                UpdateListView();
                SetModified(true, NewCurve);
            };

            MenuItem MenuItem_DeleteCurve = new MenuItem();
            MenuItem_DeleteCurve.SetText("Delete");
            MenuItem_DeleteCurve.SetEnable(IsSelectedItem);
            MenuItem_DeleteCurve.ClickedEvent += (Sender) =>
            {
                DeleteSelectedCurves();
            };

            MenuItem MenuItem_SetColor = new MenuItem();
            MenuItem_SetColor.SetText("Set Color");
            MenuItem_SetColor.SetEnable(SelectedCount == 1);
            MenuItem_SetColor.ClickedEvent += (Sender) =>
            {
                ColorSelectUI ColorSelectUI = new ColorSelectUI();
                ColorSelectUI.Initialize(GetUIManager(), "Color", SelectedCurveList[0].UnselectedColor);
                ColorSelectUI.ColorSelectedEvent += (Sender1) =>
                {
                    Color NewColor = Sender1.GetNewColor();
                    bool bColorModified = Sender1.GetColorModified();
                    if (bColorModified)
                    {
                        CurveManager ModifiedCurve = SelectedCurveList[0];
                        Color OldColor = SelectedCurveList[0].UnselectedColor;
                        ModifiedCurve.UnselectedColor = NewColor;

                        var EditOperation = new EditOperation_ChangeCurveColor(ModifiedCurve, OldColor, NewColor);
                        EditOperationManager.GetInstance().AddOperation(EditOperation);

                        UpdateListView();
                        SetModified(true, ModifiedCurve);
                    }
                };
                DialogUIManager.GetInstance().ShowDialogUI(ColorSelectUI);
            };

            MenuItem MenuItem_Rename = new MenuItem();
            MenuItem_Rename.SetText("Rename");
            MenuItem_Rename.SetEnable(!bSeqOpen && SelectedCount == 1);
            MenuItem_Rename.ClickedEvent += (Sender) =>
            {
                var ItemToRename = _ListView.GetSelectedItem(0);
                _ListView.StartRename(ItemToRename);
            };

            _ContextMenu.AddMenuItem(MenuItem_NewCurve);
            _ContextMenu.AddMenuItem(MenuItem_DeleteCurve);
            _ContextMenu.AddSeperator();
            _ContextMenu.AddMenuItem(MenuItem_SetColor);
            _ContextMenu.AddMenuItem(MenuItem_Rename);

            GetUIManager().GetContextMenu().ShowMenu(_ContextMenu, MouseX, MouseY);
        }

        #endregion Context Menu

        #region Update

        public void UpdateToolBar()
        {
            if (!_ToolBarUI.GetModified())
            {
                ForceUpdateToolBar();
            }

            HashSet<CurveManager> SelectedPointCurves = CollectOwnerCurves(SelectedPointList);
            _ToolBarUI.Update(SelectedPointCurves);
        }

        void ForceUpdateToolBar()
        {
            if (SelectedPointList.Count() == 0)
            {
                _ToolBarUI.ClearValue();
            }
            else if (SelectedPointList.Count() == 1)
            {
                _ToolBarUI.SetValue(SelectedPointList.First().ValueX, (float)SelectedPointList.First().ValueY);
            }
            else
            {
                _ToolBarUI.SetString("Multi Value", "Multi Value");
            }
        }

        public void UpdateListView()
        {
            _ListView.ClearItems();
            CurveListItems.Clear();
            foreach (var Cur in Curves)
            {
                var NewItem = GetCurveItem(Cur);
                _ListView.AddItem(NewItem);
                CurveListItems.Add(NewItem);
                if (SelectedCurveList.Contains(Cur))
                {
                    _ListView.AddSelection(NewItem);
                }
            }
            _ListView.UpdateUI();
        }

        void UpdateDockingCardText()
        {
            _DockingCard.SetText(GetModified() ? "Curve*" : "Curve");
        }

        #endregion Update

        ListViewItem GetCurveItem(CurveManager Curve)
        {
            Texture NewTexture = UIManager.CreateUIImage(16, 16, Curve.UnselectedColor.ToDword());
            return new ListViewItem() { Name = Curve.Name, Image = NewTexture, bIsDirectory = false };
        }

        static public HashSet<CurveManager> CollectOwnerCurves(IEnumerable<Point> inContainer)
        {
            HashSet<CurveManager> ret = new HashSet<CurveManager>();
            foreach (var pt in inContainer)
            {
                if (pt.OwnerCurve != null)
                {
                    ret.Add(pt.OwnerCurve);
                }
            }
            return ret;
        }

        public bool IsHoldingRotateCurve()
        {
            if (bSeqOpen && Curves[0].Name == "t")
            {
                return true;
            }
            return false;
        }
    }
}
