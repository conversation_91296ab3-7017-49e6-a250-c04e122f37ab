using CEngine;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class PointMoveTool : BaseEditTool
    {
        private List<SmoothPoint> ModifiedSmoothPointList = new List<SmoothPoint>();
        private List<Vector2m> BeginValues = new List<Vector2m>();
        private List<ControlAttributes> BeginAttrs = new List<ControlAttributes>();
        private float CumulatedX = 0f;
        private decimal CumulatedY = 0;
        private CurveEditToolBarUI ToolBar;

        public PointMoveTool() : base() { }

        public PointMoveTool(List<Point> SelectedPointList) : base(SelectedPointList) { }

        public void Initialize(List<Point> SelectedPointList, CurveEditToolBarUI ToolBar)
        {
            base.Initialize(SelectedPointList);
            this.ToolBar = ToolBar;
        }

        public void AssignNewLocation(float NewValueX, decimal NewValueY)
        {
            List<Point> ModifiedPoint = new List<Point>();
            List<Vector2m> OldLocs = new List<Vector2m>();
            List<Vector2m> NewLocs = new List<Vector2m>();

            foreach (var Pt in SelectedPointList)
            {
                if (Pt.ValueX != NewValueX || Pt.ValueY != NewValueY)
                {
                    ModifiedPoint.Add(Pt);
                    OldLocs.Add(Pt);
                    NewLocs.Add(new Vector2m((decimal)NewValueX, NewValueY));
                    Pt.ValueX = NewValueX;
                    Pt.ValueY = NewValueY;
                }
            }

            if (ModifiedPoint.Count() > 0)
            {
                LastOperation = new EditOperation_MovePoints(ModifiedPoint, OldLocs, NewLocs);
                EditOperationManager.GetInstance().AddOperation(LastOperation);

                CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPoint));
            }
        }

        #region IMovable Interface Implement

        public override void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            if ((ToolBar.EnableSnappingX || ToolBar.EnableSnappingY) && !SelectedPointList.Exists(ControlPoint.IsSameType))
            {
                CumulatedX += (float)DeltaX;
                CumulatedY += DeltaY;
                for (int i = 0; i < SelectedPointList.Count(); ++i)
                {
                    if (!(SelectedPointList[i] is ControlPoint) && CurveEditorUI.GetInstance().IsHoldingRotateCurve())
                    {
                        continue;
                    }
                    Vector2m BeginLoc = CurveGraphicsHelper.WorldToScreen(BeginValues[i]);
                    BeginLoc.X += (decimal)CumulatedX;
                    BeginLoc.Y += CumulatedY;
                    BeginLoc = CurveGraphicsHelper.ScreenToWorld(BeginLoc);

                    Vector2m NearestGridPoint = new Vector2m(0,0);
                    NearestGridPoint.X = ToolBar.EnableSnappingX ? CurveGraphicsHelper.GetNearestX(BeginLoc.X, (decimal)ToolBar.SnappingIntervalX) : BeginLoc.X;
                    NearestGridPoint.Y = ToolBar.EnableSnappingY ? CurveGraphicsHelper.GetNearestY(BeginLoc.Y, (decimal)ToolBar.SnappingIntervalY) : BeginLoc.Y;

                    if (SelectedPointList[i].OwnerCurve.CheckIsValidX((float)NearestGridPoint.X, SelectedPointList[i]))
                    {
                        SelectedPointList[i].ValueX = (float)NearestGridPoint.X;
                    }

                    SelectedPointList[i].ValueY = NearestGridPoint.Y;
                    SelectedPointList[i].OnMove(new MoveEvnetArgs());
                }
            }
            else
            {
                foreach (Point Pt in SelectedPointList)
                {
                    if (!(Pt is ControlPoint) && CurveEditorUI.GetInstance().IsHoldingRotateCurve())
                    {
                        continue;
                    }

                    Pt.Move(DeltaX, DeltaY, CurveGraphicsHelper);

                    if (ControlPoint.IsSameType(Pt))
                    {
                        ControlPoint CPt = Pt as ControlPoint;
                        SmoothPoint Parent = CPt.Parent as SmoothPoint;

                        var P0 = CPt == Parent.ControlPointLeft ? Parent.ControlPointLeft : Parent.ControlPointRight;
                        var P1 = CPt == Parent.ControlPointLeft ? Parent.ControlPointRight : Parent.ControlPointLeft;

                        switch (Parent.Mode)
                        {
                            case KeySmoothMode.KSM_AUTO:
                                ModifiedSmoothPointList.Add(Parent);
                                Parent.Mode = KeySmoothMode.KSM_FLAT;
                                P1.Tangent = P0.Tangent;
                                P1.Weight = P0.Weight;
                                break;
                            case KeySmoothMode.KSM_FLAT:
                                P1.Tangent = P0.Tangent;
                                P1.Weight = P0.Weight;
                                break;
                        }
                    }
                }
            }

            var CurvePointMap = new Dictionary<CurveManager, List<Point>>();
            CurvePointMap[new CurveManager()] = SelectedPointList;
            LinearColorCurveEditorUI.GetInstance().GradientColor.DeleteStopFromPoints(CurvePointMap);
        }

        public override void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper CurveGraphicsHelper) { }

        public override void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
            ModifiedSmoothPointList.Clear();
            CumulatedX = 0;
            CumulatedY = 0;
            if (!SelectedPointList.Exists(ControlPoint.IsSameType))
            {
                BeginValues.Clear();
                foreach (Point Pt in SelectedPointList)
                {
                    BeginValues.Add(Pt);
                }
            }
            else
            {
                BeginAttrs.Clear();
                foreach (Point Pt in SelectedPointList)
                {
                    var CPt = Pt as ControlPoint;
                    BeginAttrs.Add(new ControlAttributes(CPt.Weight, CPt.Tangent));
                }
            }

            foreach (var Pt in SelectedPointList) Pt.MoveBegin(StartPoint, CurveGraphicsHelper);

            LastOperation = null;
        }

        public override void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
            if (!SelectedPointList.Exists(ControlPoint.IsSameType))
            {
                List<Point> ModifiedPoint = new List<Point>();
                List<Vector2m> OldLocs = new List<Vector2m>();
                List<Vector2m> NewLocs = new List<Vector2m>();

                for (int i = 0; i < SelectedPointList.Count(); ++i)
                {
                    if (BeginValues[i] != SelectedPointList[i].ToVector2m())
                    {
                        ModifiedPoint.Add(SelectedPointList[i]);
                        OldLocs.Add(BeginValues[i]);
                        NewLocs.Add(SelectedPointList[i]);
                    }
                }

                if (ModifiedPoint.Count() > 0)
                {
                    LastOperation = new EditOperation_MovePoints(ModifiedPoint, OldLocs, NewLocs);
                    EditOperationManager.GetInstance().AddOperation(LastOperation);

                    CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPoint));
                }
            }
            else
            {
                List<ControlPoint> ModifiedPoints = new List<ControlPoint>();
                List<ControlAttributes> OldAttrs = new List<ControlAttributes>();
                List<ControlAttributes> NewAttrs = new List<ControlAttributes>();

                for (int i = 0; i < SelectedPointList.Count(); ++i)
                {
                    ControlPoint CPt = SelectedPointList[i] as ControlPoint;
                    if (CPt.Weight != BeginAttrs[i].Weight || CPt.Tangent != BeginAttrs[i].Tangent)
                    {
                        ModifiedPoints.Add(CPt);
                        OldAttrs.Add(BeginAttrs[i]);
                        NewAttrs.Add(new ControlAttributes(CPt.Weight, CPt.Tangent));

                        SmoothPoint Parent = CPt.Parent as SmoothPoint;
                        if (Parent.Mode == KeySmoothMode.KSM_FLAT)
                        {
                            var OtherCPt = CPt == Parent.ControlPointLeft ? Parent.ControlPointRight : Parent.ControlPointLeft;

                            ModifiedPoints.Add(OtherCPt);
                            OldAttrs.Add(BeginAttrs[i]);
                            NewAttrs.Add(new ControlAttributes(CPt.Weight, CPt.Tangent));
                        }
                    }
                }

                if (ModifiedPoints.Count() > 0 || ModifiedSmoothPointList.Count > 0)
                {
                    if (ModifiedSmoothPointList.Count > 0)
                    {
                        List<KeySmoothMode> OldModes = new List<KeySmoothMode>(Enumerable.Repeat(KeySmoothMode.KSM_AUTO, ModifiedSmoothPointList.Count));
                        List<KeySmoothMode> NewModes = new List<KeySmoothMode>(Enumerable.Repeat(KeySmoothMode.KSM_FLAT, ModifiedSmoothPointList.Count));
                        var ChangeModeOperation = new EditOperation_ChangeSmoothMode(ModifiedSmoothPointList, OldModes, NewModes);
                        LastOperation = new EditOperation_MoveControlPoints(ModifiedPoints, OldAttrs, NewAttrs, ChangeModeOperation);
                        CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedSmoothPointList));
                    }
                    else
                    {
                        LastOperation = new EditOperation_MoveControlPoints(ModifiedPoints, OldAttrs, NewAttrs);
                        CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPoints));
                    }
                    EditOperationManager.GetInstance().AddOperation(LastOperation);
                }
            }
        }

        #endregion IMovable Interface Implement

        public override void Draw(CurveGraphicsHelper CurveGraphicsHelper) { }

        public override IMovable HitTest(float X, float Y, CurveGraphicsHelper CurveGraphicsHelper) { return null; }

        public override IMovable Select(IMovable Object) { return null; }
    }
}
