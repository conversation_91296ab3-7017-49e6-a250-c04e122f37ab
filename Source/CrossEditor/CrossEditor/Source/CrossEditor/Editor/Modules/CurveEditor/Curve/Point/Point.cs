using EditorUI;
using System;
using System.Xml.Serialization;

namespace CrossEditor
{
    public enum PointType { Linear, Constant, Smooth, Control };

    [XmlInclude(typeof(ConstantPoint))]
    [XmlInclude(typeof(ControlPoint))]
    [XmlInclude(typeof(LinearPoint))]
    [XmlInclude(typeof(SmoothPoint))]
    [Serializable()]
    public abstract class Point : IComparable<Point>, IMovable
    {
        public static implicit operator Vector2f(Point Pt) { return new Vector2f((Pt.ValueX), (float)(Pt.ValueY)); }
        public static bool IsSameType(Point Pt) { return false; }

        public static void Unselect(Point Pt) { Pt.bSelected = false; }

        public virtual event MoveHandler MoveEvent;

        public PointType PointType;
        public float ValueX;
        public decimal ValueY;
        public float Size;

        public  Vector2m ToVector2m()
        {
            return new Vector2m(Convert.ToDecimal(ValueX), Convert.ToDecimal(ValueY));
        }

        [XmlIgnore(), NonSerialized()]
        public CurveManager OwnerCurve;

        [XmlIgnore(), NonSerialized()]
        public Color UnSelectedColor = Color.White;
        [XmlIgnore(), NonSerialized()]
        public Color SelectedColor = Color.FromRGB(0, 0, 255);

        [XmlIgnore(), NonSerialized()]
        public bool bSelected = false;

        public Point() { }

        public virtual void Initialize(float ValueX, decimal ValueY, float Size, CurveManager OwnerCurve)
        {
            this.ValueX = ValueX;
            this.ValueY = ValueY;
            this.Size = Size;
            this.OwnerCurve = OwnerCurve; 
        }

        public virtual void PostDeserialize() { }

        public virtual void Update()
        {
            //Vector2f ScreenPoint = Axis.WorldToScreen(new Vector2f(ValueX, ValueY));
            //DispX = ScreenPoint.X;
            //DispY = ScreenPoint.Y;
        }

        abstract public void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager);

        public virtual Point HitTest(decimal X, decimal Y, CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2m DispPoint = CurveGraphicsHelper.WorldToScreen(new Vector2m(Convert.ToDecimal(ValueX), Convert.ToDecimal(ValueY)));
            RectangleM Bound = new RectangleM
            {
                X = DispPoint.X - 2 * (decimal)Size,
                Y = DispPoint.Y - 2 * (decimal)Size,
                Width = 4 * (decimal)Size,
                Height = 4 * (decimal)Size
            };
            return Bound.Contains(new Vector2m((decimal)X, (decimal)Y)) ? this : null;
        }

        public virtual int CompareTo(Point OtherPoint)
        {
            int XCompare = ValueX.CompareTo(OtherPoint.ValueX);
            return XCompare == 0 ? ValueY.CompareTo(OtherPoint.ValueY) : XCompare;
        }

        public virtual void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2m DispPoint = CurveGraphicsHelper.WorldToScreen(new Vector2m(Convert.ToDecimal(ValueX), Convert.ToDecimal(ValueY)));
            DispPoint.X += (decimal)DeltaX;
            DispPoint.Y += (decimal)DeltaY;
            Vector2m ValuePoint = CurveGraphicsHelper.ScreenToWorld(DispPoint);
            if (OwnerCurve.CheckIsValidX((float)ValuePoint.X, this))
            {
                ValueX = (float)ValuePoint.X;
            }
            else
            {
                EditorLogger.Log(LogMessageType.Error, "Point: Move: invalid key x position: " + ValuePoint.X);
            }

            ValueY = ValuePoint.Y;
            OnMove(new MoveEvnetArgs() { });
        }

        public virtual void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2m NewStart = CurveGraphicsHelper.ScreenToWorld(new Vector2m((decimal)DispX, (decimal)DispY));
            ValueX = (float)NewStart.X;
            ValueY = NewStart.Y;
        }

        public virtual void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper CurveGraphicsHelper) { }

        public virtual void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper CurveGraphicsHelper) { }

        public virtual void OnMove(MoveEvnetArgs Args)
        {
            MoveEvent?.Invoke(this, new MoveEvnetArgs() { });
        }

        public virtual void SaveToXml(Record RecordPoint)
        {
            RecordPoint.SetTypeString(GetType().Name);
            RecordPoint.SetString("PointType", Enum.GetName(typeof(PointType), PointType));
            RecordPoint.SetFloat("ValueX", ValueX);
            RecordPoint.SetDecimal("ValueY", ValueY);
            RecordPoint.SetFloat("Size", Size);
        }

        public virtual void LoadFromXml(Record RecordPoint)
        {
            PointType = (PointType)Enum.Parse(typeof(PointType), RecordPoint.GetString("PointType"));
            ValueX = RecordPoint.GetFloat("ValueX");
            ValueY = RecordPoint.GetDecimal("ValueY");
            Size = RecordPoint.GetFloat("Size");
        }
    }
}
