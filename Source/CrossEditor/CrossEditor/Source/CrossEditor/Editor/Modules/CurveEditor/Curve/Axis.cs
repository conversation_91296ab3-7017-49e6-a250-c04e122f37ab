using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class Axis : IMovable
    {
        public static readonly int[] ScaleBaseList = { 1, 2, 5 };
        public static readonly decimal[] ScaleFactorList = { 2m, 2.5m, 2m };

        public decimal StartX; // Start value of axis X
        public decimal StartY; // Start value of axis Y

        public decimal UnitX; // Unit in axis X
        public decimal UnitY; // Unit in axis Y
        public decimal UnitWidth; // Distance between each unit in axis X
        public decimal UnitHeight; // Distance between each unit in axis Y

        public decimal UnitXMin;
        public decimal UnitXMax;
        public decimal UnitYMin;
        public decimal UnitYMax;

        public decimal BoundaryCoefficient;

        public int ScaleBaseX;
        public int ScaleBaseY;

        public decimal EndX;
        public decimal EndY;

        public bool bDrawGrid;

        private List<decimal> AxisValueListX;
        private List<decimal> AxisValueListY;
        public event MoveHandler MoveEvent;

        public Axis()
        {
            StartX = 0;
            StartY = 0;

            UnitX = 0;
            UnitY = 0;
            UnitWidth = 0;
            UnitHeight = 0;

            UnitXMin = 1e-1m;
            UnitXMax = 1e6m;
            UnitYMin = 1e-6m;
            UnitYMax = 1e6m;

            BoundaryCoefficient = 1e3m;

            ScaleBaseX = 0;
            ScaleBaseY = 0;

            bDrawGrid = true;

            AxisValueListX = new List<decimal>();
            AxisValueListY = new List<decimal>();
        }

        public void Initialize(decimal StartX, decimal StartY, decimal UnitX, decimal UnitY, decimal UnitWidth, decimal UnitHeight)
        {
            this.StartX = StartX;
            this.StartY = StartY;
            this.UnitX = UnitX;
            this.UnitY = UnitY;
            this.UnitWidth = UnitWidth;
            this.UnitHeight = UnitHeight;
        }

        public void UpdateEndValue(CurveGraphicsHelper CurveGraphicsHelper)
        {
            int ScreenWidth = CurveGraphicsHelper.Width;
            int ScreenHeight = CurveGraphicsHelper.Height;

            EndX = ScreenWidth / UnitWidth * UnitX + StartX;
            EndY = ScreenHeight / UnitHeight * UnitY + StartY;
        }

        public void Scale(float Percent)
        {
            ScaleX(Percent);
            ScaleY(Percent);
        }

        public void ScaleX(float Percent, bool Manul = true)
        {
            try
            {
                UnitWidth = Manul ? Math.Clamp(UnitWidth * (decimal)Percent, 30m, decimal.MaxValue) : UnitWidth * (decimal)Percent;

                while (UnitWidth < 60)
                {
                    decimal NewUnitX = UnitX * ScaleFactorList[ScaleBaseX];
                    if (NewUnitX > UnitXMax)
                    {
                        break;
                    }
                    UnitX = NewUnitX;
                    UnitWidth *= ScaleFactorList[ScaleBaseX];
                    ScaleBaseX = (ScaleBaseX + 1) % ScaleFactorList.Length;
                }
                while (UnitWidth > 120)
                {
                    int NewScaleBaseX = (ScaleBaseX + ScaleFactorList.Length - 1) % ScaleFactorList.Length;
                    decimal NewUnitX = UnitX / ScaleFactorList[NewScaleBaseX];
                    if (NewUnitX < UnitXMin)
                    {
                        break;
                    }
                    UnitX = NewUnitX;
                    UnitWidth /= ScaleFactorList[NewScaleBaseX];
                    ScaleBaseX = NewScaleBaseX;
                }
            }
            catch (OverflowException e)
            {
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Error, e.Message);
            }
        }

        public void ScaleY(float Percent, bool Manul = true)
        {
            try
            {
                UnitHeight = Manul ? Math.Clamp(UnitHeight * (decimal)Percent, 20m, decimal.MaxValue) : UnitHeight * (decimal)Percent;

                while (UnitHeight < 40)
                {
                    decimal NewUnitY = UnitY * ScaleFactorList[ScaleBaseY];
                    if (NewUnitY > UnitYMax)
                    {
                        break;
                    }
                    UnitY = NewUnitY;
                    UnitHeight *= ScaleFactorList[ScaleBaseY];
                    ScaleBaseY = (ScaleBaseY + 1) % ScaleFactorList.Length;
                }
                while (UnitHeight > 80)
                {
                    int NewScaleBaseY = (ScaleBaseY + ScaleFactorList.Length - 1) % ScaleFactorList.Length;
                    decimal NewUnitY = UnitY / ScaleFactorList[NewScaleBaseY];
                    if (NewUnitY < UnitYMin)
                    {
                        break;
                    }
                    UnitY = NewUnitY;
                    UnitHeight /= ScaleFactorList[NewScaleBaseY];
                    ScaleBaseY = NewScaleBaseY;
                }
            }
            catch (OverflowException e)
            {
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Error, e.Message);
            }
        }

        public void ScaleToPoint(Vector2m Disp, float Percent, CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2m Value = CurveGraphicsHelper.ScreenToWorld(Disp);

            Scale(Percent);

            Vector2m NewDisp = CurveGraphicsHelper.WorldToScreen(Value);
            Move(Disp.X - NewDisp.X, Disp.Y - NewDisp.Y, CurveGraphicsHelper);
        }

        public void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager)
        {
            AxisValueListX.Clear();
            AxisValueListY.Clear();

            UpdateEndValue(CurveGraphicsHelper);

            decimal PointX, PointY;
            if (StartX % UnitX == 0)
            {
                PointX = StartX;
            }
            else
            {
                AxisValueListX.Add(StartX);
                PointX = CurveMathHelper.FindNextInUnit(StartX, UnitX);
            }

            if (StartY % UnitY == 0)
            {
                PointY = StartY;
            }
            else
            {
                AxisValueListY.Add(StartY);
                PointY = CurveMathHelper.FindNextInUnit(StartY, UnitY);
            }

            while (PointX < EndX)
            {
                AxisValueListX.Add(PointX);
                PointX += UnitX;
            }

            while (PointY < EndY)
            {
                AxisValueListY.Add(PointY);
                PointY += UnitY;
            }

            if (bDrawGrid)
            {
                Color Color = Color.FromRGB(128, 128, 128);
                int Size = 1;
                Vector2m StartPoint;
                Vector2m EndPoint;
                foreach (decimal AxisValueX in AxisValueListX)
                {
                    StartPoint = new Vector2m(AxisValueX, StartY);
                    EndPoint = new Vector2m(AxisValueX, EndY);
                    CurveGraphicsHelper.DrawSegment(UIManager, Color, Size, StartPoint, EndPoint);
                }
                foreach (decimal AxisValueY in AxisValueListY)
                {
                    StartPoint = new Vector2m(StartX, AxisValueY);
                    EndPoint = new Vector2m(EndX, AxisValueY);
                    CurveGraphicsHelper.DrawSegment(UIManager, Color, Size, StartPoint, EndPoint);
                }
            }

            Font AxisStringFont = UIManager.GetDefaultFont(14);

            var AxisValueToDrawX = AxisValueListX.Clone();
            AxisValueToDrawX.RemoveAt(0);
            foreach (decimal AxisValueX in AxisValueToDrawX)
            {
                Vector2f ScreenPointAxisX = CurveGraphicsHelper.WorldToScreen(new Vector2m(AxisValueX, StartY));
                ScreenPointAxisX.Y = CurveGraphicsHelper.Y;
                CurveGraphicsHelper.DrawDecimal(UIManager, AxisValueX, AxisStringFont, Color.White, ScreenPointAxisX);
            }
            var AxisValueToDrawY = AxisValueListY.Clone();
            AxisValueToDrawY.RemoveAt(0);
            foreach (decimal AxisValueY in AxisValueToDrawY)
            {
                Vector2f ScreenPointAxisY = CurveGraphicsHelper.WorldToScreen(new Vector2m(StartX, AxisValueY));
                CurveGraphicsHelper.DrawDecimal(UIManager, AxisValueY, AxisStringFont, Color.White, ScreenPointAxisY);
            }
        }

        public void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            try
            {
                UpdateEndValue(CurveGraphicsHelper);

                decimal DistanceX = EndX - StartX;
                decimal DistanceY = EndY - StartY;

                StartX -= (decimal)DeltaX * UnitX / UnitWidth;
                StartY += (decimal)DeltaY * UnitY / UnitHeight;

                if (StartX < -UnitXMax * BoundaryCoefficient)
                {
                    StartX = -UnitXMax * BoundaryCoefficient;
                }
                if (StartX > UnitXMax * BoundaryCoefficient - DistanceX)
                {
                    StartX = UnitXMax * BoundaryCoefficient - DistanceX;
                }
                if (StartY < -UnitYMax * BoundaryCoefficient)
                {
                    StartY = -UnitYMax * BoundaryCoefficient;
                }
                if (StartY > UnitYMax * BoundaryCoefficient - DistanceY)
                {
                    StartY = UnitYMax * BoundaryCoefficient - DistanceY;
                }

                MoveEvent?.Invoke(this, new MoveEvnetArgs() { });
            }
            catch (OverflowException e)
            {
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Error, e.Message);
            }
        }

        public void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2m NewStart = CurveGraphicsHelper.ScreenToWorld(new Vector2m(DispX, DispY));
            StartX = (decimal)NewStart.X;
            StartY = (decimal)NewStart.Y;
        }

        public void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper CurveGraphicsHelper) { }

        public void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper CurveGraphicsHelper) { }

        public float GetAxisValue(int GradientX, CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2f Pos = new Vector2f(GradientX, 0);
            Vector2f Value = CurveGraphicsHelper.ScreenToWorld(Pos);

            //Console.WriteLine("Time : {0}", Value.X);
            return Value.X;
        }
    }
}
