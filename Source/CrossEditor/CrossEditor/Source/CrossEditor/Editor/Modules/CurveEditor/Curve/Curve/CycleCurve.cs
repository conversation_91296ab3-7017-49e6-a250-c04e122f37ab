using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class CycleCurve : BaseCurve
    {
        public List<BaseCurve> Curves;
        public bool bIsForward;

        public CycleCurve()
        {
            Curves = new List<BaseCurve>();
            bIsForward = true;
        }

        public void Initialize(IEnumerable<BaseCurve> Curves, bool IsForward)
        {
            this.Curves.AddRange(Curves);
            this.bIsForward = IsForward;
        }

        public override decimal ComputeValue(float X)
        {
            if (Curves.Count() > 0)
            {
                decimal StartX = Curves.First().StartPoint.X;
                decimal EndX = Curves.Last().EndPoint.X;
                decimal DeltaX = EndX - StartX;
                decimal XD = Convert.ToDecimal(X);
                XD -= StartX;
                XD %= DeltaX;
                XD += StartX;
                foreach (var Cur in Curves)
                {
                    if (Cur.GetIsInRange(XD))
                        return Cur.ComputeValue(X);
                }
            }

            return 0;
        }

        public override bool GetIsInRange(decimal X)
        {
            if (bIsForward)
            {
                return X >= Curves.Last().EndPoint.X;
            }
            else
            {
                return X <= Curves.First().StartPoint.X;
            }
        }

        public override List<(Vector2m, Vector2m)> GetSegments(CurveGraphicsHelper CurveGraphicsHelper)
        {
            var AllSegments = new List<(Vector2m, Vector2m)>();

            Vector2m First, Second;
            decimal PixelDensity;
            foreach (var Cur in Curves)
            {
                var CurSegments = Cur.GetSegments(CurveGraphicsHelper);

                First = CurveGraphicsHelper.WorldToScreen(CurSegments.First().Item1);
                Second = CurveGraphicsHelper.WorldToScreen(CurSegments.Last().Item2);
                PixelDensity = (Second.X - First.X) / CurSegments.Count;

                if (PixelDensity < 1 && CurSegments.Count == 3)
                {
                    AllSegments.Add((
                        new Vector2m(CurSegments[0].Item1.X, CurSegments[0].Item2.Y),
                        new Vector2m(CurSegments[2].Item2.X, CurSegments[2].Item1.Y)
                        ));
                }
                else
                {
                    AllSegments.AddRange(CurSegments);
                }
            }

            var Segments = new List<(Vector2m, Vector2m)>();
            if (AllSegments.Count() > 0 &&
                (bIsForward && (decimal)AllSegments.Last().Item2.X < CurveGraphicsHelper.Axis.EndX ||
                !bIsForward && (decimal)AllSegments.First().Item1.X > CurveGraphicsHelper.Axis.StartX))
            {
                decimal T = (decimal)AllSegments.Last().Item2.X - (decimal)AllSegments.First().Item1.X;
                if (T == 0)
                {
                    var HalfLine = new HalfLineCurve();
                    if (bIsForward)
                    {
                        HalfLine.Initialize(AllSegments.Last().Item2, 0);
                    }
                    else
                    {
                        HalfLine.Initialize(AllSegments.First().Item1, 180);
                    }
                    return HalfLine.GetSegments(CurveGraphicsHelper);
                }

                decimal AxisWidth, CurCount, PixelWidth;
                if (bIsForward)
                {
                    AxisWidth = CurveGraphicsHelper.Axis.EndX - (decimal)AllSegments.Last().Item2.X;
                    CurCount = AxisWidth / T;
                    PixelWidth = CurveGraphicsHelper.WorldToScreen(new Vector2m(CurveGraphicsHelper.Axis.EndX, 0)).X -
                        CurveGraphicsHelper.WorldToScreen(new Vector2m((decimal)AllSegments.Last().Item2.X, 0)).X;
                }
                else
                {
                    AxisWidth = (decimal)AllSegments.First().Item1.X - CurveGraphicsHelper.Axis.StartX;
                    CurCount = AxisWidth / T;
                    PixelWidth = CurveGraphicsHelper.WorldToScreen(new Vector2m((decimal)AllSegments.First().Item1.X, 0)).X -
                        CurveGraphicsHelper.WorldToScreen(new Vector2m(CurveGraphicsHelper.Axis.StartX, 0)).X;
                }
                PixelDensity = PixelWidth / CurCount;

                if (PixelDensity > 1)
                {
                    decimal Offset = (decimal)T;
                    bool bNeedLastToFirst = Math.Abs(AllSegments.Last().Item2.Y - AllSegments.First().Item1.Y) > (decimal)CurveMathHelper.FloatPresicion;
                    (Vector2m, Vector2m) LastToFirstSeg = (AllSegments.Last().Item2, AllSegments.First().Item1);
                    if (bNeedLastToFirst)
                    {
                        if (bIsForward)
                        {
                            LastToFirstSeg.Item2 += new Vector2m(Offset, 0);
                        }
                        else
                        {
                            LastToFirstSeg.Item1 -= new Vector2m(Offset, 0);
                        }
                    }

                    int Count = Convert.ToInt32(Math.Ceiling(CurCount));

                    if (bNeedLastToFirst && bIsForward)
                    {
                        Segments.Add(LastToFirstSeg);
                    }

                    for (int i = 1; i <= Count; ++i)
                    {
                        Vector2m Deviation = bIsForward ? new Vector2m(Offset * i, 0) : new Vector2m(-Offset * (Count - i + 1), 0);

                        if (bNeedLastToFirst && bIsForward)
                        {
                            Segments.Add((LastToFirstSeg.Item1 + Deviation, LastToFirstSeg.Item2 + Deviation));
                        }

                        foreach (var Seg in AllSegments)
                        {
                            Segments.Add((Seg.Item1 + Deviation, Seg.Item2 + Deviation));
                        }

                        if (bNeedLastToFirst && !bIsForward)
                        {
                            Segments.Add((LastToFirstSeg.Item1 + Deviation, LastToFirstSeg.Item2 + Deviation));
                        }
                    }

                    if (bNeedLastToFirst && !bIsForward)
                    {
                        Segments.Add(LastToFirstSeg);
                    }
                }
                else
                {
                    decimal MinY = decimal.MaxValue, MaxY = decimal.MinValue;
                    foreach (var Seg in AllSegments)
                    {
                        MaxY = Math.Max(Seg.Item1.Y, Math.Max(Seg.Item2.Y, MaxY));
                        MinY = Math.Min(Seg.Item1.Y, Math.Min(Seg.Item2.Y, MinY));
                    }

                    decimal MiddleX = (AllSegments.First().Item1.X + AllSegments.Last().Item2.X) / 2;
                    decimal Offset = (decimal)(AxisWidth / PixelWidth);
                    (Vector2m, Vector2m) MergedSeg = (new Vector2m(MiddleX, MinY), new Vector2m(MiddleX, MaxY));

                    int Count = Convert.ToInt32(Math.Ceiling(PixelWidth));
                    for (int i = 0; i <= Count; ++i)
                    {
                        Vector2m Deviation = bIsForward ? new Vector2m(Offset * i, 0) : new Vector2m(-Offset * (Count - i + 1), 0);

                        Segments.Add((MergedSeg.Item1 + Deviation, MergedSeg.Item2 + Deviation));
                    }
                }
            }

            return Segments;
        }
    }
}
