using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public abstract class BaseCurve
    {
        public Vector2m StartPoint;
        public Vector2m EndPoint;

        public float Size;
        public Color Color;

        public BaseCurve()
        {
            Size = 2f;
            Color = Color.White;
        }

        public virtual void Initialize(Vector2m StartPoint, Vector2m EndPoint)
        {
            this.StartPoint = new Vector2m((decimal)StartPoint.X, (decimal)StartPoint.Y);
            this.EndPoint = new Vector2m((decimal)EndPoint.X, (decimal)EndPoint.Y);
        }

        abstract public decimal ComputeValue(float X);

        abstract public List<(Vector2m, Vector2m)> GetSegments(CurveGraphicsHelper CurveGraphicsHelper);

        public virtual void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager)
        {
            var Segments = GetSegments(CurveGraphicsHelper);

            List<Vector2m> SegmentsPoints = CurveGraphicsHelper.GeneratePointsBySegments(Segments);
            if (SegmentsPoints.Count == 0)
                return;

            Vector2m[] Points = SegmentsPoints.ToArray();
            CurveGraphicsHelper.DrawSegments(UIManager, Points, Points.Length, Size, Color);
        }

        public virtual object HitTest(decimal X, decimal Y, CurveGraphicsHelper CurveGraphicsHelper)
        {
            var Segments = GetSegments(CurveGraphicsHelper);
            var ClipedSegs = new List<(Vector2m, Vector2m)>();
            foreach (var Seg in Segments)
            {
                Vector2m P0 = CurveGraphicsHelper.WorldToScreen(Seg.Item1);
                Vector2m P1 = CurveGraphicsHelper.WorldToScreen(Seg.Item2);
                if (CurveGraphicsHelper.ClipSegment(ref P0, ref P1))
                    ClipedSegs.Add((P0, P1));
            }

            float Distance = Size + 4;
            foreach (var Seg in ClipedSegs)
            {
                var P0 = Seg.Item1;
                var P1 = Seg.Item1;
                var Direction = (P1 - P0).Normalize();
                var Normal = new Vector2m(Direction.Y, Direction.X * -1);
                decimal Offset = (decimal)Distance / 2;

                var Vertices = new Vector2m[]
                {
                    P0 - Normal * Offset,
                    P1 - Normal * Offset,
                    P1 + Normal * Offset,
                    P0 + Normal * Offset
                };

                if (CurveMathHelper.IsPointInPolygon(new Vector2m(X, Y), Vertices))
                {
                    return this;
                }
            }

            return null;
        }

        public virtual bool IntersectWithRect(RectangleM Bound, CurveGraphicsHelper CurveGraphicsHelper)
        {
            var Segments = GetSegments(CurveGraphicsHelper);
            foreach (var Seg in Segments)
            {
                Vector2m P0 = CurveGraphicsHelper.WorldToScreen(Seg.Item1);
                Vector2m P1 = CurveGraphicsHelper.WorldToScreen(Seg.Item2);
                if (CurveMathHelper.CohenSutherland.Intersect(P0, P1, Bound, out _, out _))
                    return true;
            }

            return false;
        }

        public virtual bool GetIsInRange(decimal X)
        {
            return X >= StartPoint.X && X <= EndPoint.X;
        }
    }
}
