using EditorUI;
using System;

namespace CrossEditor
{
    public class SelectRectangle : IMovable
    {
        Vector2f StartPoint;
        Vector2f EndPoint;

        public event MoveHandler MoveEvent;

        public RectangleF GetBoundRect()
        {
            float X = Math.Min(StartPoint.X, EndPoint.X);
            float Y = Math.Min(StartPoint.Y, EndPoint.Y);
            float Width = Math.Abs(StartPoint.X - EndPoint.X);
            float Height = Math.Abs(StartPoint.Y - EndPoint.Y);
            return new RectangleF(X, Y, Width, Height);
        }

        public RectangleM GetBoundRectM()
        {
            decimal X = Math.Min((decimal)StartPoint.X, (decimal)EndPoint.X);
            decimal Y = Math.Min((decimal)StartPoint.Y, (decimal)EndPoint.Y);
            decimal Width = Math.Abs((decimal)StartPoint.X - (decimal)EndPoint.X);
            decimal Height = Math.Abs((decimal)StartPoint.Y - (decimal)EndPoint.Y);
            return new RectangleM(X, Y, Width, Height);
        }
        public void Draw(UIManager UIManager)
        {
            {
                RectangleF Bound = GetBoundRect();
                Color BorderColor = Color.FromRGB(211, 211, 211);
                Color BackColor = Color.FromRGBA(128, 128, 128, 128);
                EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
                EditorUICanvas.FillRectangle((int)Bound.X, (int)Bound.Y, (int)Bound.Width, (int)Bound.Height, ref BackColor);
                EditorUICanvas.DrawRectangle((int)Bound.X, (int)Bound.Y, (int)Bound.Width, (int)Bound.Height, ref BorderColor);
            }
        }

        public void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            EndPoint.X += (float)DeltaX;
            EndPoint.Y += (float)DeltaY;

            MoveEvent?.Invoke(this, new MoveEvnetArgs() { });
        }

        public void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
            this.StartPoint = new Vector2f((float)StartPoint.X, (float)StartPoint.Y);
            EndPoint = new Vector2f((float)StartPoint.X, (float)StartPoint.Y);
        }

        public void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
            this.EndPoint = new Vector2f((float)EndPoint.X, (float)EndPoint.Y);
        }

        public void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            EndPoint = new Vector2f((float)DispX, (float)DispY);
        }
    }
}
