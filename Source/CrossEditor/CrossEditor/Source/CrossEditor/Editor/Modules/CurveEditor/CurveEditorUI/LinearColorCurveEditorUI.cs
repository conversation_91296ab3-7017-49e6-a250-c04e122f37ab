using CEngine;
using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class LinearColorCurveEditorUI : CurveEditorUI
    {
        static readonly LinearColorCurveEditorUI _Instance = new LinearColorCurveEditorUI();

        Dictionary<string, Color> ColorCurveMap = new Dictionary<string, Color>{
                                                  { "R", Color.FromRGB(255, 0, 0) },
                                                  { "G", Color.FromRGB(0, 255, 0) },
                                                  { "B", Color.FromRGB(0, 0, 255) },
                                                  { "A", Color.FromRGB(255, 255, 255) }
                                                  };
        bool bGradientColor = true;
        int GradientPointSize = 10;
        bool bLoadCurve = false;
        VContainer _GradientContainer;

        public new static LinearColorCurveEditorUI GetInstance()
        {
            return _Instance;
        }

        public new bool Initialize()
        {
            bModified = false;
            bSeqOpen = false;

            _PanelBack = new Panel();
            _PanelBack.Initialize();

            #region Initialize Panel

            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.SetCanFocus(true);

            _Panel.PaintEvent += OnPanelPaint;

            _Panel.LeftMouseDownEvent += OnPanelLeftMouseDown;
            _Panel.LeftMouseUpEvent += OnPanelLeftMouseUp;

            _Panel.RightMouseDownEvent += OnPanelRightMouseDown;
            _Panel.RightMouseUpEvent += OnPanelRightMouseUp;

            _Panel.MouseMoveEvent += OnPanelMouseMove;
            _Panel.MouseWheelEvent += OnPanelMouseWheel;

            #endregion Initialize Panel

            _ListView = new ListView();
            _ListView.Initialize();
            _ListView.SetWidth(50);
            _ListView.SetListViewStyle(ListViewStyle.List);
            _ListView.SetEnableRename(true);

            Texture NewTexture = UIManager.CreateUIImage(16, 16, Color.White.ToDword());


            _ListView.SelectionChangedEvent += OnListViewSelectionChanged;
            //_ListView.RightMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) => { ShowCurveManageContextMenu(MouseX, MouseY); };
            _ListView.KeyDownEvent += OnListViewKeyDown;
            _ListView.ItemRenameEvent += OnListViewItemRename;

            GradientColor = new GradientColor();
            GradientColor.Initialize(120);

            _GradientContainer = new VContainer();
            _GradientContainer.Initialize();
            if (bGradientColor)
            {
                _GradientContainer.AddFixedChild(GradientColor.GetPanel());
            }
            _GradientContainer.AddSizableChild(_Panel, 1f);

            _HSplitter = new HSplitter();
            _HSplitter.Initialize();
            _HSplitter.AddChild(_ListView);
            _HSplitter.AddChild(_GradientContainer);

            _ToolBarUI = new CurveEditToolBarUI();
            _ToolBarUI.Initialize(GetUIManager());
            _ToolBarUI.InputEnterEvent += OnToolBarUIInputEnter;
            _ToolBarUI.ZoomToFitClickEvent += OnZoomToFitButtonClick;
            _ToolBarUI.SaveClickEvent += (Sender) => { Save(); };
            _ToolBarUI.ConstantClickEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Constant); };
            _ToolBarUI.LinearClickEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Linear); };
            _ToolBarUI.SmoothClickEvent += (Sender) => { EditTool.ChangeSelectedPointType(PointType.Smooth); };
            _ToolBarUI.RepeatTypeMenuClickedEvent += (Sender, Type) => { ChangeRepeatType(Sender.bLeaveType, Type); };
            _ToolBarUI.GenerateClickEvent += ToolBarUITestGeneClickEvent;
            _ToolBarUI.AdjustClickEvent += ToolBarUIAdjustClickEvent;
            _ToolBarUI._ButtonTool_Save.SetVisible(false);
            _ToolBarUI._ButtonTool_SnappingX.SetVisible(false);
            _ToolBarUI._ButtonTool_SnappingY.SetVisible(false);
            _ToolBarUI._ButtonTool_ZoomToFit.SetVisible(false);
            _ToolBarUI._EnterRepeatTypeMenu._MenuButton.SetVisible(false);
            _ToolBarUI._LeaveRepeatTypeMenu._MenuButton.SetVisible(false);

            _PanelBack.AddChild(_ToolBarUI.GetPanel());
            _PanelBack.AddChild(_HSplitter);

            Filename = "";

            Axis = new Axis();
            Axis.Initialize(-0.5m, -0.5m, 0.1m, 0.1m, 100m, 50m);

            CurveGraphicsHelper = new CurveGraphicsHelper();
            CurveGraphicsHelper.Initialize(Axis, _Panel);

            Curves = new List<CurveManager>();
            SelectedCurveList = new List<CurveManager>();
            if (bGradientColor)
            {
                var CorlorCurves = InitializeColorCurve();
                Curves.AddRange(CorlorCurves);
                UpdateListView();
            }

            SelectedPointList = new List<Point>();
            CurveListItems = new List<ListViewItem>();

            SelectRectangle = null;
            MoveTool = new PointMoveTool();
            MoveTool.Initialize(SelectedPointList, _ToolBarUI);
            EditTool = new PointEditTool();
            EditTool.Initialize(SelectedPointList, SelectedCurveList);

            Initialize("LiearColorCurve", _PanelBack);
            _DockingCard.KeyDownEvent += OnDockingCardKeyDown;

            return true;
        }

        #region  Event Trigger
        private void ToolBarUIAdjustClickEvent(Button Sender)
        {
            InspectAdjustColor();
        }

        protected override void ToolBarUITestGeneClickEvent(Button Sender)
        {
            GradientColor.GenerateCurveGradientTex(Sender.GetUIManager());
        }

        void OnPanelPaint(Control Sender)
        {
            UIManager UIManager = GetUIManager();

            UpdateToolBar();
            Axis.Draw(CurveGraphicsHelper, UIManager);

            if (bGradientColor)
            {
                GradientColor.UpdateWidth(CurveGraphicsHelper);
            }

            foreach (var Cur in SelectedCurveList) Cur.Draw(CurveGraphicsHelper, UIManager);
            if (SelectRectangle != null) SelectRectangle.Draw(UIManager);

            Color Color = Color.FromRGB(255, 255, 255);
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawRectangle(_Panel.GetScreenX(), _Panel.GetScreenY(), _Panel.GetWidth(), _Panel.GetHeight(), ref Color.EDITOR_UI_BUTTON_BORDER_COLOR);
        }

        public new void OnListViewSelectionChanged(ListView Sender)
        {
            SelectedPointList.Clear();
            SelectedCurveList.Clear();

            var Items = Sender.GetSelectedItems();
            if (Items.Count > 0)
            {
                foreach (var Item in Items)
                {
                    SelectedCurveList.Add(Curves[Item.Index]);
                }
            }
        }

        #endregion

        #region ColorCurve
        public IEnumerable<CurveManager> InitializeColorCurve()
        {
            List<CurveManager> Curves = new List<CurveManager>();
            foreach (var Value in ColorCurveMap)
            {
                var CurveManager = new CurveManager(CurveRepeatType.CRT_LINEAR, CurveRepeatType.CRT_CONSTANT);
                CurveManager.Name = Value.Key;

                if (Value.Key != "A")
                {
                    CurveManager.AddNewPoint(0, 0, GradientPointSize, PointType.Linear);
                    CurveManager.AddNewPoint(1, 1, GradientPointSize, PointType.Linear);
                }
                else
                {
                    CurveManager.AddNewPoint(0, 1, GradientPointSize, PointType.Linear);
                    CurveManager.AddNewPoint(1, 1, GradientPointSize, PointType.Linear);
                }
                CurveManager.SetUnselectedColor(Value.Value);
                CurveManager.UpdateCurves();
                Curves.Add(CurveManager);
                SelectedCurveList.Add(CurveManager);
            }
            return Curves;
        }

        public Float4 GetUnadjustedLinearColorValue(float Time)
        {
            float R = 0f, G = 0, B = 0, A = 0;
            foreach (var Curve in Curves)
            {
                double Value = Curve.GetGradientValue(Time);
                switch (Curve.Name)
                {
                    case "R":
                        R = (float)Value;
                        break;
                    case "G":
                        G = (float)Value;
                        break;
                    case "B":
                        B = (float)Value;
                        break;
                    case "A":
                        A = (float)Value;
                        break;
                }
            }
            return new Float4(R, G, B, A);
        }

        public Axis GetAxis()
        {
            return Axis;
        }

        public void InspectAdjustColor()
        {
            //Inspect AdjustColor
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            InspectorUI.SetObjectInspected(GradientColor.GetAdjustColor());
            InspectorUI.InspectObject();
        }

        public List<Point> AddKeyInGradient(int ScreenX, int ScreenY, StopType StopType)
        {
            Vector2m Pos = new Vector2m(ScreenX, ScreenY);
            Vector2m Value = CurveGraphicsHelper.ScreenToWorld(Pos);

            List<Point> Points = new List<Point>();
            foreach (var CurveManager in Curves)
            {
                Value.Y = (decimal)CurveManager.GetGradientValue((float)Value.X);
                if (Value.X < 0 || Value.X > 1)
                {
                    Value.Y = 1;
                }
                if (StopType == StopType.AlphaStop)
                {
                    if (CurveManager.Name == "A")
                    {

                        Points.Add(CurveManager.AddNewPoint((float)Value.X, (double)Value.Y, GradientPointSize, PointType.Linear));
                    }
                }
                else
                {
                    if (CurveManager.Name != "A")
                    {
                        Points.Add(CurveManager.AddNewPoint((float)Value.X, (double)Value.Y, GradientPointSize, PointType.Linear));
                    }
                }

                CurveManager.PostModifiedCurve();
            }
            return Points;
        }

        public void DeleteKeyInGradientAlpha(List<Point> Pts)
        {
            if (Pts.Count == 1)
            {
                foreach (var CurveManager in Curves)
                {
                    if (CurveManager.Name == "A")
                    {
                        CurveManager.DeletePoint(Pts[0]);
                        CurveManager.PostModifiedCurve();
                    }
                }
            }
            else
            {
                foreach (var CurveManager in Curves)
                {
                    switch (CurveManager.Name)
                    {
                        case "R":
                            CurveManager.DeletePoint(Pts[0]);
                            break;
                        case "G":
                            CurveManager.DeletePoint(Pts[1]);
                            break;
                        case "B":
                            CurveManager.DeletePoint(Pts[2]);
                            break;
                    }
                    CurveManager.PostModifiedCurve();
                }
            }
        }

        #endregion

        public new void LoadFromCurves(IEnumerable<CurveManager> InCurves)
        {
            bLoadCurve = true;
            Clear();
            Curves.AddRange(InCurves);
            SelectedCurveList.AddRange(Curves);
            PostDeserialize();
        }

        void PostDeserialize()
        {
            GradientColor.ClearGradientStops();
            GradientColor.SetLoadCurve(Curves);
        }

        public bool GetBLoadLoadCurve()
        {
            return bLoadCurve;
        }

        public void Save()
        {

        }

        public List<CurveManager> GetCurves()
        {
            return Curves;
        }
    }
}
