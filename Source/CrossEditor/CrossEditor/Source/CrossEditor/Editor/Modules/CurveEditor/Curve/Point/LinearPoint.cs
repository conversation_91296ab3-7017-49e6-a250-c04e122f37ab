using EditorUI;

namespace CrossEditor
{
    public class LinearPoint : Point
    {
        public LinearPoint() : base()
        {
            PointType = PointType.Linear;
            UnSelectedColor = Color.FromRGB(144, 238, 144);
        }

        public override void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager)
        {
            Color Color;
            if (bSelected)
            {
                Color = SelectedColor;
            }
            else
            {
                Color = UnSelectedColor;
            }

            CurveGraphicsHelper.FillTriangle(UIManager, Color, new Vector2f(ValueX, (float)ValueY), Size);
            CurveGraphicsHelper.DrawTriangle(UIManager, Color.FromRGB(0, 0, 0), 1f, new Vector2f(ValueX, (float)ValueY), Size);
        }
    }
}
