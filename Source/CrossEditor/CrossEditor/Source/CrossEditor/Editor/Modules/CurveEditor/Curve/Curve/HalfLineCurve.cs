using System.Collections.Generic;

namespace CrossEditor
{
    public class HalfLineCurve : BaseCurve
    {
        int Angle;

        public HalfLineCurve() : base()
        {
            Angle = 0;
        }

        public void Initialize(Vector2f StartPoint, int Angle)
        {
            base.Initialize(StartPoint, StartPoint);
            this.Angle = Angle;
        }

        public override decimal ComputeValue(float X)
        {
            return StartPoint.Y;
        }

        public override bool GetIsInRange(decimal X)
        {
            if (Angle == 0)
            {
                return X >= StartPoint.X;
            }
            else
            {
                return X <= StartPoint.X;
            }
        }

        public override List<(Vector2m, Vector2m)> GetSegments(CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2f P0 = CurveGraphicsHelper.WorldToScreen(StartPoint);
            Vector2f P1;
            switch (Angle)
            {
                case 0:
                    P1 = new Vector2f(CurveGraphicsHelper.X + CurveGraphicsHelper.Width, P0.Y);
                    break;
                case 180:
                    P1 = new Vector2f(CurveGraphicsHelper.X, P0.Y);
                    break;
                default:
                    P1 = new Vector2f(CurveGraphicsHelper.Width, P0.Y);
                    break;
            }
            P1 = CurveGraphicsHelper.ScreenToWorld(P1);
            return new List<(Vector2m, Vector2m)> { (StartPoint, P1) };
        }
    }
}
