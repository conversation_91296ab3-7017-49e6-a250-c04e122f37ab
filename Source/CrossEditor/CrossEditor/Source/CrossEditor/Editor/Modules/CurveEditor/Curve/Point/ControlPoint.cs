using EditorUI;
using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace CrossEditor
{
    public struct ControlAttributes
    {
        public decimal Weight;
        public decimal Tangent;

        public ControlAttributes(decimal Weight, decimal Tangent)
        {
            this.Weight = Weight;
            this.Tangent = Tangent;
        }
    }

    public class ControlPoint : Point
    {
        public new static bool IsSameType(Point Pt) { return Pt.PointType == PointType.Control; }

        [XmlIgnore()] public Point Parent;

        public decimal Weight;
        public decimal Tangent;

        public bool bLeavePoint;

        public bool bAutoWeight;
        public decimal AutoDispLength;

        [XmlIgnore()] private Vector2m NextPoint;
        [XmlIgnore()] private Vector2m LastMousePosition;

        public ControlPoint() : base()
        {
            PointType = PointType.Control;
            UnSelectedColor = Color.White;

            Parent = null;
            Weight = 1 / 3;
            Tangent = 0;
            bLeavePoint = true;
            bAutoWeight = true;
            AutoDispLength = 60;
        }

        public void Initialize(decimal Weight, decimal Tangent, bool IsLeavePoint, float Size, CurveManager OwnerCurve, Point Parent)
        {
            base.Initialize(0, 0, Size, OwnerCurve);
            this.Parent = Parent;
            this.Weight = Weight;
            this.Tangent = Tangent;
            bLeavePoint = IsLeavePoint;
        }

        public void UpdateValue(Vector2m NextPoint)
        {
            this.NextPoint = NextPoint;
            if ((float)NextPoint.X == Parent.ValueX)
            {
                ValueX = Parent.ValueX;
                ValueY = (NextPoint.Y + Parent.ValueY) / 2;
            }
            else
            {
                decimal Length = Math.Abs((decimal)Parent.ValueX - NextPoint.X) * Weight;
                Length *= bLeavePoint ? 1 : -1;

                ValueX = (float)(Length + (decimal)Parent.ValueX);
                ValueY = Length * Tangent + Parent.ValueY;
            }
        }

        public override void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager)
        {
            Color Color;
            float LineWidth = 1f;
            if (bSelected)
            {
                Color = SelectedColor;
            }
            else
            {
                Color = UnSelectedColor;
            }

            if (bAutoWeight)
            {
                Vector2m ParentDispPoint = CurveGraphicsHelper.WorldToScreen(Parent);
                GetDispCenterInAutoWeight(CurveGraphicsHelper, out Vector2m DispCenter);

                EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
                EditorUICanvas.DrawLineF(ParentDispPoint, DispCenter, LineWidth, ref Color);

                int Segment = 20;
                double Delta = 2f * Math.PI / Segment;
                double Theta = 0;
                List<Vector2m> SpherePoints = new List<Vector2m>();
                for (int i = 0; i < Segment; ++i)
                {
                    Vector2m NextPoint = new Vector2m()
                    {
                        X = DispCenter.X + (decimal)Math.Cos(Theta) * (decimal)Size,
                        Y = DispCenter.Y + (decimal)Math.Sin(Theta) * (decimal)Size
                    };
                    SpherePoints.Add(NextPoint);
                    Theta += Delta;
                }
                CurveGraphicsHelper.DrawPolygon(UIManager, Color, 1f, SpherePoints.ToArray());
            }
            else
            {
                CurveGraphicsHelper.DrawSegment(UIManager, Color, LineWidth, Parent, this);
                CurveGraphicsHelper.DrawSphere(UIManager, Color, LineWidth, this, Size);
            }
        }

        public override void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            decimal MoveLimit = 1e-8m;
            Vector2m ParentM = Parent;
            Vector2m NextVector2m = NextPoint;

            if (bAutoWeight)
            {
                LastMousePosition.X += DeltaX;
                LastMousePosition.Y += DeltaY;
                Vector2m Center = CurveGraphicsHelper.ScreenToWorld(LastMousePosition);
                if (bLeavePoint)
                {
                    Center.X = Center.X <= ParentM.X ? ParentM.X + MoveLimit : Center.X;
                }
                else
                {
                    Center.X = Center.X >= ParentM.X ? ParentM.X - MoveLimit : Center.X;
                }

                decimal NewTangent = (Center.Y - ParentM.Y) / (Center.X - ParentM.X);
                Tangent = NewTangent;

                Weight = 1 / 3;
            }
            else
            {
                Vector2m DispPoint = CurveGraphicsHelper.WorldToScreen(this);
                DispPoint.X += DeltaX;
                DispPoint.Y += DeltaY;
                Vector2m ValuePoint = CurveGraphicsHelper.ScreenToWorld(DispPoint);
                if (bLeavePoint)
                {
                    ValuePoint.X = ValuePoint.X > NextVector2m.X ? NextVector2m.X : ValuePoint.X;
                    ValuePoint.X = ValuePoint.X <= ParentM.X ? ParentM.X + MoveLimit : ValuePoint.X;
                }
                else
                {
                    ValuePoint.X = ValuePoint.X < NextVector2m.X ? NextVector2m.X : ValuePoint.X;
                    ValuePoint.X = ValuePoint.X >= ParentM.X ? ParentM.X - MoveLimit : ValuePoint.X;
                }

                decimal Length = (decimal)ValueX - ParentM.X;
                decimal NewLength = ValuePoint.X - ParentM.X;

                decimal NewWeight = NewLength / (NextVector2m.X - ParentM.X);

                decimal NewTangent = (ValuePoint.Y - ParentM.Y) / (ValuePoint.X - ParentM.X);

                Weight = NewWeight;
                Tangent = NewTangent;
            }

            OnMove(new MoveEvnetArgs());
        }

        public override void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
            base.MoveBegin(StartPoint, CurveGraphicsHelper);
            LastMousePosition = StartPoint;
        }

        public override Point HitTest(decimal X, decimal Y, CurveGraphicsHelper CurveGraphicsHelper)
        {
            if (bAutoWeight)
            {
                GetDispCenterInAutoWeight(CurveGraphicsHelper, out Vector2m DispCenter);
                RectangleM Bound = new RectangleM
                {
                    X = DispCenter.X - 2 * (decimal)Size,
                    Y = DispCenter.Y - 2 * (decimal)Size,
                    Width = 4 * (decimal)Size,
                    Height = 4 * (decimal)Size
                };
                return Bound.Contains(new Vector2m(X, Y)) ? this : null;
            }
            else
                return base.HitTest(X, Y, CurveGraphicsHelper);
        }

        private void GetDispCenterInAutoWeight(CurveGraphicsHelper CurveGraphicsHelper, out Vector2m DispCenter)
        {
            Vector2m ParentDispPoint = CurveGraphicsHelper.WorldToScreen(Parent);
            DispCenter = CurveGraphicsHelper.WorldToScreen(this);
            double Radian = (double)Math.Atan2((double)(ParentDispPoint.Y - DispCenter.Y), (double)(DispCenter.X - ParentDispPoint.X));
            DispCenter.X = ParentDispPoint.X + AutoDispLength * (decimal)Math.Cos(Radian);
            DispCenter.Y = ParentDispPoint.Y - AutoDispLength * (decimal)Math.Sin(Radian);
        }

        public override void SaveToXml(Record RecordPoint)
        {
            RecordPoint.SetTypeString(GetType().Name);
            RecordPoint.SetString("PointType", Enum.GetName(typeof(PointType), PointType));
            RecordPoint.SetFloat("Size", Size);
            RecordPoint.SetDecimal("Weight", Weight);
            RecordPoint.SetDecimal("Tangent", Tangent);
            RecordPoint.SetBool("bLeavePoint", bLeavePoint);
            RecordPoint.SetBool("bAutoWeight", bAutoWeight);
        }

        public override void LoadFromXml(Record RecordPoint)
        {
            PointType = (PointType)Enum.Parse(typeof(PointType), RecordPoint.GetString("PointType"));
            Size = RecordPoint.GetFloat("Size");
            Weight = RecordPoint.GetDecimal("Weight");
            Tangent = RecordPoint.GetDecimal("Tangent");
            bLeavePoint = RecordPoint.GetBool("bLeavePoint");
            bAutoWeight = RecordPoint.GetBool("bAutoWeight");
        }
    }
}
