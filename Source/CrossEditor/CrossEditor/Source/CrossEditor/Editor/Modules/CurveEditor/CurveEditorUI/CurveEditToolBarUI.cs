using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public delegate void InputEnterEventHandler(CurveEditToolBarUI Sender, float InputX, float InputY);
    public delegate void RepeatTypeMenuItemClickedEventHandler(RepeatTypeMenu Sender, CurveRepeatType ClickedType);

    public class RepeatTypeMenu
    {
        public event RepeatTypeMenuItemClickedEventHandler ClickedEvent;

        public Button _MenuButton;
        public Menu _Menu;
        public MenuItem _MenuItem_Constant;
        public MenuItem _MenuItem_Cycle;

        public Texture CheckedImage;

        public bool bLeaveType;

        public RepeatTypeMenu()
        {
            bLeaveType = false;
        }

        public void Initialize(UIManager UIManager)
        {
            CheckedImage = UIManager.LoadUIImage("Editor/Icons/Special/CheckedMark.png");

            _MenuItem_Constant = new MenuItem();
            _MenuItem_Constant.SetText("Constant");
            _MenuItem_Constant.ClickedEvent += (Sender) =>
            {
                ClickedEvent?.Invoke(this, CurveRepeatType.CRT_CONSTANT);
            };

            _MenuItem_Cycle = new MenuItem();
            _MenuItem_Cycle.SetText("Cycle");
            _MenuItem_Cycle.ClickedEvent += (Sender) =>
            {
                ClickedEvent?.Invoke(this, CurveRepeatType.CRT_CYCLE);
            };

            _Menu = new Menu(UIManager);
            _Menu.Initialize();
            _Menu.AddMenuItem(_MenuItem_Constant);
            _Menu.AddMenuItem(_MenuItem_Cycle);

            _MenuButton = new Button();
            _MenuButton.Initialize();
            _MenuButton.SetText(bLeaveType ? "Leave Type" : "Enter Type");
            _MenuButton.ClickedEvent += (Sender) =>
            {
                int X = _MenuButton.GetScreenX();
                int Y = _MenuButton.GetScreenY() + _MenuButton.GetHeight();
                UIManager.GetContextMenu().ShowMenu(_Menu, X, Y);
            };
        }

        public void Initialize(UIManager UIManager, bool bLeaveType)
        {
            this.bLeaveType = bLeaveType;
            Initialize(UIManager);
        }

        public void UpdateMenuItem(ICollection<CurveManager> SelectedCurves)
        {
            bool IsSelectedCurves = SelectedCurves.Count > 0;

            bool IsSameType = true;
            CurveRepeatType LastType = CurveRepeatType.CRT_CONSTANT;
            if (!IsSelectedCurves)
            {
                IsSameType = false;
            }
            else
            {
                LastType = bLeaveType ? SelectedCurves.First().LeaveRepeatType : SelectedCurves.First().EnterRepeatType;
                foreach (var Cur in SelectedCurves)
                {
                    CurveRepeatType NowType = bLeaveType ? Cur.LeaveRepeatType : Cur.EnterRepeatType;
                    if (NowType != LastType)
                    {
                        IsSameType = false;
                        break;
                    }
                }
            }

            _MenuItem_Constant.SetEnable(IsSelectedCurves);
            _MenuItem_Constant.SetImage(IsSameType && LastType == CurveRepeatType.CRT_CONSTANT ? CheckedImage : null);

            _MenuItem_Cycle.SetEnable(IsSelectedCurves);
            _MenuItem_Cycle.SetImage(IsSameType && LastType == CurveRepeatType.CRT_CYCLE ? CheckedImage : null);
        }
    }

    public class CurveEditToolBarUI
    {
        public const int FONT_SIZE = 16;
        public const int BUTTON_SIZE = 26;
        public const int BAR_HEIGHT = 32;

        public event InputEnterEventHandler InputEnterEvent;
        public event ButtonClickedEventHandler SaveClickEvent;
        public event ButtonClickedEventHandler ZoomToFitClickEvent;
        public event ButtonClickedEventHandler ConstantClickEvent;
        public event ButtonClickedEventHandler LinearClickEvent;
        public event ButtonClickedEventHandler SmoothClickEvent;
        public event RepeatTypeMenuItemClickedEventHandler RepeatTypeMenuClickedEvent;
        public event ButtonClickedEventHandler GenerateClickEvent;
        public event ButtonClickedEventHandler AdjustClickEvent;

        public static Texture CheckedImage = UIManager.LoadUIImage("Editor/Icons/Special/CheckedMark.png");
        public static float[] GridIntervals = { 0.1f, 0.5f, 1f, 2f, 5f, 10f, 50f, 100f };

        public bool EnableSnappingX = true;
        public bool EnableSnappingY = true;
        public float SnappingIntervalX = 0.1f;
        public float SnappingIntervalY = 0.0f;

        OperationBarUI _OperationBar;

        Edit _InputX;
        Edit _InputY;

        public Button _ButtonTool_Save;
        public Button _ButtonTool_SnappingX;
        public Button _ButtonTool_SnappingY;
        public Button _ButtonTool_ZoomToFit;
        Button _ButtonTool_Constant;
        Button _ButtonTool_Linear;
        Button _ButtonTool_Smooth;
        public Button _ButtonTool_Adjust;
        public Button _ButtonTool_Generate;

        public RepeatTypeMenu _EnterRepeatTypeMenu;
        public RepeatTypeMenu _LeaveRepeatTypeMenu;

        public UIManager GetUIManager()
        {
            Panel PanelBar = _OperationBar.GetPanelBar();
            if (PanelBar != null)
            {
                return PanelBar.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public void Initialize(UIManager UIManager)
        {
            _InputX = new Edit();
            _InputY = new Edit();

            var Edits = new Edit[] { _InputX, _InputY };
            foreach (var Edit in Edits)
            {
                Edit.Initialize(EditMode.Simple_SingleLine);
                Edit.LoadSource("");
                Edit.SetFontSize(FONT_SIZE);
                Edit.SetSize(FONT_SIZE * 8, FONT_SIZE);
                Edit.KeyDownEvent += OnEditKeyDown;
            }

            _ButtonTool_Save = new Button();
            _ButtonTool_Save.Initialize();
            _ButtonTool_Save.SetText("Save");
            _ButtonTool_Save.SetFontSize(FONT_SIZE);
            _ButtonTool_Save.SetSize(FONT_SIZE * 4, BUTTON_SIZE);
            _ButtonTool_Save.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonTool_Save.ClickedEvent += (Sender) => SaveClickEvent?.Invoke(Sender);

            _ButtonTool_ZoomToFit = new Button();
            _ButtonTool_ZoomToFit.Initialize();
            _ButtonTool_ZoomToFit.SetText("Zoom To Fit");
            _ButtonTool_ZoomToFit.SetTextOffsetY(2);
            _ButtonTool_ZoomToFit.SetFontSize(FONT_SIZE);
            _ButtonTool_ZoomToFit.SetSize(FONT_SIZE * 8, BUTTON_SIZE);
            _ButtonTool_ZoomToFit.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonTool_ZoomToFit.ClickedEvent += (Sender) => ZoomToFitClickEvent?.Invoke(Sender);

            _ButtonTool_Constant = new Button();
            _ButtonTool_Constant.Initialize();
            _ButtonTool_Constant.SetText("C");
            _ButtonTool_Constant.ClickedEvent += (Sender) => ConstantClickEvent?.Invoke(Sender);

            _ButtonTool_Linear = new Button();
            _ButtonTool_Linear.Initialize();
            _ButtonTool_Linear.SetText("L");
            _ButtonTool_Linear.ClickedEvent += (Sender) => LinearClickEvent?.Invoke(Sender);

            _ButtonTool_Smooth = new Button();
            _ButtonTool_Smooth.Initialize();
            _ButtonTool_Smooth.SetText("S");
            _ButtonTool_Smooth.ClickedEvent += (Sender) => SmoothClickEvent?.Invoke(Sender);

            var Buttons = new Button[] { _ButtonTool_Constant, _ButtonTool_Linear, _ButtonTool_Smooth };
            foreach (var Button in Buttons)
            {
                Button.SetTextOffsetY(2);
                Button.SetFontSize(FONT_SIZE);
                Button.SetSize(BUTTON_SIZE, BUTTON_SIZE);
                Button.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            }

            _ButtonTool_SnappingX = new Button();
            _ButtonTool_SnappingX.Initialize();
            _ButtonTool_SnappingX.SetText("Snap X");
            _ButtonTool_SnappingX.SetTextOffsetX(2);
            _ButtonTool_SnappingX.SetFontSize(FONT_SIZE);
            _ButtonTool_SnappingX.SetSize(FONT_SIZE * 5, BUTTON_SIZE);
            _ButtonTool_SnappingX.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonTool_SnappingX.ClickedEvent += (Sender) => { ShowSnappingXMenu(); };

            _ButtonTool_SnappingY = new Button();
            _ButtonTool_SnappingY.Initialize();
            _ButtonTool_SnappingY.SetText("Snap Y");
            _ButtonTool_SnappingY.SetTextOffsetX(2);
            _ButtonTool_SnappingY.SetFontSize(FONT_SIZE);
            _ButtonTool_SnappingY.SetSize(FONT_SIZE * 5, BUTTON_SIZE);
            _ButtonTool_SnappingY.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonTool_SnappingY.ClickedEvent += (Sender) => { ShowSnappingYMenu(); };

            _ButtonTool_Adjust = new Button();
            _ButtonTool_Adjust.Initialize();
            _ButtonTool_Adjust.SetText("Adjust");
            _ButtonTool_Adjust.SetTextOffsetX(2);
            _ButtonTool_Adjust.SetFontSize(FONT_SIZE);
            _ButtonTool_Adjust.SetSize(FONT_SIZE * 6, BUTTON_SIZE);
            _ButtonTool_Adjust.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonTool_Adjust.ClickedEvent += (Sender) => AdjustClickEvent?.Invoke(Sender);

            _ButtonTool_Generate = new Button();
            _ButtonTool_Generate.Initialize();
            _ButtonTool_Generate.SetText("Generate");
            _ButtonTool_Generate.SetTextOffsetX(2);
            _ButtonTool_Generate.SetFontSize(FONT_SIZE);
            _ButtonTool_Generate.SetSize(FONT_SIZE * 6, BUTTON_SIZE);
            _ButtonTool_Generate.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonTool_Generate.ClickedEvent += (Sender) => GenerateClickEvent?.Invoke(Sender);

            _EnterRepeatTypeMenu = new RepeatTypeMenu();
            _EnterRepeatTypeMenu.Initialize(UIManager, false);
            _EnterRepeatTypeMenu._MenuButton.SetTextOffsetY(2);
            _EnterRepeatTypeMenu._MenuButton.SetFontSize(FONT_SIZE);
            _EnterRepeatTypeMenu._MenuButton.SetSize(FONT_SIZE * 8, BUTTON_SIZE);
            _EnterRepeatTypeMenu._MenuButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _EnterRepeatTypeMenu.ClickedEvent += (Sender, Type) => { RepeatTypeMenuClickedEvent?.Invoke(Sender, Type); };

            _LeaveRepeatTypeMenu = new RepeatTypeMenu();
            _LeaveRepeatTypeMenu.Initialize(UIManager, true);
            _LeaveRepeatTypeMenu._MenuButton.SetTextOffsetY(2);
            _LeaveRepeatTypeMenu._MenuButton.SetFontSize(FONT_SIZE);
            _LeaveRepeatTypeMenu._MenuButton.SetSize(FONT_SIZE * 8, BUTTON_SIZE);
            _LeaveRepeatTypeMenu._MenuButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _LeaveRepeatTypeMenu.ClickedEvent += (Sender, Type) => { RepeatTypeMenuClickedEvent?.Invoke(Sender, Type); };

            _OperationBar = new OperationBarUI();
            _OperationBar.Initialize();
            _OperationBar.SetBarHeight(BAR_HEIGHT);

            _OperationBar.AddLeft(_ButtonTool_Save);
            _OperationBar.AddLeft(_InputX);
            _OperationBar.AddLeft(_InputY);
            _OperationBar.AddLeft(_ButtonTool_SnappingX);
            _OperationBar.AddLeft(_ButtonTool_SnappingY);
            _OperationBar.AddLeft(_ButtonTool_Adjust);
            _OperationBar.AddLeft(_ButtonTool_Generate);
            _OperationBar.AddRight(_ButtonTool_ZoomToFit);
            _OperationBar.AddRight(_ButtonTool_Constant);
            _OperationBar.AddRight(_ButtonTool_Linear);
            _OperationBar.AddRight(_ButtonTool_Smooth);
            _OperationBar.AddRight(_EnterRepeatTypeMenu._MenuButton);
            _OperationBar.AddRight(_LeaveRepeatTypeMenu._MenuButton);
            _OperationBar.GetPanelBar().PositionChangedEvent += OnOperationBarPositionChanged;
        }

        public OperationBarUI GetOperationBar()
        {
            return _OperationBar;
        }

        void OnEditKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && Key == Key.Enter)
            {
                if (float.TryParse(_InputX.GetSourceString(), out float InputX) &&
                    float.TryParse(_InputY.GetSourceString(), out float InputY))
                {
                    InputEnterEvent?.Invoke(this, InputX, InputY);
                }
            }
        }

        void OnOperationBarPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            int EditSpanY = (BAR_HEIGHT - FONT_SIZE) / 2;
            _InputX.SetY(Sender.GetY() + EditSpanY);
            _InputY.SetY(Sender.GetY() + EditSpanY);

            int ButtonSpanY = (BAR_HEIGHT - BUTTON_SIZE) / 2;
            int ButtonY = Sender.GetY() + ButtonSpanY;
            _ButtonTool_Save.SetY(ButtonY);
            _ButtonTool_SnappingX.SetY(ButtonY);
            _ButtonTool_SnappingY.SetY(ButtonY);
            _ButtonTool_Adjust.SetY(ButtonY);
            _ButtonTool_Generate.SetY(ButtonY);
            _ButtonTool_ZoomToFit.SetY(ButtonY);
            _ButtonTool_Constant.SetY(ButtonY);
            _ButtonTool_Linear.SetY(ButtonY);
            _ButtonTool_Smooth.SetY(ButtonY);
            _EnterRepeatTypeMenu._MenuButton.SetY(ButtonY);
            _LeaveRepeatTypeMenu._MenuButton.SetY(ButtonY);
        }

        void ShowSnappingXMenu()
        {
            Menu Menu = new Menu(GetUIManager());

            foreach (float Spacing in GridIntervals)
            {
                MenuItem MenuItem = new MenuItem();
                MenuItem.SetText(Spacing.ToString());
                MenuItem.ClickedEvent += (Sender) =>
                {
                    EnableSnappingX = true;
                    SnappingIntervalX = Spacing;
                };
                if (EnableSnappingX && SnappingIntervalX == Spacing)
                {
                    MenuItem.SetImage(CheckedImage);
                }
                Menu.AddMenuItem(MenuItem);
            }

            MenuItem MenuItem_Automatic = new MenuItem();
            MenuItem_Automatic.SetText("Automatic");
            MenuItem_Automatic.ClickedEvent += (Sender) =>
            {
                EnableSnappingX = true;
                SnappingIntervalX = 0.0f;
            };
            if (EnableSnappingX && SnappingIntervalX == 0.0f)
            {
                MenuItem_Automatic.SetImage(CheckedImage);
            }
            Menu.AddMenuItem(MenuItem_Automatic);

            MenuItem MenuItem_Disable = new MenuItem();
            MenuItem_Disable.SetText("Disable");
            MenuItem_Disable.ClickedEvent += (Sender) =>
            {
                EnableSnappingX = false;
            };
            if (!EnableSnappingX)
            {
                MenuItem_Disable.SetImage(CheckedImage);
            }
            Menu.AddMenuItem(MenuItem_Disable);

            int X = _ButtonTool_SnappingX.GetScreenX();
            int Y = _ButtonTool_SnappingX.GetScreenY() + _ButtonTool_SnappingX.GetHeight();
            GetUIManager().GetContextMenu().ShowMenu(Menu, X, Y);
        }

        void ShowSnappingYMenu()
        {
            Menu Menu = new Menu(GetUIManager());

            foreach (float Spacing in GridIntervals)
            {
                MenuItem MenuItem = new MenuItem();
                MenuItem.SetText(Spacing.ToString());
                MenuItem.ClickedEvent += (Sender) =>
                {
                    EnableSnappingY = true;
                    SnappingIntervalY = Spacing;
                };
                if (EnableSnappingY && SnappingIntervalY == Spacing)
                {
                    MenuItem.SetImage(CheckedImage);
                }
                Menu.AddMenuItem(MenuItem);
            }

            MenuItem MenuItem_Automatic = new MenuItem();
            MenuItem_Automatic.SetText("Automatic");
            MenuItem_Automatic.ClickedEvent += (Sender) =>
            {
                EnableSnappingY = true;
                SnappingIntervalY = 0.0f;
            };
            if (EnableSnappingY && SnappingIntervalY == 0.0f)
            {
                MenuItem_Automatic.SetImage(CheckedImage);
            }
            Menu.AddMenuItem(MenuItem_Automatic);

            MenuItem MenuItem_Disable = new MenuItem();
            MenuItem_Disable.SetText("Disable");
            MenuItem_Disable.ClickedEvent += (Sender) =>
            {
                EnableSnappingY = false;
            };
            if (!EnableSnappingY)
            {
                MenuItem_Disable.SetImage(CheckedImage);
            }
            Menu.AddMenuItem(MenuItem_Disable);

            int X = _ButtonTool_SnappingY.GetScreenX();
            int Y = _ButtonTool_SnappingY.GetScreenY() + _ButtonTool_SnappingY.GetHeight();
            GetUIManager().GetContextMenu().ShowMenu(Menu, X, Y);
        }

        public Panel GetPanel() { return _OperationBar.GetPanelBar(); }

        public bool GetModified()
        {
            return _InputX.GetModified() || _InputY.GetModified();
        }

        public void SetValue(float X, float Y)
        {
            _InputX.LoadSource(string.Format("{0:0.####}", X));
            _InputY.LoadSource(string.Format("{0:0.####}", Y));
        }

        public void SetString(string StrX, string StrY)
        {
            _InputX.LoadSource(StrX);
            _InputY.LoadSource(StrY);
        }

        public void ClearValue()
        {
            _InputX.LoadSource("");
            _InputY.LoadSource("");
        }

        public void Update(in ICollection<CurveManager> SelectedCurves)
        {
            _EnterRepeatTypeMenu.UpdateMenuItem(SelectedCurves);
            _LeaveRepeatTypeMenu.UpdateMenuItem(SelectedCurves);
        }
    }
}
