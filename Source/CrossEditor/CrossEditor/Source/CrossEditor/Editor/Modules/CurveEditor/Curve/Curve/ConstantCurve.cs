using System.Collections.Generic;

namespace CrossEditor
{
    public class ConstantCurve : BaseCurve
    {
        public override decimal ComputeValue(float X)
        {
            return (decimal)X == EndPoint.X ? EndPoint.Y : StartPoint.Y;
        }

        public override List<(Vector2m, Vector2m)> GetSegments(CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2m MidPoint = new Vector2m(EndPoint.X, StartPoint.Y);
            return new List<(Vector2m, Vector2m)> { (StartPoint, MidPoint), (MidPoint, EndPoint) };
        }
    }
}
