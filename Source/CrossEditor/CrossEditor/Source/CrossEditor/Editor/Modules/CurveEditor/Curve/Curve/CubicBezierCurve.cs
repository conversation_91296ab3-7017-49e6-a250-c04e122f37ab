using System;
using System.Collections.Generic;
using System.Numerics;

namespace CrossEditor
{
    public class CubicBezierCurve : BaseCurve
    {
        public Vector2m P1, P2;

        // A, B, C represents three coefficient
        private decimal AX, BX, CX;
        private decimal AY, BY, CY;

        public CubicBezierCurve()
        {
            AX = 0;
            BX = 0;
            CX = 0;
            AY = 0;
            BY = 0;
            CY = 0;
        }

        public override void Initialize(Vector2m StartPoint, Vector2m EndPoint)
        {
            base.Initialize(StartPoint, EndPoint);
            P1 = new Vector2m((decimal)0.25 * StartPoint.X + (decimal)0.75 * EndPoint.X, StartPoint.Y);
            P2 = new Vector2m((decimal)0.75 * StartPoint.X + (decimal)0.25f * EndPoint.X, EndPoint.Y);

            CX = 3 * (P1.X - StartPoint.X);
            BX = 3 * (P2.X - P1.X) - CX;
            AX = EndPoint.X - StartPoint.X - CX - BX;

            CY = 3 * (P1.Y - StartPoint.Y);
            BY = 3 * (P2.Y - P1.Y) - CY;
            AY = EndPoint.Y - StartPoint.Y - CY - BY;
        }

        public void Initialize(Vector2m P0, Vector2m P1, Vector2m P2, Vector2m P3)
        {
            this.StartPoint = new Vector2m(P0.X, P0.Y);
            this.P1 = new Vector2m(P1.X, P1.Y);
            this.P2 = new Vector2m(P2.X, P2.Y);
            this.EndPoint = new Vector2m(P3.X, P3.Y);

            CX = 3 * (P1.X - P0.X);
            BX = 3 * (P2.X - P1.X) - CX;
            AX = P3.X - P0.X - CX - BX;

            CY = 3 * (P1.Y - P0.Y);
            BY = 3 * (P2.Y - P1.Y) - CY;
            AY = P3.Y - P0.Y - CY - BY;
        }

        public Vector2m ComputeCubicBezier(float T)
        {
            Vector2m Result = new Vector2m();
            float TSquared = T * T;
            float TCubed = TSquared * T;

            Result.X = (AX * (decimal)TCubed) + (BX * (decimal)TSquared) + (CX * (decimal)T) + StartPoint.X;
            Result.Y = (AY * (decimal)TCubed) + (BY * (decimal)TSquared) + (CY * (decimal)T) + StartPoint.Y;

            return Result;
        }
        public Vector2m ComputeCubicBezier(decimal T)
        {
            Vector2m Result = new Vector2m();
            decimal TSquared = T * T;
            decimal TCubed = TSquared * T;

            Result.X = (AX * (decimal)TCubed) + (BX * (decimal)TSquared) + (CX * (decimal)T) + StartPoint.X;
            Result.Y = (AY * (decimal)TCubed) + (BY * (decimal)TSquared) + (CY * (decimal)T) + StartPoint.Y;

            return Result;
        }

        public override decimal ComputeValue(float X)
        {
            if ((decimal)X == StartPoint.X) return StartPoint.Y;
            else if ((decimal)X == EndPoint.X) return EndPoint.Y;

            decimal T = decimal.MaxValue;
            Complex[] Solutions;
            if (CurveMathHelper.IsNearlyZero((float)AX))
            {
                if (CurveMathHelper.IsNearlyZero((float)BX))
                {
                    T = CurveMathHelper.SolveLinearEquation(CX, StartPoint.X - (decimal)X);
                }
                else
                {
                    Solutions = CurveMathHelper.SolveQuadraticEquation((float)BX, (float)CX, (float)(StartPoint.X - (decimal)X));
                    T = (decimal)Solutions[0].Real;
                }
            }
            else
            {

                Solutions = CurveMathHelper.SolveCubicEquation((float)AX, (float)BX, (float)CX, (float)(StartPoint.X - (decimal)X));
                foreach (Complex Solve in Solutions)
                {
                    if (Solve.Real > 0.0 && Solve.Real < 1.0 && CurveMathHelper.IsNearlyZero((float)Solve.Imaginary))
                    {
                        T = (decimal)Solve.Real;
                        break;
                    }
                }
                //T = (float)Solutions[1].Real;
                //for (int i = 0; i < 3; ++i) Console.WriteLine(string.Format("{0}: {1}, {2}", i, Solutions[i].Real, Solutions[i].Imaginary));
            }

            return ComputeCubicBezier(T).Y;
        }

        public override List<(Vector2m, Vector2m)> GetSegments(CurveGraphicsHelper CurveGraphicsHelper)
        {
            decimal Distance = CurveGraphicsHelper.WorldToScreen(EndPoint).X - CurveGraphicsHelper.WorldToScreen(StartPoint).X;
            int Segment = (int)Math.Clamp(Distance / 5, 3, 100);

            Vector2m LastPoint = new Vector2m(StartPoint.X, StartPoint.Y);
            Vector2m NewPoint;
            var Segments = new List<(Vector2m, Vector2m)>();
            for (int i = 1; i < Segment; ++i)
            {
                float T = i * 1f / Segment;
                NewPoint = ComputeCubicBezier(T);
                Segments.Add((LastPoint, NewPoint));
                LastPoint = NewPoint;
            }
            Segments.Add((LastPoint, EndPoint));

            return Segments;
        }

        public void GetLimitValue(ref decimal MinY, ref decimal MaxY)
        {
            for (int i = 1; i < 100; ++i)
            {
                float T = i * 1f / 100;
                decimal Y = (decimal)ComputeCubicBezier(T).Y;
                MinY = Math.Min(Y, MinY);
                MaxY = Math.Max(Y, MaxY);
            }
        }
    }
}
