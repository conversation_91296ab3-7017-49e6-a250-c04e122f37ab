using CEngine;
using EditorUI;
using System;
using System.Xml.Serialization;

namespace CrossEditor
{
    public class SmoothPoint : Point
    {
        public new static bool IsSameType(Point Pt) { return Pt.PointType == PointType.Smooth; }

        public ControlPoint ControlPointLeft, ControlPointRight;

        private bool bAutoWeighted;
        public bool AutoWeighted { set { ControlPointLeft.bAutoWeight = ControlPointRight.bAutoWeight = bAutoWeighted = value; } get { return bAutoWeighted; } }



        private string _EventString;
        public string EventString { set { _EventString = value; } get { return _EventString; } }

        public KeySmoothMode Mode { set; get; }

        [XmlIgnore()] public bool bControlPointActiveLeft, bControlPointActiveRight;

        public SmoothPoint() : base()
        {
            PointType = PointType.Smooth;
            UnSelectedColor = Color.White;

            ControlPointLeft = new ControlPoint();
            ControlPointRight = new ControlPoint();

            AutoWeighted = true;

            Mode = KeySmoothMode.KSM_AUTO;
            bControlPointActiveLeft = true;
            bControlPointActiveRight = true;

            EventString = "";
        }

        public override void Initialize(float ValueX, decimal ValueY, float Size, CurveManager OwnerCurve)
        {
            base.Initialize(ValueX, ValueY, Size, OwnerCurve);
            InitializeControlPoints();
        }

        public void InitializeControlPoints(decimal ArriveWeight = 1 / 3, decimal ArriveTangent = 0, decimal LeaveWeight = 1 / 3, decimal LeaveTangent = 0)
        {
            ControlPointLeft.Initialize(ArriveWeight, ArriveTangent, false, Size, OwnerCurve, this);
            ControlPointRight.Initialize(LeaveWeight, LeaveTangent, true, Size, OwnerCurve, this);
        }

        public override void PostDeserialize()
        {
            base.PostDeserialize();

            ControlPointLeft.Parent = this;
            ControlPointRight.Parent = this;
            //ControlPointLeft.MoveEvent += AfterMove;
            //ControlPointRight.MoveEvent += AfterMove;
        }

        public override void Update()
        {
            base.Update();
        }

        public override void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager)
        {
            Color Color;
            if (bSelected)
            {
                Color = SelectedColor;
            }
            else
            {
                Color = UnSelectedColor;
            }

            if (bSelected || ControlPointLeft.bSelected || ControlPointRight.bSelected)
            {
                if (bControlPointActiveLeft && ControlPointLeft != null)
                {
                    ControlPointLeft.Draw(CurveGraphicsHelper, UIManager);
                }

                if (bControlPointActiveRight && ControlPointRight != null)
                {
                    ControlPointRight.Draw(CurveGraphicsHelper, UIManager);
                }
            }

            CurveGraphicsHelper.FillDiamond(UIManager, Color, new Vector2f(ValueX, (float)ValueY), Size);
            CurveGraphicsHelper.DrawDiamond(UIManager, Color.FromRGB(0, 0, 0), 1f, new Vector2f(ValueX, (float)ValueY), Size);
        }

        public override Point HitTest(decimal X, decimal Y, CurveGraphicsHelper CurveGraphicsHelper)
        {
            if (base.HitTest(X, Y, CurveGraphicsHelper) != null) return this;

            Point HitPoint = null;
            if (bSelected || ControlPointLeft.bSelected || ControlPointRight.bSelected)
            {
                if (bControlPointActiveLeft && ControlPointLeft != null)
                {
                    HitPoint = ControlPointLeft.HitTest(X, Y, CurveGraphicsHelper);
                    if (HitPoint != null) return HitPoint;
                }

                if (bControlPointActiveRight && ControlPointRight != null)
                {
                    HitPoint = ControlPointRight.HitTest(X, Y, CurveGraphicsHelper);
                    if (HitPoint != null) return HitPoint;
                }
            }

            return null;
        }

        public override void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
            base.MoveBegin(StartPoint, CurveGraphicsHelper);
            if (bControlPointActiveLeft)
                ControlPointLeft?.MoveBegin(StartPoint, CurveGraphicsHelper);
            if (bControlPointActiveRight)
                ControlPointRight?.MoveBegin(StartPoint, CurveGraphicsHelper);
        }

        public override void SaveToXml(Record RecordPoint)
        {
            base.SaveToXml(RecordPoint);
            RecordPoint.SetBool("bAutoWeighted", bAutoWeighted);
            RecordPoint.SetString("Mode", Enum.GetName(typeof(KeySmoothMode), Mode));

            Record RecordLeft = new Record();
            ControlPointLeft.SaveToXml(RecordLeft);
            RecordPoint.AddChild(RecordLeft);

            Record RecordRight = new Record();
            ControlPointRight.SaveToXml(RecordRight);
            RecordPoint.AddChild(RecordRight);
        }

        public override void LoadFromXml(Record RecordPoint)
        {
            base.LoadFromXml(RecordPoint);
            bAutoWeighted = RecordPoint.GetBool("bAutoWeighted");
            Mode = (KeySmoothMode)Enum.Parse(typeof(KeySmoothMode), RecordPoint.GetString("Mode"));

            ControlPointLeft.LoadFromXml(RecordPoint.GetChild(0));
            ControlPointRight.LoadFromXml(RecordPoint.GetChild(1));
        }
    }
}
