using System.Collections.Generic;

namespace CrossEditor
{
    public class LinearCurve : BaseCurve
    {
        public override decimal ComputeValue(float X)
        {
            decimal K = (EndPoint.Y - StartPoint.Y) / (EndPoint.X - StartPoint.X);
            decimal B = StartPoint.Y;
            return K * ((decimal)X - StartPoint.X) + B;
        }

        public override List<(Vector2m, Vector2m)> GetSegments(CurveGraphicsHelper CurveGraphicsHelper)
        {
            return new List<(Vector2m, Vector2m)> { (StartPoint, EndPoint) };
        }
    }
}
