using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public enum StopType
    {
        None = 0,
        ColorStop = 1,
        AlphaStop = 2,
    }


    public class MoveStop : IMovable
    {
        Color _UnLargeColor = Color.FromRGB(186, 186, 186);
        Color _EnLargeColor = Color.FromRGB(161, 118, 4);
        Color _ColorSmallColor = Color.FromRGB(0, 0, 0);
        Color _AplhaSmallColor = Color.FromRGB(255, 255, 255);

        public int _ValueX, _ValueY, _LargeSize, _SmallSize;
        public float _ScaleX;
        public Color _Color;
        public float _Alpha = 1;
        public StopType _Type;
        protected bool bHighLight = false;
        public List<Point> _ContainPoints = new List<Point>();

        public event MoveHandler MoveEvent;

        public MoveStop() { }

        public MoveStop(Vector2i Vector2i, Color Color, float ScaleX)
        {
            _ValueX = Vector2i.X;
            _ValueY = Vector2i.Y;
            _Color = Color;
            _ScaleX = ScaleX;
        }

        public void Initialize(StopType Type, int ValueX, int ValueY, float ScaleX)
        {
            this._Type = Type;
            this._ValueX = ValueX;
            this._ValueY = ValueY;
            this._LargeSize = 20;
            this._SmallSize = 10;
            this._ScaleX = ScaleX;
        }

        public void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            _ValueX += (int)DeltaX;
        }

        public void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
        }

        public void MoveTo(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            _ValueX += (int)DeltaX;
            _ScaleX = (float)CurveGraphicsHelper.ScreenToWorld(new Vector2f(_ValueX + 10, _ValueY)).X;

            if (_ContainPoints.Count != 0)
            {
                foreach (var Point in _ContainPoints)
                {
                    Point.Move(DeltaX, 0, CurveGraphicsHelper);
                    Point.OwnerCurve.PostModifiedCurve();
                }
            }

        }

        public void OnMove(MoveEvnetArgs Args)
        {
            MoveEvent?.Invoke(this, Args);
        }

        public void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
        }

        public void Scale(float DeltaX, float DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            _ValueX += (int)DeltaX;
        }

        public MoveStop HitTest(float X, float Y, CurveGraphicsHelper CurveGraphicsHelper)
        {
            RectangleF Bound = new RectangleF
            {
                X = _ValueX,
                Y = _ValueY,
                Width = _LargeSize,
                Height = _LargeSize
            };
            return Bound.Contains(new Vector2f(X, Y)) ? this : null;
        }

        public void Draw(UIManager UIManager, StopType StopType, int Y, CurveGraphicsHelper CurveGraphicsHelper)
        {
            //refresh ValueX, ValueY
            Vector2f Scale = CurveGraphicsHelper.WorldToScreen(new Vector2f(_ScaleX, 0));
            _ValueX = (int)Scale.X - 10;
            _ValueY = Y;

            Color LargeColor = bHighLight ? _EnLargeColor : _UnLargeColor;
            Color SmallColor = StopType == StopType.ColorStop ? _ColorSmallColor : _AplhaSmallColor;

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.FillRectangle(_ValueX, _ValueY, _LargeSize, _LargeSize, ref LargeColor);
            EditorUICanvas.FillRectangle(_ValueX + 5, _ValueY + 5, _SmallSize, _SmallSize, ref SmallColor);
        }

        public void SetHighLight(bool flag)
        {
            bHighLight = flag;
        }

        public StopType GetStopType()
        {
            return _Type;
        }

        public void SetPoints(List<Point> Points)
        {
            _ContainPoints = Points;
            if (Points.Count == 1)
            {
                _Alpha = (float)Points[0].ValueY;
            }
            else
            {
                _Color = new Color((float)Points[0].ValueY, (float)Points[1].ValueY, (float)Points[2].ValueY, 1.0f);
            }
        }
    }
}
