using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class PointEditTool : BaseEditTool
    {
        List<CurveManager> SelectedCurveList;

        public void Initialize(List<Point> SelectedPointList, List<CurveManager> SelectedCurveList)
        {
            base.Initialize(SelectedPointList);
            this.SelectedCurveList = SelectedCurveList;
        }

        public void Add(decimal ValueX, decimal ValueY, PointType Type, CurveGraphicsHelper CurveGraphicsHelper)
        {
            Vector2m Value = new Vector2m(ValueX, ValueY);
            var CurvePointMap = new Dictionary<CurveManager, List<Point>>();

            foreach (var Cur in SelectedCurveList)
            {
                if (Cur.CheckIsValidX((float)Value.X))
                {
                    Point NewPoint = Cur.AddNewPoint((float)Value.X, (double)Value.Y, 4, Type);
                    CurvePointMap.Add(Cur, new List<Point> { NewPoint });
                }
                else
                {
                    EditorLogger.Log(LogMessageType.Error, "Add: invalid key x position: " + Value.X);
                }
            }

            if (CurvePointMap.Count > 0)
            {
                EditOperation_AddPoint AddOperation = new EditOperation_AddPoint(CurvePointMap);
                LastOperation = AddOperation;
                EditOperationManager.GetInstance().AddOperation(AddOperation);

                CurveEditorUI.GetInstance().SetModified(true, CurvePointMap.Keys);
            }
        }

        public void Delete(bool LinearColor = false)
        {
            var CurvePointMap = new Dictionary<CurveManager, List<Point>>();

            foreach (Point Pt in SelectedPointList)
            {
                CurveManager Cur = Pt.OwnerCurve;
                if (Cur.DeletePoint(Pt) != null)
                {
                    if (!CurvePointMap.ContainsKey(Cur)) CurvePointMap.Add(Cur, new List<Point>());
                    CurvePointMap[Cur].Add(Pt);
                }
            }

            if (CurvePointMap.Count > 0)
            {
                EditOperation_RemovePoints RemoveOperation = new EditOperation_RemovePoints(CurvePointMap);
                LastOperation = RemoveOperation;
                EditOperationManager.GetInstance().AddOperation(RemoveOperation);

                SelectedPointList.Clear();

                CurveEditorUI.GetInstance().SetModified(true, CurvePointMap.Keys);

                if (LinearColor)
                {
                    LinearColorCurveEditorUI.GetInstance().GradientColor.DeleteStopFromPoints(CurvePointMap);
                }
            }
        }

        public void ChangeSelectedPointType(PointType Type)
        {
            var Map = new Dictionary<CurveManager, List<(Point, Point)>>();

            for (int i = 0; i < SelectedPointList.Count(); ++i)
            {
                CurveManager Cur = SelectedPointList[i].OwnerCurve;

                Point NewPoint = Cur.ChangePointType(SelectedPointList[i], Type);
                if (NewPoint != null)
                {
                    if (!Map.ContainsKey(Cur)) Map.Add(Cur, new List<(Point, Point)>());
                    Map[Cur].Add((SelectedPointList[i], NewPoint));
                    SelectedPointList[i] = NewPoint;
                }
            }

            if (Map.Count() > 0)
            {
                EditOperation_ModifyPointType ModifyOperation = new EditOperation_ModifyPointType(Map, SelectedPointList);
                LastOperation = ModifyOperation;
                EditOperationManager.GetInstance().AddOperation(ModifyOperation);

                CurveEditorUI.GetInstance().SetModified(true, Map.Keys);
            }
        }

        public void ChangeSmoothMode(KeySmoothMode Mode)
        {
            var SmoothPointList = SelectedPointList.FindAll((Pt) => SmoothPoint.IsSameType(Pt));
            List<SmoothPoint> ModifiedPointList = new List<SmoothPoint>();
            List<KeySmoothMode> OldModes = new List<KeySmoothMode>();
            List<KeySmoothMode> NewModes = new List<KeySmoothMode>();
            foreach (SmoothPoint Pt in SmoothPointList)
            {
                if (Pt.Mode != Mode)
                {
                    ModifiedPointList.Add(Pt);
                    OldModes.Add(Pt.Mode);
                    NewModes.Add(Mode);
                    Pt.Mode = Mode;
                }
            }

            if (ModifiedPointList.Count > 0)
            {
                LastOperation = new EditOperation_ChangeSmoothMode(ModifiedPointList, OldModes, NewModes);
                EditOperationManager.GetInstance().AddOperation(LastOperation);

                CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPointList));
            } 
        }

        public void SetAutoWeighted(bool bAutoWeighted)
        {
            var SmoothPointList = SelectedPointList.FindAll((Pt) => SmoothPoint.IsSameType(Pt));
            List<SmoothPoint> ModifiedPointList = new List<SmoothPoint>();
            List<bool> OldAttrs = new List<bool>();
            List<bool> NewAttrs = new List<bool>();
            foreach (SmoothPoint Pt in SmoothPointList)
            {
                if (Pt.AutoWeighted != bAutoWeighted)
                {
                    ModifiedPointList.Add(Pt);
                    OldAttrs.Add(Pt.AutoWeighted);
                    NewAttrs.Add(bAutoWeighted);
                    Pt.AutoWeighted = bAutoWeighted;
                }
            }

            if (ModifiedPointList.Count > 0)
            {
                LastOperation = new EditOperation_ChangeAutoWeighted(ModifiedPointList, OldAttrs, NewAttrs);
                EditOperationManager.GetInstance().AddOperation(LastOperation);

                CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPointList));
            }
        }

        public override void Draw(CurveGraphicsHelper CurveGraphicsHelper) { }

        public override IMovable HitTest(float X, float Y, CurveGraphicsHelper CurveGraphicsHelper) { return null; }

        public override IMovable Select(IMovable Object) { return null; }
    }
}
