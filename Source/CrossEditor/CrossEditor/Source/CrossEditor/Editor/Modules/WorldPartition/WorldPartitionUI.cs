using CEngine;
using EditorUI;

namespace CrossEditor
{
    public class WorldPartitioConfig
    {
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Draw Grid")]
        public bool DrawGrid
        {
            get
            {
                if (EditorScene.GetInstance().GetWorld() == null) return false;
                return CrossEngineApi.IsDrawingWorldPartitionGrid(EditorScene.GetInstance().GetWorld().GetNativePointer());
            }
            set
            {
                CrossEngineApi.SetDrawWorldPartitionGrid(EditorScene.GetInstance().GetWorld().GetNativePointer(), value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "LoadingPolicy")]
        public WorldLoadingPolicy LoadingPolicy
        {
            get
            {
                return CrossEngineApi.GetWorldPartitionLoadingPolicy(EditorScene.GetInstance().GetWorld().GetNativePointer());
            }
            set
            {
                CrossEngineApi.SetWorldPartitionLoadingPolicy(EditorScene.GetInstance().GetWorld().GetNativePointer(), value);
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "World Partition Configuration")]
        public WorldPartitionConfiguration WPConfig { get; set; }

        public WorldPartitioConfig()
        {
            DrawGrid = false;
            WPConfig = new WorldPartitionConfiguration();
        }
    }


    public class WorldPartitionUI : DockingUI
    {
        const int SPAN_X = 5;
        const int SEPARATOR_HEIGHT = 1;
        const int BUTTON_FONT_SIZE = 18;
        const int BUTTON_HEIGHT = 30;

        static WorldPartitionUI _Instance = new WorldPartitionUI();

        public static WorldPartitionUI GetInstance() { return _Instance; }

        ScrollView _ScrollView;
        Panel _ScrollPanel;
        Inspector _Inspector;
        InspectorHandler _InspectorHandler;
        Button _NewSettingButton;
        Button _AddCustomBlockButtom;
        WorldPartitioConfig _Setting;

        public bool Initialize()
        {
            _Setting = new WorldPartitioConfig();
            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.GetHScroll().SetEnable(false);
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);

            _ScrollPanel = _ScrollView.GetScrollPanel();
            _ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);

            base.Initialize("World Partition", _ScrollView);

            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_Setting); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            _Inspector = new Inspector_Struct_With_Property();
            _Inspector.InspectObject(_Setting);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);

            _NewSettingButton = new Button();
            _NewSettingButton.Initialize();
            _NewSettingButton.SetText("Rearrange World");
            _NewSettingButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _NewSettingButton.SetFontSize(BUTTON_FONT_SIZE);
            _NewSettingButton.SetTextOffsetY(2);
            _NewSettingButton.ClickedEvent += onClockRearrangeWorld;
            _ScrollPanel.AddChild(_NewSettingButton);

            _AddCustomBlockButtom = new Button();
            _AddCustomBlockButtom.Initialize();
            _AddCustomBlockButtom.SetText("Add Custom Block");
            _AddCustomBlockButtom.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _AddCustomBlockButtom.SetFontSize(BUTTON_FONT_SIZE);
            _AddCustomBlockButtom.SetTextOffsetY(2);
            _AddCustomBlockButtom.ClickedEvent += onClockAddCustomBlock;
            _ScrollPanel.AddChild(_AddCustomBlockButtom);

            return true;
        }

        public void Refresh()
        {
            _Setting.WPConfig.CopyFrom(CrossEngineApi.GetWorldPartitionConfig(EditorScene.GetInstance().GetWorld().GetNativePointer()));
            _Inspector.ReadValue();
        }

        public bool IsEditorStreamingEnabled()
        {
            return _Setting.LoadingPolicy == WorldLoadingPolicy.Streaming;
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bSizeChanged)
            {
                UpdateLayout();
            }
        }

        public void onClockRearrangeWorld(Button Sender)
        {
            if (_Setting.LoadingPolicy == WorldLoadingPolicy.All)
            {
                CrossEngineApi.SetWorldPartitionConfig(EditorScene.GetInstance().GetWorld().GetNativePointer(), _Setting.WPConfig);
            }
            else
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Rearrange World Failed", "LoadingPolicy Must Be All");
            }
        }

        public void onClockAddCustomBlock(Button Sender)
        {
            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Input Block Name", "Please input a block name:", "NewBlock");
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                if (StringInputed != "")
                {
                    CrossEngineApi.AddWorldCustomBlock(EditorScene.GetInstance().GetWorld().GetNativePointer(), StringInputed);
                }
            };
            TextInputUI.ShowDialog();
        }

        public void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 5;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }

            int AvailableWidth = _ScrollView.GetWidth() - 2 * SPAN_X;
            Y += 5;
            _NewSettingButton.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;
            _AddCustomBlockButtom.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;

            _ScrollPanel.SetSize(ScrollPanelWidth, Y);
            _ScrollView.UpdateScrollBar();
        }
    }
}
