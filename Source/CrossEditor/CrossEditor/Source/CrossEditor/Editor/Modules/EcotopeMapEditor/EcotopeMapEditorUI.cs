using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class EditOperation_EcotopeMap : EditOperation
    {
        EcotopeMap Ecotope = null;
        float U;
        float V;
        float EndU;
        float EndV;
        float Density;

        public EditOperation_EcotopeMap(
        EcotopeMap _EcotopeMap,
        float _U,
        float _V,
        float _EndU,
        float _EndV,
        float _Density)
        {
            Ecotope = _EcotopeMap;
            U = _U;
            V = _V;
            EndU = _EndU;
            EndV = _EndV;
            Density = _Density;
        }

        public override void Undo()
        {
            Ecotope.Brush(U, V, EndU, EndV, -Density);
        }

        public override void Redo()
        {
            Ecotope.Brush(U, V, EndU, EndV, Density);
        }

        public override bool Valid()
        {
            return Ecotope != null;
        }
    }

    class EcotopeMapEditorUI : DockingUI
    {
        Panel _GlobalPannel;
        Panel _EditablePannel;
        Panel _TextureContainerPanel;
        Panel _BrushPannel;
        EcotopeMap _EcotopeMap = null;
        bool _Dirty = false;
        static EcotopeMapEditorUI LastOpened = null;

        public EcotopeMapEditorUI()
        {
        }

        public void OpenMap(EcotopeMap map)
        {
            if (LastOpened != null && LastOpened != this)
                LastOpened.GetDockingCard().CloseCard();

            if (_EcotopeMap == null && map.IsValid())
            {
                _EcotopeMap = map;
                EditorUICanvas.GetInstance().LoadUIImage_EcotopeMap(_EcotopeMap);
                Initialize();
            }

            if (_EcotopeMap != null)
            {
                LastOpened = this;
                MainUI.GetInstance().ActivateDockingCard_EcotopeMapEditor(GetDockingCard());
            }
        }

        public bool Initialize()
        {
            float BrushScale = 1.0f;
            float TextureScale = 1.0f;
            bool GlobalMouseRightClicked = false;
            bool GlobalMouseLeftClicked = false;
            int GlobalShiftClickVersion = 0;
            int PreMouseX = 0;
            int PreMouseY = 0;
            int PreTextureX = 0;
            int PreTextureY = 0;
            float InitDentisy = 0.1f;
            float Dentisy = -0.1f;
            int InitMapSize = _EcotopeMap.GetWidth();
            int InitBrushSize = 12;

            OperationBarUI _OperationBarUI = new OperationBarUI();
            _OperationBarUI.Initialize();

            Button _ButtonSave = OperationBarUI.CreateTextButton("Save");
            _ButtonSave.ClickedEvent += (Button Sender) => { _EcotopeMap.Save(_EcotopeMap.GetFilePath()); _Dirty = false; };

            Check _CheckErasing = new Check();
            _CheckErasing.Initialize();
            _CheckErasing.SetPosition(0, _ButtonSave.GetY(), 80, _ButtonSave.GetHeight());
            _CheckErasing.SetText("Erasing");
            _CheckErasing.SetTextAlign(TextAlign.CenterCenter);
            _CheckErasing.SetFontSize(_ButtonSave.GetFontSize());
            _CheckErasing.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _CheckErasing.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _CheckErasing.SetAutoCheck(true);
            _CheckErasing.SetEnable(true);
            _CheckErasing.SetChecked(true);
            _CheckErasing.ClickedEvent += (Check Sender) =>
            {
                GlobalShiftClickVersion = Sender.GetChecked() ? 0 : 1;
                Dentisy = GlobalShiftClickVersion % 2 == 0 ? -InitDentisy : InitDentisy;
                Color C = GlobalShiftClickVersion % 2 == 0 ? Color.EDITOR_UI_TEST_COLOR_RED : Color.EDITOR_UI_TEST_COLOR_GREEN;
                _TextureContainerPanel.SetBorderColor(C);
                _BrushPannel.SetBackgroundColor(C);
            };

            Label _LabelDensity = new Label();
            _LabelDensity.Initialize();
            _LabelDensity.SetFontSize(_ButtonSave.GetFontSize());
            _LabelDensity.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _LabelDensity.SetText("Strength:");
            _LabelDensity.SetPosition(0, _ButtonSave.GetY(), 80, _ButtonSave.GetHeight());
            _LabelDensity.SetTextAlign(TextAlign.CenterCenter);

            Edit _EditDensity = new Edit();
            TrackBar _PlayRateSlider = new TrackBar();

            _EditDensity.Initialize(EditMode.Simple_SingleLine);
            _EditDensity.SetPosition(0, _ButtonSave.GetY(), 40, _ButtonSave.GetHeight());
            _EditDensity.SetFontSize(_ButtonSave.GetFontSize());
            _EditDensity.SetText(InitDentisy.ToString());
            _EditDensity.SetTextAlign(TextAlign.BottomRight);
            _EditDensity.TextChangedEvent += (Edit Sender) =>
            {
                float p;
                if (float.TryParse(Sender.GetText(), out p))
                {
                    InitDentisy = Math.Clamp(p, 0.0f, 1.0f);
                    _PlayRateSlider.SetValue(InitDentisy);
                    Dentisy = GlobalShiftClickVersion % 2 == 0 ? -InitDentisy : InitDentisy;
                }
            };

            _PlayRateSlider.Initialize();
            _PlayRateSlider.SetPosition(0, _ButtonSave.GetY() + 1, 100, _ButtonSave.GetHeight() - 2);
            _PlayRateSlider.SetValue(InitDentisy);
            _PlayRateSlider.ValueChangedEvent += (TrackBar Sender) =>
            {
                InitDentisy = Sender.GetValue();
                _EditDensity.SetText(InitDentisy.ToString());
                Dentisy = GlobalShiftClickVersion % 2 == 0 ? -InitDentisy : InitDentisy;
            };


            _OperationBarUI.AddLeft(_LabelDensity);
            _OperationBarUI.AddLeft(_EditDensity);
            _OperationBarUI.AddLeft(_PlayRateSlider);
            _OperationBarUI.AddRight(_CheckErasing);
            _OperationBarUI.AddRight(_ButtonSave);

            _TextureContainerPanel = new Panel();
            _TextureContainerPanel.Initialize();
            _TextureContainerPanel.SetEnable(true);
            _TextureContainerPanel.SetVisible(true);
            _TextureContainerPanel.SetBackgroundColor(Color.EDITOR_UI_TEST_COLOR_RED);
            _TextureContainerPanel.SetPosition(0, 0, InitMapSize + 2, InitMapSize + 2);

            Panel _TexturePanel = new Panel();
            _TexturePanel.SetImage(UIManager.LoadUIImage(_EcotopeMap.GetFilePath()));
            _TexturePanel.SetPosition(1, 1, InitMapSize, InitMapSize);
            _TexturePanel.SetEnable(true);
            _TexturePanel.SetVisible(true);
            _TextureContainerPanel.AddChild(_TexturePanel);

            _BrushPannel = new Panel();
            _BrushPannel.Initialize();
            _BrushPannel.SetEnable(true);
            _BrushPannel.SetVisible(true);
            _BrushPannel.SetImage(UIManager.LoadUIImage("EngineResource/Editor/PCG/brush_circle.png"));
            _BrushPannel.SetPosition(0, 0, InitBrushSize, InitBrushSize);
            _BrushPannel.SetBackgroundColor(Color.EDITOR_UI_TEST_COLOR_RED);

            _EditablePannel = new Panel();
            _EditablePannel.Initialize();
            _EditablePannel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _EditablePannel.AddChild(_TextureContainerPanel);
            _EditablePannel.AddChild(_BrushPannel);

            _EditablePannel.MouseWheelEvent += (Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue) =>
            {
                if (IsBrushInTexturePanel(MouseX, MouseY))
                {
                    if (GetDevice().IsControlDownOnly())
                    {
                        BrushScale *= 1f + MouseDeltaZ * 0.1f;
                        BrushScale = Math.Clamp(BrushScale, 0.2f, 5f);

                        int Size = (int)(BrushScale * InitBrushSize);
                        int CenterX = (_BrushPannel.GetEndX() + _BrushPannel.GetX()) / 2;
                        int CenterY = (_BrushPannel.GetEndY() + _BrushPannel.GetY()) / 2;
                        int X = CenterX - Size / 2;
                        int Y = CenterY - Size / 2;
                        _BrushPannel.SetPosition(X, Y, Size, Size);
                    }
                    else
                    {
                        TextureScale *= 1f + MouseDeltaZ * 0.1f;
                        TextureScale = Math.Clamp(TextureScale, 0.02f, 50f);

                        int Size = (int)(TextureScale * InitMapSize);
                        int CenterX = (_TextureContainerPanel.GetEndX() + _TextureContainerPanel.GetX()) / 2;
                        int CenterY = (_TextureContainerPanel.GetEndY() + _TextureContainerPanel.GetY()) / 2;
                        UpdateTexturePanelLayout(CenterX, CenterY, Size, Size);
                    }
                }
            };

            _EditablePannel.RightMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                PreMouseX = MouseX; PreMouseY = MouseY;
                PreTextureX = _TextureContainerPanel.GetX(); PreTextureY = _TextureContainerPanel.GetY();
                GlobalMouseRightClicked = true;
                Sender.CaptureMouse();
            };

            _EditablePannel.RightMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                GlobalMouseRightClicked = false;
                Sender.ReleaseMouse();
            };

            _EditablePannel.LeftMouseDownEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                GlobalMouseLeftClicked = true;
                Sender.CaptureMouse();
            };

            _EditablePannel.LeftMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                GlobalMouseLeftClicked = false;
                Sender.ReleaseMouse();
            };

            _EditablePannel.MouseMoveEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            {
                _BrushPannel.SetVisible(false);

                if (GlobalMouseRightClicked)
                {
                    int Size = _TextureContainerPanel.GetWidth();
                    int DeltaX = PreMouseX - MouseX - Size / 2;
                    int DeltaY = PreMouseY - MouseY - Size / 2;
                    UpdateTexturePanelLayout(PreTextureX - DeltaX, PreTextureY - DeltaY, Size, Size);
                }
                else if (IsBrushInTexturePanel(MouseX, MouseY))
                {
                    _BrushPannel.SetVisible(true);
                    _BrushPannel.SetPos(MouseX - _EditablePannel.GetScreenX() - _BrushPannel.GetWidth() / 2, MouseY - _EditablePannel.GetScreenY() - _BrushPannel.GetHeight() / 2);

                    if (GlobalMouseLeftClicked)
                    {
                        int X = MouseX - _TexturePanel.GetScreenX() - _BrushPannel.GetWidth() / 2;
                        int Y = MouseY - _TexturePanel.GetScreenY() - _BrushPannel.GetHeight() / 2;
                        float U = (float)(X) / _TexturePanel.GetWidth();
                        float V = (float)(Y) / _TexturePanel.GetHeight();
                        float EndU = (float)(X + _BrushPannel.GetWidth()) / _TexturePanel.GetWidth();
                        float EndV = (float)(Y + _BrushPannel.GetHeight()) / _TexturePanel.GetHeight();

                        _EcotopeMap.Brush(U, V, EndU, EndV, Dentisy);

                        EditOperation_EcotopeMap Operation_EcotopeMap = new EditOperation_EcotopeMap(_EcotopeMap, U, V, EndU, EndV, Dentisy);
                        EditOperationManager.GetInstance().AddOperation(Operation_EcotopeMap);

                        _Dirty = true;
                    }
                }
            };

            //_EditablePannel.KeyDownEvent += (Control Sender, Key Key, ref bool bContinue) =>
            //{
            //    System.Console.WriteLine("KEY DOWN EVENT");
            //};

            _GlobalPannel = new Panel();
            _GlobalPannel.AddChild(_EditablePannel);
            _GlobalPannel.AddChild(_OperationBarUI.GetPanelBar());

            bool _FirstOpen = true;
            _GlobalPannel.UpdateEvent += (Control Sender, int TimeElapsed) =>
            {
                int width = _GlobalPannel.GetWidth();
                _OperationBarUI.GetPanelBar().SetWidth(width);
                _OperationBarUI.Refresh();
                int offsetY = _OperationBarUI.GetPanelBar().GetHeight();
                _EditablePannel.SetPosition(0, offsetY, width, _GlobalPannel.GetHeight() - offsetY);
                GetDockingCard().SetText(_Dirty ? "Ecotope*" : "Ecotope");

                if (_FirstOpen)
                {
                    _FirstOpen = false;
                    int CenterX = _EditablePannel.GetWidth() / 2;
                    int CenterY = _EditablePannel.GetHeight() / 2;
                    int Offset = 10;
                    int Width = _EditablePannel.GetWidth() - Offset * 2;
                    int Height = _EditablePannel.GetHeight() - Offset * 2;
                    int Size = Width < Height ? Width : Height;
                    TextureScale = (float)Size / InitMapSize;
                    UpdateTexturePanelLayout(CenterX, CenterY, Size, Size);
                }
            };

            //GetDevice().KeyEvent += (Device Sender, KeyAction keyAction, Key Key) =>
            //{
            //    if (GetDockingCard().GetActive())
            //    {
            //        if (Key == Key.Shift && keyAction == KeyAction.Up)
            //        {
            //            if (IsBrushInTexturePanel(GetDevice().GetMouseX(), GetDevice().GetMouseY()))
            //            {
            //                GlobalShiftClickVersion++;
            //                bool Checked = GlobalShiftClickVersion % 2 == 0;
            //                Dentisy = Checked ? -InitDentisy : InitDentisy;
            //                Color C = Checked ? Color.EDITOR_UI_TEST_COLOR_RED : Color.EDITOR_UI_TEST_COLOR_GREEN;
            //                _TextureContainerPanel.SetBorderColor(C);
            //                _BrushPannel.SetBackgroundColor(C);
            //                _CheckErasing.SetChecked(Checked);
            //            }
            //        }
            //        else if (Key == Key.S && keyAction == KeyAction.Down && GetDevice().IsControlDownOnly())
            //        {
            //            if (_Dirty)
            //            {
            //                _EcotopeMap.Save(_EcotopeMap.GetFilePath());
            //                _Dirty = false;
            //            }
            //        }
            //    }
            //};

            base.Initialize("Ecotope", _GlobalPannel);

            GetDockingCard().KeyUpEvent += (Control Sender, Key Key, ref bool bContinue) =>
            {
                if (GetDockingCard().GetFocused())
                {
                    if (Key == Key.Shift)
                    {
                        if (IsBrushInTexturePanel(GetDevice().GetMouseX(), GetDevice().GetMouseY()))
                        {
                            GlobalShiftClickVersion++;
                            bool Checked = GlobalShiftClickVersion % 2 == 0;
                            Dentisy = Checked ? -InitDentisy : InitDentisy;
                            Color C = Checked ? Color.EDITOR_UI_TEST_COLOR_RED : Color.EDITOR_UI_TEST_COLOR_GREEN;
                            _TextureContainerPanel.SetBorderColor(C);
                            _BrushPannel.SetBackgroundColor(C);
                            _CheckErasing.SetChecked(Checked);
                        }
                    }
                    else if (Key == Key.S && GetDevice().IsControlDownOnly())
                    {
                        if (_Dirty)
                        {
                            _EcotopeMap.Save(_EcotopeMap.GetFilePath());
                            _Dirty = false;
                        }
                    }
                }
            };

            return true;
        }

        protected override void OnEnter()
        {
            base.OnEnter();
            _TextureContainerPanel.MakeCenter();
        }

        public override void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            if (_Dirty)
            {
                CommonDialogUI CommonDialogUI = new CommonDialogUI();
                CommonDialogUI.Initialize(GetUIManager(), "Tips", "Do you want to save changes?", CommonDialogType.YesNoCancel);
                CommonDialogUI.CloseEvent += (CommonDialogUI Sender1, CommonDialogResult Result) =>
                {
                    if (Result == CommonDialogResult.Cancel)
                        return;

                    if (Result == CommonDialogResult.Yes)
                        _EcotopeMap.Save(_EcotopeMap.GetFilePath());

                    _Dirty = false;
                    GetDockingCard().CloseCard();
                };
                DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
                bNotToClose = true;
            }
            else
            {
                base.OnClose(Sender, ref bNotToClose);
            }
        }

        void UpdateTexturePanelLayout(int CenterX, int CenterY, int W, int H)
        {
            int PW = _TextureContainerPanel.GetParent().GetWidth();
            int PH = _TextureContainerPanel.GetParent().GetHeight();
            int X = Math.Clamp(CenterX, 0, PW) - W / 2;
            int Y = Math.Clamp(CenterY, 0, PH) - H / 2;
            _TextureContainerPanel.SetPosition(X, Y, W, H);
            _TextureContainerPanel.GetChild(0).SetSize(W - 2, H - 2);
        }

        bool IsBrushInTexturePanel(int X, int Y)
        {
            return UIManager.RectInRect(X - _BrushPannel.GetWidth() / 2, Y - _BrushPannel.GetHeight() / 2, _BrushPannel.GetWidth(), _BrushPannel.GetHeight(),
            _TextureContainerPanel.GetScreenX(), _TextureContainerPanel.GetScreenY(), _TextureContainerPanel.GetWidth(), _TextureContainerPanel.GetHeight());
        }
    }
}