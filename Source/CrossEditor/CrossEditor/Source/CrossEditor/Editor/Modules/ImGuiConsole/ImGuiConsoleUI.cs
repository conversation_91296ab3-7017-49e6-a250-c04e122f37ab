using Clicross;
using Editor<PERSON>;

namespace CrossEditor
{
    internal class ImGuiConsoleUI : DockingUI
    {
        public class NativeListener : EditorImGuiCallback
        {
            public ImGuiConsoleUI Editor { get; set; }

            public override void OnSetCursor(int cursor)
            {
                switch (cursor)
                {
                    case 0: Editor.mImGuiPanel.Cursor = SystemCursor.Arrow; break;
                    case 1: Editor.mImGuiPanel.Cursor = SystemCursor.Edit; break;
                    case 2: Editor.mImGuiPanel.Cursor = SystemCursor.Dragging; break;
                    case 3: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNS; break;
                    case 4: Editor.mImGuiPanel.Cursor = SystemCursor.SizeWE; break;
                    case 5: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNESW; break;
                    case 6: Editor.mImGuiPanel.Cursor = SystemCursor.SizeNWSE; break;
                    case 7: Editor.mImGuiPanel.Cursor = SystemCursor.Hand; break;
                    default: break;
                }
            }

            public override void OnSetInputScreenPosition(int x, int y)
            {
                Editor.GetUIManager().GetDevice().SetCaret(true, x + Editor.mImGuiPanel.Panel.GetScreenX(), y + Editor.mImGuiPanel.Panel.GetScreenY(), 1, 1);
            }
        }

        protected static ImGuiConsoleUI mInstance = new ImGuiConsoleUI();

        protected ImGuiConsoleContext mContext;
        protected ImGuiPanel mImGuiPanel;
        protected NativeListener mNativeListener;

        public ImGuiConsoleUI()
        {
            mNativeListener = new NativeListener { Editor = this };
            mContext = new ImGuiConsoleContext(mNativeListener);
            mImGuiPanel = new ImGuiPanel(mContext);
        }

        public static ImGuiConsoleUI GetInstance()
        {
            return mInstance;
        }

        public void Initialize()
        {
            base.Initialize("ImGui Console", mImGuiPanel.Panel);
        }

        protected override void OnEnter()
        {
            base.OnEnter();
            mContext.OnActivate(true);
        }

        protected override void OnLeave()
        {
            base.OnLeave();
            mContext.OnActivate(false);
        }
    }
}
