using CEngine;
using EditorUI;

namespace CrossEditor
{
    internal class Inspector_ParticleSystemResource : Inspector_Struct_With_Property
    {
        protected ParticleSystemResource _ParticleSystem;
        protected Panel _Panel;
        protected Label _LabelName;

        public Inspector_ParticleSystemResource()
        { }

        public override void InspectObject(object Object, object Tag)
        {
            _ParticleSystem = Object as ParticleSystemResource;

            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.SetEnable(true);
            _Panel.SetTagString1(_ParticleSystem.Path);
            ThumbnailHelper.GetInstance().EnableThumbnail(_Panel);
            _SelfContainer.AddChild(_Panel);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetFontSize(16);
            _LabelName.SetText(PathHelper.GetNameOfPath(_ParticleSystem.Path) + "(ParticleSystem)");
            _SelfContainer.AddChild(_LabelName);

            base.InspectObject(_ParticleSystem.SystemInfo, null);
        }

        private void RefreshIcon()
        {
            _Panel.SetImage(null);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _LabelName.SetPosition(SPAN_X, Height, _LabelName.CalculateTextWidth(), 16);
            Height += 18;

            if (_Panel != null)
            {
                Height += 65;
                _Panel.SetPosition(SPAN_X, Height, 64, 64);
                _SelfContainer.SetPosition(0, Y, Width, Height);
            }

            Y += Height;
            base.UpdateLayout(Width, ref Y);
        }

        public override void ModifyButtonClickedEvent(Button Sender)
        {
            GetUIManager().SetFocusControl(null);
            CEResource.UpdateParticleSystem(_ParticleSystem.ResourcePtr.GetPointer(), _ParticleSystem.SystemInfo);
            _ParticleSystem.Save();
            _ParticleSystem.Reload();
        }
    }
}