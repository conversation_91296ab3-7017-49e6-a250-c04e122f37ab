using EditorUI;

namespace CrossEditor
{
    public delegate void EnableChangedDelegate(bool Checked);

    public class ParticleModuleItem
    {
        public const int StringMarginX = 15;

        public event EnableChangedDelegate OnEnableChanged;

        private int _Index = -1;
        public int Index { get => _Index; }

        private string _Name;
        public string Name { get => _Name; }

        private int _Width;
        public int Width { get => _Width; }

        private int _Height;
        public int Height { get => _Height; }

        private ParticleEmitterNode _Parent = null;
        public ParticleEmitterNode Parent { get => _Parent; }

        private bool _Checked = true;
        public bool Checked { get => _Checked; set => _Checked = value; }

        private bool _Highlight = false;
        public bool Highlight { set => _Highlight = value; get => _Highlight; }

        private string _Tag = null;
        public string Tag { get => _Tag; }

        private Color _TagColor;

        public ParticleModuleItem(int Index, string Name, int Width, int Height, string Tag)
        {
            _Index = Index;
            _Name = Name;
            _Width = Width;
            _Height = Height;
            _Tag = Tag;

            switch (Tag)
            {
                case "EmitterUpdate":
                    _TagColor = Color.FromRGBA(255, 165, 2, 255);
                    break;

                case "ParticleSpawn":
                    _TagColor = Color.FromRGBA(196, 229, 56, 255);
                    break;

                case "ParticleUpdate":
                    _TagColor = Color.FromRGBA(56, 196, 229, 255);
                    break;

                case "ParticleRenderer":
                    _TagColor = Color.FromRGBA(190, 135, 229, 255);
                    break;

                default:
                    _TagColor = Color.FromRGBA(125, 125, 125, 255);
                    break;
            }
        }

        public void SetParent(ParticleEmitterNode Parent)
        {
            _Parent = Parent;
        }

        public void Draw(UIManager UIManager, int X, int Y, int W, int H)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Color StringColor = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, Name, StringColor, X + StringMarginX, Y, W, H, TextAlign.CenterLeft);

            int AnchorOffset = (int)(Height * 0.5);

            int ItemX = Parent.X + 2;
            int ItemY = GetPositionY();

            // Draw Backgroud
            Color ItemBgColor = Highlight ? Color.FromRGBA(126, 214, 223, 80) : Color.FromRGBA(200, 200, 200, 50);
            GraphicsHelper.FillRectangle(UIManager, ItemBgColor, ItemX, ItemY, Width, Height);

            // Draw Tag
            GraphicsHelper.FillRectangle(UIManager, _TagColor, ItemX, ItemY, 10, Height);

            // Draw Enable's checkbox
            Color CheckboxBgColor = Color.FromRGBA(0, 0, 0, 255);
            GraphicsHelper.FillRectangle(UIManager, CheckboxBgColor, ItemX + Width - 20, ItemY + 1, 18, 18);
            if (Checked)
            {
                Color CheckboxFgColor = Color.FromRGBA(250, 200, 36, 155);
                GraphicsHelper.FillRectangle(UIManager, CheckboxFgColor, ItemX + Width - 18, ItemY + 3, 14, 14);
            }
        }

        public bool Hitted(int WorldX, int WorldY)
        {
            if (UIManager.PointInRect(WorldX, WorldY, Parent.X, GetPositionY(), Width, Height))
            {
                if (UIManager.PointInRect(WorldX, WorldY, Parent.X + Width - 20, GetPositionY(), 20, 20))
                {
                    Checked = !Checked;
                    OnEnableChanged(Checked);
                    return false;
                }
                else
                {
                    return true;
                }
            }
            return false;
        }

        private int GetPositionY()
        {
            if (Parent == null)
            {
                return 0;
            }

            int AnchorOffset = (int)(Height * 0.5);
            int Ret = Parent.Y + Index * (Height + ParticleEmitterNode.ModuleItemMarginY) + AnchorOffset;
            return Ret;
        }
    }
}