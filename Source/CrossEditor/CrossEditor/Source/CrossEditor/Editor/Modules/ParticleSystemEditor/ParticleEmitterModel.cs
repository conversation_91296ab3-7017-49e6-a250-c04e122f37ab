using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class ParticleEmitterModel
    {
        public string Name;

        public List<ParticleEmitterNode> Nodes;

        public GraphCamera2D Camera;

        private ParticleSystemResource _SystemResource;

        public ParticleSystemResource SystemResource
        {
            set { _SystemResource = value; }
            get { return _SystemResource; }
        }

        public ParticleEmitterModel()
        {
            Name = "";
            Nodes = new List<ParticleEmitterNode>();
            Camera = new GraphCamera2D();
        }

        public virtual void Draw(UIManager UIManager)
        {
            foreach (ParticleEmitterNode Node in Nodes)
            {
                Node.DoLayout();
                Node.Draw(UIManager);
            }
        }

        public void ReloadEmitters(ParticleSystemResource SystemResource)
        {
            if (SystemResource == null || SystemResource.SystemInfo == null)
            {
                return;
            }
            Nodes.Clear();
            _SystemResource = SystemResource;

            int WorldX = 0, WorldY = 0;
            int NewEmitterIndex = 1;

            var EmitterInfos = SystemResource.SystemInfo.EmitterInfos;
            for (int EmitterIndex = 0; EmitterIndex < EmitterInfos.Count; EmitterIndex++)
            {
                var EmitterInfo = EmitterInfos[EmitterIndex];
                if (WorldX == EmitterInfo.WorldX)
                {
                    WorldX += 100;
                }
                WorldY = EmitterInfo.WorldY;
                EmitterInfo.EmitterName = EmitterInfo.EmitterName.Length > 0 ? EmitterInfo.EmitterName : string.Format("Emitter_{0}", NewEmitterIndex++);
                ParticleEmitterNode Node = new ParticleEmitterNode(EmitterIndex, EmitterInfos[EmitterIndex])
                {
                    X = WorldX,
                    Y = WorldY
                };
                Node.Initialize();
                AddNode(Node);

                WorldX += (Node.ContentWidth + 100);
            }

            MoveCameraToTarget(Nodes.Count > 0 ? Nodes[0] : null);
        }

        public void MoveCameraToTarget(ParticleEmitterNode Node)
        {
            if (Node == null)
            {
                return;
            }

            int X = 0, Y = 0, Width = 0, Height = 0;
            GetNodesBlock(new List<ParticleEmitterNode> { Node }, ref X, ref Y, ref Width, ref Height);

            Camera.WorldX = X + Width / 2 - Camera.WorldHeight * Camera.AspectRatio / 2;
            Camera.WorldY = Y + Height / 2 - Camera.WorldHeight / 2;
        }

        public void GetNodesBlock(List<ParticleEmitterNode> Nodes, ref int X, ref int Y, ref int Width, ref int Height)
        {
            foreach (ParticleEmitterNode Node in Nodes)
            {
                Node.DoLayout();
            }

            int XMin = int.MaxValue, XMax = int.MinValue, YMin = int.MaxValue, YMax = int.MinValue;
            foreach (ParticleEmitterNode Node in Nodes)
            {
                XMin = Math.Min(Node.X, XMin);
                XMax = Math.Max(Node.X + Node.Width, XMax);
                YMin = Math.Min(Node.Y, YMin);
                YMax = Math.Max(Node.Y + Node.Height, YMax);
            }
            X = XMin;
            Y = YMin;
            Width = XMax - XMin;
            Height = YMax - YMin;
        }

        public virtual object HitTest(int WorldX, int WorldY)
        {
            for (int i = Nodes.Count - 1; i >= 0; --i)
            {
                ParticleEmitterNode Node = Nodes[i];
                object HitObject = Node.HitTest(WorldX, WorldY);

                if (HitObject != null)
                {
                    return HitObject;
                }
            }

            return null;
        }

        public void AddNode(ParticleEmitterNode Node)
        {
            if (Nodes.Contains(Node))
            {
                return;
            }

            Nodes.Add(Node);
        }
    }
}