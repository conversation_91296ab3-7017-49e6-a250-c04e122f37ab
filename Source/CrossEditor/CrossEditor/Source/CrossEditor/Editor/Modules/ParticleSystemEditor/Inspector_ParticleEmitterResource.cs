using CEngine;
using EditorUI;
using System.Reflection;

namespace CrossEditor
{
    internal class Inspector_ParticleEmitterResource : Inspector_Struct_With_Property
    {
        protected Button _ApplyButton;
        protected Label _LabelName;
        protected Panel _Panel;
        protected ParticleEmitterResource _ParticleEmitter;
        public Inspector_ParticleEmitterResource()
        { }

        public override void InspectObject(object Object, object Tag)
        {
            _ParticleEmitter = Object as ParticleEmitterResource;
            if (_ParticleEmitter == null)
            {
                return;
            }

            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.SetEnable(true);
            _Panel.SetTagString1(_ParticleEmitter.Path);
            ThumbnailHelper.GetInstance().EnableThumbnail(_Panel);
            _SelfContainer.AddChild(_Panel);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetFontSize(16);
            _LabelName.SetText(_ParticleEmitter.Path);
            _SelfContainer.AddChild(_LabelName);

            _ApplyButton = new Button();
            _ApplyButton.Initialize();
            _ApplyButton.SetText("Save Emitter");
            _ApplyButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ApplyButton.SetFontSize(18);
            _ApplyButton.SetTextOffsetY(2);
            _ApplyButton.SetToolTips("Save emitter resource");
            _ApplyButton.ClickedEvent += ModifyButtonClickedEvent;
            _ApplyButton.SetVisible(_ParticleEmitter.ModuleIndex == (int)EmitterResourceMode.EMITTER_SINGLE);
            _ChildContainer.AddChild(_ApplyButton);

            if (_ParticleEmitter.ModuleIndex < 0)
            {
                FilterEmitter();
                base.InspectObject(_ParticleEmitter.EmitterInfo, null);
            }
            else
            {
                PropertyInfo[] Properties = typeof(ParticleEmitterInfo).GetProperties();
                object Module = Properties[_ParticleEmitter.ModuleIndex].GetValue(_ParticleEmitter.EmitterInfo);
                FilterModuleProp(Module);
                base.InspectObject(Module, null);
            }
        }

        public override void ModifyButtonClickedEvent(Button Sender)
        {
            CEResource.UpdateParticleEmitter(_ParticleEmitter.ResourcePtr.GetPointer(), _ParticleEmitter.EmitterInfo);
            _ParticleEmitter.Save();
            _ParticleEmitter.Reload();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _LabelName.SetPosition(SPAN_X, Height, _LabelName.CalculateTextWidth(), 16);
            Height += 18;

            if (_Panel != null)
            {
                Height += 65;
                _Panel.SetPosition(SPAN_X, Height, 64, 64);
                _SelfContainer.SetPosition(0, Y, Width, Height);
            }

            Y += Height;
            base.UpdateLayout(Width, ref Y);

            int Y1 = _ChildContainer.GetHeight();
            if (_ApplyButton != null)
            {
                _ApplyButton.SetPosition(SPAN_X, Y1 + 5, Width - SPAN_X * 2, 20);
                Y1 += 20 + 5;
                _ChildContainer.SetHeight(Y1);
                Y += 20 + 5;
            }
        }

        private void FilterEmitter()
        {
            var isGPU = _ParticleEmitter.EmitterInfo.Simulation == SimulationType.GPU;

            //Not to modify SimulationPath in the editor
            SetPropertyVisible(_ParticleEmitter.EmitterInfo.GetType().GetProperty("SimulationPath"), false);
            SetPropertyVisible(_ParticleEmitter.EmitterInfo.GetType().GetProperty("SimulationParameters"), isGPU);
            SetPropertyVisible(_ParticleEmitter.EmitterInfo.GetType().GetProperty("OverrideDashboard"), isGPU);
            SetPropertyVisible(_ParticleEmitter.EmitterInfo.GetType().GetProperty("OverrideInitStage"), isGPU);
            SetPropertyVisible(_ParticleEmitter.EmitterInfo.GetType().GetProperty("OverrideUpdateStage"), isGPU);
        }

        private void FilterModuleProp(object Module)
        {
            if (Module.GetType() == typeof(ParticleInitInfo))
            {
                FilterParticleInitInfo(Module);
            }
            else if (Module.GetType() == typeof(LocationShapeInfo))
            {
                FilterParticleShapeInfo(Module);
            }
        }

        private void FilterParticleInitInfo(object Module)
        {
            var RendererType = _ParticleEmitter.EmitterInfo.RendererType;
            var EnableOriginVelocity = _ParticleEmitter.EmitterInfo.ParticleInit.EnableOriginVelocity;
            if (EnableOriginVelocity)
                SetPropertyVisible(Module.GetType().GetProperty("OriginVelocity"), true);
            else
                SetPropertyVisible(Module.GetType().GetProperty("OriginVelocity"), false);

            if (RendererType == ParticleRendererType.Mesh)
            {
                SetPropertyVisible(Module.GetType().GetProperty("SpriteRotation"), false);
                SetPropertyVisible(Module.GetType().GetProperty("SpriteSizeX"), false);
                SetPropertyVisible(Module.GetType().GetProperty("SpriteSizeY"), false);
                SetPropertyVisible(Module.GetType().GetProperty("MeshSizeX"), true);
                SetPropertyVisible(Module.GetType().GetProperty("MeshSizeY"), true);
                SetPropertyVisible(Module.GetType().GetProperty("MeshSizeZ"), true);
                SetPropertyVisible(Module.GetType().GetProperty("MeshRotationX"), true);
                SetPropertyVisible(Module.GetType().GetProperty("MeshRotationY"), true);
                SetPropertyVisible(Module.GetType().GetProperty("MeshRotationZ"), true);
            }
            else if (RendererType == ParticleRendererType.Sprite)
            {
                SetPropertyVisible(Module.GetType().GetProperty("SpriteRotation"), true);
                SetPropertyVisible(Module.GetType().GetProperty("SpriteSizeX"), true);
                SetPropertyVisible(Module.GetType().GetProperty("SpriteSizeY"), true);
                SetPropertyVisible(Module.GetType().GetProperty("MeshSizeX"), false);
                SetPropertyVisible(Module.GetType().GetProperty("MeshSizeY"), false);
                SetPropertyVisible(Module.GetType().GetProperty("MeshSizeZ"), false);
                SetPropertyVisible(Module.GetType().GetProperty("MeshRotationX"), false);
                SetPropertyVisible(Module.GetType().GetProperty("MeshRotationY"), false);
                SetPropertyVisible(Module.GetType().GetProperty("MeshRotationZ"), false);
            }
        }

        private void FilterParticleShapeInfo(object Module)
        {
            var LocationShape = _ParticleEmitter.EmitterInfo.LocationShape;
            SetPropertyVisible(Module.GetType().GetProperty("DistributionType"), false);
            SetPropertyVisible(Module.GetType().GetProperty("UDistribution"), false);
            SetPropertyVisible(Module.GetType().GetProperty("DiskCoverage"), false);
            SetPropertyVisible(Module.GetType().GetProperty("UniformSpiralAmount"), false);
            SetPropertyVisible(Module.GetType().GetProperty("UniformSpiralFalloff"), false);
            SetPropertyVisible(Module.GetType().GetProperty("InnerAngle"), false);
            SetPropertyVisible(Module.GetType().GetProperty("Axis"), false);
            switch (LocationShape.ShapeType)
            {
                case LocationShapeType.Box:
                    SetPropertyVisible(Module.GetType().GetProperty("Box"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("Radius"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Angle"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Arc"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Length"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("ModelPath"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("LodIndex"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitType"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitFrom"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("MoveState"), true);
                    break;

                case LocationShapeType.Sphere:
                    SetPropertyVisible(Module.GetType().GetProperty("Box"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Radius"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("Angle"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Arc"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Length"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("ModelPath"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("LodIndex"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitType"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitFrom"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("MoveState"), false);
                    break;

                case LocationShapeType.Cone:
                    SetPropertyVisible(Module.GetType().GetProperty("Box"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Radius"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("Angle"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("Arc"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("Length"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("ModelPath"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("LodIndex"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitType"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitFrom"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("MoveState"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("InnerAngle"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("Axis"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("UDistribution"), true);
                    break;

                case LocationShapeType.Circle:
                    SetPropertyVisible(Module.GetType().GetProperty("Box"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Radius"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("Angle"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Arc"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("Length"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("ModelPath"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("LodIndex"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitType"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitFrom"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("MoveState"), false);
                    //GPU
                    SetPropertyVisible(Module.GetType().GetProperty("DistributionType"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("UDistribution"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("DiskCoverage"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("UniformSpiralAmount"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("UniformSpiralFalloff"), false);

                    var distributionType = _ParticleEmitter.EmitterInfo.LocationShape.DistributionType;
                    if (distributionType == EmitDistributionType.Random)
                    {
                        SetPropertyVisible(Module.GetType().GetProperty("UDistribution"), true);
                        SetPropertyVisible(Module.GetType().GetProperty("DiskCoverage"), true);
                    }
                    else if (distributionType == EmitDistributionType.Uniform)
                    {
                        SetPropertyVisible(Module.GetType().GetProperty("UniformSpiralAmount"), true);
                        SetPropertyVisible(Module.GetType().GetProperty("UniformSpiralFalloff"), true);
                    }
                    break;

                case LocationShapeType.MeshSurface:
                    SetPropertyVisible(Module.GetType().GetProperty("Box"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Radius"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Angle"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Arc"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("Length"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitFrom"), false);
                    SetPropertyVisible(Module.GetType().GetProperty("ModelPath"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("LodIndex"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("EmitType"), true);
                    SetPropertyVisible(Module.GetType().GetProperty("MoveState"), false);
                    break;

                default:
                    break;
            }
        }

        private void SetPropertyVisible(PropertyInfo Prop, bool Visible)
        {
            if (Prop == null)
            {
                return;
            }
            AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(Prop);
            if (AttributeList != null)
            {
                AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();
                AttributeData.NamedAttributes["bHide"] = !Visible;
            }
        }
    }
}