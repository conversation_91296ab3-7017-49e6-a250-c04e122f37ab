#pragma once

#include <cstdint>
#include <optional>
#include "async_task/jobs/jobs_define.hpp"
#include "async_task/async_task_export.hpp"
#include "reflection/objects/value.hpp"

namespace gbf::coro {

class coro_service;
class coro_service_manager;
class iasync_task;
class async_task17;
using async_task_ptr = std::shared_ptr<iasync_task>;
using async_task_weak_ptr = std::weak_ptr<iasync_task>;

enum class CoroImplementType {
  kUnknown = 0,
  kCxx17 = 1,
  kCxx20 = 2,
};

enum class AwaitMode {
  kAwaitUnknown = 0,
  kAwaitNever = 1,  // Just send to immediate queue
  kAwaitNextframe = 2,
  kAwaitForNotifyNoTimeout = 3,    // Wait a notify with no timeout
  kAwaitForNotifyWithTimeout = 4,  // Wait a notify with timeout
  kAwaitDoNothing = 5,             // like kill command, so we not to add task back to queue anymore
};

enum class AwaitResult {
  kAwaitSuc = 0,      // Await suc
  kAwaitFailded = 1,  // Failed by request result
  kAwaitTimeout = 2,  // Failed by timeout
};

enum CoroRunningState {
  kRunning = 1,  // bigger than 0 means running
  kBegin = 0,
  kEndOk = -1,      // end by normal
  kEndFailed = -2,  // end by exception or other errors
  kTerminate = -3,  // end by manual terminate
};

using coro_defer_function = std::function<void()>;

template <class ResultType>
using coro_return_function = std::function<void(CoroRunningState, const std::optional<ResultType>&)>;

using coro_return_void_function = std::function<void(CoroRunningState)>;


using JobType = ::gbf::JobType;

}  // namespace gbf::coro
