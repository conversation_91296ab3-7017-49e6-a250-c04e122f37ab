#include "async_task/tasks/default_awaitable20.hpp"

#if defined(GBF_ENABLE_CPP20)
#include "async_task/tasks/coro_service_manager.hpp"
#include "core/core_global.hpp"

namespace gbf::coro::tasks {

//-------------------------------------------------------------------------------------
void next_frame::await_suspend(std::coroutine_handle<>) const noexcept { co_query_self()->await_setting(AwaitMode::kAwaitNextframe); }
//-------------------------------------------------------------------------------------

bool sleep::await_ready() { return false; }
void sleep::await_suspend(std::coroutine_handle<>) const noexcept {
  co_query_self()->await_setting(AwaitMode::kAwaitForNotifyWithTimeout, timeout_ms_);
}
//////-----------------------------------------------------------------------------------------------
////void create_task::await_suspend(coroutine_handle<>) const noexcept {
////  // Do real task create here
////  create_task_func_();
////  co_query_self()->await_setting(AwaitMode::kAwaitNever);
////}
//////-------------------------------------------------------------------------------------
////void wait_task_finish::await_suspend(std::coroutine_handle<>) const noexcept {
////  auto* task = co_query_self();
////  auto& manager = task->manager();
////  if (manager.request_wait_for_task_quit(task, task->work_job_type(), wait_task_id_, AwaitMode::kAwaitForNotifyWithTimeout, timeout_ms_)) {
////    task->await_setting(AwaitMode::kAwaitForNotifyWithTimeout, timeout_ms_);
////  } else {
////    task->await_setting(AwaitMode::kAwaitNextframe);
////  }
////}

void transfer::await_suspend(coroutine_handle<>) const noexcept {
  auto* task = co_query_self();
  co_query_manager()->request_task_transfer(task, task->work_job_type(), target_job_type_);
  co_query_self()->await_setting(AwaitMode::kAwaitForNotifyNoTimeout);
}

}  // namespace gbf::coro::awaitables

#endif
