#pragma once

//-----------------------------------------------------------------------------------------------
// c++17 coroutine macros
//-----------------------------------------------------------------------------------------------
#define co_query_self() ((gbf::coro::iasync_task*)gbf::coro::iasync_task::this_thread_task())
#define co_query_self17() ((gbf::coro::async_task17*)co_query_self())
#define co_query_manager() (&(co_query_self()->manager()))
#define co_query_job_type() (co_query_self()->work_job_type())
#define co_query_job_type_name() (gbf::jobs::get_job_type_name(co_query_job_type()).data())

////#define CORO_UNIQUE_VAR_NAME_IMPL(prefix, suffix) prefix##suffix
////#define CORO_UNIQUE_VAR_NAME(prefix, suffix) CORO_UNIQUE_VAR_NAME_IMPL(prefix, suffix)

////#define __co_await(...)                                             \
////  co_query_self17()->do_awaitable_suspend(__VA_ARGS__);          \
////  __co_yield()


#ifdef __linux__
#define __co_await(...)                                          \
  do {                                                           \
    gbf::coro::current_promise()->set_state(__COUNTER__ + 1); \
    co_query_self17()->do_awaitable_suspend(__VA_ARGS__);        \
    return nullptr;                                              \
    case __COUNTER__:;                                           \
  } while (0)
#else
#define __co_await(...)                          \
  do {                                                    \
    gbf::coro::current_promise()->set_state(__LINE__); \
    co_query_self17()->do_awaitable_suspend(__VA_ARGS__); \
    return nullptr;                                       \
    case __LINE__:;                                       \
  } while (0)
#endif



#define co_query_value(ResumeObjectType) co_query_self()->get_resume_object_as<ResumeObjectType>()

#define __co_await_using_begin(...)                                 \
  __co_await(__VA_ARGS__);                                          \
  {                                                                 \
  using __co_resumming_type = decltype(__VA_ARGS__)::resume_type;

#define __co_using_value() co_query_value(__co_resumming_type)
  
#define __co_await_using_end()                                      \
  }

////#define __co_let_value(NAME, VAL) 

#define co_last_suc() co_query_self()->last_invoke_ok()

////#define rco_emit_finish_event(ReturnValue) \
////  rco_self_task()->DoReturn(ReturnValue);  \
////  rco_return

#define __co_return(ReturnValue)     \
  co_query_self()->bind_return_as(ReturnValue); \
  __co_return_internel

#define __co_return_void()            \
  co_query_self()->bind_return_void(); \
  __co_return_internel


//-----------------------------------------------------------------------------------------------
// c++20 coroutine macros
//-----------------------------------------------------------------------------------------------
#if GBF_ENABLE_CPP20

////#define co_create_task(JOB_TYPE, FUNC) co_query_manager()->create_task20(JOB_TYPE, FUNC) 
////#define co_create_logic_task(FUNC) co_create_task(gbf::JobSlotType::kLogicJob, FUNC) 
////#define co_create_work_task(FUNC) co_create_task(gbf::JobSlotType::kWorkJob, FUNC) 
////#define co_create_slow_task(FUNC) co_create_task(gbf::JobSlotType::kSlowJob, FUNC) 
////#define co_create_network_task(FUNC) co_create_task(gbf::JobSlotType::kNetworkJob, FUNC) 

#endif


