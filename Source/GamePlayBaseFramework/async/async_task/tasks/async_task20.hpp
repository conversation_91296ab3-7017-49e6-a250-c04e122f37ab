#pragma once

#include <unordered_set>

#include "async_task/tasks/coroutines/cotask20.hpp"
#include "async_task/tasks/iasync_task.hpp"
#include "async_task/tasks/tasks_define.hpp"

#if defined(GBF_ENABLE_CPP20)
#ifndef _MANAGED
namespace gbf::coro {

template <class ReturnType>
class async_task20 : public iasync_task {
 public:
  async_task20(uint64_t taskId, cotask20<ReturnType>::CoTaskFunctionType&& taskFunc, const cotask20<ReturnType>& coro_task,
              coro_service_manager* manager)
      : iasync_task(taskId, *manager), task_function_(std::move(taskFunc)), co_resuming_task_(coro_task) {
    implement_type_ = CoroImplementType::kCxx20;
    //// co_resuming_task_ = task_function_();
    if constexpr (!std::is_same_v<void, ReturnType>) {
      co_resuming_task_.bind_return_function([this](ReturnType&& val) { this->bind_return_as(std::move(val)); });
    } else {
      co_resuming_task_.bind_return_function([this]() { this->bind_return_void(); });
    }
    co_resuming_task_.set_bind_task(this);
  }

  ~async_task20() { co_resuming_task_.destroy(); }

  CoroRunningState resume_impl(JobType job_type) override {
    // Just clear system call before resume
    await_setting(AwaitMode::kAwaitUnknown, 0);

    co_resuming_task_.resume();

    clear_resume_object();  // Clear resume object here

    if (co_resuming_task_.done()) {
      return CoroRunningState::kEndOk;
    } else {
      return CoroRunningState::kRunning;
    }
  }

  uint64_t get_return_type_id() const override { return co_resuming_task_.co_handle_.promise().m_return_type_id; }

  std::string_view get_return_type_name() const override { return co_resuming_task_.co_handle_.promise().m_return_type_name; }

  const cotask20<ReturnType>& get_resuming_task() const { return co_resuming_task_; }

 protected:
  cotask20<ReturnType> co_resuming_task_;
  cotask20<ReturnType>::CoTaskFunctionType task_function_;
};

}  // namespace gbf::coro
#endif
#endif
