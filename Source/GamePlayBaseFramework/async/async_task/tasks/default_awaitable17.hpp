#pragma once

#include <string_view>

#include "async_task/tasks/async_task17.hpp"
#include "async_task/tasks/coro_service_manager.hpp"
#include "async_task/tasks/coroutines/cotask17.hpp"
#include "async_task/tasks/coroutines/impl17/new_co_task.hpp"
#include "async_task/tasks/iawaitable17.hpp"
#include "async_task/tasks/resume_object.hpp"
#include "async_task/tasks/tasks_define.hpp"
#include "reflection/objects/args.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/objects/value.hpp"

namespace gbf::coro::tasks17 {

//-------------------------------------------------------------------------------------
class ASYNC_TASKS_API next_frame : public iawaitable17 {
 public:
  void invoke_suspend(async_task17* task, coro_service_manager* manager) override;
};
//-------------------------------------------------------------------------------------
class ASYNC_TASKS_API sleep : public iawaitable17 {
 public:
  sleep(int sleepTimeMs) { timeout_ms_ = sleepTimeMs; }
  ~sleep() {}

  void invoke_suspend(async_task17* task, coro_service_manager* manager) override;

 protected:
  int timeout_ms_ = 0;
};
//-------------------------------------------------------------------------------------
class ASYNC_TASKS_API create_task : public iawaitable17 {
 public:
  using resume_type = create_task_resume_object;

 public:
  // CreateTaskSysCall(co_task::CoroutineHandle handle, bool isChild): mHandle(handle), mIsChild(isChild){}

  template <class Func, class... Args>
  create_task(JobType job_type, bool isChild, Func f, Args&&... args) : is_child_(isChild) {
    auto nativeTask = coro::co_start(f, std::forward<Args>(args)...);

    task_create_func_ = [jtype = job_type, nativeTask = std::move(nativeTask), this](async_task17* task, coro_service_manager* manager) mutable {
      uint64_t new_task_id = manager->create_task17_impl(jtype, std::move(nativeTask));
      ////IAsyncTask* atask = manager->GetSchedTaskFromCoroutineTask(task);
      if (is_child_) {
        task->queue_for_add_child_task(new_task_id);
      }

      create_task_resume_object ev;
      ev.task_id = task->task_id();
      ev.new_task_id = new_task_id;
      task->queue_for_bind_resume_value_as(std::move(ev));

      task->await_setting(AwaitMode::kAwaitNever);
    };
  }

  ~create_task() {}

  //-------------------------------------------------------------------------------------
  void invoke_suspend(async_task17* task, coro_service_manager* manager) override { task_create_func_(task, manager); }

 protected:
  std::function<void(async_task17*, coro_service_manager*)> task_create_func_;
  bool is_child_;
};
//-------------------------------------------------------------------------------------
class ASYNC_TASKS_API wait_task_finish : public iawaitable17 {
 public:
  wait_task_finish(uint64_t tid, uint64_t timeoutMs) : wait_task_id_(tid), timeout_ms_(timeoutMs) {}
  ~wait_task_finish() {}

  void invoke_suspend(async_task17* task, coro_service_manager* manager) override;

 protected:
  uint64_t wait_task_id_;
  uint64_t timeout_ms_;
};
//-----------------------------------------------------------------------------------------------
////template <typename Func, typename CoTask17Type = reflection::detail::TFunctionTraits<Func>::ExposedType,
////          typename CoReturnType = cotask17_type_traits<CoTask17Type>::co_return_type,
////          typename U = std::enable_if_t<cotask17_type_traits<CoTask17Type>::value> >
////class spawn_task {
//// public:
////  spawn_task() = delete;
////  ~spawn_task() = default;
////  spawn_task(JobType job_type, Func&& task_func) {
////    ret_type = co_query_manager()->create_task17(job_type, std::move(task_func));
////    // add as child task
////    co_query_self()->queue_for_add_child_task(ret_type.get_bind_task()->task_id());
////  }
////
////  auto operator co_await() { return tasks::cotask_awaitable<CoReturnType>(ret_type.get_bind_task()); }
////
//// private:
////  CoTask20Type ret_type;
////};
//-----------------------------------------------------------------------------------------------
class ASYNC_TASKS_API transfer : public iawaitable17 {
 public:
  transfer() = delete;
  transfer(JobType target_job_type): target_job_type_(target_job_type) {}
  ~transfer() = default;

  void invoke_suspend(async_task17* task, coro_service_manager* manager) override;

 protected:
  JobType target_job_type_ = JobType::kInvalidJob;
};

}  // namespace gbf::coro::tasks17


