#pragma once

#include "async_task/tasks/iasync_task.hpp"
#include "async_task/tasks/tasks_define.hpp"
#include "async_task/tasks/iawaitable17.hpp"
#include "async_task/tasks/coroutines/coro_util.hpp"

namespace gbf::coro::tasks17 {
//-----------------------------------------------------------------------------------------------
template <typename ResultType>
class cotask17_awaitable: public iawaitable17 {
 public:
   cotask17_awaitable(const iasync_task* wait_task) : waited_task_((const_cast<iasync_task*>(wait_task))->shared_from_this()) {}
  ~cotask17_awaitable() {
    if (is_bind_callback_) {
      waited_task_->queue_for_clear_return_callback();
      is_bind_callback_ = false;
    }
  }

  void invoke_suspend(async_task17* task, coro_service_manager* manager) override {
    auto* src_task = iasync_task::this_thread_task();

    if constexpr (std::is_same_v<ResultType, void>) {
      //No result type here
      coro_return_void_function func = [manager, awake_id = src_task->task_id(), this](CoroRunningState run_state) {
        is_bind_callback_ = false;
        coro_util::awake_one_task_in_manager_with_nothing(manager, awake_id);
      };
      waited_task_->queue_for_bind_return_callback<ResultType>(std::move(func));
    } else {
      //with result type here
      coro_return_function<ResultType> func = [manager, awake_id = src_task->task_id(), this](CoroRunningState run_state,
                                                                                              const std::optional<ResultType>& ret) {
        reflection::UserObject obj;
        if (ret.has_value()) {
          obj = reflection::make_user_object(ret.value());
        }

        is_bind_callback_ = false;
        coro_util::awake_one_task_in_manager_with_user_object(manager, awake_id, obj);
      };
      waited_task_->queue_for_bind_return_callback<ResultType>(std::move(func));
    }



    is_bind_callback_ = true;
    src_task->await_setting(AwaitMode::kAwaitForNotifyNoTimeout);
  }

  void invoke_resume(async_task17* task, coro_service_manager* manager) override {

  }

 private:
  async_task_ptr waited_task_;
  bool is_bind_callback_ = false;
};
//-----------------------------------------------------------------------------------------------

}  // namespace gbf::coro::tasks
