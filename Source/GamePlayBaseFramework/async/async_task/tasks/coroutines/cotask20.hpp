#pragma once

#if defined(GBF_ENABLE_CPP20)
#ifndef _MANAGED
#include "async_task/tasks/coroutines/coroutine20_define.hpp"

#include "core/export.hpp"
#include "core/utils/meta_type_hash.hpp"

#include "async_task/tasks/coroutines/cotask_awaitable20.hpp"

#include "reflection/traits/function_traits.hpp"

namespace gbf::coro {

template <class ReturnType = void>
struct cotask20 {
  template <class RetType>
  friend class async_task20;
  friend class coro_service_manager;

  using ReturnValueOrVoidType = typename return_value_or_void<ReturnType>::type;
  using ReturnValueSettingFunc = return_value_or_void<ReturnType>::ReturnValueSettingFunc;
  ////using ExceptionThrowFunc = std::function<void(std::exception_ptr)>;

  struct promise_type : return_value_or_void<ReturnType>::type {
    // Using a conditional here for protect void type~~

    static constexpr bool IsReturnVoid = std::is_same_v<void, ReturnType>;
    promise_type() {
      m_return_type_id = MetatypeHash::Hash<ReturnType>();
      m_return_type_name = MetatypeHash::NamePretty<ReturnType>();
    }

    cotask20<ReturnType> get_return_object() { return cotask20<ReturnType>(coroutine_handle<promise_type>::from_promise(*this)); }

    auto initial_suspend() { return suspend_always{}; }

    auto final_suspend() noexcept { return suspend_always{}; }

    void unhandled_exception() {
      // Just rethrow exception here, we will handle the exception in IAsyncTask::Resume()~~
      std::rethrow_exception(std::current_exception());
    }

    auto yield_value(nullptr_t) {
      return suspend_always{};  // need always yield in this case.
    }

    // This await transformation support wait for a CoTask20<> object, so we can co_await another coroutine for result
    ////template<class OtherCoroReturnType>
    ////auto await_transform(CoTask20<OtherCoroReturnType>&& _other_task) noexcept<CoTask20<OtherCoroReturnType>> {
    ////  return CoTaskAwaitable<OtherCoroReturnType>((IAsyncTask*)(_other_task.m_user_data));
    ////}

    uint64_t m_return_type_id = 0;
    std::string_view m_return_type_name;
  };

  using CoTaskFunctionType = std::function<cotask20<ReturnType>(void)>;
  // Using a conditional here for protect void type~~

  cotask20() = default;

  explicit cotask20(coroutine_handle<promise_type> co_handle) : co_handle_(co_handle) {}

  cotask20(const cotask20& other) : co_handle_(other.co_handle_), bind_task_(other.bind_task_) {}

  cotask20& operator=(const cotask20& other) noexcept {
    if (&other != this) {
      co_handle_ = other.co_handle_;
      bind_task_ = other.bind_task_;
    }
    return *this;
  }

  ~cotask20() {}

  bool done() const noexcept { return co_handle_.done(); }

  ////const void* get_user_data() const { return bind_task_; }
  ////void set_user_data(const void* ud) { bind_task_ = ud; }

  iasync_task* get_bind_task() const { return bind_task_; }

  void bind_return_function(ReturnValueSettingFunc&& func) {
    co_handle_.promise().return_value_setting_func_ = std::forward<ReturnValueSettingFunc>(func);
  }

  auto operator co_await() {
    return tasks::cotask_awaitable<ReturnType>((const iasync_task*)bind_task_);
  }

 protected:
  void resume() { co_handle_.resume(); }

  void destroy() { co_handle_.destroy(); }

  void set_bind_task(iasync_task* _task) { bind_task_ = _task; }
 protected:
  coroutine_handle<promise_type>  co_handle_ = nullptr;
  iasync_task*                    bind_task_ = nullptr;
};

//-----------------------------------------------------------------------------------------------
////template <class OtherCoroReturnType>
////auto operator co_await(const cotask20<OtherCoroReturnType>& _other_task) {
////  return tasks::cotask_awaitable<OtherCoroReturnType>((const iasync_task*)_other_task.get_user_data());
////}
//-----------------------------------------------------------------------------------------------

// template utils
template <typename T, typename U = void>
struct cotask_type_traits {
  static constexpr bool value = false;
  using co_return_type = void;
};

template <typename T>
struct cotask_type_traits<cotask20<T>> {
  static constexpr bool value = true;
  using co_return_type = T;
};

static_assert(cotask_type_traits<cotask20<int>>::value, "Must be true for cotask20<int> here!");
static_assert(std::is_same_v<cotask_type_traits<cotask20<int>>::co_return_type, int>, "[co_return_type = int] Must right for cotask20<int> here!");

//-----------------------------------------------------------------------------------------------



}  // namespace gbf::coro
#endif
#endif
