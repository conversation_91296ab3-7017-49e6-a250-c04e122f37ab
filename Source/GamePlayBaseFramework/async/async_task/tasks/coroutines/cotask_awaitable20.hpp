#pragma once

#if defined(GBF_ENABLE_CPP20)
#ifndef _MANAGED
#include "async_task/tasks/coroutines/coroutine20_define.hpp"
#include "async_task/tasks/iasync_task.hpp"
#include "async_task/tasks/tasks_define.hpp"
#include "async_task/tasks/coroutines/coro_util.hpp"

namespace gbf::coro::tasks {

//-----------------------------------------------------------------------------------------------
template <typename ResultType>
class cotask_awaitable {
 public:
  cotask_awaitable(const iasync_task* wait_task) : waited_task_((const_cast<iasync_task*>(wait_task))->shared_from_this()) {}
  ~cotask_awaitable() {
    if (is_bind_callback_) {
      waited_task_->queue_for_clear_return_callback();
      is_bind_callback_ = false;
    }
  }

  bool await_ready() { return false; }

  void await_suspend(coroutine_handle<>) noexcept {
    auto* src_task = iasync_task::this_thread_task();
    auto* manager = &(src_task->manager());

    coro_return_function<ResultType> func = [manager, awake_id = src_task->task_id(), this](CoroRunningState run_state,
                                                                                      const std::optional<ResultType>& ret) {
      ret_value_ = ret;
      is_bind_callback_ = false;
      coro_util::awake_one_task_in_manager_with_nothing(manager, awake_id);
    };
    waited_task_->queue_for_bind_return_callback(std::move(func));
    
    is_bind_callback_ = true;
    src_task->await_setting(AwaitMode::kAwaitForNotifyNoTimeout);
  }

  std::optional<ResultType> await_resume() const noexcept { return ret_value_; }

 private:
  async_task_ptr waited_task_;
  std::optional<ResultType> ret_value_;
  bool is_bind_callback_ = false;
};
//-----------------------------------------------------------------------------------------------
template <>
class cotask_awaitable<void> {
 public:
  cotask_awaitable(const iasync_task* wait_task) : waited_task_((const_cast<iasync_task*>(wait_task))->shared_from_this()) {}
  ~cotask_awaitable() {
    if (is_bind_callback_) {
      waited_task_->queue_for_clear_return_callback();
      is_bind_callback_ = false;
    }
  }

  bool await_ready() { return false; }

  void await_suspend(coroutine_handle<>) noexcept {
    auto* src_task = iasync_task::this_thread_task();
    auto* manager = &(src_task->manager());

    coro_return_void_function func = [manager, awake_id = src_task->task_id(), this](CoroRunningState run_state) {
      is_bind_callback_ = false;
      coro_util::awake_one_task_in_manager_with_nothing(manager, awake_id);
    };
    waited_task_->queue_for_bind_return_void_callback(std::move(func));

    is_bind_callback_ = true;
    src_task->await_setting(AwaitMode::kAwaitForNotifyNoTimeout);
  }

  void await_resume() const noexcept {  }
 private:
  async_task_ptr waited_task_;
  bool is_bind_callback_ = false;
};
//-----------------------------------------------------------------------------------------------


}  // namespace gbf::coro::tasks
#endif
#endif
