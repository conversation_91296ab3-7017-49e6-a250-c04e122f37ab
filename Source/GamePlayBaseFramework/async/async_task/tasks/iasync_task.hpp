#pragma once

#include <functional>
#include <queue>
#include <stack>
#include <unordered_set>

#include "async_task/jobs/jobs_define.hpp"
#include "async_task/tasks/tasks_define.hpp"
////#include "async_task/tasks/coroutines/impl17/new_co_task.hpp"

#include "reflection/meta/meta_class.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/builder/class_builder.inl"
#include "async_task/tasks/resume_object.hpp"
#include "core/imodules/ilog_module.h"
#include "core/thread/normal_mutex.h"

namespace gbf::coro {

class ASYNC_TASKS_API iasync_task : public std::enable_shared_from_this<iasync_task> {
  friend class coro_service;
  friend class jobs::job_system_slot;

 public:
  iasync_task() = delete;
  iasync_task(const iasync_task&) = delete;
  iasync_task(uint64_t _task_id, coro_service_manager& _manager);

  virtual ~iasync_task();

  uint64_t task_id() const { return task_id_; }

  CoroImplementType implement_type() const noexcept { return implement_type_; }

  bool is_done() const noexcept { return last_running_state_ < 0; }

  CoroRunningState resume(JobType job_type);

  CoroRunningState last_running_state() const noexcept { return last_running_state_; }

  AwaitMode await_mode() const noexcept { return await_mode_; }

  bool is_awakable() const noexcept {
    return await_mode_ == AwaitMode::kAwaitForNotifyNoTimeout || await_mode_ == AwaitMode::kAwaitForNotifyWithTimeout;
  }

  int await_timeout() const noexcept { return await_timeout_; }

  template <typename AwaitEventType>
  auto get_resume_object_as() -> std::enable_if_t<std::is_base_of<resume_object, AwaitEventType>::value, AwaitEventType*> {
    AwaitEventType* obj = nullptr;

    if (resume_object_.IsValid() && resume_object_.GetClass().id() == MetatypeHash::Hash<AwaitEventType>()) {
      obj = (AwaitEventType*)resume_object_.GetPointer();
    }

    return obj;
  }

  bool has_resume_object() const noexcept { return resume_object_.IsValid(); }

  void clear_resume_object() { resume_object_ = reflection::UserObject::nothing; }

  bool last_invoke_ok() const noexcept {
    if (resume_object_.IsValid()) {
      resume_object* ev = (resume_object*)resume_object_.GetPointer();
      return ev->result == AwaitResult::kAwaitSuc;
    }
    return true;
  }

  bool last_invoke_timeout() const noexcept {
    if (resume_object_.IsValid()) {
      resume_object* ev = (resume_object*)resume_object_.GetPointer();
      return ev->result == AwaitResult::kAwaitTimeout;
    }
    return false;
  }

  bool last_invoke_failed() const noexcept {
    if (resume_object_.IsValid()) {
      resume_object* ev = (resume_object*)resume_object_.GetPointer();
      return ev->result != AwaitResult::kAwaitSuc;
    }
    return false;
  }

  const auto& child_task_array() const { return child_array_; }

  const auto& wait_quit_notify_array() const { return wait_quit_notify_array_; }

  coro_service_manager& manager() const { return manager_; }

  void await_setting(AwaitMode mode, int awaitTimeMs = 0);

  template <typename ReturnType>
  void bind_return_as(ReturnType&& val) {
    // Check type match first
    constexpr auto tid = __type_id<ReturnType>();
    constexpr auto tname = __type_name<ReturnType>();
    if (tid != get_return_type_id()) {
      auto expectn = get_return_type_name();
      ERR_DEF("[coroutine]BindReturnValue() find do not match return value to coroutine, expect:%s, but bind type is:%s", expectn.data(),
              tname.data());
      return;
    }

    return_object_ = reflection::make_user_object(std::move(val));
  }

  void bind_return_void() {
    // Do nothing here
  }

  template <typename ReturnType>
  std::shared_ptr<ReturnType> get_return_as() {
    // Check type match first
    constexpr auto tid = __type_id<ReturnType>();
    constexpr auto tname = __type_name<ReturnType>();
    if (tid != get_return_type_id()) {
      auto expectn = get_return_type_name();
      ERR_DEF("[coroutine]GetReturnValue() find do not match return value to coroutine, expect:%s, but query type is:%s", expectn.data(),
              tname.data());
      return {};
    }

    if (return_object_.IsEmpty()) {
      return {};
    }

    std::shared_ptr<ReturnType> ret;
    reflection::__unbox(return_object_, ret);
    return ret;
  }

  virtual uint64_t get_return_type_id() const { return 0; }

  virtual std::string_view get_return_type_name() const { return {}; }

  void defer(coro_defer_function&& func) { defer_func_stack_.emplace(std::move(func)); }

  JobType work_job_type() const noexcept { return work_job_type_; }

  static iasync_task* this_thread_task();

  static coro_service_manager* this_thread_coro_manager();


  ////void set_job_type(JobSystemType job_type) { work_job_type_ = job_type; }
  //-----------------------------------------------------------------------------------------------
  // Queued functions call for operate thread
  //-----------------------------------------------------------------------------------------------
  // resume value handle functions
  template <typename AwaitEventType>
  auto queue_for_bind_resume_value_as(AwaitEventType&& awaitEvent) -> std::enable_if_t<std::is_base_of<resume_object, AwaitEventType>::value> {
    auto obj = reflection::make_user_object<AwaitEventType>(std::forward<AwaitEventType>(awaitEvent));
    add_operate([obj = std::move(obj), this]() {
      resume_object_ = std::move(obj);
      // Clear maybe timeout task here
      bind_timeout_handle(0);
    });
  }

  auto queue_for_bind_resume_value(const reflection::UserObject& obj) {
    add_operate([obj, this]() {
      resume_object_ = obj;
      // Clear maybe timeout task here
      bind_timeout_handle(0);
    });
  }

  void queue_for_terminate() {
    // Do flag setting here, not do real action
    add_operate([this]() { last_running_state_ = CoroRunningState::kTerminate; });
  }


  template <class ResultType>
  void queue_for_bind_return_callback(coro_return_function<ResultType>&& func) {
    // type erase here~~
    auto tmpfunc = [func = std::move(func), this]() {
      std::optional<ResultType> ret;
      auto v = get_return_as<ResultType>();
      if (v) {
        ret = *v;
      }
      func(last_running_state_, ret);
    };

    add_operate([tmpfunc = std::move(tmpfunc), this] { return_func_ = std::move(tmpfunc); });
  }

  void queue_for_bind_return_void_callback(coro_return_void_function&& func) {
    // type erase here~~
    auto tmpfunc = [func = std::move(func), this]() {
      func(last_running_state_);
    };

    add_operate([tmpfunc = std::move(tmpfunc), this] { return_func_ = std::move(tmpfunc); });
  }


  void queue_for_clear_return_callback() {
    add_operate([this]() {
      coro_defer_function empty_func;
      return_func_.swap(empty_func); 
    });
  }

  void queue_for_add_child_task(uint64_t tid) {
    add_operate([tid, this]() { child_array_.emplace_back(tid); });
  }

  void queue_for_add_wait_quit_nofity_task(uint64_t tid) {
    add_operate([tid, this]() { wait_quit_notify_array_.emplace_back(tid); });
  }

 protected:
  void call_all_defers_impl() {
    while (!defer_func_stack_.empty()) {
      auto& func = defer_func_stack_.top();
      func();
      defer_func_stack_.pop();
    }

    // call awake wait quit task here
    awake_wait_quit_task_impl();
  }

  void call_return_callback_impl() {
    // extra queued operates call here.
    do_queue_operates_impl();

    // keep lock here to protect return_func_ change by operate thread
    {
      threads::NormalMutex::LockGuard lock(op_mutex_);
      if (!return_func_) {
        return;
      }

      // check last running state is not terminate here
      if (GBF_UNLIKELY(last_running_state_ == CoroRunningState::kTerminate)) {
        // just return not call return callback here, for the coroutine is terminate now!
        return;
      }
      return_func_();
    }
  }

  void awake_wait_quit_task_impl();

  void do_queue_operates_impl();

  void add_operate(coro_defer_function&& func) {
    if (this_thread_task() == this) {
      //run immediate
      func();
    } else {
      //run by queue
      threads::NormalMutex::LockGuard lock(op_mutex_);
      operate_queue_.emplace(std::move(func));
    }
  }

  void bind_timeout_handle(uint64_t handle);

 protected:
  ////virtual bool IsDoneImpl() const = 0;
  virtual CoroRunningState resume_impl(JobType job_type) = 0;

 protected:
  uint64_t task_id_;
  coro_service_manager& manager_;
  CoroImplementType implement_type_ = CoroImplementType::kUnknown;

  std::vector<uint64_t> child_array_;

  std::vector<uint64_t> wait_quit_notify_array_;

  // value used to return from coroutine
  AwaitMode await_mode_ = AwaitMode::kAwaitDoNothing;
  int await_timeout_ = 0;

  // value used to send to coroutine(now as a AwaitEvent)
  reflection::UserObject resume_object_;

  uint64_t sleep_handle_ = 0;

  reflection::UserObject return_object_;

  bool is_defer_called = false;
  std::stack<coro_defer_function> defer_func_stack_;

  coro_defer_function return_func_;

  CoroRunningState last_running_state_ = CoroRunningState::kBegin;

  std::queue<coro_defer_function> operate_queue_;
  threads::NormalMutex op_mutex_;

  mutable JobType work_job_type_ = JobType::kInvalidJob;
};

}  // namespace gbf::coro
