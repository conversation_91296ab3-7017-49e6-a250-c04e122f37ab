#pragma once

#include "async_task/tasks/tasks_define.hpp"
#include "reflection/objects/args.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/objects/value.hpp"

namespace gbf::coro {

struct resume_object {
  uint64_t task_id = 0;
  AwaitResult result = AwaitResult::kAwaitSuc;
};

struct create_task_resume_object : public resume_object {
  uint64_t new_task_id = 0;
};

////struct RpcResumeObject : public ResumeObject {
//// public:
////  uint64_t rpc_id = 0;
////  network::RpcResponseResultType rpc_result_type = network::RpcResponseResultType::Timeout;
////  int total_ret = 0;
////  reflection::Value ret_value;
////};

struct timeout_resume_object : public resume_object {};

struct wait_task_quit_resume_object : public resume_object {
  uint64_t wait_target_id = 0;
};

struct create_line_resume_object : public resume_object {
  uint32_t line_no = 0;
  uint64_t space_id = 0;
  uint64_t space_no = 0;
};

}  // namespace gbf::coro
