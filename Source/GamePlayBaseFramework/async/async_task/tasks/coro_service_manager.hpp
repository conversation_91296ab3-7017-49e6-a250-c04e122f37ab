#pragma once

#include <queue>
#include <unordered_set>

#include "async_task/jobs/jobs_define.hpp"
#include "async_task/tasks/tasks_define.hpp"
#include "reflection/meta/meta_class.hpp"
#include "reflection/objects/userobject.hpp"

#include "async_task/tasks/iasync_task.hpp"
////#include "async_task/tasks/resume_object.hpp"

#include "async_task/tasks/async_task17.hpp"

#if defined(GBF_ENABLE_CPP20)
#include "async_task/tasks/async_task20.hpp"
#include "async_task/tasks/coroutines/cotask20.hpp"
////#include "async_task/tasks/await_handle20.h"
#endif

#include "core/thread/normal_mutex.h"

namespace gbf::coro {

class ASYNC_TASKS_API coro_service_manager {
  friend class coro_service;
  // Nested types
  using async_task_info_map = std::unordered_map<uint64_t, async_task_weak_ptr>;

  struct waited_task_info {
    uint64_t tid = 0;
    JobType awake_job_type = JobType::kInvalidJob;
    AwaitMode await_mode = AwaitMode::kAwaitUnknown;
    uint64_t timeout_ms = 0;
    async_task_ptr wait_task;
  };
  using waited_task_info_map = std::unordered_map<uint64_t, waited_task_info>;

 public:
  coro_service_manager() = delete;
  coro_service_manager(const coro_service_manager&) = delete;
  coro_service_manager(jobs::job_system& _parent_job_system);
  ~coro_service_manager();

  template <class Func, class... Args>
  auto create_task17(JobType job_type, Func&& f, Args&&... args) {
    auto native_task = coro::co_start(f, std::forward<Args>(args)...);
    create_task17_impl(job_type, std::move(native_task));
    return native_task;
  }

  template <class ReturnType>
  uint64_t create_task17_impl(JobType job_type, cotask17<ReturnType>&& cotask) {
    auto tid = ++id_count_;

    async_task_ptr atask = std::make_shared<async_task17>(tid, cotask.m_handler, this);
    cotask.m_handler.set_user_data(atask.get());
    cotask.m_handler.set_user_id(tid);

    add_task_info(atask);
    dispatch_async_task_impl(job_type, std::move(atask));

    return tid;
  }

#if defined(GBF_ENABLE_CPP20)
  template <class Func, class CoTask20Type = reflection::detail::TFunctionTraits<Func>::ExposedType,
            class U = std::enable_if_t<cotask_type_traits<CoTask20Type>::value>>
  auto create_task20(JobType job_type, Func&& taskFunc) {
    using CoReturnType = cotask_type_traits<CoTask20Type>::co_return_type;
    auto tid = ++id_count_;

    auto coro_ret = taskFunc();
    async_task_ptr atask = std::make_shared<async_task20<CoReturnType>>(tid, std::forward<Func>(taskFunc), coro_ret, this);
    // need extra operate here
    coro_ret.set_bind_task(atask.get());

    add_task_info(atask);
    dispatch_async_task_impl(job_type, std::move(atask));

    return coro_ret;
  }
#endif

  // Kill, but not destroy the task immediate
  void manual_terminate_task(uint64_t t_id);

  // Not a real event notify here, just do need things
  template <typename E>
  auto awake_task_by(uint64_t tid, E&& awaitObj) -> std::enable_if_t<std::is_base_of<resume_object, E>::value> {
    // Only in await set task can be resume
    auto* task_info = query_waited_task_info(tid);
    if (GBF_LIKELY(task_info != nullptr)) {
      // just copy here for move
      async_task_ptr task = task_info->wait_task;
      auto job_type = task_info->awake_job_type;
      remove_task_from_waited(tid);
      task->queue_for_bind_resume_value_as(std::forward<E>(awaitObj));
      dispatch_async_task_impl(job_type, std::move(task));
    } else {
      WRN_DEF("Try to awake not in wait list object, may be problems here, tid: %llu,  awake type:%s", tid,
              std::string(MetatypeHash::NamePretty<E>()).c_str());
    }
  }

  void awake_task_by_user_object(uint64_t tid, const reflection::UserObject& obj) {
    // Only in await set task can be resume
    auto* task_info = query_waited_task_info(tid);
    if (GBF_LIKELY(task_info != nullptr)) {
      // just copy here for move
      async_task_ptr task = task_info->wait_task;
      auto job_type = task_info->awake_job_type;
      remove_task_from_waited(tid);
      task->queue_for_bind_resume_value(obj);
      dispatch_async_task_impl(job_type, std::move(task));
    } else {
      WRN_DEF("Try to awake not in wait list object, may be problems here, tid: %llu,  awake type:%s", tid,
              obj.GetClass().name().c_str());
    }
  }

  void awake_task_by_nothing(uint64_t tid) {
    // Only in await set task can be resume
    auto* task_info = query_waited_task_info(tid);
    if (GBF_LIKELY(task_info != nullptr)) {
      // just copy here for move
      async_task_ptr task = task_info->wait_task;
      auto job_type = task_info->awake_job_type;
      remove_task_from_waited(tid);
      dispatch_async_task_impl(job_type, std::move(task));
    } else {
      WRN_DEF("Try to awake not in wait list object, may be problems here, tid: %llu", tid);
    }
  }

  template <typename RetType>
  static iasync_task* async_task_from_cotask17(const cotask17<RetType>& cotask) {
    return (iasync_task*)const_cast<void*>(cotask.m_handler.get_user_data());
  }

#if GBF_ENABLE_CPP20
#ifndef _MANAGED
  template <typename RetType>
  static iasync_task* async_task_from_cotask20(const cotask20<RetType>& cotask) {
    return cotask.get_bind_task();
  }
#endif
#endif
 
  jobs::job_system& parent_job_system() const { return parent_job_system_; }

  void request_task_transfer(iasync_task* task, JobType src_job_type, JobType target_job_type);
  bool request_wait_for_task_quit(iasync_task* task, JobType awake_job_type, uint64_t target_task_id, AwaitMode wait_mode, uint64_t timeout_ms);

 protected:
  void request_task_suspend(const async_task_ptr task, JobType awake_job_type, AwaitMode wait_mode, uint64_t timeout_ms);

  void add_task_info(const async_task_ptr& task);

  void remove_task_info(uint64_t tid);

 protected:
  static void do_reflection_register();

  void dispatch_async_task_impl(JobType job_type, async_task_ptr&& atask);

  async_task_ptr query_task_info(uint64_t tid);

  waited_task_info* query_waited_task_info(uint64_t tid);
  void add_task_to_waited(const async_task_ptr& task, JobType awake_job_type, AwaitMode await_mode, uint64_t timeout_ms);
  void remove_task_from_waited(uint64_t tid);

 protected:
  static bool s_is_ponder_registered_;

  uint64_t id_count_ = 0;
  async_task_info_map all_task_map_;
  threads::NormalMutex all_task_map_mutex_;

  waited_task_info_map waited_task_map_;
  threads::NormalMutex waited_task_map_mutex_;

  jobs::job_system& parent_job_system_;
};

}  // namespace gbf::coro

#include "async_task/tasks/coro_service_manager.inl"
