#include "async_task/tasks/coro_service.hpp"

#include <chrono>

#include "async_task/asio_lite/timer/steady_timer.hpp"
#include "async_task/jobs/job_system.hpp"
#include "async_task/jobs/job_system_slot.hpp"
#include "async_task/tasks/coro_service_manager.hpp"
#include "async_task/tasks/iasync_task.hpp"

namespace gbf::coro {

core_service_id coro_service::id;
//-----------------------------------------------------------------------------------------------

void coro_service::dispatch(const async_task_ptr& async_task) {
  job_slot_.dispatch([atask = async_task, this]() {
    do {
      auto running_state = atask->resume(job_slot_.job_type());
      if (running_state < 0) {
        // coroutine run finish, end loop
        break;
      }

      auto await_mode = atask->await_mode();
      if (await_mode == AwaitMode::kAwaitNextframe) {
        if (GBF_LIKELY(support_next_frame_)) {
          add_to_next_frame_queue(atask);
          break;
        } else {
          // Just ignore await here
          WRN_DEF("[coroutine] Do not support NextFrame in this slot, just ignore here!");
          continue;
        }
        // add to next frame run, end loop
        break;
      } else if (await_mode == AwaitMode::kAwaitForNotifyNoTimeout || await_mode == AwaitMode::kAwaitForNotifyWithTimeout) {
        // do suspend handle, end loop
        after_suspend_handle(atask, await_mode, atask->await_timeout());
        break;
      } else if (await_mode == AwaitMode::kAwaitDoNothing) {
        // do nothing, end loop
        break;
      } else if (await_mode == AwaitMode::kAwaitNever) {
        // do nothing, repeat again
        continue;
      } else if (await_mode == AwaitMode::kAwaitUnknown) {
        // Just handle as await never here
        continue;
        ////GBF_ERROR(CoroutineSuspendNotCallAwaitSetting());
        ////break;
      } else {
        GBF_ERROR(CanNotRunToHereError());
        break;
      }
    } while (true);
  });
}

void coro_service::awake_next_frame_queue() {
  if (GBF_UNLIKELY(!support_next_frame_)) {
    return;
  }

  while (!next_frame_queue_.empty()) {
    auto atask = next_frame_queue_.front();
    dispatch(atask);
    next_frame_queue_.pop();
  }
}

void coro_service::after_suspend_handle(const async_task_ptr& async_task, AwaitMode amode, uint64_t atimeout_ms) {
  parent_manager_.request_task_suspend(async_task, job_slot_.job_type(), amode, atimeout_ms);
  if (amode == AwaitMode::kAwaitForNotifyWithTimeout) {
    // Need timeout handle here
    register_timeout_for_task(async_task, atimeout_ms);
  }
}

void coro_service::register_timeout_for_task(const async_task_ptr& async_task, uint64_t atimeout_ms) {
  auto tid = async_task->task_id();

  ////asio::steady_timer timer(job_slot_.context());
  ////timer.expires_after(std::chrono::milliseconds(atimeout_ms));
  ////timer.async_wait([this, tid](asio::error_code ec) {
  ////  timeout_resume_object te;
  ////  te.task_id = tid;
  ////  te.result = AwaitResult::kAwaitTimeout;
  ////  parent_manager_.awake_task_by(tid, std::move(te));
  ////});

  auto handle = job_slot_.parent_system().add_delay_run_job(
      job_slot_.job_type(),
      [this, tid]() {
        timeout_resume_object te;
        te.task_id = tid;
        te.result = AwaitResult::kAwaitTimeout;
        parent_manager_.awake_task_by(tid, std::move(te));
      },
      atimeout_ms);
  async_task->bind_timeout_handle(handle);
}

}  // namespace gbf::coro
