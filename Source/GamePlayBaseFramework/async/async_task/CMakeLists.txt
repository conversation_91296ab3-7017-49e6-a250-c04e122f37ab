cmake_minimum_required(VERSION 3.16)

include_directories(
    ${PROJECT_SOURCE_DIR}
    ${PROJECT_SOURCE_DIR}/base
    ${PROJECT_SOURCE_DIR}/meta
    ${PROJECT_SOURCE_DIR}/async
    ${PROJECT_SOURCE_DIR}/externals/include/
)

link_directories(
    ${CMAKE_BINARY_DIR}
#    ${EXTERNAL_LIB_DIR}/tinyxml2
)

add_definitions(-DASYNC_TASKS_EXPROTS)

set(all_project_src "")

file(GLOB top_src                     "*.*")
source_group(\\                       FILES      ${top_src})
list(APPEND all_project_src           ${top_src})

# tasks code here
file(GLOB tasks_src                   "tasks/*.*")
source_group(\\tasks                  FILES      ${tasks_src})
list(APPEND all_project_src           ${tasks_src})

file(GLOB coroutines_src              "tasks/coroutines/*.*")
source_group(\\tasks\\coroutines             FILES ${coroutines_src})
list(APPEND all_project_src           ${coroutines_src})

file(GLOB coroutines_impl17_src       "tasks/coroutines/impl17/*.*")
source_group(\\tasks\\coroutines\\impl17     FILES ${coroutines_impl17_src})
list(APPEND all_project_src           ${coroutines_impl17_src})

# jobs
file(GLOB job_src "jobs/*.*")
source_group(\\jobs FILES ${job_src})
list(APPEND all_project_src ${job_src})

# legacy asio_only_post(asio1.5 ???)
# file(GLOB asio_only_post_src "asio_only_post/*.*")
# source_group(\\asio_only_post FILES ${asio_only_post_src})
# list(APPEND all_project_src ${asio_only_post_src})

# file(GLOB asio_only_post_impl_src "asio_only_post/impl/*.*")
# source_group(\\asio_only_post\\impl FILES ${asio_only_post_impl_src})
# list(APPEND all_project_src ${asio_only_post_impl_src})

# imodules
# file(GLOB imodules_src "imodules/*.*")
# source_group(\\imodules FILES ${imodules_src})
# list(APPEND all_project_src ${imodules_src})

# asio lite code from here
file(GLOB asio_top_src                "asio_lite/*.*")
source_group(\\asio_lite              FILES      ${asio_top_src})
list(APPEND all_project_src           ${asio_top_src})

file(GLOB detail_src                  "asio_lite/detail/*.*")
source_group(\\asio_lite\\detail                 FILES      ${detail_src})
list(APPEND all_project_src           ${detail_src})

file(GLOB detail_impl_src             "asio_lite/detail/impl/*.*")
source_group(\\asio_lite\\detail\\impl           FILES      ${detail_impl_src})
list(APPEND all_project_src           ${detail_impl_src})

file(GLOB impl_src                    "asio_lite/impl/*.*")
source_group(\\asio_lite\\impl                   FILES      ${impl_src})
list(APPEND all_project_src           ${impl_src})

file(GLOB asio_coro_src               "asio_lite/coro/*.*")
source_group(\\asio_lite\\coro        FILES      ${asio_coro_src})
list(APPEND all_project_src           ${asio_coro_src})

file(GLOB sync_primitives_src         "asio_lite/sync_primitives/*.*")
source_group(\\asio_lite\\sync_primitives        FILES      ${sync_primitives_src})
list(APPEND all_project_src           ${sync_primitives_src})

file(GLOB error_src                   "asio_lite/error/*.*")
source_group(\\asio_lite\\error                  FILES      ${error_src})
list(APPEND all_project_src           ${error_src})

file(GLOB tools_src                   "asio_lite/tools/*.*")
source_group(\\asio_lite\\tools                  FILES      ${tools_src})
list(APPEND all_project_src           ${tools_src})

file(GLOB context_src                 "asio_lite/context/*.*")
source_group(\\asio_lite\\context                FILES      ${context_src})
list(APPEND all_project_src           ${context_src})

file(GLOB memory_src                  "asio_lite/memory/*.*")
source_group(\\asio_lite\\memory                 FILES      ${memory_src})
list(APPEND all_project_src           ${memory_src})

file(GLOB timer_src                   "asio_lite/timer/*.*")
source_group(\\asio_lite\\timer                  FILES      ${timer_src})
list(APPEND all_project_src           ${timer_src})

file(GLOB service_src                 "asio_lite/service/*.*")
source_group(\\asio_lite\\service                FILES      ${service_src})
list(APPEND all_project_src           ${service_src})

file(GLOB strand_src                  "asio_lite/strand/*.*")
source_group(\\asio_lite\\strand                 FILES      ${strand_src})
list(APPEND all_project_src           ${strand_src})

file(GLOB operation_src               "asio_lite/operation/*.*")
source_group(\\asio_lite\\operation              FILES      ${operation_src})
list(APPEND all_project_src           ${operation_src})

file(GLOB handler_src                 "asio_lite/operation/handler/*.*")
source_group(\\asio_lite\\operation\\handler                FILES      ${handler_src})
list(APPEND all_project_src           ${handler_src})

file(GLOB concepts_src                "concepts/*.*")
source_group(\\asio_lite\\concepts               FILES      ${concepts_src})
list(APPEND all_project_src           ${concepts_src})

file(GLOB concepts_detail_src         "asio_lite/concepts/detail/*.*")
source_group(\\asio_lite\\concepts\\detail       FILES      ${concepts_detail_src})
list(APPEND all_project_src           ${concepts_detail_src})

file(GLOB scheduler_src                "asio_lite/scheduler/*.*")
source_group(\\asio_lite\\scheduler               FILES      ${scheduler_src})
list(APPEND all_project_src           ${scheduler_src})

file(GLOB scheduler_detail_src         "asio_lite/scheduler/detail/*.*")
source_group(\\asio_lite\\scheduler\\detail       FILES      ${scheduler_detail_src})
list(APPEND all_project_src            ${scheduler_detail_src})

file(GLOB platform_src                 "asio_lite/platform/*.*")
source_group(\\asio_lite\\platform                FILES      ${platform_src})
list(APPEND all_project_src            ${platform_src})

file(GLOB platform_win_src             "asio_lite/platform/win/*.*")
source_group(\\asio_lite\\platform\\win           FILES      ${platform_win_src})
list(APPEND all_project_src            ${platform_win_src})

file(GLOB platform_linux_src           "asio_lite/platform/linux/*.*")
source_group(\\asio_lite\\platform\\linux         FILES      ${platform_linux_src})
list(APPEND all_project_src            ${platform_linux_src})

if(GBF_ENABLE_CPP20)
  file(GLOB coro_src                    "asio_lite/coro/*.*")
  source_group(\\asio_lite\\coro                   FILES      ${coro_src})
  list(APPEND all_project_src           ${coro_src})

  file(GLOB coro_experimental_src       "asio_lite/coro/experimental/*.*")
  source_group(\\asio_lite\\coro\\experimental     FILES      ${coro_experimental_src})
  list(APPEND all_project_src           ${coro_experimental_src})

  file(GLOB coro_experimental_detail_src "asio_lite/coro/experimental/detail/*.*")
  source_group(\\asio_lite\\coro\\experimental\\detail FILES ${coro_experimental_detail_src})
  list(APPEND all_project_src           ${coro_experimental_detail_src})
else()
  message(STATUS "asio coroutine support is disabled in c++17")
endif()

file(GLOB cancellation_src             "asio_lite/cancellation/*.*")
source_group(\\asio_lite\\cancellation            FILES      ${cancellation_src})
list(APPEND all_project_src            ${cancellation_src})


set(async_task_name "async_task")

add_library(${async_task_name} SHARED
  ${all_project_src}
)

target_link_libraries(${async_task_name} 
    gbf_core
    reflection
)

add_dependencies(${async_task_name} 
    gbf_core
    reflection
)

set_target_properties(${async_task_name} PROPERTIES UNITY_BUILD ON)
SET_PROPERTY(TARGET ${async_task_name} PROPERTY FOLDER "framework c++/async")
