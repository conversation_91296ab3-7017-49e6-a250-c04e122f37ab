#pragma once

#include "async_task/async_task_config.hpp"

namespace asio {
namespace detail {

template <typename T>
struct non_const_lvalue
{
  explicit non_const_lvalue(T& t)
    : value(static_cast<typename std::conditional<
        std::is_same<T, typename std::decay<T>::type>::value,
          typename std::decay<T>::type&, T&&>::type>(t))
  {
  }

  typename std::conditional<std::is_same<T, typename std::decay<T>::type>::value,
      typename std::decay<T>::type&, typename std::decay<T>::type>::type value;
};

} // namespace detail
} // namespace asio

