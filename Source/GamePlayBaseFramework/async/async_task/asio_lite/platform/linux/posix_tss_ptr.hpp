#pragma once

#include "async_task/async_task_config.hpp"

#if GBF_CORE_PLATFORM != GBF_CORE_PLATFORM_WIN32

#include <pthread.h>
#include "async_task/asio_lite/tools/noncopyable.hpp"

namespace asio {
namespace detail {

// Helper function to create thread-specific storage.
ASYNC_TASKS_API void posix_tss_ptr_create(pthread_key_t& key);

template <typename T>
class posix_tss_ptr
  : private noncopyable
{
public:
  // Constructor.
  posix_tss_ptr()
  {
    posix_tss_ptr_create(tss_key_);
  }

  // Destructor.
  ~posix_tss_ptr()
  {
    ::pthread_key_delete(tss_key_);
  }

  // Get the value.
  operator T*() const
  {
    return static_cast<T*>(::pthread_getspecific(tss_key_));
  }

  // Set the value.
  void operator=(T* value)
  {
    ::pthread_setspecific(tss_key_, value);
  }

private:
  // Thread-specific storage to allow unlocked access to determine whether a
  // thread is a member of the pool.
  pthread_key_t tss_key_;
};

} // namespace detail
} // namespace asio


#endif // defined(ASIO_HAS_PTHREADS)

