#include "async_task/asio_lite/platform/linux/posix_tss_ptr.hpp"

#if GBF_CORE_PLATFORM != GBF_CORE_PLATFORM_WIN32

#include "async_task/asio_lite/error/throw_error.hpp"

namespace asio {
namespace detail {

void posix_tss_ptr_create(pthread_key_t& key)
{
  int error = ::pthread_key_create(&key, 0);
  asio::error_code ec(error,
      asio::system_category());
  asio::detail::throw_error(ec, "tss");
}

} // namespace detail
} // namespace asio


#endif // GBF_CORE_PLATFORM != GBF_CORE_PLATFORM_WIN32

