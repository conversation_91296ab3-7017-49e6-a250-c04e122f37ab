#pragma once

#include "platform.hpp"
#include "async_task/async_task_config.hpp"

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32

#define WIN32_LEAN_AND_MEAN
#include <windows.h>

#include "async_task/asio_lite/tools/noncopyable.hpp"

namespace asio {
namespace detail {

// Helper function to create thread-specific storage.
ASYNC_TASKS_API DWORD win_tss_ptr_create();

template <typename T>
class win_tss_ptr
  : private noncopyable
{
public:
  // Constructor.
  win_tss_ptr()
    : tss_key_(win_tss_ptr_create())
  {
  }

  // Destructor.
  ~win_tss_ptr()
  {
    ::TlsFree(tss_key_);
  }

  // Get the value.
  operator T*() const
  {
    return static_cast<T*>(::TlsGetValue(tss_key_));
  }

  // Set the value.
  void operator=(T* value)
  {
    ::TlsSetValue(tss_key_, value);
  }

private:
  // Thread-specific storage to allow unlocked access to determine whether a
  // thread is a member of the pool.
  DWORD tss_key_;
};

} // namespace detail
} // namespace asio


#endif // GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32

