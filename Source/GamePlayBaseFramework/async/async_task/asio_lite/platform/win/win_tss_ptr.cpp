#include "async_task/asio_lite/platform/win/win_tss_ptr.hpp"

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32

#include "async_task/asio_lite/error/throw_error.hpp"

namespace asio {
namespace detail {

DWORD win_tss_ptr_create()
{
#if defined(UNDER_CE)
  const DWORD out_of_indexes = 0xFFFFFFFF;
#else
  const DWORD out_of_indexes = TLS_OUT_OF_INDEXES;
#endif

  DWORD tss_key = ::TlsAlloc();
  if (tss_key == out_of_indexes)
  {
    DWORD last_error = ::GetLastError();
    asio::error_code ec(last_error,
        asio::system_category());
    asio::detail::throw_error(ec, "tss");
  }
  return tss_key;
}

} // namespace detail
} // namespace asio



#endif // GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32

