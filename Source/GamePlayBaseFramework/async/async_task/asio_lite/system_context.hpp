#pragma once

#include "async_task/async_task_config.hpp"

#include "async_task/asio_lite/scheduler/scheduler.hpp"
#include "async_task/asio_lite/context/thread_group.hpp"
#include "async_task/asio_lite/execution_context.hpp"
////#include "async_task/asio_lite/system_executor.hpp"

namespace asio {

class system_executor;

/// The executor context for the system executor.
class system_context : public execution_context
{
public:
  /// The executor type associated with the context.
  using executor_type = system_executor;

  /// Destructor shuts down all threads in the system thread pool.
  ASYNC_TASKS_API ~system_context();

  /////// Obtain an executor for the context.
  ////inline executor_type get_executor() noexcept {
  ////  return system_executor();
  ////}
  ASYNC_TASKS_API executor_type get_executor() noexcept;

  /// Signal all threads in the system thread pool to stop.
  ASYNC_TASKS_API void stop();

  /// Determine whether the system thread pool has been stopped.
  ASYNC_TASKS_API bool stopped() const noexcept;

  /// Join all threads in the system thread pool.
  ASYNC_TASKS_API void join();

  // Constructor creates all threads in the system thread pool.
  ASYNC_TASKS_API system_context();

private:
  friend class system_executor;

  struct thread_function;

  // Helper function to create the underlying scheduler.
  ASYNC_TASKS_API detail::scheduler& add_scheduler(detail::scheduler* s);

  // The underlying scheduler.
  detail::scheduler& scheduler_;

  // The threads in the system thread pool.
  detail::thread_group threads_;
};

////inline system_context::executor_type system_context::get_executor() noexcept { 
////  return system_executor(); 
////}

} // namespace asio




