#pragma once

#include "async_task/async_task_config.hpp"

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
# include "async_task/asio_lite/platform/win/win_tss_ptr.hpp"
#else
# include "async_task/asio_lite/platform/linux/posix_tss_ptr.hpp"
#endif

namespace asio {
namespace detail {

template <typename T>
class tss_ptr
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  : public win_tss_ptr<T>
#else
  : public posix_tss_ptr<T>
#endif
{
public:
  void operator=(T* value)
  {
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
    win_tss_ptr<T>::operator=(value);
#else
    posix_tss_ptr<T>::operator=(value);
#endif
  }
};

} // namespace detail
} // namespace asio

