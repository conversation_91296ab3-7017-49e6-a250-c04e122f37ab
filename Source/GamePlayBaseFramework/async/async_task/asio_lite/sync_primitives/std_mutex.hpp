#pragma once

#include <mutex>

#include "async_task/asio_lite/sync_primitives/std_mutex.hpp"
#include "async_task/asio_lite/tools/noncopyable.hpp"
#include "async_task/asio_lite/sync_primitives/scoped_lock.hpp"

namespace asio {
namespace detail {

class std_event;

class std_mutex
  : private noncopyable
{
public:
  using scoped_lock = asio::detail::scoped_lock<std_mutex>;

  // Constructor.
  std_mutex()
  {
  }

  // Destructor.
  ~std_mutex()
  {
  }

  // Lock the mutex.
  void lock()
  {
    mutex_.lock();
  }

  // Unlock the mutex.
  void unlock()
  {
    mutex_.unlock();
  }

private:
  friend class std_event;
  std::mutex mutex_;
};

} // namespace detail
} // namespace asio


