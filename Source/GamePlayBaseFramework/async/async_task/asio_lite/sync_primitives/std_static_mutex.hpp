#pragma once


#include <mutex>
#include "async_task/asio_lite/tools/noncopyable.hpp"
#include "async_task/asio_lite/sync_primitives/scoped_lock.hpp"


namespace asio {
namespace detail {

class std_event;

class std_static_mutex
  : private noncopyable
{
public:
  typedef asio::detail::scoped_lock<std_static_mutex> scoped_lock;

  // Constructor.
  std_static_mutex(int)
  {
  }

  // Destructor.
  ~std_static_mutex()
  {
  }

  // Initialise the mutex.
  void init()
  {
    // Nothing to do.
  }

  // Lock the mutex.
  void lock()
  {
    mutex_.lock();
  }

  // Unlock the mutex.
  void unlock()
  {
    mutex_.unlock();
  }

private:
  friend class std_event;
  std::mutex mutex_;
};

#define ASIO_STD_STATIC_MUTEX_INIT 0

} // namespace detail
} // namespace asio
