#pragma once

#include "async_task/async_task_config.hpp"

#include "async_task/asio_lite/sync_primitives/std_event.hpp"
#include "async_task/asio_lite/sync_primitives/std_fenced_block.hpp"
#include "async_task/asio_lite/sync_primitives/std_global.hpp"
#include "async_task/asio_lite/sync_primitives/std_mutex.hpp"
#include "async_task/asio_lite/sync_primitives/std_static_mutex.hpp"
#include "async_task/asio_lite/sync_primitives/std_thread.hpp"


namespace asio {
namespace detail {

using mutex = std_mutex;
using event = std_event;
using fenced_block = std_fenced_block;
using thread = std_thread;
using static_mutex = std_static_mutex;


template <typename T>
inline T& global() {
  return std_global<T>();
}

}  // namespace detail
}  // namespace asio
