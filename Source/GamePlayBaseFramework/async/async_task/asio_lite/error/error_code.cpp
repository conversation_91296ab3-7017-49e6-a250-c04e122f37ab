#include "async_task/asio_lite/error/error_code.hpp"

namespace asio {
namespace detail {

class system_category : public error_category
{
public:
  const char* name() const noexcept override
  {
    return "asio.system";
  }

  std::string message(int value) const override
  {
    char buf[128] = { 0 };
    sprintf(buf, "error.no: %d", value);
    return std::string(buf);
  }

private:
  // Helper function to adapt the result from glibc's variant of strerror_r.
  static const char* strerror_result(int, const char* s) { return s; }
  static const char* strerror_result(const char* s, const char*) { return s; }
};

} // namespace detail

const error_category& system_category()
{
  static detail::system_category instance;
  return instance;
}

} // namespace asio
