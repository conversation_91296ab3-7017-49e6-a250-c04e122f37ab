#pragma once


#include <exception>
#include "async_task/async_task_config.hpp"

#include "async_task/asio_lite/cancellation/cancellation_type.hpp"
#include "async_task/asio_lite/error/error_code.hpp"
#include "async_task/asio_lite/tools/thread_context_type_traits.hpp"


namespace asio {
namespace experimental {

/// Wait for all operations to complete.
class wait_for_all
{
public:
  template <typename... Args>
  constexpr cancellation_type_t operator()(
      Args&&...) const noexcept
  {
    return cancellation_type::none;
  }
};

/// Wait until an operation completes, then cancel the others.
class wait_for_one
{
public:
  constexpr explicit wait_for_one(
      cancellation_type_t cancel_type = cancellation_type::all)
    : cancel_type_(cancel_type)
  {
  }

  template <typename... Args>
  constexpr cancellation_type_t operator()(
      Args&&...) const noexcept
  {
    return cancel_type_;
  }

private:
  cancellation_type_t cancel_type_;
};

/// Wait until an operation completes without an error, then cancel the others.
/**
 * If no operation completes without an error, waits for completion of all
 * operations.
 */
class wait_for_one_success
{
public:
  constexpr explicit wait_for_one_success(
      cancellation_type_t cancel_type = cancellation_type::all)
    : cancel_type_(cancel_type)
  {
  }

  constexpr cancellation_type_t
  operator()() const noexcept
  {
    return cancel_type_;
  }

  template <typename E, typename... Args>
  constexpr typename constraint<
    !std::is_same<typename std::decay<E>::type, asio::error_code>::value
      && !std::is_same<typename std::decay<E>::type, std::exception_ptr>::value,
    cancellation_type_t
  >::type operator()(const E&, Args&&...) const noexcept
  {
    return cancel_type_;
  }

  template <typename E, typename... Args>
  constexpr typename constraint<
      std::is_same<typename std::decay<E>::type, asio::error_code>::value
        || std::is_same<typename std::decay<E>::type, std::exception_ptr>::value,
      cancellation_type_t
  >::type operator()(const E& e, Args&&...) const noexcept
  {
    return !!e ? cancellation_type::none : cancel_type_;
  }

private:
  cancellation_type_t cancel_type_;
};

/// Wait until an operation completes with an error, then cancel the others.
/**
 * If no operation completes with an error, waits for completion of all
 * operations.
 */
class wait_for_one_error
{
public:
  constexpr explicit wait_for_one_error(
      cancellation_type_t cancel_type = cancellation_type::all)
    : cancel_type_(cancel_type)
  {
  }

  constexpr cancellation_type_t
  operator()() const noexcept
  {
    return cancellation_type::none;
  }

  template <typename E, typename... Args>
  constexpr typename constraint<
    !std::is_same<typename std::decay<E>::type, asio::error_code>::value
      && !std::is_same<typename std::decay<E>::type, std::exception_ptr>::value,
    cancellation_type_t
  >::type operator()(const E&, Args&&...) const noexcept
  {
    return cancellation_type::none;
  }

  template <typename E, typename... Args>
  constexpr typename constraint<
      std::is_same<typename std::decay<E>::type, asio::error_code>::value
        || std::is_same<typename std::decay<E>::type, std::exception_ptr>::value,
      cancellation_type_t
  >::type operator()(const E& e, Args&&...) const noexcept
  {
    return !!e ? cancel_type_ : cancellation_type::none;
  }

private:
  cancellation_type_t cancel_type_;
};

} // namespace experimental
} // namespace asio

