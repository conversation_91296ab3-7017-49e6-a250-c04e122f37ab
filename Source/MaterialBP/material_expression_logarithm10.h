#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionLogarithm10 : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Logarithm10";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Input;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross