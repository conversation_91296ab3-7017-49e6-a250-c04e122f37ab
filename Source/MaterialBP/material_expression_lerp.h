#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionLerp : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Lerp";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_A;

    CEProperty(Reflect)
    ExpressionInput m_B;

    CEProperty(Reflect)
    ExpressionInput m_Alpha;

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect, meta(OverrideInputProperty = m_A))
    float m_ConstA;

    CEProperty(Reflect, meta(OverrideInputProperty = m_B))
    float m_ConstB;

    CEProperty(Reflect, meta(OverrideInputProperty = m_Alpha))
    float m_ConstAlpha;
};
}   // namespace cross