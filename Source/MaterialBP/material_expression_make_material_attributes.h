#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionMakeMaterialAttributes : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "MakeMaterialAttributes";
    }

public:

    CEProperty(Reflect)
    ExpressionInput m_BaseColor;

    CEProperty(Reflect)
    ExpressionInput m_Metallic;

    CEProperty(Reflect)
    ExpressionInput m_Specular;

    CEProperty(Reflect)
    ExpressionInput m_Roughness;

    CEProperty(Reflect)
    ExpressionInput Anisotropy;

    CEProperty(Reflect)
    ExpressionInput m_EmissiveColor;

    CEProperty(Reflect)
    ExpressionInput m_Opacity;

    CEProperty(Reflect)
    ExpressionInput m_OpacityMask;

    CEProperty(Reflect)
    ExpressionInput m_Normal;

    CEProperty(Reflect)
    ExpressionInput m_Tangent;

    CEProperty(Reflect)
    ExpressionInput m_WorldPositionOffset;

    CEProperty(Reflect)
    ExpressionInput m_SubsurfaceColor;

    CEProperty(Reflect)
    ExpressionInput m_AmbientOcclusion;

    CEProperty(Reflect)
    ExpressionInput m_ShadingModel;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross