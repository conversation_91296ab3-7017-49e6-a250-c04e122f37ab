#pragma once

#include "material_expression.h"

namespace cross {

enum class CEMeta(Cli) MaterialLuminanceMode
{
    ACEScg,
    Rec709,
    Rec2020,
    Rec2100 = Rec2020,
    Custom
};

class CEMeta(Cli) Material_API MaterialExpressionLuminance : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    std::string GetCaption() const override
    {
        return "Luminance";
    }
    
    void DoHandlePropertyChange(IMaterialEditor * editor) override;

public:
    CEProperty(Reflect)
    ExpressionInput m_Input;

    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "Float3AsColor"))
    Float3 m_LuminanceFactors;

    CEProperty(Reflect)
    MaterialLuminanceMode m_LuminanceMode;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross
