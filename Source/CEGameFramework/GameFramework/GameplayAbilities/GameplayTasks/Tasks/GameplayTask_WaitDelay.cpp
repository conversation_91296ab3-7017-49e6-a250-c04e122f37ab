// Copyright Epic Games, Inc. All Rights Reserved.

#include "GameplayTask_WaitDelay.h"
#include "GameFramework/GameWorld.h"
namespace cegf {


    //#include UE_INLINE_GENERATED_CPP_BY_NAME(GameplayTask_WaitDelay)

    UGameplayTask_WaitDelay::UGameplayTask_WaitDelay()
    {
        Time = 0.f;
        TimeStarted = 0.f;
    }

    std::shared_ptr<UGameplayTask_WaitDelay> UGameplayTask_WaitDelay::TaskWaitDelay(IGameplayTaskOwnerInterface* TaskOwner, float Time, const uint8_t Priority)
    {
        std::shared_ptr<UGameplayTask_WaitDelay> MyTask = NewTaskUninitialized<UGameplayTask_WaitDelay>();
        if (MyTask && TaskOwner != nullptr)
        {
            MyTask->InitTask(*TaskOwner, Priority);
            MyTask->Time = Time;
        }
        return MyTask;
    }

    std::shared_ptr<UGameplayTask_WaitDelay> UGameplayTask_WaitDelay::TaskWaitDelay(IGameplayTaskOwnerInterface& InTaskOwner, float Time, const uint8_t Priority)
    {
        std::shared_ptr<UGameplayTask_WaitDelay> MyTask = NewTaskUninitialized<UGameplayTask_WaitDelay>();
        if (MyTask)
        {
            MyTask->InitTask(InTaskOwner, Priority);
            MyTask->Time = Time;
        }
        return MyTask;
    }

    void UGameplayTask_WaitDelay::Activate()
    {
        GameWorld* World = GetWorld();
        TimeStarted = World->GetTimeSeconds();

        if (Time <= 0.0f)
        {
            cross::FTimerManager::GetInstance().SetTimerForNextTick([this]() {OnTimeFinish(); });
        }
        else
        {
            // Use a dummy timer handle as we don't need to store it for later but we don't need to look for something to clear
            cross::FTimerHandle TimerHandle;
            cross::FTimerManager::GetInstance().SetTimer(TimerHandle, [this]() {OnTimeFinish(); }, (float)Time, false);
        }
    }

    void UGameplayTask_WaitDelay::OnTimeFinish()
    {
        OnFinish.Broadcast();
        EndTask();
    }

    std::string UGameplayTask_WaitDelay::GetDebugString() const
    {
        double TimeLeft = Time - GetWorld()->TimeSince(TimeStarted);
        return fmt::format("WaitDelay. Time: {:.2f}. TimeLeft: {:.2f}", Time, TimeLeft);
    }

}