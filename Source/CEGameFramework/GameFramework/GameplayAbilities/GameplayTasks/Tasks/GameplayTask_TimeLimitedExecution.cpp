#include "GameplayTask_TimeLimitedExecution.h"
#include "CrossBase/TimerManager.h"
#include "GameFramework/GameWorld.h"

//#include UE_INLINE_GENERATED_CPP_BY_NAME(GameplayTask_TimeLimitedExecution)
namespace cegf {

	UGameplayTask_TimeLimitedExecution::UGameplayTask_TimeLimitedExecution()
	{
		Time = 0.f;
		TimeStarted = 0.f;

		bTimeExpired = false;
		bChildTaskFinished = false;
	}

	std::shared_ptr<UGameplayTask_TimeLimitedExecution> UGameplayTask_TimeLimitedExecution::LimitExecutionTime(IGameplayTaskOwnerInterface& InTaskOwner, float Time, const uint8_t Priority, const cross::UniqueString InInstanceName)
	{
		if (Time <= 0.f)
		{
			return nullptr;
		}

		std::shared_ptr<UGameplayTask_TimeLimitedExecution> MyTask = NewTaskUninitialized<UGameplayTask_TimeLimitedExecution>();
		if (MyTask)
		{
			MyTask->InstanceName = InInstanceName;
			MyTask->InitTask(InTaskOwner, Priority);
			MyTask->Time = Time;
		}
		return MyTask;
	}

	void UGameplayTask_TimeLimitedExecution::Activate()
	{
		// bail when there's no child task
		if (ChildTask == nullptr)
		{
			EndTask();
			return;
		}

		GameWorld* World = GetWorld();
		TimeStarted = World->GetTimeSeconds();

		// Use a dummy timer handle as we don't need to store it for later but we don't need to look for something to clear
        cross::FTimerHandle TimerHandle;
		cross::FTimerManager::GetInstance().SetTimer(TimerHandle, [this]() {
			OnTimer();
		} , (float)Time, false);

		LOG_INFO("{}> started timeout: {:.2f}fs for task:{}", GetName(), Time, ChildTask->GetName());
		//UE_VLOG(GetGameplayTasksComponent(), LogGameplayTasks, Verbose, GAS_TEXT("%s> started timeout: %.2fs for task:%s"), *GetName(), Time, *ChildTask->GetName());

		if (!ChildTask->IsActive())
		{
			ChildTask->ReadyForActivation();
		}
	}

	void UGameplayTask_TimeLimitedExecution::OnGameplayTaskActivated(UGameplayTask& Task)
	{
		if (!IsActive())
		{
			ReadyForActivation();
		}
	}

	void UGameplayTask_TimeLimitedExecution::OnGameplayTaskDeactivated(UGameplayTask& Task)
	{
		//Super::OnGameplayTaskDeactivated(Task);

		if (Task.IsFinished())
		{
			if (!bTimeExpired && !bChildTaskFinished)
			{
				OnFinished.Broadcast();
			}

			bChildTaskFinished = true;
			EndTask();
		}
	}

	void UGameplayTask_TimeLimitedExecution::OnTimer()
	{
		if (!bTimeExpired && !bChildTaskFinished)
		{
			LOG_INFO("{}> time expired!", GetName());
			//UE_VLOG(GetGameplayTasksComponent(), LogGameplayTasks, Verbose, GAS_TEXT("%s> time expired!"), *GetName());
			OnTimeExpired.Broadcast();
		}

		bTimeExpired = true;
		EndTask();
	}

	std::string UGameplayTask_TimeLimitedExecution::GetDebugString() const
	{
		const double TimeLeft = Time - GetWorld()->TimeSince(TimeStarted);

		return fmt::format("TimeLimit for {}. Time: {:.2f}. TimeLeft: {:.2f}", GetChildTask()->GetName(), Time, TimeLeft);
	}

}