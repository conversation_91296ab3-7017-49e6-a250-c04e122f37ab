#include "GASTestObject.h"
#include "GameObjects/GameObject.h"
#include "GameWorld.h"

#include "AbilitySystemTestAttributeSet.h"
#include "GASTestGameAbility.h"
#include "GameEngine.h"
#define CONSTRUCT_CLASS(Class, Name)                                                                                                                                                                                                           \
    auto Name##Holder = NewObject<Class>();                                                                                                                                                                                                            \
    if (!Name##Holder)                                                                                                                                                                                                                                 \
    {                                                                                                                                                                                                                                          \
        LOG_ERROR("Failed to create object of type {}", #Class);                                                                                                                                                                               \
                                                                                                                                                                                                                                        \
    }                                                                                                                                                                                                                                          \
    Name##Holder->SetName(GAS_TEXT(#Name));                                                                                                                                                \
    auto Name = Name##Holder.get();                                                                                                                                                                                                                  \

#define GET_FIELD_CHECKED(Class, Field) const_cast<gbf::reflection::Property*>(& (gbf::reflection::query_meta_class<Class>()->GetProperty(#Field)))

namespace cegf {

template<typename MODIFIER_T>
FGameplayModifierInfo& AddModifier(UGameplayEffect* Effect, gbf::reflection::Property* Property, EGameplayModOp::Type Op, const MODIFIER_T& Magnitude)
{
    int32 Idx = Effect->Modifiers.size();
    Effect->Modifiers.resize(Idx + 1);
    FGameplayModifierInfo& Info = Effect->Modifiers[Idx];
    Info.ModifierMagnitude = Magnitude;
    Info.ModifierOp = Op;
    Info.Attribute.SetUProperty(Property);
    return Info;
}

bool GASTestObject::Deserialize(const DeserializeNode& in, SerializeContext& context)
{
    return GameObject::Deserialize(in, context);
}

GASTestObject::GASTestObject()
    : GameObject()
{
    mTickFunction->bCanEverTick = true;
    mTickFunction->bCanTickInEditorViewport = true;
}

class ScopedTestLocker
{
public:
    ScopedTestLocker(GASTestObject* InObj)
    {
        mTestObject = InObj;
        mTestObject->InitTestData();
    }

    ~ScopedTestLocker() { mTestObject->RemoveTestData(); }
    GASTestObject* mTestObject;
};

GASTestObject::~GASTestObject() {}

void GASTestObject::InitializeComponents()
{
    GameObject::InitializeComponents();
}

void GASTestObject::PostInitializeComponents()
{
    GameObject::PostInitializeComponents();
    World = gGameEngine->CreateGameWorld("GASTestWorld", cross::WorldTypeTag::DefaultWorld);

    Test = std::make_shared<LocalTester>();
}

void GASTestObject::Serialize(SerializeNode& node, SerializeContext& context) const
{
    
}

void GASTestObject::InitTestData()
{
    TSubclassOf<UAbilitySystemTestAttributeSet> SubClass = {gbf::reflection::query_meta_class<UAbilitySystemTestAttributeSet>()};

    SourceObject = World->CreateGameObject<GameObject>({}, {}, {1, 1, 1});
    SourceComponent = SourceObject->AddComponent<UAbilitySystemComponent>();
    SourceComponent->InitStats(SubClass, nullptr);

    DestObject = World->CreateGameObject<GameObject>({}, {}, {1, 1, 1});
    DestComponent = DestObject->AddComponent<UAbilitySystemComponent>();
    DestComponent->InitStats(SubClass, nullptr);

    const float StartingHealth = 100.f;
    const float StartingMana = 200.f;

    SourceComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health = StartingHealth;
    SourceComponent->GetSet<UAbilitySystemTestAttributeSet>()->MaxHealth = StartingHealth;
    SourceComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana = FGameplayAttributeData(StartingMana);
    SourceComponent->GetSet<UAbilitySystemTestAttributeSet>()->MaxMana = StartingMana;
    SourceComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute1 = 0;
    SourceComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute2 = 0;

    DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health = StartingHealth;
    DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->MaxHealth = StartingHealth;
    DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana = FGameplayAttributeData(StartingMana);
    DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->MaxMana = StartingMana;
    DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute1 = 0;
    DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute2 = 0;

    NumTest++;

    {
        TSubclassOf<UGameplayAbility> AbilityClass = { gbf::reflection::query_meta_class<UGASTestGameAbility>() };
        SourceComponent->GiveAbility(FGameplayAbilitySpec(AbilityClass, 1, INDEX_NONE, this));
    }
}

void GASTestObject::RemoveTestData() {
    if (SourceObject)
    {
        World->DestroyGameObject(SourceObject.get());
    }
    if (DestObject)
    {
        World->DestroyGameObject(DestObject.get());
    }
    cross::FTimerManager::GetInstance().ClearAllTimers();
}
void GASTestObject::Tick(float deltaTime)
{
    RunGameplayEffectTest();
    RunGameplayAbilityTest();
}

void GASTestObject::TickWorld(float Time)
{
    const float step = 0.1f;
    while (Time > 0.f)
    {
        World->Tick(cross::MathUtils::Min(Time, step));
        Time -= step;
    }
}

UTestExecutionCalculation::UTestExecutionCalculation()
{
    RelevantAttributesToCapture.push_back({GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, Health), EGameplayEffectAttributeCaptureSource::Target, false});
    RelevantAttributesToCapture.push_back({GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, Damage), EGameplayEffectAttributeCaptureSource::Target, false});
}

void UTestExecutionCalculation::Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
    FAggregatorEvaluateParameters EvalParams;

    const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();

    auto* Set = ExecutionParams.GetTargetAbilitySystemComponent()->GetSet<UAbilitySystemTestAttributeSet>();
   
    float ValueHealth = Set->Health;
    float ValueDamage = Set->Damage;
    float AdditionalValue = 1.1f;

    // (ValueA + ValueB) * AdditionalValue
    float Result = (ValueHealth - ValueDamage) * AdditionalValue;

    OutExecutionOutput.AddOutputModifier(FGameplayModifierEvaluatedData(GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, Health), EGameplayModOp::Override, Result));
}

void GASTestObject::RunGameplayEffectTest()
{
    ScopedTestLocker Locker(this);
    if (!SourceComponent)
    {
        return;
    }

    // Test_InstantDamage
    {
        const float DamageValue = 5.f;
        const float StartingHealth = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health;
        {
            CONSTRUCT_CLASS(UGameplayEffect, BaseDmgEffect);
            AddModifier(BaseDmgEffect, GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, Health), EGameplayModOp::Additive, FScalableFloat(-DamageValue));
            BaseDmgEffect->DurationPolicy = EGameplayEffectDurationType::Instant;
        
            SourceComponent->ApplyGameplayEffectToTarget(BaseDmgEffect, DestComponent, 1.f);
        }
    
        // make sure health was reduced
        Test->TestEqual(GAS_TEXT("Health Reduced"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health, StartingHealth - DamageValue);
    }
    
    // Test_InstantDamageRemap
    {
        const float DamageValue = 5.f;
        const float StartingHealth = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health;
        LOG_INFO("Starting Health: {}", StartingHealth);
        {
            // This is the same as GameplayEffectsTest_InstantDamage but modifies the Damage attribute and confirms it is remapped to -Health by UAbilitySystemTestAttributeSet::PostAttributeModify
    
            CONSTRUCT_CLASS(UGameplayEffect, BaseDmgEffect);
            AddModifier(BaseDmgEffect, GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, Damage), EGameplayModOp::Additive, FScalableFloat(DamageValue));
            BaseDmgEffect->DurationPolicy = EGameplayEffectDurationType::Instant;
    
            SourceComponent->ApplyGameplayEffectToTarget(BaseDmgEffect, DestComponent, 1.f);
        }
    
        // Now we should have lost some health
        Test->TestEqual(GAS_TEXT("Health Reduced"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health, StartingHealth - DamageValue);
    
        // make sure health was reduced
        Test->TestEqual(GAS_TEXT("Health Reduced"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health, StartingHealth - DamageValue);
    }
    
    // Test_ManaBuff()
    {
        const float BuffValue = 30.f;
        const float StartingMana = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetCurrentValue();
    
        FActiveGameplayEffectHandle BuffHandle;
    
        // apply the buff
        {
            CONSTRUCT_CLASS(UGameplayEffect, DamageBuffEffect);
            AddModifier(DamageBuffEffect, GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, Mana), EGameplayModOp::Additive, FScalableFloat(BuffValue));
            DamageBuffEffect->DurationPolicy = EGameplayEffectDurationType::Infinite;
    
            BuffHandle = SourceComponent->ApplyGameplayEffectToTarget(DamageBuffEffect, DestComponent, 1.f);
        }
    
        // check that the value changed
        Test->TestEqual(GAS_TEXT("Mana Buffed"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetCurrentValue(), StartingMana + BuffValue);
    
        // remove the effect
        {
            DestComponent->RemoveActiveGameplayEffect(BuffHandle);
        }
    
        // check that the value changed back
        Test->TestEqual(GAS_TEXT("Mana Restored"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetCurrentValue(), StartingMana);
    }
    //void Test_AttributeAggregators()
    {
        constexpr float BuffValue = 2.0f;
        const float ManaBaseValue = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetBaseValue();
    
        // Define a common lambda for applying a GE with a specific GameplayModOp
        auto ApplyGameplayModOp = [this, BuffValue](EGameplayModOp::Type GameplayModOp) -> FActiveGameplayEffectHandle {
            CONSTRUCT_CLASS(UGameplayEffect, DamageBuffEffect);
            AddModifier(DamageBuffEffect, GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, Mana), GameplayModOp, FScalableFloat(BuffValue));
            DamageBuffEffect->DurationPolicy = EGameplayEffectDurationType::Infinite;
    
            return SourceComponent->ApplyGameplayEffectToTarget(DamageBuffEffect, DestComponent, 1.f);
        };
    
        // Define a common lambda for testing if an op applied correctly
        auto TestGameplayModOp = [this, &ApplyGameplayModOp, BuffValue](EGameplayModOp::Type GameplayModOp) {
            const float PrevValue = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetCurrentValue();
            const float ExpectedValue = FAggregator::StaticExecModOnBaseValue(PrevValue, GameplayModOp, BuffValue);
    
            FActiveGameplayEffectHandle AGEHandle = ApplyGameplayModOp(GameplayModOp);
            const float CurrentValue = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetCurrentValue();
            Test->TestEqual(fmt::format("Attribute GameplayModOp {}", GameplayEffectUtilities::EGameplayModOpToString(GameplayModOp)), CurrentValue, ExpectedValue);
    
            return AGEHandle;
        };
    
        // Test all of the ops (order matters here due to the implementation of the above StaticExecModOnBaseValue)
        FActiveGameplayEffectHandle AdditiveHandle = TestGameplayModOp(EGameplayModOp::Additive);
        FActiveGameplayEffectHandle MultiplicativeHandle = TestGameplayModOp(EGameplayModOp::Multiplicitive);
        FActiveGameplayEffectHandle DivisionHandle = TestGameplayModOp(EGameplayModOp::Division);
        FActiveGameplayEffectHandle CompoundHandle = TestGameplayModOp(EGameplayModOp::MultiplyCompound);
        FActiveGameplayEffectHandle FinalAddHandle = TestGameplayModOp(EGameplayModOp::AddFinal);
    
        // Test the override quickly here before testing the aggregation is as expected
        {
            FActiveGameplayEffectHandle OverrideHandle = TestGameplayModOp(EGameplayModOp::Override);
            DestComponent->RemoveActiveGameplayEffect(OverrideHandle);
        }
    
        Test->TestEqual(GAS_TEXT("Mana BaseValue is Unchanged"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetBaseValue(), ManaBaseValue);
    
        // Add some compounding GameplayEffects and manually test our equation to see if it's all compounded correctly.
        {
            FActiveGameplayEffectHandle BaseAdd2 = ApplyGameplayModOp(EGameplayModOp::Additive);
            FActiveGameplayEffectHandle BaseMultiply2 = ApplyGameplayModOp(EGameplayModOp::Multiplicitive);
            FActiveGameplayEffectHandle Compound2 = ApplyGameplayModOp(EGameplayModOp::MultiplyCompound);
            FActiveGameplayEffectHandle FinalAdd2 = ApplyGameplayModOp(EGameplayModOp::AddFinal);
    
            // ExpectedResult = ((BaseValue + Additive) * Multiplicative / Division * CompoundMultiply) + FinalAdd;
            // Multiplicative and Division Compound as: 1.0f + ForEachValue(Value - 1.0f). E.g. two applications of 1.5 = 2.
            // CompoundMultiply Compounds as: 1.0f *= ForEachValue(Value) e.g. two applications of 1.5 = 2.25.
            constexpr float X = BuffValue;
            const float ExpectedResult = ((ManaBaseValue + X + X) * (1.0f + (X - 1.0f) + (X - 1.0f)) / (1.0f + (X - 1.0f)) * (X * X)) + X + X;
            const float CurrentValue = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetCurrentValue();
            Test->TestEqual(GAS_TEXT("Aggregation Equation Works as Expected"), CurrentValue, ExpectedResult);
    
            // Remove the compounding GE's
            DestComponent->RemoveActiveGameplayEffect(FinalAdd2);
            DestComponent->RemoveActiveGameplayEffect(Compound2);
            DestComponent->RemoveActiveGameplayEffect(BaseMultiply2);
            DestComponent->RemoveActiveGameplayEffect(BaseAdd2);
        }
    
        // Remove the base GE's
        {
            DestComponent->RemoveActiveGameplayEffect(FinalAddHandle);
            DestComponent->RemoveActiveGameplayEffect(CompoundHandle);
            DestComponent->RemoveActiveGameplayEffect(DivisionHandle);
            DestComponent->RemoveActiveGameplayEffect(MultiplicativeHandle);
            DestComponent->RemoveActiveGameplayEffect(AdditiveHandle);
        }
    
        // check that the value changed back
        Test->TestEqual(GAS_TEXT("Mana Restored to BaseValue"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Mana.GetCurrentValue(), ManaBaseValue);
    }
    
    //void Test_PeriodicDamage()
    {
        const int32 NumPeriods = 10;
        const float PeriodSecs = 1.0f;
        const float DamagePerPeriod = 5.f;
        const float StartingHealth = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health;
    
        // just try and reduce the health attribute
        {
            CONSTRUCT_CLASS(UGameplayEffect, BaseDmgEffect);
            AddModifier(BaseDmgEffect, GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, Health), EGameplayModOp::Additive, FScalableFloat(-DamagePerPeriod));
            BaseDmgEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
            BaseDmgEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(NumPeriods * PeriodSecs));
            BaseDmgEffect->Period.Value = PeriodSecs;
    
            SourceComponent->ApplyGameplayEffectToTarget(BaseDmgEffect, DestComponent, 1.f);
        }
    
        int32 NumApplications = 0;
    
        // Tick a small number to verify the application tick
        TickWorld(cross::MathUtils::MathSmallNumber);
        ++NumApplications;
    
        Test->TestEqual(GAS_TEXT("Health Reduced"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health, StartingHealth - (DamagePerPeriod * NumApplications));
    
        // Tick a bit more to address possible floating point issues
        TickWorld(PeriodSecs * .1f);
    
        for (int32 i = 0; i < NumPeriods; ++i)
        {
            // advance time by one period
            TickWorld(PeriodSecs);
    
            ++NumApplications;
    
            // check that health has been reduced
            Test->TestEqual(GAS_TEXT("Health Reduced"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health, StartingHealth - (DamagePerPeriod * NumApplications));
        }
    
        // advance time by one extra period
        TickWorld(PeriodSecs);
    
        // should not have reduced further
        Test->TestEqual(GAS_TEXT("Health Reduced"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health, StartingHealth - (DamagePerPeriod * NumApplications));
    
        // TODO: test that the effect is no longer applied
    }
    
    //void Test_StackLimit()
    {
        const float Duration = 10.0f;
        const float HalfDuration = Duration / 2.f;
        const float ChangePerGE = 5.f;
        const uint32 StackLimit = 2;
        const float StartingAttributeValue = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute1;
    
        // Apply one copy of the stacking GE
        CONSTRUCT_CLASS(UGameplayEffect, StackingEffect);
        AddModifier(StackingEffect, GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, StackingAttribute1), EGameplayModOp::Additive, FScalableFloat(ChangePerGE));
        StackingEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
        StackingEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(Duration));
        StackingEffect->StackLimitCount = StackLimit;
        StackingEffect->StackingType = EGameplayEffectStackingType::AggregateByTarget;
        StackingEffect->StackDurationRefreshPolicy = EGameplayEffectStackingDurationPolicy::NeverRefresh;
        StackingEffect->StackExpirationPolicy = EGameplayEffectStackingExpirationPolicy::ClearEntireStack;
    
        // Apply the GE StackLimit + 1 times
        for (uint32 Idx = 0; Idx <= StackLimit; ++Idx)
        {
            SourceComponent->ApplyGameplayEffectToTarget(StackingEffect, DestComponent, 1.f);
        }
    
        Test->TestEqual(GAS_TEXT("Stacking GEs"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute1, StartingAttributeValue + (StackLimit * ChangePerGE));
    }

    //void Test_SetByCallerStackingDuration()
    {
        const float Duration = 10.0f;
        const float HalfDuration = Duration / 2.f;
        const float ChangePerGE = 5.f;
        const uint32 StackLimit = 2;
        const float StartingAttributeValue = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute2;
    
        const FName DurationName(GAS_TEXT("Duration"));
        FSetByCallerFloat SetByCallerDuration;
        SetByCallerDuration.DataName = DurationName;
    
        // Apply one copy of the stacking GE
        CONSTRUCT_CLASS(UGameplayEffect, StackingEffect);
        AddModifier(StackingEffect, GET_FIELD_CHECKED(UAbilitySystemTestAttributeSet, StackingAttribute2), EGameplayModOp::Additive, FScalableFloat(ChangePerGE));
        StackingEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
        StackingEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(SetByCallerDuration);
        StackingEffect->StackLimitCount = StackLimit;
        StackingEffect->StackingType = EGameplayEffectStackingType::AggregateByTarget;
        StackingEffect->StackDurationRefreshPolicy = EGameplayEffectStackingDurationPolicy::NeverRefresh;
        StackingEffect->StackExpirationPolicy = EGameplayEffectStackingExpirationPolicy::RemoveSingleStackAndRefreshDuration;
    
        // create a spec, set the magnitude and apply the GE
        {
            FGameplayEffectSpec Spec(StackingEffect, FGameplayEffectContextHandle(), 1.f);
            Spec.SetSetByCallerMagnitude(DurationName, Duration);
            SourceComponent->ApplyGameplayEffectSpecToTarget(Spec, DestComponent, FPredictionKey());
        }
    
        // Tick to partway through the GE's duration and apply a second copy of the GE
        TickWorld(HalfDuration);
    
        Test->TestEqual(GAS_TEXT("Stacking GEs"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute2, StartingAttributeValue + ChangePerGE);
    
        // apply second copy of GE
        {
            FGameplayEffectSpec Spec(StackingEffect, FGameplayEffectContextHandle(), 1.f);
            Spec.SetSetByCallerMagnitude(DurationName, Duration);
            SourceComponent->ApplyGameplayEffectSpecToTarget(Spec, DestComponent, FPredictionKey());
        }
    
        Test->TestEqual(GAS_TEXT("Stacking GEs"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute2, StartingAttributeValue + (2 * ChangePerGE));
    
        // Tick to just after the first GE should have expired
        TickWorld(HalfDuration + 0.1f);
    
        // we should have removed one copy and still have the second copy
        Test->TestEqual(GAS_TEXT("Stacking GEs"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute2, StartingAttributeValue + ChangePerGE);
    
        // check again near the end of the remaining GE's duration
        TickWorld(Duration - 0.2f);
    
        Test->TestEqual(GAS_TEXT("Stacking GEs"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute2, StartingAttributeValue + ChangePerGE);
    
        // Tick to a point just after where the remaining GE should have been removed
        TickWorld(0.2f);
    
        Test->TestEqual(GAS_TEXT("Stacking GEs"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->StackingAttribute2, StartingAttributeValue);
    }

    // Test_InstantDamage
    {
        const float DamageValue = 5.f;
        const float AdditionValue = 1.1f;
        const float StartingHealth = DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health;
        DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Damage = DamageValue;
        {
            CONSTRUCT_CLASS(UGameplayEffect, BaseDmgEffect);
            FGameplayEffectExecutionDefinition ExecDef;
            ExecDef.CalculationClass = TSubclassOf<UGameplayEffectExecutionCalculation>(gbf::reflection::query_meta_class<UTestExecutionCalculation>());
            BaseDmgEffect->Executions.push_back(ExecDef);
            BaseDmgEffect->DurationPolicy = EGameplayEffectDurationType::Instant;
            SourceComponent->ApplyGameplayEffectToTarget(BaseDmgEffect, DestComponent, 1.f);

        }

        // make sure health was reduced
        Test->TestEqual(GAS_TEXT("Health Reduced"), DestComponent->GetSet<UAbilitySystemTestAttributeSet>()->Health, (StartingHealth - DamageValue) * AdditionValue);
    }
}

void GASTestObject::RunGameplayAbilityTest()
{
    ScopedTestLocker Locker(this);
    if (!SourceComponent)
    {
        return;
    }

    {
        TSubclassOf<UGameplayAbility> SubClass = { gbf::reflection::query_meta_class<UGASTestGameAbility>() };
        SourceComponent->TryActivateAbilityByClass(SubClass);
    }
}

void GASTestObject::RunGameplayGameplayTagTest(){}



}   // namespace cegf