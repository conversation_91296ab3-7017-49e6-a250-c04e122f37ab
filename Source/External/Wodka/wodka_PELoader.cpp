// Written by <PERSON><PERSON>
// I hereby place this code in the public domain

#include "wodka_PELoader.h"
#include "wodka_WinHelper.h"
#include "wodka_KnownImports.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <errno.h>
#ifdef __APPLE__
#include <i386/user_ldt.h>
#include <architecture/i386/desc.h>
#endif
#if WODKA_LINUX
#undef NDEBUG
#include <unistd.h>
#include <sys/syscall.h>
#include <sys/types.h>
#include <asm/ldt.h>
#endif
#if WODKA_WINDOWS
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#else
#include <sys/mman.h>
#include <pthread.h>
#endif
#include <assert.h>

#define DEBUG_IMPORT_EXPORT 0

// -------------------------------------------------------------------

#define kMapFailed ((void*)-1L)


void* MapExecMemory (size_t bytes)
{
#if WODKA_WINDOWS
	void* res = VirtualAlloc (nullptr, bytes, MEM_COMMIT|MEM_RESERVE, PAGE_EXECUTE_READWRITE);
	if (res == nullptr)
		return kMapFailed;
	return res;
#else
	return mmap (0, bytes, PROT_READ|PROT_WRITE|PROT_EXEC, MAP_ANON|MAP_PRIVATE, 0, 0);
#endif
}

void UnmapExecMemory (void* ptr, size_t bytes)
{
#if WODKA_WINDOWS
	if (ptr != kMapFailed)
		VirtualFree (ptr, 0, MEM_RELEASE);
#else
	munmap (ptr, bytes);
#endif
}


// -------------------------------------------------------------------


#define IMAGE_DOS_SIGNATURE             0x5A4D      // MZ
#define IMAGE_NT_SIGNATURE              0x00004550  // PE00
#define IMAGE_NT_OPTIONAL_HDR32_MAGIC      0x10b
#define IMAGE_NT_OPTIONAL_HDR64_MAGIC      0x20b
#define IMAGE_SIZEOF_SHORT_NAME   8

#define PE_IMAGE_NUMBEROF_DIRECTORY_ENTRIES 16

#define FIELD_OFFSET_IMPL(type,fld) ((DWORD_PTR)&(((type*)0)->fld))

#define IMAGE_FIRST_SECTION_IMPL(h) ((PE_IMAGE_SECTION_HEADER*) ((DWORD_PTR)h+FIELD_OFFSET_IMPL(PE_IMAGE_NT_HEADERS,OptionalHeader)+((PE_IMAGE_NT_HEADERS*)(h))->FileHeader.sizeOfOptionalHeader))
#define IMAGE_SIZEOF_BASE_RELOCATION 8
#define IMAGE_REL_BASED_HIGHLOW 3
#define IMAGE_REL_BASED_DIR64 10

#define IMAGE_DIRECTORY_ENTRY_EXPORT 0
#define IMAGE_DIRECTORY_ENTRY_IMPORT 1
#define IMAGE_DIRECTORY_ENTRY_BASERELOC 5


struct PE_IMAGE_DOS_HEADER {
	WORD magic;
	WORD lastPageBytes;
	WORD pageCount;
	WORD relocations;
	WORD headerParaSize;
	WORD minAlloc;
	WORD maxAlloc;
	WORD initialSS;
	WORD initialSP;
	WORD checksum;
	WORD initialIP;
	WORD initialCS;
	WORD relocAddr;
	WORD overlay;
	WORD reserved[4];
	WORD oemID;
	WORD oemInfo;
	WORD reserved2[10];
	LONG ntHeader;
};

struct PE_IMAGE_FILE_HEADER {
	WORD  machine;
	WORD  numberOfSections;
	DWORD timeDateStamp;
	DWORD pointerToSymbolTable;
	DWORD numberOfSymbols;
	WORD  sizeOfOptionalHeader;
	WORD  characteristics;
};

struct PE_IMAGE_DATA_DIRECTORY {
    ULONG va;
    ULONG size;
};

struct PE_IMAGE_OPTIONAL_HEADER {
	WORD    Magic;
	BYTE    MajorLinkerVersion;
	BYTE    MinorLinkerVersion;
	DWORD   SizeOfCode;
	DWORD   SizeOfInitializedData;
	DWORD   SizeOfUninitializedData;
	DWORD   AddressOfEntryPoint;
	DWORD   BaseOfCode;
#if !WODKA_64
	DWORD   BaseOfData;
#endif
	size_t	ImageBase;

	DWORD   SectionAlignment;
	DWORD   FileAlignment;
	WORD    MajorOperatingSystemVersion;
	WORD    MinorOperatingSystemVersion;
	WORD    MajorImageVersion;
	WORD    MinorImageVersion;
	WORD    MajorSubsystemVersion;
	WORD    MinorSubsystemVersion;
	DWORD   Win32VersionValue;
	DWORD   SizeOfImage;
	DWORD   SizeOfHeaders;
	DWORD   CheckSum;
	WORD    Subsystem;
	WORD    DllCharacteristics;
	size_t  SizeOfStackReserve;
	size_t  SizeOfStackCommit;
	size_t  SizeOfHeapReserve;
	size_t  SizeOfHeapCommit;
	DWORD   LoaderFlags;
	DWORD   NumberOfRvaAndSizes;
	PE_IMAGE_DATA_DIRECTORY DataDirectory[PE_IMAGE_NUMBEROF_DIRECTORY_ENTRIES];
};

struct PE_IMAGE_NT_HEADERS {
	DWORD Signature;
	PE_IMAGE_FILE_HEADER FileHeader;
	PE_IMAGE_OPTIONAL_HEADER OptionalHeader;
};

struct PE_IMAGE_SECTION_HEADER {
	BYTE  Name[IMAGE_SIZEOF_SHORT_NAME];
	union {
		DWORD PhysicalAddress;
		DWORD VirtualSize;
	} Misc;
	DWORD VirtualAddress;
	DWORD SizeOfRawData;
	DWORD PointerToRawData;
	DWORD PointerToRelocations;
	DWORD PointerToLinenumbers;
	WORD  NumberOfRelocations;
	WORD  NumberOfLinenumbers;
	DWORD Characteristics;
};

struct PE_IMAGE_EXPORT_DIRECTORY {
	DWORD   Characteristics;
	DWORD   TimeDateStamp;
	WORD    MajorVersion;
	WORD    MinorVersion;
	DWORD   Name;
	DWORD   Base;
	DWORD   NumberOfFunctions;
	DWORD   NumberOfNames;
	DWORD   AddressOfFunctions;     // RVA from base of image
	DWORD   AddressOfNames;         // RVA from base of image
	DWORD   AddressOfNameOrdinals;  // RVA from base of image
};

struct PE_IMAGE_IMPORT_DESCRIPTOR {
	union {
		DWORD   Characteristics;            // 0 for terminating null import descriptor
		DWORD   OriginalFirstThunk;         // RVA to original unbound IAT (PIMAGE_THUNK_DATA)
	};
	DWORD   TimeDateStamp;                  // 0 if not bound,
	// -1 if bound, and real date\time stamp
	//     in IMAGE_DIRECTORY_ENTRY_BOUND_IMPORT (new BIND)
	// O.W. date/time stamp of DLL bound to (Old BIND)
	DWORD   ForwarderChain;                 // -1 if no forwarders
	DWORD   Name;
	DWORD   FirstThunk;                     // RVA to IAT (if bound this IAT has actual addresses)
};

struct PE_IMAGE_IMPORT_BY_NAME {
    WORD    Hint;
    BYTE    Name[1];
};

struct PE_IMAGE_BASE_RELOCATION {
	DWORD va;
	DWORD sizeOfBlock;
};



struct PEModule
{
	BYTE *codeBase;
	size_t codeSize;
	PE_IMAGE_NT_HEADERS* headers;
	BYTE *thunks;
	BYTE *thunksCurr;
	size_t thunksSize;
};


static void CopySections (const BYTE *data, const PE_IMAGE_NT_HEADERS* ntHeaders, PEModule& module)
{
	BYTE *codeBase = module.codeBase;
	PE_IMAGE_SECTION_HEADER* section = IMAGE_FIRST_SECTION_IMPL(module.headers);
	for (int i = 0; i < module.headers->FileHeader.numberOfSections; ++i, ++section)
	{
		if (section->SizeOfRawData == 0)
		{
			// section with uninitialized data only
			int size = ntHeaders->OptionalHeader.SectionAlignment;
			if (size > 0)
			{
				BYTE* dst = codeBase + section->VirtualAddress;
				section->Misc.PhysicalAddress = (DWORD)(size_t)dst;
				memset(dst, 0, size);
			}
		}
		else
		{
			// copy actual data from section
			BYTE* dst = codeBase + section->VirtualAddress;
			memcpy (dst, data + section->PointerToRawData, section->SizeOfRawData);
			section->Misc.PhysicalAddress = (DWORD)(size_t)dst;
		}
	}
}


static void DumpExports (PEModule& info)
{
	PE_IMAGE_DATA_DIRECTORY& dir = info.headers->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT];
	if (dir.size == 0)
	{
		printf ("  No exports\n");
		return;
	}

	PE_IMAGE_EXPORT_DIRECTORY* exports = (PE_IMAGE_EXPORT_DIRECTORY*)(info.codeBase + dir.va);
	DWORD* nameAddr = (DWORD*)(info.codeBase + exports->AddressOfNames);
	printf ("  Exports %d functions:\n", (int)exports->NumberOfNames);
	for (DWORD i = 0; i < exports->NumberOfNames; ++i, ++nameAddr)
	{
		printf ("    %s\n", (const char*)(info.codeBase + *nameAddr));
	}
}

void WINAPI_IMPL FailedImportProcImpl(const char* func)
{
	printf ("Called unresolved function %s\n", func);
}

typedef void (WINAPI_IMPL *FailedImportProcPtr)(const char* func);
static FailedImportProcPtr s_FailedProcPtr;


void* PECreateCallThunk (PEModule* module, void* func, int unwind, void** destAddr)
{
	void* res = nullptr;
	if (destAddr)
		*destAddr = nullptr;
	
#if WODKA_USE_ABI_THUNKS && WODKA_64
	
	// http://www.agner.org/optimize/calling_conventions.pdf
	// Registers that are scratch on non-Win but need to be preserved on Win:
	// RSI, RDI.
	//
	// Argument passing on Windows:
	// First 4 args passed in RCX, RDX, R8, R9. (floats, XMM0, XMM1, XMM2, XMM3).
	// 32 bytes of space always on stack (equivalent of "space for 4 registers").
	//
	// Argument passing on non-Win:
	// RDI, RSI, RDX, RCX, R8, R9.
	//
	// Return in RAX (floats XMM0) on Windows.
	// RAX, RDX on non-Win.
	
	res = module->thunksCurr;
	BYTE* d = module->thunksCurr;
	
	*d++=0x56;									// push rsi
	*d++=0x56;									// push rsi
	*d++=0x57;									// push rdi
	*d++=0x48;*d++=0x89;*d++=0xCF;				// mov rdi,rcx
	*d++=0x48;*d++=0x89;*d++=0xD6;				// mov rsi,rdx
	*d++=0x4C;*d++=0x89;*d++=0xC2;				// mov rdx,r8
	*d++=0x4C;*d++=0x89;*d++=0xC9;				// mov rcx,r9
	
	// call <func>
	//*d++=0xcc; // int 3, for breaking in debugger
	*d++=0xE8;
	unsigned int relAddr = (unsigned int)((size_t)func - (size_t)(d+4));
	if (destAddr)
		*destAddr = d;
	*(unsigned int*)d = relAddr;
	d+=4;
	
	*d++=0x5F;									// pop rdi
	*d++=0x5E;									// pop rsi
	*d++=0x5E;									// pop rsi
	*d++=0xC3;									// ret
		
	module->thunksCurr = d;

#elif WODKA_USE_ABI_THUNKS && !WODKA_64
	
	// 32 bit ABI. cdecl calling convention mostly fixes things, but then Mac
	// requires stack frames to be 16 byte aligned.
	// llvm-gcc-4.2 just ignores __force_align_arg_pointer__ attribute, so
	// we have to do our own thunks for stack alignment

	// Increase this if we ever need more space for the function arguments!
	const int kArgsSpace = 40;

	res = module->thunksCurr;
	BYTE* d = module->thunksCurr;

	//*d++=0xcc; // int 3, for breaking in debugger

	*d++=0x8d;*d++=0x4c;*d++=0x24;*d++=0x04;	// lea ecx,[esp+4]
	*d++=0x55;									// push ebp
	*d++=0x8b;*d++=0xec;						// mov ebp,esp
	*d++=0x83;*d++=0xec;*d++=kArgsSpace+4;		// sub esp,<size>+4: args + space to save ecx
	*d++=0x83;*d++=0xe4;*d++=0xf0;				// and esp,0xfffffff0
	*d++=0x89;*d++=0x4d;*d++=0xfc;				// mov dword ptr[ebp-4],ecx

	for (int j = 0; j < kArgsSpace; j+=4)
	{
		*d++=0x8b;*d++=0x41;*d++=j;				// mov eax,dword ptr[ecx+j]
		*d++=0x89;*d++=0x44;*d++=0x24;*d++=j;	// mov dword ptr[esp+j], eax
	}

	// call <func>
	*d++=0xe8;
	unsigned relAddr = (unsigned)func - (unsigned)(d+4);
	if (destAddr)
		*destAddr = d;
	*(unsigned*)d = relAddr;
	d+=4;

	*d++=0x8b;*d++=0x4d;*d++=0xfc;				// mov ecx,dword ptr[ebp-4]
	*d++=0xc9;									// leave: ...aka mov esp,ebp, pop ebp
	*d++=0x8d;*d++=0x61;*d++=0xfc;				// lea esp,[ecx-4]
	*d++=0xc2; *(short*)d = unwind; d+=2;		// ret <unwind>

	module->thunksCurr = d;

#else // #if WODKA_USE_ABI_THUNKS

	res = func;

#endif

	return res;
}


static void* PECreateImportThunk (PEModule* module, void* func)
{
	void* res = nullptr;
	
#if WODKA_USE_ABI_THUNKS && WODKA_64
	
	// http://www.agner.org/optimize/calling_conventions.pdf
	// Args setup by caller on non-Win --> where they have to land on Win
	//	RDI		RCX
	//	RSI		RDX
	//	RDX		R8
	//	RCX		R9
	//	R8		RSP+40
	//	R9		RSP+48
	//	RSP+8	RSP+56
	//	RSP+16	RSP+64
	//	RSP+24	RSP+72
	//	RSP+32	RSP+80
	//	RSP+40	RSP+88
	
	res = module->thunksCurr;
	BYTE* d = module->thunksCurr;
	
	const int kArgsSpace = 16*8;
	
	//*d++=0xcc; // int 3, for breaking in debugger

	*d++=0x48;*d++=0x81;*d++=0xEC;*(int*)d=kArgsSpace+8;d+=4; // sub rsp, kArgsSpace+8
	*d++=0x4C;*d++=0x89;*d++=0x44;*d++=0x24;*d++=0x28;	// mov [rsp+40], r8
	*d++=0x4C;*d++=0x89;*d++=0x4C;*d++=0x24;*d++=0x30;	// mov [rsp+48], r9
	*d++=0x49;*d++=0x89;*d++=0xD0;						// mov r8, rdx
	*d++=0x49;*d++=0x89;*d++=0xC9;						// mov r9, rcx
	*d++=0x48;*d++=0x89;*d++=0xF9;						// mov rcx, rdi
	*d++=0x48;*d++=0x89;*d++=0xF2;						// mov rdx, rsi
	
	for (int j = 0; j < kArgsSpace; j+=8)
	{
		*d++=0x48;*d++=0x8B;*d++=0x84;*d++=0x24;*(int*)d=kArgsSpace+8+j;d+=4;	// mov rax, [rsp+kArgsSpace+8+j]
		*d++=0x48;*d++=0x89;*d++=0x84;*d++=0x24;*(int*)d=40+j;d+=4;				// mov [rsp+40+j], rax
	}
	
	// call <func>
	*d++=0xE8;
	unsigned int relAddr = (unsigned int)((size_t)func - (size_t)(d+4));
	*(unsigned int*)d = relAddr;
	d+=4;
	
	*d++=0x48;*d++=0x81;*d++=0xC4;*(int*)d=kArgsSpace+8;d+=4; // add rsp, kArgsSpace+8
	*d++=0xC3;									// ret
	
	module->thunksCurr = d;
	
#else // #if WODKA_USE_ABI_THUNKS && WODKA_64
	
	res = func;
	
#endif
	
	return res;
}


static bool FindMatchingImport(const char* name, PEModule& info, const PEKnownImport* imports, unsigned importCount, size_t* funcPtr)
{
	for (unsigned i = 0; i < importCount; ++i)
	{
		if (strcasecmp(imports[i].name,name) == 0)
		{
			*funcPtr = (size_t)PECreateCallThunk(&info, imports[i].func, imports[i].unwind);
			return true;
		}
	}
	return false;
}

static void ProcessImports (PEModule& info, const PEKnownImport* imports, unsigned importCount)
{
	PE_IMAGE_DATA_DIRECTORY& dir = info.headers->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT];
	if (dir.size == 0)
	{
		printf ("  No imports\n");
		return;
	}
	PE_IMAGE_IMPORT_DESCRIPTOR* importDesc = (PE_IMAGE_IMPORT_DESCRIPTOR*)(info.codeBase + dir.va);

	const int kMaxThunks = 1024;
	const int kBytesPerThunk = 256;
	info.thunksSize = kMaxThunks*kBytesPerThunk;
	info.thunks = (BYTE*)MapExecMemory(info.thunksSize);
	info.thunksCurr = info.thunks;
	memset(info.thunks,0xCC,info.thunksSize);
	s_FailedProcPtr = FailedImportProcImpl;

	while (importDesc->Name)
	{
		size_t *thunkPtr, *funcPtr;
		if (importDesc->OriginalFirstThunk)
			thunkPtr = (size_t*)(info.codeBase + importDesc->OriginalFirstThunk);
		else
			thunkPtr = (size_t*)(info.codeBase + importDesc->FirstThunk);
		funcPtr = (size_t*)(info.codeBase + importDesc->FirstThunk);
		while (*thunkPtr)
		{
			//@TODO: by ordinal?
			PE_IMAGE_IMPORT_BY_NAME* thunkData = (PE_IMAGE_IMPORT_BY_NAME*)(info.codeBase + *thunkPtr);

			// first find in provided table to allow overriding default impls
			bool found = imports && FindMatchingImport((const char*)thunkData->Name, info, imports, importCount, funcPtr);
			if(!found)
				found = FindMatchingImport((const char*)thunkData->Name, info, kWodkaKnownImports, kWodkaKnownImportsCount, funcPtr);

			// dummy import
			if (!found)
			{
			#if DEBUG_IMPORT_EXPORT
				const char* importName = (const char*)(info.codeBase + importDesc->Name);
				printf ("  missing %s : %s\n", importName, thunkData->Name);
			#endif

				*funcPtr = (size_t)info.thunksCurr;
				BYTE* d = info.thunksCurr;
			#if !WODKA_64
				*d++=0x55; // push ebp
				*d++=0x89; *d++=0xe5; // mov ebp, esp
				*d++=0x83; *d++=0xec; *d++=0x18; // sub esp, 0x18
				*d++=0xc7; *d++=0x04; *d++=0x24; *(size_t*)d = (size_t)thunkData->Name; d+=4; // mov dword ptr[esp],<name>
				*d++=0xff; *d++=0x15; *(size_t*)d = (size_t)&s_FailedProcPtr; d+=4; // call dword ptr ds:<func>
				*d++=0xc9; // leave
				*d++=0xcc; // int 3  (breakpoint)
				*d++=0xc3; // ret
			#else
				*d++=0x48; *d++=0xb9; *(size_t*)d = (size_t)thunkData->Name; d+=8; // mov rcx,<name>
				*d++=0x48; *d++=0xb8; *(size_t*)d = (size_t)&FailedImportProcImpl; d+=8; // mov rax,FailedImportProcImpl
				*d++=0x48; *d++=0xff; *d++=0xe0; // jmp rax
			#endif
				info.thunksCurr = d;
			}

			++thunkPtr;
			++funcPtr;
		}

		++importDesc;
	}
}


static void DoRelocation (PEModule& module, size_t delta)
{
	PE_IMAGE_DATA_DIRECTORY& dir = module.headers->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC];
	if (dir.size == 0)
		return;

	PE_IMAGE_BASE_RELOCATION* relocation = (PE_IMAGE_BASE_RELOCATION*)(module.codeBase + dir.va);
	while (relocation->va > 0)
	{
		BYTE *dest = module.codeBase + relocation->va;
		WORD *relInfo = (WORD*)((BYTE*)relocation + IMAGE_SIZEOF_BASE_RELOCATION);
		for (DWORD i = 0; i < ((relocation->sizeOfBlock-IMAGE_SIZEOF_BASE_RELOCATION) / 2); ++i, ++relInfo)
		{
			int type = *relInfo >> 12; // type in upper 4 bits
			if (type == IMAGE_REL_BASED_HIGHLOW || type == IMAGE_REL_BASED_DIR64)
			{
				int offset = *relInfo & 0xFFF; // offset in lower 12 bits
				size_t* patchAddr = (size_t*)(dest + offset);
				*patchAddr += delta;
			}
		}

		relocation = (PE_IMAGE_BASE_RELOCATION*)((BYTE*)relocation + relocation->sizeOfBlock);
	}
}


PEModule* PELoadLibrary (const void* data, size_t size, const PEKnownImport* imports, unsigned importCount)
{
	if (size < sizeof(PE_IMAGE_DOS_HEADER))
		return nullptr;

	const PE_IMAGE_DOS_HEADER* dosHeader = (const PE_IMAGE_DOS_HEADER*)data;
	if (dosHeader->magic != IMAGE_DOS_SIGNATURE)
		return nullptr;

	const PE_IMAGE_NT_HEADERS* ntHeader = (PE_IMAGE_NT_HEADERS*)((const BYTE*)data + dosHeader->ntHeader);
	if (ntHeader->Signature != IMAGE_NT_SIGNATURE)
		return nullptr;

	#if WODKA_64
	if (ntHeader->OptionalHeader.Magic != IMAGE_NT_OPTIONAL_HDR64_MAGIC)
		return nullptr;
	#else
	if (ntHeader->OptionalHeader.Magic != IMAGE_NT_OPTIONAL_HDR32_MAGIC)
		return nullptr;
	#endif


	size_t mapSize = ntHeader->OptionalHeader.SizeOfImage;
	BYTE* code = (BYTE*)MapExecMemory (mapSize);
	if (code == kMapFailed)
		return nullptr;

	PEModule* info = new PEModule();
	info->codeBase = code;
	info->codeSize = mapSize;
	info->thunks = nullptr;

	memcpy (code, dosHeader, dosHeader->ntHeader + ntHeader->OptionalHeader.SizeOfHeaders);
	info->headers = (PE_IMAGE_NT_HEADERS*)(code+dosHeader->ntHeader);

	info->headers->OptionalHeader.ImageBase = (size_t)code;

	CopySections ((const BYTE*)data, ntHeader, *info);

	size_t locDelta = (size_t)(code - ntHeader->OptionalHeader.ImageBase);
	if (locDelta != 0)
	{
		DoRelocation (*info, locDelta);
	}

#if DEBUG_IMPORT_EXPORT
	DumpExports (*info);
#endif
	ProcessImports (*info, imports, importCount);

	PESetupFS ();

	// call entry point
	typedef BOOL (WINAPI_IMPL *DllEntryProc)(HMODULE_impl dll, DWORD reason, void* reserved);
	DllEntryProc entry = (DllEntryProc)(info->codeBase + ntHeader->OptionalHeader.AddressOfEntryPoint);
	entry = (DllEntryProc)PECreateImportThunk(info, (void*)entry);
	(*entry)((HMODULE_impl)info->codeBase, 1, 0); // 1 = DLL_PROCESS_ATTACH

	return info;
}


void PEFreeLibrary (PEModule* module)
{
	if (!module)
		return;

	UnmapExecMemory (module->codeBase, module->codeSize);
	if (module->thunks != 0 && module->thunks != kMapFailed)
		UnmapExecMemory (module->thunks, module->thunksSize);
	delete module;
}

PEModule* PELoadLibrary (const char* path, const PEKnownImport* imports, unsigned importCount)
{
	unsigned dllSize = 0;
	void*    dllData = 0;

	{
		FILE* dllf = fopen(path, "rb");
		if (!dllf)
			return nullptr;

		fseek (dllf, 0, SEEK_END);
		dllSize = ftell (dllf);
		fseek (dllf, 0, SEEK_SET);

		dllData = (char*)malloc (dllSize);
		if (!dllData)
			return nullptr;

		if (fread (dllData, 1, dllSize, dllf) != dllSize)
		{
			free (dllData);
			return nullptr;
		}

		fclose (dllf);
	}

	PEModule* ret = PELoadLibrary (dllData, dllSize, imports, importCount);
	free (dllData);

	return ret;
}


void* PEGetProcAddress (PEModule* module, const char* name)
{
	if (!module)
		return nullptr;

	PE_IMAGE_DATA_DIRECTORY& dir = module->headers->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT];
	if (dir.size == 0)
		return nullptr;

	PE_IMAGE_EXPORT_DIRECTORY* exports = (PE_IMAGE_EXPORT_DIRECTORY*)(module->codeBase + dir.va);
	DWORD* nameAddr = (DWORD*)(module->codeBase + exports->AddressOfNames);
	WORD* ordinal = (WORD*)(module->codeBase + exports->AddressOfNameOrdinals);
	int index = -1;
	for (DWORD i = 0; i < exports->NumberOfNames; ++i, ++nameAddr, ++ordinal)
	{
		const char* namePtr = (const char*)(module->codeBase + *nameAddr);
		if (!strcasecmp(name, namePtr))
		{
			index = *ordinal;
			break;
		}
	}
	if (index == -1)
		return nullptr;
	if ((DWORD)index > exports->NumberOfFunctions)
		return nullptr;

	DWORD funcAddr = *(DWORD*)(module->codeBase + exports->AddressOfFunctions + (index*4));
	BYTE* funcPtr = module->codeBase + funcAddr;
	return PECreateImportThunk(module, funcPtr);
}



// http://en.wikipedia.org/wiki/Win32_Thread_Information_Block
struct ThreadInformationBlock
{
	void* seh;
	void* stackTop;
	void* stackBottom;
	void* unknown1;
	DWORD fiberData;
	DWORD arbitrary;
	void* tibSelf;
	void* env;
	DWORD processID;
	DWORD threadID;
	DWORD activeRpc;
	void* tlsArray;
	void* peb;
	DWORD lastError;
	DWORD ownedCsCount;
	void* csrClientThread;
	DWORD threadInfo;
	//@TODO?
};


#if WODKA_MAC || WODKA_LINUX
struct SimpleMutex
{
	SimpleMutex() {
		pthread_mutex_init(&mutex, nullptr);
	}
	~SimpleMutex() {
		pthread_mutex_destroy(&mutex);
	}
	void Lock() {
		pthread_mutex_lock(&mutex);
	}
	void Unlock() {
		pthread_mutex_unlock(&mutex);
	}
	pthread_mutex_t	mutex;
};
static SimpleMutex s_PESetupFSMutex;
#endif


#if WODKA_LINUX
static int FindUnusedLDT () {
	user_desc entries[LDT_ENTRIES];

	int retval = syscall (__NR_modify_ldt, 0, entries, sizeof (entries));

	if (LDT_ENTRIES > retval)
		return retval / sizeof (user_desc);
	else {
		retval = -1;
		for (int i = 0; i < LDT_ENTRIES; ++i) {
			if (!entries[i].base_addr) { // HACK?
				retval = i;
				break;
			}
		}
	}

	return retval;
}
#endif


void PESetupFS()
{
	#if WODKA_MAC && !WODKA_64

	s_PESetupFSMutex.Lock();

	static int selIdx = -1;
	static bool done = false;
	if (!done)
	{
		done = true;

		// Windows x86 code uses FS to access thread information block, which is
		// used for exception handling and thread local storage, among other
		// things.
		//
		// Mac ABI seems to not use FS for anything, so let's make it point
		// to a dummy thread information block structure.

		// Dummy TIB
		const int kTIBSize = 4096;
		static char s_TIB[kTIBSize];
		memset (s_TIB, 0, kTIBSize);
		ThreadInformationBlock* tibPtr = (ThreadInformationBlock*)s_TIB;
		tibPtr->tibSelf = s_TIB;
		
		size_t ptr = (size_t)s_TIB;

		// Data segment descriptor
		data_desc_t e;
		memset (&e, 0, sizeof(e));
		e.limit00 = (ptr+kTIBSize)&0xFFFF;
		e.base00 = ptr & 0xFFFF;
		e.base16 = (ptr >> 16) & 0xFF;
		e.type = DESC_DATA_WRITE;
		e.dpl = 3;
		e.present = 1;
		e.limit16 = ((ptr+kTIBSize)>>16)&0xFFFF;
		e.stksz = DESC_DATA_32B;
		e.granular = DESC_GRAN_BYTE;
		e.base24 = (ptr >> 24) & 0xFF;

		// Allocate new LDT
		selIdx = i386_set_ldt(LDT_AUTO_ALLOC, (const ldt_entry*)&e, 1);
		assert(selIdx != -1);
	}

	// Set FS to the new LDT
	sel_t sel;
	sel.index = selIdx;
	sel.rpl = USER_PRIV;
	sel.ti = SEL_LDT;
	__asm__ volatile ("mov %0,%%fs\n" : : "r"(sel));

	s_PESetupFSMutex.Unlock();

	#elif WODKA_LINUX

	s_PESetupFSMutex.Lock();

	// Setup FS => dummy thread information block like Mac

	// Dummy TIB
	const int kTIBSize = 4096;
	static char s_TIB[kTIBSize];
	memset (s_TIB, 0, kTIBSize);
	ThreadInformationBlock* tibPtr = (ThreadInformationBlock*)s_TIB;
	tibPtr->tibSelf = s_TIB;
	size_t ptr = (size_t)s_TIB;

	// Data segment descriptor
	user_desc e;
	memset (&e, 0, sizeof(e));
	e.entry_number = FindUnusedLDT();
	e.base_addr = ptr;
	e.limit = (ptr+kTIBSize)&0xFFFF;
	e.seg_32bit = 1;
	e.contents = 0;
	e.read_exec_only = 0;
	e.limit_in_pages = ((ptr+kTIBSize)>>16)&0xFFFF;
	e.seg_not_present = 0;
	e.useable = 1;

	// Allocate new LDT
	assert(0 == syscall (__NR_modify_ldt, 1, &e, sizeof (e)));

	// Set FS to the new LDT
	#if WODKA_64
	__asm__ volatile ("mov %0,%%gs\n" : : "r"((e.entry_number << 3) | 0x7));
	#else
	__asm__ volatile ("mov %0,%%fs\n" : : "r"((e.entry_number << 3) | 0x7));
	#endif
	
	s_PESetupFSMutex.Unlock();

	#endif
}
