#pragma once

#include "EnginePrefix.h"
#include "ECS/Develop/Framework/Types.h"
#include "Resource/Resource.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"

namespace cross::editor {
  
enum class CEMeta(Editor) ModelSplitErrorCode
{
    ERR_OK = 0,
    ERR_PARAMETERS = 1
};

enum class CEMeta(Editor) ModelAsMode
{
    ModelAsEntity = 0,
    ModelAsSubModel = 1,
    ModelAsMeshPart = 2,
};

enum class CEMeta(Editor) ModelCombineErrorCode
{
    ERR_OK              = 0,
    ERR_PARAMETERS      = 1,
    ERR_VERTEX_CHANNEL  = 2,
    ERR_INDEX_CHANNEL   = 3,
    ERR_LOD_PART        = 4,
    ERR_FX              = 5,
    ERR_TEX_PROPERTY    = 6,
    ERR_TEX_ARRARY_INDEX_PROPERTY = 7
};

class ModelChange
{
public:
    CEFunction(Editor)
    static void CreateModel(const std::vector<std::string>& meshs, const std::vector<std::vector<std::vector<std::string>>>& mats, const std::string& outName, ModelAsMode mam);
    
    CEFunction(Editor)
    static Float3 ChangeModelCenter(const std::string& mesh);

    CEFunction(Editor)
    static ModelSplitErrorCode ModelSplit(const std::string& mesh, const std::vector<std::string>& mats, Float2 center, Float2 blockSize, ModelAsMode mam);

    CEFunction(Editor)
    static void BuildingSplit(const std::string& mesh, const std::vector<std::string>& mats, ModelAsMode mam);

    CEFunction(Editor)
    static int ModelLodCombineAndSplit(std::vector<std::vector<std::string>> meshs, const std::vector<std::string>& mats, bool enableSplit, Float2 blockSize, const std::string& outName, ModelAsMode mam);

    CEFunction(Editor)
    static ModelCombineErrorCode ModelCombine(const std::vector<std::string>& meshs, const std::vector<std::string>& mats, const std::vector<Float4x4>& trans, const std::string& outName, ModelAsMode mam);
};
}