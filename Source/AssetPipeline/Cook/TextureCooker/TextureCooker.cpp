#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "TextureCooker.h"
#include "AssetPipeline/Utils/AssetIO.h"
#include "Resource/DataCompressor.h"
#include "Resource/Texture/Texture.h"
#include "Resource/Texture/TextureUDIM.h"
#include "CrossBase/Serialization/Archive/FileArchive.h"

namespace cross::editor {

SerializeNode TextureCookSettting::Serialize()
{
    SerializeNode setting = ICookSetting::Serialize();
    return std::move(setting);
}

bool TextureCookSettting::Deserialize(const DeserializeNode& node)
{
    ICookSetting::Deserialize(node);
    return true;
}

bool TextureCooker::CheckClassID(int classID)
{
    return classID == ClassID(Texture2D) || classID == ClassID(Texture3D) || classID == ClassID(TextureCube);
}

bool TextureCooker::Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& settingMgr)
{
    auto textureSetting = settingMgr.GetCookSetting<TextureCookSettting>();
    if (!textureSetting || !textureSetting->mIsCook)
        return false;
    auto offset = fileInfo.GetOffet();
    auto textureData = CrossSchema::GetResourceAsset(fileData->Data() + offset)->resource_as_TextureAsset();
    auto textureAsset = textureData->UnPack();
    textureAsset->rawdata.clear();
    // create asset
    flatbuffers::FlatBufferBuilder meshBuilder(1024);
    auto mloc = CrossSchema::CreateTextureAsset(meshBuilder, textureAsset);
    // create resource
    auto classID = fileInfo.GetClassID();
    CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, (int32_t)textureAsset->data.size(), (int32_t)textureAsset->data.size());
    auto ma = CrossSchema::CreateResourceAsset(meshBuilder, &header, meshBuilder.CreateString(dstNdaPath), CrossSchema::ResourceType::TextureAsset, mloc.Union());
    FinishResourceAssetBuffer(meshBuilder, ma);
    // write file
    bool ret = WriteAsset(fileInfo, meshBuilder.GetBufferPointer(), meshBuilder.GetSize(), textureSetting, settingMgr.mLossCompressor, dstNdaPath);
    return ret;
}

bool TextureCooker::CookTexture(const TextureAssetT& srcTexture, TextureAssetT& desTexture)
{
    desTexture = srcTexture;
    return true;
}
}   // namespace cross::editor
