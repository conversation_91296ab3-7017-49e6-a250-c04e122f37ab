#include "AssetPipeline/Cook/BinCooker/BinCooker.h"

namespace cross::editor {
SerializeNode BinCookSettting::Serialize()
{
    SerializeNode setting = ICookSetting::Serialize();
    return std::move(setting);
}

bool BinCookSettting::Deserialize(const DeserializeNode& node)
{
    ICookSetting::Deserialize(node);
    return true;
}

bool BinCooker::<PERSON>(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting)
{
    auto binSetting = setting.GetCookSetting<BinCookSettting>();
    if (!binSetting || !binSetting->mIsCook)
        return false;
    // write file
    UInt64 offset = fileInfo.GetOffet();
    bool ret = WriteAsset(fileInfo, fileData->Data() + offset, fileData->Size() - offset, binSetting, setting.mLossCompressor, dstNdaPath);
    return ret;
}
}   // namespace cross::editor