#pragma once
#include <unordered_map>
#include <set>
#include "AssetPipeline/Cook/CookSetting.h"
#include "AssetPipeline/Cook/AssetCooker.h"
#include "AssetPipeline/Utils/AssetIO.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"

struct ASTCParam;

namespace cross::editor {
class ASSET_API JsonCookSettting : public ICookSetting
{
public:
    static ICookSetting* Produce()
    {
        return new JsonCookSettting();
    }

public:
    //@brief Serialize
    virtual SerializeNode Serialize() override;
    //@brief Deserialize
    virtual bool Deserialize(const DeserializeNode& node) override;
    //@brief ClassName
    static constexpr const char* ClassName = "JsonCookSettting";
};

class JsonCooker : public AssetCooker
{
public:
    ASSET_API bool CheckClassID(int classID) override{ return classID == -1; };
    ASSET_API bool Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting) override;
};
}   // namespace cross::editor
