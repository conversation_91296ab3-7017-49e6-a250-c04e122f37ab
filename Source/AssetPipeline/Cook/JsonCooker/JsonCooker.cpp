#include "AssetPipeline/Cook/JsonCooker/JsonCooker.h"
#include "Resource/ResourceManager.h"

namespace cross::editor {
void ReplaceGuidToPath(SerializeNode& node) {
    if (node.IsObject())
    {
        for (auto it = node.begin(); it != node.end(); ++it)
        {
            SerializeNode vNode = it.Value();
            if (vNode.IsString())
            {
                node[it.Key()] = gResourceMgr.ConvertGuidToPath(vNode.AsString());
            }
            else
            {
                ReplaceGuidToPath(vNode);
            }
        }
    }
    else if (node.IsArray())
    {
        for (int idx = 0; idx < node.Size(); idx++)
        {
            SerializeNode vNode = node.At(idx);
            if (vNode.IsString())
            {
                node.At(idx) = gResourceMgr.ConvertGuidToPath(vNode.AsString());
            }
            else
            {
                ReplaceGuidToPath(vNode);
            }
        }
    }
}

SerializeNode JsonCookSettting::Serialize()
{
    SerializeNode setting = ICookSetting::Serialize();
    return std::move(setting);
}

bool JsonCookSettting::Deserialize(const DeserializeNode& node)
{
    ICookSetting::Deserialize(node);
    return true;
}

bool JsonCooker::Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting)
{
    auto jsonSetting = setting.GetCookSetting<JsonCookSettting>();
    if (!jsonSetting || !jsonSetting->mIsCook)
        return false;
    UInt64 fileSize = fileData->Size();
    UInt64 offset = fileInfo.GetOffet();
    // Cook content
    fileData->Seek(offset);
    std::string str(fileSize - offset, '\0');
    void* strtAddress = str.data();
    fileData->Read(strtAddress, fileSize - offset);
    bool success = false;
    SerializeNode jData = SerializeNode::ParseFromJson(str, &success);
    //ReplaceGuidToPath(jData);
    auto const& conBinData = jData.FormatToBin();
    // write file
    bool ret = WriteAsset(fileInfo, &conBinData[0], conBinData.size(), jsonSetting, setting.mLossCompressor, dstNdaPath);
    return ret;
}
}