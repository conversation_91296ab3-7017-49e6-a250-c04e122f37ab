#include "AssetPipeline/Cook/AssetCookerManager.h"
#include "AssetPipeline/Cook/CookSetting.h"
#include "AssetPipeline/Cook/TextureCooker/TextureCooker.h"
#include "AssetPipeline/Cook/ShaderCooker/ShaderCooker.h"
#include "AssetPipeline/Cook/MeshCooker/MeshCooker.h"
#include "AssetPipeline/Cook/JsonCooker/JsonCooker.h"
#include "AssetPipeline/Cook/BinCooker/BinCooker.h"
#include "Resource/ResourceConvertManager.h"
#include <filesystem>


namespace cross::editor {
extern AssetCookerManager gAssetCookManager;

AssetCookerManager& AssetCookerManager::Instance() noexcept
{
    return gAssetCookManager;
}

AssetCookerManager::AssetCookerManager()
{
    RegisterCooker(std::make_unique<TextureCooker>());
    RegisterCooker(std::make_unique<MeshCooker>());
    RegisterCooker(std::make_unique<ShaderCooker>());
    mJsonCooker = std::make_unique<JsonCooker>();
    mBinCooker = std::make_unique<BinCooker>();
    // initialize cook setting
    CookSettingManager::Initialize();
}

AssetCookerManager::~AssetCookerManager()
{
    ClearCooker();
    ClearCookRecord();
    ClearCookEvents();
    ClearCookTask();
    CookSettingManager::Finitialize();
}

bool AssetCookerManager::CookProject(AssetCookTaskQueue& allTask, AssetPlatform platform, ResultGeneralCallBack callback)
{
    mAllTasks = allTask;
    mCookPlatform = platform;
    mCookCallback = callback;
    DoCookTask();
    return true;
}

bool AssetCookerManager::CookDirectory(std::string srcDir, std::string desDir, AssetPlatform platform, ResultGeneralCallBack callback)
{
    mCookPlatform = platform;
    mCookCallback = callback;
    CollectDirectoryTasks(srcDir, desDir);
    DoCookTask();
    return true;
}

bool AssetCookerManager::CookFile(std::string srcFile, std::string desFile, AssetPlatform platform, ResultGeneralCallBack callback)
{
    mCookPlatform = platform;
    mCookCallback = callback;
    mAllTasks.Push(srcFile, desFile);
    DoCookTask();
    return true;
}

bool AssetCookerManager::CookAsset(const char* srcNdaPath, const char* dstNdaPath, AssetPlatform cookPlatform)
{
    // LOG_DEBUG("Cooking Asset {} ...", srcNdaPath);
    auto dir = PathHelper::GetDirectoryFromAbsolutePath(dstNdaPath);
    if (!PathHelper::IsDirectoryExist(dir))
    {
        // create all the folders needed
        std::lock_guard<std::mutex> lockGurad(mMutex);
        PathHelper::CheckDirectory(dir);
    }
    bool ret = false;
    BinaryArchive* fileData = gResourceAssetMgr.ReadAssetFile(srcNdaPath);
    if (fileData)
    {
        resource::LoadNDAFileInfo fileInfo;
        if (gResourceAssetMgr.GetLoadNDAInfo(fileData, fileInfo))
        {
            bool hitCooker = false;
            for (auto& cooker : mCookers)
            {
                if (cooker->CheckClassID(fileInfo.GetClassID()))
                {
                    hitCooker = true;
                    cooker->SetSourceNdaPath(srcNdaPath);
                    ret = cooker->Cook(fileData, fileInfo, dstNdaPath, cookPlatform, mCookSetting);
                    break;
                }
            }
            if (!hitCooker)
            {
                if (fileInfo.GetContentType() == CONTENT_TYPE::CONTENT_TYPE_JSON)
                    ret = mJsonCooker->Cook(fileData, fileInfo, dstNdaPath, cookPlatform, mCookSetting);
                else
                    ret = mBinCooker->Cook(fileData, fileInfo, dstNdaPath, cookPlatform, mCookSetting);
            }
        }
        delete fileData;
    }
    // if cook failed copy file
    if (!ret)
    {
        std::error_code errcode;
        ret = std::filesystem::copy_file(srcNdaPath, dstNdaPath, std::filesystem::copy_options::overwrite_existing, errcode);
        if (!ret)
            LOG_EDITOR_ERROR("Copy file faild: {}", srcNdaPath);
    }
    return ret;
}

bool AssetCookerManager::UnCookAsset(const char* srcNdaPath)
{
    BinaryArchive* fileData = gResourceAssetMgr.ReadAssetFile(srcNdaPath);
    if (fileData)
    {
        gResourceAssetMgr.SetCheckDependFile(false);
        resource::LoadNDAFileInfo fileInfo;
        if (gResourceAssetMgr.GetLoadNDAInfo(fileData, fileInfo))
        {
            // decompress
            bool isCoompressed = false;
            auto contentData = gResourceAssetMgr.DecompressFileData(fileData, isCoompressed, fileInfo.GetOffet());
            // uncook
            if (fileInfo.GetContentType() == CONTENT_TYPE::CONTENT_TYPE_BSON)
            {
                DeserializeNode jData = DeserializeNode::ParseFromBin(contentData->Data(), contentData->Size());
                std::string contentStr = SerializeNode::CreateFromDeserializeNode(jData).FormatToJson();
                
                ResourceMetaHeader metaHeader = fileInfo.GetMetaHeader();
                metaHeader.mContentType = static_cast<UInt32>(CONTENT_TYPE::CONTENT_TYPE_JSON);

                Resource::Serialize(metaHeader, contentStr, std::string(srcNdaPath));
            }
            else
            {
                if (isCoompressed)
                {
                    ResourceMetaHeader metaHeader = fileInfo.GetMetaHeader();
                    Resource::Serialize(metaHeader, contentData->Data(), contentData->Size(), std::string(srcNdaPath));
                }
            }
            DELETE_BINARYDATA(contentData, !isCoompressed);
        }
        DELETE_BINARYDATA(fileData, false);
        gResourceAssetMgr.SetCheckDependFile(true);
    }
    return true;
}

bool AssetCookerManager::CookImportAsset(const char* filepath)
{
    if (!PathHelper::IsFileExist(filepath))
        return false;
    bool ret = false;
    BinaryArchive* fileData = gResourceAssetMgr.ReadAssetFile(filepath);
    if (fileData)
    {
        resource::LoadNDAFileInfo fileInfo;
        if (gResourceAssetMgr.GetLoadNDAInfo(fileData, fileInfo)) {
            for (auto& cooker : mCookers)
            {
                if (cooker->CheckClassID(fileInfo.GetClassID()))
                {
                    ret = cooker->CookImport(fileData, fileInfo, filepath);
                    break;
                }
            }
        }
    }
    return ret;
}

void AssetCookerManager::CookAbort()
{
    ClearCookEvents();
    ClearCookTask();
}

int AssetCookerManager::HandleCookingTask()
{
    int cookedIdx = -1;
    for (int idx = 0; idx < mCookingTasks.size(); idx++)
    {
        auto task = mCookingTasks[idx];
        if (!task)
            continue;
        if (task->mCookStatus == AssetCookStatus::CookSuccess)
        {
            mCookRecord.AddRecord(task->mSrcFile.c_str(), task->mDesFile.c_str());
            mCookingTasks[idx] = nullptr;
            delete task;
        }
        else if (++cookedIdx < idx)
        {
            std::swap(mCookingTasks[cookedIdx], mCookingTasks[idx]);
        }
    }
    return cookedIdx;
}

bool AssetCookerManager::CheckFinishCook()
{
    auto compelteNum = mTaskEventArrayPtr->GetNumCompletedTasks();
    mFinishTaskNum += static_cast<int>(compelteNum);
    if (mFinishTaskNum >= mAllTaskNum)
    {
        DoFinishTask();
        return true;
    }
    return false;
}

void AssetCookerManager::PushTaskToCook(int num)
{
    int cookedIdx = mMaxTaskNum - num;
    while (!mAllTasks.Empty() && num > 0)
    {
        --num;
        auto task = mAllTasks.Pop();
        // check cook
        if (!mCookForce && mCookRecord.CheckRecord(task->mSrcFile, task->mDesFile))
        {
            mFinishTaskNum++;
            continue;
        }
        // add cooking task
        mCookingTasks[cookedIdx++] = task;
        // add to cook
        mTaskEventArrayPtr->Add(threading::Async([task, this](const auto&) {
            task->mCookStatus = AssetCookStatus::Cooking;
            bool ret = CookAsset(task->mSrcFile.c_str(), task->mDesFile.c_str(), mCookPlatform);
            task->mCookStatus = ret ? AssetCookStatus::CookSuccess : AssetCookStatus::CookFaild;
        }));
    }
}

void AssetCookerManager::DumpResourceList() {
    gResourceConvertMgr.Dump(mDesAssetPath, true);
}

void AssetCookerManager::Tick()
{
    if (!mTaskEventArrayPtr)
        return;
    // add cook record
    int cookedIdx = HandleCookingTask();
    // check
    if (CheckFinishCook())
        return;
    // do cook
    PushTaskToCook(mMaxTaskNum - cookedIdx - 1);
    PostTickUpdates::Get()->Add([this]() { Tick(); });
}

void AssetCookerManager::RegisterCooker(AssetCookerPtr cooker)
{
    mCookers.push_back(std::move(cooker));
}

void AssetCookerManager::ClearCookTask()
{
    mAllTasks.Clear();
    mCookingTasks.clear();
    mAllTaskNum = 0;
    mFinishTaskNum = 0;
    mCookCallback = nullptr;
}

void AssetCookerManager::ClearCookRecord()
{
    mCookRecord.Clear();
}

void AssetCookerManager::ClearCookEvents()
{
    if (mTaskEventArrayPtr)
    {
        delete mTaskEventArrayPtr;
        mTaskEventArrayPtr = nullptr;
    }
}

void AssetCookerManager::CollectDirectoryTasks(std::string srcDir, std::string desDir)
{
    std::vector<std::string> dirs, files;

    bool ret = AssetIO::GetFiles(srcDir, dirs, files);
    if (ret)
    {
        for (auto file : files)
            mAllTasks.Push(srcDir + "/" + file, desDir + "/" + file);
        for (auto dir : dirs)
            CollectDirectoryTasks(srcDir + "/" + dir, desDir + "/" + dir);
    }
}

void AssetCookerManager::DoCookTask()
{
    mTaskEventArrayPtr = new threading::TaskEventArray();
    mAllTaskNum = static_cast<int>(mAllTasks.Size());
    mCookingTasks.resize(mMaxTaskNum);
    mCookRecord.Load(mRecordFilePath);
    PushTaskToCook(mMaxTaskNum);
    PostTickUpdates::Get()->Add([this]() { Tick(); });
}

void AssetCookerManager::DoFinishTask()
{
    // do cleanup file
    DoCleanUpRedundantFile();
    DumpResourceList();
    // save record
    mCookRecord.Save(mRecordFilePath);
    // callback temp
    auto callback = mCookCallback;
    // clear
    ClearCookRecord();
    ClearCookEvents();
    ClearCookTask();
    // callback
    callback(true);
}

void AssetCookerManager::DoCleanUpRedundantFile()
{
    if (!mCleanUpRedundantFile || mDesAssetPath.empty())
        return;
    DoCleanUpRedundantFile(mDesAssetPath + "/Contents");
    DoCleanUpRedundantFile(mDesAssetPath + "/EngineResource");
    mCleanUpRedundantFile = false;
}

void AssetCookerManager::DoCleanUpRedundantFile(std::string clearDir)
{
    std::vector<std::string> dirs, files;
    if (AssetIO::GetFiles(clearDir, dirs, files))
    {
        auto fileSys = EngineGlobal::GetFileSystem();
        for (auto file : files)
        {
            auto desFile = clearDir + "/" + file;
            auto relativePath = PathHelper ::GetRelativePath(mDesAssetPath, desFile);
            if (!mCookRecord.CheckInRecord(relativePath))
            {
                fileSys->RemoveFile(desFile);
            }
        }
        for (auto dir : dirs)
            DoCleanUpRedundantFile(clearDir + "/" + dir);
    }
}
}