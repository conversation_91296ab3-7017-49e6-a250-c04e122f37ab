#pragma once
#include <list>
#include <queue>
#include "RTTI/BaseObject.h"
#include "CrossBase/Threading/Task.h"
#include "Resource/resourceasset.h"
#include "Runtime/Editor/EditorCallback.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "AssetPipeline/Cook/CookSetting.h"
#include "AssetPipeline/Cook/CookRecord.h"


namespace cross::editor {
enum class CEMeta(Editor) AssetPlatform
{
    WIN,
    IOS,
    ANDROID,
    MACOS,
    WXGame,
    NONE,
};

class CookSettingManager;

class AssetCooker
{
public:
    virtual ~AssetCooker() = default;
    virtual bool CheckClassID(int classID){ return false; };
    virtual bool CookResourceMetaHeader(const ResourceMetaHeader& inMetaHeader, std::vector<UInt8>& outData);
    virtual ResourceMetaHeader GenResourceMetaHeader(const resource::LoadNDAFileInfo& fileInfo);
    virtual bool Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting){ return false; };
    virtual bool CookImport(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* filepath);
    virtual bool WriteAsset(const resource::LoadNDAFileInfo& fileInfo, const UInt8* contentData, SizeType contentSize, ICookSetting* cookSetting, CompressorType comTp, const char* filePath);
    virtual void SetSourceNdaPath(const char* srcNdaPath){};
};
}
