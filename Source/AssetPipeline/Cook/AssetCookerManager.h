#pragma once

#pragma once
#include <list>
#include <queue>
#include "RTTI/BaseObject.h"
#include "CrossBase/Threading/Task.h"
#include "Resource/resourceasset.h"
#include "Runtime/Editor/EditorCallback.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "AssetPipeline/Cook/CookSetting.h"
#include "AssetPipeline/Cook/CookRecord.h"
#include "AssetPipeline/Cook/AssetCooker.h"

namespace cross::editor {
enum class AssetCookStatus
{
    Ready,
    Cooking,
    CookSuccess,
    CookFaild
};

struct AssetCookTask
{
    std::string mSrcFile;
    std::string mDesFile;
    AssetCookStatus mCookStatus;

    AssetCookTask(std::string srcFile, std::string desFile)
        : mSrcFile(srcFile)
        , mDesFile(desFile)
        , mCookStatus(AssetCookStatus::Ready)
    {}
};

class AssetCookTaskQueue
{
public:
    //@brief Constructor
    AssetCookTaskQueue(){};
    //@brief Destructor
    ~AssetCookTaskQueue(){};
    //@brief Push
    CEFunction(Editor) void Push(std::string srcFile, std::string desFile)
    {
        mContainer.emplace(new AssetCookTask(srcFile, desFile));
    };
    //@brief Pop
    CEFunction(Editor) AssetCookTask* Pop()
    {
        auto task = mContainer.front();
        mContainer.pop();
        return task;
    }
    //@brief Size
    inline std::size_t Empty()
    {
        return mContainer.empty();
    };
    //@brief Size
    inline std::size_t Size()
    {
        return mContainer.size();
    };
    //@brief Clear
    inline void Clear()
    {
        while (!mContainer.empty())
        {
            auto task = mContainer.front();
            mContainer.pop();
            delete task;
        }
    };

private:
    std::queue<AssetCookTask*> mContainer;
};

class ASSET_API AssetCookerManager : NonCopyable
{
    using AssetCookerPtr = std::unique_ptr<AssetCooker>;

public:
    CEFunction(Editor) static AssetCookerManager& Instance() noexcept;

public:
    AssetCookerManager();
    ~AssetCookerManager();

    //@brief CookTasks
    CEFunction(Editor) 
    bool CookProject(AssetCookTaskQueue& allTask, AssetPlatform platform, ResultGeneralCallBack callback);
    //@brief CookDirectory
    CEFunction(Editor) 
    bool CookDirectory(std::string srcDir, std::string desDir, AssetPlatform platform, ResultGeneralCallBack callback);
    //@brief CookFile
    CEFunction(Editor)
    bool CookFile(std::string srcFile, std::string desFile, AssetPlatform platform, ResultGeneralCallBack callback);
    //@brief CookAsset
    CEFunction(Editor)
    bool CookAsset(const char* srcNdaPath, const char* dstNdaPath, AssetPlatform cookPlatform);
    CEFunction(Editor) 
    bool UnCookAsset(const char* srcNdaPath);
    //@brief CookImportAsset
    bool CookImportAsset(const char* filepath);
    //@brief GetCookSetting
    CEFunction(Editor)
    inline CookSettingManager& GetCookSetting()
    {
        return mCookSetting;
    };
    //@brief GetCookSetting
    CEFunction(Editor)
    inline void SetMaxCookNum(int maxNum)
    {
        mMaxTaskNum = maxNum;
    };
    //@brief GetProgress
    CEFunction(Editor)
    inline float GetProgress()
    {
        return mAllTaskNum == 0 ? 0.0f : mFinishTaskNum * 1.0f / mAllTaskNum;
    };
    //@brief AbortCook
    CEFunction(Editor) 
    void CookAbort();
    //@brief SetRecordPath
    CEFunction(Editor) 
    inline void SetRecordPath(std::string path)
    {
        mRecordFilePath = path;
    };
    //@brief SetDesAssetPath
    CEFunction(Editor)
    inline void SetDesAssetPath(std::string path)
    {
        mDesAssetPath = path;
        mCookRecord.SetDesAssetPath(path);
    };
    //@brief SetcleanUpRedundantFile
    CEFunction(Editor) 
    inline void SetcleanUpRedundantFile(bool cleanUpRedundantFile)
    {
        mCleanUpRedundantFile = cleanUpRedundantFile;
    };
    //@brief SetVersion
    CEFunction(Editor) 
    inline void SetVersion(int version)
    {
        mCookRecord.SetVersion(version);
    };
    //@brief SetCheckMD5
    CEFunction(Editor) 
    inline void SetCheckMD5(bool check)
    {
        mCheckMd5 = check;
    };
    //@brief SetCookForce
    CEFunction(Editor) 
    inline void SetCookForce(bool force)
    {
        mCookForce = force;
    };

private:
    //@brief Tick
    void Tick();
    //@brief RegisterCooker
    void RegisterCooker(AssetCookerPtr cooker);
    //@brief ClearCooker
    FORCE_INLINE void ClearCooker()
    {
        mCookers.clear();
    };
    //@brief ClearCookTask
    FORCE_INLINE void ClearCookTask();
    //@brief ClearCookRecord
    FORCE_INLINE void ClearCookRecord();
    //@brief ClearCookEvents
    FORCE_INLINE void ClearCookEvents();
    //@brief CollectDirectoryTasks
    void CollectDirectoryTasks(std::string srcDir, std::string desDir);
    //@brief DoCookTask
    void DoCookTask();
    //@brief FinishTask
    void DoFinishTask();
    //@brief DoClearDesFile
    void DoCleanUpRedundantFile();
    //@brief DoClearDesFile
    void DoCleanUpRedundantFile(std::string clearDir);
    //@brief HandleCookingTask
    int HandleCookingTask();
    //@brief CheckFinishCook
    bool CheckFinishCook();
    //@brief PushTaskToCook
    void PushTaskToCook(int num);
    //@brief DumpResourceList
    void DumpResourceList();

private:
    using CookTaskQueue = std::queue<AssetCookTask, std::vector<AssetCookTask*>>;
    std::vector<AssetCookerPtr> mCookers;
    AssetCookerPtr              mJsonCooker;
    AssetCookerPtr              mBinCooker;
    CookSettingManager          mCookSetting;
    AssetCookRecordManager      mCookRecord;
    std::mutex                  mMutex;
    AssetCookTaskQueue          mAllTasks;
    std::vector<AssetCookTask*> mCookingTasks;
    int                         mAllTaskNum = 0;
    int                         mFinishTaskNum = 0;
    AssetPlatform               mCookPlatform = AssetPlatform::WIN;
    ResultGeneralCallBack       mCookCallback = nullptr;
    threading::TaskEventArray*  mTaskEventArrayPtr = nullptr;
    int                         mMaxTaskNum = 5;
    std::string                 mRecordFilePath;
    std::string                 mDesAssetPath;
    bool                        mCleanUpRedundantFile = false;
    bool                        mCheckMd5 = true;
    bool                        mCookForce = false;
};
}   // namespace cross::editor