#include "CookSetting.h"
#include "RTTI/BaseObject.h"
#include "AssetPipeline/Cook/TextureCooker/TextureCooker.h"
#include "AssetPipeline/Cook/MeshCooker/MeshCooker.h"
#include "AssetPipeline/Cook/JsonCooker/JsonCooker.h"
#include "AssetPipeline/Cook/BinCooker/BinCooker.h"

namespace cross::editor {
SerializeNode ICookSetting::Serialize()
{
    SerializeNode setting;
    setting["mIsCompress"] = mIsCompress;
    setting["mIsCook"] = mIsCook;
    return std::move(setting);
}

bool ICookSetting::Deserialize(const DeserializeNode& node)
{
    if (node.HasMember("mIsCompress"))
        mIsCompress = node["mIsCompress"].AsBoolean();
    if (node.HasMember("mIsCook"))
        mIsCook = node["mIsCook"].AsBoolean();
    return true;
}

CookSettingManager::CookSettingFactoryFuncMap CookSettingManager::gFactoryMap;

CookSettingManager::CookSettingManager() {}

CookSettingManager::CookSettingManager(const char* path)
    : mPath(path)
{}

CookSettingManager::~CookSettingManager()
{
    mCookSettingMap.clear();
}

void CookSettingManager::Load()
{
    if (mIsLoad)
        return;

    auto absolutePath = PathHelper::GetAbsolutePath(mPath);
    if (!PathHelper::IsFileExist(absolutePath))
        return;
    // read file
    std::string fileContent;
    filesystem::IFilePtr file = EngineGlobal::GetFileSystem()->Open(absolutePath);
    size_t fileSize = file->GetSize();
    fileContent.resize(fileSize);
    size_t readSize = file->Read(fileContent.data(), fileSize);
    Assert(readSize == fileSize);
    // parse global
    DeserializeNode rootNode = DeserializeNode::ParseFromJson(fileContent);
    if (rootNode.HasMember("Global"))
    {
        DeserializeNode globalNode = rootNode["Global"];
        if (globalNode.HasMember("mLossCompressor"))
        {
            mLossCompressor = static_cast<CompressorType>(globalNode["mLossCompressor"].AsInt32());
        }
        if (globalNode.HasMember("mIsLZ4HC"))
        {
            mIsLZ4HC = globalNode["mIsLZ4HC"].AsBoolean();
        }
        if (globalNode.HasMember("mLZ4AccelerationFactor"))
        {
            mLZ4AccelerationFactor = globalNode["mLZ4AccelerationFactor"].AsInt32();
        }
    }
    // parse settings
    if (rootNode.HasMember("Settings"))
    {
        DeserializeNode setingNode = rootNode["Settings"];
        if (setingNode.IsObject())
        {
            for (auto it = setingNode.begin(); it != setingNode.end(); it++)
            {
                std::string name = it.Key().data();
                if (gFactoryMap.find(name) != gFactoryMap.end())
                {
                    GetCookSetting(name).Deserialize(it.Value());
                }
            }
        }
    }
    mIsLoad = true;
}

void CookSettingManager::Dump()
{
    SerializeNode globalNode;
    globalNode["mLossCompressor"] = mLossCompressor;
    globalNode["mIsLZ4HC"] = mIsLZ4HC;
    globalNode["mLZ4AccelerationFactor"] = mLZ4AccelerationFactor;
    SerializeNode settingNode;
    for (auto& it : mCookSettingMap)
    {
        auto name = it.first;
        settingNode[name] = it.second->Serialize();
    }
    SerializeNode rootNode;
    rootNode["Global"] = std::move(globalNode);
    rootNode["Settings"] = std::move(settingNode);
    std::string fileContent = rootNode.FormatToJson();
    auto absolutePath = PathHelper::GetAbsolutePath(mPath);
    EngineGlobal::GetFileSystem()->Save(absolutePath, fileContent.c_str(), fileContent.size());
}

std::vector<std::string> CookSettingManager::GetCookTypes()
{
    std::vector<std::string> configs;
    for (auto& it : gFactoryMap)
    {
        configs.emplace_back(it.first);
    }
    return configs;
}

void CookSettingManager::Initialize()
{
    Register<JsonCookSettting>();
    Register<BinCookSettting>();
    Register<TextureCookSettting>();
    Register<MeshCookSettting>();
}

void CookSettingManager::Finitialize()
{
    gFactoryMap.clear();
}
}   // namespace cross::editor
