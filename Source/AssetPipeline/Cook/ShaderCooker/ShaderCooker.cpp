#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "ShaderCooker.h"
#include "AssetPipeline/Utils/AssetIO.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImporter.h"
#include "CrossSchema/ShaderAsset_generated.h"
#include "AssetPipeline/Interface/AssetPipeline.h"

bool cross::editor::ShaderCooker::CheckClassID(int classID)
{
    return classID == ClassID(GraphicsShader) || classID == ClassID(ComputeShader);
}

bool cross::editor::ShaderCooker::Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting)
{
    if (fileInfo.GetClassID() == ClassID(GraphicsShader)) 
    {
        return CookGraphicsShader(fileData, fileInfo, dstNdaPath, cookPlatform, setting);
    }
    else if (fileInfo.GetClassID() == ClassID(ComputeShader))
    {
        return CookComputeShader(fileData, fileInfo, dstNdaPath, cookPlatform, setting);
    }

    Assert(false);
    return false;
}

bool cross::editor::ShaderCooker::CookGraphicsShader(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting)
{
    using namespace CrossSchema;

    std::vector<ShaderVersion> versions;
    switch (cookPlatform)
    {
    case AssetPlatform::WIN:
        //versions.emplace_back(ShaderCodeFormat::DXIL, 6, 0);
        versions.emplace_back(ShaderCodeFormat::SPIR_V, 1, 3);
        break;
    case AssetPlatform::ANDROID:
        versions.emplace_back(ShaderCodeFormat::ESSL, 3, 0);
        versions.emplace_back(ShaderCodeFormat::SPIR_V, 1, 3);
        break;
    case AssetPlatform::IOS:
        versions.emplace_back(ShaderCodeFormat::ESSL, 3, 0);
        versions.emplace_back(ShaderCodeFormat::MSL_IOS, 2, 2);
        break;
    case AssetPlatform::MACOS:
        versions.emplace_back(ShaderCodeFormat::MSL_OSX, 2, 2);
        break;
    //case AssetPlatform::WXGame:
    //    versions.emplace_back(ShaderCodeFormat::ESSL, 1, 0);
    //    break;
    case AssetPlatform::NONE:
    default:
        Assert(false);
        return false;
    }

    ResourceHeader header;
    std::string name;
    GraphicsShaderAssetT shaderAsset;

    auto initShaderAsset = [&](const BinaryArchive* buffer, const resource::LoadNDAFileInfo& info) {
        auto fbResourceAsset = GetResourceAsset(buffer->Data() + info.GetOffet());
        header = *fbResourceAsset->header();
        name = fbResourceAsset->name()->c_str();
        auto fbShaderAsset = fbResourceAsset->resource_as_GraphicsShaderAsset();
        fbShaderAsset->UnPackTo(&shaderAsset);
    };

    initShaderAsset(fileData, fileInfo);

    // holder
    std::unique_ptr<BinaryArchive> holder;

    // Format/Variant completion
    {
        bool needReload = false;
        for (auto iter = versions.begin(); iter != versions.end(); iter++)
        {
            ShaderVersion& version = *iter;
            const auto& shaders = shaderAsset.platform_shaders;

            auto iterator = shaders.begin();
            for (; iterator != shaders.end(); iterator++)
            {
                if (!std::memcmp(&version, iterator->get()->version.get(), sizeof(ShaderVersion)))
                {
                    break;
                }
            }

            // return true if need variants gen
            auto needVariantsCompletion = [&shaderAsset](CrossSchema::PlatformGraphicsShaderT* p) {
                const bool ret = p == nullptr || std::pow(2, p->keywords.size()) != p->variants.size();
                if (!ret)
                {
                    // check all varaint mtime
                    for (auto& var : p->variants)
                    {
                        if (var->mtime != shaderAsset.mtime)
                            return true;
                    }
                }
                return ret;
            };

            if (iterator == shaders.end() || needVariantsCompletion(iterator->get()))
            {
                const auto& ndaFile = mSourceNdaPath;
                const auto& srcFile = std::filesystem::path{ndaFile}.replace_extension().string();
                if (std::filesystem::exists(srcFile))
                {
                    const auto threadID = threading::TaskSystem::GetCurrentThreadID();
                    if (threading::IsMasterThread(threadID) || threading::IsTaskThread(threadID))
                    {
                        ShaderImportSettings settings;
                        settings.UseOnDemandCompilation = false;
                        SetShaderImportSettings(&settings);
                        if (ImportAsset(srcFile.c_str(), ndaFile.c_str())) 
                        {
                            needReload = true;
                            break;
                        }
                    }
                    else
                    {
                        const std::string command = PathHelper::GetEngineBinaryDirectoryPath() +
                                                    "/ShaderGen -s \"" + srcFile + "\" -o \"" + ndaFile +
                                                    "\" -eiap \"" + PathHelper::GetCurrentDirectoryPath() +
                                                    "\" -eier \"" + PathHelper::GetEngineResourceDirectoryPath() +
                                                    "\" -eieb \"" + PathHelper::GetEngineBinaryDirectoryPath() + "\" -gen-all";

                        LOG_INFO("[ShaderGen] Call With \"{}\"", command);
                        const int ret = std::system(command.c_str());

                        if (ret < 0)
                        {
                            LOG_EDITOR_ERROR("[ShaderGen] Create Process Failed");
                            return false;
                        }
                        else if (ret != 0)
                        {
                            LOG_EDITOR_ERROR("[ShaderGen] Process Return {} And Get Error {}", ret, std::strerror(errno));
                            return false;
                        }
                        else
                        {
                            needReload = true;
                            break;
                        }
                    }
                }
                else
                {
                    LOG_EDITOR_ERROR("[ShaderCook] Can`t find source shader file to compile for {}", mSourceNdaPath);
                    return false;
                }
            }
        }

        if (needReload)
        {
            const auto& ndaFile = mSourceNdaPath;
            BinaryArchive* newFileData = gResourceAssetMgr.ReadAssetFile(ndaFile.c_str());
            if (newFileData)
            {
                holder.reset(newFileData);
                resource::LoadNDAFileInfo newInfo;
                if (gResourceAssetMgr.GetLoadNDAInfo(newFileData, newInfo))
                {
                    initShaderAsset(newFileData, newInfo);
                }
            }
        }
    }

    for (auto iter = shaderAsset.platform_shaders.begin(); iter != shaderAsset.platform_shaders.end();)
    {
        auto version = *((*iter)->version);
        auto suitable = std::any_of(versions.begin(), versions.end(), [&](const auto& ver) { return memcmp(&version, &ver, sizeof(ShaderVersion)); });
        if (suitable)
        {
            iter = shaderAsset.platform_shaders.erase(iter);
        }
        else
        {
            iter++;
        }
    }

    flatbuffers::FlatBufferBuilder fbb;
    auto fbShaderAsset = CreateGraphicsShaderAsset(fbb, &shaderAsset);
    auto fbResourceAsset = CreateResourceAsset(fbb, &header, fbb.CreateString(name), ResourceType::GraphicsShaderAsset, fbShaderAsset.Union());
    CrossSchema::FinishResourceAssetBuffer(fbb, fbResourceAsset);

    return AssetIO::SerializeAsset(fbb, header.classid(), EngineGlobal::GetFileSystem()->OpenForWrite(dstNdaPath));
}

bool cross::editor::ShaderCooker::CookComputeShader(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting)
{
    using namespace CrossSchema;

    std::vector<ShaderVersion> versions;
    switch (cookPlatform)
    {
    case AssetPlatform::WIN:
        //versions.emplace_back(ShaderCodeFormat::DXIL, 6, 0);
        //versions.emplace_back(ShaderCodeFormat::ESSL, 3, 0); // es 3.0 does not support compute shader.
        versions.emplace_back(ShaderCodeFormat::SPIR_V, 1, 3);
        break;
    case AssetPlatform::ANDROID:
        versions.emplace_back(ShaderCodeFormat::SPIR_V, 1, 3);
        break;
    case AssetPlatform::IOS:
        versions.emplace_back(ShaderCodeFormat::MSL_IOS, 2, 2);
        break;
    case AssetPlatform::MACOS:
        versions.emplace_back(ShaderCodeFormat::MSL_OSX, 2, 2);
        break;
    case AssetPlatform::WXGame:
        return false;
    case AssetPlatform::NONE:
    default:
        Assert(false);
        return false;
    }

    ResourceHeader header;
    std::string name;
    ComputeShaderAssetT shaderAsset;

    auto initShaderAsset = [&]() {
        auto fbResourceAsset = GetResourceAsset(fileData->Data() + fileInfo.GetOffet());
        header = *fbResourceAsset->header();
        name = fbResourceAsset->name()->c_str();
        auto fbShaderAsset = fbResourceAsset->resource_as_ComputeShaderAsset();
        fbShaderAsset->UnPackTo(&shaderAsset);
    };

    initShaderAsset();

    // No OnDemand in use with compute
    if (false)
    {
        bool needReload = false;
        for (auto iter = versions.begin(); iter != versions.end(); iter++)
        {
            ShaderVersion& version = *iter;
            const auto& shaders = shaderAsset.platform_shaders;

            auto iterator = shaders.begin();
            for (; iterator != shaders.end(); iterator++)
            {
                if (!std::memcmp(&version, iterator->get()->version.get(), sizeof(ShaderVersion)))
                {
                    break;
                }
            }

            if (iterator == shaders.end())
            {
                const auto& ndaFile = mSourceNdaPath;
                const auto& srcFile = std::filesystem::path{ndaFile}.replace_extension().string();
                if (std::filesystem::exists(srcFile))
                {
                    ShaderImportSettings settings;
                    settings.UseOnDemandCompilation = true;
                    memcpy(&settings.Version, &version, sizeof(ShaderVersion));
                    SetComputeShaderImportSettings(&settings);
                    needReload = ImportAsset(srcFile.c_str(), ndaFile.c_str());
                }
                else
                {
                    LOG_EDITOR_ERROR("[ShaderCook] Can`t find source shader file to compile for {}", mSourceNdaPath);
                    return false;
                }
            }
        }

        if (needReload)
        {
            const auto& ndaFile = mSourceNdaPath;
            BinaryArchive* newFileData = gResourceAssetMgr.ReadAssetFile(ndaFile.c_str());
            if (newFileData)
            {
                delete fileData;
                fileData = newFileData;

                if (gResourceAssetMgr.GetLoadNDAInfo(fileData, const_cast<resource::LoadNDAFileInfo&>(fileInfo)))
                {
                    initShaderAsset();
                }
            }
        }
    }

    for (auto iter = shaderAsset.platform_shaders.begin(); iter != shaderAsset.platform_shaders.end();)
    {
        auto version = *((*iter)->version);
        auto suitable = std::any_of(versions.begin(), versions.end(), [&](const auto& ver) { return memcmp(&version, &ver, sizeof(ShaderVersion)); });
        if (suitable)
        {
            iter = shaderAsset.platform_shaders.erase(iter);
        }
        else
        {
            iter++;
        }
    }

    flatbuffers::FlatBufferBuilder fbb;
    auto fbShaderAsset = CreateComputeShaderAsset(fbb, &shaderAsset);
    auto fbResourceAsset = CreateResourceAsset(fbb, &header, fbb.CreateString(name), ResourceType::ComputeShaderAsset, fbShaderAsset.Union());
    CrossSchema::FinishResourceAssetBuffer(fbb, fbResourceAsset);

    return AssetIO::SerializeAsset(fbb, header.classid(), EngineGlobal::GetFileSystem()->OpenForWrite(dstNdaPath));
}
