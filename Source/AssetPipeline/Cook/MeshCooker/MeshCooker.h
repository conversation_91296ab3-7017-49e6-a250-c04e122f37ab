#pragma once
#pragma once

#include "AssetPipeline/Cook/AssetCooker.h"
#include "AssetPipeline/Cook/CookSetting.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "CrossBase/File/File.h"

namespace cross::editor {

class ASSET_API MeshCookSettting : public ICookSetting
{
public:
    static ICookSetting* Produce()
    {
        return new MeshCookSettting();
    }

public:
    //@brief Serialize
    virtual SerializeNode Serialize() override;
    //@brief Deserialize
    virtual bool Deserialize(const DeserializeNode& node) override;
    //@brief ClassName
    static constexpr const char* ClassName = "MeshCookSettting";
};

class MeshCooker : public AssetCooker
{
public:
    ASSET_API bool CheckClassID(int classID) override;
    ASSET_API bool Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting) override;
};
}   // namespace cross::editor
