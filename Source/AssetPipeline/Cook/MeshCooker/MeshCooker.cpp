#include "AssetPipeline/Cook/MeshCooker/MeshCooker.h"
#include "AssetPipeline/Utils/AssetIO.h"
#include "Resource/DataCompressor.h"

namespace cross::editor {

SerializeNode MeshCookSettting::Serialize()
{
    SerializeNode setting = ICookSetting::Serialize();
    return std::move(setting);
}

bool MeshCookSettting::Deserialize(const DeserializeNode& node)
{
    ICookSetting::Deserialize(node);
    return true;
}

bool MeshCooker::CheckClassID(int classID)
{
    return classID == ClassID(MeshAssetDataResource);
}

bool MeshCooker::Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& settingMgr)
{
    auto meshSetting = settingMgr.GetCookSetting<MeshCookSettting>();
    if (!meshSetting->mIsCook)
        return false;
    auto offset = fileInfo.GetOffet();
    auto meshData = CrossSchema::GetResourceAsset(fileData->Data() + offset)->resource_as_ImportMeshAssetData();
    auto meshAsset = meshData->UnPack();
    // delete fcollisiontree
    for (auto& it : meshAsset->fmeshpartinfo)
    {
        it->fcollisiontree.clear();
    }
    // create asset
    flatbuffers::FlatBufferBuilder meshBuilder(1024);
    auto mloc = CrossSchema::CreateImportMeshAssetData(meshBuilder, meshAsset);
    // create resource
    auto classID = fileInfo.GetClassID();
    CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);
    auto ma = CrossSchema::CreateResourceAsset(meshBuilder, &header, meshBuilder.CreateString(dstNdaPath), CrossSchema::ResourceType::ImportMeshAssetData, mloc.Union());
    FinishResourceAssetBuffer(meshBuilder, ma);
    // write file
    bool ret = WriteAsset(fileInfo, meshBuilder.GetBufferPointer(), meshBuilder.GetSize(), meshSetting, settingMgr.mLossCompressor, dstNdaPath);
    return ret;
}
}