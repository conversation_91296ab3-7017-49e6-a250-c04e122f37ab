#include "AssetCooker.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/filesystem.h"
#include "Resource/AssetStreaming.h"
#include "Resource/ResourceManager.h"
#include "Resource/resourceasset.h"


namespace cross::editor {
bool AssetCooker::CookResourceMetaHeader(const ResourceMetaHeader& inMetaHeader, std::vector<UInt8>& outData)
{
    SerializeNode metaHeaderNode;
    metaHeaderNode[Resource::Version_KEY] = Resource::gResourceJsonHeaderVersion;
    metaHeaderNode[Resource::Guid_KEY] = inMetaHeader.mGuid;
    metaHeaderNode[Resource::ClassID_KEY] = inMetaHeader.mClassID;
    metaHeaderNode[Resource::DataSize_KEY] = inMetaHeader.mDataSize;
    metaHeaderNode[Resource::ContentType_KEY] = (CONTENT_TYPE)inMetaHeader.mContentType == CONTENT_TYPE::CONTENT_TYPE_JSON ? (UInt32)CONTENT_TYPE::CONTENT_TYPE_BSON : inMetaHeader.mContentType;
    metaHeaderNode[Resource::IsStreamFile_KEY] = inMetaHeader.mIsStreamFile;
    if (inMetaHeader.mCustomInfo != "")
        metaHeaderNode[Resource::Custom_KEY] = SerializeNode::ParseFromJson(inMetaHeader.mCustomInfo);
    SerializeNode dependenceSeriliazeNode;
    for (const auto& depenceItem : inMetaHeader.mDependency)
        dependenceSeriliazeNode.PushBack(depenceItem);
        //dependenceSeriliazeNode.PushBack(gResourceMgr.ConvertGuidToPath(depenceItem));
    metaHeaderNode[Resource::Dependence_KEY] = std::move(dependenceSeriliazeNode);
    outData = metaHeaderNode.FormatToBin();
    return true;
}

ResourceMetaHeader AssetCooker::GenResourceMetaHeader(const resource::LoadNDAFileInfo& fileInfo)
{
    ResourceMetaHeader metaHeader;
    metaHeader.mMagicNumber = fileInfo.GetMagicNumber();
    metaHeader.mVersion = fileInfo.GetVersion();
    metaHeader.mClassID = fileInfo.GetClassID();
    metaHeader.mContentType = (UInt32)fileInfo.GetContentType();
    metaHeader.mDataSize = fileInfo.GetDataSize();
    return metaHeader;
}

bool AssetCooker::CookImport(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* filepath)
{
    // gen meta header
    ResourceMetaHeader metaHeader = fileInfo.HasMetaHeader() ? fileInfo.GetMetaHeader() : GenResourceMetaHeader(fileInfo);
    std::string headerStr = Resource::Serialize(metaHeader);
    // compress content
    auto offset = fileInfo.GetOffet();
    BinaryArchive* contentBinData = new BinaryArchive(const_cast<UInt8*>(fileData->Data() + offset), fileData->Size() - offset);
    bool compressed = false;
    if (auto compressData = gDataCompressor->TryCompress(CompressorType::LZ4, contentBinData->Data(), contentBinData->Size()))
    {
        DELETE_BINARYDATA(contentBinData, true);
        contentBinData = compressData;
        compressed = true;
    }
    // gen file data
    std::stringstream fileContent;
    fileContent.write(headerStr.c_str(), headerStr.length());
    fileContent.write(reinterpret_cast<const char*>(contentBinData->Data()), contentBinData->Size());

    std::string fileString = fileContent.str();
    EngineGlobal::GetFileSystem()->Save(filepath, fileString.c_str(), fileString.size());
    DELETE_BINARYDATA(contentBinData, !compressed);
    return true;
}

bool AssetCooker::WriteAsset(const resource::LoadNDAFileInfo& fileInfo, const UInt8* contentData, SizeType contentSize, ICookSetting* cookSetting, CompressorType comTp, const char* filePath)
{   
    // gen meta header
    ResourceMetaHeader metaHeader = fileInfo.HasMetaHeader() ? fileInfo.GetMetaHeader() : GenResourceMetaHeader(fileInfo);
    metaHeader.mDataSize = static_cast<SInt32>(contentSize);
    std::vector<UInt8> headerBinData;
    CookResourceMetaHeader(metaHeader, headerBinData);
    // compress content
    BinaryArchive* contentBinData = new BinaryArchive(const_cast<UInt8*>(contentData), contentSize);
    bool compressed = false;
    if (cookSetting && cookSetting->mIsCompress)
    {
        BinaryArchive* compressData = gDataCompressor->TryCompress(comTp, contentBinData->Data(), contentBinData->Size());
        if (compressData) {
            DELETE_BINARYDATA(contentBinData, true);
            contentBinData = compressData;
            compressed = true;
        }
    }
    SizeType headerSize = headerBinData.size();
    BinaryArchive* newFileData = new BinaryArchive(sizeof(ASSET_MAGIC_NUMBER_BMETA) + sizeof(headerSize) + headerBinData.size() + contentBinData->Size());
    newFileData->Write(&ASSET_MAGIC_NUMBER_BMETA, sizeof(ASSET_MAGIC_NUMBER_BMETA));
    newFileData->Write(&headerSize, sizeof(headerSize));
    newFileData->Write(&headerBinData[0], headerBinData.size());
    newFileData->Write(contentBinData->Data(), contentBinData->Size());
    // save
    bool ret = EngineGlobal::GetFileSystem()->Save(filePath, reinterpret_cast<const char*>(newFileData->Data()), newFileData->Size());
    DELETE_BINARYDATA(contentBinData, !compressed);
    DELETE_BINARYDATA(newFileData, true);
    return ret;
}
}   // namespace cross::editor
