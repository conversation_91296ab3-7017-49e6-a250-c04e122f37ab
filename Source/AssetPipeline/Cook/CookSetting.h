#pragma once

// Include
#include "EnginePrefix.h"
#include "ECS/Develop/Framework/Types.h"
#include "Resource/Resource.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "Resource/DataCompressor.h"

namespace cross::editor {
class ICookSetting
{
public:
    //@brief Serialize
    virtual SerializeNode Serialize();
    //@brief Deserialize
    virtual bool Deserialize(const DeserializeNode& node);
    //must has class name
    static constexpr const char* ClassName = "ICookSetting";

public:
    CEProperty(Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Compress")) 
    bool mIsCompress = true;

    CEProperty(Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Cook")) 
    bool mIsCook = true;
};

class ASSET_API CookSettingManager : cross::NonCopyable
{
public:
    //@brief Constructor
    CookSettingManager();
    //@brief Constructor
    CookSettingManager(const char* path);
    //@brief Destructor
    ~CookSettingManager();
    //@brief GetCookSetting
    template<typename T> 
    inline T* GetCookSetting()
    {
        if (mCookSettingMap.find(T::ClassName) == mCookSettingMap.end())
            mCookSettingMap.emplace(T::ClassName, gFactoryMap[T::ClassName]());
        return static_cast<T*>(mCookSettingMap[T::ClassName]);
    }
    //@brief GetCookSetting
    CEFunction(Editor)
    inline ICookSetting& GetCookSetting(const std::string& className)
    {
        if (mCookSettingMap.find(className) == mCookSettingMap.end())
            mCookSettingMap.emplace(className, gFactoryMap[className]());
        return *mCookSettingMap[className];
    }
    //@brief Load
    CEFunction(Editor)
    void Load();
    //@brief Dump
    CEFunction(Editor)
    void Dump();
    //@brief GetConfigs
    CEFunction(Editor)
    std::vector<std::string> GetCookTypes();
    //@brief SetPath
    inline void SetPath(const char* path) {mPath = path;};
public:
    // global setting
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Default Loss Compressor"))
    CEProperty(Editor)
    CompressorType mLossCompressor = CompressorType::LZ4;
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "LZ4 Use HC")) 
    CEProperty(Editor) 
    bool mIsLZ4HC = true;
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "LZ4 Acceleration Factor")) 
    CEProperty(Editor)
    int mLZ4AccelerationFactor = 1;

private:
    std::string mPath{"CookConfig.json"};
    bool        mIsLoad = false;

private:
    using CookSettingMap = std::unordered_map<std::string, ICookSetting*>;
    CookSettingMap  mCookSettingMap;

public:
    //@brief Initialize
    static void Initialize();
    //@brief Finitialize
    static void Finitialize();

private:
    //@brief Register
    template<typename T>
    static void Register(){ gFactoryMap[T::ClassName] = &T::Produce; };

    using  CookSettingFactoryFunc = ICookSetting* (*)(void);
    using  CookSettingFactoryFuncMap = std::map<std::string, CookSettingFactoryFunc>;
    static CookSettingFactoryFuncMap gFactoryMap;
};
};   // namespace cross::editor
