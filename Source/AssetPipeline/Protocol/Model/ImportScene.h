#pragma once
#include <vector>
#include "CrossPhysics/PhysicsEngine/PhysicsImportStruct.h"
#include "AssetPipeline/Import/ModelImporter/MeshDescription.h"
#include "AssetPipeline/Import/ModelImporter/MaterialDescription.h"
#include "AssetPipeline/Protocol/Model/ImportCurveData.h"

namespace cross::editor {

using MeshDescIndex = UInt32;

struct ASSET_API MaterialMeshBind
{
    void Reserve(UInt32 capacity)
    {
        meshDescIndices.reserve(capacity);
        materialAssetPaths.reserve(capacity);
    }

    void AddMeshDescIndex(MeshDescIndex index)
    {
        meshDescIndices.emplace_back(index);
    }

    void AddMaterialPath(const std::string& path)
    {
        materialAssetPaths.emplace_back(PathHelper::GetRelativePath(path));
    }

    const std::vector<MeshDescIndex>& GetMeshDescIndices() const
    {
        return meshDescIndices;
    }

    const std::string& GetMaterialPath(UInt32 index) const
    {
        static std::string emptyString;
        if (materialAssetPaths.empty())
        {
            return emptyString;
        }
        return materialAssetPaths[index];
    }

    const UInt32 GetMaterialCount() const
    {
        return static_cast<UInt32>(materialAssetPaths.size());
    }

private:
    std::vector<MeshDescIndex> meshDescIndices;
    std::vector<std::string> materialAssetPaths;
};

class CurveDataDesc;
struct ASSET_API ImportScene
{
    std::string Name;

    PhysicsCollisionImport PhyCollision;

    UInt32 LodCount{0};
    std::vector<UInt32> LodStartIndexArray;

    std::vector<MeshDescription> MeshDescriptions;
    std::vector<MaterialDescription> MaterialDescriptions;
    // 1(MaterialDesc) <--> n(MeshDesc), this vector index match MaterialDescriptions's and hold MeshDescriptions's index as content.
    std::vector<MaterialMeshBind> MaterialMeshBindings;

    std::unordered_map<void*, UInt32> MaterilGlobalIndexMap;

    std::vector<std::vector<SInt32>> MatIdxToTexIds;

    SkeletonDesc ImportSkeltData;
    std::vector<AnimationDesc> ImportAnimations;

    CurveDataDesc ImportCurveData;

    // TODO: Import assets directly to engine resource and Hand over the responsibility of serialization and deserialization to the resource itself.
};

}   // namespace cross::editor
