#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Template/TypeSafeHandle.hpp"

namespace cross::editor {

struct SkeletonDesc
{
    struct Bone
    {
        /* Identifier from fbx file's hierarchy node */
        std::string BoneName;

        /* Index in ImportSkeleton Nodes' array */
        UInt32 BoneID;

        UInt32 ParentID;

        SInt32 HierarchyDepth = -1;

        /* Used for verteices those in mesh space convert to this bone space */
        Float4x4 BindPoseInv = Float4x4::Identity();

        /* Used for bone convert from local space to world space which represent pose at 0 animation frame */
        Float4x4 RefPoseWorld = Float4x4::Identity();

        /* Used for bone convert from local space to world space grabbed from fbx file's hierarchy,
         * which should be symmetric alone an axis but 'RefPoseWorld' can't promise that
         */
        Float4x4 RefPoseBind = Float4x4::Identity();
    };

    inline int FindBoneIndex(std::string name)
    {
        for (int i = 0; i < Bones.size(); i++)
        {
            if (Bones[i].BoneName == name)
                return i;
        }

        return -1;
    }

    std::string Name;
    std::vector<Bone> Bones;
};

using SkBoneHandle = THandle<SkeletonDesc::Bone, UInt32>;
#define SK_BONE_INDEX_NONE SkBoneHandle::InvalidHandle()

}   // namespace cross::editor
