#pragma once
#include "AssetPipeline/Utils/AssetMath.h"
namespace cross::editor
{
    struct ImportNode
    {
        cross::Float4x4 LocalTransform = cross::Float4x4::Identity();
        int                         MeshIndex = -1;
        std::string                 Name;
        std::vector<ImportNode>     Children;
		bool						IsRootInImportMesh = false;
		int							BoneIndexInImportMesh = -1;	// -1 means cur-node not a bone
    };
}