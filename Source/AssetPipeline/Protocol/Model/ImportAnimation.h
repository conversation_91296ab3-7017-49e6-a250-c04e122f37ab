#pragma once
#include "CECommon/Animation/Curve/FloatCurve.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"

namespace cross::editor {

class ASSET_API AnimationDesc
{
public:
    struct TranslationKey
    {
        float Time;
        Float3 Translation;

        static FORCE_INLINE const TranslationKey& Zero()
        {
            static TranslationKey key{0.0f, Float3::Zero()};
            return key;
        }
    };

    struct RotationKey
    {
        float Time;
        Quaternion Rotation;

        static FORCE_INLINE const RotationKey& Zero()
        {
            static RotationKey key{0.0f, Quaternion::Identity()};
            return key;
        }
    };

    struct ScaleKey
    {
        float Time;
        Float3 Scale;

        static FORCE_INLINE const ScaleKey& One()
        {
            static ScaleKey key{0.0f, Float3::One()};
            return key;
        }
    };

    struct Track
    {
        std::vector<TranslationKey> TranslationKeys;
        std::vector<RotationKey> RotationKeys;
        std::vector<ScaleKey> ScaleKeys;

        FORCE_INLINE bool empty()
        {
            return TranslationKeys.empty();
        }
    };

    template<typename T>
    static float GetKeyTime(const T& key)
    {
        return key.Time;
    }

    static TranslationKey InterpTranslationKey(const TranslationKey& keyOne, const TranslationKey& keyTwo, float alpha)
    {
        Float3 vec = (1 - alpha) * keyOne.Translation + alpha * keyTwo.Translation;
        return {0.0f, vec};
    }

    static float TranslationKeyDistance(const TranslationKey& keyOne, const TranslationKey& keyTwo)
    {
        return (keyOne.Translation - keyTwo.Translation).Length();
    }

    static RotationKey InterpRotationKey(const RotationKey& keyOne, const RotationKey& keyTwo, float alpha)
    {
        Quaternion quat = Quaternion::Slerp(keyOne.Rotation, keyTwo.Rotation, alpha);
        return {0.0f, quat};
    }

    static float RotationKeyDistance(const RotationKey& keyOne, const RotationKey& keyTwo)
    {
        return keyOne.Rotation.AngularDistance(keyTwo.Rotation);
    }

    static ScaleKey InterpScaleKey(const ScaleKey& keyOne, const ScaleKey& keyTwo, float alpha)
    {
        Float3 vec = (1 - alpha) * keyOne.Scale + alpha * keyTwo.Scale;
        return {0.0f, vec};
    }

    static float ScaleKeyDistance(const ScaleKey& keyOne, const ScaleKey& keyTwo)
    {
        return (keyOne.Scale - keyTwo.Scale).Length();
    }

    static FORCE_INLINE float DefaultSampleRate()
    {
        return 30.0f;
    }

public:
    std::string Name;
    float Duration;
    std::vector<Track> TracksForAllBones;

    bool HasRootMotion = false;
    SInt32 RootMotionBoneId = -1;

    // Imported anim curves
    FloatCurveList CurveList;

    UInt32 GetFrameCount() const
    {
        UInt32 frameNum = 0;

        auto boneNum = TracksForAllBones.size();
        for (auto i = 0; i < boneNum; ++i)
        {
            if (TracksForAllBones[i].TranslationKeys.size() > 0 && frameNum > 0)
                Assert(TracksForAllBones[i].TranslationKeys.size() == frameNum);

            frameNum = (std::max)(frameNum, static_cast<UInt32>(TracksForAllBones[i].TranslationKeys.size()));
        }

        return frameNum;
    }
};

}   // namespace cross::editor
