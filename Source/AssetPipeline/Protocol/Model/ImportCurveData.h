#pragma once
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "Resource/Animation/Curve/CurveControllerRes.h"

namespace cross::editor {

class ASSET_API CurveDataDesc
{
public:
    enum SystemType
    {
        Unkonw = 0,
        Transform,
        Camera,
        Light,

        Count,
    };

    CurveControllerResPtr Res;
    std::map<UInt64, void*> EntityMap;

    std::string Staging;
    std::string Refference;
    std::string RefferencePrefab;

    void Reset();

    static void SetupSystemProperty(CurveDataDesc::SystemType type, cross::CurveControllerDataItem& item);
};

}   // namespace cross::editor