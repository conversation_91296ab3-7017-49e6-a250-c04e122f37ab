#pragma once

// nda common
// TODO .. move nda common to protocol
#include "CECommon/Utilities/NDACommon.h"
#include "Resource/BaseClasses/ClassIDs.h"

#include "AssetPipeline/Protocol/Shader/ShaderCommonDef.h"
#include "AssetPipeline/Protocol/Texture/TextureDefines.h"
#include "AssetPipeline/Protocol/Model/ImportNode.h"
#include "ImportMesh_generated.h"
#include "ImportMeshAssetData_generated.h"
#include "AssetPipeline/Protocol/Model/ImportSkeleton.h"
#include "AssetPipeline/Protocol/Model/ImportAnimation.h"
#include "AssetPipeline/Protocol/Model/ImportCurveData.h"
#include "AssetPipeline/Protocol/Model/ImportScene.h"
#include "AssetPipeline/Protocol/Model/ImportMeshAssetData.h"
#include "ResourceAsset_generated.h"
#include "ImportSkeleton_generated.h"
#include "ImportAnimation_generated.h"
