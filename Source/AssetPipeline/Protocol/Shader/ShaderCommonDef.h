#pragma once

#include <boost/preprocessor.hpp>

#define CROSS_PP_OPERATOR_OR() |
#define CROSS_PP_OPERATOR_OR_IF(cond) BOOST_PP_IF(cond, CROSS_PP_OPERATOR_OR, BOOST_PP_EMPTY)()
#define CROSS_MAKE_SHADER_CHANNEL(data, t) (1 << BOOST_PP_CAT(data, t))
#define VERTEX_FORMAT_CHANNDLE(r, data, i, t) CROSS_PP_OPERATOR_OR_IF(i) CROSS_MAKE_SHADER_CHANNEL(data, t)

#define VERTEX_FORMAT(...) (BOOST_PP_SEQ_FOR_EACH_I(VERTEX_FORMAT_CHANNDLE, ShaderChannel, BOOST_PP_VARIADIC_TO_SEQ(__VA_ARGS__)))

namespace cross::editor
{
    enum ShaderChannelType
    {
        ShaderChannelNone = -1,

        ShaderChannelPosition = 0,	// Position (vector3)
        ShaderChannelNormal,		// Normal (vector3)
        ShaderChannelColor,			// Vertex color
        ShaderChannelTexCoord0,		// UV set 0 (vector2)
        ShaderChannelTexCoord1,		// UV set 1 (vector2)
        ShaderChannelTangent,		// Tangent (vector4)

		ShaderChannelSkinWeight,	// Bone Weight (vector4)
		ShaderChannelSkinBoneId,	// Bone Id (vector4)

        ShaderChannelCount,			//  Keep this last!
    };
}