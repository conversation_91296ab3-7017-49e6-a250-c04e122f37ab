#pragma once
#include "EnginePrefix.h"
#include "TextureAsset_generated.h"

namespace CrossSchema
{
    constexpr bool IsCubeType(TextureDimension t)
    {
        return t == TextureDimension::TexCube || t == TextureDimension::TexCubeArray;
    }

    constexpr bool IsArrayType(TextureDimension t)
    {
        return t == TextureDimension::Tex2DArray || t == TextureDimension::TexCubeArray;
    }

    enum class RenderTextureDimension : UInt32
    {
		RenderTextureDimension_None = 0,

        Tex2D,
        TexCube
    };

    enum class DepthTextureDimension : UInt32
    {
		DepthTextureDimension_None = 0,

        Tex2D,
        TexCube
    };

    constexpr bool IsIntFormat(TextureFormat fmt)
    {
        return fmt >= TextureFormat::A8 && fmt <= TextureFormat::R16;
    }

    constexpr bool IsFloatFormat(TextureFormat fmt)
    {
        return fmt >= TextureFormat::RHalf && fmt <= TextureFormat::R11G11B10Float;
    }

    constexpr bool IsCompressFormat(TextureFormat fmt)
    {
        return fmt >= TextureFormat::PVRTC_RGB2 && fmt <= TextureFormat::PVRTC_RGBA4;
    }

    constexpr bool IsETCFormat(TextureFormat fmt)
    {
        return fmt >= TextureFormat::ETC_RGB4 && fmt <= TextureFormat::ETC2_RGBA8;
    }

    constexpr bool IsBCFormat(TextureFormat fmt)
    {
        return fmt >= TextureFormat::BC1 && fmt <= TextureFormat::BC7;
    }

    constexpr bool IsASTCFormat(TextureFormat fmt)
    {
        return fmt >= TextureFormat::ASTC_4x4 && fmt <= TextureFormat::ASTC_HDR_12x12;
    }

    constexpr UInt32 GetPixelByteSize(TextureFormat fmt)
    {
        if (IsASTCFormat(fmt))
            return 16;
        switch (fmt)
        {
        case TextureFormat::A8:
        case TextureFormat::R8:
            return 1;
        case TextureFormat::RG16:
        case TextureFormat::RGB565:
        case TextureFormat::R16:
        case TextureFormat::RHalf:
        case TextureFormat::RGBA4444:
            return 2;
        case TextureFormat::RGBX32:
        case TextureFormat::RGBA32:
        case TextureFormat::RFloat:
        case TextureFormat::RGHalf:
        case TextureFormat::RGB9e5Float:
        case TextureFormat::R11G11B10Float:
            return 4;
        case TextureFormat::RGFloat:
        case TextureFormat::RGBAHalf:
            return 8;
        case TextureFormat::RGBFloat:
            return 12;
        case TextureFormat::RGBAFloat:
            return 16;
        default:
            //AssertMsg(false, "None Pixel Format");
            return 0;
        }
    }
}