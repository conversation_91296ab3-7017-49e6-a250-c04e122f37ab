#pragma once

#include <string>
#include "EnginePrefix.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"

namespace cross::editor {
struct ASSET_API ImportSetting
{
    CE_Virtual_Serialize_Deserialize;

    CEFunction(Editor)
    std::string SerializeToString() const
    {
        SerializeContext context;
        return Serialize(context).FormatToJson();
    }

    CEFunction(Editor)
    bool DeserializeFromString(const std::string& nodeStr)
    {
        SerializeNode node = SerializeNode::ParseFromJson(nodeStr);
        SerializeContext context;
        return Deserialize(node, context);
    }
    CEFunction(Editor)
    void SetEngineImportSetting()
    { 
        SetEngineImportSettingImp();
    }

    virtual void SetEngineImportSettingImp()
    {

    }
};
}
