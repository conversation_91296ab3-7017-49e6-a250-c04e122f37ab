#pragma once

#include "EnginePrefix.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/TextureImporter/TextureImporter.h"
#include "FontImportSetting.h"

namespace cross::editor
{

    class FontImporter : public AssetImporter
    {
    public:
        FontImporter();
        ~FontImporter();

    public:
        bool CheckAssetName(const char* name) const override;
        void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting) override;

    private:
        bool ImportFontInfo(const std::string& assetFilename, FontResourcePtr fontRes);
        bool ImportMSDFResource(const std::string& assetFilename, FontResourcePtr fontRes);

        FontImportSetting mImportSetting;
        TextureImporter mTextureImporter;
        TextureImportSetting mMSDFSetting{ TextureType::ImageTexture, Linear, Uncompressed, false, true, false, false, false, false};
    };
}