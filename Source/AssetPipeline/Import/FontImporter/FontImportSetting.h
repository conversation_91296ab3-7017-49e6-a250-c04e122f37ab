#pragma once

#include "EnginePrefix.h"
#include "CrossBase/Math/CrossMath.h"
#include "AssetPipeline/Import/AssetImportSetting.h"
#include "AssetPipeline/Import/TextureImporter/TextureImportSetting.h"

namespace cross::editor
{
    struct ASSET_API FontImportSetting : ImportSetting
    {
        CE_Virtual_Serialize_Deserialize;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "StringAsFile", ToolTips = "", FileTypeDescriptor = "Font Info#json"))
        std::string FontInfoResource;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "StringAsFile", ToolTips = "", FileTypeDescriptor = "MSDF Resource#png"))
        std::string MSDFResource;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "List", ChildPropertyType = "String", ToolTips = "List of mip level image paths for mipmap generation"))
        std::vector<std::string> MipPaths;


        static FontImportSetting gFontImportSetting;
        void SetEngineImportSettingImp() { gFontImportSetting = *this; }
    };
}