#include "FontImporter.h"
#include "Resource/ResourceManager.h"
#include "Resource/FontResource.h"
#include "Resource/AssetStreaming.h"

namespace cross::editor
{
    FontImporter::FontImporter() : AssetImporter(AssetType::Font) {}

    FontImporter::~FontImporter() {}

    bool FontImporter::CheckAssetName(const char* name) const
    {
        return HasExtension(name, ".otf") || HasExtension(name, ".ttf");
    }

    void FontImporter::ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting)
    {
        mImportSetting = *static_cast<FontImportSetting*>(setting);
        if (mImportSetting.FontInfoResource.empty() || mImportSetting.MSDFResource.empty())
        {
            mImportResult = AssetImportState::OpenFileFail;
            return;
        }
        
        auto fontResource = gResourceMgr.CreateResourceAs<resource::FontResource>();

        if (!ImportFontInfo(mImportSetting.FontInfoResource, fontResource))
        {
            mImportResult = AssetImportState::OpenFileFail;
            return;
        }

        if (!ImportMSDFResource(mImportSetting.MSDFResource, fontResource))
        {
            mImportResult = AssetImportState::OpenFileFail;
            return;
        }

        if (fontResource->GetFontName().empty())
        {
            fontResource->SetFontName(PathHelper::GetBaseFileName(ndaSavePath));
        }

        fontResource->Serialize(ndaSavePath);
    }

    bool FontImporter::ImportFontInfo(const std::string& assetFilename, FontResourcePtr fontRes)
    {
        std::string file_content;
        {
            UInt64 size = 0;
            std::string absolute_path = PathHelper::GetAbsolutePath(assetFilename);
            filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
            Assert(fileSystem);
            filesystem::IFilePtr file = fileSystem->Open(absolute_path);
            Assert(file);

            size = file->GetSize();
            file_content.resize(size);
            file->Read(file_content.data(), size);
            file->Close();
        }

        if (file_content.empty())
        {
            return false;
        }

        DeserializeNode node = DeserializeNode::ParseFromJson(file_content);
        fontRes->SetFontInfoByJson(node);
        return true;
    }

    bool FontImporter::ImportMSDFResource(const std::string& assetFilename, FontResourcePtr fontRes)
    {
        LOG_DEBUG("Import font msdf texture: {}", assetFilename);

        std::string baseName = PathHelper::GetBaseFileName(assetFilename);
        std::string baseDir = PathHelper::GetDirectoryFromAbsolutePath(assetFilename);
        std::string ndaFilePath = baseDir + baseName + "Tex.nda";
        mMSDFSetting.ImportTextureGroup = ImportTextureGroup::UI;
        
        // Check if we have multiple mip levels to combine
        if (!mImportSetting.MipPaths.empty())
        {
            LOG_DEBUG("Combining {} mip levels for font texture", mImportSetting.MipPaths.size());
            
            // Combine multiple mip levels into a single texture
            if (!mTextureImporter.CombineMipLevels(mImportSetting.MipPaths, ndaFilePath, mMSDFSetting))
            {
                LOG_ERROR("Failed to combine mip levels for font texture");
                return false;
            }
        }
        else
        {
            // Import single MSDF texture as before
            mTextureImporter.ImportAsset(assetFilename, ndaFilePath, &mMSDFSetting);
        }

        auto texture = gAssetStreamingManager->GetResource(ndaFilePath);
        if (texture)
        {
            fontRes->SetMSDFTexture(TypeCast<cross::resource::Texture>(texture));
            return true;
        }
        return false;
    }
}