#include "EnginePrefix.h"
#include "ShaderMaps.h"
#include "IncludeHandler.h"
#include "flatbuffers/minireflect.h"
#include "Resource/ResourceManager.h"
#include <filesystem>
#include <regex>

#if 1
#    include <chrono>
#    define TICK(x) auto bench_##x = std::chrono::steady_clock::now();
#    define TOCK(x) std::cout << #x ": " << std::chrono::duration_cast<std::chrono::duration<double>>(std::chrono::steady_clock::now() - bench_##x).count() << "s" << std::endl;
#else
#    define TICK(x)
#    define TOCK(x)
#endif

namespace cross::editor {

DECLARE_CPU_TIMING_GROUP(GroupShaderMaps);

ShaderMaps& ShaderMaps::GetInstance()
{
    static ShaderMaps maps;
    return maps;
}

std::string GetBinaryFilePath()
{
    auto workPath = PathHelper::GetCurrentDirectoryPath();
    return workPath + "/Saved/ShaderMaps/ShaderMaps.bin";
}

ShaderMaps::ShaderMaps()
{
    LoadShaderMaps();
}

void ShaderMaps::LoadShaderMaps()
{
    auto absPath = GetBinaryFilePath();
    std::ifstream ifs(absPath, std::ios::in | std::ios::binary);
    if (ifs)
    {
        ifs.seekg(0, std::ios::end);
        std::vector<UInt8> data(ifs.tellg(), 0);
        ifs.seekg(0, std::ios::beg);
        ifs.read(reinterpret_cast<char*>(data.data()), data.size());
        data.resize(static_cast<size_t>(ifs.gcount()));

        flatbuffers::Verifier verifier(data.data(), data.size());
        if (CrossSchema::VerifyShaderMapsBuffer(verifier))
        {
            auto* maps = CrossSchema::GetShaderMaps(data.data());
            maps->UnPackTo(&mShaderMaps);

            for (auto& moduleMap : mShaderMaps.modulemaps)
            {
                mShaderModuleMaps.insert(std::make_pair(moduleMap->guid, moduleMap.get()));

                std::string moduleName = std::filesystem::path{moduleMap->subname}.filename().string();
                if (mShaderModuleNameMaps.find(moduleName) == mShaderModuleNameMaps.end())
                {
                    mShaderModuleNameMaps[moduleName] = {};
                }

                mShaderModuleNameMaps[moduleName].emplace_back(moduleMap.get());
            }

            for (auto& sourceMap : mShaderMaps.sourcemaps)
            {
                CrossUUID guid{sourceMap->guid->low(), sourceMap->guid->high()};
                mShaderSourceMaps.insert(std::make_pair(guid, sourceMap.get()));
            }
        }
    }
}

void SearchShaderSourceFiles(const std::string& dir, std::vector<std::string>& outFiles)
{
    if (!std::filesystem::exists(dir))
        return;

    for (const auto& file : std::filesystem::recursive_directory_iterator(dir))
    {
        if (!file.is_directory())
        {
            auto ext = file.path().extension();
            if (ext == ".shader" || ext == ".compute")
            {
                std::string s = file.path().string();
                cross::PathHelper::Normalize(s);
                outFiles.emplace_back(s);
            }
        }
    }
}

std::string RemoveComments(const std::string& comments)
{
    std::string result;
    result.reserve(comments.size());

    bool s_comment = false;
    bool m_comment = 0;

    for (std::size_t i = 0; i < comments.size(); i++)
    {
        if (s_comment)
        {
            if (comments[i] == '\n')
            {
                s_comment = false;
            }

            continue;
        }
        else if (m_comment)
        {
            if (comments[i] == '*' && comments[i + 1] == '/')
            {
                m_comment = false;
                i++;
            }

            continue;
        }

        if (comments[i] == '/')
        {
            if (comments[i + 1] == '/')
            {
                s_comment = true;
                i++;
            }
            else if (comments[i + 1] == '*')
            {
                m_comment = true;
                i++;
            }
        }

        if (!s_comment && !m_comment)
            result += comments[i];
    }
    return result;
}

void SearchIncludeModuleFiles(const std::string& source, std::vector<std::string>& outFiles)
{
    for (std::size_t i = 0; i < source.size(); i++)
    {
        if (source[i] == '#')
        {
            for (std::size_t j = i + 1; i < source.size(); j++)
            {
                if (source[j] != ' ' && source[j] != '\t')
                {
                    i = j;
                    break;
                }
            }

            if (source[i + 0] == 'i' && source[i + 1] == 'n' && source[i + 2] == 'c' && source[i + 3] == 'l' && source[i + 4] == 'u' && source[i + 5] == 'd' && source[i + 6] == 'e')
            {
                i += 6;

                for (std::size_t j = i + 1; i < source.size(); j++)
                {
                    if (source[j] != ' ' && source[j] != '\t')
                    {
                        i = j;
                        break;
                    }
                }

                if (source[i] == '"' || source[i] == '<')
                {
                    std::size_t j = i + 1;
                    for (; i < source.size(); j++)
                    {
                        if (source[j] == '"' || source[j] == '>')
                        {
                            break;
                        }
                    }

                    // std::cout << "[include]: " << source.substr(i + 1, j - i - 1) << std::endl;
                    outFiles.emplace_back(source.substr(i + 1, j - i - 1));

                    i = j + 1;
                }
            }
        }
    }
}

void RecurseShaderModuleFiles(std::tuple<SizeType, const void*> cache, const std::filesystem::path& prefix, IncludeHandler& includer)
{
    const auto& [size, data] = cache;
    if (!data)
        return;

    /*static const std::regex gIncludePattern{R"""(\s*#\s*include\s*([<"])([^>"]+)([>"]))"""};
    static const std::regex gCommentPattern{R"""(\/\*[^*]*\*+(?:[^/*][^*]*\*+)*\/|\/{2,}.*)"""};*/
    std::vector<std::string> includes;
    std::string s0{reinterpret_cast<const char*>(data), size};
    std::string s;
    {
        SCOPED_CPU_TIMING(GroupShaderMaps, "CommentPattern");
        s = RemoveComments(s0);
    }

    // try
    //{
    //     {
    //         SCOPED_CPU_TIMING(GroupShaderMaps, "CommentPattern");
    //         s = std::regex_replace(s0, gCommentPattern, "");
    //     }
    // }
    // catch (const std::exception&)
    //{
    //     // assert(0);
    // }

    {
        SCOPED_CPU_TIMING(GroupShaderMaps, "IncludePattern");
        // for (std::sregex_iterator itr{s.begin(), s.end(), gIncludePattern}, end{}; itr != end; ++itr)
        //{
        //     if (itr->size() > 0)
        //     {
        //         std::string match_str = itr->str(2);
        //         cross::PathHelper::Normalize(match_str);
        //         includes.emplace_back(match_str);
        //         // std::cout << "[include]: " << match_str << '\n';
        //     }
        // }

        SearchIncludeModuleFiles(s, includes);
    }

    if (includes.size())
    {
        for (const auto& m : includes)
        {
            std::filesystem::path pm = prefix / m;
            auto c = includer.OpenPath(pm);
            if (std::get<1>(c))
            {
                RecurseShaderModuleFiles(c, pm.parent_path(), includer);
            }
            else
            {
                std::filesystem::path cm = m;
                auto c2 = includer.OpenPath(cm);
                if (std::get<1>(c2))
                {
                    RecurseShaderModuleFiles(c2, cm.parent_path(), includer);
                }
            }
        }
    }
}

void ShaderMaps::BuildShaderMaps()
{
    SCOPED_CPU_TIMING(GroupShaderMaps, "BuildShaderMaps");
    LOG_INFO("Build Shader Maps Begin");
    TICK(BuildShaderMaps);

    std::vector<std::string> shaderSources{};
    SearchShaderSourceFiles(PathHelper::GetCurrentDirectoryPath() + "/Contents", shaderSources);
    SearchShaderSourceFiles(PathHelper::GetEngineResourceDirectoryPath(), shaderSources);

    for (const auto& source : shaderSources)
    {
        resource::ResourceLoadError err;
        std::string ndaRes = source + ".nda";
        ndaRes = PathHelper::GetRelativePath(ndaRes);
        ResourcePtr res = nullptr;
        {
            SCOPED_CPU_TIMING(GroupShaderMaps, "ShaderGUID");
            res = gResourceAssetMgr.LoadNDAFile(ndaRes.c_str(), err);
        }

        if (res)
        {
            std::string shader_guid = res->GetGuid_Str();
            // std::string shader_guid = gResourceMgr.GetGuidByPath(source + ".nda");
            // std::cout << "[shader]: " << source << " [guid]:" << shader_guid << std::endl;
            if (shader_guid.size() == 32)
            {
                std::ifstream ifs(source, std::ios_base::in);
                if (ifs)
                {
                    ifs.seekg(0, std::ios::end);
                    std::vector<UInt8> data;
                    data.resize(ifs.tellg(), 0);
                    ifs.seekg(0, std::ios::beg);
                    ifs.read(reinterpret_cast<char*>(data.data()), data.size());
                    data.resize(static_cast<size_t>(ifs.gcount()));

                    IncludeHandler includer{source.c_str()};
                    RecurseShaderModuleFiles(std::tuple<SizeType, const void*>{data.size(), data.data()}, "", includer);
                    includer.RecordFinish(shader_guid, false, true);
                }
            }
        }
    }

    ShaderMaps::GetInstance().SaveToDisk(true);

    TOCK(BuildShaderMaps);
    LOG_INFO("Build Shader Maps Done");
}

void ShaderMaps::SaveToDisk(bool genDebugInfo)
{
    SCOPED_CPU_TIMING(GroupShaderMaps, "SaveToDisk");
    std::lock_guard<std::mutex> guard{mRecordMutex};
    flatbuffers::FlatBufferBuilder flatbuf{4096};
    flatbuf.Finish(CrossSchema::CreateShaderMaps(flatbuf, &mShaderMaps));

    auto absPath = std::filesystem::path{GetBinaryFilePath()};
    std::filesystem::create_directories(absPath.parent_path());

    std::ofstream ostrm(absPath, std::ios::out | std::ios::binary);
    ostrm.write(reinterpret_cast<char*>(flatbuf.GetBufferPointer()), flatbuf.GetSize());

#if 1
    // std::cout << " GenDebugInfo : " << genDebugInfo << std::endl;
    if (genDebugInfo)
    {
        auto jsonText = flatbuffers::FlatBufferToString(flatbuf.GetBufferPointer(), CrossSchema::ShaderMapsTypeTable());
        std::ofstream ostrm2(absPath.replace_extension("json"), std::ios::out);
        ostrm2 << jsonText << '\n';
        ostrm2.close();
    }
#endif
}

std::uint64_t ShaderMaps::GetModuleID(const std::string& subname, const int idx)
{
    cross::HashString moduleID{std::to_string(idx) + "@" + subname};
    // LOG_EDITOR_ERROR("STR: {}  HASH: {}", moduleID.GetString(), moduleID.GetHash64().GetValue());
    return moduleID.GetHash64().GetValue();
}

void ShaderMaps::RecordModuleIncludes(const std::string& include, std::size_t idx)
{
    std::lock_guard<std::mutex> guard{mRecordMutex};
    if (mShaderMaps.moduledirs.size() <= idx)
        mShaderMaps.moduledirs.resize(idx + 1);

    mShaderMaps.moduledirs[idx] = include;
}

bool Equals(const CrossSchema::GUID& L, const CrossSchema::GUID& R)
{
    return L.low() == R.low() && L.high() == R.high();
}

void ShaderMaps::RecordShaderModuleMap(CrossSchema::ShaderModuleMapT& module, CrossSchema::GUID& shader_guid)
{
    SCOPED_CPU_TIMING(GroupShaderMaps, "RecordModule");
    std::lock_guard<std::mutex> guard{mRecordMutex};
    CrossSchema::ShaderModuleMapT* oldMap = nullptr;
    if (auto Found = mShaderModuleMaps.find(module.guid); Found != mShaderModuleMaps.end())
    {
        oldMap = Found->second;
    }

    if (oldMap)
    {
        oldMap->size = module.size;
        oldMap->mtime = module.mtime;
        oldMap->hash = module.hash;
        auto Found = std::find_if(oldMap->shaders.begin(), oldMap->shaders.end(), [&](auto& R) { return Equals(shader_guid, R); });
        if (Found == oldMap->shaders.end())
            oldMap->shaders.emplace_back(shader_guid);
    }
    else
    {
        module.shaders.emplace_back(shader_guid);
        auto& map = mShaderMaps.modulemaps.emplace_back(std::make_unique<CrossSchema::ShaderModuleMapT>(module));
        mShaderModuleMaps[map->guid] = map.get();
    }

    std::string moduleName = std::filesystem::path{module.subname}.filename().string();
    if (mShaderModuleNameMaps.find(moduleName) == mShaderModuleNameMaps.end())
    {
        mShaderModuleNameMaps[moduleName] = {};
        mShaderModuleNameMaps[moduleName].emplace_back(mShaderModuleMaps[module.guid]);
    }
    else
    {
        bool hasDumplicate = false;

        for (auto beg = mShaderModuleNameMaps[moduleName].begin(); beg != mShaderModuleNameMaps[moduleName].end(); beg++)
        {
            if ((*beg)->diridx == module.diridx && (*beg)->subname == module.subname)
            {
                hasDumplicate = true;
                break;
            }
        }

        if (!hasDumplicate)
        {
            mShaderModuleNameMaps[moduleName].emplace_back(mShaderModuleMaps[module.guid]);
        }
    }
}

void ShaderMaps::RecordShaderSourceMap(CrossSchema::ShaderSourceMapT& source)
{
    SCOPED_CPU_TIMING(GroupShaderMaps, "RecordSource");
    std::lock_guard<std::mutex> guard{mRecordMutex};
    CrossUUID guid{source.guid->low(), source.guid->high()};
    if (auto FoundS = mShaderSourceMaps.find(guid); FoundS != mShaderSourceMaps.end())
    {
        for (auto module_guid : FoundS->second->modules)
        {
            if (std::find(source.modules.begin(), source.modules.end(), module_guid) == std::end(source.modules))
            {
                if (auto FoundM = mShaderModuleMaps.find(module_guid); FoundM != mShaderModuleMaps.end())
                {
                    auto L = *source.guid.get();
                    auto& shaders = FoundM->second->shaders;
                    auto FoundMS = std::find_if(shaders.begin(), shaders.end(), [&](auto& R) { return Equals(L, R); });
                    if (FoundMS != std::end(shaders))
                    {
                        shaders.erase(FoundMS);
                    }
                }
            }
        }

        FoundS->second->modules = source.modules;
    }
    else
    {
        auto& map = mShaderMaps.sourcemaps.emplace_back(std::make_unique<CrossSchema::ShaderSourceMapT>());
        map->guid = std::make_unique<CrossSchema::GUID>(*source.guid.get());
        map->modules = source.modules;
        mShaderSourceMaps[guid] = map.get();
    }
}

void ShaderMaps::CheckShaderModuleFile(const char* filePath, std::vector<std::string>& outShaders, ShaderFileOutdateLevelE level)
{
    SCOPED_CPU_TIMING(GroupShaderMaps, "CheckModule");
    const std::string filePathStr = filePath;
    const std::filesystem::path shaderModule{filePath};
    const std::string shaderMoudleName = shaderModule.filename().string();

    if (auto Found = mShaderModuleNameMaps.find(shaderMoudleName); Found != mShaderModuleNameMaps.end())
    {
        auto dirs = IncludeHandler::GetShaderImportIncludes(filePath);
        // std::cout << "module size : " << Found->second.size();
        for (auto& module : Found->second)
        {
            // std::cout << "module name : " << module->subname << " shader size : " << module->shaders.size() << std::endl;
            if (mShaderModuleMaps.find(module->guid) != mShaderModuleMaps.end())
            {
                for (auto& shader_guid : module->shaders)
                {
                    CrossUUID shader_guid2{shader_guid.low(), shader_guid.high()};
                    auto shader_guid_str = shader_guid2.ToString();
                    if (!gResourceMgr.HasGuid(shader_guid_str))
                    {
                        // std::cout << "[" + std::to_string(outShaders.size()) + "]" << "Error: Can`t find guid : " 
                        // << shader_guid_str << std::endl;
                        continue;
                    }
                    else
                    {
                        // std::cout << "[" + std::to_string(outShaders.size()) + "]" << "Has find guid : " 
                        // << shader_guid_str << std::endl;
                    }

                    if (mShaderSourceMaps.find(shader_guid2) != mShaderSourceMaps.end())
                    {
                        auto& shader = mShaderSourceMaps[shader_guid2];
                        auto FS = EngineGlobal::GetFileSystem();
                        auto ndaPath = std::filesystem::path{FS->GetAbsolutePath(gResourceMgr.ConvertGuidToPath(shader_guid_str))};
                        bool hasFound = false;

                        if (module->diridx == 0)
                        {
                            auto relPath = shaderModule.lexically_proximate(ndaPath.parent_path());

                            if (relPath.is_relative())
                            {
                                hasFound = std::find(shader->modules.begin(), shader->modules.end(), module->guid) != shader->modules.end();
                            }
                        }
                        else if (module->diridx < dirs.size())
                        {
                            std::string tryFoundFile = dirs[module->diridx] + "/" + module->subname;

                            if (tryFoundFile == filePathStr)
                            {
                                hasFound = std::find(shader->modules.begin(), shader->modules.end(), module->guid) != shader->modules.end();
                            }
                        }

                        if (hasFound)
                        {
                            bool hasChange = true;
                            if (level > ShaderFileOutdateLevelE::MTIME)
                            {
                                std::ifstream ifs(filePath, std::ios_base::in);
                                if (ifs)
                                {
                                    std::vector<UInt8> data{};
                                    ifs.seekg(0, std::ios::end);
                                    data.resize(ifs.tellg(), 0);
                                    ifs.seekg(0, std::ios::beg);
                                    ifs.read(reinterpret_cast<char*>(data.data()), data.size());
                                    data.resize(static_cast<size_t>(ifs.gcount()));
                                    auto moduleHash = HashFunction::HashString64(reinterpret_cast<const char*>(data.data()), static_cast<SInt32>(data.size()));
                                    hasChange = module->hash != moduleHash.GetValue();
                                    ifs.close();
                                }
                            }

                            if (!hasChange)
                                continue;

                            std::string shader_src = ndaPath.replace_extension().string();
                            if (std::find(outShaders.begin(), outShaders.end(), shader_src) == outShaders.end())
                            {
                                outShaders.emplace_back(shader_src);
                                // LOG_EDITOR_ERROR("Shader: {},  SubName: {},  DirIndex: {}, FullName: {}", ndaPath.string(), module->subname, module->diridx, filePath);

                                // std::cout << "[" + std::to_string(outShaders.size()) + "]"
                                //           << "Has find guid source : " << shader_guid_str << ", ndaPath : " << ndaPath << std::endl;
                            }
                        }
                        else
                        {
                            // std::cout << "[" + std::to_string(outShaders.size()) + "]"
                            //           << "Cant`t find guid source : " << shader_guid_str << ", ndaPath : " << ndaPath << std::endl;
                        }
                    }
                }
            }
        }
    }
}

}   // namespace cross::editor
