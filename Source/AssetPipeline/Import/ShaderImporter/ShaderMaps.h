#pragma once

#include <CrossSchema/ShaderMaps_generated.h>
#include "ShaderImportSettings.h"

namespace cross::editor {

class ShaderMaps
{
private:
    ShaderMaps();

public:
    static ShaderMaps& GetInstance();

    void LoadShaderMaps();
    void BuildShaderMaps();

    void SaveToDisk(bool genDebugInfo = false);

    std::uint64_t GetModuleID(const std::string& subname, const int idx);

    void RecordModuleIncludes(const std::string& include, std::size_t idx);
    void RecordShaderModuleMap(CrossSchema::ShaderModuleMapT& module, CrossSchema::GUID& shader_guid);
    void RecordShaderSourceMap(CrossSchema::ShaderSourceMapT& source);
    
    void CheckShaderModuleFile(const char* filePath, std::vector<std::string>& outShaders, ShaderFileOutdateLevelE Level = ShaderFileOutdateLevelE::ALL);

private:
    std::mutex mRecordMutex;
    CrossSchema::ShaderMapsT mShaderMaps;
    std::unordered_map<uint64_t, CrossSchema::ShaderModuleMapT*> mShaderModuleMaps;
    std::unordered_map<CrossUUID, CrossSchema::ShaderSourceMapT*> mShaderSourceMaps;
    std::unordered_map<std::string, std::vector<CrossSchema::ShaderModuleMapT*>> mShaderModuleNameMaps;
};

}   // namespace cross::editor
