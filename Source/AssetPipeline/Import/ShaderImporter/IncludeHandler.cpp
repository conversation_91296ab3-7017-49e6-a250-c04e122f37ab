#include "EnginePrefix.h"
#include "IncludeHandler.h"
#include "ShaderMaps.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "Resource/ResourceManager.h"
#include <fstream>

const std::vector<std::string> cross::editor::IncludeHandler::GetShaderImportIncludes(const char* filePath)
{
    // Fixed default search order
    // 只增不减，请勿改动顺序
    std::string rpname = "";
    if (EngineGlobal::Inst().GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpTypeHeadless)
    {
        rpname = "FFSRP";
    }
    else
    {
        rpname = EngineGlobal::Inst().GetSettingMgr()->GetRenderPipelineSetting()->UseRenderPipeline.substr(3);
    }
    std::vector<std::string> searchIncludes;
    {
        searchIncludes = {
            PathHelper::GetDirectoryFromAbsolutePathWithoutEndSeparator(filePath),
            PathHelper::GetCurrentDirectoryPath() + "/EngineResource/Shader",
            PathHelper::GetEngineResourceDirectoryPath() + "/EngineResource/Shader",
            PathHelper::GetCurrentDirectoryPath(),
            PathHelper::GetEngineResourceAbsolutePath(std::string("PipelineResource/").append(rpname).append("/Shader")),
            PathHelper::GetEngineResourceAbsolutePath(std::string("PipelineResource/").append(rpname).append("/Shader/Shared")),
            PathHelper::GetCurrentDirectoryPath() + "/Contents",
            PathHelper::GetEngineResourceAbsolutePath(std::string("PipelineResource/").append(rpname).append("/Shader/Material/Lit")),
        };
    }

    return searchIncludes;
}

cross::editor::IncludeHandler::IncludeHandler(const char* filePath)
{
    const auto& searchIncludes = GetShaderImportIncludes(filePath);

    for (const auto& var : searchIncludes)
    {
        mIncludePaths.emplace_back(var);
    }

    std::string path = filePath;
    PathHelper::Normalize(path);
    mFilePath = path;
    mFileDirectoryPath = mFilePath.parent_path();
}

const std::filesystem::path& cross::editor::IncludeHandler::GetFilePath()
{
    return mFilePath;
}

const std::filesystem::path& cross::editor::IncludeHandler::GetFileDirectoryPath() 
{
    return mFileDirectoryPath;
}

void cross::editor::IncludeHandler::AddHeaderDirectory(const std::filesystem::path& includePath)
{
    Assert(includePath.is_absolute());
    std::string str0 = includePath.string();
    PathHelper::Normalize(str0);
    if (std::find_if(mIncludePaths.begin(), mIncludePaths.end(), [&includePath, &str0](const std::filesystem::path& p) {
            std::string str1 = p.string();
            PathHelper::Normalize(str1);
            return str0 == str1;
        }) == mIncludePaths.end())
    {
        mIncludePaths.emplace_back(includePath);
    }
}

std::tuple<SizeType, const void*> cross::editor::IncludeHandler::OpenPath(const std::filesystem::path& relPath)
{
    if (relPath.is_absolute())
    {
        return OpenAbsolutePath("", -1, relPath);
    }
    else
    {
        int index = 0;
        for (auto& dir : mIncludePaths)
        {
            auto [size, data] = OpenAbsolutePath(dir, index, relPath);
            if (data)
            {
                return {size, data};
            }
            index++;
        }
    }
    return {};
}

std::tuple<SizeType, const void*> cross::editor::IncludeHandler::OpenAbsolutePath(const std::filesystem::path& dirPath, int dirIndex, const std::filesystem::path& relPath)
{
    std::lock_guard<std::mutex> guard{mIncludeMutex};

    const std::filesystem::path absPath = (dirPath / relPath).lexically_normal();
    if (auto ret = mFileCache.find(absPath); ret != mFileCache.end())
    {
        return {ret->second.data.size(), ret->second.data.data()};
    }
    else
    {
        std::ifstream ifs(absPath, std::ios_base::in);
        if (ifs)
        {
            ifs.seekg(0, std::ios::end);

            FileCache cache;
            cache.data.resize(ifs.tellg(), 0);
            cache.index = dirIndex;
            cache.relPath = relPath.lexically_normal().string();

            ifs.seekg(0, std::ios::beg);
            ifs.read(reinterpret_cast<char*>(cache.data.data()), cache.data.size());
            cache.data.resize(static_cast<size_t>(ifs.gcount()));

            auto itr = mFileCache.emplace(absPath, std::move(cache));

            return {itr.first->second.data.size(), itr.first->second.data.data()};
        }
    }
    return {};
}

void cross::editor::IncludeHandler::RecordFinish(std::string shader_guid, bool saveToDisk, bool genDebugInfo)
{
    if (!shader_guid.empty())
    {
        for (int i = 0; i < mIncludePaths.size(); i++)
        {
            std::string str0 = mIncludePaths[i].lexically_normal().string();
            PathHelper::Normalize(str0);
            ShaderMaps::GetInstance().RecordModuleIncludes(str0, i);
        }

        CrossUUID shaderGUID{};
        shaderGUID.FromString(shader_guid);
        mSourceMap.guid = std::make_unique<CrossSchema::GUID>(shaderGUID.low, shaderGUID.high);        

        for (auto& includeFile : mFileCache)
        {
            const auto& absPath = includeFile.first;
            const auto& cache = includeFile.second;
            if (cache.index >= 0)
            {
                CrossSchema::ShaderModuleMapT moduleMap{};
                moduleMap.fullname = absPath.string();
                moduleMap.subname = cache.relPath;
                PathHelper::Normalize(moduleMap.fullname);
                PathHelper::Normalize(moduleMap.subname);
                moduleMap.diridx = static_cast<int8_t>(cache.index);

                // LOG_EDITOR_ERROR("Cache Size: {} , Path: {}", mFileCache.size(), absPath.string());
                auto moduleID = ShaderMaps::GetInstance().GetModuleID(moduleMap.subname, moduleMap.diridx);

                moduleMap.guid = moduleID;

                auto moduleHash = HashFunction::HashString64(reinterpret_cast<const char*>(cache.data.data()), static_cast<SInt32>(cache.data.size()));
                moduleMap.hash = moduleHash.GetValue();

                moduleMap.size = cache.data.size();
                moduleMap.mtime = 0;

                struct stat result;
                if (stat(moduleMap.fullname.c_str(), &result) == 0)
                {
                    moduleMap.mtime = static_cast<UInt64>(result.st_mtime);
                }

                ShaderMaps::GetInstance().RecordShaderModuleMap(moduleMap, *mSourceMap.guid.get());
                mSourceMap.modules.emplace_back(moduleID);
            }
        }

        ShaderMaps::GetInstance().RecordShaderSourceMap(mSourceMap);
        
        if (saveToDisk)
        {
            ShaderMaps::GetInstance().SaveToDisk(genDebugInfo);
        }
    }
}