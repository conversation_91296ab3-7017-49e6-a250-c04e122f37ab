#include "Resource/ResourceManager.h"
#include "AssetPipeline/Import/SHPImporter/SHPImporter.h"
#include "CrossBase/String/StringHelper.h"
namespace cross::editor {
const std::unordered_map<std::string, std::string> PropMap = {{"Elevation", "maxheight"}, {"height", "maxheight"}};
SHPImporter::SHPImporter()
    : AssetImporter(AssetType::SHP)
{}

SHPImporter::~SHPImporter() {}

void SHPImporter::ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath /* = "" */, ImportSetting* setting /* = nullptr */)
{
    auto shpSet = static_cast<SHPImportSetting*>(setting);
    mCDTransfer.SetLatLon(shpSet->mLongitude, shpSet->mLatitude);
    SetProgress(0.01f);
    // load
    PCGMap map(shpSet->mType);
    map.mBlockSize = static_cast<Double2>(shpSet->mBlockSize);
    if (!LoadShpFile(assetFilename, map))
    {
        mImportResult = AssetImportState::OpenFileFail;
        return;
    }
    SetProgress(0.3f);
    if (!LoadDbfFile(assetFilename, map))
    {
        mImportResult = AssetImportState::OpenFileFail;
        return;
    }
    SetProgress(0.5f);
    PCGMapRebuilder::Rebuild(map, {shpSet->mRange, shpSet->mDedumplicate, shpSet->mSimplifyMapEpsilon, shpSet->mDistanceThreshold});
    SetProgress(0.99f);
    if (!SaveToNda(map, ndaSavePath))
    {
        mImportResult = AssetImportState::WriteNdaFail;
    }
    SetProgress(0.f);
}

bool SHPImporter::CheckAssetName(const char* name) const
{
    return HasExtension(name, ".shp");
}

bool SHPImporter::LoadShpFile(const std::string& filename, PCGMap& map)
{
    auto fileSystem = EngineGlobal::GetFileSystem();
    if (!fileSystem->HaveFile(filename))
        return false;
    auto fp = fileSystem->Open(filename, true);
    auto fileSize = fp->GetSize();
    FileArchive* fileArch = new FileArchive(fp);
    // 1 file code
    UInt32 fileCode;
    fileArch->Read(&fileCode, sizeof(UInt32));
    fileCode = GetValueByBig(fileCode);
    if (fileCode != 9994)
        return false;
    // 2 unused 5 int
    UInt32 unused[5];
    fileArch->Read(&unused, 5 * sizeof(UInt32));
    // 3 file length
    UInt32 fileLen;
    fileArch->Read(&fileLen, sizeof(UInt32));
    fileLen = GetValueByBig(fileLen);
    // 4 version
    UInt32 version;
    fileArch->Read(&version, sizeof(UInt32));
    if (version != 1000)
        return false;
    // 5 shape
    UInt32 shape;
    fileArch->Read(&shape, sizeof(UInt32));
    //if (shape != 5)
    //    return false;
    // 6 bound
    double bound;
    fileArch->Read(&bound, sizeof(double));
    fileArch->Read(&bound, sizeof(double));
    fileArch->Read(&bound, sizeof(double));
    fileArch->Read(&bound, sizeof(double));
    fileArch->Read(&bound, sizeof(double));
    fileArch->Read(&bound, sizeof(double));
    // 7 measure
    double minMeasure, maxMeasure;
    fileArch->Read(&minMeasure, sizeof(double));
    fileArch->Read(&maxMeasure, sizeof(double));
    // 8 content
    UInt32 recordNum = 0;
    UInt32 recordLen;
    SInt64 nodeNum = 0;
    SInt64 wayNum = 0;
    UInt32 shapeType;
    double box[4];
    UInt32 numParts;
    UInt32 numPoints;
    std::vector<UInt32> parts;
    UInt32 pointNum;
    while (fileArch->Tell() < fileSize)
    {
        fileArch->Read(&recordNum, sizeof(UInt32));
        recordNum = GetValueByBig(recordNum);
        fileArch->Read(&recordLen, sizeof(UInt32));
        recordLen = GetValueByBig(recordLen);
        fileArch->Read(&shapeType, sizeof(shapeType));
        fileArch->Read(&(box), 4 * sizeof(double));
        fileArch->Read(&numParts, sizeof(UInt32));
        fileArch->Read(&numPoints, sizeof(UInt32));
        parts.resize(numParts);
        for (UInt32 idx = 0; idx < numParts; idx++)
            fileArch->Read(&(parts[idx]), sizeof(UInt32));
        PCGGroup* group = new PCGGroup();
        group->mID = recordNum;
        for (UInt32 idx = 0; idx < numParts; idx++)
        {
            PCGWay* way = new PCGWay();
            way->mID = wayNum + idx;
            pointNum = idx == numParts - 1 ? (numPoints - parts[idx]) : (parts[idx + 1] - parts[idx]);
            way->mRefNodes.reserve(pointNum);
            for (UInt32 nodeIdx = 0; nodeIdx < pointNum; nodeIdx++)
            {
                PCGNode* node = new PCGNode();
                node->mID = nodeNum + nodeIdx;
                fileArch->Read(&(node->mPos.x), sizeof(double));
                fileArch->Read(&(node->mPos.y), sizeof(double));
                node->mPos = LonLatTo2D(node->mPos.x, node->mPos.y);
                map.mNodeMap.emplace(node->mID, node);
                way->mRefNodes.emplace_back(node->mID);
                way->mBound.Encapsulate(node->mPos);
            }
            nodeNum += pointNum;
            group->mRefWays.push_back(way->mID);
            group->mBound.Encapsulate(way->mBound);
            map.mWayMap.emplace(way->mID, way);
        }
        wayNum += numParts;
        map.mGroupMap.emplace(group->mID, group);
        map.mBound.Encapsulate(group->mBound);
    };
    fp->Close();
    return true;
}

bool SHPImporter::LoadDbfFile(const std::string& filename, PCGMap& map)
{
    std::string dbfFileName = StringHelper::Replace(filename, ".shp", ".dbf");
    auto fileSystem = EngineGlobal::GetFileSystem();
    if (!fileSystem->HaveFile(dbfFileName))
        return false;
    auto fp = fileSystem->Open(dbfFileName, true);
    FileArchive* fileArch = new FileArchive(fp);
    // 1 header
    char version;
    fileArch->Read(&version, sizeof(char));
    char date[3];
    fileArch->Read(&date, 3 * sizeof(char));
    SInt32 recordNum;
    fileArch->Read(&recordNum, sizeof(SInt32));
    SInt16 headerByteNum;
    fileArch->Read(&headerByteNum, sizeof(SInt16));
    SInt16 recordByteNum;
    fileArch->Read(&recordByteNum, sizeof(SInt16));
    SInt16 reserved1;
    fileArch->Read(&reserved1, sizeof(SInt16));
    char flag4s;
    fileArch->Read(&flag4s, sizeof(char));
    char encrypteFlag;
    fileArch->Read(&encrypteFlag, sizeof(char));
    UInt32 unused[3];
    fileArch->Read(&unused, 3 * sizeof(UInt32));
    char mdxFlag;
    fileArch->Read(&mdxFlag, sizeof(char));
    char ldriID;
    fileArch->Read(&ldriID, sizeof(char));
    SInt16 reserved2;
    fileArch->Read(&reserved2, sizeof(SInt16));

    struct RecordFormat
    {
        char name[11];
        char fieldType;
        char reserved3[4];
        UInt8 fieldLength;
        char decimalCount;
        char reserved4[2];
        char workID;
        char reserved5[11];
    };
    UInt32 fieldscount = (headerByteNum - 32) / 32;
    std::vector<RecordFormat> recordFormats;
    for (UInt32 i = 0; i < fieldscount; i++)
    {
        RecordFormat& format = recordFormats.emplace_back();
        fileArch->Read(&format, sizeof(RecordFormat));
    }
    char terminator;
    fileArch->Read(&terminator, sizeof(char));
    recordNum = std::min(recordNum, static_cast<SInt32>(map.mGroupMap.size()));
    for (SInt32 i = 0; i < recordNum; i++)
    {
        UInt8 deleteFlag;
        fileArch->Read(&deleteFlag, 1);
        for (UInt32 j = 0; j < recordFormats.size(); j++)
        {
            const auto& format = recordFormats[j];
            std::string propName = format.name;
            std::string propValue(format.fieldLength + 1, '\0');
            fileArch->Read(propValue.data(), format.fieldLength);
            if (PropMap.find(propName) != PropMap.end())
            {
                map.mGroupMap[i + 1]->mTags.emplace(PropMap.at(propName), propValue);
            }
        }
    }
    fp->Close();
    return true;
}

bool SHPImporter::SaveToNda(const PCGMap& map, const std::string& savePath)
{
    PCGResourcePtr osmRes = gResourceMgr.CreateResourceAs<cross::resource::PCGResource>();
    Assert(osmRes);
    osmRes->CreateAsset(savePath);
    osmRes->Serialize(map);
    return true;
}

UInt32 SHPImporter::GetValueByBig(UInt32 bigValue)
{
    return (bigValue & 0x000000FFU) << 24 | (bigValue & 0x0000FF00U) << 8 | (bigValue & 0x00FF0000U) >> 8 | (bigValue & 0xFF000000U) >> 24;
}
}
