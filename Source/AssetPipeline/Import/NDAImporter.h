#pragma once
#include "AssetPipeline/Import/AssetImporter.h"


namespace cross::editor
{
    class NDAImporter : public cross::editor::AssetImporter
    {
    public:
        NDAImporter();
        ~NDAImporter();

        bool CheckAssetName(const char* cszName) const override;
        void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath = "", ImportSetting* setting = nullptr){};
    protected:
    };
}