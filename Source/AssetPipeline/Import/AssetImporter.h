#pragma once
#include <string>
#include <vector>
#include <map>
#include <set>
#include <list>

#include "Threading/Task.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "AssetPipeline/Import/AssetImportSetting.h"
#include "CrossBase/Template/Functional.hpp"
namespace cross::editor
{
    enum class CEMeta(Editor) AssetType : int
    {
        Unknown = -1,
        Default = 0,    //nda asset
        Texture = 1,
        Model = 2,
        Shader = 3,
        Audio = 4,
        Material = 5,
        Usd = 6,
        ComputeShader = 7,
        Terrain = 8,
        Font = 9,
        OSM = 10,
        SHP = 11,
        AnimBlendSpace = 12,
        AnimComposite = 13,
        Animatrix = 14,
        TMap = 15,
        FoliagePointCloud = 16,
        City = 17,
        IsolatedPointCloud = 18,
        WindsockPointCloud = 19,
        RayTracingShader = 20,
        Count,
    };

    enum class CEMeta(Editor) AssetImportState : int 
    {
        Success = 0,
        OpenFileFail = 1,
        ImportFail = 2,
        WriteNdaFail = 3,
        ExtensionError = 4,
        PaddingVertexColor = 5,
        HasBadTangent = 6,
        ShaderCompileFail = 7,
        TextureCompressFail = 8,
        SearchFileFail = 9,
        MissImportTextureFile = 10,
    };

    enum class ModelType
    {
        Unknown,

        StaticMesh,
        SkeletalMesh
    };

    struct CEMeta(Editor) AssetImportResult
    {
        CEMeta(Editor)
        bool bSuccess = true;

        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "List", ToolTips = "Import errors", bHide = true))
        std::vector<AssetImportState> ErrorCodes;

        AssetImportResult() = default;

        AssetImportResult(const AssetImportState& state)
        {
            if (state != AssetImportState::Success)
            {
                bSuccess = false;
                ErrorCodes.emplace_back(state);
            }
        }

        AssetImportResult operator=(AssetImportResult const& result)
        {
            if (!result.bSuccess)
            {
                bSuccess = false;
                ErrorCodes.insert(ErrorCodes.end(), result.ErrorCodes.begin(), result.ErrorCodes.end());
            }
            return (*this);
        }

        AssetImportResult operator=(AssetImportState const& code)
        {
            if (code != AssetImportState::Success)
            {
                bSuccess = false;
                if (ErrorCodes.empty())
                {
                    ErrorCodes.emplace_back(code);
                }
                else if (!std::any_of(ErrorCodes.begin(), ErrorCodes.end(), [code](const auto& item) { return item == code; }))
                {
                    ErrorCodes.emplace_back(code);
                }
            }
            return (*this);
        }
    };

    class ASSET_API AssetImporter
    {
    public:
        using SerializeFunction = TFunction<bool(filesystem::IFilePtr)>;

    public:
        AssetImporter(AssetType type) noexcept
            : mAssetType(type) {}
        virtual ~AssetImporter() noexcept = default;
        AssetImporter(AssetImporter const&) = delete;
        AssetImporter& operator=(AssetImporter const&) = delete;
        AssetType GetType() const noexcept { return mAssetType; }
        void SetType(AssetType type)
        {
            mAssetType = type;
        }
        AssetImportResult& GetImportResult() { return mImportResult; }
        //void SetImportResult(const AssetImportResult& result){ mImportResult = result; }
        const AssetImportResult& GetImportResult() const { return mImportResult; }

    public:
        static bool HasExtension(const char* cszFileName, const char* cszEx);
        static bool ContainBaseName(const char* fileName, const char* baseStr);

    public:
        virtual bool CheckAssetName(const char* name) const = 0;
        virtual void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath = "", ImportSetting* setting = nullptr) = 0;
        virtual bool UpdateAsset(const std::string& assetFilename, const std::string& ndaSavePath){ return false; };
        virtual bool CheckUpdateAsset(const std::string& assetFilename, const std::string& ndaSavePath);
        virtual threading::TaskEventPtr ImportAssetAsync(const std::string& assetFilename, const std::string& ndaSavePath = "");
        virtual bool ReImportAsset(const std::string& ndaPath, ImportSetting* setting = nullptr){ return false; };
        virtual float GetProgress() { return mProgress; };
        virtual void SetProgress(float progress) { mProgress = progress; };
        virtual void AddProgress(float progress) { mProgress += progress; };

    protected:
        //static void ImportAssetImpl(int classID, const std::string& name, bool recycle);
        static bool IsResourceClass(int classID) noexcept;
        static bool IsAbstractResourceClass(int classID) noexcept;
    public:
        static bool DropAssetAsSingleFile(std::string const& assetFileName, SerializeFunction sFunc);

    protected:
        AssetType                       mAssetType;
        AssetImportResult               mImportResult;
        float                           mProgress = 0.f;
    };
}
