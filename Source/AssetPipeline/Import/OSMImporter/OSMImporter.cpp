
#include "tinyxml2.h"
#include "Resource/ResourceManager.h"
#include "Resource/resourceasset.h"
#include "AssetPipeline/Import/OSMImporter/OSMImporter.h"
#include "AssetPipeline/Import/OSMImporter/OSMImportSetting.h"

namespace cross::editor {
OSMImporter::OSMImporter(): AssetImporter(AssetType::OSM)
{
}

OSMImporter::~OSMImporter() {}

void OSMImporter::ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting) 
{
    // apply setting
    auto osmSet = static_cast<OSMImportSetting*>(setting);
    mCDTransfer.SetLatLon(osmSet->mLongitude, osmSet->mLatitude);
    // gen
    PCGMap map(osmSet->mType);
    if (!LoadFromXml(assetFilename, map))
    {
        mImportResult = AssetImportState::OpenFileFail;
        return;
    }
    PCGMapRebuilder::Rebuild(map, {osmSet->mRange, osmSet->mDedumplicate, osmSet->mSimplifyMapEpsilon, osmSet->mDistanceThreshold});
    if (!SaveToNda(map, ndaSavePath))
    {
        mImportResult = AssetImportState::WriteNdaFail;
        return;
    }
}

bool OSMImporter::CheckAssetName(const char* name) const
{
    return HasExtension(name, ".osm");
}

bool OSMImporter::LoadFromXml(const std::string& osmXml, PCGMap& map)
{
    auto fileSystem = EngineGlobal::GetFileSystem();
    if (!fileSystem->HaveFile(osmXml))
        return false;
    tinyxml2::XMLDocument doc;
    if (doc.LoadFile(osmXml.c_str()) != tinyxml2::XML_SUCCESS)
        return false;
    auto rootEle = doc.RootElement();
    // parse nodes
    for (auto nodeEle = rootEle->FirstChildElement("node"); nodeEle != nullptr; nodeEle = nodeEle->NextSiblingElement("node"))
    {
        PCGNode* node = new PCGNode();
        node->mID = nodeEle->Int64Attribute("id");
        node->mPos.x  = nodeEle->DoubleAttribute("lon");
        node->mPos.y  = nodeEle->DoubleAttribute("lat");
        node->mPos = LonLatTo2D(node->mPos.x, node->mPos.y);
        map.mNodeMap.emplace(node->mID, node);
    }
    // parse ways
    for (auto wayEle = rootEle->FirstChildElement("way"); wayEle != nullptr; wayEle = wayEle->NextSiblingElement("way"))
    {
        PCGWay* way = new PCGWay();
        way->mID = wayEle->Int64Attribute("id");
        for (auto refNodeEle = wayEle->FirstChildElement("nd"); refNodeEle != nullptr; refNodeEle = refNodeEle->NextSiblingElement("nd"))
        {
            auto nodeId = refNodeEle->Int64Attribute("ref");
            auto node = map.mNodeMap.at(nodeId);
            way->mBound.Encapsulate(node->mPos);
            way->mRefNodes.emplace_back(nodeId);
        }
        for (auto tagEle = wayEle->FirstChildElement("tag"); tagEle != nullptr; tagEle = tagEle->NextSiblingElement("tag"))
        {
            auto key = tagEle->Attribute("k");
            auto value = tagEle->Attribute("v");
            way->mTags.emplace(key, value);
        }
        map.mWayMap.emplace(way->mID, way);
    }
    // parse relation
    for (auto relationEle = rootEle->FirstChildElement("relation"); relationEle != nullptr; relationEle = relationEle->NextSiblingElement("relation"))
    {
        PCGGroup* group = new PCGGroup();
        group->mID = relationEle->Int64Attribute("id");
        for (auto memberEle = relationEle->FirstChildElement("member"); memberEle != nullptr; memberEle = memberEle->NextSiblingElement("member"))
        {
            std::string memberTp = memberEle->Attribute("type");
            auto refId = memberEle->Int64Attribute("ref");
            //auto refRole = memberEle->Attribute("role");
            if (memberTp == "way") {
                auto way = map.mWayMap.at(refId);
                group->mBound.Encapsulate(way->mBound);
                group->mRefWays.emplace_back(refId);
            }
        }
        for (auto tagEle = relationEle->FirstChildElement("tag"); tagEle != nullptr; tagEle = tagEle->NextSiblingElement("tag"))
        {
            auto key = tagEle->Attribute("k");
            auto value = tagEle->Attribute("v");
            group->mTags.emplace(key, value);
        }
        map.mGroupMap.emplace(group->mID, group);
        mMaxGroupId = std::max(mMaxGroupId, group->mID);
    }
    RebuildOSMMapType(map);
    return true;
}

PCGType OSMImporter::GetPCGTypeByTags(const TagMap& tagMap)
{
    // building
    if (tagMap.find("building") != tagMap.end() || tagMap.find("building:part") != tagMap.end())
    {
        return PCGType::Building;
    }
    // highway
    if (tagMap.find("highway") != tagMap.end())
    {
        return PCGType::Highway;
    }
    // crossing
    if (tagMap.find("crossing") != tagMap.end())
    {
        return PCGType::Highway;
    }
    // bridge
    if (tagMap.find("bridge") != tagMap.end())
    {
        return PCGType::Bridge;
    }
    // natural
    if (auto it =tagMap.find("natural"); it != tagMap.end())
    {
        // water
        if (it->second == "water")
        {
            return PCGType::Water;
        }
        // foliage
        else if (it->second == "wood")
        {
            return PCGType::Foliage;
        }
    }
    return PCGType::Unkown;
}

void OSMImporter::RebuildOSMMapType(PCGMap& map) {
    // groups
    std::unordered_set<SInt64> groupWayIds;
    for (auto it = map.mGroupMap.begin(); it != map.mGroupMap.end();)
    {
        auto group = it->second;
        auto groupType = GetPCGTypeByTags(group->mTags);
        if (groupType != map.mType)
        {
            for (auto wayId : group->mRefWays)
            {
                if (auto wayIt = map.mWayMap.find(wayId); wayIt != map.mWayMap.end())
                {
                    auto way = wayIt->second;
                    for (auto nodeId : way->mRefNodes)
                    {
                        if (auto nodeIt = map.mNodeMap.find(nodeId); nodeIt != map.mNodeMap.end())
                        {
                            auto node = nodeIt->second;
                            map.mNodeMap.erase(nodeIt);
                            delete node;
                        }
                    }
                    map.mWayMap.erase(wayIt);
                    delete way;
                }
            }
            it = map.mGroupMap.erase(it);
            delete group;
        }
        else
        {
            for (auto wayId : group->mRefWays) {
                groupWayIds.insert(wayId);
            }
            group->mTags.clear();
            map.mBound.Encapsulate(group->mBound);
            it++;
        }
    }
    // ways not in group
    SInt64 groupId = mMaxGroupId;
    for (auto it = map.mWayMap.begin(); it != map.mWayMap.end();)
    {
        auto way = it->second;
        if (groupWayIds.find(it->first) != groupWayIds.end())
            continue;
        if (GetPCGTypeByTags(way->mTags) != map.mType)
            continue;
        PCGGroup* group = new PCGGroup();
        group->mID = ++groupId;
        group->mBound.Encapsulate(way->mBound);
        group->mRefWays.emplace_back(it->first);
        map.mBound.Encapsulate(group->mBound);
        map.mGroupMap.emplace(group->mID, group);
    }
}

bool OSMImporter::SaveToNda(const PCGMap& map, const std::string& savePath)
{
    PCGResourcePtr osmRes = gResourceMgr.CreateResourceAs<cross::resource::PCGResource>();
    Assert(osmRes);
    osmRes->CreateAsset(savePath);
    osmRes->Serialize(map);
    return true;
}
}   // namespace cross::editor