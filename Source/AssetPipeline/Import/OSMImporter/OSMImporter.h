#pragma once

#include "Resource/RuntimePCG/PCGResource.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/OSMImporter/OSMImportSetting.h"


namespace cross::editor {
    class OSMImporter : public AssetImporter
    {
    public:
        OSMImporter();
        ~OSMImporter();
    public:
        virtual void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath = "", ImportSetting* setting = nullptr) override;
        virtual bool CheckAssetName(const char* name) const override;
    private:
        bool LoadFromXml(const std::string& osmXml, PCGMap& map);
        bool SaveToNda(const PCGMap& osmData, const std::string& savePath);
        void RebuildOSMMapType(PCGMap& map);
        PCGType GetPCGTypeByTags(const TagMap& tagMap);
        Double2 LonLatTo2D(double lon, double lat) { return mCDTransfer.WGS84_To_2D(lon, lat);}

    private:
        CoordinateTransfer  mCDTransfer;
        SInt64              mMaxGroupId = 0;
    };
}