#pragma once
#include "TextureAsset_generated.h"
#include "TextureImportSetting.h"
#include "cmft/image.h"
#include "Resource/TextureResourceInfo.h"
#include "image.h"

namespace cross::editor
{
    static constexpr UInt64 MinPixelsPerJob = 16384;
    static constexpr UInt64 MinPixelsForAnyJob = 136 * 136;

    using CrossSchema::TextureFormat;
    using CrossSchema::TextureDimension;
    using namespace imageio;

    bool ToBasis(CrossSchema::TextureAssetT& texture);

    CrossSchema::TextureFormat MapToCEFormat(cmft::TextureFormat::Enum importformat);

    bool LoadImageRaw(cmft::Image &image, CrossSchema::TextureAssetT &texture, TextureImportSetting setting);
    
    bool LoadImageCubeRaw(cmft::Image& image, CrossSchema::TextureAssetT& texture, TextureImportSetting setting, std::vector<Float3>& outShCoefs);

    bool LoadImageCubeBC(cmft::Image& image, CrossSchema::TextureAssetT& texture, TextureImportSetting setting, std::vector<Float3>& outShCoefs);

    bool LoadImage2DArrayRaw(cmft::Image& image, CrossSchema::TextureAssetT& texture, TextureImportSetting setting);

    bool LoadImage2DArrayBC(cmft::Image& image, CrossSchema::TextureAssetT& texture, TextureImportSetting setting);

    bool LoadImageBC(cmft::Image& image, CrossSchema::TextureAssetT& texture, TextureImportSetting setting);

    bool LoadImageBasis(cmft::Image& image, CrossSchema::TextureAssetT& texture, TextureImportSetting setting);

    void Transcode(CrossSchema::TextureAssetT& texture, TextureFormat format);

    void GetLinearColorValue(cmft::Image& src, TextureResourceInfo info);

    void ProcessColorAdjustments(UInt8& x, UInt8& y, UInt8& z, UInt8& w, TextureResourceInfo info);

    void GenerateMipChain(cmft::Image& src, CrossSchema::TextureAssetT& texture, std::vector<cmft::Image>& mipmaps, const TextureResourceInfo info); 

    UInt32 ParallelForComputeNumJobs(UInt64& outNumberPerJob, UInt64 numItems, UInt64 minNumItemsPerJobs, UInt64 minNumItemsForAnyJobs = 0);

    UInt32 ImageParallelForComputeNumJobsForRows(UInt32& outNumberPerJob, UInt32 sizeX, UInt32 sizeY);

    UInt32 ImageParallelForComputeNumJobsForPixels(UInt64& outNumPixelsPerJob, UInt64 numPixels);
#if CROSSENGINE_WIN
    void NVTTTransformTexture(CrossSchema::TextureAssetT& texture, TextureImportSetting setting, imageio::GPUImage& gpu_image);
    void ProcessBCFormatNVTT(std::vector<cmft::Image>& images, imageio::GPUImage& gpu_image, TextureImportSetting setting);
    bool LoadImageCubeHDR(cmft::Image& image, CrossSchema::TextureAssetT& texture, TextureImportSetting setting, std::vector<Float3>& outShCoefs);
#endif
}
