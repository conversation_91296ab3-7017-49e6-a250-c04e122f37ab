#pragma once

#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Cook/TextureCooker/TextureCooker.h"
#include "ImageLoader.h"
#include "TextureImportSetting.h"
#include "cmft/image.h"
#include "CrossImage/image.h"
#include "Material.h"

class Texture2D;

namespace cross::editor
{
    #define CE_DELTA (0.00001f)
    const UInt32 SampleTileEdgeLength = 4;
    const UInt32 MaxTilesPerAxis = 16 /*The UE is 16. This is for the time being to improve performance */;

    const float ColorComponentNearlyZeroThreshold = (2.0f / 255.0f);
    const float AlphaComponentNearlyZeroThreshold = (1.0f / 255.0f);
    const float RejectedToTakenRatioThreshold = 0.33f;
    const float NormalMapMinLengthConfidenceThreshold = 0.55f;
    const float NormalMapMaxLengthConfidenceThreshold = 1.1f;
    const float NormalMapDeviationThreshold = 0.8f;
    const float NormalVectorUnitLengthDeltaThreshold = 0.45f;


    enum TextureImportType
    {
        TextureImportType2D,
        TextureImportTypeCube
    };

    class ASSET_API TextureImporter : public AssetImporter
    {
    public:
        TextureImporter();
        ~TextureImporter();

    public:
        void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting) override;
        bool UpdateAsset(const std::string& assetFilename, const std::string& ndaSavePath) override;
        bool CheckAssetName(const char* name) const override;
        void AddDependency(std::string& dep);

        bool ByPathGetTextureRawData(const std::string& ndaPath, cmft::Image& src, CrossSchema::TextureAssetT& texture);
        bool GetTextureRawData(const std::string& ndaPath);
        void UpdateTextureAsset(const std::string& ndaPath, const TextureImportSetting& setting, const TextureResourceInfo& info);
        void GenerateSharpenedMip(const std::string& ndaPath, const TextureImportSetting setting, const TextureResourceInfo info);
        void CopyAttributesToTexture(CrossSchema::TextureAssetT& texture, const TextureResourceInfo info);
        TextureImportSetting UpdateDefaultSetting(const std::string& ndaPath, TextureImportSetting setting, bool AutoFFS = false);
        bool GenerateCurveGradientTex(const std::string& fileName, const TextureImportSetting& setting, UInt32* stopColors, int n);
        bool CombineMipLevels(const std::vector<std::string>& mipPaths, const std::string& outputPath, const TextureImportSetting& setting);
        bool ReImportAsset(const std::string& ndaPath, ImportSetting* setting) override;
    private:
        TexturePtr ImportTexture2DFile(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting);
        TexturePtr ImportTexture3DFile(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting);
        TexturePtr ImportTextureCubeFile(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting);
        TexturePtr ImportTexture2DArrayFile(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting);
        AssetImportState ImportUDIMTextures(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting);

        TexturePtr ImportTexture2D(cmft::Image& image, CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting, bool isExist = false);
        TexturePtr ImportTexture3D(cmft::Image& image, CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting);
        TexturePtr ImportTextureCube(cmft::Image &image, CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting);
        TexturePtr ImportTexture2DArray(cmft::Image& image, CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting);

        bool DoesTexture2DLookLikelyToBeANormalMap(cmft::Image& image, bool& isAlpha);
        void EvaluateSubBlock(SInt32 left, SInt32 top, SInt32 width, SInt32 height, Float4& averageColor, SInt32& numSamplesTaken, SInt32& numSamplesRejected, imageio::image& image);

        bool Compress(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting);

public:
        static TexturePtr SerializeTexture2D(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting, bool isExist = false);
        static TexturePtr SerializeTexture3D(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting);
        static TexturePtr SerializeTextureCube(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, std::vector<Float3>& shCoefs, const TextureImportSetting& setting);
        static TexturePtr SerializeTexture2DArray(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting);

private:
        void SaveRawData(const std::string& assetFilename, CrossSchema::TextureAssetT& texture, bool isSRGB);
        bool LoadImageFile(const std::string& path, cmft::Image& src, const TextureImportSetting& setting);
        bool ReimportUtil(const std::string& ndaPath, const TextureImportSetting& setting);
    private:
        TextureCooker mTextureCooker;
        TextureImportSetting mImportSetting;
        std::vector<std::string> mDependencies;

        std::mutex mMutex;
    };

}

