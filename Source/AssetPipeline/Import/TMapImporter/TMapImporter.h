#pragma once

#include "Resource/RuntimePCG/PCGResource.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/TMapImporter/TMapImportSetting.h"

namespace cross::editor {
class TMapImporter : public AssetImporter
{
public:
    TMapImporter();
    ~TMapImporter();

public:
    virtual void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath = "", ImportSetting* setting = nullptr) override;
    virtual bool CheckAssetName(const char* name) const override;

private:
    bool LoadTMapFile(const std::string& filename, PCGMap& map);
    bool SaveToNda(const PCGMap& map, const std::string& savePath);
    Double2 LonLatTo2D(double lon, double lat){ return mCDTransfer.GCJ02_To_2D(lon, lat); };

private:
    CoordinateTransfer  mCDTransfer;
};
}   // namespace cross::editor