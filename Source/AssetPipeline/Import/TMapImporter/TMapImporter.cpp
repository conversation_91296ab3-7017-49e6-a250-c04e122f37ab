#include "Resource/ResourceManager.h"
#include "AssetPipeline/Import/TMapImporter/TMapImporter.h"
#include <regex>

namespace cross::editor {
const std::regex gLinePattern{R""((.+),(.+),(.+),(.+),(.+))""};

// ------------------- TMapImporter -----------------------------//
TMapImporter::TMapImporter()
    : AssetImporter(AssetType::TMap)
{}

TMapImporter::~TMapImporter() {}

void TMapImporter::ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath /* = "" */, ImportSetting* setting /* = nullptr */)
{
    auto tmapSet = static_cast<TMapImportSetting*>(setting);
    mCDTransfer.SetLatLon(tmapSet->mLongitude, tmapSet->mLatitude);
    // load
    PCGMap map(tmapSet->mType);
    map.mBlockSize = static_cast<Double2>(tmapSet->mBlockSize);
    if (!LoadTMapFile(assetFilename, map))
    {
        mImportResult = AssetImportState::OpenFileFail;
        return;
    }
    // simplify way polygon
    // rebuild
    PCGMapRebuilder::Rebuild(map, {tmapSet->mRange, tmapSet->mDedumplicate, tmapSet->mSimplifyMapEpsilon, tmapSet->mDistanceThreshold});
    if (!SaveToNda(map, ndaSavePath))
    {
        mImportResult = AssetImportState::WriteNdaFail;
    }
}

bool TMapImporter::CheckAssetName(const char* name) const
{
    return HasExtension(name, ".tmap");
}

bool TMapImporter::LoadTMapFile(const std::string& filename, PCGMap& map)
{
    // open file
    auto fileSystem = EngineGlobal::GetFileSystem();
    if (!fileSystem->HaveFile(filename))
        return false;
    auto fp = fileSystem->Open(filename, true);
    auto fileSize = fp->GetSize();
    FileArchive* fileArch = new FileArchive(fp);
    // init parameters
    char* buffer = new char[1024];
    std::vector<std::string> lineStrs{""};
    // init map
    PCGWay* way = nullptr;
    PCGGroup* group = nullptr;

    float height = 0.0f;
    bool isEndLine = false;
    while (fileSize > fileArch->Tell())
    {
        auto readSize = std::min(1024ULL, fileSize - fileArch->Tell());
        if (readSize != fileArch->Read(buffer, readSize))
            break;
        for (int i = 0; i < readSize; i++)
        {
            if (buffer[i] == '\r')
                continue;
            if (buffer[i] != '\n') 
            {
                if (isEndLine)
                    continue;
                if (buffer[i] == ':')
                    isEndLine = true;
                else if (buffer[i] == ',') 
                    lineStrs.emplace_back("");
                else
                    lineStrs[lineStrs.size() - 1] += buffer[i];
                continue;
            }
            if (lineStrs.size() > 1)
            {
                if (!group || !way)
                {
                    way = map.AddWay();
                    group = map.AddGroup();
                }
                auto node = map.AddNode();
                height = std::stof(lineStrs[0]);
                node->mPos.x = std::stod(lineStrs[4]);
                node->mPos.y = std::stod(lineStrs[3]);
                node->mPos = LonLatTo2D(node->mPos.x, node->mPos.y);
                way->mRefNodes.emplace_back(node->mID);
                way->mBound.Encapsulate(node->mPos);
            }
            else
            {
                if (way && way->mRefNodes.size() > 0)
                {
                    // finish group
                    group->mRefWays.emplace_back(way->mID);
                    group->mTags.emplace("maxheight", std::to_string(height));
                    group->mBound.Encapsulate(way->mBound);
                    map.mBound.Encapsulate(group->mBound);
                    // reset
                    way = nullptr;
                    group = nullptr;
                }
            }
            lineStrs[0] = "";
            lineStrs.resize(1);
            isEndLine = false;
        }
    }
    delete[] buffer;
    fp->Close();
    return true;
}

bool TMapImporter::SaveToNda(const PCGMap& map, const std::string& savePath)
{
    PCGResourcePtr pcgRes = gResourceMgr.CreateResourceAs<cross::resource::PCGResource>();
    Assert(pcgRes);
    pcgRes->CreateAsset(savePath);
    pcgRes->Serialize(map);
    return true;
}
}   // namespace cross::editor
