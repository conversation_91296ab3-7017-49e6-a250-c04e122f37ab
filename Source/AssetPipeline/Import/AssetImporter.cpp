#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "Resource/BaseClasses/ClassIDs.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/filesystem.h"

// Add a warning/error which will be serialized!
// If the error string is "" it will be ignored!
//#define LogImportError(x) AddImportError(x, __FILE__, __LINE__, kLog | kAssetImportError, nullptr)
//#define LogImportWarning(x) AddImportError(x, __FILE__, __LINE__, kLog | kAssetImportWarning, nullptr)
//#define LogImportErrorObject(x,o) AddImportError(x, __FILE__, __LINE__, kLog | kAssetImportError, o)
//#define LogImportWarningObject(x,o) AddImportError(x, __FILE__, __LINE__, kLog | kAssetImportWarning, o)

namespace cross::editor
{
    //void AssetImporter::AddImportError(const std::string& error, const char* file, int line, int mode, const Resource* object)
    //{
    //    if (error.empty())
    //        return;
    //
    //    mImportErrors.push_back(ImportError());
    //    mImportErrors.back().error = error;
    //    mImportErrors.back().object = object;
    //    mImportErrors.back().line = line;
    //    mImportErrors.back().file = file;
    //    mImportErrors.back().mode = mode;
    //}
	


    //void AssetImporter::ImportAssetImpl(int classID, const std::string& name, [[maybe_unused]] bool recycle)
    //{
    //    if (!IsResourceClass(classID) && IsAbstractResourceClass(classID))
    //        return nullptr;
    //
    //    Resource* producedObject = dynamic_cast<Resource*>(Object::Produce(classID));
    //    if (!producedObject)
    //        throw std::bad_cast{};
    //
    //    // set name
    //    producedObject->SetName(name.c_str());
    //    return producedObject;
    //}

bool AssetImporter::CheckUpdateAsset(const std::string& assetFilename, const std::string& ndaSavePath)
{
    if (!PathHelper::IsFileExist(assetFilename))
        return false;
    if (!PathHelper::IsFileExist(ndaSavePath))
        return false;
    auto fileSys = EngineGlobal::GetFileSystem();
    return fileSys->GetTimeStamp(assetFilename) > fileSys->GetTimeStamp(ndaSavePath);
}

threading::TaskEventPtr AssetImporter::ImportAssetAsync(const std::string& assetFilename, const std::string& ndaSavePath)
    {
        return threading::Async<bool>([=](const auto&) { return true; });
    };

    bool AssetImporter::IsResourceClass(int classID) noexcept
    {
        return classID >= ClassIDType::CLASS_Resource && classID <= ClassIDType::CLASS_RED;
    }

    bool AssetImporter::IsAbstractResourceClass(int classID) noexcept
    {
        return classID == ClassIDType::CLASS_Resource || classID == ClassIDType::CLASS_SubResource ||
            classID == ClassIDType::CLASS_Texture;
    }

    bool AssetImporter::DropAssetAsSingleFile(const std::string& assetFileName, SerializeFunction sFunc)
    {
        if (!sFunc)
            return false;

        std::string fileName(std::move(assetFileName));
        PathHelper::MakePathIllegal(fileName);
        auto dir = PathHelper::GetDirectoryFromAbsolutePath(fileName);
        if (!PathHelper::IsDirectoryExist(dir))
        {
            // create all the folders needed
            PathHelper::CheckDirectory(dir);
        }

        filesystem::FileSystem* fileSystem = EngineGlobal::Inst().GetFileSystem();
        Assert(fileSystem);
        

        filesystem::IFilePtr file = fileSystem->OpenForWrite(fileName);
        Assert(file);

        
        return sFunc(file);
    }

    bool AssetImporter::HasExtension(const char* cszFileName, const char* cszEx)
    {
        if (cszFileName == nullptr || cszEx == nullptr)
            return false;
        unsigned int nLen = (UInt32)strlen(cszFileName);
        unsigned int nExLen = (UInt32)strlen(cszEx);
        if (nLen <= nExLen)  return false;
        const char* cszCmp = cszFileName + (nLen - nExLen);
#if CROSSENGINE_OSX
        return strcasecmp(cszCmp, cszEx) == 0;
#else
        return _stricmp(cszCmp, cszEx) == 0;
#endif
    }

    bool AssetImporter::ContainBaseName(const char* fileName, const char* baseStr)
    {
        if (fileName == nullptr || baseStr == nullptr)
        {
            return false;
        }

        UInt32 nLen = static_cast<UInt32>(strlen(fileName));
        UInt32 nBaseLen = static_cast<UInt32>(strlen(baseStr));
        if (nLen <= nBaseLen)
        {
            return false;
        }

        return strstr(fileName, baseStr) != nullptr;
    }
}
