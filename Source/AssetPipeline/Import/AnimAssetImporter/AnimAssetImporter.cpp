#include "EnginePrefix.h"
#include "AnimAssetImporter.h"
#include "Resource/Animation/BlendSpace/AnimBlendSpaceRes.h"
#include "Resource/Animation/Sequence/AnimSequenceRes.h"
#include "Resource/Animation/Composite/AnimCompositeRes.h"
#include "Resource/Animation/Composite/AnimatrixRes.h"

namespace cross::editor {
AnimBlendSpaceImporter::AnimBlendSpaceImporter()
    : AssetImporter(AssetType::AnimBlendSpace)
{}

bool AnimBlendSpaceImporter::CheckAssetName(const char* name) const
{
    return HasExtension(name, ".blendspace");
}

void AnimBlendSpaceImporter::ImportAsset(const std::string& assetFileName, const std::string& ndaSavePath, ImportSetting* setting)
{
    auto resource = gResourceMgr.CreateResourceAs<anim::AnimBlendSpaceRes>();
    std::string content;
    if (FileHelper::LoadFileToString(content, assetFileName.c_str()))
    {
        if (!resource->Deserialize(DeserializeNode::ParseFromJson(content)))
        {
            mImportResult = AssetImportState::ImportFail;
        }
        if (!resource->Serialize(ndaSavePath))
        {
            mImportResult = AssetImportState::WriteNdaFail;
        }
    }
    else
    {
        mImportResult = AssetImportState::OpenFileFail;
    }
}

AnimCompositeImporter::AnimCompositeImporter()
    : AssetImporter(AssetType::AnimComposite)
{}

bool AnimCompositeImporter::CheckAssetName(const char* name) const
{
    return HasExtension(name, ".composite");
}

void AnimCompositeImporter::ImportAsset(const std::string& assetFileName, const std::string& ndaSavePath, ImportSetting* setting)
{
    auto resource = gResourceMgr.CreateResourceAs<anim::AnimCompositeRes>();
    std::string content;
    if (FileHelper::LoadFileToString(content, assetFileName.c_str()))
    {
        if (!resource->Deserialize(DeserializeNode::ParseFromJson(content)))
        {
            mImportResult = AssetImportState::ImportFail;
        }
        if (!resource->Serialize(ndaSavePath))
        {
            mImportResult = AssetImportState::WriteNdaFail;
        }
    }
    else
    {
        mImportResult = AssetImportState::OpenFileFail;
    }
}

AnimatrixImporter::AnimatrixImporter()
    : AssetImporter(AssetType::Animatrix)
{}

bool AnimatrixImporter::CheckAssetName(const char* name) const
{
    return HasExtension(name, ".animatrix");
}

void AnimatrixImporter::ImportAsset(const std::string& assetFileName, const std::string& ndaSavePath, ImportSetting* setting)
{
    auto resource = gResourceMgr.CreateResourceAs<anim::AnimatrixRes>();

    std::string content;
    if (FileHelper::LoadFileToString(content, assetFileName.c_str()))
    {
        if (!resource->Deserialize(DeserializeNode::ParseFromJson(content)))
        {
            mImportResult = AssetImportState::ImportFail;
        }
        if (!resource->Serialize(ndaSavePath))
        {
            mImportResult = AssetImportState::WriteNdaFail;
        }
    }
    else
    {
        mImportResult = AssetImportState::OpenFileFail;
    }
}
}   // namespace cross::editor
