#pragma once

#include "AssetPipeline/Import/AssetImporter.h"

namespace cross::editor {

class AnimBlendSpaceImporter final : public AssetImporter
{
public:
    AnimBlendSpaceImporter();
    ~AnimBlendSpaceImporter() = default;

    bool CheckAssetName(const char* name) const override;
    void ImportAsset(const std::string& assetFileName, const std::string& ndaSavePath, ImportSetting* setting = nullptr) override;
};

class AnimCompositeImporter final : public AssetImporter
{
public:
    AnimCompositeImporter();
    ~AnimCompositeImporter() = default;

    bool CheckAssetName(const char* name) const override;
    void ImportAsset(const std::string& assetFileName, const std::string& ndaSavePath, ImportSetting* setting = nullptr) override;
};

class AnimatrixImporter final : public AssetImporter
{
public:
    AnimatrixImporter();
    ~AnimatrixImporter() = default;

    bool CheckAssetName(const char* name) const override;
    void ImportAsset(const std::string& assetFileName, const std::string& ndaSavePath, ImportSetting* setting = nullptr) override;
};
}   // namespace cross::editor
