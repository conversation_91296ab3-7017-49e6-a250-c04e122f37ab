#pragma once
#include <unordered_map>
#include <optional>
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/TextureImporter/TextureImportSetting.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImportSettings.h"
#include "AssetPipeline/Import/OSMImporter/OSMImportSetting.h"
#include "AssetPipeline/Import/SHPImporter/SHPImportSetting.h"
#include "Resource/TextureResourceInfo.h"
#include "ModelImporter/ModelImportSettings.h"
#include "AssetPipeline/Import/FontImporter/FontImportSetting.h"
namespace cross::editor
{
    // manage the importers that used to generate nda asset
    class ASSET_API AssetImporterManager
    {
    public:
        using ImporterContainer = std::unordered_multimap<AssetType, std::unique_ptr<AssetImporter>>;
    public:
        AssetImporterManager();
        ~AssetImporterManager() = default;
        AssetImporterManager(AssetImporterManager const&) = delete;
        AssetImporterManager& operator= (AssetImporterManager const&) = delete;
        AssetImporterManager(AssetImporterManager&&) = delete;
        AssetImporterManager& operator= (AssetImporterManager&&) = delete;

        std::unique_ptr<AssetImportResult> ImportAssetInternal(std::optional<SInt64> task, const char* name, const char* ndaSavePath);

        CEFunction(Editor)
        static AssetImporterManager& Instance() noexcept;

    public:
        // loader
        void RegisterImporter(AssetImporter* importer, bool override_sametype = false);
        //@return 0 means succ, other are error number
        //int UnregisterImporter(AssetImporter* importer);

        // import asset from file to nda asset
        AssetImporter* GetAssetImporter(const char* assetFilePath);

        AssetImporter* GetAssetImporter(AssetType type);

        CEFunction(Editor)
        bool GenComputeShaderOnDemand(const std::string& code, const std::string& ndaSavePath);

        CEFunction(Editor)
        AssetType GetAssetType(const char* assetFilePath);

        CEFunction(Editor)
        void ImportAssetAsync(SInt64 task, const char* name, const char* ndaSavePath = nullptr);

        threading::TaskEventPtr ImportAssetAsync(const char* name, const char* ndaSavePath = nullptr);

        CEFunction(Editor)
        AssetImportResult ImportAsset(const char* name, const char* ndaSavePath = nullptr);

        CEFunction(Editor) 
        void UpdateTextureAsset(const char* ndaSavePath, const TextureImportSetting setting, TextureResourceInfo& info);

        CEFunction(Editor)
        bool GenerateCurveGradientTex(const std::string& fileName, const TextureImportSetting& setting, UInt32* stopColors, int n);

        CEFunction(Editor) 
        bool GetTextureAssetRawData(const char* ndaSavePath, TextureResourceInfo& info);

        CEFunction(Editor) 
        void GenerateSharpenedMip(const char* ndaSavePath, const TextureImportSetting setting, TextureResourceInfo& info);

        CEFunction(Editor) 
        TextureImportSetting GetTextureImportSettings(const char* ndaSavePath, TextureImportSetting setting, bool AutoFFS = false);

        CEFunction(Editor)
        bool UpdateTerrainHeightmap(const std::string& terrainNdaPath, const std::string& updateDir);

        CEFunction(Editor)
        bool UpdateTerrainWeightTexture(const std::string& terrainNdaPath, const std::string& updateDir);

        CEFunction(Editor) 
        bool UpdateAsset(const char* assetPath, const char* ndaSavePath);

        CEFunction(Editor) 
        bool CheckUpdateAsset(const char* assetPath, const char* ndaSavePath);

        CEFunction(Editor)
        bool ReImportAsset(const char* ndaPath, ImportSetting* Setting, AssetType assetType = AssetType::Texture);

        CEFunction(Editor)
        TextureImportSetting GetTextureImportSetting(const char* ndaPath);
        //set import settings
        CEFunction(Editor)
        void SetImportSettings(AssetType assetType, void* assetImportSettings);
        CEFunction(Editor)
        void SetFontImporterSettings(FontImportSetting assetImportSettings);
        CEFunction(Editor) 
        void SetShaderImportSettings(const ShaderImportSettings settings);

        CEFunction(Editor) 
        void SetComputeShaderImportSettings(const ShaderImportSettings settings);

        CEFunction(Editor) 
        void BuildShaderMaps();

        CEFunction(Editor) 
        void CheckShaderModuleFile(const char* filePath, std::vector<std::string>& outShaders, ShaderFileOutdateLevelE Level = ShaderFileOutdateLevelE::ALL);



        CEFunction(Editor) float GetProgress(AssetType type);

        CEFunction(Editor)
        bool GenerateCollision(const std::string& FilePath, CollisionGenerateSetting setting);

    protected:
        void Init();
        std::string GetNDAResourceFileName(const char* cszNDAName) const;

    private:
        // same AssetType may contain multiple importer for now, which could be a problem
        ImporterContainer   mImporters;
    };
}
