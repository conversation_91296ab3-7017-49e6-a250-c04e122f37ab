#pragma once

#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/TextureImporter/TextureImporter.h"
#include "TerrainImportSetting.h"
#include "TileImportHelper.h"

namespace cross::editor {

class TerrainImporter : public AssetImporter
{
public:
    TerrainImporter();
    ~TerrainImporter() = default;

public:
    void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting) override;
    bool CheckAssetName(const char* name) const override;

    bool UpdateHeightmap(const std::string& terrainNdaPath, const std::string& updatePath);

    bool UpdateWeightTexture(const std::string& terrainNdaPath, const std::string& updatePath);

private:
    void ImportTexture(const std::string& inDir, const std::string& outDir, const std::string& prefix, TextureImportSetting& importSetting);
    void ImportTexture(const std::vector<std::string>& textures, const std::string& outDir, std::vector<std::string>& outNdas, TextureImportSetting& importSetting);

    void ImportHeightMap(const std::string& inDir, const std::string& outDir, const std::string& prefix, UInt32 blockX, UInt32 blockY, const TerrainInfo& terrainInfo);
    void ImportHeightMapDFS(const std::string& inDir, const std::string& outDir, const std::string& prefix, std::vector<ImageView>& imageList, const TileIndex& tileIndex, const TerrainInfo& terrainInfo, UInt32 stopLevel = 0);
    void ImportHeightMapQuick(const std::string& inDir, const std::string& outDir, const std::string& prefix, const TerrainInfo& terrainInfo);
 
    void BuildQuadtree(const std::string& inDir, const std::string& outDir, const std::string& prefix, UInt32 blockX, UInt32 blockY, const TerrainInfo& terrainInfo, TerrainImageType type);

    void UpdateQuadtreeOfHeightmap(const std::string& inDir, const std::string& outDir, const std::string& prefix, const TerrainInfo& terrainInfo);
    void UpdateQuadtreeOfTexture(const std::string& inDir, const std::string& outDir, const std::string& prefix, const TerrainInfo& terrainInfo);

private:
    static TextureImportSetting HMImportSetting;
    static TextureImportSetting ATImportSetting;
    static TextureImportSetting WTImportSetting;

    TextureImporter mTextureImporter;

    TerrainImportSetting* mImportSetting;

    bool enableMultiThreading;
};
}   // namespace cross::editor
