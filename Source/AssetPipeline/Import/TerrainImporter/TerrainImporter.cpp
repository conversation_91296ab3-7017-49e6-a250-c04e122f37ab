#include "EnginePrefix.h"

#include "TerrainImporter.h"
#include <filesystem>

//#include "CrossImage/image.h"
#include "Resource/ResourceManager.h"
#include "AssetPipeline/Import/AssetImporterManager.h"
#include "Resource/Texture/Texture2D.h"
#include "AssetPipeline/Import/ModelImporter/ModelImportSettings.h"

#include "PhysicsEngine/PhysicsEngine.h"
#include "PhysicsEngine/PhysicsCooker.h"

#include "CrossImage/imageio.h"

namespace cross::editor {

namespace fs = std::filesystem;

inline imageio::image TileLoadImage(const std::string& dir, const std::string& prefix, const TileIndex& tileIdx)
{
    imageio::image image;
    std::string imagePath = TileIndex::GetPngPath(dir, prefix, tileIdx);
    imageio::load_png(imagePath, image);
    return image;
}

inline void TileSaveImage(const imageio::image& image, const std::string& dir, const std::string& prefix, const TileIndex& tileIdx)
{
    std::string imagePath = TileIndex::GetPngPath(dir, prefix, tileIdx);
    imageio::save_png(imagePath, image);
}

std::pair<UInt32, UInt32> GetTileBlockCrood(const std::string& blockPrefix)
{
    std::string_view s = blockPrefix;
    s = s.substr(0, s.find_last_not_of('_') + 1);
    Assert(s.length() > 0);
    UInt32 crood[2];

    for (size_t rp = 0; rp < 2; rp++)
    {
        size_t pos = s.rfind('_');
        crood[rp] = std::stoul(std::string(s.substr(pos + 1)));
        s = s.substr(0, pos);
    }
    return {crood[1], crood[0]};
}

std::tuple<std::string, UInt32, UInt32> SplitBlockPrefix(const std::string& blockPrefix)
{
    std::string_view s = blockPrefix;
    s = s.substr(0, s.find_last_not_of('_') + 1);
    Assert(s.length() > 0);
    UInt32 crood[2];

    for (size_t rp = 0; rp < 2; rp++)
    {
        size_t pos = s.rfind('_');
        crood[rp] = std::stoul(std::string(s.substr(pos + 1)));
        s = s.substr(0, pos);
    }
    return {std::string(s), crood[1], crood[0]};
}

TileIndex GetTileIndexFromFileName(const std::string& filename)
{
    auto ext_pos = filename.rfind(".");
    std::string_view s = filename;
    s = s.substr(0, ext_pos);
    UInt32 arr[5];
    for (int i = 0; i < 5; i++)
    {
        size_t pos = s.rfind('_');
        arr[i] = std::stoul(std::string(s.substr(pos + 1)));
        s = s.substr(0, pos);
    }
    return {arr[4], arr[3], arr[2], arr[1], arr[0]};
}


void SerializeTexture2D(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const ImportSetting& importSetting)
{
    Texture2DPtr tex2d = gResourceMgr.CreateResourceAs<resource::Texture2D>();
    tex2d->CreateAsset(ndaSavePath);
    tex2d->SetImportSet(importSetting.SerializeToString());
    tex2d->Serialize(texture);
}

std::unordered_set<std::string> GetBlockPrefix(const std::string& dir, const std::string& prefix)
{
    std::unordered_set<std::string> blockPrefixSet;
    for (const auto& f : fs::directory_iterator(dir))
    {
        const auto& fpath = f.path();
        if (fpath.extension() == ".png")
        {
            const auto& filename = fpath.filename().string();
            if (filename.compare(0, prefix.size(), prefix) == 0)
            {
                size_t idx = prefix.size();
                for (; idx < filename.size() && filename[idx] != '_'; idx++) {}
                for (size_t rp = 0; rp < 2; rp++)
                {
                    idx++;
                    for (; idx < filename.size() && filename[idx] != '_'; idx++) {}
                }
                if (idx < filename.length())
                {
                    blockPrefixSet.insert(filename.substr(0, idx));
                }
            }
        }
    }
    return blockPrefixSet;
}

std::vector<TileIndex> GetTileIndexList(const std::string& dir, const std::string& prefix)
{
    std::vector<TileIndex> tileIndexList;
    for (const auto& f : fs::directory_iterator(dir))
    {
        const auto& fpath = f.path();
        if (fpath.extension() == ".png")
        {
            const auto& filename = fpath.filename().string();
            if (filename.compare(0, prefix.size(), prefix) == 0)
            {
                auto tileIdx = GetTileIndexFromFileName(filename);
                tileIndexList.push_back(tileIdx);
            }
        }
    }
    return tileIndexList;
}

void ImageProcess1(imageio::image& leftImage, imageio::image& rightImage, TerrainImageType type)
{
    if (leftImage.get_width() && rightImage.get_width())
    {
        auto pitch = leftImage.get_pitch();
        auto leftPtr = reinterpret_cast<TerrainRGBA*>(leftImage.get_ptr() + pitch - 1);
        auto rightPtr = reinterpret_cast<TerrainRGBA*>(rightImage.get_ptr());
        for (size_t i = 0; i < leftImage.get_height(); i++)
        {
            if (type == TerrainImageType::Height)
            {
                if (!leftPtr->isHole())
                {
                    auto normal = 0.5 * (leftPtr->GetNormal() + rightPtr->GetNormal());
                    normal.Normalize();
                    leftPtr->SetNormal(normal);
                }
            }
            else
            {
                auto color = 0.5 * (leftPtr->ToFloat4() + rightPtr->ToFloat4());
                leftPtr->FromFloat4(color);
            }

            *rightPtr = *leftPtr;

            rightPtr += pitch;
            leftPtr += pitch;
        }
    }
}

void ImageProcess2(imageio::image& topImage, imageio::image& bottomImage, TerrainImageType type)
{
    if (topImage.get_width() && bottomImage.get_width())
    {
        auto pitch = topImage.get_pitch();
        auto topPtr = reinterpret_cast<TerrainRGBA*>(bottomImage.get_ptr() + pitch * bottomImage.get_height() - pitch);
        auto bottomPtr = reinterpret_cast<TerrainRGBA*>(topImage.get_ptr());
        for (size_t i = 0; i < topImage.get_width(); i++)
        {
            if (type == TerrainImageType::Height)
            {
                if (!topPtr->isHole())
                {
                    auto normal = 0.5 * (topPtr->GetNormal() + bottomPtr->GetNormal());
                    normal.Normalize();
                    topPtr->SetNormal(normal);
                }
            }
            else
            {
                auto color = 0.5 * (topPtr->ToFloat4() + bottomPtr->ToFloat4());
                topPtr->FromFloat4(color);
            }
            *bottomPtr = *topPtr;

            bottomPtr += 1;
            topPtr += 1;
        }
    }
}

void TerrainImporter::BuildQuadtree(const std::string& inDir, const std::string& outDir, const std::string& prefix, UInt32 blockX, UInt32 blockY, const TerrainInfo& terrainInfo, TerrainImageType type)
{
    UInt32 num_levels = static_cast<UInt32>(std::log2(terrainInfo.mBlockSize)) + 1;

    UInt32 tile_x_begin = 0, tile_x_end = terrainInfo.mBlockSize / 2;
    UInt32 tile_y_begin = 0, tile_y_end = terrainInfo.mBlockSize / 2;

    auto postProcess = [&](TileIndex begin, TileIndex end, UInt32 dx, UInt32 dy, std::function<void(imageio::image&, imageio::image&, TerrainImageType)> imageProcess) {
        Assert(begin.mLevel == end.mLevel);
        TileIndex prevTile = begin;
        TileIndex tile = {begin.mBlockX, begin.mBlockY, begin.mLevel, begin.mTileX + dx, begin.mTileY + dy};

        auto prevImage = TileLoadImage(inDir, prefix, prevTile);
        for (; tile != end; tile.mTileX += dx, tile.mTileY += dy)
        {
            auto image = TileLoadImage(inDir, prefix, tile);
            imageProcess(prevImage, image, type);
            if (prevImage.get_width())
            {
                TileSaveImage(prevImage, inDir, prefix, prevTile);
            }
            prevImage.swap(image);
            prevTile = tile;
        }
        if (prevImage.get_width())
        {
            TileSaveImage(prevImage, inDir, prefix, prevTile);
        }
    };

    for (UInt32 level = 1; level < num_levels; level++)
    {
        Assert(tile_x_end > tile_x_begin && tile_y_end > tile_y_begin);

        const auto num_tiles = terrainInfo.mBlockSize >> level;
        UInt32 next_tile_x_begin = num_tiles / 2, next_tile_x_end = 0;
        UInt32 next_tile_y_begin = num_tiles / 2, next_tile_y_end = 0;

        threading::TaskEventArray rowEvents;
        threading::TaskEventArray colEvents;

        std::vector<threading::TaskEventArray> tasks;
        for (UInt32 ty = tile_y_begin; ty < tile_y_end; ty++)
        {
            auto& rowtasks = tasks.emplace_back();
            for (UInt32 tx = tile_x_begin; tx < tile_x_end; tx++)
            {
                TileIndex tileIdx = {blockX, blockY, level, tx, ty};
                auto targetPath = TileIndex::GetPngPath(inDir, prefix, tileIdx);

                std::array<std::string, 4> imagePathList;
                bool needGererate = false;

                UInt32 t = 0;
                for (auto childTileIdx : tileIdx.GetChildern())
                {
                    auto tilePath = TileIndex::GetPngPath(inDir, prefix, childTileIdx);
                    imagePathList[t++] = tilePath;
                    needGererate |= fs::exists(tilePath);
                }

                if (needGererate)
                {
                    next_tile_x_begin = std::min(next_tile_x_begin, tx / 2);
                    next_tile_x_end = std::max(next_tile_x_end, tx / 2 + 1);
                    next_tile_y_begin = std::min(next_tile_y_begin, ty / 2);
                    next_tile_y_end = std::max(next_tile_y_end, ty / 2 + 1);

                    if (enableMultiThreading)
                    {
                        rowtasks.Add(threading::Dispatch({}, [=](auto) {
                            LOG_DEBUG("Generate tile {}_{}_{}_{}.png", prefix, level, tx, ty);
                            GenerateTileImage(targetPath, imagePathList, type);
                        }));
                    }
                    else
                    {
                        GenerateTileImage(targetPath, imagePathList, type);
                    }
                }
            }
        }
        if (false)
        {
            for (UInt32 ty = tile_y_begin, idx = 0; ty < tile_y_end; ty++, idx++)
            {
                rowEvents.Add(threading::Dispatch(tasks[idx], [=](auto) {
                    TileIndex begin = {blockX, blockY, level, tile_x_begin, ty};
                    TileIndex end = {blockX, blockY, level, tile_x_end, ty};
                    postProcess(begin, end, 1, 0, ImageProcess1);
                }));
            }
            rowEvents.WaitForCompletion();

            for (UInt32 tx = tile_x_begin; tx < tile_x_end; tx++)
            {
                colEvents.Add(threading::Dispatch({}, [=](auto) {
                    TileIndex begin = {blockX, blockY, level, tx, tile_y_begin};
                    TileIndex end = {blockX, blockY, level, tx, tile_y_end};
                    postProcess(begin, end, 0, 1, ImageProcess2);
                }));
            }
            colEvents.WaitForCompletion();
        }
        else
        {
            for (UInt32 ty = tile_y_begin; ty < tile_y_end; ty++)
            {
                TileIndex begin = {blockX, blockY, level, tile_x_begin, ty};
                TileIndex end = {blockX, blockY, level, tile_x_end, ty};
                postProcess(begin, end, 1, 0, ImageProcess1);
            }

            for (UInt32 tx = tile_x_begin; tx < tile_x_end; tx++)
            {
                TileIndex begin = {blockX, blockY, level, tx, tile_y_begin};
                TileIndex end = {blockX, blockY, level, tx, tile_y_end};
                postProcess(begin, end, 0, 1, ImageProcess2);
            }
        }
        tile_x_begin = next_tile_x_begin;
        tile_x_end = next_tile_x_end;
        tile_y_begin = next_tile_y_begin;
        tile_y_end = next_tile_y_end;
    }
}

void TerrainImporter::UpdateQuadtreeOfHeightmap(const std::string& inDir, const std::string& outDir, const std::string& prefix, const TerrainInfo& terrainInfo)
{
    const auto fileSystem = cross::EngineGlobal::Inst().GetFileSystem();

    const UInt32 width = terrainInfo.mTileSize + 1, height = width;
    const UInt32 numLevels = static_cast<UInt32>(std::log2(terrainInfo.mBlockSize)) + 1;

    auto tileList = GetTileIndexList(inDir, prefix);
    tileList.erase(std::remove_if(tileList.begin(), tileList.end(), [&](const TileIndex & t) { return t.mLevel > 0; }), tileList.end());

    std::unordered_set<TileIndex> tileSet, nextTileSet;
    for (auto& tile : tileList)
    {
        nextTileSet.insert(tile.GetParent());
    }

    for (UInt32 level = 1; level < numLevels; level++)
    {
        tileSet.swap(nextTileSet);
        nextTileSet.clear();

        for (auto& tileIdx : tileSet)
        {
            std::array<std::string, 4> imagePathList;
            auto childern = tileIdx.GetChildern();
            size_t validChild = 0;
            for (size_t i = 0; i < 4; i++)
            {
                auto tilePath = TileIndex::GetPngPath(inDir, prefix, childern[i]);
                imagePathList[i] = tilePath;
                validChild += !!fs::exists(tilePath);
            }

            if (validChild > 0)
            {
                nextTileSet.insert(tileIdx.GetParent());
                std::string ndaPath = TileIndex::GetNdaPath(outDir, prefix, tileIdx);
                std::string imagePath = TileIndex::GetPngPath(inDir, prefix, tileIdx);
                // printf("!! Generate %s\n", imagePath.c_str());

                TileHeightmap tileData(height, width);
                if (validChild < 4 && fileSystem->HaveFile(ndaPath))
                {
                    Assert(tileData.LoadNda(ndaPath));
                    Assert(tileData.height == height && tileData.width == width);
                }

                // UpdateTileHeightmap(tileData, imagePath, imagePathList);
                auto TL = TileHeightmap::LoadFromPng(imagePathList[0]);
                auto BL = TileHeightmap::LoadFromPng(imagePathList[1]);
                auto TR = TileHeightmap::LoadFromPng(imagePathList[2]);
                auto BR = TileHeightmap::LoadFromPng(imagePathList[3]);

                HeightampStitching(tileData, TL, BL, TR, BR);
                tileData.SaveToPng(imagePath);
            }
        }
    }
}

void TerrainImporter::UpdateQuadtreeOfTexture(const std::string& inDir, const std::string& outDir, const std::string& prefix, const TerrainInfo& terrainInfo)
{
    const auto fileSystem = cross::EngineGlobal::Inst().GetFileSystem();

    const UInt32 numLevels = static_cast<UInt32>(std::log2(terrainInfo.mBlockSize)) + 1;

    //std::string blockPrefix = fmt::format("{}_{}_{}", prefix, blockX, blockY);
    auto tileList = GetTileIndexList(inDir, prefix);
    tileList.erase(std::remove_if(tileList.begin(), tileList.end(), [&](const TileIndex& t) { return t.mLevel > 0; }), tileList.end());

    std::unordered_set<TileIndex> tileSet, nextTileSet;
    for (auto& tile : tileList)
    {
        nextTileSet.insert(tile.GetParent());
    }

    for (UInt32 level = 1; level < numLevels; level++)
    {
        tileSet.swap(nextTileSet);
        nextTileSet.clear();

        for (auto& tileIdx : tileSet)
        {
            std::array<std::string, 4> imagePathList;
            auto childern = tileIdx.GetChildern();
            size_t validChild = 0;
            for (size_t i = 0; i < 4; i++)
            {
                auto tilePath = TileIndex::GetPngPath(inDir, prefix, childern[i]);
                imagePathList[i] = tilePath;
                validChild += !!fs::exists(tilePath);
            }

            if (validChild > 0)
            {
                nextTileSet.insert(tileIdx.GetParent());
                std::string ndaPath = TileIndex::GetNdaPath(outDir, prefix, tileIdx);
                std::string imagePath = TileIndex::GetPngPath(inDir, prefix, tileIdx);
                // printf("!! Generate %s\n", imagePath.c_str());

                auto TL = TileHeightmap::LoadFromPng(imagePathList[0]);
                auto BL = TileHeightmap::LoadFromPng(imagePathList[1]);
                auto TR = TileHeightmap::LoadFromPng(imagePathList[2]);
                auto BR = TileHeightmap::LoadFromPng(imagePathList[3]);

                UInt32 tSize = std::max(std::max(TL.width, BL.width), std::max(TR.width, BR.width));

                TileHeightmap tileData(tSize, tSize);
                if (validChild < 4 && fileSystem->HaveFile(ndaPath))
                {
                    Assert(tileData.LoadNda(ndaPath));
                }
                TextureStitching(tileData, TL, BL, TR, BR);
                tileData.SaveToPng(imagePath);
            }
        }
    }
}

void TerrainImporter::ImportHeightMapDFS(const std::string& inDir, const std::string& outDir, const std::string& prefix, std::vector<ImageView>& imageList, const TileIndex& tileIndex, const TerrainInfo& terrainInfo, UInt32 stopLevel)
{
    std::string imagePath = TileIndex::GetPngPath(inDir, prefix, tileIndex);

    if (!fs::exists(imagePath))
        return;

    TileHeightmap heightmap = TileHeightmap::LoadFromPng(imagePath);
    UInt32 tileSize = heightmap.width;

    ImageView imageBase(heightmap);
    imageList[tileIndex.mLevel] = imageBase;

    UInt32 offsetX = 0, offsetY = 0;
    UInt32 xx = tileIndex.mTileX, yy = tileIndex.mTileY;
    UInt32 ts = (tileSize + 1) / 2;

    std::vector<ImageView> subImageList;

    for (size_t i = tileIndex.mLevel + 1; i < imageList.size(); i++)
    {
        offsetX = offsetX / 2 + (xx % 2) * (tileSize / 2);
        offsetY = offsetY / 2 + (yy % 2) * (tileSize / 2);
        subImageList.push_back(imageList[i].SubImage(offsetY, offsetX, ts, ts));
        xx /= 2;
        yy /= 2;
        ts = (ts + 1) / 2;
    }

    if (mImportSetting->GenerateCollision)
    {
        auto modelPath = TileIndex::GetCollisionPath(outDir, prefix, tileIndex);
        GenerateMeshCollision(imageBase, modelPath, tileIndex, terrainInfo);
    }

    CrossSchema::TextureAssetT texture;
    LoadTextureAssetWithMipMap(texture, imageBase, subImageList);

    // std::string prefix = fmt::format("{}/{}_{}_{}", interOutDir, level, x, y);
    // for (int i = 0; i < texture.images.size(); i++) {
    //    std::string fpath = prefix + "_mip_" + std::to_string(i) + ".png";
    //    UInt32 offset = texture.images[i].dataoffset();
    //    UInt32 width = texture.images[i].width();
    //    UInt32 height = texture.images[i].height();
    //    SavePng(texture.data.data() + offset, width, height, fpath);
    //}

    std::string ndaFilePath = TileIndex::GetNdaPath(outDir, prefix, tileIndex);
    SerializeTexture2D(texture, ndaFilePath, HMImportSetting);
    // LOG_DEBUG("Import HeightMap {} -> {}", imagePath, ndaFilePath);

    if (tileIndex.mLevel > stopLevel)
    {
        for (auto& childTile : tileIndex.GetChildern())
        {
            ImportHeightMapDFS(inDir, outDir, prefix, imageList, childTile, terrainInfo, stopLevel);
        }
    }
}

TextureImportSetting TerrainImporter::HMImportSetting = {TextureType::ImageTexture, Linear, Uncompressed, true, false, false, false, false, false};
TextureImportSetting TerrainImporter::ATImportSetting = {TextureType::ImageTexture, SRGB, BC7, true, false, false, false, false, false};
TextureImportSetting TerrainImporter::WTImportSetting = {TextureType::ImageTexture, Linear, Uncompressed, true, false, false, false, false, false};

TerrainImporter::TerrainImporter()
    : AssetImporter(AssetType::Terrain)
{}

bool TerrainImporter::CheckAssetName(const char* name) const
{
    return HasExtension(name, ".terrain");
}

bool TerrainImporter::UpdateHeightmap(const std::string& terrainNdaPath, const std::string& updatePath)
{
    if (!PathHelper::IsDirectoryExist(updatePath))
    {
        LOG_ERROR("{0} doest not exists!", updatePath);
        return false;
    }

    const auto terrainResourcePtr = TypeCast<resource::TerrainResource>(gAssetStreamingManager->LoadSynchronously(terrainNdaPath));
    const auto terrainInfo = terrainResourcePtr->mTerrainInfo;

    const auto rootDir = PathHelper::GetCurrentDirectoryPath() + "/" + terrainInfo.mRootDataPath;
    const auto prefixParent = PathHelper::GetParentPath(terrainInfo.mHeightmapPrefix);
    const auto prefixBase = PathHelper::GetBaseFileName(terrainInfo.mHeightmapPrefix);
    const auto outputPath = rootDir + "/" + prefixParent;
    const auto inputPath = updatePath + "/" + prefixParent;

    Assert(PathHelper::IsDirectoryExist(inputPath));

    if (!PathHelper::IsDirectoryExist(outputPath))
    {
        PathHelper::MakeDirectory(outputPath);
    }

    UpdateQuadtreeOfHeightmap(inputPath, outputPath, prefixBase, terrainInfo);
    ImportHeightMapQuick(inputPath, outputPath, prefixBase, terrainInfo);
    return true;
}

bool TerrainImporter::UpdateWeightTexture(const std::string& terrainNdaPath, const std::string& updatePath)
{
    if (!fs::exists(updatePath))
    {
        LOG_ERROR("{0} doest not exists!", updatePath);
        return false;
    }

    const auto terrainResourcePtr = TypeCast<resource::TerrainResource>(gAssetStreamingManager->LoadSynchronously(terrainNdaPath));
    const auto terrainInfo = terrainResourcePtr->mTerrainInfo;

    const auto rootDir = PathHelper::GetCurrentDirectoryPath() + "/" + terrainInfo.mRootDataPath;
    const auto prefixParent = PathHelper::GetParentPath(terrainInfo.mWeightTexturePrefix);
    const auto prefixBase = PathHelper::GetBaseFileName(terrainInfo.mWeightTexturePrefix);
    const auto outputPath = rootDir + "/" + prefixParent;
    const auto inputPath = updatePath + "/" + prefixParent;

    Assert(PathHelper::IsDirectoryExist(inputPath));

    for (size_t i = 0; i < NumMaxTerrainBlendLayers; i++)
    {
        UpdateQuadtreeOfTexture(inputPath, outputPath, prefixBase + std::to_string(i), terrainInfo);
        ImportTexture(inputPath, outputPath, prefixBase + std::to_string(i), WTImportSetting);
    }

    return true;
}

void TerrainImporter::ImportTexture(const std::string& inDir, const std::string& outDir, const std::string& prefix, TextureImportSetting& importSetting)
{
    threading::TaskEventArray events;

    auto textureImporter = [&](std::vector<std::string> list)
    {
        for (const auto& filename : list)
        {
            LOG_INFO("Importing texture {} ...", filename);
            std::string baseName = PathHelper::GetBaseFileName(filename);
            std::string ndaFilePath = outDir + "/" + baseName + ".nda";
            std::string assetFilePath = inDir + "/" + filename;
            const auto fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
            if (!fileSystem->HaveFile(ndaFilePath))
            {
                mTextureImporter.ImportAsset(assetFilePath, ndaFilePath, &importSetting);
            }
        }
    };

    const auto numFiles = std::distance(fs::directory_iterator(inDir), fs::directory_iterator{});
    const auto numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
    const auto batchSize = static_cast<std::size_t>((numFiles + numWorkers - 1U) / numWorkers);

    std::vector<std::string> list;
    for (const auto& f : fs::directory_iterator(inDir))
    {
        const auto& fpath = f.path();
        if (fpath.extension() == ".png")
        {
            const auto& filename = fpath.filename().string();

            if (filename.rfind(prefix) == 0)
            {
                list.push_back(filename);
                if (batchSize == list.size())
                {
                    if (enableMultiThreading)
                    {
                        auto taskEvent = threading::Dispatch({}, [&, localList = std::move(list)](auto) { textureImporter(localList); });
                        events.Add(taskEvent);
                    }
                    else
                    {
                        textureImporter(std::move(list));
                    }

                    Assert(list.empty());
                }
            }
        }
    }

    if (enableMultiThreading)
    {
        events.WaitForCompletion();
    }

    textureImporter(std::move(list));
}

void TerrainImporter::ImportTexture(const std::vector<std::string>& textures, const std::string& outDir, std::vector<std::string>& outNdas, TextureImportSetting& importSetting)
{
    const auto& curDir = PathHelper::GetCurrentDirectoryPath();

    outNdas.reserve(textures.size());
    for (const auto& fpath : textures)
    {
        if (fs::exists(fpath))
        {
            const auto& ext = PathHelper::GetExtension(fpath);
            if (mTextureImporter.CheckAssetName(fpath.c_str()))
            {
                std::string baseName = PathHelper::GetBaseFileName(fpath);
                std::string ndaFilePath = outDir + "/" + baseName + ".nda";
                mTextureImporter.ImportAsset(fpath, ndaFilePath, &importSetting);
                outNdas.push_back(PathHelper::GetRelativePath(curDir, ndaFilePath));
            }
            else if (ext == "tex.nda" || ext == "nda")
            {
                if (fpath.find("EngineResource") != -1)
                {
                    const auto& engineResourceDirectory = PathHelper::GetEngineResourceDirectoryPath();
                    outNdas.push_back(PathHelper::GetRelativePath(engineResourceDirectory, fpath));
                }
                else
                {
                    outNdas.push_back(PathHelper::GetRelativePath(curDir, fpath));
                }
            }
            else
            {
                outNdas.push_back("");
                LOG_EDITOR_ERROR("Can not import this texture, {}", fpath);
            }
        }
        else
        {
            LOG_EDITOR_ERROR("texture does not exist: {}", fpath);
        }
    }
}

void TerrainImporter::ImportHeightMap(const std::string& inDir, const std::string& outDir, const std::string& prefix, UInt32 blockX, UInt32 blockY, const TerrainInfo& terrainInfo)
{
    static constexpr UInt32 stopLevel = 3;
    UInt32 numlevel = static_cast<UInt32>(std::log2(terrainInfo.mBlockSize));
    const TileIndex rootTile = {blockX, blockY, numlevel, 0, 0};

    std::vector<ImageView> imageViewList(rootTile.mLevel + 1U);

    if (enableMultiThreading && rootTile.mLevel > stopLevel)
    {
        threading::TaskEventArray events;

        events.Add(threading::Dispatch({}, [=](const auto&) mutable { ImportHeightMapDFS(inDir, outDir, prefix, imageViewList, rootTile, terrainInfo, stopLevel); }));

        std::unordered_map<TileIndex, TileHeightmap> imageDict;

        std::vector<TileIndex> nextTileList, currentTileList;
        currentTileList.push_back(rootTile);

        for (UInt32 level = rootTile.mLevel; level > stopLevel; level--)
        {
            nextTileList.clear();
            for (auto& tileIdx : currentTileList)
            {
                std::string imagePath = TileIndex::GetPngPath(inDir, prefix, tileIdx);
                if (fs::exists(imagePath))
                {
                    imageDict[tileIdx] = TileHeightmap::LoadFromPng(imagePath);
                    for (const auto& childTile : tileIdx.GetChildern())
                    {
                        nextTileList.push_back(childTile);
                    }
                }
            }
            currentTileList.swap(nextTileList);
        }

        for (auto& tileIdx : currentTileList)
        {
            std::string imagePath = TileIndex::GetPngPath(inDir, prefix, tileIdx);
            if (fs::exists(imagePath))
            {
                for (auto idx = tileIdx.GetParent(); idx.mLevel <= rootTile.mLevel; idx = idx.GetParent())
                {
                    imageViewList[idx.mLevel] = ImageView(imageDict[idx]);
                }
                events.Add(threading::Dispatch({}, [=](auto) mutable { ImportHeightMapDFS(inDir, outDir, prefix, imageViewList, tileIdx, terrainInfo); }));
            }
        }

        events.WaitForCompletion();
    }
    else
    {
        ImportHeightMapDFS(inDir, outDir, prefix, imageViewList, rootTile, terrainInfo);
    }
}

void TerrainImporter::ImportHeightMapQuick(const std::string& inDir, const std::string& outDir, const std::string& prefix, const TerrainInfo& terrainInfo)
{
    threading::TaskEventArray events;

    auto textureImporter = [&](std::vector<std::string> list)
    {
        for (const auto& filename : list)
        {
            LOG_DEBUG("Importing heightmap {} ...", filename);
            std::string baseName = PathHelper::GetBaseFileName(filename);
            std::string ndaFilePath = outDir + "/" + baseName + ".nda";
            std::string assetFilePath = inDir + "/" + filename;
            mTextureImporter.ImportAsset(assetFilePath, ndaFilePath, &HMImportSetting);
            if (terrainInfo.mHasCollision)
            {
                auto tileIdx = GetTileIndexFromFileName(filename);
                auto heightmap = TileHeightmap::LoadFromPng(assetFilePath);
                auto collisionPath = outDir + "/" + baseName + "_collision.nda";
                GenerateMeshCollision(heightmap, collisionPath, tileIdx, terrainInfo);
            }
        }
    };

    const auto numFiles = std::distance(fs::directory_iterator(inDir), fs::directory_iterator{});
    const auto numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
    const auto batchSize = static_cast<std::size_t>((numFiles + numWorkers - 1U) / numWorkers);

    std::vector<std::string> list;
    for (const auto& f : fs::directory_iterator(inDir))
    {
        const auto& fpath = f.path();
        if (fpath.extension() == ".png")
        {
            const auto& filename = fpath.filename().string();

            if (filename.rfind(prefix) == 0)
            {
                list.push_back(filename);
                if (batchSize == list.size())
                {
                    if (enableMultiThreading)
                    {
                        auto taskEvent = threading::Dispatch({}, [&, localList = std::move(list)](auto) { textureImporter(localList); });
                        events.Add(taskEvent);
                    }
                    else
                    {
                        textureImporter(std::move(list));
                    }

                    Assert(list.empty());
                }
            }
        }
    }

    if (enableMultiThreading)
    {
        events.WaitForCompletion();
    }

    textureImporter(std::move(list));
}

void TerrainImporter::ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting)
{
    enableMultiThreading = EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeCrossEditor;

    mImportSetting = static_cast<TerrainImportSetting*>(setting);

    auto outputRoot = PathHelper::GetDirectoryFromAbsolutePath(ndaSavePath);
    const auto& curDir = PathHelper::GetCurrentDirectoryPath();

    auto tileRootPath = mImportSetting->TileRootPath;

    if (!PathHelper::IsDirectoryExist(tileRootPath))
    {
        LOG_EDITOR_ERROR("Directory does not exist: {}", tileRootPath);
        return;
    }

    TerrainResourcePtr terrainRes = gResourceMgr.CreateResourceAs<resource::TerrainResource>();
    TerrainInfo& terrainInfo = terrainRes->mTerrainInfo;

    terrainInfo.mSurfaceType = mImportSetting->SurfaceType;
    terrainInfo.mGridSizeX = mImportSetting->GridSizeX;
    terrainInfo.mGridSizeY = mImportSetting->GridSizeY;
    terrainInfo.mBlockSize = mImportSetting->BlockSize;
    terrainInfo.mTileSize = mImportSetting->TileSize;
    terrainInfo.mHeightmapPrefix = mImportSetting->HeightmapPrefix;
    terrainInfo.mAlbedoTexturePrefix = mImportSetting->AlbedoTexturePrefix;
    terrainInfo.mWeightTexturePrefix = mImportSetting->WeightTexturePrefix;
    terrainInfo.mHasCollision = mImportSetting->GenerateCollision;
    terrainInfo.mWGS84SemiMajor = mImportSetting->WGS84SemiMajor;
    terrainInfo.mWGS84HeightScale = mImportSetting->WGS84HeightScale;
    terrainInfo.mRootDataPath = PathHelper::GetRelativePath(curDir, outputRoot);


    {
        auto prefixParent = PathHelper::GetParentPath(terrainInfo.mHeightmapPrefix);
        auto prefixBase = PathHelper::GetBaseFileName(terrainInfo.mHeightmapPrefix);
        auto inputPath = tileRootPath + prefixParent;
        auto outputPath = outputRoot + prefixParent;
        Assert(PathHelper::IsDirectoryExist(inputPath));
        if (!PathHelper::IsDirectoryExist(outputPath))
        {
            PathHelper::MakeDirectory(outputPath);
        }
        for (const auto& blockPrefix : GetBlockPrefix(inputPath, prefixBase))
        {
            auto [blockX, blockY] = GetTileBlockCrood(blockPrefix);
            //BuildQuadtree(inputPath, outputPath, prefixBase, blockX, blockY, terrainInfo, TerrainImageType::Height);
            ImportHeightMap(inputPath, outputPath, prefixBase, blockX, blockY, terrainInfo);
        }
    }

    if (terrainInfo.mBaseColorTextures.empty())
    {
        auto prefixParent = PathHelper::GetParentPath(terrainInfo.mAlbedoTexturePrefix);
        auto prefixBase = PathHelper::GetBaseFileName(terrainInfo.mAlbedoTexturePrefix);
        auto inputPath = tileRootPath + prefixParent;
        auto outputPath = outputRoot + prefixParent;
        Assert(PathHelper::IsDirectoryExist(inputPath));
        //if (!PathHelper::IsDirectoryExist(outputPath))
        //{
        //    PathHelper::MakeDirectory(outputPath);
        //}
        //for (const auto& blockPrefix : GetBlockPrefix(inputPath, prefixBase))
        //{
        //    auto [blockX, blockY] = GetTileBlockCrood(blockPrefix);
        //    BuildQuadtree(inputPath, outputPath, prefixBase, blockX, blockY, terrainInfo, TerrainImageType::Albedo);
        //}

        ATImportSetting.FlipUV = mImportSetting->FlipUV;
        ImportTexture(inputPath, outputPath, prefixBase, ATImportSetting);
    }
    else
    {
        auto prefixParent = PathHelper::GetParentPath(terrainInfo.mWeightTexturePrefix);
        auto prefixBase = PathHelper::GetBaseFileName(terrainInfo.mWeightTexturePrefix);
        auto inputPath = tileRootPath + prefixParent;
        auto outputPath = outputRoot + prefixParent;
        Assert(PathHelper::IsDirectoryExist(inputPath));
        if (!PathHelper::IsDirectoryExist(outputPath))
        {
            PathHelper::MakeDirectory(outputPath);
        }
        for (size_t i = 0; i < NumMaxTerrainBlendLayers; i++)
        {
            UpdateQuadtreeOfTexture(inputPath, outputPath, prefixBase + std::to_string(i), terrainInfo);
            ImportTexture(inputPath, outputPath, prefixBase + std::to_string(i), WTImportSetting);
        }

        ImportTexture(mImportSetting->BaseColorTextures, outputRoot, terrainInfo.mBaseColorTextures, WTImportSetting);
        ImportTexture(mImportSetting->NormalTextures, outputRoot, terrainInfo.mNormalTextures, WTImportSetting);
        ImportTexture(mImportSetting->HMRATextures, outputRoot, terrainInfo.mHMRATextures, WTImportSetting);
    }

    // { // only import mesh collision
    //     for (auto p : fs::directory_iterator(heightmapPath)) {
    //         auto stemName = p.path().filename().stem().string();
    //         auto imagePath = p.path().string();
    //         auto tileIdx = GetTileIndexFromFileName(stemName);
    //         std::string modelPath = outputPath + "/" + stemName + "_collision.nda";

    //         printf("%s -> %s\n", imagePath.c_str(), modelPath.c_str());
    //         //if (tileIdx.Level < 3) continue;

    //         imageio::image image;
    //         imageio::load_png(imagePath, image);
    //         GenerateMeshCollision(image, modelPath, tileIdx, terrainInfo);
    //     }
    // }

    terrainRes->Serialize(ndaSavePath);
}

}   // namespace cross::editor
