#pragma once
#include "EnginePrefix.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "AssetPipeline/Import/AssetImportSetting.h"
#include "Resource/TerrainResource.h"

namespace cross::editor {
struct ASSET_API TerrainImportSetting : ImportSetting
{
    CE_Virtual_Serialize_Deserialize;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    TerrainSurfaceType SurfaceType;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    UInt32 GridSizeX = 1U;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    UInt32 GridSizeY = 1U;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    UInt32 BlockSize = 1U;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    UInt32 TileSize = 128U;

    CEProperty(<PERSON><PERSON><PERSON>, Editor) CECSAttribute(PropertyInfo(PropertyType = "StringAsFolder"))
    std::string TileRootPath = "";

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    std::string AlbedoTexturePrefix = "";

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    std::string HeightmapPrefix = "";

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    std::string WeightTexturePrefix = "";

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    bool GenerateCollision = true;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    bool FlipUV = false;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    double WGS84SemiMajor = cross::WGS84SemiMajorAxis;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    float WGS84HeightScale = 1.0f;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    std::vector<std::string> BaseColorTextures;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    std::vector<std::string> NormalTextures;

    CEProperty(Serialize, Editor) CECSAttribute(PropertyInfo())
    std::vector<std::string> HMRATextures;
};

CEMeta(Editor) extern TerrainImportSetting gTerrainImportSetting;
}   // namespace cross::editor