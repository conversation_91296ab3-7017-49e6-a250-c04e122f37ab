#pragma once
#include "EnginePrefix.h"

#include "CrossImage/image.h"
#include "AssetPipeline/Protocol/Model/ImportMeshAssetData.h"
#include "Resource/TerrainResource.h"
#include "TextureAsset_generated.h"
#include "TerrainImportSetting.h"

namespace cross::editor {
enum class TerrainImageType
{
    Height,
    Albedo,
    Normal
};

struct TerrainRGBA
{
    UInt8 r, g, b, a;

    TerrainRGBA() = default;
    TerrainRGBA(UInt8 r, UInt8 g, UInt8 b, UInt8 a)
        : r(r)
        , g(g)
        , b(b)
        , a(a)
    {}

    Float4 ToFloat4() const
    {
        return {static_cast<float>(r), static_cast<float>(g), static_cast<float>(b), static_cast<float>(a)};
    }
    void FromFloat4(const Float4& value)
    {
        r = static_cast<UInt8>(std::clamp(value.x + 0.5f, 0.0f, 255.0f));
        g = static_cast<UInt8>(std::clamp(value.y + 0.5f, 0.0f, 255.0f));
        b = static_cast<UInt8>(std::clamp(value.z + 0.5f, 0.0f, 255.0f));
        a = static_cast<UInt8>(std::clamp(value.w + 0.5f, 0.0f, 255.0f));
    }

    Float3 GetNormal() const
    {
        float x = 2.f / 255.f * b - 1.f;
        float z = 2.f / 255.f * a - 1.f;
        float y = sqrt(std::clamp(1.f - x * x - z * z, 0.0f, 1.0f));
        return {x, y, z};
    }

    void SetNormal(const Float3& normal)
    {
        b = static_cast<UInt8>(std::clamp(127.5f * (normal.x + 1.f) + 0.5f, 0.f, 255.f));
        a = static_cast<UInt8>(std::clamp(127.5f * (normal.z + 1.f) + 0.5f, 0.f, 255.f));
    }

    void SetNormal(const TerrainRGBA& other)
    {
        b = other.b;
        a = other.a;
    }

    bool isHole() const
    {
        return r == 0 && g == 0;
    }

    float GetHeight() const
    {
        return g * 256.0f + r - 32768.0f;
    }

    void SetHeight(float value)
    {
        if (!std::isnan(value))
        {
            const auto height = std::clamp(value + 32768.f, 0.0f, 65536.0f);
            UInt16 H = static_cast<UInt16>(height + 0.5f);
            g = UInt8(H >> 8);
            r = UInt8(H);
        }
        else
        {
            g = r = 0;
        }
    }
};

struct ASSET_API TileHeightmap
{
    UInt32 height;
    UInt32 width;
    std::vector<TerrainRGBA> pixels;

    TileHeightmap()
        : height(0)
        , width(0)
        , pixels(0)
    {}

    TileHeightmap(UInt32 height, UInt32 width)
        : height(height)
        , width(width)
        , pixels(width * height)
    {}

    const TerrainRGBA* GetPtr() const
    {
        return pixels.data();
    }
    TerrainRGBA* GetPtr()
    {
        return pixels.data();
    }

    bool IsValid() const
    {
        return !pixels.empty();
    }

    void Load(const UInt8* data)
    {
        memcpy(GetPtr(), data, sizeof(pixels[0]) * height * width);
    }

    bool LoadPng(const std::string& fpath);
    bool LoadNda(const std::string& ndapath);
    bool SaveToPng(const std::string& fpath) const;
    static TileHeightmap LoadFromPng(const std::string& fpath);
    static TileHeightmap LoadFromNda(const std::string& fpath);
};

struct ImageView
{
    UInt32 height;
    UInt32 width;
    UInt32 pitch;
    const TerrainRGBA* data;

    ImageView()
        : height(0)
        , width(0)
        , pitch(0)
        , data(nullptr)
    {}

    ImageView(UInt32 height, UInt32 width, UInt32 pitch, const TerrainRGBA* data)
        : height(height)
        , width(width)
        , pitch(pitch)
        , data(data)
    {}

    ImageView(const imageio::image& image)
        : height(image.get_height())
        , width(image.get_width())
        , pitch(image.get_pitch())
        , data(reinterpret_cast<const TerrainRGBA*>(image.get_ptr()))
    {}

    ImageView(const TileHeightmap& heightmap)
        : height(heightmap.height)
        , width(heightmap.width)
        , pitch(heightmap.width)
        , data(heightmap.GetPtr())
    {}

    ImageView SubImage(UInt32 offsetY, UInt32 offsetX, UInt32 subHeight, UInt32 subWidth)
    {
        Assert(offsetX < width && offsetY < height);
        return ImageView(subHeight, subWidth, pitch, data + offsetY * pitch + offsetX);
    }
};

imageio::image ImageStitching(const imageio::image& image0, const imageio::image& image1, const imageio::image& image2, const imageio::image& image3);

imageio::image ImageDownSampling(const imageio::image& srcImage, TerrainImageType type);

void GenerateTileImage(const std::string& targetPath, const std::array<std::string, 4>& imagePathList, TerrainImageType type);

void LoadTextureAssetWithMipMap(CrossSchema::TextureAssetT& texture, ImageView baseImage, const std::vector<ImageView>& mipList);

// MeshCollision
void AddPhysicsCollision(editor::MeshAssetData& meshAssetData, const std::vector<Float3>& positions, const std::vector<UInt32>& indices);

void AddSubMeshAssetData(editor::MeshAssetData& meshAssetData, const std::vector<Float3>& positions, const std::vector<UInt32>& indices, MeshBound meshBound);

bool GenerateMeshCollision(const ImageView& image, const std::string& filePath, const TileIndex& tileIndex, const TerrainInfo& terrainInfo);

TileHeightmap GenerateHeightmap(const TileIndex& tileIdx, const float* data, TerrainInfo terrainInfo);

bool HeightampStitching(TileHeightmap& dstHeightmap, const TileHeightmap& TL, const TileHeightmap& BL, const TileHeightmap& TR, const TileHeightmap& BR);

bool TextureStitching(TileHeightmap& dstHeightmap, const TileHeightmap& TL, const TileHeightmap& BL, const TileHeightmap& TR, const TileHeightmap& BR);

Float3 CalcNormal(const Float3& c, const Float3& top, const Float3& bottom, const Float3& left, const Float3& right);

CEFunction(Editor)
ASSET_API bool ConvertHeightmap(const std::string& inputFile, const std::string& outputFile);

}   // namespace cross::editor