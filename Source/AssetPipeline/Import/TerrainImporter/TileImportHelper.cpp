#include "EnginePrefix.h"

#include "TileImportHelper.h"
#include "Resource/Texture/Texture2D.h"
#include "AssetPipeline/Import/ModelImporter/ModelImportSettings.h"

#include "PhysicsEngine/PhysicsEngine.h"
#include "PhysicsEngine/PhysicsCooker.h"

#include "Resource/Resource.h"
#include "Resource/Texture/Texture.h"

#include "CrossImage/lodepng.h"

#include <filesystem>

namespace cross::editor {
namespace fs = std::filesystem;

constexpr UInt32 MaxMipNum = 16;

bool TileHeightmap::LoadPng(const std::string& fpath)
{
    imageio::image image;
    imageio::load_png(fpath, image);
    if (image.get_width() > 0)
    {
        width = image.get_width();
        height = image.get_height();
        pixels.resize(width * height);
        Load(reinterpret_cast<const UInt8*>(image.get_ptr()));
        return true;
    }
    return false;
}

bool TileHeightmap::LoadNda(const std::string& ndaPath)
{
    std::string ndaPathCurDir = ndaPath;
    if (PathHelper::IsAbsoluteFilePath(ndaPath))
    {
        std::string curDir = PathHelper::GetCurrentDirectoryPath();
        ndaPathCurDir = PathHelper::GetRelativePath(ndaPath);
    }
    const auto fileSystem = cross::EngineGlobal::Inst().GetFileSystem();

    if (fileSystem->HaveFile(ndaPathCurDir))
    {
        resource::ResourceLoadError errorCode{resource::ResourceLoadError::Succeeded};
        ResourcePtr resource(gResourceAssetMgr.LoadNDAFile(ndaPathCurDir.c_str(), errorCode));
        auto textureRes = TypeCast<resource::Texture2D>(resource);
        auto data = (textureRes->GetTextureData()->GetImageData(0));
        auto textureInfo = textureRes->GetTextureData()->GetTextureInfo();
        width = textureInfo.Width;
        height = textureInfo.Height;
        pixels.resize(width * height);
        if (errorCode == resource::ResourceLoadError::Succeeded)
        {
            Load(data);
            return true;
        }
    }
    return false;
}

bool TileHeightmap::SaveToPng(const std::string& fpath) const
{
    unsigned err = lodepng::encode(fpath, reinterpret_cast<const UInt8*>(GetPtr()), width, height, LCT_RGBA, 8);
    return err == 0;
}

TileHeightmap TileHeightmap::LoadFromPng(const std::string& fpath)
{
    TileHeightmap heightmap;
    heightmap.LoadPng(fpath);
    return heightmap;
}

TileHeightmap TileHeightmap::LoadFromNda(const std::string& fpath)
{
    TileHeightmap heightmap;
    heightmap.LoadNda(fpath);
    return heightmap;
}

imageio::image ImageStitching(const imageio::image& image0, const imageio::image& image1, const imageio::image& image2, const imageio::image& image3)
{
    UInt32 tileSize = std::max(std::max(image0.get_width(), image1.get_width()), std::max(image2.get_width(), image3.get_width()));
    UInt32 newTileSize = (tileSize * 2 - 1);
    imageio::image new_image(newTileSize, newTileSize);

    memset(new_image.get_ptr(), 0, new_image.get_total_pixels() * 4U);

    auto fill_rect = [&](imageio::image& dst, const imageio::image& src, size_t offset_x, size_t offset_y) {
        auto dstPitch = dst.get_pitch();
        auto srcPitch = src.get_pitch();
        size_t dstOffset = offset_y * dstPitch + offset_x;
        for (size_t y = 0; y != tileSize; y++)
        {
            memcpy(dst.get_ptr() + y * dstPitch + dstOffset, src.get_ptr() + y * srcPitch, tileSize * 4U);
        }
    };

    if (image0.get_pitch())
        fill_rect(new_image, image0, 0, 0);
    if (image1.get_pitch())
        fill_rect(new_image, image1, 0, tileSize - 1);
    if (image2.get_pitch())
        fill_rect(new_image, image2, tileSize - 1, 0);
    if (image3.get_pitch())
        fill_rect(new_image, image3, tileSize - 1, tileSize - 1);

    return new_image;
}

imageio::image ImageDownSampling(const imageio::image& srcImage, TerrainImageType type)
{
    static_assert(sizeof(TerrainRGBA) == sizeof(imageio::color_rgba));

    UInt32 srcPitch = srcImage.get_pitch();
    UInt32 dstPitch = srcPitch / 2 + 1;

    imageio::image image(dstPitch, dstPitch);
    auto dst_ptr = reinterpret_cast<TerrainRGBA*>(image.get_ptr());
    auto src_ptr = reinterpret_cast<const TerrainRGBA*>(srcImage.get_ptr());

    for (size_t i = 0; i != dstPitch; i++)
    {
        for (size_t j = 0; j != dstPitch; j++)
        {
            size_t co = (i * srcPitch + j) * 2;   // center of origin
            size_t to = i != 0 ? co - srcPitch : co;
            size_t bo = i != dstPitch - 1 ? co + srcPitch : co;
            size_t lo = j != 0 ? co - 1 : co;
            size_t ro = j != dstPitch - 1 ? co + 1 : co;

            if (type == TerrainImageType::Height)
            {
                *dst_ptr = src_ptr[co];
                if (!src_ptr[co].isHole())
                {
                    auto cn = src_ptr[co].GetNormal();
                    auto tn = !src_ptr[to].isHole() ? src_ptr[to].GetNormal() : cn;
                    auto bn = !src_ptr[bo].isHole() ? src_ptr[bo].GetNormal() : cn;
                    auto ln = !src_ptr[lo].isHole() ? src_ptr[lo].GetNormal() : cn;
                    auto rn = !src_ptr[ro].isHole() ? src_ptr[ro].GetNormal() : cn;

                    auto normal = 0.5 * cn + 0.125 * (tn + bn + ln + rn);
                    normal.Normalize();
                    dst_ptr->SetNormal(normal);
                }
            }
            else
            {
                auto ct = src_ptr[co].ToFloat4();
                auto tt = src_ptr[to].ToFloat4();
                auto bt = src_ptr[bo].ToFloat4();
                auto lt = src_ptr[lo].ToFloat4();
                auto rt = src_ptr[ro].ToFloat4();

                auto p = 0.2f * (ct + tt + bt + lt + rt);

                dst_ptr->FromFloat4(p);
            }
            dst_ptr++;
        }
    }

    return image;
}

TileHeightmap HeightampDownSampling(const TileHeightmap& srcHeightmap)
{
    UInt32 srcPitch = srcHeightmap.width;
    UInt32 dstPitch = srcPitch / 2 + 1;

    TileHeightmap dstHeightmap(dstPitch, dstPitch);
    auto dst_ptr = dstHeightmap.GetPtr();
    auto src_ptr = srcHeightmap.GetPtr();

    for (size_t i = 0; i != dstPitch; i++)
    {
        for (size_t j = 0; j != dstPitch; j++)
        {
            size_t co = (i * srcPitch + j) * 2;   // center of origin
            size_t to = i != 0 ? co - srcPitch : co + srcPitch;
            size_t bo = i != dstPitch - 1 ? co + srcPitch : co - srcPitch;
            size_t lo = j != 0 ? co - 1 : co + 1;
            size_t ro = j != dstPitch - 1 ? co + 1 : co - 1;

            *dst_ptr = src_ptr[co];
            if (!src_ptr[co].isHole())
            {
                auto cn = src_ptr[co].GetNormal();
                auto tn = !src_ptr[to].isHole() ? src_ptr[to].GetNormal() : cn;
                auto bn = !src_ptr[bo].isHole() ? src_ptr[bo].GetNormal() : cn;
                auto ln = !src_ptr[lo].isHole() ? src_ptr[lo].GetNormal() : cn;
                auto rn = !src_ptr[ro].isHole() ? src_ptr[ro].GetNormal() : cn;

                auto normal = 0.5 * cn + 0.125 * (tn + bn + ln + rn);
                normal.Normalize();
                dst_ptr->SetNormal(normal);
            }

            dst_ptr++;
        }
    }
    return dstHeightmap;
}

TileHeightmap HeightampSystematicSampling(const TileHeightmap& srcHeightmap)
{
    UInt32 srcPitch = srcHeightmap.width;
    UInt32 dstPitch = srcPitch / 2 + 1;

    TileHeightmap dstHeightmap(dstPitch, dstPitch);
    auto dst_ptr = dstHeightmap.GetPtr();
    auto src_ptr = srcHeightmap.GetPtr();

    for (size_t i = 0; i != dstPitch; i++)
    {
        for (size_t j = 0; j != dstPitch; j++)
        {
            size_t co = (i * srcPitch + j) * 2;   // center of origin
            *dst_ptr = src_ptr[co];
            dst_ptr++;
        }
    }
    return dstHeightmap;
}

bool HeightampStitching(TileHeightmap& dstHeightmap, const TileHeightmap& TL, const TileHeightmap& BL, const TileHeightmap& TR, const TileHeightmap& BR)
{
    UInt32 halfWidth = dstHeightmap.width / 2 + 1;

    static auto fill_rect = [](TileHeightmap& dst, const TileHeightmap& src, size_t offset_x, size_t offset_y) {
        auto dstPitch = dst.width;
        auto srcPitch = src.width;
        size_t dstOffset = offset_x * dstPitch + offset_y;
        for (size_t y = 0; y != src.height; y++)
        {
            memcpy(dst.GetPtr() + y * dstPitch + dstOffset, src.GetPtr() + y * srcPitch, src.width * 4U);
        }
    };

    if (TL.IsValid())
    {
        fill_rect(dstHeightmap, HeightampDownSampling(TL), 0, 0);
    }
    if (BL.IsValid())
    {
        fill_rect(dstHeightmap, HeightampDownSampling(BL), halfWidth - 1, 0);
    }
    if (TR.IsValid())
    {
        fill_rect(dstHeightmap, HeightampDownSampling(TR), 0, halfWidth - 1);
    }
    if (BR.IsValid())
    {
        fill_rect(dstHeightmap, HeightampDownSampling(BR), halfWidth - 1, halfWidth - 1);
    }

    return true;
}

bool TextureStitching(TileHeightmap& dstHeightmap, const TileHeightmap& TL, const TileHeightmap& BL, const TileHeightmap& TR, const TileHeightmap& BR)
{
    UInt32 halfWidth = dstHeightmap.width / 2 + 1;

    static auto fill_rect = [](TileHeightmap& dst, const TileHeightmap& src, size_t offset_x, size_t offset_y) {
        auto dstPitch = dst.width;
        auto srcPitch = src.width;
        size_t dstOffset = offset_x * dstPitch + offset_y;
        for (size_t y = 0; y != src.height; y++)
        {
            memcpy(dst.GetPtr() + y * dstPitch + dstOffset, src.GetPtr() + y * srcPitch, src.width * 4U);
        }
    };

    if (TL.IsValid())
    {
        fill_rect(dstHeightmap, HeightampSystematicSampling(TL), 0, 0);
    }
    if (BL.IsValid())
    {
        fill_rect(dstHeightmap, HeightampSystematicSampling(BL), halfWidth - 1, 0);
    }
    if (TR.IsValid())
    {
        fill_rect(dstHeightmap, HeightampSystematicSampling(TR), 0, halfWidth - 1);
    }
    if (BR.IsValid())
    {
        fill_rect(dstHeightmap, HeightampSystematicSampling(BR), halfWidth - 1, halfWidth - 1);
    }

    return true;
}

void GenerateTileImage(const std::string& targetPath, const std::array<std::string, 4>& imagePathList, TerrainImageType type)
{
    imageio::image image0, image1, image2, image3;
    imageio::load_png(imagePathList[0], image0);
    imageio::load_png(imagePathList[1], image1);
    imageio::load_png(imagePathList[2], image2);
    imageio::load_png(imagePathList[3], image3);

    imageio::image tempImage = ImageStitching(image0, image1, image2, image3);
    imageio::image new_image = ImageDownSampling(tempImage, type);

    imageio::save_png(targetPath, new_image);
}

void LoadTextureAssetWithMipMap(CrossSchema::TextureAssetT& texture, ImageView baseImage, const std::vector<ImageView>& mipList)
{
    constexpr UInt32 bytesPerPixel = sizeof(TerrainRGBA);

    const UInt32 textureWidth = baseImage.width;
    const UInt32 textureHeight = baseImage.height;

    UInt32 mipCount = 0;
    UInt32 dstOffsets[MaxMipNum];
    UInt32 offset = 0;

    for (UInt32 width = textureWidth, height = textureHeight; (mipCount < MaxMipNum) && (width != 1) && (height != 1); ++mipCount)
    {
        dstOffsets[mipCount] = offset;
        width = std::max(1U, textureWidth >> mipCount);
        height = std::max(1U, textureHeight >> mipCount);
        offset += width * height;
    }
    UInt32 dstDataSize = offset * bytesPerPixel;

    std::vector<UInt8> buffer(dstDataSize);
    TerrainRGBA* dstData = reinterpret_cast<TerrainRGBA*>(buffer.data());

    UInt32 mip = 0;
    {
        memcpy(dstData, baseImage.data, textureWidth * textureHeight * bytesPerPixel);
    }

    mip = 1;
    for (int i = 0; i < mipList.size() && mip < mipCount; i++, mip++)
    {
        auto width = std::max(1U, textureWidth >> mip);
        auto height = std::max(1U, textureHeight >> mip);
        const auto& imageRef = mipList[i];

        Assert(imageRef.width == width + 1);
        auto dstPtr = dstData + dstOffsets[mip];
        auto srcPtr = imageRef.data;

        UInt32 halfWidth = width / 2;

        for (int rp = 0; rp < 2; rp++)
        {
            for (UInt32 h = 0; h < height / 2; h++)
            {
                memcpy(dstPtr, srcPtr, halfWidth * bytesPerPixel);
                memcpy(dstPtr + halfWidth, srcPtr + halfWidth + 1, halfWidth * bytesPerPixel);

                dstPtr += width;
                srcPtr += imageRef.pitch;
            }
            srcPtr += imageRef.pitch;
        }
    }

    if (mip == 1 && textureWidth % 2 == 1)
    {
        UInt32 width = textureWidth / 2;
        UInt32 height = textureHeight / 2;

        UInt32 parentWidth = textureWidth;

        const TerrainRGBA* parentMipData = dstData;
        TerrainRGBA* dstPtr = dstData + dstOffsets[mip];

        // TODO(ericying) 对于 root tile 的 mip level 1 不正确
        for (UInt32 y = 0; y < height; y++)
        {
            for (UInt32 x = 0; x < width; x++)
            {
                auto p00 = parentMipData + y * 2 * parentWidth + x * 2;
                auto p01 = p00 + 1;
                auto p10 = p00 + parentWidth;
                auto p11 = p10 + 1;

                *dstPtr = *p00;
                if (!p00->isHole())
                {
                    auto n00 = p00->GetNormal();
                    auto n01 = !p01->isHole() ? p01->GetNormal() : n00;
                    auto n10 = !p10->isHole() ? p10->GetNormal() : n00;
                    auto n11 = !p11->isHole() ? p11->GetNormal() : n00;
                    auto normal = 0.25 * (n00 + n01 + n10 + n11);
                    normal.Normalize();
                    dstPtr->SetNormal(normal);
                }

                dstPtr++;
            }
        }
        mip++;
    }

    for (; mip < mipCount; mip++)
    {
        UInt32 width = std::max(1U, textureWidth >> mip);
        UInt32 height = std::max(1U, textureHeight >> mip);

        UInt32 parentMip = mip - 1;
        UInt32 parentWidth = std::max(1U, textureWidth >> parentMip);

        const TerrainRGBA* parentMipData = dstData + dstOffsets[parentMip];
        TerrainRGBA* dstPtr = dstData + dstOffsets[mip];

        for (UInt32 y = 0; y < height; y++)
        {
            for (UInt32 x = 0; x < width; x++)
            {
                auto p00 = parentMipData + y * 2 * parentWidth + x * 2;
                auto p01 = p00 + 1;
                auto p10 = p00 + parentWidth;
                auto p11 = p10 + 1;

                *dstPtr = *p00;
                if (!p00->isHole())
                {
                    auto n00 = p00->GetNormal();
                    auto n01 = !p01->isHole() ? p01->GetNormal() : n00;
                    auto n10 = !p10->isHole() ? p10->GetNormal() : n00;
                    auto n11 = !p11->isHole() ? p11->GetNormal() : n00;
                    auto normal = 0.25 * (n00 + n01 + n10 + n11);
                    normal.Normalize();
                    dstPtr->SetNormal(normal);
                }

                dstPtr++;
            }
        }
    }

    std::vector<CrossSchema::TextureAssetImage> texImages;
    for (UInt32 i = 0; i < mipCount; i++)
    {
        UInt32 width = std::max(1U, textureWidth >> i);
        UInt32 height = std::max(1U, textureHeight >> i);
        auto& image = texImages.emplace_back();
        image.mutate_width(width);
        image.mutate_height(height);
        image.mutate_databytesize(width * height * bytesPerPixel);
        image.mutate_depth(1);
        image.mutate_rowpitch(width * bytesPerPixel);
        image.mutate_dataoffset(dstOffsets[i] * bytesPerPixel);
    }

    texture.data = std::move(buffer);
    texture.images = texImages;
    texture.mipcount = static_cast<uint32_t>(texImages.size());
    texture.format = CrossSchema::TextureFormat::RGBA32;
    texture.dimension = CrossSchema::TextureDimension::Tex2D;
    texture.colorspace = CrossSchema::ColorSpace::Linear;
    texture.flags = 0;
}

void AddPhysicsCollision(editor::MeshAssetData& meshAssetData, const std::vector<Float3>& positions, const std::vector<UInt32>& indices)
{
    using namespace cross::editor;

    PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();

    std::shared_ptr<PhysicsTriangleMesh> tran = physicsEngine->GetCooker()->BuildTriangleMesh(reinterpret_cast<const UInt8*>(positions.data()),
                                                                                              static_cast<UInt32>(positions.size()),
                                                                                              static_cast<UInt16>(sizeof(Float3)),
                                                                                              reinterpret_cast<const UInt8*>(indices.data()),
                                                                                              static_cast<UInt32>(indices.size()),
                                                                                              static_cast<UInt16>(sizeof(UInt32)));
    std::vector<UInt8> serializeData = physicsEngine->GetCooker()->SerializeTriangleMesh(tran.get());
    PhysicsMeshCollisionImport coll({0.0, 0.0, 0.0}, Quaternion::Identity(), std::move(serializeData));
    meshAssetData.GetPhysicsCollision().meshCollision.push_back(std::move(coll));
}

void AddSubMeshAssetData(editor::MeshAssetData& meshAssetData, const std::vector<Float3>& positions, const std::vector<UInt32>& indices, MeshBound meshBound)
{
    using namespace cross::editor;
    // mock MeshDescription
    MeshDescription meshDesc("WGS84");
    meshDesc.SetMaterialPath("Contents/Material/DefaultMaterial.nda");
    // Init MeshAssetData

    // add mesh
    meshAssetData.AddSubMeshBegin(meshDesc);
    meshAssetData.AddIndexStream(indices);

    std::vector<CrossSchema::float3> tmpPositions;
    tmpPositions.reserve(positions.size());
    for (const auto& pos : positions)
    {
        tmpPositions.emplace_back(pos.x, pos.y, pos.z);
    }
    meshAssetData.AddVertexPosition(VertexChannel::Position0, tmpPositions);

    // MeshBound meshBound;
    if (meshBound.Min.IsInfinite() || meshBound.Max.IsInfinite())
    {
        for (const auto& pos : positions)
        {
            meshBound.Encapsulate(pos);
        }
    }
    meshAssetData.AddSubMeshEnd(meshBound);
}

bool GenerateMeshCollision(const ImageView& image, const std::string& filePath, const TileIndex& tileIndex, const TerrainInfo& terrainInfo)
{
    std::string name = PathHelper::GetBaseFileName(filePath);

    auto tileSize = terrainInfo.mTileSize;
    auto blockSize = terrainInfo.mBlockSize;
    auto gridSizeX = terrainInfo.mGridSizeX;
    auto gridSizeY = terrainInfo.mGridSizeY;
    auto gridDimX = gridSizeX * blockSize * tileSize;
    auto gridDimY = gridSizeY * blockSize * tileSize;

    constexpr size_t rate = 1 << 3;
    UInt32 newWidth = image.width / rate + 1;
    UInt32 newHeight = image.height / rate + 1;

    std::vector<Float3> positions;
    positions.reserve(newWidth * newHeight);
    // std::vector<Float3> normals;
    // normals.reserve(newWidth * newHeight);

    UInt32 offsetX = (tileIndex.mBlockX * blockSize + (tileIndex.mTileX << tileIndex.mLevel)) * tileSize;
    UInt32 offsetY = (tileIndex.mBlockY * blockSize + (tileIndex.mTileY << tileIndex.mLevel)) * tileSize;

    if (terrainInfo.mSurfaceType == TerrainSurfaceType::Flat)
    {
        UInt32 stepSize = 1 << tileIndex.mLevel;

        for (size_t i = 0; i < image.height; i += rate)
        {
            for (size_t j = 0; j < image.width; j += rate)
            {
                auto pixel = image.data + i * image.pitch + j;
                float h = pixel->GetHeight() * TerrainYScale;
                positions.emplace_back(static_cast<float>(offsetX + stepSize * j), h, static_cast<float>(offsetY + stepSize * i));
                // normals.emplace_back(pixel->GetNormal());
            }
        }
    }
    else if (terrainInfo.mSurfaceType == TerrainSurfaceType::WGS84)
    {
        double stepX = MathUtils::Math2PiD / gridDimX * (1 << tileIndex.mLevel);
        double stepY = MathUtils::MathPiD / gridDimY * (1 << tileIndex.mLevel);

        double lonOffset = MathUtils::Math2PiD * (static_cast<double>(offsetX) / gridDimX - 0.5);
        double latOffset = MathUtils::MathPiD * (static_cast<double>(offsetY) / gridDimY - 0.5);

        for (size_t i = 0; i < image.height; i += rate)
        {
            for (size_t j = 0; j < image.width; j += rate)
            {
                auto pixel = image.data + i * image.pitch + j;
                double altitude = pixel->GetHeight();
                auto wgs84_pos = WGS84CoordinateTo3D_Radian(lonOffset + stepX * j, latOffset + stepY * i, altitude * terrainInfo.mWGS84HeightScale, terrainInfo.mWGS84SemiMajor);
                positions.emplace_back(wgs84_pos);
                // normals.emplace_back(pixel->GetNormal());
            }
        }
    }
    else
    {
        Assert(false);
    }

    std::vector<UInt32> indices = GenerateHeightmapIndicesCounterclockwise(newHeight, newWidth);

    editor::ModelImportSettings modelImportSettings;
    modelImportSettings.ModelSystemUnit = editor::UNIT_M;
    editor::MeshAssetData meshAssetData;
    meshAssetData.SetName(name);

    std::vector<Float3> meshPositions = {
        positions[0],
        positions[newWidth - 1],
        positions[newWidth * (newHeight - 1)],
        positions[newWidth * newHeight - 1],
    };
    static std::vector<UInt32> meshIndices = GenerateHeightmapIndicesCounterclockwise(2, 2);

    MeshBound meshBound;
    for (auto& pos : positions)
    {
        meshBound.Encapsulate(pos);
    }

    AddSubMeshAssetData(meshAssetData, meshPositions, meshIndices, meshBound);
    AddPhysicsCollision(meshAssetData, positions, indices);

    bool success = true;
    success = meshAssetData.SerializeToFlatbufferFile(filePath, modelImportSettings.SerializeToString());

    return success;
}

TileHeightmap GenerateHeightmap(const TileIndex& tileIdx, const float* data, TerrainInfo terrainInfo)
{
    const UInt32 height = terrainInfo.mTileSize + 1, width = height;

    auto tileSize = terrainInfo.mTileSize;
    auto blockSize = terrainInfo.mBlockSize;
    auto gridSizeY = terrainInfo.mGridSizeY;
    auto gridSizeX = terrainInfo.mGridSizeX;
    auto gridDimX = gridSizeX * blockSize * tileSize;
    auto gridDimY = gridSizeY * blockSize * tileSize;

    TileHeightmap heightmap(height, width);

    // set heightmap

    std::vector<Float3> positions;
    positions.reserve(height * width);

    UInt32 offsetX = (tileIdx.mBlockX * blockSize + (tileIdx.mTileX << tileIdx.mLevel)) * tileSize;
    UInt32 offsetY = (tileIdx.mBlockY * blockSize + (tileIdx.mTileY << tileIdx.mLevel)) * tileSize;

    if (terrainInfo.mSurfaceType == TerrainSurfaceType::Flat)
    {
        UInt32 stepSize = 1 << tileIdx.mLevel;

        for (size_t i = 0; i < height; i += 1)
        {
            for (size_t j = 0; j < width; j += 1)
            {
                size_t idx = i * width + j;
                TerrainRGBA& pixel = heightmap.pixels[idx];

                float h = data[idx];
                pixel.SetHeight(h / TerrainYScale);
                float h2 = pixel.GetHeight() * TerrainYScale;;
                positions.emplace_back(static_cast<float>(offsetX + stepSize * j), h2, static_cast<float>(offsetY + stepSize * i));
            }
        }
    }
    else if (terrainInfo.mSurfaceType == TerrainSurfaceType::WGS84)
    {
        double stepX = MathUtils::Math2PiD / gridDimX * (1 << tileIdx.mLevel);
        double stepY = MathUtils::MathPiD / gridDimY * (1 << tileIdx.mLevel);

        double lonOffset = MathUtils::Math2PiD * (static_cast<double>(offsetX) / gridDimX - 0.5);
        double latOffset = MathUtils::MathPiD * (static_cast<double>(offsetY) / gridDimY - 0.5);

        for (size_t i = 0; i < height; i += 1)
        {
            for (size_t j = 0; j < width; j += 1)
            {
                size_t idx = i * width + j;
                TerrainRGBA& pixel = heightmap.pixels[idx];
                float h = data[idx];
                pixel.SetHeight(h);
                float h2 = pixel.GetHeight();
                auto wgs84_pos = WGS84CoordinateTo3D_Radian(lonOffset + stepX * j, latOffset + stepY * i, static_cast<double>(h2) * terrainInfo.mWGS84HeightScale, terrainInfo.mWGS84SemiMajor);
                positions.emplace_back(wgs84_pos);
            }
        }
    }
    else
    {
        Assert(0);
    }

    for (size_t i = 0; i < height; i++)
    {
        for (size_t j = 0; j < width; j++)
        {
            size_t idx = i * width + j;
            TerrainRGBA& pixel = heightmap.pixels[idx];
            if (!pixel.isHole())
            {
                const auto& cp = positions[idx];
                const auto tc = (i > 0) ? (positions[idx - width] - cp) : (cp - positions[idx + width]);
                const auto bc = (i < height - 1) ? (positions[idx + width] - cp) : (cp - positions[idx - width]);
                const auto lc = (j > 0) ? (positions[idx - 1] - cp) : (cp - positions[idx + 1]);
                const auto rc = (j < width - 1) ? (positions[idx + 1] - cp) : (cp - positions[idx - 1]);

                auto v0 = tc.Cross(lc);
                auto v1 = rc.Cross(tc);
                auto v2 = bc.Cross(rc);
                auto v3 = lc.Cross(bc);

                auto w0 = v0.Length();
                auto w1 = v1.Length();
                auto w2 = v2.Length();
                auto w3 = v3.Length();
                auto wt = w0 + w1 + w2 + w3;

                auto n = (w0 / wt * v0 + w1 / wt * v1 + w2 / wt * v2 + w3 / wt * v3).Normalized();

                pixel.SetNormal(n);
            }
        }
    }

    return heightmap;
}

bool Load16BitPng(std::vector<UInt16>& out, UInt32& width, UInt32& height, const std::string& filePath)
{
    unsigned err = 0;
    std::vector<UInt8> imageData;
    err = lodepng::decode(imageData, width, height, filePath, LCT_GREY, 16);
    if (err)
    {
        return false;
    }

    auto cur = imageData.data();
    auto cur16 = reinterpret_cast<UInt16*>(cur);

    // force the image data from big-endian to platform-native.
    for (size_t i = 0; i < width * height; i++, cur16++, cur += 2)
    {
        *cur16 = (cur[0] << 8U) | cur[1];
    }

    out.resize(height * width);
    memcpy(out.data(), imageData.data(), height * width * sizeof(UInt16));
    return true;
}

Float3 CalcNormal(const Float3& ct, const Float3& cb, const Float3& cl, const Float3& cr)
{
    const auto v0 = ct.Cross(cl);
    const auto v1 = cr.Cross(ct);
    const auto v2 = cb.Cross(cr);
    const auto v3 = cl.Cross(cb);

    const auto w0 = v0.Length();
    const auto w1 = v1.Length();
    const auto w2 = v2.Length();
    const auto w3 = v3.Length();

    const auto wt = w0 + w1 + w2 + w3;
    const auto n = (w0 / wt * v0 + w1 / wt * v1 + w2 / wt * v2 + w3 / wt * v3).Normalized();
    return n;
};

bool ConvertHeightmap(const std::string& inputFile, const std::string& outputFile)
{
    bool enableMultiThreading = EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeCrossEditor;
    printf("enableMultiThreading %d\n", enableMultiThreading);

    unsigned width = 0, height = 0, err;

    std::vector<UInt16> heightData;
    bool success = Load16BitPng(heightData, width, height, inputFile);
    if (!success)
    {
        return false;
    }

    TileHeightmap tileHeightmap(height, width);
    auto& pixels = tileHeightmap.pixels;

    for (size_t h = 0; h < height; h++)
    {
        for (size_t w = 0; w < width; w++)
        {
            size_t idx = h * width + w;
            auto& pixel = tileHeightmap.pixels[idx];
            pixel.r = static_cast<UInt8>(heightData[idx] & 0xFF);
            pixel.g = static_cast<UInt8>(heightData[idx] >> 8);
        }
    }

    Float3 tc = {0, 0, -1}, bc = {0, 0, 1}, lc = {-1, 0, 0}, rc = {1, 0, 0};
    for (size_t h = 0; h < height; h++)
    {
        for (UInt32 w = 0U; w != width; w++)
        {
            const auto c = h * width + w;
            if (!pixels[c].isHole())
            {
                tc.y = TerrainYScale * ((h > 0) ? (heightData[c - width] - heightData[c]) : (heightData[c + width] - heightData[c]));
                bc.y = TerrainYScale * ((h < height - 1) ? (heightData[c + width] - heightData[c]) : (heightData[c - width] - heightData[c]));
                lc.y = TerrainYScale * ((w > 0) ? (heightData[c - 1] - heightData[c]) : (heightData[c + 1] - heightData[c]));
                rc.y = TerrainYScale * ((w < width - 1) ? (heightData[c + 1] - heightData[c]) : (heightData[c - 1] - heightData[c]));
                Float3 n = CalcNormal(tc, bc, lc, rc);
                pixels[c].SetNormal(n);
            }
        }
    }

    auto data = reinterpret_cast<UInt8*>(tileHeightmap.GetPtr());
    err = lodepng::encode(outputFile, data, width, height);
    if (err)
        return false;

    return true;
}
}   // namespace cross::editor
