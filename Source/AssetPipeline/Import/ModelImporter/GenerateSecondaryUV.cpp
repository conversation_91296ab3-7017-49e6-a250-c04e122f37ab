#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "GenerateSecondaryUV.h"
#include "External/Wodka/wodka_PELoader.h"
#if CROSSENGINE_OSX  //TODO 
#else

#if UNWRAPIMPL_USE_WODKA_DLL

#ifndef UNWRAPIMPL_USE_DLL_WINAPI
#define UNWRAPIMPL_USE_DLL_WINAPI 0 && CROSSENGINE_WIN
#endif

#if UNWRAPIMPL_USE_DLL_WINAPI
static HMODULE _UnwrapDll = 0;
#else
static PEModule* _UnwrapDll = 0;
#endif

typedef bool(*_GenerateSecondaryUVSetImpl)(const float*, SInt32, const float*, const float*, const UInt32*, SInt32, float*, const UnwrapParam&, char*, UInt32);
static _GenerateSecondaryUVSetImpl GenerateSecondaryUVSetImpl;

#else // UNWRAPIMPL_USE_WODKA_DLL

#include "External/Unwrap/include/Unwrap.hpp"

#endif

extern PEKnownImport kGeometryToolboxKnownImports[];
extern int kGeometryToolboxKnownImportsCount;

namespace UnwrapImpl
{
	void LoadDll()
	{
#if UNWRAPIMPL_USE_WODKA_DLL
		if (_UnwrapDll == 0)
		{
            // TODO ... crossplatform
            char executablePath[MAX_PATH];
            ZeroMemory(executablePath, static_cast<DWORD>(cross::ArrayCount(executablePath)));
            GetModuleFileName(nullptr, executablePath, static_cast<DWORD>(cross::ArrayCount(executablePath)));
            std::string path = cross::PathHelper::Combine(
                cross::PathHelper::GetDirectoryFromAbsolutePath(executablePath).c_str(), 
                "Unwrap.dll");



#if UNWRAPIMPL_USE_DLL_WINAPI
			_UnwrapDll = LoadLibraryA(path.c_str());
#else

			PEKnownImport* gtImport = nullptr;
			unsigned gtImportCount = 0;
			_UnwrapDll = PELoadLibrary(path.c_str(), gtImport, gtImportCount);
#endif

			if (_UnwrapDll != 0)
			{
#if UNWRAPIMPL_USE_DLL_WINAPI
				GenerateSecondaryUVSetImpl = (_GenerateSecondaryUVSetImpl)GetProcAddress(_UnwrapDll, "GenerateSecondaryUVSet");
#else
				GenerateSecondaryUVSetImpl = (_GenerateSecondaryUVSetImpl)PEGetProcAddress(_UnwrapDll, "GenerateSecondaryUVSet");
#endif
			}
		}
#endif
	}

    // current problems
    // 1. Wrong DLL loading path
    // 2. 64 or 32(only 64bits)
    // 3. calling GenerateSecondaryUVSetImpl cash crash
	bool GenerateSecondaryUVSet(const float* vertex, SInt32 vertexCount,
		const float* triNormal, const float* triUV, const UInt32* triangleList, SInt32 triangleCount,
		float* outputUV, const UnwrapParam& param,
		char* errorBuffer, UInt32 bufferSize)
	{
#if UNWRAPIMPL_USE_WODKA_DLL
		PESetupFS();
		LoadDll();
		return GenerateSecondaryUVSetImpl(vertex, vertexCount, triNormal, triUV, triangleList, triangleCount, outputUV, param, errorBuffer, bufferSize);
#else
		return ::GenerateSecondaryUVSet(vertex, vertexCount, triNormal, triUV, triangleList, triangleCount, outputUV, param, errorBuffer, bufferSize);
#endif
	}
}

#endif //#if CROSSENGINE_OSX  //TODO 
