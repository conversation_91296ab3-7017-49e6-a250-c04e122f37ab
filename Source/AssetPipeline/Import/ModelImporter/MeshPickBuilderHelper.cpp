#include <vector>
#include <numeric>

    
#include "MeshPickBuilderHelper.h"
#include "MeshDescription.h"
#include "CrossBase/Log.h"

namespace cross::editor {

    constexpr SInt32 CollisionTriangleCountThreshold = 128;

    void MeshPickBuilderHelper::BuildCollisionTree(const MeshDescription& mesh, CollisionTree& collisionTree)
    {
        const UInt32 triangleNum = mesh.GetTriangleNum();
        std::vector<Float3> centroids;
        centroids.resize(triangleNum);
        std::vector<MeshDescription::VertexID> tmpIndices(triangleNum * 3);
        UInt32 wedgeIndex = 0;
        const auto& triangleInstances = mesh.GetTriangleInstances();
        UInt32 centroidIndex = 0;
        for (const auto& triangleView : triangleInstances)
        {
            Float3 centroid = Float3::Zero();
            for (UInt32 corner = 0; corner < 3; ++corner)
            {
                auto vertexInstanceID = triangleView[corner];
                auto vertexID = mesh.GetVertexInstanceVertex(vertexInstanceID);
                const auto& meshVertex = mesh.GetMeshBuildVertex(vertexID);
                centroid += meshVertex.Position;

                tmpIndices[wedgeIndex++] = vertexID;
            }
            centroids[centroidIndex++] = centroid * 0.3333333333f;
        }

        collisionTree.reserve(1000);
        CrossSchema::CollisionNodeT* rootNode = mBuilder.AllocateCollisionNode(collisionTree);
        rootNode->trianglelist.resize(triangleNum);
        std::iota(std::begin(rootNode->trianglelist), std::end(rootNode->trianglelist), 0);
        mBuilder.SplitCollisionNode(centroids, collisionTree, rootNode);
        mBuilder.CalculateNodeBound(rootNode, collisionTree, [&mesh, &tmpIndices](UInt32 index) -> TrianglePos { 
            Float3 pos1 = mesh.GetMeshBuildVertex(tmpIndices[index]).Position; 
            Float3 pos2 = mesh.GetMeshBuildVertex(tmpIndices[index + 1]).Position; 
            Float3 pos3 = mesh.GetMeshBuildVertex(tmpIndices[index + 2]).Position; 
            return std::make_tuple(pos1, pos2, pos3);
        });
    }

    }   // namespace cross::editor