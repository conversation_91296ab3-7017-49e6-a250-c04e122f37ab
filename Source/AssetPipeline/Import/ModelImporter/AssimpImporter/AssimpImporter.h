/*
#pragma once

#include <assimp/anim.h>
#include "AssetPipeline/Import/ModelImporter/ModelImporter.h"

struct aiScene;
struct aiNode;
struct aiMesh;
struct aiAnimation;

namespace cross::editor
{
    class AssimpImporter : public ModelImporter
    {
        using PersistenceFunction = TFunction<bool(std::string const&)>;
        using PersistenceFunctionContainer = std::vector<PersistenceFunction>;
        using ImportFunction = TFunction<bool(std::string const&, aiScene const&, PersistenceFunctionContainer&)>;
        using ImportFunctionContainer = std::unordered_map<ModelType, ImportFunction>;

    public:
		AssimpImporter() = default;
        ~AssimpImporter() = default;

        bool ImportAsset(const std::string& assetFilename, ModelImportSettings& settings, const std::string& ndaSavePath = "") override;
        bool CheckAssetName(const char* name) const override;

    private:
        bool ImportNodeData(aiScene const& scene, ImportNode& root, const std::string& assetFilename);
        bool ImportMeshData(aiScene const& scene, ImportMeshes& meshes);
        bool ImportSkeletonData(aiScene const& scene, ImportSkeletonDep& bones);
        bool ImportAnimationData(aiScene const& scene, ImportAnimations& animations);

		bool ImportNodeImpl(aiScene const& scene, aiNode const& aiNode, ImportNode& node);
		bool ImportNodeImpl(aiNode const& aiNode, aiMesh const& aiMesh, ImportNode const& parent, ImportNode& child, UInt32 index);
		void ConvertMatrix(const aiMatrix4x4& aiMatrix, Matrix4x4f& matrix);
		AnimBehaviour GetAnimationBehaviour(aiAnimBehaviour behaviour);
    };

}*/
