#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "FBXImportData.h"
#include "External/Unwrap/include/UnwrapParam.hpp"

namespace cross::editor
{

    FBXImportNode::FBXImportNode() :
        cameraIndex(-1),
        lightIndex(-1)
    {
        position = AssetMath::Vector3f::Zero();
        rotation = AssetMath::Quaternionf::Identity();
        scale = AssetMath::Vector3f::One();
        meshIndex = -1;
        meshTransform = AssetMath::Matrix4x4f::Identity();
        boneNodeIndex = -1;
    }

        FBXImportMaterial::FBXImportMaterial() :
        diffuse(1.0F, 1.0F, 1.0F, 1.0F),
        ambient(1.0F, 1.0F, 1.0F, 1.0F),
        hasTransparencyTexture(false)
    {

    }

    unsigned FBXImportMesh::AdviseVertexFormat() const
    {
        unsigned vertexFormat = 0;
        if (!vertices.empty()) vertexFormat |= VERTEX_FORMAT(Position, Normal, Color, TexCoord0);
        if (!normals.empty()) vertexFormat |= VERTEX_FORMAT(Normal);
        if (!colors.empty()) vertexFormat |= VERTEX_FORMAT(Color);
        if (!uvs[0].empty()) vertexFormat |= VERTEX_FORMAT(TexCoord0);
        if (!uvs[1].empty()) vertexFormat |= VERTEX_FORMAT(TexCoord1);
        if (!tangents.empty()) vertexFormat |= VERTEX_FORMAT(Tangent);
        return vertexFormat;
    }

    void FBXImportMesh::Reserve(int vertexCount, int faceCount, const FBXImportMesh* src)
    {
        if (src)
        {
            if (!src->polygons.empty()) polygons.reserve(faceCount * 3);
            if (!src->polygonSizes.empty()) polygonSizes.reserve(faceCount);
            if (!src->materials.empty()) materials.reserve(faceCount);

            if (!src->vertices.empty()) vertices.reserve(vertexCount);
            if (!src->skin.empty()) skin.reserve(vertexCount);
            if (!src->normals.empty()) normals.reserve(vertexCount);
            if (!src->tangents.empty()) tangents.reserve(vertexCount);
            if (!src->colors.empty()) colors.reserve(vertexCount);
            if (!src->uvs[0].empty()) uvs[0].reserve(vertexCount);
            if (!src->uvs[1].empty()) uvs[1].reserve(vertexCount);
        }
        else
        {
            polygons.reserve(faceCount * 3);
            polygonSizes.reserve(faceCount);
            materials.reserve(faceCount);

            vertices.reserve(vertexCount);
            skin.reserve(vertexCount);
            normals.reserve(vertexCount);
            tangents.reserve(vertexCount);
            colors.reserve(vertexCount);
            uvs[0].reserve(vertexCount);
            uvs[1].reserve(vertexCount);
        }
    }

    //FBXImportSettings::FBXImportSettings() :
    //	optimizeMesh(true),
    //	weldVertices(true),
    //	invertWinding(false),
    //	swapUVChannels(false),
    //	generateSecondaryUV(false),
    //	normalImportMode(TangentSpaceOptionsImport),
    //	tangentImportMode(TangentSpaceOptionsCalculate),
    //	normalSmoothAngle(60.0F),
    //	splitTangentsAcrossUV(true),
    //	resampleCurves(true)
    //{
    //	UnwrapParam defaultUnwrapParam;
    //	defaultUnwrapParam.Reset();
    //
    //	secondaryUVAngleDistortion = 100.0f  * defaultUnwrapParam.angleDistortionThreshold;
    //	secondaryUVAreaDistortion = 100.0f  * defaultUnwrapParam.areaDistortionThreshold;
    //	secondaryUVHardAngle = defaultUnwrapParam.hardAngle;
    //	secondaryUVPackMargin = 1024.0f * defaultUnwrapParam.packMargin;
    //}

}

