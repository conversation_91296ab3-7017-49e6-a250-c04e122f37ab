#pragma once

#include <fbxsdk.h>
#include "FBXConversion.h"
#include "FBXImportUtility.h"

namespace cross
{
	namespace editor
	{
        template<class SrcT>
        class FBXLayerData
		{
			public:
				FBXLayerData()
				{}

				~FBXLayerData()
				{}
                
                FbxMesh*                        mFbxMesh;
                FbxLayerElementTemplate<SrcT>*  mElement;
                int*                            mIndices;
                int                             mIndexCount;
                UInt32*                         mPolygonSizes;
                int                             mPolygonCount;
                int                             mVertexCount;
                const char*                     mLayerName;
                const std::string*              mMeshName;
		};
        
#define OVER_CHECK_CONTINUE(idx,min,max) if((idx) < (min) || (idx) > (max)){ continue;}

        template<class DstT,class SrcT>
        class FBXLayerConvert
        {
            public:
                FBXLayerConvert(std::vector<DstT>& dstVec)
                :mDstVec(dstVec)
                {}
                
                ~FBXLayerConvert()
                {}
                
                        
                std::vector<DstT>& mDstVec;
                
                friend FBXLayerConvert& operator <<(FBXLayerConvert& dst, FBXLayerData<SrcT>& src)
                {
                    bool refIsDirect = true;
                    if (src.mElement->GetReferenceMode() == FbxLayerElement::eDirect)
                    {
                        refIsDirect = true;
                    }
                    else if(src.mElement->GetReferenceMode() == FbxLayerElement::eIndexToDirect)
                    {
                        refIsDirect = false;
                    }
                    else
                    {
                        LOG_EDITOR_ERROR("unsupport reference mode type.\n");
                        return dst;
                    }
                    
                    
                    dst.mDstVec.resize(src.mIndexCount);
					int directArraySize = src.mElement->GetDirectArray().GetCount();
                    int indexArraySize = src.mElement->GetIndexArray().GetCount();
                    switch (src.mElement->GetMappingMode())
                    {
                        case FbxLayerElement::eByControlPoint:
						{
							if (directArraySize != src.mVertexCount)
							{
								dst.mDstVec.clear();
								ReportError("this mesh %s has invalid %s. please cleaning and triangulating %s in your 3D modeller before importing to crossengine\n",
									src.mMeshName->c_str(), src.mLayerName, src.mMeshName->c_str(), src.mLayerName);
								return dst;
							}

							for (int i = 0; i < src.mIndexCount; i++)
							{
								int idx = src.mIndices[i];
								if(!refIsDirect) OVER_CHECK_CONTINUE(idx, 0, indexArraySize);
								int readIdx = refIsDirect ? idx : src.mElement->GetIndexArray().GetAt(idx);
								OVER_CHECK_CONTINUE(readIdx, 0, directArraySize)
								dst.mDstVec[i] = FBXToBasicType(src.mElement->GetDirectArray().GetAt(readIdx));
							}
						}
                            break;
                        case FbxLayerElement::eByPolygonVertex:
						{
                            for(int i =0;i<src.mIndexCount;i++)
                            { 
								if (!refIsDirect) OVER_CHECK_CONTINUE(i, 0, indexArraySize);
								int readIdx = refIsDirect ? i : src.mElement->GetIndexArray().GetAt(i);
								OVER_CHECK_CONTINUE(readIdx, 0, directArraySize)
                                dst.mDstVec[i] = FBXToBasicType(src.mElement->GetDirectArray().GetAt(readIdx));
                            }
						}
                            break;
                        case FbxLayerElement::eByPolygon:
						{
                            int edgeIdx = 0;
                            for(int i =0 ;i< src.mPolygonCount;i++)
                            {
                                for(unsigned int j = 0;j<src.mPolygonSizes[i];j++,edgeIdx++)
                                {
									if (!refIsDirect) OVER_CHECK_CONTINUE(i, 0, indexArraySize);
									int readIdx = refIsDirect ? i : src.mElement->GetIndexArray().GetAt(i);
									OVER_CHECK_CONTINUE(readIdx, 0, directArraySize)
                                    dst.mDstVec[edgeIdx] = FBXToBasicType(src.mElement->GetDirectArray().GetAt(readIdx));
                                }
                            }
						}
                            break;
                        case FbxLayerElement::eAllSame:
						{
							DstT v;
                            int readIdx = refIsDirect?0:src.mElement->GetIndexArray().GetAt(0);
                            if(readIdx<0 || readIdx >directArraySize)
                            {
                                ReportError("mesh[%s] layername[%s]\n",src.mMeshName->c_str(), src.mLayerName);
                                return dst;
                            }
                            v = FBXToBasicType(src.mElement->GetDirectArray().GetAt(readIdx));
                            for(int i=0;i<src.mIndexCount;i++)
                            {
                                dst.mDstVec[i] = v;
                            }
						}
                            break;
                        default:

                            return dst;
                    }
					return dst;
                }
        };
	}
}
