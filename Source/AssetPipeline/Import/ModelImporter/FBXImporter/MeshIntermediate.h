#pragma once
#include <fbxsdk.h>
#include "FBXAuxiliary.h"
namespace cross
{
	namespace editor
	{
		template <typename ElementIDType>
		class MeshAttributes
		{
		private:
			std::vector<ElementIDType> mData;
		public:
			MeshAttributes()
			{}
			MeshAttributes(ElementIDType v)
			{}
			~MeshAttributes()
			{}
			void Push(ElementIDType v)
			{
				mData.push_back(v);
			}

			ElementIDType& At(int i)
			{
				if (i < 0)
				{
					assert(false);
				}
				if (i >= mData.size())
				{
					if (i == mData.size())
					{
						mData.emplace_back();
						return mData.back();
					}
					else
					{
						mData.resize(i + 1);
						return mData[i];
					}
				}

				if (i >= 0 && i < mData.size())
				{
					return mData[i];
				}

				assert(false);
				return mData[i];
			}

			size_t GetSize()
			{
				return mData.size();
			}

			void IncreaseSize(int addSize)
			{
				mData.resize(mData.size() + addSize);
			}

			ElementIDType& operator[](int i)
			{
				if (i < 0)
				{
					assert(false);
				}
				return At(i);

			}
		};

		template <typename ElementIDType>
		class MeshAttributesMap
		{
		public:
			std::map<int, std::shared_ptr<MeshAttributes<ElementIDType>>> mMap;

			void Set(int key, int valuePos,ElementIDType value)
			{
				std::map<int, std::shared_ptr<MeshAttributes<ElementIDType>>>::iterator it = mMap.find(key);
				if(it == mMap.end())//not found
				{
					std::shared_ptr<MeshAttributes<ElementIDType>> meshAttributes = std::make_shared<MeshAttributes<ElementIDType>>();
					mMap.insert(std::pair<int, std::shared_ptr<MeshAttributes<ElementIDType>>>(key, meshAttributes));
					it = mMap.find(key);
				}
				std::shared_ptr<MeshAttributes<ElementIDType>> pMeshAttributes = it->second;
				pMeshAttributes->At(valuePos) = value;
			}
		};


		class SubMeshIntermediate
		{
		public:
			MeshAttributes<fbxsdk::FbxVector4>      mVertexPosition;
			MeshAttributes<int>                     mTriangleVertexList;
			MeshAttributes<int>                     mSmoothingGroupList; //by Polygon/Triangle
			MeshAttributesMap<fbxsdk::FbxVector2>   mUVs;
			MeshAttributes<fbxsdk::FbxColor>        mColor;
			MeshAttributes<fbxsdk::FbxVector4>      mNormal;
			MeshAttributes<fbxsdk::FbxVector4>      mTangent;
			MeshAttributes<fbxsdk::FbxVector4>      mBinormal;
			std::string                             mMaterialName;

			SubMeshIntermediate(std::string& MaterialName)
			{
				mMaterialName = MaterialName;
			}

			~SubMeshIntermediate()
			{
			}

			
		};

		class MeshIntermediate
		{
		public:
			//  mMaterialName's hash   <==>  SubMeshIntermediate's share_ptr
			std::map<size_t, std::shared_ptr<SubMeshIntermediate>> mMap;

			std::shared_ptr<SubMeshIntermediate>& FindOrAddSubMesh(std::string& MaterialName)
			{
				std::hash<std::string> hash_fn;
				size_t hash = hash_fn(MaterialName);
				std::map<size_t, std::shared_ptr<SubMeshIntermediate>>::iterator it = mMap.find(hash);
				if (it == mMap.end())//not find
				{
					std::shared_ptr<SubMeshIntermediate> subMeshInter = std::make_shared<SubMeshIntermediate>(MaterialName);
					mMap.insert(std::pair<size_t, std::shared_ptr<SubMeshIntermediate>>(hash, subMeshInter));
					it = mMap.find(hash);
				}

				return it->second;
			}
		};
    }
}