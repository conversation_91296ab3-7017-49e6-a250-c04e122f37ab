#pragma once

#include "FBXImportUtility.h"
#include <map>

namespace cross::editor
{
    struct AnimationSettings
    {
        bool importBlendShapes;
        bool importAnimatedCustomProperties;
        bool importCameras;
        bool importLights;
        bool importMaterials;
        int animationOversampling;
        double sampleRate{ 30.0 };
    };

    struct AnimationTimeRange
    {
        float timeRangeStart, timeRangeEnd, timeRangeOffset;

        double bakeStart;
        double bakeStop;

        int animationOversampling;
    };

    struct ImportScene;
    struct ImportNode;
    using FBXMeshToInfoMap = FBXMeshToSharedMeshInfoMap;
    void ImportAnimationClipsName(FbxScene& scene, FBXImportScene& importScene);
    void ImportAllAnimations(FbxManager& sdkManager, FbxScene& fbxScene, FbxNode* root, FBXImportScene& unity,
        const std::map<FbxNode*, FBXImportNode*>& fbxNodeMap, const FBXMeshToInfoMap& fbxMeshToInfoMap, const AnimationSettings& animationSettings);
    void SyncCurveNodeChannelValue(FbxNode* node);
    void RemoveInvalidTransformCurves(FbxScene& fbxScene, FbxNode* root);

}