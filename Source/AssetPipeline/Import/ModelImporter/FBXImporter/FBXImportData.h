#pragma once
#include "FBXMaterial.h"
#include "AssetPipeline/Utils/AssetMath.h"
namespace cross::editor
{
    struct FBXImportNode;

    struct FBXImportTexture
    {
        std::string path;
        std::string relativePath;
        AssetMath::Vector2f offset = { 0.0f, 0.0f };
        AssetMath::Vector2f scale = { 1.0f, 1.0f };
    };

    struct FBXImportMaterial
    {
        std::string name;
        ColorRGBAf diffuse; // white (default)
        ColorRGBAf ambient; // white (default)
        FBXImportTexture texture;
        FBXImportTexture normalMap;
        bool hasTransparencyTexture;
        std::shared_ptr<FBXMaterial> fbxMaterial;
        FBXImportMaterial();
    };

    struct FBXImportBone
    {
        // The node this bone uses to deform the mesh!
        FBXImportNode*	node;
        AssetMath::Matrix4x4f		bindpose;
        UInt32			parent;
        int				used;

		FBXImportBone()
			:bindpose(AssetMath::Matrix4x4f::Identity())
		{
			parent = THandle<FBXImportBone, UInt32>::InvalidHandle(); used = 0; node = nullptr;
		}
    };

    struct FBXImportBlendShape
    {
        float targetWeight;

        // vertices, normals and tangents are stored as non-delta data, i.e. plain object-space just as mesh data
        std::vector<AssetMath::Vector3f> vertices;
        std::vector<AssetMath::Vector3f> normals;
        std::vector<AssetMath::Vector3f> tangents;
    };

    struct FBXFBXImportBlendShapeChannel
    {
        std::string name;

        // index into shapes
        int frameIndex;
        int frameCount;
    };

    enum UserDataType
    {
        UserDataBool,
        UserDataFloat,
        UserDataColor,
        UserDataInt,
        UserDataVector,
        UserDataString
    };

    struct FBXImportNodeUserData
    {
        std::string name;

        UserDataType data_type_indicator;

        //data
        bool boolData;
        AssetMath::Vector4f vectorData;
        std::string stringData;
        ColorRGBA32 colorData;
        int intData;
        float floatData;
    };

    enum TangentSpaceOptions
    {
        TangentSpaceOptionsImport,
        TangentSpaceOptionsCalculate,
        TangentSpaceOptionsNone
    };

    //struct FBXImportSettings
    //{
    //	bool optimizeMesh; // default true;
    //	bool weldVertices;// true (default)
    //	bool invertWinding;// false (default)
    //	bool swapUVChannels; // false
    //	bool generateSecondaryUV; //false
    //	bool resampleCurves;//true
    //
    //	// padding goes here due to align fail
    //	float secondaryUVAngleDistortion;
    //	float secondaryUVAreaDistortion;
    //	float secondaryUVHardAngle;
    //	float secondaryUVPackMargin;
    //
    //	// Tangent space settings
    //	TangentSpaceOptions normalImportMode;
    //	TangentSpaceOptions tangentImportMode;
    //	float normalSmoothAngle;	/// 60 (default)
    //	bool splitTangentsAcrossUV; // default false
    //
    //	FBXImportSettings();
    //};


    struct FBXImportInfo
    {
        std::string applicationName;
        std::string applicationDetailedName;
        std::string exporterInfo;
        bool hasApplicationName{ false };
        bool hasSkeleton{ false };
        bool hasEmbeddedTextures{ false };
        float fileScale{ 1.0f };
        std::string fileScaleUnit{ "" };
        float fileScaleFactor{ 1.0f };
        float sampleRate{ 0.0f };
    };
	// for some special reason, the data is used in this format, you can modify this if you're interested
	struct ImportVertexBone
	{
		std::array<float, 4> Weights;
		std::array<UInt32, 4> BoneIndices;
	};

    struct FBXImportNode
    {
        std::string name;
        AssetMath::Vector3f position;			// zero (default)
        AssetMath::Quaternionf rotation;		// identity (default)
        AssetMath::Vector3f scale;				// identity (default)
        int meshIndex;				// -1 default
        AssetMath::Matrix4x4f meshTransform;	// identity (default)
        int	cameraIndex;
        int	lightIndex;

        std::vector<FBXImportNodeUserData> userData;

        std::vector<FBXImportNode> children;

        int boneNodeIndex;

        FBXImportNode();
    };

    struct FBXImportMesh  
    {
        std::vector<AssetMath::Vector3f> vertices;
        std::vector<ImportVertexBone> skin; // per vertex skin info

        // Before mesh is split across seams, these arrays are per polygon vertex!
        // That is their size == polygons.size
        std::vector<AssetMath::Vector3f> normals;
        std::vector<AssetMath::Vector4f> tangents;
        std::vector<ColorRGBA32> colors;
        std::vector<AssetMath::Vector2f> uvs[2];
        std::vector<int> smoothingGroups;

        // Index buffer, potentially arbitrary number of indices per polygon
        std::vector<UInt32> polygons;

        // Per-polygon arrays.
        std::vector<UInt32> polygonSizes; // Size of each polygon
        std::vector<int> materials; // Material index of each polygon

        std::string name;
        std::vector<int> bones;
        std::vector<FBXImportBlendShape> shapes;
        std::vector<FBXFBXImportBlendShapeChannel> shapeChannels;
        bool hasAnyQuads;

        FBXImportMesh() : hasAnyQuads(false) { }

        // src argument is optional
        void Reserve(int vertexCount, int faceCount, const FBXImportMesh* src);
        unsigned AdviseVertexFormat() const;
    };


    struct FBXImportBaseAnimation
    {
        FBXImportNode*    node;
        /*FbxNode*/ void* pFbxNode;
    };

    enum  RotationImportType
    {
        kRotationImportQuat = 0,
        kRotationImportEuler
    };

    struct FBXImportNodeAnimation : public FBXImportBaseAnimation
    {
        AnimationCurve rotation[4];
        AnimationCurve translation[3];
        AnimationCurve scale[3];
    };
    typedef std::vector<FBXImportNodeAnimation> FBXImportNodeAnimations;

    struct FBXImportFloatAnimation : public FBXImportBaseAnimation
    {
        AnimationCurve curve;
        std::string className;
        std::string propertyName;
    };
    typedef std::vector<FBXImportFloatAnimation> FBXImportFloatAnimations;

    struct FBXFrameKey
    {
        AssetMath::Vector4f position;
        AssetMath::Quaternionf rotation;
        AssetMath::Matrix4x4f matrix;
    };
    typedef std::vector<FBXFrameKey> FBXFrame;

    struct FBXImportAnimationClip
    {
        std::string name;
        double bakeStart;
        double bakeStop;

        FBXImportNodeAnimations nodeAnimations;
        FBXImportFloatAnimations floatAnimations;
        std::vector<FBXFrame> allFrames;

        bool HasAnimations() { return !nodeAnimations.empty() || !floatAnimations.empty(); }
    };


    struct FBXAnimationTimeRange
    {
        float timeRangeStart, timeRangeEnd, timeRangeOffset;

        double bakeStart;
        double bakeStop;
    };


    // constraint

    enum ConstraintKind
    {
        ConstraintKindUndefined,
        ConstraintKindPosition,
        ConstraintKindRotation,
        ConstraintKindScale,
        ConstraintKindParent,
        ConstraintKindSingleChainIK,
        ConstraintKindAim,
        ConstraintKindCharacter,

        ConstraintKindCount
    };

    enum Axis
    {
        AxisNone = 0,
        AxisX = 1,
        AxisY = 2,
        AxisZ = 4
    };

    enum WorldUpType
    {
        WorldUpTypeSceneUp,
        WorldUpTypeObjectUp,
        WorldUpTypeObjectRotationUp,
        WorldUpTypeVector,
        WorldUpTypeNone,

        WorldUpTypeCount
    };

    enum ActivationType
    {
        ActivationNone,
        ActivationPreserveOffset,
        ActivationZeroOffset,
        UserUpdateOffset,
    };


    struct FBXImportConstraint
    {
        std::string name;
        ConstraintKind kind{ ConstraintKindUndefined };
        float weight{ 0.0f };
        const FBXImportNode* target{ nullptr };
        std::vector<const FBXImportNode*> sources;
        std::vector<float> sourceWeights;
        std::vector<AssetMath::Vector3f> sourceTranslationOffsets;
        std::vector<AssetMath::Vector3f> sourceRotationOffsets;

        AssetMath::Vector3f translation{ AssetMath::Vector3f::Zero() };
        AssetMath::Vector3f rotation{ AssetMath::Vector3f::Zero() };
        AssetMath::Vector3f scaling{ AssetMath::Vector3f::One() };

        AssetMath::Vector3f animVector{ 0.0f,0.0f,1.0f };
        AssetMath::Vector3f upVector{ 0.0f, 1.0f, 0.0f };
        AssetMath::Vector3f worldUpVector{ 0.0f, 1.0f, 0.0f };
        const FBXImportNode* upObject{ nullptr };
        WorldUpType worldUpType{ WorldUpTypeSceneUp };

        bool affectTranslationX{ true };
        bool affectTranslationY{ true };
        bool affectTranslationZ{ true };

        bool affectRotationX{ true };
        bool affectRotationY{ true };
        bool affectRotationZ{ true };

        bool affectScalingX{ true };
        bool affectScalingY{ true };
        bool affectScalingZ{ true };

        bool isActive{ true };
        bool isLocked{ false };
    };

    struct FBXImportScene
    {
        std::vector<FBXImportNode> nodes;
        std::vector<FBXImportMesh> meshes;
        std::vector<FBXImportMaterial> materials;
        FBXImportInfo sceneInfo;
        // camera
        // light

        // constraint
        std::vector<FBXImportConstraint> constraints;
    };
}
