#pragma once

#include <vector>
#include <unordered_map>

#include "CrossBase/Math/CrossMath.h"

namespace cross { namespace editor {

#define THRESH_POINT_ON_PLANE   (0.10f)         /* Thickness of plane for front/back/inside test */
#define THRESH_POINT_ON_SIDE    (0.20f)         /* Thickness of polygon side's side-plane for point-inside/outside/on side test */
#define THRESH_POINTS_ARE_SAME  (0.00002f)      /* Two points are same if within this distance */
#define THRESH_POINTS_ARE_NEAR  (0.015f)        /* Two points are near if within this distance and can be combined if imprecise math is ok */
#define THRESH_NORMALS_ARE_SAME (0.00002f)      /* Two normal points are same if within this distance */
#define THRESH_UVS_ARE_SAME     (0.0009765625f) /* Two UV are same if within this threshold (1.0f/1024f) */

    class MeshDescription;
    struct MeshBuildVertex;
    struct SkinBuildVertex;

    struct OverlappingCorners
    {
        void Add(SInt32 key, SInt32 value)
        {
            auto iter = mIndexBelongsTo.find(key);
            if (iter != mIndexBelongsTo.end())
            {
                iter->second.emplace_back(value);
            }
            else
            {
                std::vector<SInt32> container;
                container.reserve(6);
                container.emplace_back(value);
                mIndexBelongsTo.emplace(std::make_pair(key, container));
            }
        }

        const std::vector<SInt32>& FindIfOverlapping(SInt32 key) const
        {
            const auto& iter = mIndexBelongsTo.find(key);
            if (iter != mIndexBelongsTo.end())
            {
                return iter->second;
            }
            else
            {
                return mEmpty;
            }
        }

    private:
        std::unordered_map<SInt32, std::vector<SInt32>> mIndexBelongsTo;
        std::vector<SInt32> mEmpty;
    };

    struct IndexAndZ
    {
        float Z;
        // Index is actually vertexInstanceID
        SInt32 Index;
        UInt32 VertexID;
        const Float3* OriginVector;

        IndexAndZ() = default;

        IndexAndZ(SInt32 idx, const Float3& v, UInt32 id)
        {
            Z = 0.30f * v.x + 0.33f * v.y + 0.37f * v.z;
            Index = idx;
            OriginVector = &v;
            VertexID = id;
        }

        bool operator<(const IndexAndZ& other) const
        {
            return Z < other.Z;
        }
    };

    class FBXHelper
    {
    public:
        static void FindOverlappingCorners(const MeshDescription& meshDescription, float comparisonThreshold, OverlappingCorners& outputCorners);
        static bool AreVerticesEqual(MeshBuildVertex const& v1, MeshBuildVertex const& v2, float comparisonThreshold);
        static bool AreVerticesPosEqual(MeshBuildVertex const& v1, MeshBuildVertex const& v2, float comparisonThreshold);
        static void ValidateSkin(const std::vector<SkinBuildVertex>& influences, const int boneCount, const char* meshName);
        static void ValidateIdenticalWeights(const std::vector<SkinBuildVertex>& influences, const int boneCount, const char* meshName);
        static void ValidateBoneWeightRanges(const std::vector<SkinBuildVertex>& influences, const int boneCount, const char* meshName);
        static bool HasBadNTB(const MeshDescription& meshDescription);
        static bool HasNormal(const MeshDescription& meshDescription);
        static void GenerateSmoothNormals(const MeshDescription& meshDescription, const std::vector<int> smoothingInfo, std::vector<Float3>& output);

        static inline bool PointsEqual(const Float3& v1, const Float3& v2, const float comparisonThreshold)
        {
            if (MathUtils::Abs(v1.x - v2.x) > comparisonThreshold ||
                MathUtils::Abs(v1.y - v2.y) > comparisonThreshold ||
                MathUtils::Abs(v1.z - v2.z) > comparisonThreshold)
            {
                return false;
            }
            return true;
        }

        static inline bool NormalsEqual(const Float3& v1, const Float3& v2)
        {
            const float epsilon = THRESH_NORMALS_ARE_SAME;
            return MathUtils::Abs(v1.x - v2.x) <= epsilon && MathUtils::Abs(v1.y - v2.y) <= epsilon && MathUtils::Abs(v1.z - v2.z) <= epsilon;
        }

        static inline bool TangentEqual(const Float4& v1, const Float4& v2)
        {
            const float epsilon = THRESH_NORMALS_ARE_SAME;
            return MathUtils::Abs(v1.x - v2.x) <= epsilon &&
                   MathUtils::Abs(v1.y - v2.y) <= epsilon &&
                   MathUtils::Abs(v1.z - v2.z) <= epsilon &&
                   MathUtils::Abs(v1.w - v2.w) <= epsilon;
        }

        static inline bool UVsEqual(const Float2& v1, const Float2& v2)
        {
            const float epsilon = THRESH_UVS_ARE_SAME;
            return MathUtils::Abs(v1.x - v2.x) <= epsilon && MathUtils::Abs(v1.y - v2.y) <= epsilon;
        }

        static inline bool SubmeshNameIsPhysicsCollisionBox(const std::string& name)
        {
            return name.find("CEBX_") == 0 || name.find("UBX_") == 0;
        }

        static inline bool SubmeshNameIsPhysicsCollisionSphere(const std::string& name)
        {
            return name.find("CESP_") == 0 || name.find("USP_") == 0;
        }

        static inline bool SubmeshNameIsPhysicsCollisionCapsule(const std::string& name)
        {
            return name.find("CECA_") == 0 || name.find("UCP_") == 0;
        }

        static inline bool SubmeshNameIsPhysicsCollisionConvex(const std::string& name)
        {
            return name.find("CECO_") == 0 || name.find("UCX_") == 0;
        }

        static inline bool SubmeshNameIsPhysicsCollision(const std::string& name)
        {
            return SubmeshNameIsPhysicsCollisionBox(name) || SubmeshNameIsPhysicsCollisionCapsule(name) ||
                SubmeshNameIsPhysicsCollisionConvex(name) || SubmeshNameIsPhysicsCollisionSphere(name);
        }
    };

}}   // namespace cross::editor