#include "FBXImportUtility.h"
#include "AssetPipeline/Import/ModelImporter/GenerateSecondaryUV.h"

namespace cross::editor
{

    static std::string gFbxImportErrors;
    static std::string gFbxImportWarnings;

    ConnectedMesh::ConnectedMesh(int vertexCount, const UInt32* indices, int indexCount, const UInt32* faceSizes, int faceCount)
    {
        Vertex temp; temp.faces = nullptr; temp.faceCount = 0;
        vertices.resize(vertexCount, temp);

        // Count faces that use each vertex
        for (int i = 0, idx = 0; i < faceCount; ++i)
        {
            const int fs = faceSizes[i];
            for (int e = 0; e < fs; ++e)
                vertices[indices[idx + e]].faceCount++;
            idx += fs;
        }

        // Assign memory to faces (reset faceCount - so we reuse it as a counter)
        m_FaceAllocator = new UInt32[indexCount];
        UInt32* memory = m_FaceAllocator;

        for (int i = 0; i < vertexCount; ++i)
        {
            vertices[i].faces = memory;
            memory += vertices[i].faceCount;
            vertices[i].faceCount = 0;
        }

        // Assign vertex->face connections
        for (int i = 0, idx = 0; i < faceCount; ++i)
        {
            const int fs = faceSizes[i];
            for (int e = 0; e < fs; ++e)
            {
                int vertexIndex = indices[idx + e];
                vertices[vertexIndex].faces[vertices[vertexIndex].faceCount] = i;
                vertices[vertexIndex].faceCount++;
            }
            idx += fs;
        }
    }

    void AddVertexByIndex(const FBXImportMesh& srcMesh, FBXImportMesh& dstMesh, int srcVertexIndex)
    {
        dstMesh.vertices.push_back(srcMesh.vertices[srcVertexIndex]);
        for (int i = 0; i < srcMesh.shapes.size(); ++i)
        {
            dstMesh.shapes[i].vertices.push_back(srcMesh.shapes[i].vertices[srcVertexIndex]);
        }

        if (!srcMesh.skin.empty())
        {
            dstMesh.skin.push_back(srcMesh.skin[srcVertexIndex]);
        }
    }

    void AddPolygonAttribute(const FBXImportMesh& srcMesh, FBXImportMesh& dstMesh, int srcAttributeIndex)
    {
        if (!srcMesh.normals.empty())
            dstMesh.normals.push_back(srcMesh.normals[srcAttributeIndex]);
        if (!srcMesh.colors.empty())
            dstMesh.colors.push_back(srcMesh.colors[srcAttributeIndex]);
        if (!srcMesh.uvs[0].empty())
            dstMesh.uvs[0].push_back(srcMesh.uvs[0][srcAttributeIndex]);
        if (!srcMesh.uvs[1].empty())
            dstMesh.uvs[1].push_back(srcMesh.uvs[1][srcAttributeIndex]);
        if (!srcMesh.tangents.empty())
        {
            AssetMath::Vector4f tangent = srcMesh.tangents[srcAttributeIndex];
            //tangent.w = -tangent.w;
            dstMesh.tangents.push_back(tangent);
        }

        for (size_t i = 0; i < srcMesh.shapes.size(); ++i)
        {
            const FBXImportBlendShape& srcShape = srcMesh.shapes[i];
            FBXImportBlendShape& dstShape = dstMesh.shapes[i];

            if (!srcShape.normals.empty())
            {
                dstShape.normals.push_back(srcShape.normals[srcAttributeIndex]);
            }
            if (!srcShape.tangents.empty())
            {
                dstShape.tangents.push_back(srcShape.tangents[srcAttributeIndex]);
            }
        }
    }

    void ReportError(const char* format, ...)
    {
        constexpr int size = 10240;
        char buffer[size];

        va_list ap;
        va_start(ap, format);
#if CROSSENGINE_OSX
        vsnprintf(buffer, size, format, ap);
#else
        _vsnprintf_s(buffer, size, _TRUNCATE, format, ap);
#endif
        va_end(ap);

        if (gFbxImportErrors.size() == 0)
        {
            gFbxImportErrors = "ImportFBX Errors:\n";
        }

        gFbxImportErrors += buffer;
    }

    void ReportWarning(const char* format, ...)
    {
        constexpr int size = 10240;
        char buffer[size];

        va_list ap;
        va_start(ap, format);
#if CROSSENGINE_OSX
        vsnprintf(buffer, size, format, ap);
#else
        _vsnprintf_s(buffer, size, _TRUNCATE, format, ap);
#endif
        va_end(ap);

        if (gFbxImportWarnings.size() == 0)
        {
            gFbxImportWarnings = "ImportFBX Warnings:\n";
        }

        gFbxImportWarnings += buffer;
    }

    const char* GetImportError()
    {
        return gFbxImportErrors.c_str();
    }

    const char* GetImportWarnings()
    {
        return gFbxImportWarnings.c_str();
    }

#if CROSSENGINE_OSX
#else
    // Generate secondary UV set - used for lightmap
    void GenerateSecondaryUVSetImportMesh(FBXImportMesh& originalMesh, const ModelImportSettings& settings, std::vector<std::string>& warnings, std::string& error)
    {
      //TODO(wangwentao):SUpport better generator tool
    }
#endif

}
