#pragma once
#include "FBXMaterial.h"
#include "AssetPipeline/Import/TextureImporter/TextureImporter.h"
#include "Resource/AssetFileHeader.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/filesystem.h"

namespace cross {
    namespace editor
    {

        FBXMaterial::FBXMaterial()
        {
        }
        FBXMaterial::~FBXMaterial()
        {
        }

        void FBXMaterial::SetMaterialName(std::string& name)
        {
            mName.clear();
            mName.append(name.c_str());
        }

        void FBXMaterial::AddProperty(std::string name,std::vector<float>& value)
        {
            std::vector<float> vec;
            vec.reserve(value.size());
            for (auto& f : value)
            {
                vec.push_back(f);
            }
            mPropertyMap[name] = std::move(vec);
        }

        void FBXMaterial::AddProperty(std::string name, std::string value)
        {
            mPropertyMap[name] = std::move(value);
        }

        void FBXMaterial::SetMaterial(FbxSurfaceMaterial* pFbxSurfaceMaterial)
        {
            std::string tmpString;
            char * newStr = NULL;
            FbxString fbxstr = pFbxSurfaceMaterial->GetNameWithoutNameSpacePrefix();
#if defined(FBXSDK_ENV_WIN)
            FbxUTF8ToAnsi(fbxstr.Buffer(), newStr);
#else
			newStr = fbxstr.Buffer();
#endif
            assert(newStr != NULL);
            
            
            tmpString.append(newStr);
            SetMaterialName(tmpString);

            fbxsdk::FbxProperty p = pFbxSurfaceMaterial->GetFirstProperty();

            while (p.IsValid())
            {
                // save FbxProperty to Material
            
                //FbxString name = p.GetName();
//#if defined(FBXSDK_ENV_WIN)
                //FbxUTF8ToAnsi(name.Buffer(), newStr);
//#else
				//newStr = name.Buffer();
//#endif
                //assert(newStr != NULL);
               
                const char* pName = p.GetNameAsCStr();

                FbxDataType dataType = p.GetPropertyDataType();
                EFbxType type = dataType.GetType();
                switch (type)
                {
                case EFbxType::eFbxDouble:
                    AddProperty(pFbxSurfaceMaterial, pName,1.0f);
                    break;
                case EFbxType::eFbxDouble2:
                    AddProperty(pFbxSurfaceMaterial, pName, AssetMath::Vector2f(0.0f, 0.0f));
                    break;
                case EFbxType::eFbxDouble3:
                    AddProperty(pFbxSurfaceMaterial, pName, AssetMath::Vector3f(0.0f, 0.0f, 0.0f));
                    break;
                case EFbxType::eFbxDouble4:
                    AddProperty(pFbxSurfaceMaterial, pName, AssetMath::Vector4f(0.0f, 0.0f, 0.0f, 0.0f));
                    break;
                //case EFbxType::eFbxString:
                //    AddProperty(p, pName,std::string("UnKnow"));
                //    break;
                default:
                    break;
                }

                std::string tex = getTexture(p);
                std::string strpName = pName;
                if(tex.size()>0)
                { 
                    AddProperty(strpName, tex);
                }
                if (dataType.GetType() == EFbxType::eFbxDouble)
                {

                }
                /*
                EFbxType::eFbxUndefined,
                    eFbxChar,
                    eFbxUChar,
                    eFbxShort,
                    eFbxUShort,
                    eFbxUInt,
                    eFbxLongLong,
                    eFbxULongLong,
                    eFbxHalfFloat,
                    eFbxBool,
                    eFbxInt,
                    eFbxFloat,
                    eFbxDouble,
                    eFbxDouble2,
                    eFbxDouble3,
                    eFbxDouble4,
                    eFbxDouble4x4,
                    eFbxEnum = 17
                    eFbxEnumM = -17
                    eFbxString = 18
                    eFbxTime,
                    eFbxReference,
                    eFbxBlob,
                    eFbxDistance,
                    eFbxDateTime,
                    eFbxTypeCount = 24*/

                //material->AddProperty();
                p = pFbxSurfaceMaterial->GetNextProperty(p);
            }

            //AddProperty(pFbxSurfaceMaterial, FbxSurfaceMaterial::sEmissive, FbxSurfaceMaterial::sEmissiveFactor);
            //AddProperty(pFbxSurfaceMaterial, FbxSurfaceMaterial::sAmbient, FbxSurfaceMaterial::sAmbientFactor);
            //AddProperty(pFbxSurfaceMaterial, FbxSurfaceMaterial::sDiffuse, FbxSurfaceMaterial::sDiffuseFactor, Vector3f(1, 1, 1));
            //AddProperty(pFbxSurfaceMaterial, FbxSurfaceMaterial::sSpecular, FbxSurfaceMaterial::sSpecularFactor);
        }

        //return the material json path
        std::string FBXMaterial::Serialize(const std::string& meshNdaAssetPath,const std::string& materialDir)
        {
            //copy texture to CE editor project texture dir
            //std::unique_ptr<TextureImporter> importer = std::make_unique<TextureImporter>();
            //std::string texPath = PathHelper::GetEngineResourceDirectoryPath() + "\\Texture";
            resource::AssetFileHeader     h;
            int texture_count = 0;
            for (auto &kv : mPropertyMap)
            {
                if (std::holds_alternative<std::vector<float>>(kv.second))
                {
                }
                else if (std::holds_alternative<bool>(kv.second))
                {
                }
                else if (std::holds_alternative<std::string>(kv.second))
                {
                    const std::string texPath = std::get<std::string>(kv.second);
                    //should be texture path
                    if (!PathHelper::IsFileExist(texPath))
                    {
                        //error: this texture file is not exist
                    }
                    else
                    {
                        
                        std::string texFileName = PathHelper::GetBaseFileName(texPath);
                        std::string texFilePathName= materialDir + "/";
                        std::string ndaTexFileName = std::to_string(texture_count) + texFileName + ".nda";
                        texFilePathName = texFilePathName + ndaTexFileName;
                        // const std::filesystem::path fromPath = kv.second;
                        // const std::filesystem::path toPath = texFilePathName;
                        // std::filesystem::copy(fromPath, toPath);

                        std::unique_ptr<TextureImporter> importer = std::make_unique<TextureImporter>();
                        importer->ImportAsset(texPath, texFilePathName, &TextureImportSetting::gTextureImportSetting);
                        kv.second = ndaTexFileName;// texFilePathName;
                        h.AddDependency(ndaTexFileName);
                    }
                }
            }

            //create json
            std::string materialJsonPath = materialDir + "/material.mat";

            nlohmann::json s;

            using namespace CrossSchema; // safe
            auto classID = ClassID(NullType);
            classID = ClassID(Material);

            h.SetMagicNumber(ASSET_MAGIC_NUMBER);
            h.SetVersion(4);
            h.SetGUID(0x0001, 0x0002);
            h.SetClassID(classID);
            h.SetContenType((UInt32)CONTENT_TYPE::CONTENT_TYPE_JSON);

            PrepareJsonContent(s);
            std::string body = s.dump();

            h.SetDataSize((SInt32)body.size());

            
            std::string headerString;
            h.Serialize(headerString);
            
            filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
            Assert(fileSystem);
            

            std::string  fileContent;
            const size_t bufferSize   = headerString.size() + body.size();
            char*        headerOffset = fileContent.data() + headerString.size();

            
            fileContent.resize(bufferSize);
            
            std::copy(headerString.begin(), headerString.end(), fileContent.data());
            std::copy(body.data(), body.data() + body.size(), headerOffset);
            

            fileSystem->Save(materialJsonPath, fileContent.c_str(), fileContent.size());

            
            return materialJsonPath;
        }

        bool FBXMaterial::PrepareJsonContent(nlohmann::json& s)
        {
            auto jsonVector = json::array();

            for (auto &kv : mPropertyMap)
            {
                std::string name = kv.first;
                auto tmpj = json::object();
                if (std::holds_alternative<std::vector<float>>(kv.second))
                {
                    auto jv = json::array();
                    std::vector<float>& data= std::get<std::vector<float>>(kv.second);
                    for (int i = 0; i < data.size(); i++)
                    {
                        jv.push_back(data[i]);
                    }
                    tmpj[name.c_str()] = jv;
                }
                else if (std::holds_alternative<bool>(kv.second))
                {

                }
                else if (std::holds_alternative<std::string>(kv.second))
                {
                    std::string texPath = std::get<std::string>(kv.second);
                    tmpj[name.c_str()] = texPath;
                }
                jsonVector.push_back(tmpj);
            }
            s["properties"] = jsonVector;
            s["fx"] = "default_fx";
            /*
            cross::FileArchive archive{ file };
            cross::SimpleSerializer serializer{ archive };
            CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);
            flatbuffers::FlatBufferBuilder builder(4096);
            //auto mloc = CrossSchema::CreateImportMeshes(builder, meshes);
            auto mloc = CrossSchema::CreateImportMeshAssetData(builder, &meshAssetDataT);
            auto name = PathHelper::GetBaseFileName(ndaFilePath);
            auto mloc2 = CrossSchema::CreateResourceAsset(builder, &header, builder.CreateString(name), CrossSchema::ResourceType::ImportMeshAssetData, mloc.Union());
            FinishResourceAssetBuffer(builder, mloc2);

            h.SetDataSize(builder.GetSize());
            h.Serialize(file);

            archive.Write(builder.GetBufferPointer(), builder.GetSize());
            */

            return true;
        }

        std::string FBXMaterial::getTexture(const FbxProperty & property) 
        {
            int fileTextureCount = property.GetSrcObjectCount<FbxFileTexture>();
            if (0 >= fileTextureCount) return "";

            for (int j = 0; fileTextureCount > j; j++) 
            {
                auto texture = property.GetSrcObject<FbxFileTexture>(j);
                if (!texture) continue;

                //return texture->GetRelativeFileName();
                return texture->GetFileName();
            }
            return "";
        }

        /*
        Vector3f FBXMaterial::getProperty(FbxSurfaceMaterial * material,const char * pPropertyName,const char * pFactorPropertyName,Vector3f default) 
        {
            auto property = material->FindProperty(pPropertyName);
            auto factor = material->FindProperty(pFactorPropertyName);
            if (property.IsValid() && factor.IsValid()) 
            {
                FbxDouble f = factor.Get<FbxDouble>();
                FbxDouble3 _ = property.Get<FbxDouble3>();
                return Vector3f((float)_[0] * (float)f, (float)_[1] * (float)f, (float)_[2] * (float)f);
            }
            return default;
        }

        void FBXMaterial::AddProperty(FbxSurfaceMaterial * material, const char * pPropertyName, const char * pFactorPropertyName, Vector3f default)
        {
            Vector3f value = getProperty(material, pPropertyName, pFactorPropertyName, default);
            std::vector<float> vec;
            vec.push_back(value.x());
            vec.push_back(value.y());
            vec.push_back(value.z());
            std::string pName = pPropertyName;
            AddProperty(pName, vec);
        }
        */
        //--------------------------------------------------------------------------------------------------------------
        float FBXMaterial::getProperty(FbxSurfaceMaterial * material, const char * pPropertyName, float defaultValue)
        {
            auto property = material->FindProperty(pPropertyName);
            if (property.IsValid())
            {
                FbxDouble f = property.Get<FbxDouble>();
                return (float)f;
            }
            return defaultValue;
        }

        void FBXMaterial::AddProperty(FbxSurfaceMaterial * material, const char * pPropertyName, float defaultValue)
        {
            float value = getProperty(material, pPropertyName, defaultValue);
            std::vector<float> vec;
            vec.push_back(value);
            std::string pName = pPropertyName;
            AddProperty(pName, vec);
        }

        //--------------------------------------------------------------------------------------------------------------
        AssetMath::Vector2f FBXMaterial::getProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector2f defaultValue)
        {
            auto property = material->FindProperty(pPropertyName);
            if (property.IsValid())
            {
                FbxDouble2 f = property.Get<FbxDouble2>();
                return AssetMath::Vector2f((float)f[0], (float)f[1]);
            }
            return defaultValue;
        }

        void FBXMaterial::AddProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector2f defaultValue)
        {
            AssetMath::Vector2f value = getProperty(material, pPropertyName, defaultValue);
            std::vector<float> vec;
            vec.push_back(value.x);
            vec.push_back(value.y);
            std::string pName = pPropertyName;
            AddProperty(pName, vec);
        }
        //--------------------------------------------------------------------------------------------------------------
        AssetMath::Vector3f FBXMaterial::getProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector3f defaultValue)
        {
            auto property = material->FindProperty(pPropertyName);
            if (property.IsValid())
            {
                FbxDouble3 f = property.Get<FbxDouble3>();
                return AssetMath::Vector3f((float)f[0], (float)f[1], (float)f[2]);
            }
            return defaultValue;
        }

        void FBXMaterial::AddProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector3f defaultValue)
        {
            AssetMath::Vector3f value = getProperty(material, pPropertyName, defaultValue);
            std::vector<float> vec;
            vec.push_back(value.x);
            vec.push_back(value.y);
            vec.push_back(value.z);
            std::string pName = pPropertyName;
            AddProperty(pName, vec);
        }
        //--------------------------------------------------------------------------------------------------------------
        AssetMath::Vector4f FBXMaterial::getProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector4f defaultValue)
        {
            auto property = material->FindProperty(pPropertyName);
            if (property.IsValid())
            {
                FbxDouble4 f = property.Get<FbxDouble4>();
                return AssetMath::Vector4f((float)f[0], (float)f[1], (float)f[2], (float)f[3]);
            }
            return defaultValue;
        }

        void FBXMaterial::AddProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector4f defaultValue)
        {
            AssetMath::Vector4f value = getProperty(material, pPropertyName, defaultValue);
            std::vector<float> vec;
            vec.push_back(value.x);
            vec.push_back(value.y);
            vec.push_back(value.z);
            vec.push_back(value.w);
            std::string pName = pPropertyName;
            AddProperty(pName, vec);
        }

        //--------------------------------------------------------------------------------------------------------------

        void FBXMaterial::AddProperty(FbxProperty& property, const char * pPropertyName, std::string defaultValue)
        {
            FbxString value = property.Get<FbxString>();
            
            char* tempCstr =NULL;
#if defined(FBXSDK_ENV_WIN)
            FbxUTF8ToAnsi(value.Buffer(), tempCstr);
#else
			tempCstr = value.Buffer();
#endif
            std::string tempstr;
            if (tempCstr == NULL)
            { 
                tempstr = defaultValue;
            }
            else
            { 
                tempstr.clear();
                tempstr = tempCstr;
            }
            std::string pName = pPropertyName;
            AddProperty(pName, tempstr);
        }
    }
}
