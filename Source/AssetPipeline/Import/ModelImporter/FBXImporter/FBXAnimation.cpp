// TODO : include something else
#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "FBXAnimation.h"
#include "FBXConstraints.h"
#include "FBXImportUtility.h"
#include "FBXConversion.h"
#include <map>
#undef max

namespace cross::editor
{
    template<class T>
    T SafeDeltaDivide(T y, float x)
    {
        if (Abs(x) > kCurveTimeEpsilon)
            return y / x;
        else
            return Zero<T>();
    }

    template<class T>
    void RecalculateSplineSlopeT(AnimationCurveTpl<T>& curve, int key, float b = 0.0f)
    {
        AssertIf(key < 0 || key >= curve.GetKeyCount());
        if (curve.GetKeyCount() < 2)
            return;

        // First keyframe
        // in and out slope are set to be the slope from this to the right key
        if (key == 0)
        {
            float dx = curve.GetKey(1).time - curve.GetKey(0).time;
            T dy = curve.GetKey(1).value - curve.GetKey(0).value;
            T m = dy / dx;
            curve.GetKey(key).inSlope = m; curve.GetKey(key).outSlope = m;
        }
        // last keyframe
        // in and out slope are set to be the slope from this to the left key
        else if (key == curve.GetKeyCount() - 1)
        {
            float dx = curve.GetKey(key).time - curve.GetKey(key - 1).time;
            T dy = curve.GetKey(key).value - curve.GetKey(key - 1).value;
            T m = dy / dx;
            curve.GetKey(key).inSlope = m; curve.GetKey(key).outSlope = m;
        }
        // Keys are on the left and right
        // Calculates the slopes from this key to the left key and the right key.
        // Then blend between them using the bias
        // A bias of zero doesn't bend in any direction
        // a positive bias bends to the right
        else
        {
            float dx1 = curve.GetKey(key).time - curve.GetKey(key - 1).time;
            T dy1 = curve.GetKey(key).value - curve.GetKey(key - 1).value;

            float dx2 = curve.GetKey(key + 1).time - curve.GetKey(key).time;
            T dy2 = curve.GetKey(key + 1).value - curve.GetKey(key).value;

            T m1 = SafeDeltaDivide(dy1, dx1);
            T m2 = SafeDeltaDivide(dy2, dx2);

            T m = (1.0F + b) * 0.5F * m1 + (1.0F - b) * 0.5F * m2;
            curve.GetKey(key).inSlope = m; curve.GetKey(key).outSlope = m;
        }

        curve.InvalidateCache();
    }

    void EnsureQuaternionContinuity(AnimationCurve** curves)
    {
        if (!curves[0] || !curves[1] || !curves[2] || !curves[3])
            return;

        int keyCount = curves[0]->GetKeyCount();
        if (keyCount != curves[1]->GetKeyCount() || keyCount != curves[2]->GetKeyCount() || keyCount != curves[3]->GetKeyCount())
            return;

        if (keyCount == 0)
            return;

        AssetMath::Quaternionf last{ curves[0]->GetKey(keyCount - 1).value, curves[1]->GetKey(keyCount - 1).value, curves[2]->GetKey(keyCount - 1).value, curves[3]->GetKey(keyCount - 1).value };
        for (int i = 0; i < keyCount; i++)
        {
            AssetMath::Quaternionf cur{curves[0]->GetKey(i).value, curves[1]->GetKey(i).value, curves[2]->GetKey(i).value, curves[3]->GetKey(i).value};
            if (cross::Quaternion::Dot(cur, last) < 0.0F)
                cur = AssetMath::Quaternionf{-cur.x, -cur.y, -cur.z, -cur.w};
            last = cur;
            curves[0]->GetKey(i).value = cur.x;
            curves[1]->GetKey(i).value = cur.y;
            curves[2]->GetKey(i).value = cur.z;
            curves[3]->GetKey(i).value = cur.w;
        }

        for (int j = 0; j < 4; j++)
        {
            for (int i = 0; i < keyCount; i++)
                RecalculateSplineSlopeT(*curves[j], i);
        }
    }

    void ImportAnimationClipsName(FbxScene & fbx, FBXImportScene & scene)
    {
		/*
        FbxArray<FbxString*> animStacks;

        int count = fbx.GetSrcObjectCount<FbxAnimStack>();
        scene.animationClips.resize(count);
        for (int i = 0; i < count; ++i)
        {
            FbxAnimStack* animStack = fbx.GetSrcObject<FbxAnimStack>(i);
            FBXImportAnimationClip& clip = scene.animationClips[i];
            clip.name = animStack ? animStack->GetName() : "NULL";
        }*/
    }

    namespace
    {
        void ValidateExtrapolationMode(int extrapolationMode, const char* const name)
        {
            if (extrapolationMode != FbxAnimCurveBase::eConstant)
                ReportWarning("Unsupported infinity mode (%d) is used for %s. Only kClampForever (i.e. FbxAnimCurveBase::eConstant) is supported.\n", extrapolationMode, name);
        }

        void ValidateCurveInfinityMode(FbxAnimCurve* curve)
        {
            ValidateExtrapolationMode(curve->GetPreExtrapolation(), "PreExtrapolation");
            ValidateExtrapolationMode(curve->GetPostExtrapolation(), "PostExtrapolation");
        }
    }

    void SetupTimeRange(FbxScene& fbxScene, FbxAnimStack& animStack, const AnimationSettings& settings, AnimationTimeRange& output)
    {
        {
            FbxTimeSpan bakeTimeSpan = animStack.GetLocalTimeSpan();
            // This is a special workaround for a change in FBXSDK 2012.2
            // For some COLLADA files animStack->GetLocalTimeSpan() is [0 0]
            // So we get animation range from keyframes themselves using GetAnimationInterval
            if (bakeTimeSpan.GetDuration().GetSecondDouble() < std::numeric_limits<float>::epsilon())
            {
                FbxTimeSpan tempbakeTimeSpan;
                const int animLayerCount = animStack.GetSrcObjectCount<FbxAnimLayer>();
                bool isRangeValid = false;
                for (int i = 0; i < animLayerCount; ++i)
                    isRangeValid = fbxScene.GetRootNode()->GetAnimationInterval(tempbakeTimeSpan, &animStack, i);
                if (isRangeValid)
                {
                    bakeTimeSpan = tempbakeTimeSpan;
                }
            }

            output.bakeStart = bakeTimeSpan.GetStart().GetSecondDouble();
            output.bakeStop = bakeTimeSpan.GetStop().GetSecondDouble();
        }

		output.timeRangeStart = static_cast<float>(output.bakeStart);
		output.timeRangeEnd = static_cast<float>(output.bakeStop);
		output.timeRangeOffset = -output.timeRangeStart;

        output.animationOversampling = settings.animationOversampling;
    }

    namespace
    {
        float EvaluateCurve(FbxAnimCurve* curve, FbxTime fbxtime, float defaultValue, bool remap, int coordIndex, int* lastCurveIndex, bool& hasNANs)
        {
            float value = defaultValue;
            if (curve)
            {
                value = curve->Evaluate(fbxtime, lastCurveIndex);
                if (IsNAN(value))
                {
                    value = defaultValue;
                    hasNANs = true;
                }
            }

            if (remap)
                value = FBXCoordinateRemap(value, coordIndex);
            return value;
        }

        float EvaluateCurve(FbxAnimCurve* curve, float time, float defaultValue, bool remap, int coordIndex, int* lastCurveIndex, bool& hasNANs)
        {
            FbxTime fbxtime;
            fbxtime.SetSecondDouble(time);

            return EvaluateCurve(curve, fbxtime, defaultValue, remap, coordIndex, lastCurveIndex, hasNANs);
        }

        struct KeyInfo
        {
            KeyInfo() : mode((FbxAnimCurveDef::EInterpolationType) - 1), stepHelperKey(false) {}
            KeyInfo(FbxAnimCurveDef::EInterpolationType m, float v, bool helperKey) : mode(m), value(v), stepHelperKey(helperKey) {}

            FbxAnimCurveDef::EInterpolationType mode;
            float value;
            // use this to mark keys inserter as extra key for const interpolation
            bool stepHelperKey;
        };

        typedef std::vector<std::pair<float, KeyInfo> > KeyInfoVector;

        const float kStepKeyOffset = 1e-5f;
        const float kOverlappingKeyOffset = kStepKeyOffset;
        const float kOversamplingKeyOffset = 1e-4f;

        // curve should be const, but KFCurve is not implemented properly
        void GetKeyModes(FbxAnimCurve& curve, const float maxTime, KeyInfoVector& keyInfo)
        {
            keyInfo.clear();
            keyInfo.reserve(curve.KeyGetCount());

            float lastKeyTime = -1e10f;

            for (int i = 0, c = curve.KeyGetCount(); i < c; ++i)
            {
                FbxAnimCurveKey key = curve.KeyGet(i);
                const FbxAnimCurveDef::EInterpolationType mode = key.GetInterpolation();
                const float value = key.GetValue();
                float time = static_cast<float>(key.GetTime().GetSecondDouble());

                // Separating overlapping keys
                if (time < lastKeyTime + kOverlappingKeyOffset)
                    time = lastKeyTime + kOverlappingKeyOffset;

                // optimization - skip keys after max time 
                // TODO : potential optimization: skip keys before minTime (it must keep at least one key before minTime)
                if (time > maxTime)
                    break;

                keyInfo.push_back(KeyInfoVector::value_type(time, KeyInfo(mode, value, false)));
                lastKeyTime = time;

                if (FbxAnimCurveDef::eInterpolationConstant == mode && i < curve.KeyGetCount() - 1)
                {
                    // we need to insert additional key, because there is no other way to do stepped curves

                    FbxAnimCurveKey nextKey = curve.KeyGet(i + 1);
                    const float nextValue = nextKey.GetValue();

                    // we don't need extra key if values match
                    if (nextValue != value)
                    {
                        if (FbxAnimCurveDef::eConstantNext == key.GetConstantMode())
                        {
                            // insert key right after this one, because const value is taken from next one

                            // modify current key - it acts as a step helper key
                            KeyInfo& thisKeyInfo = keyInfo.back().second;
                            thisKeyInfo.stepHelperKey = true;
                            thisKeyInfo.mode = FbxAnimCurveDef::eInterpolationLinear;

                            const float time2 = time + kStepKeyOffset;
                            keyInfo.push_back(KeyInfoVector::value_type(time2, KeyInfo(FbxAnimCurveDef::eInterpolationConstant, nextValue, false)));
                        }
                        else
                        {
                            // insert helper key right before the next one
                            const float nextKeyTime = static_cast<float>(nextKey.GetTime().GetSecondDouble());
                            const float time2 = nextKeyTime - kStepKeyOffset;
                            keyInfo.push_back(KeyInfoVector::value_type(time2, KeyInfo(FbxAnimCurveDef::eInterpolationLinear, value, true)));
                        }

                        lastKeyTime = keyInfo.back().first;
                    }
                }
            }
        }

        struct KeyInfoGroup
        {
            KeyInfo keys[3];

            bool HasStepHelperKey() const
            {
                return keys[0].stepHelperKey || keys[1].stepHelperKey || keys[1].stepHelperKey;
            }

            bool HasOnlyCubicInterpolation() const
            {
                return
                    keys[0].mode == FbxAnimCurveDef::eInterpolationCubic &&
                    keys[1].mode == FbxAnimCurveDef::eInterpolationCubic &&
                    keys[2].mode == FbxAnimCurveDef::eInterpolationCubic;
            }
        };

        typedef std::map<float, KeyInfoGroup> KeyInfoMap;

        void AddKeyModes(const KeyInfoVector& keyModes, float minTime, float maxTime, int coordIndex, KeyInfoMap& timeMap, FbxAnimCurve* curve, float defaultValue, bool& hasNANs)
        {
            int firstKeyIndex = -1;

            for (UInt32 i = 0; i < keyModes.size(); ++i)
            {
                const float time = keyModes[i].first;
                const KeyInfo& keyInfo = keyModes[i].second;

                // we need key right before or on minTime
                if (time <= minTime)
                    firstKeyIndex = i;

                // skip keys which are out of range
                if (time > minTime && time <= maxTime)
                {
                    KeyInfoMap::iterator it = timeMap.find(time);
                    if (it == timeMap.end())
                    {
                        it = timeMap.insert(KeyInfoMap::value_type(time, KeyInfoGroup())).first;
                    }

                    it->second.keys[coordIndex] = keyInfo;
                }
            }

            if (firstKeyIndex >= 0)
            {
                KeyInfoGroup& firstKeyInfo = timeMap.begin()->second;

                firstKeyInfo.keys[coordIndex] = keyModes[firstKeyIndex].second;

                if (keyModes[firstKeyIndex].first != minTime)
                {
                    // doesn't remap value - remapping will be done later
                    firstKeyInfo.keys[coordIndex].value = EvaluateCurve(curve, minTime, defaultValue, false, coordIndex, NULL, hasNANs);
                }
            }
        }

        void AddKeyModesInfo(float time, KeyInfoMap& timeMap)
        {
            std::pair<KeyInfoMap::iterator, bool> result = timeMap.insert(KeyInfoMap::value_type(time, KeyInfoGroup()));
            if (result.second)
            {
                const KeyInfoMap::iterator it = result.first;

                bool remove = false;

                if (it != timeMap.begin())
                {
                    KeyInfoMap::iterator prev = it;
                    --prev;

                    if (time - prev->first <= kOversamplingKeyOffset)
                        remove = true;
                }

                if (!remove)
                {
                    KeyInfoMap::iterator next = it;
                    ++next;
                    if (next != timeMap.end())
                    {
                        if (next->first - time <= kOversamplingKeyOffset)
                            remove = true;
                    }
                }

                if (remove)
                    timeMap.erase(it);
            }
        }

        void GroupKeyModes(FbxAnimCurve** curve, float minTime, float maxTime, KeyInfoMap& timeMap, FbxVector4 defaultValue, bool& hasNANs)
        {
            timeMap.clear();
            AddKeyModesInfo(minTime, timeMap);
            AddKeyModesInfo(maxTime, timeMap);

            KeyInfoVector keyModes;

            for (int i = 0; i < 3; ++i)
            {
                if (curve[i])
                {
                    GetKeyModes(*curve[i], maxTime, keyModes);
                    AddKeyModes(keyModes, minTime, maxTime, i, timeMap, curve[i], static_cast<float>(defaultValue[i]), hasNANs);
                }
            }
        }

        void GenerateKeyModes(FbxAnimCurve** curve, float minTime, float maxTime, float sampleRate, int oversampling, KeyInfoMap& timeMap, FbxVector4 defaultValue, bool& hasNANs)
        {
            // insert key modes from keyframes		
            //GroupKeyModes(curve, minTime, maxTime, timeMap, defaultValue, hasNANs);

            // insert key modes from (over)sampling
            if (oversampling > 0)
            {
                //const float delta = sampleRate / oversampling;
                //for (float t = minTime; t < maxTime; t += delta)
                //	AddKeyModesInfo(t, timeMap);

                // TODO : switch to the code above as it much more simple
                // we are using this one, just to minimize differences between old and new code
                int firstFrame = RoundfToInt(minTime / sampleRate * oversampling);
                int lastFrame = RoundfToInt(maxTime / sampleRate * oversampling);
                lastFrame = std::max(firstFrame + 1, lastFrame);

                for (int f = firstFrame; f <= lastFrame; f++)
                {
                    float t = f * sampleRate / oversampling;
                    AddKeyModesInfo(t, timeMap);
                }
            }
        }

        void EnsureQuaternionContinuity(const AssetMath::Quaternionf& last, AssetMath::Quaternionf& current)
        {
            if (cross::Quaternion::Dot(current, last) < 0.0F)
                current = { -current.w, -current.y, -current.z, -current.x };
        }

        template<class T>
        void AddValues(AnimationCurve* outcurves, float time, const T& values, int coordinateCount, const float timeRangeOffset)
        {
            for (int j = 0; j < coordinateCount; ++j)
            {
                AnimationCurve::Keyframe key;
                key.time = time + timeRangeOffset;
                if constexpr (std::is_same<T, AssetMath::Quaternionf>::value)
                {
                    switch (j)
                    {
                    case 0:
                        key.value = values.x;
                        break;
                    case 1:
                        key.value = values.y;
                        break;
                    case 2:
                        key.value = values.z;
                        break;
                    case 3:
                        key.value = values.w;
                        break;
                    }
                }
                else
                {
                    key.value = values[j];
                }
                key.inSlope = key.outSlope = 0;
                outcurves[j].AddKeyBackFast(key);
            }
        }


        // Calculates Hermite curve coefficients
        void HermiteCooficients(double t, double& a, double& b, double& c, double& d)
        {
            double t2 = t * t;
            double t3 = t2 * t;

            a = 2.0F * t3 - 3.0F * t2 + 1.0F;
            b = t3 - 2.0F * t2 + t;
            c = t3 - t2;
            d = -2.0F * t3 + 3.0F * t2;
        }

        namespace TToArray
        {
            template <class T> float& Index(T& value, int index) { return value[index]; }
            template <class T> float  Index(const T& value, int index) { return value[index]; }

            template <> float& Index<float>(float& value, int index)
            {
                AssertIf(index != 0);
                return value;
            }
            template <> float  Index<float>(const float& value, int index)
            {
                AssertIf(index != 0);
                return value;
            }

            template <class T> int CoordinateCount();
            template <> int CoordinateCount<float>() { return 1; }
            template <> int CoordinateCount<AssetMath::Vector3f>() { return 3; }
            template <> int CoordinateCount<AssetMath::Quaternionf>() { return 4; }
        }

        template <class T>
        void FitTangents(KeyframeTpl<T>& key0, KeyframeTpl<T>& key1, float time1, float time2, const T& value1, const T& value2)
        {
            AssertIf(fabsf(time1) < std::numeric_limits<float>::epsilon());
            AssertIf(fabsf(time2) < std::numeric_limits<float>::epsilon());

            const float dt = key1.time - key0.time;

            const int coordinateCount = TToArray::CoordinateCount<T>();

            if (fabsf(dt) < std::numeric_limits<float>::epsilon())
            {
                for (int i = 0; i < coordinateCount; ++i)
                {
                    TToArray::Index(key0.outSlope, i) = 0;
                    TToArray::Index(key1.inSlope, i) = 0;
                }
            }
            else
            {
                // p0 and p1 for Hermite curve interpolation equation
                const T p0 = key0.value;
                const T p1 = key1.value;

                // Hermite coefficients at points time1 and time2
                double a1, b1, c1, d1;
                double a2, b2, c2, d2;

                // TODO : try using doubles, because it doesn't work well when p0==p1==v0==v1
                HermiteCooficients(time1, a1, b1, c1, d1);
                HermiteCooficients(time2, a2, b2, c2, d2);

                for (int i = 0; i < coordinateCount; ++i)
                {
                    // we need to solve these two equations in order to find m0 and m1
                    // b1 * m0 + c1 * m1 = v0 - a1 * p0 - d1 * p1;
                    // b2 * m0 + c2 * m1 = v1 - a2 * p0 - d2 * p1;

                    // c1, c2 is never equal 0, because time1 and time2 not equal to 0

                    // divide by c1 and c2
                    // b1 / c1 * m0 + m1 = (v0 - a1 * p0 - d1 * p1) / c1;
                    // b2 / c2 * m0 + m1 = (v1 - a2 * p0 - d2 * p1) / c2;

                    // subtract one from another
                    // b1 / c1 * m0 - b2 / c2 * m0 = (v0 - a1 * p0 - d1 * p1) / c1 - (v1 - a2 * p0 - d2 * p1) / c2;

                    // solve for m0
                    // (b1 / c1 - b2 / c2) * m0 = (v0 - a1 * p0 - d1 * p1) / c1 - (v1 - a2 * p0 - d2 * p1) / c2;

                    const double v0 = TToArray::Index(value1, i);
                    const double v1 = TToArray::Index(value2, i);
                    const double pp0 = TToArray::Index(p0, i);
                    const double pp1 = TToArray::Index(p1, i);

                    // calculate m0
                    const double m0 = ((v0 - a1 * pp0 - d1 * pp1) / c1 - (v1 - a2 * pp0 - d2 * pp1) / c2) / (b1 / c1 - b2 / c2);

                    // solve for m1 using m0
                    // c1 * m1 = p0 - a1 * p0 - d1 * p1 - b1 * m0;

                    // calculate m1
                    const double m1 = (v0 - a1 * pp0 - d1 * pp1 - b1 * m0) / c1;

                    TToArray::Index(key0.outSlope, i) = static_cast<float>(m0 / dt);
                    TToArray::Index(key1.inSlope, i) = static_cast<float>(m1 / dt);
                }
            }
        }

        // we need two points on the curve for fitting
        const float FitTime1 = 0.3f;
        const float FitTime2 = 1 - FitTime1;

        // Fits tangents (keys(keyIndex).outSlope and keys(keyIndex).inSlope) of outcurve to curve "curve"
        void FitCurve(AnimationCurve& outcurve, float value1, float value2, int keyIndex)
        {
            AnimationCurve::Keyframe& key0 = outcurve.GetKey(keyIndex);
            AnimationCurve::Keyframe& key1 = outcurve.GetKey(keyIndex + 1);

            FitTangents(key0, key1, FitTime1, FitTime2, value1, value2);
        }

        template<class T>
        void RecalculateSplineSlope(AnimationCurveTpl<T>& curve)
        {
            for (int i = 0; i < curve.GetKeyCount(); i++)
                RecalculateSplineSlopeT(curve, i);
        }

        template<class T>
        void RecalculateSplineSlopeLinear(AnimationCurveTpl<T>& curve)
        {
            if (curve.GetKeyCount() < 2)
                return;

            for (int i = 0; i < curve.GetKeyCount() - 1; i++)
            {
                RecalculateSplineSlopeLinear(curve, i);
            }
        }

        template<class T>
        void RecalculateSplineSlopeLinear(AnimationCurveTpl<T>& curve, int key)
        {
            AssertIf(key < 0 || key >= curve.GetKeyCount() - 1);
            if (curve.GetKeyCount() < 2)
                return;

            float dx = curve.GetKey(key).time - curve.GetKey(key + 1).time;
            T dy = curve.GetKey(key).value - curve.GetKey(key + 1).value;
            T m = dy / dx;
            curve.GetKey(key).outSlope = m;
            curve.GetKey(key + 1).inSlope = m;
        }

        template <class T>
        void AddKeyValues(AnimationCurve* outcurves, const KeyInfoMap& timeMap, const std::vector<T>& values, const std::vector<T>& fitValues, int coordinateCount, const float timeRangeOffset)
        {
            int keyIndex = 0;
            for (KeyInfoMap::const_iterator it = timeMap.begin(), end = timeMap.end(); it != end; ++it, ++keyIndex)
            {
                const float time = it->first;

                AddValues(outcurves, time, values[keyIndex], coordinateCount, timeRangeOffset);
            }

            // perform curve fitting
            for (int j = 0; j < coordinateCount; ++j)
            {
                KeyInfoMap::const_iterator end = timeMap.end();
                if (end != timeMap.begin())
                    --end;

                keyIndex = 0;
                for (KeyInfoMap::const_iterator it = timeMap.begin(); it != end; ++it, ++keyIndex)
                {
                    const KeyInfoGroup& keyInfo = it->second;

                    const FbxAnimCurveDef::EInterpolationType mode = keyInfo.keys[j].mode;

                    if (FbxAnimCurveDef::eInterpolationConstant == mode)
                    {
                        // we don't need to perform fitting because slopes are assigned to 0 already
                    }
                    else if (FbxAnimCurveDef::eInterpolationLinear == mode)
                    {
                        // linear curve
                        RecalculateSplineSlopeLinear(outcurves[j], keyIndex);
                    }
                    else
                    {
                        // cubic curve - fit it
                        if constexpr (std::is_same<T, AssetMath::Quaternionf>::value)
                        {
                            switch (j)
                            {
                                case 0:
                                    FitCurve(outcurves[j], fitValues[keyIndex * 2].x, fitValues[keyIndex * 2 + 1].x, keyIndex);
                                    break;
                                case 1:
                                    FitCurve(outcurves[j], fitValues[keyIndex * 2].y, fitValues[keyIndex * 2 + 1].y, keyIndex);
                                    break;
                                case 2:
                                    FitCurve(outcurves[j], fitValues[keyIndex * 2].z, fitValues[keyIndex * 2 + 1].z, keyIndex);
                                    break;
                                case 3:
                                    FitCurve(outcurves[j], fitValues[keyIndex * 2].w, fitValues[keyIndex * 2 + 1].w, keyIndex);
                                    break;

                            }

                        }
                        else
                        {
                            FitCurve(outcurves[j], fitValues[keyIndex * 2][j], fitValues[keyIndex * 2 + 1][j], keyIndex);
                        }
                    }
                }

                if (outcurves[j].GetKeyCount() > 0)
                {
                    // make first key have matching in/out slopes
                    outcurves[j].GetKey(0).inSlope = outcurves[j].GetKey(0).outSlope;

                    // make last key have matching in/out slopes
                    const int last = outcurves[j].GetKeyCount() - 1;
                    outcurves[j].GetKey(last).outSlope = outcurves[j].GetKey(last).inSlope;
                }
            }
        }

        const float kMaxTimeRange = 100000;

        bool IsValidTimeRange(const char* const takeName, const char* const nodeName, const char* const curveName, const float minimum, const float maximum)
        {
            const float timeRange = maximum - minimum;
            if (timeRange > kMaxTimeRange)
            {
                ReportError("Time range (%d) for %s curve(s) on node '%s' on take '%s' larger than maximum allowed (%d). These curves won't be imported. "
                    "Check your file - it most likely has keys in far negative or positive timeline.\n",
                    static_cast<int>(timeRange), curveName, nodeName, takeName, static_cast<int>(kMaxTimeRange));
                return false;
            }
            else
                return true;
        }
    }

    // TODO : remove
    // Old function - we use it for rotation conversion
    void ConvertCurveOld(
        // for error reporting
        const char* const takeName, const char* const nodeName, const char* const curveName,
        FbxAnimCurve** curve, AnimationCurve* outcurves, float sampleRate, FbxVector4 defaultValue, bool remap, bool rotation, const AnimationTimeRange& range, bool& hasNANs)
    {
        float minimum = std::numeric_limits<float>::infinity();
        float maximum = -std::numeric_limits<float>::infinity();

        for (int i = 0; i < 3; ++i)
        {
            if (curve[i])
            {
                if (curve[i]->KeyGetCount())
                {
                    float curMin = static_cast<float>(curve[i]->KeyGet(0).GetTime().GetSecondDouble());
                    float curMax = static_cast<float>(curve[i]->KeyGet(curve[i]->KeyGetCount() - 1).GetTime().GetSecondDouble());
                    if (curMin > range.timeRangeEnd || curMax < range.timeRangeStart)
                        continue;

                    minimum = std::min<float>(curMin, minimum);
                    minimum = std::max<float>(minimum, range.timeRangeStart);

                    maximum = std::max<float>(curMax, maximum);
                    maximum = std::min<float>(maximum, range.timeRangeEnd);
                }

                if (!rotation)
                    ValidateCurveInfinityMode(curve[i]);
            }
        }

        if (rotation)
        {
            if (curve[0])
                ValidateCurveInfinityMode(curve[0]);
        }


        if (minimum == std::numeric_limits<float>::infinity())
            return;

        if (!IsValidTimeRange(takeName, nodeName, curveName, minimum, maximum))
        {
            // Error: time range is too big
            return;
        }

        FbxTime fbxtime;
        int firstFrame = RoundfToInt(minimum / sampleRate);
        int lastFrame = RoundfToInt(maximum / sampleRate);
        lastFrame = std::max(firstFrame + 1, lastFrame); // generate at least two keys at a minimum

        // used for optimized sampling
        // TODO : use static array
        std::vector<int> lastCurveIndices(3, 0);

        for (int f = firstFrame * range.animationOversampling; f <= lastFrame * range.animationOversampling; f++)
        {
            float time = f * sampleRate / range.animationOversampling;
            fbxtime.SetSecondDouble(time);
            FbxVector4 temp;


            for (int i = 0; i < 3; i++)
            {
                if (curve[i])
                {
                    temp[i] = curve[i]->Evaluate(fbxtime, &lastCurveIndices[i]);
                    if (IsNAN(temp[i]))
                    {
                        temp[i] = defaultValue[i];
                        hasNANs = true;
                    }
                }
                else
                    temp[i] = defaultValue[i];
            }

            if (rotation)
            {
                AssetMath::Quaternionf values = ExtractQuaternionFromFBXEuler(temp);
                AddValues(outcurves, time, values, 4, range.timeRangeOffset);
            }
            else
            {
                AssetMath::Vector3f values = remap ? FBXPointToVector3Remap(temp) : FBXPointToVector3(temp);
                AddValues(outcurves, time, values, 3, range.timeRangeOffset);
            }
        }

        if (rotation)
        {
            AnimationCurve* temp[4] = { outcurves + 0, outcurves + 1, outcurves + 2, outcurves + 3 };
            EnsureQuaternionContinuity(temp);
        }
        else
        {
            // Check for step curves and adjust tangents
            for (int j = 0; j < 3; j++)
            {
                RecalculateSplineSlope(outcurves[j]);

                for (int i = 0; i < outcurves[j].GetKeyCount() - 1; i++)
                {
                    if (curve[j])
                    {
                        FbxTime fbxtime0;
                        fbxtime0.SetSecondDouble(outcurves[j].GetKey(i).time);
                        double dKeyIndex = curve[j]->KeyFind(fbxtime0);
                        int keyIndex = static_cast<int>(dKeyIndex);
                        if (keyIndex > -1 && (curve[j]->KeyGetTangentMode(keyIndex) & FbxAnimCurveDef::eTangentGenericBreak))
                        {
                            // TODO : this doesn't work with oversampling
                            RecalculateSplineSlopeLinear(outcurves[j], i);
                        }
                    }
                }
            }
        }
    }

    void ConvertCurve(
        // for error reporting
        const char* const takeName, const char* const nodeName, const char* const curveName,
        FbxAnimCurve** curve, AnimationCurve* outcurves, float sampleRate, FbxVector4 defaultValue, bool remap, bool rotation, const AnimationTimeRange& range, bool& hasNANs)
    {
        hasNANs = false;

        {
            // validating default value
            for (int i = 0; i < 3; i++)
            {
                if (IsNAN(defaultValue[i]))
                {
                    defaultValue[i] = 0;
                    hasNANs = true;
                }
            }
        }

        // HACK : we know that !remap == scale, we will remove this once we have an answer from FBX support
        if (rotation || !remap)
        {
            ConvertCurveOld(takeName, nodeName, curveName, curve, outcurves, sampleRate, defaultValue, remap, rotation, range, hasNANs);
            return;
        }

        float minimum = std::numeric_limits<float>::infinity();
        float maximum = -std::numeric_limits<float>::infinity();

        for (int i = 0; i < 3; ++i)
        {
            if (curve[i])
            {
                if (curve[i]->KeyGetCount())
                {
                    float curMin = static_cast<float>(curve[i]->KeyGet(0).GetTime().GetSecondDouble());
                    float curMax = static_cast<float>(curve[i]->KeyGet(curve[i]->KeyGetCount() - 1).GetTime().GetSecondDouble());
                    if (curMin > range.timeRangeEnd || curMax < range.timeRangeStart)
                        continue;

                    minimum = std::min<float>(curMin, minimum);
                    maximum = std::max<float>(curMax, maximum);
                }

                if (!rotation)
                    ValidateCurveInfinityMode(curve[i]);
            }
        }

        minimum = std::max<float>(minimum, range.timeRangeStart);
        maximum = std::min<float>(maximum, range.timeRangeEnd);

        if (rotation)
        {
            if (curve[0])
                ValidateCurveInfinityMode(curve[0]);
        }


        if (minimum == std::numeric_limits<float>::infinity())
            return;

        if (!IsValidTimeRange(takeName, nodeName, curveName, minimum, maximum))
        {
            // Error: time range is too big
            return;
        }

        // generate at least two keys at a minimum
        if (minimum >= maximum)
            maximum = minimum + sampleRate;

        KeyInfoMap timeMap;
        GenerateKeyModes(curve, minimum, maximum, sampleRate, range.animationOversampling, timeMap, defaultValue, hasNANs);

        // TODO : should carry over stepHelper flag too - just in case some sampling key falls in between?
        FbxAnimCurveDef::EInterpolationType lastModes[3];
        lastModes[0] = lastModes[1] = lastModes[2] = FbxAnimCurveDef::eInterpolationCubic;

        std::vector<AssetMath::Vector3f> fitValues(timeMap.size() * 2 - 2);

        // used for optimized sampling
        // TODO : use static array
        std::vector<int> lastCurveIndices(3, 0);

        int keyIndex = 0;
        for (KeyInfoMap::iterator it = timeMap.begin(), end = timeMap.end(); it != end; ++it, ++keyIndex)
        {
            const float time = it->first;
            FbxTime fbxtime;
            fbxtime.SetSecondDouble(time);

            KeyInfoGroup& keyInfo = it->second;

            bool last = false;
            float sampleTime1 = 0, sampleTime2 = 0;

            {
                float nextTime = maximum;
                KeyInfoMap::const_iterator nextIt = it;
                ++nextIt;

                if (nextIt != end)
                {
                    nextTime = nextIt->first;

                    const float dt = nextTime - time;

                    sampleTime1 = time + dt * FitTime1;
                    sampleTime2 = time + dt * FitTime2;
                }
                else
                    last = true;
            }

            for (int i = 0; i < 3; ++i)
            {
                FbxAnimCurveDef::EInterpolationType mode = keyInfo.keys[i].mode;
                // there is no linear interpolation for rotation curve, because it's in euler angles and we operate in quaternions, 
                // so we need to perform full curve fitting
                if (rotation && FbxAnimCurveDef::eInterpolationLinear == mode)
                    keyInfo.keys[i].mode = mode = FbxAnimCurveDef::eInterpolationCubic;

                if ((FbxAnimCurveDef::EInterpolationType) - 1 == mode)
                {
                    keyInfo.keys[i].value = EvaluateCurve(curve[i], fbxtime, static_cast<float>(defaultValue[i]), remap, i, &lastCurveIndices[i], hasNANs);
                    keyInfo.keys[i].mode = mode = lastModes[i];
                }
                else
                {
                    if (remap)
                        keyInfo.keys[i].value = FBXCoordinateRemap(keyInfo.keys[i].value, i);
                    lastModes[i] = mode;
                }


                if (!last)
                {
                    // we need to evaluate all values for rotation, because we need it for conversion to Quaternions
                    if (FbxAnimCurveDef::eInterpolationCubic == mode)
                    {
                        fitValues[keyIndex * 2 + 0][i] = EvaluateCurve(curve[i], sampleTime1, static_cast<float>(defaultValue[i]), remap, i, &lastCurveIndices[i], hasNANs);
                        fitValues[keyIndex * 2 + 1][i] = EvaluateCurve(curve[i], sampleTime2, static_cast<float>(defaultValue[i]), remap, i, &lastCurveIndices[i], hasNANs);
                    }
                    else if (rotation && FbxAnimCurveDef::eInterpolationConstant == mode)
                    {
                        fitValues[keyIndex * 2 + 0][i] = fitValues[keyIndex * 2 + 1][i] = keyInfo.keys[i].value;
                    }
                    else
                    {
                        // rotation should have its' fitValues assigned
                        AssertIf(rotation);
                    }
                }
            }
        }

        if (rotation)
        {
            std::vector<AssetMath::Quaternionf> quatValues(timeMap.size());
            std::vector<AssetMath::Quaternionf> quatFitValues(fitValues.size());

            AssetMath::Quaternionf last;

            keyIndex = 0;
            for (KeyInfoMap::const_iterator it = timeMap.begin(), end = timeMap.end(); it != end; ++it, ++keyIndex)
            {
                const KeyInfoGroup& keyInfo = it->second;

                AssetMath::Vector3f value{ keyInfo.keys[0].value, keyInfo.keys[1].value, keyInfo.keys[2].value };
                quatValues[keyIndex] = ExtractQuaternionFromFBXEuler(Vector3ToFBXPoint(value));

                if (keyIndex > 0)
                    EnsureQuaternionContinuity(last, quatValues[keyIndex]);

                if (keyIndex < static_cast<int>(timeMap.size()) - 1)
                {
                    const int i1 = keyIndex * 2 + 0;
                    const int i2 = keyIndex * 2 + 1;

                    AssetMath::Vector3f fitValue1 = fitValues[i1];
                    AssetMath::Vector3f fitValue2 = fitValues[i2];

                    //if (keyInfo.HasStepHelperKey())
                    //{
                    //    KeyInfoMap::const_iterator nextIt = it;
                    //    ++nextIt;

                    //    const KeyInfoGroup& nextKeyInfo = nextIt->second;
                    //    Vector3f nextValue{ nextKeyInfo.keys[0].value, nextKeyInfo.keys[1].value, nextKeyInfo.keys[2].value };

                    //    for (int i = 0; i < 3; ++i)
                    //    {
                    //        if (keyInfo.keys[i].stepHelperKey)
                    //        {
                    //            // we supply value and nextValue in opposite order because factor = 1 - time
                    //            // TODO : it might be weird results if one is ~350 and other ~10
                    //            // Should use repeat or something
                    //            quatFitValues[i1][i] = Lerp(nextValue[i], value[i], FitTime1);
                    //            quatFitValues[i2][i] = Lerp(nextValue[i], value[i], FitTime2);
                    //        }
                    //    }
                    //}

                    quatFitValues[i1] = ExtractQuaternionFromFBXEuler(Vector3ToFBXPoint(fitValue1));
                    quatFitValues[i2] = ExtractQuaternionFromFBXEuler(Vector3ToFBXPoint(fitValue2));

                    EnsureQuaternionContinuity(quatValues[keyIndex], quatFitValues[i1]);
                    EnsureQuaternionContinuity(quatFitValues[i1], quatFitValues[i2]);

                    last = quatFitValues[i2];
                }
            }

            AddKeyValues(outcurves, timeMap, quatValues, quatFitValues, 4, range.timeRangeOffset);
        }
        else
        {
            std::vector<AssetMath::Vector3f> values(timeMap.size());

            float lastTime1 = 0;
            float lastTime2 = 0;

            keyIndex = 0;
            for (KeyInfoMap::const_iterator it = timeMap.begin(), end = timeMap.end(); it != end; ++it, ++keyIndex)
            {
                const KeyInfoGroup& keyInfo = it->second;

                values[keyIndex] = AssetMath::Vector3f{ keyInfo.keys[0].value, keyInfo.keys[1].value, keyInfo.keys[2].value };

                lastTime2 = lastTime1;
                lastTime1 = it->first;
            }

            AddKeyValues(outcurves, timeMap, values, fitValues, 3, range.timeRangeOffset);
        }
    }

    void ConvertCurveVec3(
        // for error reporting
        const char* const takeName, const char* const nodeName, const char* const curveName,
        FbxAnimCurve** curve, AnimationCurve* outcurves, float sampleRate, FbxVector4 defaultValue, bool remap, const AnimationTimeRange& range, bool& hasNANs)
    {
        ConvertCurve(takeName, nodeName, curveName, curve, outcurves, sampleRate, defaultValue, remap, false, range, hasNANs);
    }

    void ConvertCurveRotation(
        // for error reporting
        const char* const takeName, const char* const nodeName, const char* const curveName,
        FbxAnimCurve** curve, AnimationCurve* outcurves, float sampleRate, FbxVector4 defaultValue, const AnimationTimeRange& range, bool& hasNANs)
    {
        ConvertCurve(takeName, nodeName, curveName, curve, outcurves, sampleRate, defaultValue, false, true, range, hasNANs);
    }

    void ConvertCurveFloat(
        // for error reporting
        const char* const takeName, const char* const nodeName, const char* const curveName,
        FbxAnimCurve& curve, AnimationCurve& outcurve, float sampleRate, double defaultValue, const AnimationTimeRange& range, bool& hasNANs)
    {
        // HACK : remove this ultimate hack - faking Vector3 curve in order to be able to use the same ConvertCurveVec3 code...
        FbxAnimCurve* fbxCurves[] = { &curve, &curve, &curve };
        AnimationCurve curves[3];

        ConvertCurveVec3(takeName, nodeName, curveName, fbxCurves, curves, sampleRate, FbxVector4(defaultValue, defaultValue, defaultValue), false, range, hasNANs);
        outcurve = curves[0];
    }

    bool HasAnyCurveValues(const FbxAnimCurve* curve)
    {
        return curve && curve->KeyGetCount() != 0;
    }

    bool HasAnyCurveValues(FbxAnimCurve** curves)
    {
        for (int i = 0; i < 3; i++)
        {
            if (HasAnyCurveValues(curves[i]))
                return true;
        }
        return false;
    }

    namespace
    {

        void CollectCurvesFromAllShapeChannels(FBXImportNode& importNode, FbxMesh& fbxMesh, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const AnimationTimeRange& range, const float sampleRate, bool& hasNANs)
        {
            // collect curves from all channels
            const int blendShapeDeformerCount = fbxMesh.GetDeformerCount(FbxDeformer::eBlendShape);
            for (int i = 0; i < blendShapeDeformerCount; ++i)
            {
                FbxBlendShape* blendShapeDeformer = (FbxBlendShape*)fbxMesh.GetDeformer(i, FbxDeformer::eBlendShape);

                const int blendShapeChannelCount = blendShapeDeformer->GetBlendShapeChannelCount();
                for (int j = 0; j < blendShapeChannelCount; ++j)
                {
                    FbxBlendShapeChannel* blendShapeChannel = blendShapeDeformer->GetBlendShapeChannel(j);

                    FbxAnimCurve* curve = fbxMesh.GetShapeChannel(i, j, &animLayer);
                    if (HasAnyCurveValues(curve))
                    {
                        clip.floatAnimations.push_back(FBXImportFloatAnimation());
                        FBXImportFloatAnimation& animation = clip.floatAnimations.back();
                        animation.node = &importNode;
                        animation.className = "SkinnedMeshRenderer";
                        animation.propertyName = GenerateBlendShapeAnimationAttribute(blendShapeChannel);

                        bool curveHasNANs = false;
                        ConvertCurveFloat(clip.name.c_str(), importNode.name.c_str(), "shape", *curve, animation.curve, sampleRate, 0, range, curveHasNANs);
                        hasNANs = hasNANs || curveHasNANs;
                    }
                }
            }
        }

        void ImportShapesAnimation(FbxNode* node, FBXImportNode& importNode, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const AnimationTimeRange& range, const float sampleRate, bool& hasNANs, const FBXMeshToInfoMap& fbxMeshToInfoMap)
        {
            FbxMesh* fbxMesh = node->GetMesh();
            if (fbxMesh)
            {
                ////@TODO: Remove if there are no channels...
                CollectCurvesFromAllShapeChannels(importNode, *fbxMesh, animLayer, clip, range, sampleRate, hasNANs);
            }
        }

        template <class ConverterData, class Converter>
        void ConvertCurves(AnimationCurve& curve, Converter converter, ConverterData& data)
        {
            for (int i = 0, size = curve.GetKeyCount(); i < size; ++i)
            {
                AnimationCurve::Keyframe& key = curve.GetKey(i);
                float valueIn = key.value + key.inSlope;
                float value = key.value;
                float valueOut = key.value + key.outSlope;

                valueIn = converter(data, valueIn);
                value = converter(data, value);
                valueOut = converter(data, valueOut);

                key.inSlope = valueIn - value;
                key.value = value;
                key.outSlope = valueOut - value;
            }
        }
        /*
        FBXImportFloatAnimation* ConvertFloatAnimation(FbxAnimCurve& fbxCurve, const double defaultValue, const char* className, const char* propertyName,
            FBXImportNode*& importNode, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const AnimationTimeRange& range, const float sampleRate, bool& hasNANs)
        {
            if (!HasAnyCurveValues(&fbxCurve))
                return NULL;
            else
            {
                clip.floatAnimations.push_back(FBXImportFloatAnimation());
                FBXImportFloatAnimation& animation = clip.floatAnimations.back();
                animation.node = importNode;
                animation.className = className;
                animation.propertyName = propertyName;

                ConvertCurveFloat(clip.name.c_str(), animation.node->name.c_str(), propertyName, fbxCurve, animation.curve, sampleRate, defaultValue, range, hasNANs);

                return &animation;
            }
        }

        FBXImportFloatAnimation* ConvertFloatAnimation(FbxPropertyT<FbxDouble>& property, const char* className, const char* propertyName,
            FBXImportNode*& importNode, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const AnimationTimeRange& range, const float sampleRate, bool& hasNANs)
        {
            FbxAnimCurve* fbxCurve = property.GetCurve(&animLayer);
            const double defaultValue = property.Get();

            return ConvertFloatAnimation(*fbxCurve, defaultValue, className, propertyName,
                importNode, animLayer, clip, range, sampleRate, hasNANs);
        }

        FBXImportFloatAnimation* ConvertFloatAnimation(FbxPropertyT<FbxDouble3>& property, const int valueId, const char* curveId, const char* className, const char* propertyName,
            FBXImportNode*& importNode, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const AnimationTimeRange& range, const float sampleRate, bool& hasNANs)
        {
            FbxAnimCurve* fbxCurve = property.GetCurve(&animLayer, curveId);
            const double defaultValue = property.Get()[valueId];

            return ConvertFloatAnimation(*fbxCurve, defaultValue, className, propertyName,
                importNode, animLayer, clip, range, sampleRate, hasNANs);
        }
        */
        void ImportCameraAnimations(FbxNode* fbxNode, FBXImportNode*& importNode, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const AnimationTimeRange& range, const float sampleRate, bool& hasNANs)
        {
            assert(0 && "no camera animation supported! ");
            /*	Assert(fbxNode->GetNodeAttribute()->GetAttributeType() == FbxNodeAttribute::eCamera);
                FbxCamera& fbxCamera = *(FbxCamera*)fbxNode->GetNodeAttribute();

                FbxPropertyT<FbxDouble>* property;
                FOVConversionFunction* converter;
                if (GetFieldOfViewProperty(fbxCamera, property, converter))
                {
                    FBXImportFloatAnimation* animation = ConvertFloatAnimation(*property, "Camera", "field of view", importNode, animLayer, clip, range, sampleRate, hasNANs);

                    // XSI will never set ApertureMode to eFOCAL_LENGTH even when Focal Length is animated, but
                    // Field Of View is not, so we just check for Focal Length animation and use that instead if it's available
                    if (!animation)
                    {
                        GetFocalLengthProperty(fbxCamera, property, converter);
                        animation = ConvertFloatAnimation(*property, "Camera", "field of view", importNode, animLayer, clip, range, sampleRate, hasNANs);
                    }

                    if (animation && converter)
                        ConvertCurves(animation->curve, converter, fbxCamera);
                }
                */
        }

        void ImportLightAnimations(FbxNode* fbxNode, FBXImportNode*& importNode, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const AnimationTimeRange& range, const float sampleRate, bool& hasNANs)
        {
            assert(0 && "no light animation supported!");
            //Assert(fbxNode->GetNodeAttribute()->GetAttributeType() == FbxNodeAttribute::eLight);
            //FbxLight& fbxLight = *(FbxLight*)fbxNode->GetNodeAttribute();

            //ConvertFloatAnimation(fbxLight.Color, 0, FBXSDK_CURVENODE_COLOR_RED, "Light", "m_Color.r", importNode, animLayer, clip, range, sampleRate, hasNANs);
            //ConvertFloatAnimation(fbxLight.Color, 1, FBXSDK_CURVENODE_COLOR_GREEN, "Light", "m_Color.g", importNode, animLayer, clip, range, sampleRate, hasNANs);
            //ConvertFloatAnimation(fbxLight.Color, 2, FBXSDK_CURVENODE_COLOR_BLUE, "Light", "m_Color.b", importNode, animLayer, clip, range, sampleRate, hasNANs);

            //ConvertFloatAnimation(fbxLight.OuterAngle, "Light", "m_SpotAngle", importNode, animLayer, clip, range, sampleRate, hasNANs);

            //FBXImportFloatAnimation* animation = ConvertFloatAnimation(fbxLight.Intensity, "Light", "m_Intensity", importNode, animLayer, clip, range, sampleRate, hasNANs);

            //if (animation)
            //	ConvertCurves(animation->curve, ScaleLightIntensity, fbxLight);
        }
    }

    struct NANAnimationInfo
    {
        NANAnimationInfo() : hasNANsPos(false), hasNANsRot(false), hasNANsScale(false), hasNANsBlendShape(false), hasNANsCamera(false), hasNANsLight(false) {}

        bool HasNAN() const { return hasNANsPos || hasNANsRot || hasNANsScale || hasNANsBlendShape || hasNANsCamera || hasNANsLight; }
        void Accumulate(const NANAnimationInfo& other)
        {
            hasNANsPos = hasNANsPos || other.hasNANsPos;
            hasNANsRot = hasNANsRot || other.hasNANsRot;
            hasNANsScale = hasNANsScale || other.hasNANsScale;
            hasNANsBlendShape = hasNANsBlendShape || other.hasNANsBlendShape;
            hasNANsCamera = hasNANsCamera || other.hasNANsCamera;
            hasNANsLight = hasNANsLight || other.hasNANsLight;
        }

        std::string GetHelperString() const
        {
            const std::string infos[] = {
                !hasNANsPos ? "" : "P - position",
                !hasNANsRot ? "" : "R - rotation",
                !hasNANsScale ? "" : "S - scale",
                !hasNANsBlendShape ? "" : "B - BlendShape curves",
                !hasNANsCamera ? "" : "C - Camera curves",
                !hasNANsLight ? "" : "L - Light curves",
            };

            std::ostringstream str;
            for (int i = 0; i < 5; ++i)
                if (!infos[i].empty())
                {
                    if (str.tellp() > 0)
                        str << ", ";
                    str << infos[i];
                }

            return str.str();
        }

        std::string GetString() const
        {
            std::ostringstream str;
            if (hasNANsPos) str << "P";
            if (hasNANsRot) str << "R";
            if (hasNANsScale) str << "S";
            if (hasNANsBlendShape) str << "B";
            if (hasNANsCamera) str << "C";
            if (hasNANsLight) str << "L";

            return str.str();
        }

        std::string nodeName;
        bool hasNANsPos, hasNANsRot, hasNANsScale, hasNANsBlendShape, hasNANsCamera, hasNANsLight;
    };

    using NANAnimationInfos = std::vector<NANAnimationInfo>;
    //typedef std::vector<NANAnimationInfo> NANAnimationInfos;

    // TODO : get rid of fbxScene and pass sampleRate instead
    static void ImportAnimationTake(FbxScene& fbxScene, FbxNode* node, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const std::map<FbxNode*, FBXImportNode*>& fbxNodeMap, const FBXMeshToInfoMap& fbxMeshToInfoMap, const AnimationTimeRange& range, NANAnimationInfos& nanAnimationInfos, const bool importBlendShapes)
    {
        if ( node->GetNodeAttribute() == nullptr || node->GetNodeAttribute()->GetAttributeType() == FbxNodeAttribute::eCamera ||
            node->GetNodeAttribute()->GetAttributeType() == FbxNodeAttribute::eLight)
            return;
        const float sampleRate = 1.0F / 30.0f;// static_cast<float>(FbxTime::GetFrameRate(fbxScene.GetGlobalSettings().GetTimeMode()));

        FbxAnimCurve* translation[3] =
        {
            node->LclTranslation.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_X),
            node->LclTranslation.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_Y),
            node->LclTranslation.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_Z)
        };
        FbxAnimCurve* scale[3] =
        {
            node->LclScaling.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_X),
            node->LclScaling.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_Y),
            node->LclScaling.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_Z)
        };
        FbxAnimCurve* rotation[3] =
        {
            node->LclRotation.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_X),
            node->LclRotation.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_Y),
            node->LclRotation.GetCurve(&animLayer, FBXSDK_CURVENODE_COMPONENT_Z)
        };


        std::map<FbxNode*, FBXImportNode*>::const_iterator it = fbxNodeMap.find(node);
        if (it == fbxNodeMap.end())
        {
            ReportError("Internal error: node for animation not found!");
            return;
        }

        FBXImportNode* importNode = it->second;
        NANAnimationInfo nanInfo;

        if (HasAnyCurveValues(translation) || HasAnyCurveValues(scale) || HasAnyCurveValues(rotation))
        {
            if (importNode->boneNodeIndex == -1)
            {
                return;
                //auto keycount = rotation[0]->KeyGetCount();
                //keycount = scale[0]->KeyGetCount();
                //keycount = translation[0]->KeyGetCount();
                //animation.node = importNode;
                //auto attr = it->first->GetNodeAttribute();
                //if (attr != nullptr)
                //{
                //    auto type = attr->GetAttributeType();
                //    if (type == FbxNodeAttribute::eSkeleton)
                //    {
                //        animation.node = importNode;
                //    }
                //}
            }

            clip.nodeAnimations.push_back(FBXImportNodeAnimation());
            FBXImportNodeAnimation& animation = clip.nodeAnimations.back();

            animation.node = importNode;
            animation.pFbxNode = node;

            ConvertCurveVec3(clip.name.c_str(), animation.node->name.c_str(), "translation", translation, animation.translation, sampleRate, node->LclTranslation.Get(), true, range, nanInfo.hasNANsPos);
            ConvertCurveVec3(clip.name.c_str(), animation.node->name.c_str(), "scale", scale, animation.scale, sampleRate, node->LclScaling.Get(), false, range, nanInfo.hasNANsScale);
            ConvertCurveRotation(clip.name.c_str(), animation.node->name.c_str(), "rotation", rotation, animation.rotation, sampleRate, node->LclRotation.Get(), range, nanInfo.hasNANsRot);
        }

        if (importBlendShapes)
            ImportShapesAnimation(node, *importNode, animLayer, clip, range, sampleRate, nanInfo.hasNANsBlendShape, fbxMeshToInfoMap);

        if (importNode->cameraIndex >= 0)
            ImportCameraAnimations(node, importNode, animLayer, clip, range, sampleRate, nanInfo.hasNANsCamera);

        if (importNode->lightIndex >= 0)
            ImportLightAnimations(node, importNode, animLayer, clip, range, sampleRate, nanInfo.hasNANsLight);

        if (nanInfo.HasNAN())
        {
            Assert(importNode);
            nanInfo.nodeName = importNode->name;
            nanAnimationInfos.push_back(nanInfo);
        }
    }

    static void RecursiveImportAnimation(FbxScene& fbxScene, FbxNode* node, FbxAnimLayer& animLayer, FBXImportAnimationClip& clip, const std::map<FbxNode*, FBXImportNode*>& fbxNodeMap, const FBXMeshToInfoMap& fbxMeshToInfoMap, const AnimationTimeRange& range, NANAnimationInfos& nanAnimationInfos, const bool importBlendShapes)
    {
        //const char* nodeName = node->GetName();

        ImportAnimationTake(fbxScene, node, animLayer, clip, fbxNodeMap, fbxMeshToInfoMap, range, nanAnimationInfos, importBlendShapes);

        int count = node->GetChildCount();
        for (int i = 0; i < count; i++)
        {
            FbxNode* curChild = node->GetChild(i);
            RecursiveImportAnimation(fbxScene, curChild, animLayer, clip, fbxNodeMap, fbxMeshToInfoMap, range, nanAnimationInfos, importBlendShapes);
        }
    }

    void ImportAllAnimations(FbxManager& sdkManager, FbxScene& fbxScene, FbxNode* root, FBXImportScene& scene, const std::map<FbxNode*, FBXImportNode*>& fbxNodeMap, const FBXMeshToInfoMap& fbxMeshToInfoMap, const AnimationSettings& animationSettings)
    {
        // The root node never has any animation, so only process it's children.
        int childCount = root->GetChildCount();
		childCount++;
		childCount--;
        int i = 0;
        int animStackCount = fbxScene.GetSrcObjectCount<FbxAnimStack>();
        for (int c = animStackCount; i < c; ++i)
        {
            FbxAnimStack* animStack = fbxScene.GetSrcObject<FbxAnimStack>(i);
            const std::string takeName = animStack->GetName();

            AnimationTimeRange range;

            {
                SetupTimeRange(fbxScene, *animStack, animationSettings, range);
                double start = animStack->LocalStart.Get().GetSecondDouble();
                double end = animStack->LocalStop.Get().GetSecondDouble();
                float timeRange = (float)(end - start);
                /*float*/ timeRange = range.timeRangeEnd - range.timeRangeStart;
                bool usesInfinity = range.timeRangeStart == -std::numeric_limits<float>::infinity() && range.timeRangeEnd == std::numeric_limits<float>::infinity();
                if (timeRange > kMaxTimeRange && !usesInfinity)
                {
                    ReportError("Time range (%d) for take '%s' is larger than maximum allowed (%d). "
                        "Check your file - it most likely has keys in far negative or positive timeline.\n",
                        static_cast<int>(timeRange), takeName.c_str(), static_cast<int>(kMaxTimeRange));
                    continue;
                }
            }

            int animLayerCount = animStack->GetSrcObjectCount<FbxAnimLayer>();
            if (animLayerCount > 1)
            {
                FbxTime bakeStartTime, bakeEndTime, fbxSampleRate;
                bakeStartTime.SetSecondDouble(range.bakeStart);
                bakeEndTime.SetSecondDouble(range.bakeStop);

                {
                    const double sampleRate = 1.0f / FbxTime::GetFrameRate(fbxScene.GetGlobalSettings().GetTimeMode());
                    fbxSampleRate.SetSecondDouble(sampleRate);
                }

                if (!animStack->BakeLayers(fbxScene.GetAnimationEvaluator(), bakeStartTime, bakeEndTime, fbxSampleRate))
                    ReportWarning("Failed to bake layers.\n");

                animLayerCount = animStack->GetSrcObjectCount<FbxAnimLayer>();
                if (animLayerCount > 1)
                    ReportWarning("Expected to have 1 layer after BakeLayers. It has %d now.\n", animLayerCount);
            }

			/*
            if (animLayerCount > 0)
            {
                FBXImportAnimationClip* clip = 0;
                for (UInt32 j = 0; j < scene.animationClips.size(); ++j)
                    if (scene.animationClips[j].name == takeName)
                    {
                        clip = &scene.animationClips[j];
                        break;
                    }

                if (!clip)
                {
                    ReportError("Internal error: animation clip by name '%s' not found!", takeName.c_str());
                }
                else
                {
                    FbxAnimLayer* animLayer = animStack->GetSrcObject<FbxAnimLayer>();

                    clip->bakeStart = range.bakeStart;
                    clip->bakeStop = range.bakeStop;

                    NANAnimationInfos nanAnimationInfos;

                    for (int ii = 0; ii < childCount; ii++)
                    {
                        RecursiveImportAnimation(fbxScene, root->GetChild(ii), *animLayer, *clip, fbxNodeMap, fbxMeshToInfoMap, range, nanAnimationInfos, animationSettings.importBlendShapes);
                    }

                    double ifps = 1.0 / animationSettings.sampleRate;

                    for (double time = range.bakeStart; time < range.bakeStop; time += ifps)
                    {
                        fbxScene.SetCurrentAnimationStack(animStack);
                        auto eva = fbxScene.GetAnimationEvaluator();
                        FbxTime ft;
                        ft.SetSecondDouble(time);

                        clip->allFrames.push_back({});
                        auto& newFrame = clip->allFrames.back();
                        for (SInt32 j = 0; j < (SInt32)clip->nodeAnimations.size(); ++j)
                        {
                            FbxNode* fNode = (FbxNode*)clip->nodeAnimations[j].pFbxNode;
                            newFrame.resize(scene.meshBoneOffsetNodes.size(), { Vector4f::Identity(), Quaternionf::Identity(), Matrix4x4f::Identity() });// {1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f}});// clip->nodeAnimations.size());
                            const auto &matrix = eva->GetNodeGlobalTransform(fNode, ft);
                            //fNode->EvaluateGlobalTransform(ft);
                            newFrame[clip->nodeAnimations[j].node->boneNodeIndex].matrix = FBXMatrixToMatrix4x4(matrix);
                            //auto T = FBXPointToVector3Remap(matrix.GetT());
                            //auto S = FBXPointToVector3(matrix.GetS());
                            //auto R = ExtractQuaternionFromFBXEuler(matrix.GetR());
                            //newFrame[clip->nodeAnimations[j].node->boneNodeIndex].position = Vector4f{ T.x(), T.y(), T.z(), (S.x() + S.y() + S.z()) / 3.0f };
                            //newFrame[clip->nodeAnimations[j].node->boneNodeIndex].rotation = R;
                        }
                    }

                    if (!nanAnimationInfos.empty())
                    {
                        NANAnimationInfo accumulated;
                        for (NANAnimationInfos::const_iterator it = nanAnimationInfos.begin(), begin = nanAnimationInfos.begin(), end = nanAnimationInfos.end(); it != end; ++it)
                            accumulated.Accumulate(*it);

                        std::ostringstream str;
                        str << "Take/AnimStack '" << takeName << "' contains animation curves with invalid numbers (NANs) on these nodes"
                            << " (" << accumulated.GetHelperString() << "): ";
                        for (NANAnimationInfos::const_iterator it = nanAnimationInfos.begin(), begin = nanAnimationInfos.begin(), end = nanAnimationInfos.end(); it != end; ++it)
                        {
                            if (it != begin)
                                str << ", ";

                            str << "'" << it->nodeName << "' (" << it->GetString() << ")";
                        }
                        str << ". The invalid numbers will be replaced by default numbers from default pose.\n"
                            << "This error indicates corrupt FBX file. "
                            << "In some case this error can be caused by having 0 scales in animation. "
                            << "0 scales are not supported in FBXSDK. Use very small scales (like 1e-5) instead.";

                        ReportError(str.str().c_str());
                    }
                }
            }*/
        }
    }

    namespace
    {
        // Workaround suggested by Autodesk for case 360294 (support request id 1261296)
        // We reset all curve channels which have value (0, 0, 0) and keyframe which is not (0, 0, 0)
        // as these channels can be considered corrupt
        void SyncCurveNodeChannelValue(const std::string& nodeName, FbxAnimCurveNode* curveNode, const char* channelName)
        {
            if (curveNode && 3 == curveNode->GetChannelsCount())
            {
                // channels which have value (0, 0, 0) are considered invalid
                bool valid = false;
                for (unsigned int i = 0; i < curveNode->GetChannelsCount(); ++i)
                {
                    const double channelValue = curveNode->GetChannelValue(i, -1.0);
                    if (0 != channelValue)
                    {
                        valid = true;
                        break;
                    }
                }

                if (!valid)
                {
                    std::ostringstream str;

                    bool performedReset = false;

                    for (unsigned int i = 0; i < curveNode->GetChannelsCount(); ++i)
                    {
                        double value = 1;
                        FbxAnimCurve* curve = curveNode->GetCurve(i);
                        if (curve && curve->KeyGetCount() > 0)
                            value = curve->KeyGetValue(0);

                        curveNode->SetChannelValue(i, value);

                        if (0 != value)
                            performedReset = true;

                        if (i > 0)
                            str << ", ";
                        str << value;
                    }

                    // Ignore this warning since user don t really know what to do with it if it appears.
                    //if (performedReset)
                    //	ReportWarning("Node '%s' has invalid %s ChannelValues (0, 0, 0). Resetting it to (%s).\n", nodeName.c_str(), channelName, str.str().c_str());	
                }
            }
        }
    }

    void SyncCurveNodeChannelValue(FbxNode* node)
    {
        if (node == NULL)
            return;

        const std::string nodeName = static_cast<const char*>(node->GetNameWithoutNameSpacePrefix());

        //SyncCurveNodeChannelValue(nodeName, node->LclTranslation.GetCurveNode(), "Position");
        //SyncCurveNodeChannelValue(nodeName, node->LclRotation.GetCurveNode(), "Rotation");
        SyncCurveNodeChannelValue(nodeName, node->LclScaling.GetCurveNode(), "Scale");

        for (int i = 0; i < node->GetChildCount(); ++i)
            SyncCurveNodeChannelValue(node->GetChild(i));
    }

    namespace
    {
        void RemoveInvalidCurves(const char* const takeName, const char* const nodeName, FbxAnimLayer& animLayer, const char* const curveName, FbxPropertyT<FbxDouble3>& fbxProperty, const char* const* ids)
        {
            FbxAnimCurve* curves[3] =
            {
                fbxProperty.GetCurve(&animLayer, ids[0]),
                fbxProperty.GetCurve(&animLayer, ids[1]),
                fbxProperty.GetCurve(&animLayer, ids[2])
            };

            bool valid = true;
            for (int i = 0; i < 3; ++i)
            {
                if (curves[i] && curves[i]->KeyGetCount())
                {
                    const float curMin = static_cast<float>(curves[i]->KeyGet(0).GetTime().GetSecondDouble());
                    const float curMax = static_cast<float>(curves[i]->KeyGet(curves[i]->KeyGetCount() - 1).GetTime().GetSecondDouble());

                    if (!IsValidTimeRange(takeName, nodeName, curveName, curMin, curMax))
                    {
                        valid = false;
                        break;
                    }
                }
            }

            if (!valid)
            {
                for (int i = 0; i < 3; ++i)
                    if (curves[i])
                        curves[i]->KeyClear();
            }
        }

        static void RecursiveRemoveInvalidTransformCurves(const char* const takeName, FbxAnimLayer& animLayer, FbxNode* node)
        {
            const char* const translationIds[3] = { FBXSDK_CURVENODE_COMPONENT_X, FBXSDK_CURVENODE_COMPONENT_Y, FBXSDK_CURVENODE_COMPONENT_Z };
            const char* const scaleIds[3] = { FBXSDK_CURVENODE_COMPONENT_X, FBXSDK_CURVENODE_COMPONENT_Y, FBXSDK_CURVENODE_COMPONENT_Z };
            const char* const rotationIds[3] = { FBXSDK_CURVENODE_COMPONENT_X, FBXSDK_CURVENODE_COMPONENT_Y, FBXSDK_CURVENODE_COMPONENT_Z };

            const char* const nodeName = node->GetName();
            RemoveInvalidCurves(takeName, nodeName, animLayer, "translation", node->LclTranslation, translationIds);
            RemoveInvalidCurves(takeName, nodeName, animLayer, "scale", node->LclScaling, scaleIds);
            RemoveInvalidCurves(takeName, nodeName, animLayer, "rotation", node->LclRotation, rotationIds);

            for (int i = 0, count = node->GetChildCount(); i < count; ++i)
            {
                FbxNode* curChild = node->GetChild(i);
                RecursiveRemoveInvalidTransformCurves(takeName, animLayer, curChild);
            }
        }
    }

    // This will clear keys from all transform curves that have keys outside of range.
    // We only need to fix transform curves (not FOV, BlendShape, etc), because they cause 
    // crashes in ResetPivotSetAndConvertAnimation. The crash is caused by out-of-memory, because it has
    // to resample such a long range, so there can not be a proper fix from Autodesk.
    void RemoveInvalidTransformCurves(FbxScene& fbxScene, FbxNode* root)
    {
        // The root node never has any animation, so only process it's children.
        const int childCount = root->GetChildCount();

        for (int i = 0, c = fbxScene.GetSrcObjectCount<FbxAnimStack>(); i < c; ++i)
        {
            FbxAnimStack* animStack = fbxScene.GetSrcObject<FbxAnimStack>(i);
            const std::string takeName = animStack->GetName();

            const int animLayerCount = animStack->GetSrcObjectCount<FbxAnimLayer>();
            for (int j = 0; j < animLayerCount; ++j)
            {
                FbxAnimLayer* animLayer = animStack->GetSrcObject<FbxAnimLayer>(j);

                for (int k = 0; k < childCount; ++k)
                    RecursiveRemoveInvalidTransformCurves(takeName.c_str(), *animLayer, root->GetChild(k));
            }
        }
    }
}