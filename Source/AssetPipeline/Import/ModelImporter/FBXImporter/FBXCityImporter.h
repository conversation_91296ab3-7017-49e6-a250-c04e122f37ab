#pragma once
#include <vector>

#include "CECommon/Common/TRS.h"
#include "AssetPipeline/Import/ModelImporter/FBXImporter/FBXImporter.h"
#include "AssetPipeline/Import/ModelImporter/CityImportSettings.h"

namespace cross { namespace editor {
    class ASSET_API FBXCityImporter : public FBXImporter
    {
    public:
        FBXCityImporter();
        virtual ~FBXCityImporter();

        virtual void ImportAsset(const std::string& readPath, const std::string& writePath, ImportSetting* setting) override;
        virtual bool CheckAssetName(const char* name) const override;

    protected:
        virtual void SerializeCombineMaterials(ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                               std::vector<MeshDescription>& meshDescs) override;
        virtual cross::TexturePtr ImportTexture(const FbxSurfaceMaterial* fbxMaterial, const std::string& texProperty, const std::string& savePath, const std::string& assetPath, TextureCompression compression) override;
    
        virtual void SerializeModel(const ImportScene& importScene, const std::string& writePath, bool importSkeleton) override;
    private:
        void SerializeNewCombineMaterials(ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                          std::vector<MeshDescription>& meshDescs);
        void SerializeOldCombineMaterials(ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                          std::vector<MeshDescription>& meshDescs);

        TextureCompression GetTextureComporessionByMatProperty(const std::string& property);

        ImportColorSpace GetTextureColorSpaceByComporession(TextureCompression compression);
    private:
        CityImportSettings* mCityImporter = nullptr;
    };
}}   // namespace cross::editor