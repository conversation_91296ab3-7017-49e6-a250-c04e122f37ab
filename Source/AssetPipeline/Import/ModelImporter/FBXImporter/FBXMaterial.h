#pragma once
#include <fbxsdk.h>
#include <variant>
#include <map>
#include "AssetPipeline/Utils/AssetMath.h"
namespace cross {
    namespace editor
    {
        using ProType = std::variant<std::vector<float>, bool, std::string>;
        
        class FBXMaterial
        {
        public:
            FBXMaterial();
            ~FBXMaterial();

            void SetMaterialName(std::string& name);

            void AddProperty(std::string name, std::vector<float>& value);

            void AddProperty(std::string name, std::string value);
            std::string mName;
            std::map<std::string, ProType> mPropertyMap;

            void SetMaterial(FbxSurfaceMaterial* pFbxSurfaceMaterial);
            std::string Serialize(const std::string& meshNdaAssetPath, const std::string& materialDir);
        private:

            std::string getTexture(const FbxProperty & property);
            AssetMath::Vector3f getProperty(FbxSurfaceMaterial* material, const char* pPropertyName, const char* pFactorPropertyName, AssetMath::Vector3f defaultValue = AssetMath::Vector3f(0, 0, 0));
            void AddProperty(FbxSurfaceMaterial* material, const char* pPropertyName, const char* pFactorPropertyName, AssetMath::Vector3f defaultValue = AssetMath::Vector3f(0, 0, 0));

            float getProperty(FbxSurfaceMaterial * material, const char * pPropertyName, float defaultValue);
            void AddProperty(FbxSurfaceMaterial * material, const char * pPropertyName, float defaultValue);
            //--------------------------------------------------------------------------------------------------------------
            AssetMath::Vector2f getProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector2f defaultValue);
            void AddProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector2f defaultValue);
            //--------------------------------------------------------------------------------------------------------------
            AssetMath::Vector3f getProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector3f defaultValue);
            void AddProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector3f defaultValue);
            //--------------------------------------------------------------------------------------------------------------
            AssetMath::Vector4f getProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector4f defaultValue);
            void AddProperty(FbxSurfaceMaterial* material, const char* pPropertyName, AssetMath::Vector4f defaultValue);

            void AddProperty(FbxProperty& property, const char * pPropertyName, std::string defaultValue);

            bool PrepareJsonContent(nlohmann::json& s);
        };
    }
}
