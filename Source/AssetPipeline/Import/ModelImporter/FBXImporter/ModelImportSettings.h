#pragma once

namespace cross
{
	namespace editor
	{
		const int kMaxLength = 260;

#pragma pack(push, 1)

		struct ModelImportSettings
		{
			bool ImportNormals{ true };
			bool ImportTangents{ true };
			bool ImportSecondaryUV{ true };
			bool ImportAnimations{ true };

			bool ImportBlendShapes{ true };
			bool ImportSkin{ true };
			bool ImportCameras{ false };
			bool ImportLights{ false };

			bool ImportMaterials{ true };
			bool ResampleCurves{ true };
			bool UseFileScale{ true };
			bool ImportAnimatedCustomProperties{ true };

			bool GenerateSecondaryUV{ false };
			bool MergeWithMaterial{ false };

			float   secondaryUVAngleDistortion{ 80.0f };
			float   secondaryUVAreaDistortion{ 15.0f };
			float   secondaryUVHardAngle{ 88.0f };
			float   secondaryUVPackMargin{ 4.0f };
			//float   secondaryUVAngleDistortion{ 100.0f  * defaultUnwrapParam.angleDistortionThreshold };
			//float   secondaryUVAreaDistortion{ 100.0f  * defaultUnwrapParam.areaDistortionThreshold };
			//float   secondaryUVHardAngle{ defaultUnwrapParam.hardAngle };
			//float   secondaryUVPackMargin{ 1024.0f * defaultUnwrapParam.packMargin };

			//const char* absolutePath;
			//const char* originalExtension;
			char textureImportFolder[kMaxLength];
		};
	}
#pragma pack(pop)
}