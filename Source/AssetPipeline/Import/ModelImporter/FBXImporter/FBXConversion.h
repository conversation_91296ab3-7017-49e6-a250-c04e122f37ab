#pragma once

#include "CrossBase/Math/CrossMath.h"
#include "AssetPipeline/Utils/AssetMath.h"
namespace cross::editor
{
    inline FbxVector4 Vector3ToFBXPoint(const AssetMath::Vector3f& vec)
    {
        return FbxVector4(vec[0], vec[1], vec[2]);
    }

    inline AssetMath::Vector3f FBXPointToVector3(const FbxVector4& vec)
    {
        return {
            static_cast<float>(vec.mData[0]),
            static_cast<float>(vec.mData[1]),
            static_cast<float>(vec.mData[2])
        };
    }

    inline Double3 FBXPointToDouble3(const FbxVector4& vec)
    {
        return {vec.mData[0], vec.mData[1], vec.mData[2]};
    }

    // We reverse all triangles
    // Keep logic of this function the same as function bellow

	//Right handed coordinate system with Y up and (-Z)-front -> Left handed coordinate system with Y-up and Z-front
	/*

							| 1			|
		V =    VBefore  *	|    1		|
							|      -1	|
							|         1 |


			| 1			|					| 1			|
		M =	|    1		|   *  Mbefore  *	|    1		|
			|      -1	|					|      -1	|
			|         1 |					|         1 |

	*/

    inline AssetMath::Vector3f FBXPointToVector3Remap(const FbxVector4& vec)
    {
        return {
            static_cast<float>(vec.mData[0]),
            static_cast<float>(vec.mData[1]),
            static_cast<float>(-vec.mData[2])
        };
    }

    inline Float3 FBXPointToFloat3Remap(const FbxVector4& vec)
    {
        return
        {
            static_cast<float>(vec.mData[0]),
            static_cast<float>(vec.mData[1]),
            static_cast<float>(-vec.mData[2])
        };
    }

    inline Float3 FBXPointToFloat3Remap(const Double3& vec)
    {
        return {static_cast<float>(vec.x), static_cast<float>(vec.y), static_cast<float>(-vec.z)};
    }

    inline Float3 ConvertDir(const FbxVector4& vec)
    {
        return 
        {
            static_cast<float>(vec.mData[0]),
            static_cast<float>(vec.mData[1]),
            static_cast<float>(-vec.mData[2])
        };
    }

    // Keep logic of this function the same as function above
    inline float FBXCoordinateRemap(const float value, int coordinateIndex)
    {
        return coordinateIndex == 0 ? -value : value;
    }

    inline ColorRGBAf FBXColorToColorRGBA(const FbxColor& vec)
    {
        return ColorRGBAf(
            static_cast<float>(vec.mRed),
            static_cast<float>(vec.mGreen),
            static_cast<float>(vec.mBlue),
            static_cast<float>(vec.mAlpha));
    }

    inline float FBXEulerRemap(const float value, int coordinateIndex)
    {
        return coordinateIndex == 1 || coordinateIndex == 2 ? -value : value;
    }

    inline ColorRGBAf FBXColorToColorRGB(const FbxColor& vec)
    {
        return ColorRGBAf(
            static_cast<float>(vec.mRed),
            static_cast<float>(vec.mGreen),
            static_cast<float>(vec.mBlue),
            1);
    }

	//Right handed coordinate system with Y up and (-Z)-front -> Left handed coordinate system with Y-up and Z-front
	/*
		
							        | 1			|
		Vertex =    VertexBefore  *	|    1		|
							        |      -1	|
							        |         1 |


			        | 1			|					    | 1			|
		Matrix =	|    1		|   *  Matrixbefore  *	|    1		|
			        |      -1	|					    |      -1	|
			        |         1 |					    |         1 |

	*/
	inline AssetMath::Matrix4x4f FBXMatrixToMatrix4x4(FbxAMatrix before)
	{
#if USE_DXMATH != 1
        AssetMath::Matrix4x4f converted;
		const double* beforePtr = before;


	    converted.m00 = static_cast<float>(beforePtr[0]), converted.m01 = static_cast<float>(beforePtr[1]), converted.m02 = static_cast<float>(-beforePtr[2]), converted.m03 = static_cast<float>(beforePtr[3]);
        converted.m10 = static_cast<float>(beforePtr[4]), converted.m11 = static_cast<float>(beforePtr[5]), converted.m12 = static_cast<float>(-beforePtr[6]), converted.m23 = static_cast<float>(beforePtr[7]);
        converted.m20 = static_cast<float>(beforePtr[8]), converted.m21 = static_cast<float>(beforePtr[8]), converted.m22 = static_cast<float>(-beforePtr[10]), converted.m23 = static_cast<float>(beforePtr[11]);
        converted.m30 = static_cast<float>(beforePtr[12]), converted.m31 = static_cast<float>(beforePtr[13]), converted.m32 = static_cast<float>(-beforePtr[14]), converted.m33 = static_cast<float>(beforePtr[15]);
		return converted;
#else

		const double* beforePtr = before;

		AssetMath::Matrix4x4f converted(
			static_cast<float>(beforePtr[0]),
			static_cast<float>(beforePtr[1]),
			static_cast<float>(-beforePtr[2]),
			static_cast<float>(beforePtr[3]),

			static_cast<float>(beforePtr[4]),
			static_cast<float>(beforePtr[5]),
			static_cast<float>(-beforePtr[6]),
			static_cast<float>(beforePtr[7]),

			static_cast<float>(-beforePtr[8]),
			static_cast<float>(-beforePtr[9]),
			static_cast<float>(beforePtr[10]),
			static_cast<float>(-beforePtr[11]),

			static_cast<float>(beforePtr[12]),
			static_cast<float>(beforePtr[13]),
			static_cast<float>(-beforePtr[14]),
			static_cast<float>(beforePtr[15])
		);
		return converted;
#endif
	}

    inline Float4x4 FBXMatrixToFloat4x4(FbxAMatrix before)
    {
        Float4x4 result;
        const double* beforePtr = before;

        result.m00 = static_cast<float>(beforePtr[0]);
        result.m01 = static_cast<float>(beforePtr[1]);
        result.m02 = static_cast<float>(-beforePtr[2]);
        result.m03 = static_cast<float>(beforePtr[3]);

        result.m10 = static_cast<float>(beforePtr[4]);
        result.m11 = static_cast<float>(beforePtr[5]);
        result.m12 = static_cast<float>(-beforePtr[6]);
        result.m13 = static_cast<float>(beforePtr[7]);

        result.m20 = static_cast<float>(-beforePtr[8]);
        result.m21 = static_cast<float>(-beforePtr[9]);
        result.m22 = static_cast<float>(beforePtr[10]);
        result.m23 = static_cast<float>(-beforePtr[11]);

        result.m30 = static_cast<float>(beforePtr[12]);
        result.m31 = static_cast<float>(beforePtr[13]);
        result.m32 = static_cast<float>(-beforePtr[14]);
        result.m33 = static_cast<float>(beforePtr[15]);

        return result;
    }

    inline Quaternion ExtractQuaternion(FbxVector4 euler)
    {
        euler[1] = -euler[1];
        euler[2] = -euler[2];
        FbxAMatrix rotationMatrix;
        rotationMatrix.SetR(euler);

        FbxQuaternion quat = rotationMatrix.GetQ();
        Quaternion result(static_cast<float>(quat[3]), static_cast<float>(quat[0]), static_cast<float>(quat[1]), static_cast<float>(quat[2]));

        // FBX SDK (i.e. FbxAMatrix::GetQ) doesn't guarante to return normalized quaternion - so we do that ourselves
        result.Normalize();

        return result;
    }

    inline AssetMath::Quaternionf ExtractQuaternionFromFBXEuler(FbxVector4 euler)
    {
        euler[1] = -euler[1];
        euler[2] = -euler[2];
        FbxAMatrix rotationMatrix;
        rotationMatrix.SetR(euler);

        FbxQuaternion rotationQuat = rotationMatrix.GetQ();
        AssetMath::Quaternionf q{
            static_cast<float>(rotationQuat[3]),
            static_cast<float>(rotationQuat[0]),
            static_cast<float>(rotationQuat[1]),
            static_cast<float>(rotationQuat[2])
        };

        // FBX SDK (i.e. FbxAMatrix::GetQ) doesn't guarante to return normalized quaternion - so we do that ourselves
        //q = Normalize(q);

        return q.Normalized();
    }

    // This doesn't do anything, but it's used by template functions
    inline int FBXToBasicType(const int& t)
    {
        return t;
    }

    inline AssetMath::Vector2f FBXToBasicType(const FbxVector2& t)
    {
        return {
            static_cast<float>(t.mData[0]),
            static_cast<float>(t.mData[1])
        };
    }

    inline ColorRGBA32 FBXToBasicType(const FbxColor& t)
    {
        return FBXColorToColorRGBA(t);
    }

    inline AssetMath::Vector3f FBXToBasicType(const FbxVector4& t)
    {
        return FBXPointToVector3Remap(t);
    }

    inline bool ConvertFBXColorProperty(const FbxPropertyT<FbxDouble3>& prop, ColorRGBAf& color)
    {
        if (!prop.IsValid())
        {
            return false;
        }
        else
        {
            FbxDouble3 value = prop.Get();
            color.Set(
                static_cast<float>(value[0]),
                static_cast<float>(value[1]),
                static_cast<float>(value[2]),
                1.0f);
            return true;
        }
    }

    inline bool ConvertFBXColorProperty(const FbxPropertyT<FbxDouble3>& prop, ColorRGBAf& color, const ColorRGBAf& defaultValue)
    {
        if (!ConvertFBXColorProperty(prop, color))
        {
            ///@TODO: This looks very strange...  Investigate...
            return true;
            color = defaultValue;
        }
        else
        {
            return false;
        }
    }

    inline bool IsSet(const FbxProperty& prop)
    {
        return prop.IsValid() && prop.Modified();
    }

    inline bool ConvertFBXDoubleProperty(const FbxPropertyT<FbxDouble>& prop, float& value, const float defaultValue)
    {
        const bool isSet = IsSet(prop);
        value = isSet ? static_cast<float>(prop.Get()) : defaultValue;
        return isSet;
    }

    inline bool ConvertFBXBoolProperty(const FbxPropertyT<FbxBool>& prop, bool& value, const bool defaultValue)
    {
        const bool isSet = IsSet(prop);
        value = isSet ? prop.Get() : defaultValue;
        return isSet;
    }

    inline std::string GenerateBlendShapeName(const FbxBlendShapeChannel* blendShapeChannel)
    {
        return blendShapeChannel->GetName();
    }

    inline std::string GenerateBlendShapeAnimationAttribute(const FbxBlendShapeChannel* blendShapeChannel)
    {
        return "blendShape." + GenerateBlendShapeName(blendShapeChannel);
    }
}
