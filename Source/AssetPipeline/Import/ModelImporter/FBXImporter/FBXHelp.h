#pragma once

#include <fbxsdk.h>
#include "FBXImportUtility.h"

namespace cross
{
	namespace editor
	{
		class FBXHelp
		{
			public:
				FBXHelp()
				{}

				~FBXHelp()
				{}

				static void PrintGenericInvalidList(const std::string& nameStr, const UInt32 maxIndexCount, const int totalCount, const std::vector<int>& indices, const std::string& messageText)
				{
					std::ostringstream oss;
					oss << nameStr << " has " << indices.size()
						<< " (out of " << totalCount << ") " << messageText << ". The list of vertices: ";

					for (int i = 0, size = std::min<int>(maxIndexCount, (int)indices.size()); i < size; ++i)
					{
						if (i != 0)
						{
							oss << ", ";
						}
						oss << indices[i];
					}

					if (indices.size() > maxIndexCount)
					{
						oss << " and so on...";
					}

					oss << std::endl;

					ReportWarning("%s", oss.str().c_str());
				}

				static void PrintInvalidList(const std::string& meshName, const UInt32 maxIndexCount, const int totalCount, const std::vector<int>& indices, const std::string& messageText)
				{
					PrintGenericInvalidList("Mesh '" + meshName + "'", maxIndexCount, totalCount, indices, messageText);
				}

				template <class T>
				static bool IsValidVertex(const T& v)
				{
					return !v.hasNaN();
				}

				template<class T, class T2, class GetValueAt>
				static void GetLayerDataImp([[maybe_unused]] FbxMesh* fbxMesh, FbxLayerElementTemplate<T>& element, std::vector<T2>& output, int* indices, int indexCount, UInt32* polygonSizes, int nPolygonCount, int vertexCount, const char* layerName, const std::string& meshName)
				{
					GetValueAt input(element);
					if (!input.IsValid())
						return;

					output.resize(indexCount);

					const FbxLayerElement::EMappingMode mappingMode = element.GetMappingMode();
					switch (mappingMode)
					{
						case FbxLayerElement::eByControlPoint:
							// TODO : I'm not sure this makes sense when eIndexToDirect is used
							{
								if (input.GetDirectArraySize() != vertexCount)
								{
									output.clear();
									ReportError("The mesh %s has invalid %s. Try cleaning and triangulating %s in your 3D modeller before importing it in engine.\nThis is a failure in the fbx exporter of the tool which exported the fbx file.", meshName.c_str(), layerName, meshName.c_str(), layerName);
									return;
								}

								for (int f = 0; f < indexCount; f++)
								{
									int index = indices[f];
									input.GetValue(index, output[f]);
								}
							}
							break;
						case FbxLayerElement::eByPolygonVertex:
							{
								for (int f = 0; f < indexCount; ++f)
								{
									input.GetValue(f, output[f]);
								}
							}
							break;
						case FbxLayerElement::eByPolygon:
							{
								int wedgeIndex = 0;
								for (int f = 0; f < nPolygonCount; f++)
								{
									for (UInt32 e = 0; e < polygonSizes[f]; e++, wedgeIndex++)
									{
										input.GetValue(f, output[wedgeIndex]);
									}
								}
							}
							break;
						case FbxLayerElement::eAllSame:
							{
								T2 value;
								if (input.GetValue(0, value))
								{
									for (int f = 0; f < indexCount; f++)
									{
										output[f] = value;
									}
								}
							}
							break;
						default:
							LOG_INFO("Unsupported wedge mapping {}", (int)mappingMode);
							LOG_ERROR("Unsupported wedge mapping mode type. Please report this bug.\n");
							break;
					}

					/*
					if (mappingMode == FbxLayerElement::eByControlPoint)
					{
						// TODO : I'm not sure this makes sense when eIndexToDirect is used
						if (input.GetDirectArraySize() != vertexCount)
						{
							output.clear();
							ReportError("The mesh %s has invalid %s. Try cleaning and triangulating %s in your 3D modeller before importing it in engine.\nThis is a failure in the fbx exporter of the tool which exported the fbx file.", meshName.c_str(), layerName, meshName.c_str(), layerName);
							return;
						}

						for (int f = 0; f < indexCount; f++)
						{
							int index = indices[f];
							input.GetValue(index, output[f]);
						}
					}
					else if (mappingMode == FbxLayerElement::eByPolygonVertex)
					{
						for (int f = 0; f < indexCount; ++f)
						{
							input.GetValue(f, output[f]);
						}
					}
					else if (mappingMode == FbxLayerElement::eByPolygon)
					{
						int wedgeIndex = 0;
						for (int f = 0; f < nPolygonCount; f++)
						{
							for (UInt32 e = 0; e < polygonSizes[f]; e++, wedgeIndex++)
							{
								input.GetValue(f, output[wedgeIndex]);
							}
						}
					}
					else if (mappingMode == FbxLayerElement::eAllSame)
					{
						T2 value;
						if (input.GetValue(0, value))
						{
							for (int f = 0; f < indexCount; f++)
							{
								output[f] = value;
							}
						}
					}
					else
					{
						LOG_INFO("Unsupported wedge mapping {}", (int)mappingMode);
						LOG_ERROR("Unsupported wedge mapping mode type. Please report this bug.\n");
					}
					*/
				}

				template<class T, class T2>
				static void GetLayerData(FbxMesh* fbxMesh, FbxLayerElementTemplate<T>& element, std::vector<T2>& output, int* indices, int indexCount, UInt32* polygonSizes, int nPolygonCount, int vertexCount, const char* layerName, const std::string& meshName)
				{
					const FbxLayerElement::EReferenceMode mode = element.GetReferenceMode();

					switch (mode)
					{
						case FbxLayerElement::eDirect:
							GetLayerDataImp<T, T2, GetValueAtDirect<T, T2> >(fbxMesh, element, output, indices, indexCount, polygonSizes, nPolygonCount, vertexCount, layerName, meshName);
							break;
						case FbxLayerElement::eIndexToDirect:
							GetLayerDataImp<T, T2, GetValueAtIndex <T, T2> >(fbxMesh, element, output, indices, indexCount, polygonSizes, nPolygonCount, vertexCount, layerName, meshName);
							break;
						default:
							LOG_ERROR("Unsupported wedge reference mode type. Please report this bug.\n");
					}
					/*
					if (referenceMode == FbxLayerElement::eDirect)
					{
						GetLayerDataImp<T, T2, GetValueAtDirect<T, T2> >(fbxMesh, element, output, indices, indexCount, polygonSizes, nPolygonCount, vertexCount, layerName, meshName);
					}
					else if (referenceMode == FbxLayerElement::eIndexToDirect)
					{
						GetLayerDataImp<T, T2, GetValueAtIndex <T, T2> >(fbxMesh, element, output, indices, indexCount, polygonSizes, nPolygonCount, vertexCount, layerName, meshName);
					}
					else
					{
						LOG_ERROR("Unsupported wedge reference mode type. Please report this bug.\n");
					}
					*/
				}

				static void FilterValidataValue(const std::string& name, std::vector<Vector2f>& inData)
				{
					std::vector<int> invalidDatas;
					for (UInt32 i = 0, size = (UInt32)inData.size(); i < size; ++i)
					{
						if (inData[i].hasNaN())
						{
							inData[i] = Vector2f::Zero();
							invalidDatas.push_back(i);
						}
					}
					if (!invalidDatas.empty())
					{
						LOG_ERROR("%s have %d invaliddata.\n", name.c_str(), invalidDatas.size());
					}
				}
		};
	}
}
