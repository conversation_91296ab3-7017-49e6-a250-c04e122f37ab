#include "EnginePrefix.h"
#include "FBXCityImporter.h"
#include "Resource/Material.h"
#include "Resource/ResourceManager.h"
#include "Resource/ResourceConvertManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Prefab/PrefabResource.h"
#include "Resource/resourceasset.h"
#include "Resource/Texture/Texture2D.h"
#include "Resource/Texture/Texture2DArray.h"
#include "AssetPipeline/Model/ModelChange.h"
#include "Resource/MeshAssetDataResource.h"

namespace cross { namespace editor {
    FBXCityImporter::FBXCityImporter()
    {
        SetType(AssetType::City);
    }

    FBXCityImporter::~FBXCityImporter() {}

    void FBXCityImporter::ImportAsset(const std::string& readPath, const std::string& writePath, ImportSetting* setting) {
        mCityImporter = static_cast<CityImportSettings*>(setting);
        ModelImportSettings modelSetting;
        modelSetting.SplitByMaterial = false;
        modelSetting.CreateCombineMaterials = true;
        modelSetting.UseSameUVIndex = true;
        modelSetting.ImportMaterialFx = mCityImporter->MaterialFx;
        modelSetting.MatTexImportSize = mCityImporter->TextureSize;
        modelSetting.UseMeshCurvation = mCityImporter->UseMeshCurvation;
        FBXImporter::ImportAsset(readPath, writePath, &modelSetting);
    }

    bool FBXCityImporter::CheckAssetName(const char* name) const
    {
        return HasExtension(name, ".combine");
    }
    
    void FBXCityImporter::SerializeCombineMaterials(ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                                    std::vector<MeshDescription>& meshDescs)
    {
        if (mCityImporter->CreateNewMaterial)
            SerializeNewCombineMaterials(importScene, materialDescs, assetFilePath, ndaSavePath, bindings, meshDescs);
        else
            SerializeOldCombineMaterials(importScene, materialDescs, assetFilePath, ndaSavePath, bindings, meshDescs);
    }

    cross::TexturePtr FBXCityImporter::ImportTexture(const FbxSurfaceMaterial* fbxMaterial, const std::string& texProperty, const std::string& savePath, const std::string& assetPath, TextureCompression compression)
    {
        TextureImportSetting texSeting;
        texSeting.ImportSize = mImportSettings->MatTexImportSize;
        texSeting.Compression = compression;
        texSeting.ColorSpace = GetTextureColorSpaceByComporession(texSeting.Compression);
        texSeting.SaveRawData = false;
        texSeting.VirtualTextureStreaming = mCityImporter->EnableVTStreaming;

        TexturePtr texture{nullptr};
        if (FbxProperty fbxProperty = fbxMaterial->FindProperty(texProperty.c_str()); fbxProperty.IsValid())
        {
            UInt32 textureCount = static_cast<UInt32>(fbxProperty.GetSrcObjectCount<FbxTexture>());
            if (textureCount > 0)
            {
                for (auto textureIndex = 0u; textureIndex < textureCount; ++textureIndex)
                {
                    FbxFileTexture* fbxTexture = fbxProperty.GetSrcObject<FbxFileTexture>(textureIndex);
                    if (fbxTexture)
                    {
                        bool isSpecialPath = false;
                        std::string texRelFilePath = fbxTexture->GetRelativeFileName();
                        PathHelper::Normalize(texRelFilePath);
                        if (PathHelper::IsAbsoluteFilePath(texRelFilePath))
                        {
                            if (auto pos = texRelFilePath.find("/Assets/"); pos != std::string::npos)
                            {
                                isSpecialPath = true;
                                texRelFilePath = texRelFilePath.substr(pos + 8);
                            }
                        }
                        auto texRelFilePathTemp = texRelFilePath.substr(0, texRelFilePath.rfind("."));
                        texRelFilePathTemp = StringHelper::Replace(texRelFilePathTemp, "../", "");
                        std::string texSavePath = PathHelper::Combine(savePath.c_str(), texRelFilePathTemp.c_str(), ".nda");
                        if (!PathHelper::IsFileExist(fbxTexture->GetFileName()))
                        {
                            if (isSpecialPath)
                            {
                                auto newPath = PathHelper::Combine(mCityImporter->CitySrcResLibPath.c_str(), texRelFilePath.c_str());
                                fbxTexture->SetFileName(newPath.c_str());
                            }
                            else
                            {
                                std::filesystem::path dir(assetPath);
                                dir.remove_filename();
                                std::filesystem::path texPath(fbxTexture->GetFileName());
                                std::string fileName = texPath.filename().string();
                                std::filesystem::path newPath = dir / fileName;
                                fbxTexture->SetFileName(newPath.string().c_str());
                            }
                        }
                        if (mCityImporter->UseExistTexture && PathHelper::IsFileExist(texSavePath))
                        {
                            const std::string& relativePath = PathHelper::GetRelativePath(texSavePath);
                            texture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(relativePath.c_str(), false));
                        }
                        else
                        {
                            texture = FBXImporter::ImportTexture(fbxTexture, texSavePath, texSeting);
                        }
                    }
                    break;
                }
                if (!texture)
                {
                    LOG_WARN("{} {} Property Import Texture Error\n", fbxMaterial->GetName(), texProperty);
                }
            }
            else
            {
                LOG_WARN("{} {} Property Texture Count Is Zero\n", fbxMaterial->GetName(), texProperty);
            }
        }
        else
        {
            LOG_WARN("{} Not Have Property {}\n", fbxMaterial->GetName(), texProperty);
        }
        return texture;
    }

    void FBXCityImporter::SerializeModel(const ImportScene& importScene, const std::string& writePath, bool importSkeleton) {
        FBXImporter::SerializeModel(importScene, writePath, importSkeleton);

        if (mCityImporter->SplitModel)
        {
            auto modelDir = PathHelper::GetParentPath(writePath);
            auto modelName = PathHelper::GetBaseFileName(writePath);
            const std::string& meshPath = writePath;
            std::string materialPath = mCityImporter->CreateNewMaterial ? PathHelper::Combine(modelDir.c_str(), modelName.c_str(), ".mtl.nda") : mCityImporter->CityMaterial;
            auto meshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(meshPath, false));
            Float3 center = meshAsset->GetAssetData()->GetBoundingBox().GetCenter();
            ModelChange::ModelSplit(meshPath, {materialPath}, Float2(center.x, center.z), mCityImporter->SplitModelTileSize, mCityImporter->SplitSubModelAsMode);
        }
    }

    void FBXCityImporter::SerializeNewCombineMaterials(ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& inNdaSavePath, std::vector<MaterialMeshBind>& bindings,
                                                       std::vector<MeshDescription>& meshDescs)
    {
        auto modelDir = PathHelper::GetParentPath(inNdaSavePath);
        auto modelName = PathHelper::GetBaseFileName(inNdaSavePath);
        const std::string& fxPath = gResourceMgr.ConvertGuidToPath(mImportSettings->ImportMaterialFx);
        auto fxRes = TypeCast<resource::Fx>(gResourceMgr.GetResource(fxPath.c_str()));
        auto newMaterial = resource::Material::CreateMaterialTempInstance(fxRes);
        std::string newMaterialPath = PathHelper::Combine(modelDir.c_str(), modelName.c_str(), ".mtl.nda");
        newMaterial->CreateAsset(newMaterialPath);

        std::vector<std::pair<std::string, std::string>> matPropes;
        std::vector<TextureCompression> matTextCompressions;
        if (mCityImporter->EnableVTStreaming)
        {
            matPropes = {{FbxSurfaceMaterial::sDiffuse, "_BaseMap_VT_0"}, {FbxSurfaceMaterial::sNormalMap, "_NormalMap_VT_1"}, {"ShininessExponent", "_MaskMap_VT_2"}, {FbxSurfaceMaterial::sEmissive, "_EmissiveMap_VT_3"}};
            matTextCompressions = {mCityImporter->BaseMapCompression, mCityImporter->BaseMapCompression, mCityImporter->BaseMapCompression, mCityImporter->BaseMapCompression};
        }
        else
        {
            matPropes = {{FbxSurfaceMaterial::sDiffuse, "_BaseMapArray"}, {FbxSurfaceMaterial::sNormalMap, "_NormalMapArray"}, {"ShininessExponent", "_MaskMapArray"}, {FbxSurfaceMaterial::sEmissive, "_EmissiveMapArray"}};
            matTextCompressions = {mCityImporter->BaseMapCompression, mCityImporter->NormalMapCompression, mCityImporter->MaskMapCompression, mCityImporter->EmissiveMapCompression};
        }
        std::vector<UInt32> matTexDefualtColor = {0xffffffff, 0xffff8080, 0xff000000, 0xff000000};

        importScene.MatIdxToTexIds.resize(importScene.MaterilGlobalIndexMap.size(), {-1, -1, -1, -1});
        std::vector<float> enableFlag = {-1, -1, -1, -1};

        std::map<UInt32, std::vector<TexturePtr>> matTextureArray;

        std::vector<std::pair<bool, TexturePtr>> matBlackTexs(matPropes.size());

        auto textSaveDir = mCityImporter->CityResLibPath != "" ? PathHelper::GetAssetAbsolutePath(mCityImporter->CityResLibPath) : modelDir;
        for (const auto& [voidPtr, matId] : importScene.MaterilGlobalIndexMap)
        {
            auto& matTexs = matTextureArray[matId];
            matTexs.resize(matPropes.size());
            for (int pindex = 0; pindex < matPropes.size(); pindex++)
            {
                const FbxSurfaceMaterial* fbxMat = reinterpret_cast<FbxSurfaceMaterial*>(voidPtr);
                auto tex = ImportTexture(fbxMat, matPropes[pindex].first, textSaveDir, assetFilePath, matTextCompressions[pindex]);
                if (tex && tex->GetClassID() == ClassID(Texture2D))
                {
                    matTexs[pindex] = tex;
                    matBlackTexs[pindex].second = tex;
                    enableFlag[pindex] = 1.f;
                }
                else
                {
                    matBlackTexs[pindex].first = true;
                }
            }
        }
        auto globalComporession = TextureImportSetting::gTextureImportSetting.Compression;
        std::string blackMapSaveDir = PathHelper::Combine(textSaveDir.c_str(), "default_texture");
        if (mImportSettings->UseSameUVIndex)
        {
            for (int pindex = 0; pindex < matPropes.size(); pindex++)
            {
                TextureImportSetting::gTextureImportSetting.Compression = GetTextureComporessionByMatProperty(matPropes[pindex].first);
                if (!matBlackTexs[pindex].first || enableFlag[pindex] <= 0.f)
                    continue;
                auto blackTex = GenSolidTexture(matBlackTexs[pindex].second->GetTextureInfo(), blackMapSaveDir, matTextCompressions[pindex], matTexDefualtColor[pindex], matPropes[pindex].second, false);
                Assert(blackTex);
                blackTex->CreateGPUResource();
                matBlackTexs[pindex].second = blackTex;
            }
        }
        TextureImportSetting::gTextureImportSetting.Compression = globalComporession;

        for (int pindex = 0; pindex < matPropes.size(); pindex++)
        {
            if (enableFlag[pindex] <= 0.0f)
                continue;
            const std::string& propName = matPropes[pindex].second;
            std::string propTexPath = PathHelper::Combine(modelDir.c_str(), (modelName + "." + propName + ".nda").c_str());
            auto textArrayPtr = gResourceMgr.CreateResourceAs<resource::Texture2DArray>();
            textArrayPtr->CreateAsset(propTexPath);
            textArrayPtr->SetOrigintEnableVT(mCityImporter->EnableVTStreaming);
            textArrayPtr->SetEnableVTStreaming(mCityImporter->EnableVTStreaming);
            for (const auto& [matId, matTexs] : matTextureArray)
            {
                bool ret = false;
                if (mImportSettings->UseSameUVIndex)
                {
                    ret = textArrayPtr->AddTexture(matTexs[pindex] ? matTexs[pindex] : matBlackTexs[pindex].second);
                    Assert(ret);
                    importScene.MatIdxToTexIds[matId][pindex] = (UInt32)textArrayPtr->GetTextureCount() - 1u;
                }
                else
                {
                    if (matTexs[pindex])
                    {
                        const auto& texPath = matTexs[pindex]->GetGuid_Str();
                        if (!textArrayPtr->HasTexture(texPath))
                            ret = textArrayPtr->AddTexture(matTexs[pindex]);
                        importScene.MatIdxToTexIds[matId][pindex] = textArrayPtr->GetTextureIndex(texPath);
                    }
                }
            }
            textArrayPtr->Serialize();
            newMaterial->SetTextureProp(propName, TypeCast<resource::Texture>(textArrayPtr));
            textArrayPtr.reset();
        }
        newMaterial->SetProp("TEXTURE_ARRAY_ENABLE", true);
        newMaterial->SetProp("_EnableMapArrayFlags", enableFlag);
        if (mCityImporter->EnableVTStreaming)
            newMaterial->SetProp("_EnableVTFlags", enableFlag);
        newMaterial->SetProp("OPEN_VT", mCityImporter->EnableVTStreaming);

        auto newRelMaterialPath = EngineGlobal::GetFileSystem()->GetRelativePath(newMaterialPath);
        for (auto& binding : bindings)
        {
            binding.AddMaterialPath(newRelMaterialPath);
            for (const auto& meshDescIndex : binding.GetMeshDescIndices())
            {
                meshDescs[meshDescIndex].SetMaterialPath(newRelMaterialPath);
            }
        }
        // save mat
        newMaterial->Serialize({}, newMaterialPath);
        newMaterial.reset();
    }

    void FBXCityImporter::SerializeOldCombineMaterials(ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                                       std::vector<MeshDescription>& meshDescs)
    {
        auto modelDir = PathHelper::GetParentPath(ndaSavePath);
        auto matRes = TypeCast<resource::Material>(gAssetStreamingManager->LoadSynchronously(mCityImporter->CityMaterial, false));

        std::map<UInt32, std::vector<TexturePtr>> matTextureArray;
        importScene.MatIdxToTexIds.resize(importScene.MaterilGlobalIndexMap.size(), {-1, -1, -1, -1});

        std::vector<std::pair<std::string, std::string>> matPropes;
        std::vector<TextureCompression> matTextCompressions;
        if (mCityImporter->EnableVTStreaming)
        {
            matPropes = {{FbxSurfaceMaterial::sDiffuse, "_BaseMap_VT_0"}};
            matTextCompressions = {mCityImporter->BaseMapCompression};
        }
        else
        {
            matPropes = {{FbxSurfaceMaterial::sDiffuse, "_BaseMapArray"}};
            matTextCompressions = {mCityImporter->BaseMapCompression};
        }

        auto textSaveDir = mCityImporter->CityResLibPath != "" ? PathHelper::GetAssetAbsolutePath(mCityImporter->CityResLibPath) : modelDir;
        for (const auto& [voidPtr, matId] : importScene.MaterilGlobalIndexMap)
        {
            auto& matTexs = matTextureArray[matId];
            matTexs.resize(matPropes.size());
            for (int pindex = 0; pindex < matPropes.size(); pindex++)
            {
                const FbxSurfaceMaterial* fbxMat = reinterpret_cast<FbxSurfaceMaterial*>(voidPtr);
                auto tex = ImportTexture(fbxMat, matPropes[pindex].first, textSaveDir, assetFilePath, matTextCompressions[pindex]);
                if (tex && tex->GetClassID() == ClassID(Texture2D))
                {
                    matTexs[pindex] = tex;
                }
            }
        }

        for (UInt64 i = 0; i < matPropes.size(); i++)
        {
            Texture2DArrayPtr textPtr;
            if (const auto& textPtrProp = matRes->GetProperty(matPropes[i].second))
            {
                textPtr = TypeCast<resource::Texture2DArray>(*std::get_if<TexturePtr>(textPtrProp));
            }
            if (!textPtr)
                continue;

            for (const auto& [matId, matTexs] : matTextureArray)
            {
                int texIdx = textPtr->GetTextureIndex(matTexs[0]->GetName());
                for (int pindex = 0; pindex < 4; pindex++)
                {
                    importScene.MatIdxToTexIds[matId][pindex] = texIdx;
                }
            }
        }

        for (auto& binding : bindings)
        {
            binding.AddMaterialPath(mCityImporter->CityMaterial);
            for (const auto& meshDescIndex : binding.GetMeshDescIndices())
            {
                meshDescs[meshDescIndex].SetMaterialPath(mCityImporter->CityMaterial);
            }
        }
    }
    
    TextureCompression FBXCityImporter::GetTextureComporessionByMatProperty(const std::string& property)
    {
        if (property == std::string(FbxSurfaceMaterial::sDiffuse))
            return mCityImporter->BaseMapCompression;
        if (property == std::string(FbxSurfaceMaterial::sNormalMap))
            return mCityImporter->NormalMapCompression;
        if (property == "ShininessExponent")
            return mCityImporter->MaskMapCompression;
        if (property == std::string(FbxSurfaceMaterial::sEmissive))
            return mCityImporter->EmissiveMapCompression;
        return TextureCompression::BC1;
    }
    
    ImportColorSpace FBXCityImporter::GetTextureColorSpaceByComporession(TextureCompression compression)
    {
        if (compression == TextureCompression::BC4 || compression == TextureCompression::BC5 || compression == TextureCompression::BC6H)
        {
            return ImportColorSpace::Linear;
        }
        return ImportColorSpace::SRGB;
    }
}
}   // namespace cross