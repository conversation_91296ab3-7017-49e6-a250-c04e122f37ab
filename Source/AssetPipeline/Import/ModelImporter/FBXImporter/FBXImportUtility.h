#pragma once

#include <fbxsdk.h>
#include "AssetPipeline/Import/ModelImporter/FBXImporter/FBXImportData.h"
#include "AssetPipeline/Import/ModelImporter/ModelImportSettings.h"
namespace cross::editor
{
    class FBXMaterialInfo
    {
    public:
        FBXMaterialInfo() : pFbxSurfaceMaterial(0), pFbxTexture(0), pNormalMap(0), bHasTransparencyTexture(false) {}

        friend bool operator < (const FBXMaterialInfo& lhs, const FBXMaterialInfo& rhs)
        {
            if (lhs.pFbxSurfaceMaterial != rhs.pFbxSurfaceMaterial)
            {
                return lhs.pFbxSurfaceMaterial < rhs.pFbxSurfaceMaterial;
            }
            else if (lhs.pFbxTexture != rhs.pFbxTexture)
            {
                return lhs.pFbxTexture < rhs.pFbxTexture;
            }
            else if (lhs.pNormalMap != rhs.pNormalMap)
            {
                return lhs.pNormalMap < rhs.pNormalMap;
            }
            else
            {
                return lhs.bHasTransparencyTexture < rhs.bHasTransparencyTexture;
            }
        }

    public:
        FbxSurfaceMaterial* pFbxSurfaceMaterial;
        FbxTexture* pFbxTexture;
        FbxTexture* pNormalMap;
        bool bHasTransparencyTexture;
    };

    typedef std::map<FBXMaterialInfo, int> FBXMaterialLookup;

    // Creates a vertex -> connected faces list
    // Every vertex contains a list of faces that use it
    class ConnectedMesh
    {
    public:
        struct Vertex
        {
            UInt32* faces;
            int  faceCount;
        };

        std::vector<Vertex> vertices;

        ConnectedMesh(int vertexCount, const UInt32* indices, int indexCount, const UInt32* faceSizes, int faceCount);

        ~ConnectedMesh()
        {
            delete[] m_FaceAllocator;
        }

    private:
        UInt32* m_FaceAllocator;
    };

    void AddVertexByIndex(const FBXImportMesh& srcMesh, FBXImportMesh& dstMesh, int srcVertexIndex);
    void AddPolygonAttribute(const FBXImportMesh& srcMesh, FBXImportMesh& dstMesh, int srcAttributeIndex);

    struct SplitMeshImplementation
    {
        float normalDotAngle;
        float uvEpsilon;
        const FBXImportMesh& srcMesh;
        FBXImportMesh& dstMesh;

        SplitMeshImplementation(const FBXImportMesh& src, FBXImportMesh& dst, float splitAngle)
            : srcMesh(src),
            dstMesh(dst)
        {
            uvEpsilon = 0.001F;
            normalDotAngle = cos(Deg2Rad(splitAngle)) - 0.001F; // NOTE: subtract is for more consistent results across platforms
            PerformSplit();
        }

        inline void CopyVertexAttributes(int wedgeIndex, int dstIndex)
        {
            if (!srcMesh.normals.empty())
                dstMesh.normals[dstIndex] = srcMesh.normals[wedgeIndex];
            if (!srcMesh.colors.empty())
                dstMesh.colors[dstIndex] = srcMesh.colors[wedgeIndex];
            if (!srcMesh.uvs[0].empty())
                dstMesh.uvs[0][dstIndex] = srcMesh.uvs[0][wedgeIndex];
            if (!srcMesh.uvs[1].empty())
                dstMesh.uvs[1][dstIndex] = srcMesh.uvs[1][wedgeIndex];
            if (!srcMesh.tangents.empty())
                dstMesh.tangents[dstIndex] = srcMesh.tangents[wedgeIndex];

            for (size_t i = 0; i < srcMesh.shapes.size(); ++i)
            {
                const FBXImportBlendShape& srcShape = srcMesh.shapes[i];
                FBXImportBlendShape& dstShape = dstMesh.shapes[i];

                if (!srcShape.normals.empty())
                    dstShape.normals[dstIndex] = srcShape.normals[wedgeIndex];
                if (!srcShape.tangents.empty())
                    dstShape.tangents[dstIndex] = srcShape.tangents[wedgeIndex];
            }
        }

        inline void AddVertex(int srcVertexIndex, int srcAttributeIndex)
        {
            AddVertexByIndex(srcMesh, dstMesh, srcVertexIndex);
            AddPolygonAttribute(srcMesh, dstMesh, srcAttributeIndex);
        }

        inline bool NeedTangentSplit(const AssetMath::Vector4f& lhs, const AssetMath::Vector4f& rhs)
        {
#if USE_DXMATH == 1
            AssetMath::Vec4f l(lhs.x(), lhs.y(), lhs.z(), lhs.w());
            AssetMath::Vec4f r(rhs.x(), rhs.y(), rhs.z(), rhs.w());
            if (l.block<3, 1>(0, 0).dot(r.block<3, 1>(0, 0)) < normalDotAngle)
                return true;
            return !CompareApproximately(lhs.w(), rhs.w());
#else
            if (lhs.XYZ().Dot(rhs.XYZ()) < normalDotAngle)
                return true;
            return !CompareApproximately(lhs.w, rhs.w);
#endif
        }

        inline bool NeedsSplitAttributes(int srcIndex, int dstIndex)
        {
            if (!srcMesh.normals.empty() && srcMesh.normals[srcIndex].Dot(dstMesh.normals[dstIndex]) < normalDotAngle)
                return true;
            if (!srcMesh.colors.empty() && srcMesh.colors[srcIndex] != dstMesh.colors[dstIndex])
                return true;
            if (!srcMesh.uvs[0].empty() && !cross::math::isApprox(srcMesh.uvs[0][srcIndex], dstMesh.uvs[0][dstIndex], uvEpsilon))
                return true;
            if (!srcMesh.uvs[1].empty() && !cross::math::isApprox(srcMesh.uvs[1][srcIndex], dstMesh.uvs[1][dstIndex], uvEpsilon))
                return true;
            if (!srcMesh.tangents.empty() && NeedTangentSplit(srcMesh.tangents[srcIndex], dstMesh.tangents[dstIndex]))
                return true;

            for (size_t i = 0; i < srcMesh.shapes.size(); ++i)
            {
                const FBXImportBlendShape& srcShape = srcMesh.shapes[i];
                const FBXImportBlendShape& dstShape = dstMesh.shapes[i];

                if (!srcShape.normals.empty() && srcShape.normals[srcIndex].Dot(dstShape.normals[dstIndex]) < normalDotAngle)
                    return true;

                if (!srcShape.tangents.empty() && srcShape.tangents[srcIndex].Dot(dstShape.tangents[dstIndex]) < normalDotAngle)
                    return true;
            }

            return false;
        }

        // Wedge = a per face vertex. The source mesh has all uvs, normals etc. stored as wedges. Thus srcMesh.normals.size = srcMesh.polygons.size.

        void PerformSplit()
        {
            const int attributeCount = (int)srcMesh.polygons.size();
            dstMesh.Reserve(attributeCount, (int)srcMesh.polygonSizes.size(), &srcMesh);

            // Initialize faces to source faces
            dstMesh.polygons = srcMesh.polygons;
            dstMesh.polygonSizes = srcMesh.polygonSizes;
            dstMesh.hasAnyQuads = srcMesh.hasAnyQuads;

            // Initialize other data
            dstMesh.vertices = srcMesh.vertices;
            dstMesh.skin = srcMesh.skin;
            dstMesh.materials = srcMesh.materials;
            dstMesh.name = srcMesh.name;
            dstMesh.shapeChannels = srcMesh.shapeChannels;
            dstMesh.bones = srcMesh.bones;

            // Initialize attributes to some sane default values
            if (!srcMesh.normals.empty())
                dstMesh.normals.resize(srcMesh.vertices.size(), AssetMath::Vector3f::One());
            if (!srcMesh.colors.empty())
                dstMesh.colors.resize(srcMesh.vertices.size(), ColorRGBA32(0xFFFFFFFF));
            if (!srcMesh.uvs[0].empty())
                dstMesh.uvs[0].resize(srcMesh.vertices.size(), AssetMath::Vector2f::Zero());
            if (!srcMesh.uvs[1].empty())
                dstMesh.uvs[1].resize(srcMesh.vertices.size(), AssetMath::Vector2f::Zero());
            if (!srcMesh.tangents.empty())
                dstMesh.tangents.resize(srcMesh.vertices.size(), AssetMath::Vector4f{ 1.0f, 0.0f, 0.0f, 0.0F });

            dstMesh.shapes.resize(srcMesh.shapes.size());
            for (size_t i = 0; i < srcMesh.shapes.size(); ++i)
            {
                const FBXImportBlendShape& srcShape = srcMesh.shapes[i];
                FBXImportBlendShape& dstShape = dstMesh.shapes[i];

                dstShape.targetWeight = srcShape.targetWeight;
                dstShape.vertices = srcShape.vertices;
                if (!srcShape.normals.empty())
                    dstShape.normals.resize(srcShape.vertices.size(), AssetMath::Vector3f{ 0.0f, 0.0f, 1.0f });
                if (!srcShape.tangents.empty())
                    dstShape.tangents.resize(srcShape.vertices.size(), AssetMath::Vector3f{ 1.0f, 0.0f, 0.0f });
            }

            typedef std::list<int> AlreadySplitVertices;
            std::vector<AlreadySplitVertices> splitVertices;
            splitVertices.resize(srcMesh.vertices.size());

            UInt32* indices = &dstMesh.polygons[0];
            int indexCount = (int)dstMesh.polygons.size();
            for (int i = 0; i < indexCount; ++i)
            {
                int vertexIndex = indices[i];

                // Go through the list of already assigned vertices and find the vertex with the same attributes
                int bestVertexSplitIndex = -1;
                AlreadySplitVertices& possibilities = splitVertices[vertexIndex];
                for (AlreadySplitVertices::iterator s = possibilities.begin(); s != possibilities.end(); s++)
                {
                    if (!NeedsSplitAttributes(i, *s))
                    {
                        bestVertexSplitIndex = *s;
                    }
                }

                // No vertex was found that could be reused!
                if (bestVertexSplitIndex == -1)
                {
                    // We haven't visited this vertex at all!
                    // Use the original vertex and replace the vertex attribute's with the current wedge attributes
                    if (possibilities.empty())
                    {
                        bestVertexSplitIndex = vertexIndex;
                        CopyVertexAttributes(i, vertexIndex);
                    }
                    // We need to add a new vertex
                    else
                    {
                        bestVertexSplitIndex = (int)dstMesh.vertices.size();
                        AddVertex(vertexIndex, i);
                    }

                    // Add the vertex to the possible vertices which other wedges can share!
                    possibilities.push_back(bestVertexSplitIndex);
                }

                indices[i] = bestVertexSplitIndex;
            }
        }
    };

    // Wrapper for getting data from FbxLayerElementTemplate when reference mode is FbxLayerElement::eDirect
    template <class T, class T2>
    class GetValueAtDirect
    {
    public:
        GetValueAtDirect(FbxLayerElementTemplate<T>& element)
            : input(&element.GetDirectArray()),
            directArraySize(input->GetCount())
        {
        }

        bool GetValue(int index, T2& value) const
        {
            if (index < 0 || index >= directArraySize)
            {
                return false;
            }

            value = FBXToBasicType(input->GetAt(index));
            return true;
        }

        int GetDirectArraySize() const { return directArraySize; }

        bool IsValid() const { return GetSize() > 0; }
        int GetSize() const { return directArraySize; }

    private:
        FbxLayerElementArrayTemplate<T>* input;
        int directArraySize;
    };

    // Wrapper for getting data from FbxLayerElementTemplate when reference mode is FbxLayerElement::eIndexToDirect
    template <class T, class T2>
    class GetValueAtIndex : public GetValueAtDirect<T, T2>
    {
    public:
        GetValueAtIndex(FbxLayerElementTemplate<T>& element)
            : GetValueAtDirect<T, T2>(element),
            layerIndices(&element.GetIndexArray()),
            indexArraySize(layerIndices->GetCount())
        {
        }

        bool GetValue(int index, T2& value) const
        {
            if (index < 0 || index >= indexArraySize)
                return false;

            const int index2 = layerIndices->GetAt(index);
            return GetValueAtDirect<T, T2>::GetValue(index2, value);
        }

        bool IsValid() const { return GetValueAtDirect<T, T2>::IsValid() && GetSize() > 0; }
        int GetSize() const { return indexArraySize; }

    private:
        FbxLayerElementArrayTemplate<int>* layerIndices;
        int indexArraySize;
    };

    class NormalCalculation
    {
    private:
        struct VertexNormalGroup
        {
            int smoothingGroup;
            AssetMath::Vector3f normal;
            int vertexCount;

            VertexNormalGroup() : smoothingGroup(0), normal(AssetMath::Vector3f::Zero()), vertexCount(0) {}

            void Add(int sg, const AssetMath::Vector3f& n, int vc)
            {
                smoothingGroup |= sg;
                normal += n;
                vertexCount += vc;
            }

            void Add(int sg, const AssetMath::Vector3f& n) { Add(sg, n, 1); }
            void Add(const VertexNormalGroup& g) { Add(g.smoothingGroup, g.normal, g.vertexCount); }

            void Normalize()
            {
                normal.Normalize();
            }
        };

        struct SmoothVertex
        {
            std::vector<VertexNormalGroup> groups;

            void Add(int smoothingGroup, const AssetMath::Vector3f& normal)
            {
                bool found = false;

                for (size_t i = 0; i < groups.size(); ++i)
                {
                    if (groups[i].smoothingGroup & smoothingGroup)
                    {
                        const bool hasExtraGroups = (smoothingGroup & ~groups[i].smoothingGroup) != 0;

                        if (hasExtraGroups)
                        {
                            for (size_t j = i + 1; j < groups.size(); ++j)
                            {
                                if (groups[j].smoothingGroup & smoothingGroup)
                                {
                                    groups[i].Add(groups[j]);
                                    groups.erase(groups.begin() + j);
                                    --j;
                                }
                            }
                        }

                        groups[i].Add(smoothingGroup, normal);

                        found = true;
                        break;;
                    }
                }

                if (!found)
                {
                    VertexNormalGroup group;
                    group.Add(smoothingGroup, normal);

                    groups.push_back(group);
                }
            }

            void Normalize()
            {
                for (size_t i = 0; i < groups.size(); ++i)
                {
                    groups[i].Normalize();
                }
            }

            AssetMath::Vector3f FindNormal(const int smoothingGroup)
            {
                for (size_t i = 0; i < groups.size(); ++i)
                {
                    if (groups[i].smoothingGroup & smoothingGroup)
                    {
                        for (size_t j = i + 1; j < groups.size(); ++j)
                        {
                            AssertIf(groups[j].smoothingGroup & smoothingGroup);
                        }

                        return (AssetMath::Vector3f)groups[i].normal;
                    }
                }

                AssertMsg(false, "Couldn't find normal for smoothingGroup {}", smoothingGroup);
                return AssetMath::Vector3f::Zero();
            }
        };

    public:
        static void CalculateNormalsFromSmoothingGroups(const std::vector<UInt32>& polygonSizes, const std::vector<UInt32>& polygons, const std::vector<int>& smoothingInfo, const std::vector<AssetMath::Vector3f>& vertices, std::vector<AssetMath::Vector3f>& normals)
        {
            //std::vector<Vector3f> normals;
            //normals.resize(mesh.polygons.size(), Vector3f::zero);

            std::vector<SmoothVertex> smoothNormals;
            smoothNormals.resize(vertices.size());

            normals.resize(polygons.size(), AssetMath::Vector3f::Zero());

            int index = 0;
            for (size_t p = 0; p < polygonSizes.size(); ++p)
            {
                const int polygonSize = polygonSizes[p];
                for (int i = 0; i < polygonSize; ++i)
                {
                    int prev = (i > 0 ? i - 1 : polygonSize - 1);
                    int next = (i < polygonSize - 1 ? i + 1 : 0);

                    int currentVertex = polygons[index + i];

                    AssetMath::Vector3f v0 = vertices[polygons[index + prev]];
                    AssetMath::Vector3f v1 = vertices[currentVertex];
                    AssetMath::Vector3f v2 = vertices[polygons[index + next]];

                    //Vector3f normal = NormalizeSafe(Cross(v0 - v1, v2 - v1));
                    AssetMath::Vector3f normal = (v0 - v1).Cross(v2 - v1).Normalized();
                    smoothNormals[currentVertex].Add(smoothingInfo[index + i], normal);

                    // we will use it if it has no smoothing group at all
                    normals[index + i] = normal;
                }

                index += polygonSize;
            }

            for (size_t i = 0; i < smoothNormals.size(); ++i)
            {
                smoothNormals[i].Normalize();
            }

            index = 0;
            for (size_t p = 0; p < polygonSizes.size(); ++p)
            {
                const int polygonSize = polygonSizes[p];
                for (int i = 0; i < polygonSize; ++i)
                {
                    // if vertex has smoothing group, then find it
                    if (smoothingInfo[index + i] != 0)
                    {
                        int currentVertex = polygons[index + i];

                        normals[index + i] = smoothNormals[currentVertex].FindNormal(smoothingInfo[index + i]);
                    }
                }

                index += polygonSize;
            }
        }
    };

    struct SharedMeshInfo
    {
        int nIndex;
        std::vector<FbxNode*> vecFbxNodes;
    };
    typedef std::map<FbxMesh*, SharedMeshInfo> FBXMeshToSharedMeshInfoMap;

    void ReportError(const char* format, ...);
    void ReportWarning(const char* format, ...);

    const char* GetImportError();
    const char* GetImportWarnings();

    void GenerateSecondaryUVSetImportMesh(FBXImportMesh& originalMesh, const ModelImportSettings& settings, std::vector<std::string>& warnings, std::string& error);
}
