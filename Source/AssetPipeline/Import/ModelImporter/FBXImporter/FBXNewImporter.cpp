#if 0 
#include "EnginePrefix.h"
#include "FBXNewImporter.h"

#include <queue>
#include <iostream>
#include <fstream>

#include "FBXAuxiliary.h"
#include "FBXLayerConvert.h"
#include "FBXConversion.h"
#include "FBXAnimation.h"
#include "FBXConstraints.h"
#include "FBXImportUtility.h"
#include "External/Tristripper/Adjacency.h"
#include "External/mikktspace/mikktspace.h"
#include "AssetPipeline/Utils/AssetMath.h"
#include "AssetPipeline/Utils/AnimationCompression/AnimCompress.h"
#include "CECommon/Common/EngineGlobal.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "PhysicsEngine/PhysicsCooker.h"

namespace cross { namespace editor {
    FBXNewImporter::FBXNewImporter()
        : mFbxSdkManager(nullptr)
        , mFbxImportErrors()
        , mFbxImportWarnings()
        , mFBXNodeMap()
        , mFbxNodeArray()
    {
    }

    bool FBXNewImporter::ImportAsset(const std::string& assetFilename, const ModelImportSettings& settings, const std::string& ndaSavePath)
    {
        mImportSettings = settings;

        if (assetFilename.empty())
            return false;

        std::string extension = ToLower(GetPathNameExtension(assetFilename));
        // use fbxsdk to load normal obj
        if (extension != "fbx" && extension != "obj")
            return false;

        mAssetFileName = assetFilename;
        mNdaSavePath = ndaSavePath;
        PathHelper::Normalize(mAssetFileName);
        PathHelper::Normalize(mNdaSavePath);

        fbxsdk::FbxScene* pFbxScene = CreateFbxScene(assetFilename);
        FillDataArrays(pFbxScene);

        ImportScene importScene;
        ImportAssetData(assetFilename, importScene);

        // calculate meshes bound
        auto& meshesData = importScene.Meshes.meshesdata;
        for (auto& meshData : meshesData)
        {
            BuildCollisionTree(*meshData);
            //if (mImportSettings.GenerateSecondaryUV)
            //    GenerateLightmapUv(*meshData);
        }

        // maxwan
        mMeshAssetData = std::make_shared<MeshAssetData>();

        //ImportMaterial(assetFilename, ndaSavePath);

        std::string meshname = PathHelper::GetBaseFileName(assetFilename, true);
        mMeshAssetData->SetName(meshname);
        mMeshAssetData->InitByMeshData(importScene, settings.ImportUseFullPrecisionUV, settings.ImportUseFullPrecisionNormal);

        if (settings.GenerateClusters)
        {
            mMeshAssetData->GenerateMeshPartCluster();
        }

        if(settings.IsStreamFile)
        {
            mMeshAssetData->SetIsStreamFile(true);
        }

        if (!mMeshAssetData->SerializeToFlatbufferFile(ndaSavePath))
        {
            LOG_ERROR("[FBXNewImporter] Import model [ {} ] failed.", assetFilename);
            return false;
        }

        return true;
    }

    void FBXNewImporter::ImportMaterial(const std::string& assetFilename, const std::string& ndaSavePath)
    {
        // std::unique_ptr<TextureImporter> importer = std::make_unique<TextureImporter>();
        // std::string texPath = PathHelper::GetEngineResourceDirectoryPath() + "/Texture";
        // LOG_ERROR("[FBXNewImporter] ImportMaterial [ {} ]", texPath);
        /*
        unsigned int matCount = static_cast<int>(mFbxImportScene->materials.size());
        for (unsigned int i = 0; i < matCount; i++)
        {
            FBXImportMaterial&         fbxImporterMat = mFbxImportScene->materials[i];
            std::shared_ptr<FBXMaterial>  material = std::make_shared<FBXMaterial>();
            assert(fbxImporterMat.pFbxSurfaceMaterial!=NULL);
            FbxSurfaceMaterial* pFbxSurfaceMaterial = (FbxSurfaceMaterial*)fbxImporterMat.pFbxSurfaceMaterial;
            material->SetMaterial(pFbxSurfaceMaterial);
            mMaterialVec.push_back(material);
        }
        */
        // create asset material dirctory
        std::string ndaRootDir = PathHelper::GetDirectoryFromAbsolutePath(ndaSavePath);
        std::string assetFileName = PathHelper::GetBaseFileName(assetFilename);
        std::string materialPath = ndaRootDir + assetFileName;
        if (!PathHelper::IsDirectoryExist(materialPath))
        {
            if (!PathHelper::MakeDirectory(materialPath))
            {
                assert(false);
            }
        }
    }
    void FBXNewImporter::MergeMeshWithMaterial(ImportNode& rootNode, CrossSchema::ImportMeshesT& meshes, const std::map<int, int>& meshMaterialMap, int materialCount)
    {
        // do nothing for one material mesh
        if (meshMaterialMap.size() < 2)
        {
            return;
        }

        std::stack<ImportNode*> nodeStack;
        nodeStack.push(&rootNode);
        std::vector<std::unique_ptr<CrossSchema::ImportMeshDataT>> newMeshData;
        std::vector<ImportNode> newNode;
        std::vector<CrossSchema::ImportMeshSkinT> newMeshSkin;
        std::vector<std::vector<int>> newUsedBones;

        newNode.resize(materialCount);
        for (int i = 0; i < materialCount; i++)
        {
            std::unique_ptr<CrossSchema::ImportMeshDataT> newMesh(new CrossSchema::ImportMeshDataT);
            newMesh->vertexdata = std::make_unique<CrossSchema::MeshVertexDescriptionT>();
            newMesh->vertexdata->pos = std::make_unique<CrossSchema::float3attributeT>();
            newMesh->vertexdata->pos->name = std::string("pos");
            newMesh->indices = std::make_unique<CrossSchema::IndiceDataT>();
            newMeshData.push_back(std::move(newMesh));
        }
        newMeshSkin.resize(materialCount);
        newUsedBones.resize(materialCount);

        while (!nodeStack.empty())
        {
            ImportNode* currNode = nodeStack.top();
            nodeStack.pop();
#if USE_DXMATH != 1
            AssetMath::Matrix4x4f transMatrix = currNode->LocalTransform;
            AssetMath::Transform3f transform{transMatrix};
            AssetMath::Transform3f normalTransform{transMatrix.inverse().transpose()};
#endif
            for (auto& childNode : currNode->Children)
            {
                childNode.LocalTransform *= currNode->LocalTransform;
                nodeStack.push(&childNode);
            }

            if (currNode->MeshIndex == -1)
            {
                continue;
            }

            if (currNode->MeshIndex >= meshMaterialMap.size())
            {
                // assert(false);
                continue;
            }

            if (meshMaterialMap.find(currNode->MeshIndex) == meshMaterialMap.end())
            {
                continue;
            }

            int materialId = meshMaterialMap.at(currNode->MeshIndex);
            auto& node = newNode[materialId];
            auto& meshData = newMeshData[materialId];
            auto& srcMeshData = meshes.meshesdata[currNode->MeshIndex];

            // first mesh to be merged into this material group
            if (node.MeshIndex == -1)
            {
                node.MeshIndex = materialId;
                node.Name = currNode->Name;
                meshData->name = node.Name;
            }

            size_t preVertexCount{};
            size_t srcVertexCount = 0;

            if (srcMeshData->vertexdata)
            {
                preVertexCount = meshData->vertexdata->pos->data.size();
                srcVertexCount = srcMeshData->vertexdata->pos->data.size();
                if (srcMeshData->vertexdata->pos)
                {
                    meshData->vertexdata->pos->data.insert(meshData->vertexdata->pos->data.end(), srcMeshData->vertexdata->pos->data.begin(), srcMeshData->vertexdata->pos->data.end());
                }
                if (srcMeshData->vertexdata->normal)
                {
                    if (!meshData->vertexdata->normal)
                    {
                        meshData->vertexdata->normal = std::make_unique<CrossSchema::float3attributeT>();
                        meshData->vertexdata->normal->name = std::string("normal");
                    }
                    meshData->vertexdata->normal->data.insert(meshData->vertexdata->normal->data.end(), srcMeshData->vertexdata->normal->data.begin(), srcMeshData->vertexdata->normal->data.end());
                }
                if (srcMeshData->vertexdata->color)
                {
                    if (!meshData->vertexdata->color)
                    {
                        meshData->vertexdata->color = std::make_unique<CrossSchema::uintattributeT>();
                        meshData->vertexdata->color->name = std::string("normal");
                    }
                    meshData->vertexdata->color->data.insert(meshData->vertexdata->color->data.end(), srcMeshData->vertexdata->color->data.begin(), srcMeshData->vertexdata->color->data.end());
                }
                if (srcMeshData->vertexdata->uv0)
                {
                    if (!meshData->vertexdata->uv0)
                    {
                        meshData->vertexdata->uv0 = std::make_unique<CrossSchema::float2attributeT>();
                        meshData->vertexdata->uv0->name = std::string("uv0");
                    }
                    meshData->vertexdata->uv0->data.insert(meshData->vertexdata->uv0->data.end(), srcMeshData->vertexdata->uv0->data.begin(), srcMeshData->vertexdata->uv0->data.end());
                }
                if (srcMeshData->vertexdata->uv1)
                {
                    if (!meshData->vertexdata->uv1)
                    {
                        meshData->vertexdata->uv1 = std::make_unique<CrossSchema::float2attributeT>();
                        meshData->vertexdata->uv1->name = std::string("uv1");
                    }
                    meshData->vertexdata->uv1->data.insert(meshData->vertexdata->uv1->data.end(), srcMeshData->vertexdata->uv1->data.begin(), srcMeshData->vertexdata->uv1->data.end());
                }
                if (srcMeshData->vertexdata->uv2)
                {
                    if (!meshData->vertexdata->uv2)
                    {
                        meshData->vertexdata->uv2 = std::make_unique<CrossSchema::float2attributeT>();
                        meshData->vertexdata->uv2->name = std::string("uv2");
                    }
                    meshData->vertexdata->uv2->data.insert(meshData->vertexdata->uv2->data.end(), srcMeshData->vertexdata->uv2->data.begin(), srcMeshData->vertexdata->uv2->data.end());
                }
                if (srcMeshData->vertexdata->uv3)
                {
                    if (!meshData->vertexdata->uv3)
                    {
                        meshData->vertexdata->uv3 = std::make_unique<CrossSchema::float2attributeT>();
                        meshData->vertexdata->uv3->name = std::string("uv3");
                    }
                    meshData->vertexdata->uv3->data.insert(meshData->vertexdata->uv3->data.end(), srcMeshData->vertexdata->uv3->data.begin(), srcMeshData->vertexdata->uv3->data.end());
                }
                if (srcMeshData->vertexdata->tangent)
                {
                    if (!meshData->vertexdata->tangent)
                    {
                        meshData->vertexdata->tangent = std::make_unique<CrossSchema::float4attributeT>();
                        meshData->vertexdata->tangent->name = std::string("tangent");
                    }
                    meshData->vertexdata->tangent->data.insert(meshData->vertexdata->tangent->data.end(), srcMeshData->vertexdata->tangent->data.begin(), srcMeshData->vertexdata->tangent->data.end());
                }
                if (srcMeshData->vertexdata->binormal)
                {
                    if (!meshData->vertexdata->binormal)
                    {
                        meshData->vertexdata->binormal = std::make_unique<CrossSchema::float4attributeT>();
                        meshData->vertexdata->binormal->name = std::string("binormal");
                    }
                    meshData->vertexdata->binormal->data.insert(meshData->vertexdata->binormal->data.end(), srcMeshData->vertexdata->binormal->data.begin(), srcMeshData->vertexdata->binormal->data.end());
                }
                if (srcMeshData->vertexdata->boneweights)
                {
                    if (!meshData->vertexdata->boneweights)
                    {
                        meshData->vertexdata->boneweights = std::make_unique<CrossSchema::float4attributeT>();
                        meshData->vertexdata->boneweights->name = std::string("boneweights");
                    }
                    meshData->vertexdata->boneweights->data.insert(meshData->vertexdata->boneweights->data.end(), srcMeshData->vertexdata->boneweights->data.begin(), srcMeshData->vertexdata->boneweights->data.end());
                }
                if (srcMeshData->vertexdata->boneids)
                {
                    if (!meshData->vertexdata->boneids)
                    {
                        meshData->vertexdata->boneids = std::make_unique<CrossSchema::uint4attributeT>();
                        meshData->vertexdata->boneids->name = std::string("boneids");
                    }
                    meshData->vertexdata->boneids->data.insert(meshData->vertexdata->boneids->data.end(), srcMeshData->vertexdata->boneids->data.begin(), srcMeshData->vertexdata->boneids->data.end());
                }
            }
            else
            {
                Assert(false);
            }

            size_t newVertexCount = meshData->vertexdata->pos->data.size();
            UInt32 preIndicesCount = meshData->indices->indicescount;
            UInt32 newIndicesCount = preIndicesCount + srcMeshData->indices->indicescount;
            meshData->indices->indicescount = newIndicesCount;
            UInt16* srcMeshIndicesData = reinterpret_cast<UInt16*>(srcMeshData->indices->indexbuffer.data());

            if (preVertexCount <= (std::numeric_limits<UInt16>::max)() && newVertexCount > (std::numeric_limits<UInt16>::max)())
            {
                std::vector<UInt8> newIndices;
                newIndices.resize(preIndicesCount * sizeof(UInt32));
                UInt32* newIndicesData = reinterpret_cast<UInt32*>(newIndices.data());
                UInt16* oldIndicesData = reinterpret_cast<UInt16*>(meshData->indices->indexbuffer.data());
                for (UInt32 i = 0; i < preIndicesCount; i++)
                {
                    newIndicesData[i] = oldIndicesData[i];
                }
                meshData->indices->indexbuffer = std::move(newIndices);
            }

            size_t indicesTypeSize = newVertexCount > (std::numeric_limits<UInt16>::max)() ? sizeof(UInt32) : sizeof(UInt16);
            meshData->indices->indexbuffer.resize(indicesTypeSize * newIndicesCount);
            if (indicesTypeSize == sizeof(UInt16))
            {
                UInt16* indicesData = reinterpret_cast<UInt16*>(meshData->indices->indexbuffer.data());
                for (UInt32 i = preIndicesCount; i < newIndicesCount; i++)
                {
                    // This object is not submesh, we are just merging mesh with same materials, so we need to add the vertex offset
                    // After merged, the meshData become a submesh
                    // In ImportMeshAssetData.h InitByMeshData(), we collect all the submesh into one bv/ib where we DO NOT add the vertex offset on index and we only record the vertex offset
                    indicesData[i] = srcMeshIndicesData[i - preIndicesCount] + (UInt16)preVertexCount;
                }
            }
            else
            {
                UInt32* indicesData = reinterpret_cast<UInt32*>(meshData->indices->indexbuffer.data());
                for (UInt32 i = preIndicesCount; i < newIndicesCount; i++)
                {
                    // This object is not submesh, we are just merging mesh with same materials, so we need to add the vertex offset
                    // After merged, the meshData become a submesh
                    // In ImportMeshAssetData.h InitByMeshData(), we collect all the submesh into one bv/ib where we DO NOT add the vertex offset on index and we only record the vertex offset

                    indicesData[i] = (UInt32)srcMeshIndicesData[i - preIndicesCount] + (UInt32)preVertexCount;
                }
            }

            // invalidate srcMesh Info after  mesh merged into corresponding material group
            currNode->MeshIndex = -1;
            ////WTF is this code??????????????

            srcMeshData.reset();
        }

        auto isBoneNode = [](const ImportNode& node) -> bool {
            std::stack<ImportNode> nodes;
            nodes.push(node);
            while (!nodes.empty())
            {
                auto& topNode = nodes.top();
                nodes.pop();
                if (topNode.MeshIndex != -1)
                {
                    return false;
                }
                for (auto& childNode : topNode.Children)
                {
                    nodes.push(childNode);
                }
            }
            return true;
        };

        /* for (auto srcNode : rootNode.Children)
         {
             if (srcNode.MeshIndex == -1 && (srcNode.Children.size() == 0 || isBoneNode(srcNode)))
                 newNode.push_back(srcNode);
         }*/

        rootNode.Children = std::move(newNode);
        meshes.rootnode->children.clear();
        FBXAuxiliary::buildFBNode(rootNode, *meshes.rootnode);
        meshes.meshesdata.swap(newMeshData);
    }

    bool FBXNewImporter::CheckAssetName(const char* name) const { return HasExtension(name, ".fbx") || HasExtension(name, ".obj"); }

    namespace {
        static void ImportVerticesFromFBX(FBXImportMesh const& mesh, CrossSchema::MeshVertexDescriptionT& vertices, std::vector<std::unique_ptr<CrossSchema::ImportVertexBoneT>>& bones)
        {
            for (int i = 0; i < mesh.vertices.size(); i++)
                bones.emplace_back(std::unique_ptr<CrossSchema::ImportVertexBoneT>(new CrossSchema::ImportVertexBoneT));
            bones.resize(mesh.vertices.size());
            std::vector<AssetMath::Vec4f> tangs;   // temp for serialization
            std::vector<UInt32> colors;
            if (!mesh.colors.empty())
            {
                std::transform(mesh.colors.begin(), mesh.colors.end(), std::back_inserter(colors), [](ColorRGBA32 c) -> UInt32 { return c.AsUInt32(); });
            }
            if (!mesh.tangents.empty())
            {
                std::transform(mesh.tangents.begin(), mesh.tangents.end(), std::back_inserter(tangs), [](AssetMath::Vector4f c) -> AssetMath::Vec4f { return static_cast<AssetMath::Vec4f>(c); });
            }
            for (UInt32 countVertex = 0; countVertex < mesh.vertices.size(); ++countVertex)
            {
                if (!mesh.skin.empty())
                {
                    bones[countVertex]->boneindices = std::vector<int>(mesh.skin[countVertex].BoneIndices.begin(), mesh.skin[countVertex].BoneIndices.end());
                    bones[countVertex]->weights = std::vector<float>(mesh.skin[countVertex].Weights.begin(), mesh.skin[countVertex].Weights.end());
                }
            }

            CrossSchema::float3attributeT position;
            position.name = std::string("pos");
            std::transform(mesh.vertices.begin(), mesh.vertices.end(), std::back_inserter(position.data), [](AssetMath::Vector3f c) -> CrossSchema::float3 { return CrossSchema::float3(c.x(), c.y(), c.z()); });
            vertices.pos = std::make_unique<CrossSchema::float3attributeT>(position);

            if (!mesh.normals.empty())
            {
                CrossSchema::float3attributeT normal;
                normal.name = std::string("normal");
                std::transform(mesh.normals.begin(), mesh.normals.end(), std::back_inserter(normal.data), [](AssetMath::Vector3f c) -> CrossSchema::float3 { return CrossSchema::float3(c.x(), c.y(), c.z()); });
                vertices.normal = std::make_unique<CrossSchema::float3attributeT>(normal);
            }
            // tangents
            if (!mesh.tangents.empty())
            {
                CrossSchema::float4attributeT tangent;
                tangent.name = std::string("tangent");
                std::transform(mesh.tangents.begin(), mesh.tangents.end(), std::back_inserter(tangent.data), [](AssetMath::Vector4f c) -> CrossSchema::float4 { return CrossSchema::float4(c.x(), c.y(), c.z(), c.w()); });
                vertices.tangent = std::make_unique<CrossSchema::float4attributeT>(tangent);
            }
            // colors
            if (!mesh.colors.empty())
            {
                CrossSchema::uintattributeT col;
                col.name = std::string("col");
                col.data = colors;
                vertices.color = std::make_unique<CrossSchema::uintattributeT>(col);
            }
            // UV 1
            if (!mesh.uvs[0].empty())
            {
                CrossSchema::float2attributeT uv0;
                uv0.name = std::string("uv0");
                std::transform(mesh.uvs[0].begin(), mesh.uvs[0].end(), std::back_inserter(uv0.data), [](AssetMath::Vec2f c) -> CrossSchema::float2 { return CrossSchema::float2(c.x(), 1.0f - c.y()); });

                vertices.uv0 = std::make_unique<CrossSchema::float2attributeT>(uv0);
            }
            // UV 2
            if (!mesh.uvs[1].empty())
            {
                CrossSchema::float2attributeT uv1;
                uv1.name = std::string("uv1");
                std::transform(mesh.uvs[1].begin(), mesh.uvs[1].end(), std::back_inserter(uv1.data), [](AssetMath::Vec2f c) -> CrossSchema::float2 { return CrossSchema::float2(c.x(), 1.0f - c.y()); });
                vertices.uv1 = std::make_unique<CrossSchema::float2attributeT>(uv1);
            }
            // bone blend weight & id
            if (!mesh.skin.empty())
            {
                CrossSchema::float4attributeT blendweights;
                blendweights.name = std::string("blendweight");
                std::transform(
                    mesh.skin.begin(), mesh.skin.end(), std::back_inserter(blendweights.data), [](auto& elem) -> CrossSchema::float4 { return CrossSchema::float4(elem.Weights[0], elem.Weights[1], elem.Weights[2], elem.Weights[3]); });
                vertices.boneweights = std::make_unique<CrossSchema::float4attributeT>(blendweights);

                CrossSchema::uint4attributeT blendids;
                blendids.name = std::string("blendid");
                std::transform(mesh.skin.begin(), mesh.skin.end(), std::back_inserter(blendids.data), [](auto& elem) -> CrossSchema::uint4 {
                    return CrossSchema::uint4((uint32_t)elem.BoneIndices[0], (uint32_t)elem.BoneIndices[1], (uint32_t)elem.BoneIndices[2], (uint32_t)elem.BoneIndices[3]);
                });
                vertices.boneids = std::make_unique<CrossSchema::uint4attributeT>(blendids);
            }
        }
    }   // namespace

    static void Init(CrossSchema::IndiceDataT& data, UInt32 indicesCount, UInt32 vertexCount)
    {
        if (vertexCount > (std::numeric_limits<UInt16>::max)())
        {
            data.indexbuffer.resize(indicesCount * sizeof(UInt32));
        }
        else
        {
            data.indexbuffer.resize(indicesCount * sizeof(UInt16));
        }
        data.indicescount = indicesCount;
    }

    void FBXNewImporter::InOneFbxNode(CrossSchema::ImportMeshesT& meshes, std::map<int, int>& sceneMeshMaterialMap, std::map<int, int>& meshMaterialMap, ImportScene& iScene, std::vector<ImportNode>& slibingVector, ImportNode& node,
                                      const FBXImportNode& sceneNode)
    {
        std::vector<FBXImportNode> splitNodes;
        bool split = SplitMeshByMaterial(*mFbxImportScene, sceneNode, splitNodes, sceneMeshMaterialMap);
        if (split)
        {
            node.Name = sceneNode.name;
            node.MeshIndex = -1;
            node.LocalTransform.setIdentity();
            node.BoneIndexInImportMesh = -1;
            for (auto i = 0; i < splitNodes.size(); ++i)
            {
                ImportNode& eNode = node.Children.emplace_back();
                const auto& subNode = splitNodes[i];
                InOneFbxNode(meshes, sceneMeshMaterialMap, meshMaterialMap, iScene, node.Children, eNode, subNode);
            }
        }
        else
        {
#if USE_DXMATH != 1
            Transform3f transform{};
            transform.fromPositionOrientationScale(sceneNode.position, sceneNode.rotation, sceneNode.scale);

            node.Name = sceneNode.name;
            node.LocalTransform = transform.matrix();
#else
            node.Name = sceneNode.name;
            node.LocalTransform = AssetMath::Matrix4x4f::Compose(sceneNode.position, sceneNode.rotation, sceneNode.scale);
#endif
            node.MeshIndex = -1;
            node.BoneIndexInImportMesh = iScene.ImportSkeltData.FindBoneIndex(node.Name);
            while (sceneNode.meshIndex != -1)
            {
                auto& m = mFbxImportScene->meshes[sceneNode.meshIndex];
                if (m.vertices.empty() || m.polygons.empty())
                    break;

                std::vector<FBXImportMesh> dstMeshes;
                RemapVertex(m, dstMeshes);

                node.MeshIndex = static_cast<int>(meshes.meshesdata.size());
                meshMaterialMap[node.MeshIndex] = sceneMeshMaterialMap[sceneNode.meshIndex];

                // for fix car fbx import issue, maybe can fix this issue. but it is not a good way.
                if (dstMeshes.size() > 1)
                {
                    for (UInt32 i = node.MeshIndex + 1; i < dstMeshes.size(); ++i)
                    {
                        meshMaterialMap[i] = sceneMeshMaterialMap[sceneNode.meshIndex];
                    }
                }

                for (UInt32 i = 0; i < dstMeshes.size(); ++i)
                {
                    std::unique_ptr<CrossSchema::ImportMeshDataT> data(new CrossSchema::ImportMeshDataT);
                    meshes.meshesdata.push_back(std::move(data));
                    std::unique_ptr<CrossSchema::ImportMeshSkinT> skin(new CrossSchema::ImportMeshSkinT);
                    meshes.meshskininfo.push_back(std::move(skin));
                    auto& eBones = meshes.meshskininfo.back()->vbones;
                    auto& eMesh = meshes.meshesdata.back();
                    std::unique_ptr<CrossSchema::UsedBonesIdT> tmpbone(new CrossSchema::UsedBonesIdT);
                    tmpbone->bones = std::move(dstMeshes[i].bones);
                    meshes.meshusedbones.push_back(std::move(tmpbone));
                    if (dstMeshes.size() > 1)
                    {
                        eMesh->name = dstMeshes[i].name + "_Part" + IntToString(i);
                        if (i > 0)
                        {
                            ImportNode& newNode = slibingVector.emplace_back();
                            newNode.LocalTransform = node.LocalTransform;
                            newNode.MeshIndex = static_cast<int>(meshes.meshesdata.size()) - 1;
                            newNode.Name = eMesh->name;
                        }
                    }
                    else
                    {
                        eMesh->name = dstMeshes[i].name;
                    }

                    // mesh has been triangulated
                    eMesh->indices = std::make_unique<CrossSchema::IndiceDataT>();
                    Init(*eMesh->indices, (UInt32)dstMeshes[i].polygonSizes.size() * 3, (UInt32)dstMeshes[i].vertices.size());
                    UInt32* index32 = reinterpret_cast<UInt32*>(eMesh->indices->indexbuffer.data());
                    UInt16* index16 = reinterpret_cast<UInt16*>(eMesh->indices->indexbuffer.data());
                    bool isIndex32 = dstMeshes[i].vertices.size() > (std::numeric_limits<UInt16>::max)();
                    for (UInt32 p = 0, idx = 0; p < dstMeshes[i].polygonSizes.size(); ++p)
                    {
                        AssertIf(dstMeshes[i].polygonSizes[p] != 3);
                        if (isIndex32)
                        {
                            *(index32++) = dstMeshes[i].polygons[idx + 2];
                            *(index32++) = dstMeshes[i].polygons[idx + 1];
                            *(index32++) = dstMeshes[i].polygons[idx + 0];
                        }
                        else
                        {
                            *(index16++) = static_cast<UInt16>(dstMeshes[i].polygons[idx + 2]);
                            *(index16++) = static_cast<UInt16>(dstMeshes[i].polygons[idx + 1]);
                            *(index16++) = static_cast<UInt16>(dstMeshes[i].polygons[idx + 0]);
                        }
                        idx += 3;
                    }
                    eMesh->vertexdata = std::make_unique<CrossSchema::MeshVertexDescriptionT>();
                    ImportVerticesFromFBX(dstMeshes[i], *eMesh->vertexdata, eBones);

                    // Physics collision: mesh collision
                    if(mImportSettings.GenerateComplexCollision)
                    {
                        FBXImportMesh& mesh = dstMeshes[i];
                        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
                        std::shared_ptr<PhysicsTriangleMesh> tran = physicsEngine->GetCooker()->BuildTriangleMesh(reinterpret_cast<UInt8*>(mesh.vertices.data()),
                                                                                                                  (UInt32)mesh.vertices.size(),
                                                                                                                  (UInt16)sizeof(AssetMath::Vector3f),
                                                                                                                  reinterpret_cast<UInt8*>(eMesh->indices->indexbuffer.data()),
                                                                                                                  (UInt32)mesh.polygons.size(),
                                                                                                                  (isIndex32 ? (UInt16)sizeof(UInt32) : (UInt16)sizeof(UInt16)));
                        std::vector<UInt8> serializeData = physicsEngine->GetCooker()->SerializeTriangleMesh(tran.get(), false);
                        PhysicsMeshCollisionImport coll({0.0, 0.0, 0.0}, Quaternion::Identity(), std::move(serializeData));
                        iScene.PhyCollision.meshCollision.emplace_back(std::move(coll));
                    }
                }
                break;
            }
        }

        for (auto& child : sceneNode.children)
        {
            node.Children.push_back({});
            auto& eNode = node.Children.back();

            // visit_nodes(node.Children, eNode, child);
            InOneFbxNode(meshes, sceneMeshMaterialMap, meshMaterialMap, iScene, node.Children, eNode, child);
        }
    }

    bool FBXNewImporter::SubmeshNameIsPhysicsCollisionBox(const std::string& name) { return name.find("CEBX_") == 0 || name.find("UBX_") == 0; }

    bool FBXNewImporter::SubmeshNameIsPhysicsCollisionSphere(const std::string& name) { return name.find("CESP_") == 0 || name.find("USP_") == 0; }

    bool FBXNewImporter::SubmeshNameIsPhysicsCollisionCapsule(const std::string& name) { return name.find("CECA_") == 0 || name.find("UCP_") == 0; }

    bool FBXNewImporter::SubmeshNameIsPhysicsCollisionConvex(const std::string& name) { return name.find("CECO_") == 0 || name.find("UCX_") == 0; }

    bool FBXNewImporter::SubmeshNameIsPhysicsCollision(const std::string& name)
    {
        return SubmeshNameIsPhysicsCollisionBox(name) || SubmeshNameIsPhysicsCollisionCapsule(name) || SubmeshNameIsPhysicsCollisionConvex(name) || SubmeshNameIsPhysicsCollisionSphere(name);
    }

    void FBXNewImporter::ImportAssetData(const std::string& assetFilename, ImportScene& iScene)
    {
        mFbxImportScene = std::make_unique<FBXImportScene>();
        auto fbxSdkPair = ImportFBXSceneData(assetFilename, mFbxImportScene.get());

        if (mImportSettings.ImportSkin)
        {
            ImportSkeletonData(fbxSdkPair.first, iScene.ImportSkeltData);

            // We import skin after skeleton is imported

            Assert(mFbxImportScene->meshes.size() == mFbxMeshToInfoMap.size());

            // We need to put meshes in stable order (because 3dAssetImport tests depend on that)
            std::vector<FbxMesh*> orderedFbxMeshToInfoMap(mFbxMeshToInfoMap.size(), 0);
            for (FBXMeshToSharedMeshInfoMap::const_iterator it = mFbxMeshToInfoMap.begin(), end = mFbxMeshToInfoMap.end(); it != end; ++it)
            {
                Assert(it->second.nIndex < static_cast<int>(mFbxImportScene->meshes.size()));
                orderedFbxMeshToInfoMap[it->second.nIndex] = it->first;
            }

            for (UInt32 i = 0; i < orderedFbxMeshToInfoMap.size(); ++i)
            {
                FbxMesh* pFbxMesh = orderedFbxMeshToInfoMap[i];
                Assert(pFbxMesh);
                ImportSkin(pFbxMesh, mFbxImportScene->meshes[i], *mFbxImportScene.get(), mFBXNodeMap, mFbxMeshToInfoMap);
            }
        }

        if (mImportSettings.AnimImport != None)
            ImportAllAnimations(fbxSdkPair.first, iScene.ImportSkeltData, iScene.ImportAnimations);
        
        fbxSdkPair.first->Destroy();
        fbxSdkPair.second->Destroy();

        Assert(mFbxImportScene);
        SortingFbxNode::GuaranteeUniqueNodeNames(*mFbxImportScene);

        iScene.Meshes.rootnode = std::move(std::unique_ptr<CrossSchema::ImportNodeT>(new CrossSchema::ImportNodeT));

        auto& rootNode = iScene.RootNode;
        auto& meshes = iScene.Meshes;
        for (auto& s : iScene.ImportSkeltData.Bones)
        {
            CrossSchema::ImportBoneNodeT bone;
            bone.name = s.BoneName;
            bone.boneid = s.BoneID;
            bone.parentid = s.ParentID;
            bone.bindposeinv = std::vector<float>(s.BindPoseInv.data(), s.BindPoseInv.data() + 16);
            bone.worldmatrix = std::vector<float>(s.RefPoseWorld.data(), s.RefPoseWorld.data() + 16);
            bone.bindposedef = std::vector<float>(s.RefPoseBind.data(), s.RefPoseBind.data() + 16);

            meshes.skeleton.push_back(std::move(std::make_unique<CrossSchema::ImportBoneNodeT>(bone)));
        }

        for (size_t i = 0, size = iScene.ImportSkeltData.Bones.size(); i < size; ++i)
        {
            CrossSchema::invmatrixT invmat;
            invmat.val = std::vector<float>(iScene.ImportSkeltData.Bones[i].BindPoseInv.data(), iScene.ImportSkeltData.Bones[i].BindPoseInv.data() + 16);
            meshes.bindposeinvmatrixs.push_back(std::move(std::make_unique<CrossSchema::invmatrixT>(invmat)));
        }

        meshes.rootnode = std::make_unique<CrossSchema::ImportNodeT>();
        auto& fbrootNode = meshes.rootnode;

        std::map<int, int> sceneMeshMaterialMap;
        std::map<int, int> meshMaterialMap;   //<meshIndex, materialId>

        for (auto& node : mFbxImportScene->nodes)
        {
            // Physics Collision
            if (SubmeshNameIsPhysicsCollision(node.name))
            {
                auto& m = mFbxImportScene->meshes[node.meshIndex];
                if (m.vertices.empty() || m.polygons.empty())
                    break;
                std::vector<FBXImportMesh> dstMeshes;
                RemapVertex(m, dstMeshes);

                for (UInt32 i = 0; i < dstMeshes.size(); ++i)
                {
                    // mesh has been triangulated
                    std::vector<UInt32> indexBuf;
                    for (UInt32 p = 0, idx = 0; p < dstMeshes[i].polygonSizes.size(); ++p)
                    {
                        AssertIf(dstMeshes[i].polygonSizes[p] != 3);
                        indexBuf.push_back(dstMeshes[i].polygons[idx + 2]);
                        indexBuf.push_back(dstMeshes[i].polygons[idx + 1]);
                        indexBuf.push_back(dstMeshes[i].polygons[idx + 0]);
                        idx += 3;
                    }

                    std::vector<Float3> vertex;
                    std::transform(dstMeshes[i].vertices.begin(), dstMeshes[i].vertices.end(), std::back_inserter(vertex), [](AssetMath::Vector3f c) { return Float3{c.x(), c.y(), c.z()}; });

                    BoundingBox aabb;
                    BoundingBox::CreateFromPoints(aabb, vertex.size(), vertex.data(), sizeof(Float3));
                    Float3 center;
                    Float3 extent;
                    aabb.GetCenter(&center);
                    aabb.GetExtent(&extent);

                    // simple collision
                    if (SubmeshNameIsPhysicsCollisionBox(node.name))
                    {
                        iScene.PhyCollision.boxCollision.emplace_back(center, Quaternion::Identity(), extent);
                    }
                    else if (SubmeshNameIsPhysicsCollisionSphere(node.name))
                    {
                        if (abs(extent.x - extent.y) > 0.001 || abs(extent.x - extent.z) > 0.001)
                        {
                            LOG_ERROR("FBXNewImporter: Import physics collision error: {} AABB is not square", dstMeshes[i].name.c_str());
                        }
                        iScene.PhyCollision.sphereCollision.emplace_back(center, extent.x);
                    }
                    else if (SubmeshNameIsPhysicsCollisionCapsule(node.name))
                    {
                        float radius = 0;
                        float halfExtent = 0;
                        if (abs(extent.x - extent.y) < 0.001)
                        {
                            radius = extent.x;
                            halfExtent = extent.z;
                        }
                        else if (abs(extent.x - extent.z) < 0.001)
                        {
                            radius = extent.x;
                            halfExtent = extent.y;
                        }
                        else if (abs(extent.y - extent.z) < 0.001)
                        {
                            radius = extent.y;
                            halfExtent = extent.x;
                        }
                        else
                        {
                            LOG_ERROR("FBXNewImporter: Import physics collision error: {} is not a capsule", dstMeshes[i].name.c_str());
                        }
                        iScene.PhyCollision.capsuleCollision.emplace_back(center, Quaternion::Identity(), radius, halfExtent);
                    }
                    else if (SubmeshNameIsPhysicsCollisionConvex(node.name))
                    {
                        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
                        std::shared_ptr<PhysicsConvexMesh> convex = physicsEngine->GetCooker()->BuildConvexMesh(
                            reinterpret_cast<UInt8*>(vertex.data()), (UInt32)vertex.size(), (UInt16)sizeof(Float3), reinterpret_cast<UInt8*>(dstMeshes[i].polygons.data()), (UInt32)dstMeshes[i].polygons.size(), (UInt16)sizeof(UInt32));
                        std::vector<UInt8> serializeData = physicsEngine->GetCooker()->SerializeConvexMesh(convex.get(), false);
                        iScene.PhyCollision.convexCollision.emplace_back(PhysicsConvexCollisionImport{{0.0f, 0.0f, 0.0f}, Quaternion::Identity(), std::move(serializeData)});
                    }
                }
            }
            else
            {
                rootNode.Children.push_back({});
                auto& editorNode = rootNode.Children.back();
                // visit_nodes(rootNode.Children, editorNode, node);
                InOneFbxNode(meshes, sceneMeshMaterialMap, meshMaterialMap, iScene, rootNode.Children, editorNode, node);
            }
        }

        if (rootNode.Children.size() == 1)
        {
            auto bak = rootNode.Children[0];
            rootNode = bak;
            rootNode.IsRootInImportMesh = true;
        }
        rootNode.Name = PathHelper::GetBaseFileName(assetFilename, true);

        FBXAuxiliary::buildFBNode(rootNode, *fbrootNode);
        bool hasBone = iScene.ImportSkeltData.Bones.size() != 0;
        if (mImportSettings.MergeWithMaterial && !hasBone)
        {
            // get all used material
            std::set<int> usedMaterial;
            std::for_each(meshMaterialMap.begin(), meshMaterialMap.end(), [&usedMaterial](decltype(meshMaterialMap)::reference pair) { usedMaterial.insert(pair.second); });
            MergeMeshWithMaterial(rootNode, meshes, meshMaterialMap, static_cast<int>(usedMaterial.size()));
        }
    }

    fbxsdk::FbxScene* FBXNewImporter::CreateFbxScene(const std::string& assetFilename) const
    {
        fbxsdk::FbxManager* pFbxManager = fbxsdk::FbxManager::Create();
        fbxsdk::FbxIOSettings* pFbxIOSetting = fbxsdk::FbxIOSettings::Create(pFbxManager, IOSROOT);
        pFbxManager->SetIOSettings(pFbxIOSetting);

        fbxsdk::FbxImporter* pFbximporter = fbxsdk::FbxImporter::Create(pFbxManager, "");

        if (!pFbximporter->Initialize(assetFilename.c_str(), -1, pFbxManager->GetIOSettings()))
        {
            throw std::runtime_error("fail to load model\n");
        }


        fbxsdk::FbxScene* pFbxScene = fbxsdk::FbxScene::Create(pFbxManager, "newScene");
        pFbximporter->Import(pFbxScene);
        pFbximporter->Destroy();

        fbxsdk::FbxAxisSystem SceneAxisSystem = pFbxScene->GetGlobalSettings().GetAxisSystem();
        fbxsdk::FbxAxisSystem OurAxisSystem(FbxAxisSystem::eYAxis, FbxAxisSystem::eParityOdd, FbxAxisSystem::eRightHanded);
        if (SceneAxisSystem != OurAxisSystem)
        {
            OurAxisSystem.ConvertScene(pFbxScene);
        }

        fbxsdk::FbxSystemUnit SceneSystemUnit = pFbxScene->GetGlobalSettings().GetSystemUnit();
        /*
                    if (SceneSystemUnit.GetScaleFactor() != 1.0)
                    {
                        fbxsdk::FbxSystemUnit::cm.ConvertScene(pFbxScene);
                    }
        */

        switch (mImportSettings.ModelSystemUnit)
        {
        case UNIT_MM:
            fbxsdk::FbxSystemUnit::mm.ConvertScene(pFbxScene);
            break;
        case UNIT_CM:
            fbxsdk::FbxSystemUnit::cm.ConvertScene(pFbxScene);
            break;
        case UNIT_DM:
            fbxsdk::FbxSystemUnit::dm.ConvertScene(pFbxScene);
            break;
        case UNIT_M:
            fbxsdk::FbxSystemUnit::m.ConvertScene(pFbxScene);
            break;
        case UNIT_KM:
            fbxsdk::FbxSystemUnit::km.ConvertScene(pFbxScene);
            break;
        case UNIT_INCH:
            fbxsdk::FbxSystemUnit::Inch.ConvertScene(pFbxScene);
            break;
        case UNIT_FOOT:
            fbxsdk::FbxSystemUnit::Foot.ConvertScene(pFbxScene);
            break;
        case UNIT_MILE:
            fbxsdk::FbxSystemUnit::Mile.ConvertScene(pFbxScene);
            break;
        case UNIT_YARD:
            fbxsdk::FbxSystemUnit::Yard.ConvertScene(pFbxScene);
            break;
        default:
            assert(false);
        }

        return pFbxScene;
    }

    void FBXNewImporter::ClearDataArrays()
    {
        mMeshArray.clear();
        mClusterArray.clear();
        mBoneNodeToIDMap.clear();
    }

    void FBXNewImporter::FillDataArrays(fbxsdk::FbxScene* pFbxScene)
    {
        ClearDataArrays();
        FillMeshArray(pFbxScene);
        FillClusterArray();
    }

    void FBXNewImporter::FillMeshArray(fbxsdk::FbxScene* pFbxScene)
    {
        fbxsdk::FbxNode* pFbxSceneRootNode = pFbxScene->GetRootNode();
        std::stack<fbxsdk::FbxNode*> fbxpNodeStack;
        fbxpNodeStack.push(pFbxSceneRootNode);
        while (!fbxpNodeStack.empty())
        {
            auto pNode = fbxpNodeStack.top();
            fbxpNodeStack.pop();

            for (int i = 0; i < pNode->GetChildCount(); i++)
            {
                fbxpNodeStack.push(pNode->GetChild(i));
            }

            if (pNode->GetNodeAttribute() != nullptr && pNode->GetNodeAttribute()->GetAttributeType() == fbxsdk::FbxNodeAttribute::EType::eMesh)
            {
                FbxMesh* pFbxMesh = pNode->GetMesh();

                if (pFbxMesh == nullptr)
                    continue;

                mMeshArray.emplace_back(pFbxMesh);
            }
        }
    }

    void FBXNewImporter::FillClusterArray()
    {
        for (auto& pFbxMesh : mMeshArray)
        {
            FbxSkin* pFbxSkin = reinterpret_cast<FbxSkin*>(pFbxMesh->GetDeformer(0, fbxsdk::FbxDeformer::eSkin));

            if (pFbxSkin == nullptr)
                continue;

            int nCluster = pFbxSkin->GetClusterCount();

            for (int i = 0; i < nCluster; i++)
            {
                FbxCluster* pFbxCluster = pFbxSkin->GetCluster(i);
                if (pFbxCluster == nullptr)
                    throw std::runtime_error("null cluster find\n");
                mClusterArray.emplace_back(pFbxCluster);
            }
        }
    }

    /*
    We need two steps to import skeleton data. In first go, we use DFS to iterate the fbx scene, and extract all nodes whose
    eType is eSkeleton and store their parent-child relationship. In second go, we iterate our cluster array to extract the bindpose
    of bones. We cannot simply do these two steps in one go because there exists some bones without a bindpose.
    */
    void FBXNewImporter::ImportSkeletonData(fbxsdk::FbxScene* pFbxScene, SkeletonDesc& outSkeleton)
    {
        ImportSkeletonHierarchy(pFbxScene, outSkeleton);
        ImportSkeletonBindpose(pFbxScene, outSkeleton);

        if (outSkeleton.Bones.empty())
            return;

        SerializeSkeleton(outSkeleton, mAssetFileName, mNdaSavePath);
    }

    bool FBXNewImporter::IsChildrenContain(fbxsdk::FbxNodeAttribute::EType searchType, fbxsdk::FbxNode* pRoot) const
    {
        if (pRoot == nullptr)
            return false;

        if (pRoot->GetNodeAttribute() != nullptr && pRoot->GetNodeAttribute()->GetAttributeType() == searchType)
            return true;

        bool re = false;
        for (int i = 0; i < pRoot->GetChildCount(); i++)
        {
            auto child = pRoot->GetChild(i);
            re = re || IsChildrenContain(searchType, child);

            if (re)
                break;
        }

        return re;
    }

    bool FBXNewImporter::IsNodeAnimated(FbxNode const* pNode, FbxAnimLayer const* pAnimLayer) const
    {
        if (pAnimLayer == nullptr)
            return false;

        // verify that the node is animated.
        bool bIsAnimated = false;
        FbxTimeSpan AnimTimeSpan(FBXSDK_TIME_INFINITE, FBXSDK_TIME_MINUS_INFINITE);

        // translation animation
        FbxProperty TransProp = pNode->LclTranslation;
        for (int i = 0; i < TransProp.GetSrcObjectCount<FbxAnimCurveNode>(); i++)
        {
            FbxAnimCurveNode* CurveNode = TransProp.GetSrcObject<FbxAnimCurveNode>(i);
            if (CurveNode && pAnimLayer->IsConnectedSrcObject(CurveNode))
            {
                bIsAnimated |= static_cast<bool>(CurveNode->GetAnimationInterval(AnimTimeSpan));
                break;
            }
        }

        // rotation animation
        FbxProperty RotProp = pNode->LclRotation;
        for (int i = 0; bIsAnimated == false && i < RotProp.GetSrcObjectCount<FbxAnimCurveNode>(); i++)
        {
            FbxAnimCurveNode* CurveNode = RotProp.GetSrcObject<FbxAnimCurveNode>(i);
            if (CurveNode && pAnimLayer->IsConnectedSrcObject(CurveNode))
            {
                bIsAnimated |= static_cast<bool>(CurveNode->GetAnimationInterval(AnimTimeSpan));
            }
        }

        return bIsAnimated;
    }

    void FBXNewImporter::ImportSkeletonHierarchy(fbxsdk::FbxScene* pFbxScene, SkeletonDesc& outSkeleton)
    {
        mSkeletonArray.clear();

        fbxsdk::FbxNode* pFbxSceneRootNode = pFbxScene->GetRootNode();

        // depth first search using a stack
        std::queue<fbxsdk::FbxNode*> fbxpNodeQueue;
        fbxpNodeQueue.push(pFbxSceneRootNode);

        fbxsdk::FbxAnimStack* curAnimStack = pFbxScene->GetSrcObject<fbxsdk::FbxAnimStack>(0);
        FbxAnimLayer* curAnimLayer = curAnimStack != nullptr ? curAnimStack->GetMember<FbxAnimLayer>(0) : nullptr;

        int staticRootSKNodesCount = 0;

        while (!fbxpNodeQueue.empty())
        {
            auto pNode = fbxpNodeQueue.front();
            fbxpNodeQueue.pop();

            bool isContainSkeleton = IsChildrenContain(fbxsdk::FbxNodeAttribute::EType::eSkeleton, pNode);

            bool isAnimated = IsNodeAnimated(pNode, curAnimLayer);

            bool isStaticMesh = pNode->GetNodeAttribute() != nullptr && pNode->GetNodeAttribute()->GetAttributeType() == fbxsdk::FbxNodeAttribute::EType::eMesh && pNode->GetMesh()->GetDeformerCount(FbxDeformer::eSkin) == 0;

            bool isCamera = pNode->GetNodeAttribute() != nullptr && pNode->GetNodeAttribute()->GetAttributeType() == fbxsdk::FbxNodeAttribute::EType::eCamera;

            bool isExistSkeleton = outSkeleton.Bones.size() > 0;

            // current node's attribute is skeleton node, record it as part of skeleton
            bool skeletonNode = pNode->GetNodeAttribute() != nullptr && pNode->GetNodeAttribute()->GetAttributeType() == fbxsdk::FbxNodeAttribute::EType::eSkeleton;

            // current node contains child which is skeleton node, while current node's parent exist skeleton node.
            // we mark this gap node as part of skeleton
            bool skGapNode = !skeletonNode && !isStaticMesh && isContainSkeleton && isExistSkeleton;

            // current node is static mesh & animation curve grabbed successfully,
            // mark this mesh node as part of skeleton while the node is immutable leaf in skeleton hierarchy
            bool frameAnimNode = isAnimated && isStaticMesh && !isContainSkeleton;

            if (skeletonNode || skGapNode || frameAnimNode || isCamera)
            {
                SkeletonDesc::Bone curBone;
                curBone.BoneName = pNode->GetName();

                // try to find the missed eNull type nodes from the first SkeletonPartBone to the Root node
                if (mBoneNodeToIDMap.find(pNode->GetParent()) == mBoneNodeToIDMap.end() && pNode->GetParent() != pFbxSceneRootNode)
                {
                    static std::vector<fbxsdk::FbxNode*> childToRootSkArray;
                    childToRootSkArray.clear();

                    // find missedNodes from bottom to top
                    auto missedNode = pNode->GetParent();
                    while (missedNode != nullptr && missedNode->GetParent() != pFbxSceneRootNode && mBoneNodeToIDMap.find(missedNode) == mBoneNodeToIDMap.end())
                    {
                        childToRootSkArray.emplace_back(missedNode);
                        missedNode = missedNode->GetParent();
                    }

                    // insert all missedNodes from top to left using a reverse_iterator
                    for (auto riter = childToRootSkArray.rbegin(); riter != childToRootSkArray.rend(); riter++)
                    {
                        auto node = *riter;
                        SkeletonDesc::Bone missedBone;
                        missedBone.BoneName = node->GetName();
                        missedBone.BoneID = static_cast<UInt32>(outSkeleton.Bones.size());

                        if (mBoneNodeToIDMap.find(node->GetParent()) == mBoneNodeToIDMap.end())
                        {
                            missedBone.ParentID = SK_BONE_INDEX_NONE;
                            staticRootSKNodesCount++;
                            Assert(staticRootSKNodesCount <= 1 && "More than one root node.");
                        }
                        else
                        {
                            missedBone.ParentID = mBoneNodeToIDMap.at(node->GetParent());
                        }

                        mBoneNodeToIDMap.insert(std::pair(node, missedBone.BoneID));
                        mSkeletonArray.emplace_back(node);
                        outSkeleton.Bones.emplace_back(missedBone);
                    }
                }

                // set current bone's parent id
                if (mBoneNodeToIDMap.find(pNode->GetParent()) == mBoneNodeToIDMap.end())
                {
                    curBone.ParentID = SK_BONE_INDEX_NONE;
                    staticRootSKNodesCount++;
                    Assert(staticRootSKNodesCount <= 1 && "More than one root node.");
                }
                else
                {
                    curBone.ParentID = mBoneNodeToIDMap.at(pNode->GetParent());
                }

                curBone.BoneID = static_cast<UInt32>(outSkeleton.Bones.size());
                mBoneNodeToIDMap.insert(std::pair(pNode, curBone.BoneID));
                mSkeletonArray.emplace_back(pNode);

                outSkeleton.Bones.emplace_back(curBone);
            }

            // Record fbx node hierarchy by level order traversal, which memory arrangement's
            // is more efficient in calculate bone transform without Recursion
            for (int i = 0; i < pNode->GetChildCount(); i++)
            {
                fbxpNodeQueue.push(pNode->GetChild(i));
            }
        }

        for (int i = 0; i < mClusterArray.size(); i++)
        {
            auto& pFbxCluster = mClusterArray[i];
            auto pBoneNode = pFbxCluster->GetLink();

            if (mBoneNodeToIDMap.find(pBoneNode) == mBoneNodeToIDMap.end())
            {
                auto pair = std::find_if(mBoneNodeToIDMap.begin(), mBoneNodeToIDMap.end(), [=](std::pair<fbxsdk::FbxNode*, int> const& elem) {
                    auto pCursorNode = elem.first;
                    auto pCheckerNode = pBoneNode;

                    while (strcmp(pCursorNode->GetName(), pCheckerNode->GetName()) == 0)
                    {
                        pCursorNode = pCursorNode->GetParent();
                        pCheckerNode = pCheckerNode->GetParent();

                        if (pCursorNode == pCheckerNode && pCursorNode == nullptr)
                            return true;
                    }

                    return false;
                });

                if (pair != mBoneNodeToIDMap.end())
                    mBoneNodeToIDMap.emplace(pBoneNode, pair->second);
            }
        }
    }

    /*
    Here is the formula of a mesh point's final position in engine during an animation.
    Define
    ctr_p. This control point position, which is the position of the original control point in the fbx mesh
    G = geometry transform of the fbxMesh node
    M = fbxCluster->GetTransformMatrix(). This is the offset of the model to the scene origin
    L = fbxCluster->GetTransformLinkMatrix(). This is the cluster's initial world position
    A = fbxCluster->GetLink()->EvaluateGlobalTransform(time). This is the bone world transformation at current time in the animation
    ctr_p_f. This is the final vertex position in engine world.
    Then

    ctr_p_f = A * L^{-1} * M * G * ctr_p

    In the import data, G*ctr_p is baked into the vertex position of the model.
    L^{-1}*M is represented as bindpose_inverse
    A is the animation data.

    */
    void FBXNewImporter::ImportSkeletonBindpose(fbxsdk::FbxScene* pFbxScene, SkeletonDesc& outSkeleton)
    {
        // Extract skeleton local 2 world matrix from fbx nodes
        for (int i = 0; i < mSkeletonArray.size(); ++i)
        {
            auto pSkNode = mSkeletonArray[i];

            int curBoneID = mBoneNodeToIDMap.at(pSkNode);
            auto& bone = outSkeleton.Bones[curBoneID];

            /** The Parameter below can not be modify, the default parameter to get bindpose is invalid
             *	After ResetPivotSetAndConvertAnimation called, the skeleton nodes grabbed after RESET_PIVOT are new nodes while the old nodes coexists
             *	The bindpose_offset matrix should grabbed by cluster, but not the bindpose matrix,
             *   which modified after RESET_PIVOT need be grab by new skeleton node.
             *	(the phenomenon is bindpose maybe a specify animation first frame after called by parameter 0, FBXSDK_TIME_INFINITE lead to wrong pose here -- IDKW)
             **/
            auto boneGlobalMatrix = pSkNode->EvaluateGlobalTransform(0);
            bone.RefPoseWorld = FBXMatrixToMatrix4x4(boneGlobalMatrix);
        }

        for (int i = 0; i < mClusterArray.size(); i++)
        {
            auto& pFbxCluster = mClusterArray[i];
            FbxAMatrix modelTransform, linkTransformation;
            pFbxCluster->GetTransformMatrix(modelTransform);
            pFbxCluster->GetTransformLinkMatrix(linkTransformation);

            FbxAMatrix bindPose = modelTransform.Inverse() * linkTransformation;
            FbxAMatrix bindPoseInv = bindPose.Inverse();
            std::string curBoneName = pFbxCluster->GetLink()->GetName();
            if (mBoneNodeToIDMap.size() > 0 && curBoneName != "")
            {
                auto linkNode = pFbxCluster->GetLink();
                int curBoneID = mBoneNodeToIDMap.at(linkNode);
                auto& bone = outSkeleton.Bones[curBoneID];

                bone.BindPoseInv = FBXMatrixToMatrix4x4(bindPoseInv);
                bone.RefPoseBind = FBXMatrixToMatrix4x4(linkTransformation);
            }
            else
            {
                ReportWarning("bone is empty.\n");
                LOG_WARN("Bone is empty.");
            }
        }
    }

    /*
    We iterate through all animation stacks and use FbxNode->EvaluateGlobalTransform to get their global transform at a time stamp.
    Then we use the bone hierarchy we import before to convert these global transformations to local ones. Finally we split these local
    transformations to TRS and serialize these TRS as track. The sample rate is 30 by default.
    */
    void FBXNewImporter::ImportAllAnimations(fbxsdk::FbxScene* pFbxScene, const SkeletonDesc& importSkeleton, std::vector<AnimationDesc>& outAnimations)
    {
        if (importSkeleton.Bones.empty())
            return;

        int nAnimStack = pFbxScene->GetSrcObjectCount<fbxsdk::FbxAnimStack>();

        for (int n = 0; n < nAnimStack; n++)
        {
            fbxsdk::FbxAnimStack* curAnimStack = pFbxScene->GetSrcObject<fbxsdk::FbxAnimStack>(n);
            Assert(curAnimStack);
            Assert(curAnimStack->GetSrcObjectCount<fbxsdk::FbxAnimLayer>() != 0);

            // bake animation layer into one layer in stack
            if (curAnimStack->GetSrcObjectCount<fbxsdk::FbxAnimLayer>() > 1)
            {
                FbxTime bakeStartTime, bakeEndTime, fbxSampleRate;
                FbxTimeSpan bakeTimeSpan = curAnimStack->GetLocalTimeSpan();

                bakeStartTime = bakeTimeSpan.GetStart();
                bakeEndTime = bakeTimeSpan.GetStop();

                const double sampleRate = 1.0f / FbxTime::GetFrameRate(pFbxScene->GetGlobalSettings().GetTimeMode());
                fbxSampleRate.SetSecondDouble(sampleRate);

                if (!curAnimStack->BakeLayers(pFbxScene->GetAnimationEvaluator(), bakeStartTime, bakeEndTime, fbxSampleRate))
                    LOG_WARN("Failed to bake layers.\n");
            }
            Assert(curAnimStack->GetSrcObjectCount<fbxsdk::FbxAnimLayer>() == 1);

            pFbxScene->SetCurrentAnimationStack(curAnimStack);

            // set animation name
            AnimationDesc curAnim;
            std::string curAnimName = curAnimStack->GetName();
            curAnim.Name = curAnimName;

            // set animation duration
            float duration = -1.0f;
            duration = static_cast<float>(curAnimStack->GetLocalTimeSpan().GetDuration().GetSecondDouble());
            curAnim.Duration = duration;

            // set animation sample result
            std::vector<std::vector<AssetMath::Matrix4x4f>> allBonesRefTransformationsInAnim;
            allBonesRefTransformationsInAnim.resize(importSkeleton.Bones.size());

            // at least one frame when anim duration > 0.0
            const int animFrameCount = static_cast<int>(std::ceil(duration * AnimationDesc::DefaultSampleRate()));
            FbxTime timeStep = 0;
            timeStep.SetSecondDouble(1.0 / static_cast<double>(AnimationDesc::DefaultSampleRate()));

            // Scene 1:
            // If skeleton exists bones not include in any skin_node, the bone nodes attribute "maybe" not be EType::eSkeleton
            // Ex. Bone0 - Bone1 - StaticMesh, if Bone0 not include by any skin_node in fbx file while bone0 ctrl StaticMesh's transform
            // We would still make the Bone0 as part of skeleton, but the matrix should calculated by cluster need grab manually by below code
            // Scene 2:
            // If only skeleton node exists in fbx file, we need calculated bone world matrix manually instead of grabbed by cluster
            static AssetMath::Matrix4x4f curBoneTransform;
            static std::vector<AssetMath::Matrix4x4f> perBoneRefTransformationsInAnim;
            for (auto& pFbxBone : mSkeletonArray)
            {
                auto boneId = mBoneNodeToIDMap.at(pFbxBone);
                if (boneId < 0)
                    continue;

                perBoneRefTransformationsInAnim.clear();

                FbxTime timeStamp = 0;
                for (int frame_index = 0; frame_index < animFrameCount; ++frame_index)
                {
                    auto& boneCononicalTransform = pFbxBone->EvaluateGlobalTransform(timeStamp);
                    curBoneTransform = FBXMatrixToMatrix4x4(boneCononicalTransform);

                    perBoneRefTransformationsInAnim.emplace_back(curBoneTransform);

                    timeStamp += timeStep;
                }

                if (boneId >= 0)
                    allBonesRefTransformationsInAnim[boneId] = perBoneRefTransformationsInAnim;
            }

            auto allBonesLocalTransformationsInAnim = ConvertBonesRefTransToLocal(allBonesRefTransformationsInAnim, importSkeleton);
            SplitBonesLocalTransformationsToTracks(allBonesLocalTransformationsInAnim, curAnim.TracksForAllBones);

            outAnimations.emplace_back(std::move(curAnim));
        }

        SerializeAnimations(outAnimations, mAssetFileName, mNdaSavePath, importSkeleton);
    }

    std::vector<std::vector<AssetMath::Matrix4x4f>> FBXNewImporter::ConvertBonesRefTransToLocal(std::vector<std::vector<AssetMath::Matrix4x4f>>& allBonesRefTransformationsInAnim, const SkeletonDesc& skeleton)
    {
        std::vector<std::vector<AssetMath::Matrix4x4f>> allBonesLocalTransformationsInAnim;
        for (int i = 0; i < allBonesRefTransformationsInAnim.size(); ++i)
        {
            auto& curBoneRefTransformationsInAnim = allBonesRefTransformationsInAnim[i];
            std::vector<AssetMath::Matrix4x4f> curBoneLocalTransforms;
            for (int j = 0; j < curBoneRefTransformationsInAnim.size(); ++j)
            {
                UInt32 curBoneParentID = skeleton.Bones[i].ParentID;
                if (curBoneParentID == SK_BONE_INDEX_NONE || allBonesRefTransformationsInAnim[curBoneParentID].size() == 0)
                {
                    curBoneLocalTransforms.emplace_back(curBoneRefTransformationsInAnim[j]);
                }
                else
                {
                    curBoneLocalTransforms.emplace_back(allBonesRefTransformationsInAnim[curBoneParentID][j].inverse() * curBoneRefTransformationsInAnim[j]);
                }
            }

            allBonesLocalTransformationsInAnim.emplace_back(curBoneLocalTransforms);
        }

        return allBonesLocalTransformationsInAnim;
    }

    void FBXNewImporter::SplitBonesLocalTransformationsToTracks(std::vector<std::vector<AssetMath::Matrix4x4f>>& allBonesLocalTransformationsInAnim, std::vector<AnimationDesc::Track>& trackForAllBones)
    {
        float timeStep = 1.0f / AnimationDesc::DefaultSampleRate();

        for (int i = 0; i < allBonesLocalTransformationsInAnim.size(); ++i)
        {
            auto& curBoneLocalTransform = allBonesLocalTransformationsInAnim[i];
            AnimationDesc::Track track;
            track.TranslationKeys.resize(curBoneLocalTransform.size());
            track.RotationKeys.resize(curBoneLocalTransform.size());
            track.ScaleKeys.resize(curBoneLocalTransform.size());

            float timeStamp = 0.0f;
            for (int j = 0; j < curBoneLocalTransform.size(); ++j)
            {
                track.TranslationKeys[j].Time = timeStamp;
                track.RotationKeys[j].Time = timeStamp;
                track.ScaleKeys[j].Time = timeStamp;

                timeStamp += timeStep;
#if USE_DXMATH != 1
                Decompose(curBoneLocalTransform[j], track.TranslationKeys[j].Translation, track.RotationKeys[j].Rotation, track.ScaleKeys[j].Scale);
#else
                curBoneLocalTransform[j].Compose(track.ScaleKeys[j].Scale, track.RotationKeys[j].Rotation, track.TranslationKeys[j].Translation);
#endif
            }

            trackForAllBones.emplace_back(track);
        }
    }

    auto FBXNewImporter::ImportFBXSceneData(std::string filename, FBXImportScene* fbxImportScene) -> std::pair<FbxScene*, FbxImporter*>
    {
        mFbxImportErrors.clear();
        mFbxImportWarnings.clear();
        mFBXNodeMap.clear();
        mFbxNodeArray.Clear();
        mFbxMeshToInfoMap.clear();

        if (!mFbxSdkManager)
            mFbxSdkManager = FbxManager::Create();

        FbxImporter* fbxImporter = FbxImporter::Create(mFbxSdkManager, "FbxImporter");
        if (!fbxImporter)
            return {nullptr, nullptr};

        if (!fbxImporter->Initialize(filename.c_str()))
        {
            ReportError("Couldn't read file %s.\n", filename.c_str());

            if (!fbxImporter->GetStatus())
                ReportError("%s\n\n", fbxImporter->GetStatus().GetErrorString());
            return {nullptr, nullptr};
        }

        FbxScene* fbxScene = FbxScene::Create(mFbxSdkManager, "FbxScene");
        if (!fbxScene)
            return {nullptr, nullptr};

        // If FBX file contains textures then this call will update all texture files, which might cause importing textures twice
        if (fbxImporter->Import(fbxScene))
        {
            FbxAxisSystem sceneAxisSystem = fbxScene->GetGlobalSettings().GetAxisSystem();
            FbxAxisSystem engineAxisSystem(FbxAxisSystem::eYAxis, FbxAxisSystem::eParityOdd, FbxAxisSystem::eRightHanded);

            if (sceneAxisSystem != engineAxisSystem)
                engineAxisSystem.ConvertScene(fbxScene);

            FbxSystemUnit sceneSystemUnit = fbxScene->GetGlobalSettings().GetSystemUnit();
            if (sceneSystemUnit.GetScaleFactor() != 1.0)
            {
                FbxSystemUnit engineSystemUnit(1.0);
                engineSystemUnit.ConvertScene(fbxScene);
            }

            ResetInvalid3dsMaxValue(fbxScene->GetRootNode());

            double frameRate;
            FbxTime::EMode timeMode = fbxScene->GetGlobalSettings().GetTimeMode();
            if (timeMode != FbxTime::eCustom)
                frameRate = FbxTime::GetFrameRate(timeMode);
            else
                frameRate = fbxScene->GetGlobalSettings().GetCustomFrameRate();

            if (frameRate <= 0.0)
            {
                ReportError("Framerate was set to %.2f, it's been reset to 30.0.\n", frameRate);
                frameRate = 30.0;
            }

            fbxImportScene->sceneInfo.sampleRate = static_cast<float>(frameRate);

            // This function should transform control points and animation curves to match our simplified node system.
            // The origin of the node has to be where the rotation pivot is in maya.
            // Eg. Vertices have to be transformed so a vertex which has the same world-space position as the rotation pivot
            // in world space will end up be at (0,0,0) in local-space.
            // Animation curves have to be converted so that animations look the same but match the simplified pivot context.
            ProcessAnimationCurveAndPivot(fbxScene);

            ConvertNurbsAndPatchRecursively(*mFbxSdkManager, fbxScene->GetRootNode());

            ConvertToFBXImportScene(*fbxScene, *fbxImportScene);

            ExtractSceneInfo(*fbxImporter, *fbxScene, fbxImportScene->sceneInfo);
        }
        else
        {
            ReportError("Couldn't import file %s.\n", filename.c_str());

            if (!fbxImporter->GetStatus())
            {
                ReportError("%s\n\n", fbxImporter->GetStatus().GetErrorString());
            }
        }

        // fbxScene->Destroy();
        // fbxImporter->Destroy();

        return {fbxScene, fbxImporter};
    }

    static void ResetInvalid3dsMaxValues(FbxNode* pNode, std::vector<const FbxNode*>& invalidNodes, int& nodeCount)
    {
        ++nodeCount;

        if (pNode == NULL)
            return;

        FbxProperty lTMProperty[] = {pNode->LclTranslation, pNode->LclRotation, pNode->LclScaling};

        FbxTime lNever(FbxLongLong(-20663336074399200));   // 3ds max's NEVER

        bool isValid = true;

        // 3ds max export a time range {NEVER, NEVER} and collapse to one key
        // detect these keys and reset them to 0sec
        for (int i = 0; i < 3; i++)
        {
            FbxAnimCurveNode* lCurveNode = lTMProperty[i].GetCurveNode();
            if (lCurveNode == NULL)
                continue;

            for (unsigned int j = 0; j < 3; j++)
            {
                FbxAnimCurve* lCurve = lCurveNode->GetCurve(j);
                if (lCurve && lCurve->KeyGetCount() == 1 && lCurve->KeyGetTime(0) <= lNever)
                {
                    lCurve->KeySetTime(0, FbxTime(0));
                    isValid = false;
                }
            }
        }

        if (!isValid)
            invalidNodes.emplace_back(pNode);

        for (int i = 0; i < pNode->GetChildCount(); i++)
        {
            ResetInvalid3dsMaxValues(pNode->GetChild(i), invalidNodes, nodeCount);
        }
    }

    void FBXNewImporter::ResetInvalid3dsMaxValue(FbxNode* node)
    {
        std::vector<const FbxNode*> invalidNodes;
        int nodeCount = 0;
        ResetInvalid3dsMaxValues(node, invalidNodes, nodeCount);

        if (!invalidNodes.empty())
        {
            LOG_ERROR("File contains{} (out of{} nodes with invalid data(keys at time before - 100 hours), \
                which potentially can cause FBX SDK crash.These corrupt files are produced by 3dsMax. \
                Try enabling \"Bake Animation\" option when exporting to FBX, if you want to get rid of this warning. Time for these keys will be reset to 0. ",
                      invalidNodes.size(),
                      nodeCount);
        }
    }

    static void ZeroPivotsForSkinsRecursive(FbxScene* scene, FbxNode* node)
    {
        if (node)
        {
            FbxMesh* mesh = node->GetMesh();
            if (mesh)
            {
                if (mesh->GetDeformerCount(FbxDeformer::eSkin) != 0)
                {
                    //TODO(Wangwentao):dynamic_cast is not ok for deformer class, why?
                    fbxsdk::FbxSkin* skin = static_cast<fbxsdk::FbxSkin*>(mesh->GetDeformer(0, FbxDeformer::eSkin));
                    int boneCount = skin->GetClusterCount();

                    if (boneCount > 0)
                    {
                        FbxVector4 zero(0, 0, 0);
                        node->SetRotationPivot(FbxNode::eSourcePivot, zero);
                        node->SetScalingPivot(FbxNode::eSourcePivot, zero);
                    }
                }
            }

            for (int i = 0; i < node->GetChildCount(); i++)
                ZeroPivotsForSkinsRecursive(scene, node->GetChild(i));
        }
    }

    static void SetPivotStateRecursive(FbxNode* node)
    {
        node->SetPivotState(FbxNode::eSourcePivot, FbxNode::ePivotActive);

        for (int i = 0, size = node->GetChildCount(); i < size; ++i)
        {
            FbxNode* child = node->GetChild(i);
            SetPivotStateRecursive(child);
        }
    }

    void FBXNewImporter::ProcessAnimationCurveAndPivot(FbxScene* scene)
    {
        FbxNode* root = scene->GetRootNode();

        //bool bIsContainSkin = root->GetMesh()->GetDeformerCount(FbxDeformer::eSkin) != 0;
       
        if (mImportSettings.ImportSkin)
            ZeroPivotsForSkinsRecursive(scene, root);

        const float framerate = static_cast<float>(FbxTime::GetFrameRate(scene->GetGlobalSettings().GetTimeMode()));

        // Fixing corrupt scale
        SyncCurveNodeChannelValue(root);

        // Clear curves with keys out of range
        RemoveInvalidTransformCurves(*scene, root);
        const int animStackCount = scene->GetSrcObjectCount<FbxAnimStack>();

        if (animStackCount <= 1)
        {
            // ResetPivotSetAndConvertAnimation now works on multiple takes
            root->ResetPivotSetAndConvertAnimation(framerate, false, false);

            /** ResetPivotSetAndConvertAnimation will make every single mesh's local coordinate coincide with FBX world origin point.
             *   All offset by vertex should be baked into transform matrix, vertex always around origin point
             *
             *	|	 ____
             *	|	|_o'_| mesh ctrl_p record in fbx mesh node local coordinate.
             *	|     /	   if fbx mesh node transform coincide with fbx world origin point, the vertex's xy should be all positive largely in left exp.
             *	|	 /	   after "ResetPivotSetAndConvertAnimation", mesh ctrl_p is moved into fbx origin point coordinate
             *	|   /	   all fbx mesh & animation matrix grabbed before should throw away & fill new one while old information is expired
             *  _|_ <
             * |_o_|---------------
             *
             **/
            FillDataArrays(scene);
        }
        else
        {
            // the solution was taken from: http://community.softimage.com/forum/autodesk-fbx/fbx-sdk/resetpivotsetandconvertanimation-issue
            root->ResetPivotSet(FbxNode::eDestinationPivot);
            for (int i = 0; i < animStackCount; ++i)
            {
                FbxAnimStack* animStack = scene->GetSrcObject<FbxAnimStack>(i);

                SetPivotStateRecursive(root);
                root->ConvertPivotAnimationRecursive(animStack, FbxNode::eDestinationPivot, framerate, false);
            }

            root->ResetPivotSet(FbxNode::eSourcePivot);
        }
    }

    void FBXNewImporter::ConvertNurbsAndPatchRecursively(FbxManager& mgr, FbxNode* node)
    {
        FbxGeometryConverter lConverter(&mgr);
        for (int i = 0, c = node->GetNodeAttributeCount(); i < c; ++i)
        {
            FbxNodeAttribute* lNodeAttribute = node->GetNodeAttributeByIndex(i);
            if (lNodeAttribute)
            {
                // Only triangulate nurbs and patches. Polygon triangulation is done!
                if (lNodeAttribute->GetAttributeType() == FbxNodeAttribute::eNurbs || lNodeAttribute->GetAttributeType() == FbxNodeAttribute::ePatch)
                {
                    lConverter.Triangulate(lNodeAttribute, true);
                }
            }
        }

        for (int i = 0, c = node->GetChildCount(); i < c; ++i)
        {
            ConvertNurbsAndPatchRecursively(mgr, node->GetChild(i));
        }
    }

    void FBXNewImporter::ConvertToFBXImportScene(FbxScene& fbxScene, FBXImportScene& scene)
    {
        int sceneMaterialCount = fbxScene.GetMaterialCount();
        for (int i = 0; i < sceneMaterialCount; i++)
            LOG_DEBUG("Material {}: {}\n", i, fbxScene.GetMaterial(i)->GetName());

        int sceneTextureCount = fbxScene.GetTextureCount();
        for (int i = 0; i < sceneTextureCount; i++)
            LOG_DEBUG("Texture {}: {}\n", i, fbxScene.GetTexture(i)->GetName());

        scene.meshes.reserve(fbxScene.GetSrcObjectCount<FbxNode>());
        scene.materials.reserve(fbxScene.GetSrcObjectCount<FbxNode>());

        FBXMaterialLookup fbxMaterialLookup;

        // Import nodes
        // We implicitly import all materials and meshes on the way
        FbxNode* pRootNode = fbxScene.GetRootNode();
        scene.nodes.resize(pRootNode->GetChildCount());

        for (int i = 0, c = pRootNode->GetChildCount(); i < c; ++i)
        {
            ImportNodesRecursively(fbxScene, pRootNode->GetChild(i), scene.nodes[i], scene, mFbxMeshToInfoMap, fbxMaterialLookup);
            if (pRootNode->GetChild(i)->GetSkeleton())
            {
                scene.sceneInfo.hasSkeleton = true;
            }
        }

        // BlendShapes have to be imported after we import all meshes, because we use normals and so on from meshes for blendshapes,
        // because fbx BlendShape data structures doesn't carry this data
        if (mImportSettings.ImportBlendShapes)
        {
            ImportBlendShapes(mFbxMeshToInfoMap, scene);
        }
    }

    void FBXNewImporter::ImportNodesRecursively(FbxScene& fbxScene, FbxNode* pFbxNode, FBXImportNode& outNode, FBXImportScene& scene, FBXMeshToSharedMeshInfoMap& fbxMeshToSharedMeshInfoMap, FBXMaterialLookup& fbxMaterialLookup,
                                                int parentBone)
    {
        FbxVector4 translation, eulerRotation, scale;

        // use FBXSDK_TIME_INFINITE to Grab node transform could fail caused by ResetPivotSetAndConvertAnimation(maybe),
        // using 0 timestamp to grab bindpose
        auto matLocal = pFbxNode->EvaluateLocalTransform(0);

        translation = matLocal.GetT();
        eulerRotation = matLocal.GetR();
        scale = matLocal.GetS();

        outNode.position = FBXPointToVector3Remap(translation);
        outNode.scale = FBXPointToVector3(scale);
        outNode.rotation = ExtractQuaternionFromFBXEuler(eulerRotation);

        outNode.name = pFbxNode->GetNameWithoutNameSpacePrefix();

        FbxProperty prop = pFbxNode->GetFirstProperty();
        while (prop.IsValid())
        {
            if (prop.GetFlag(FbxPropertyFlags::eUserDefined))
            {
                FBXImportNodeUserData userData{};
                userData.name = prop.GetName();

                FbxDataType lPropertyDataType = prop.GetPropertyDataType();
                std::string lName = lPropertyDataType.GetName();
                bool validprop = true;
                if (lPropertyDataType.Is(FbxBoolDT))
                {
                    userData.data_type_indicator = UserDataBool;
                    userData.boolData = prop.Get<FbxBool>();
                }
                else if (lPropertyDataType.Is(FbxFloatDT) || lPropertyDataType.Is(FbxDoubleDT) || lPropertyDataType.Is(FbxRealDT))
                {
                    userData.data_type_indicator = UserDataFloat;
                    userData.floatData = prop.Get<FbxFloat>();
                }
                else if (lPropertyDataType.Is(FbxColor3DT) || lPropertyDataType.Is(FbxColor4DT))
                {
                    userData.data_type_indicator = UserDataColor;
                    FbxColor c = prop.Get<FbxColor>();
                    userData.colorData = FBXColorToColorRGBA(c);
                }
                else if (lPropertyDataType.Is(FbxIntDT))
                {
                    userData.data_type_indicator = UserDataInt;
                    userData.intData = prop.Get<FbxInt>();
                }
                else if (lPropertyDataType.Is(FbxDouble4DT))
                {
                    userData.data_type_indicator = UserDataVector;
                    FbxDouble4 v = prop.Get<FbxDouble4>();
                    for (int i = 0; i != 4; i++)
                    {
                        userData.vectorData[i] = static_cast<float>(v[i]);
                    }
                }
                else if (lPropertyDataType.Is(FbxDouble3DT))
                {
                    userData.data_type_indicator = UserDataVector;
                    FbxDouble3 v = prop.Get<FbxDouble3>();
                    for (int i = 0; i != 3; i++)
                    {
                        userData.vectorData[i] = static_cast<float>(v[i]);
                    }
                    userData.vectorData[3] = 0;
                }
                else if (lPropertyDataType.Is(FbxStringDT))
                {
                    userData.data_type_indicator = UserDataString;
                    FbxString s = prop.Get<FbxString>();
                    userData.stringData = s;
                }
                else
                {
                    validprop = false;
                }

                if (validprop)
                {
                    outNode.userData.emplace_back(userData);
                }
            }

            prop = pFbxNode->GetNextProperty(prop);
        }

        mFBXNodeMap[pFbxNode] = &outNode;

        FbxMesh* pFbxMesh = pFbxNode->GetMesh();
        if (pFbxMesh)
        {
            SharedMeshInfo* pSharedMeshInfo = nullptr;

            FBXMeshToSharedMeshInfoMap::iterator iter = fbxMeshToSharedMeshInfoMap.find(pFbxMesh);
            if (iter != fbxMeshToSharedMeshInfoMap.end())
            {
                pSharedMeshInfo = &iter->second;
            }
            else
            {
                const int cnMeshIndex = static_cast<int>(scene.meshes.size());

                std::pair<FBXMeshToSharedMeshInfoMap::iterator, bool> pairibInsert = fbxMeshToSharedMeshInfoMap.insert(std::make_pair(pFbxMesh, SharedMeshInfo()));
                Assert(pairibInsert.second);
                pSharedMeshInfo = &pairibInsert.first->second;
                pSharedMeshInfo->nIndex = cnMeshIndex;

                scene.meshes.push_back(FBXImportMesh());

                FBXImportMesh imesh;
                ConvertFBXMesh(mFbxSdkManager, fbxScene, *pFbxNode, imesh /*scene.meshes.back()*/, scene, mImportSettings.ImportNormals, mImportSettings.ImportTangents, fbxMaterialLookup);
                Triangulate(imesh, scene.meshes.back(), false);
            }

            Assert(pSharedMeshInfo);
            pSharedMeshInfo->vecFbxNodes.emplace_back(pFbxNode);
            outNode.meshIndex = pSharedMeshInfo->nIndex;
        }

        int nCount = pFbxNode->GetChildCount();
        outNode.children.resize(nCount);
        for (int i = 0; i < nCount; ++i)
        {
            FbxNode* pChildNode = pFbxNode->GetChild(i);
            ImportNodesRecursively(fbxScene, pChildNode, outNode.children[i], scene, fbxMeshToSharedMeshInfoMap, fbxMaterialLookup, parentBone);
        }
    }

    static std::string GetText(const FbxString& fbxMainText, const FbxString& fbxAlternativeText)
    {
        std::string mainText = static_cast<const char*>(fbxMainText);
        std::string alternativeText = static_cast<const char*>(fbxAlternativeText);

        return mainText.empty() ? alternativeText : mainText;
    }

    static void GetSceneInfo(FbxDocumentInfo* pFbxDocumentInfo, std::string& applicationName, std::string& applicationVersion, std::string& author)
    {
        if (!pFbxDocumentInfo)
            return;

        if (applicationName.empty())
        {
            applicationName = GetText(pFbxDocumentInfo->LastSaved_ApplicationName.Get(), pFbxDocumentInfo->Original_ApplicationName.Get());
        }
        if (applicationVersion.empty())
        {
            applicationVersion = GetText(pFbxDocumentInfo->LastSaved_ApplicationVersion.Get(), pFbxDocumentInfo->Original_ApplicationVersion.Get());
        }
        if (author.empty())
        {
            author = pFbxDocumentInfo->mAuthor;
        }
    }

    // We can't use std::vector<std::vector>, because it seems that it makes
    // FBX SDK crash somewhere where it uses std::vector<std::vector>::push_back
    // For more details see: http://area.autodesk.com/forum/autodesk-fbx/fbx-sdk/problems-with-stdvectorstdstring/
    //
    // TODO(maxwan) : this wrapper class should be removed sometime later
    // it might be posible to do in FBX SDK 2011.3, because it used standalone dlls
    //
    // TODO(maxwan) : defining _SECURE_SCL=0 allows to get rid of this wrapper

    // ADSK: FBX SDK does not use std::string anymore, this hack could be safely removed
    // ADSK: Also, the FBX SDK compile options does not change the default value of _SECURE_SCL so this should not be changed when compiling this unit
    struct StringWrapper
    {
        StringWrapper(const std::string& s)
            : str(s)
        {}
        std::string str;
    };

    static std::vector<StringWrapper> Split(const std::string& str)
    {
        std::vector<StringWrapper> res;
        std::ostringstream currentStr;

        for (unsigned int i = 0, size = (UInt32)str.size(); i < size; ++i)
        {
            char c = str[i];
            if (c == ' ')
            {
                std::string s = currentStr.str();
                if (!s.empty())
                {
                    res.push_back(StringWrapper(s));
                    // currentStr.clear();
                    // currentStr = std::ostringstream();
                    currentStr.str("");
                }
            }
            else
                currentStr << c;
        }

        std::string s = currentStr.str();
        if (!s.empty())
            res.push_back(StringWrapper(s));

        return res;
    }

    void FBXNewImporter::ExtractSceneInfo(FbxImporter& importer, FbxScene& fbxScene, FBXImportInfo& sceneInfo)
    {
        FbxIOFileHeaderInfo* pFileHeader = importer.GetFileHeaderInfo();
        std::string strCreator = pFileHeader ? static_cast<const char*>(pFileHeader->mCreator) : "";

        std::string applicationName, applicationVersion, author;

        GetSceneInfo(fbxScene.GetSceneInfo(), applicationName, applicationVersion, author);
        GetSceneInfo(importer.GetSceneInfo(), applicationName, applicationVersion, author);

        sceneInfo.exporterInfo.clear();

        const std::string kUnknown = "Unknown";

        if (applicationName.empty())
        {
            // If applicationName is not filled we can still detect it from other fields.
            //
            // We know that Lightwave can fill it's fields in this way:
            // a) totally empty (even strCreator)
            // b) mComment=Lightwave Scene export
            //    mSubject=FBX Scene
            //    mKeywords=Scene
            //    mAuthor=Lightwave FBX Exporter
            //    strCreator="FBX SDK/FBX Plugins version 2009.1"
            // c) strCreator="FBX SDK/FBX Plugins build Mon 08/01/2005"
            //
            // We know that MotionBuilder can fill it's fields in this way:
            // a) strCreator="MotionBuilder version 10.0 build 07/20/2009"
            // b) strCreator="MotionBuilder/MoCap/OnLine version 6.0 build 20041028"
            // c) strCreator="MotionBuilder/MoCap/OnLine version 7.5 build 20080515"
            //
            // We asked Blender to fill it's fields in this way:
            // a) strCreator="Blender version 2.55 (sub 0)"

            if (author == "Lightwave FBX Exporter")
                applicationName = "Lightwave";
            else if (BeginsWith(strCreator, "MotionBuilder"))
            {
                applicationName = "MotionBuilder";

                std::vector<StringWrapper> res = Split(strCreator);
                if (res.size() > 4 && res[1].str == "version" && res[3].str == "build")
                {
                    sceneInfo.applicationDetailedName = res[0].str + " v" + res[2].str + " b" + res[4].str;
                    for (unsigned int i = 5; i < res.size(); ++i)
                    {
                        sceneInfo.applicationDetailedName += " " + res[i].str;
                    }
                }
                else
                {
                    sceneInfo.applicationDetailedName = strCreator;
                }

                sceneInfo.exporterInfo = "FBX:" + kUnknown;
            }
            else if (BeginsWith(strCreator, "Blender"))
            {
                applicationName = "Blender";
                sceneInfo.applicationDetailedName = strCreator;
                sceneInfo.exporterInfo = "FBX:" + kUnknown;
            }
        }
        else if (applicationVersion.empty())
        {
            // We know that old CityEngine versions put all info into applicationName and
            // leaves applicationVersion empty. This messes up our analytics reports, so
            // we added this hack to parse applicationVersion from applicationName.
            // applicationName is formatted as "CityEngine 2010.2 1005R"

            const std::string kCityEngine = "CityEngine";
            std::string::size_type pos = applicationName.find(kCityEngine);
            if (pos == 0)
            {
                applicationVersion = applicationName.substr(kCityEngine.size());
                applicationName = applicationName.substr(0, kCityEngine.size() - 1);
            }
        }

        sceneInfo.hasApplicationName = !applicationName.empty();
        sceneInfo.applicationName = sceneInfo.hasApplicationName ? applicationName : kUnknown;

        if (sceneInfo.applicationDetailedName.empty())
        {
            sceneInfo.applicationDetailedName = sceneInfo.applicationName + (applicationVersion.empty() ? "" : " " + applicationVersion);
        }

        if (sceneInfo.exporterInfo.empty())
        {
            // parse strCreator (i.e. make it shorter)
            const std::string buildSubStr = "FBX SDK/FBX Plugins build ";
            const std::string versionSubStr = "FBX SDK/FBX Plugins version ";
            if (BeginsWith(strCreator, buildSubStr))
            {
                strCreator = "b" + strCreator.substr(buildSubStr.size());
            }
            else if (BeginsWith(strCreator, versionSubStr))
            {
                strCreator = "v" + strCreator.substr(versionSubStr.size());
            }
            else if (strCreator.empty())
            {
                strCreator = kUnknown;
            }

            sceneInfo.exporterInfo = "FBX:" + strCreator;
        }
    }

    void FBXNewImporter::ConvertFBXMesh(FbxManager* pFbxSdkManager, FbxScene& fbxScene, FbxNode& fbxNode, FBXImportMesh& mesh, FBXImportScene& scene, const bool cbImportNormals, const bool cbImportTangets,
                                        FBXMaterialLookup& fbxMaterialLookup)
    {
        FbxMesh& fbxMesh = *fbxNode.GetMesh();
        const std::string strMeshName = static_cast<const char*>(fbxNode.GetNameWithoutNameSpacePrefix());
        const int cnVertexCount = fbxMesh.GetControlPointsCount();
        const bool cnIsSkinned = fbxMesh.GetDeformer(0, FbxDeformer::eSkin) != nullptr;

        // Import mesh vertices
        std::vector<int> vecInvalidVertices;
        ConvertMaterials::Get().ImportVerticesPosition(fbxNode, fbxMesh, nullptr, mesh.vertices, vecInvalidVertices);
        if (!vecInvalidVertices.empty())
        {
            ConvertMaterials::Get().PrintInvalidList(strMeshName, 10, cnVertexCount, vecInvalidVertices, "invalid vertices (NaNs). They will be assigned value (0,0,0)");
        }

        // Import mesh indices
        int nPolygonCount = fbxMesh.GetPolygonCount();
        int* indices = fbxMesh.GetPolygonVertices();

        if (nPolygonCount <= 0)
            return;

        mesh.polygonSizes.resize(nPolygonCount);
        int polygonIndexCount = 0;
        for (int i = 0; i < nPolygonCount; i++)
        {
            mesh.polygonSizes[i] = fbxMesh.GetPolygonSize(i);
            polygonIndexCount += mesh.polygonSizes[i];
        }
        mesh.polygons.assign(indices, indices + polygonIndexCount);

        UInt32* polygonSizes = &mesh.polygonSizes[0];

        ConvertMaterials::Get().ImportUVs(fbxMesh, mesh, indices, polygonIndexCount, polygonSizes, nPolygonCount, cnVertexCount, strMeshName);

        // Import wedge data through layers
        const int cnMainLayer = 0;
        FbxLayer* const pFbxLayer = fbxMesh.GetLayer(cnMainLayer);

        // Vertex colors
        if (pFbxLayer && pFbxLayer->GetVertexColors())
            ConvertMaterials::Get().ExtractWedgeLayerData(nullptr, *pFbxLayer->GetVertexColors(), mesh.colors, indices, polygonIndexCount, polygonSizes, nPolygonCount, cnVertexCount, "vertex Colors", strMeshName);

        bool bImportTangentSucc = false;
        if (!cbImportNormals)
        {
            if (cbImportTangets)
            {
                ReportWarning("Can't import tangents without importing normals for mesh '%s'.\n", strMeshName.c_str());
            }
        }
        else
        {
            // Normals
            if (pFbxLayer && !pFbxLayer->GetNormals())
            {
                // no normal in model file, generate smooth  normals insted;
                std::vector<cross::Vector3f> normals;
                normals.resize(mesh.vertices.size(), cross::Vector3f::Zero());

                int idx = 0;
                for (int i = 0; i < mesh.polygonSizes.size(); i++)
                {
                    // only calculate triangle
                    Assert(mesh.polygonSizes[i] == 3);

                    const auto& v0 = mesh.vertices[mesh.polygons[idx + 0]];
                    const auto& v1 = mesh.vertices[mesh.polygons[idx + 1]];
                    const auto& v2 = mesh.vertices[mesh.polygons[idx + 2]];

                    auto normal = (v2 - v0).cross(v1 - v0);
                    normals[mesh.polygons[idx + 0]] += normal;
                    normals[mesh.polygons[idx + 1]] += normal;
                    normals[mesh.polygons[idx + 2]] += normal;
                    idx += 3;
                }

                for (int i = 0; i < normals.size(); i++)
                {
                    normals[i].stableNormalize();
                }

                // remap vertex normal to indices
                mesh.normals.resize(polygonIndexCount);

                for (int i = 0; i < polygonIndexCount; i++)
                {
                    int vertex_id = mesh.polygons[i];
                    mesh.normals[i] = normals[vertex_id];
                }

                ReportWarning("Can't import normals, because mesh '%s' doesn't have it.\n", strMeshName.c_str());
            }
            else
            {
                ConvertMaterials::Get().ExtractWedgeLayerData(nullptr, *pFbxLayer->GetNormals(), mesh.normals, indices, polygonIndexCount, polygonSizes, nPolygonCount, cnVertexCount, "normals", strMeshName);
            }

            // Convert normal from local to global space
            if (!cnIsSkinned)
            {
                FbxAMatrix GlobalMat = fbxNode.EvaluateGlobalTransform(0);
                FbxAMatrix GlobalRot;
                GlobalRot.SetR(GlobalMat.GetR());
                auto GlobalRotM = FBXMatrixToMatrix4x4(GlobalRot);

                for (auto& normal : mesh.normals)
                {
                    AssetMath::Vector4f GlobalSpaceNormal = GlobalRotM * AssetMath::Vector4f(normal.x(), normal.y(), normal.z(), 1.0f);
                    normal = { GlobalSpaceNormal.x(), GlobalSpaceNormal.y(), GlobalSpaceNormal.z() };
                }
            }

            // Tangents
            if (cbImportTangets)
            {
                if (!pFbxLayer->GetTangents() || !pFbxLayer->GetBinormals())
                {
                    ReportWarning("Can't import tangents and binormals, because mesh '%s' doesn't have it.\n", strMeshName.c_str());
                }
                else
                {
                    std::vector<AssetMath::Vector3f> tangents, binormals;
                    ConvertMaterials::Get().ExtractWedgeLayerData(nullptr, *pFbxLayer->GetTangents(), tangents, indices, polygonIndexCount, polygonSizes, nPolygonCount, cnVertexCount, "tangents", strMeshName);
                    ConvertMaterials::Get().ExtractWedgeLayerData(nullptr, *pFbxLayer->GetBinormals(), binormals, indices, polygonIndexCount, polygonSizes, nPolygonCount, cnVertexCount, "binormals", strMeshName);

                    if (mesh.normals.size() != tangents.size() || mesh.normals.size() != binormals.size())
                    {
                        ReportError("Internal FBXNewImporter error: normal count (%d) doesn't match tangent (%d) or/and binormal count (%d).\n", mesh.normals.size(), tangents.size(), binormals.size());
                    }
                    else
                    {
                        bImportTangentSucc = true;

                        if (!cnIsSkinned)
                        {
                            // convert to global space
                            FbxAMatrix GlobalMat = fbxNode.EvaluateGlobalTransform(0);
                            FbxAMatrix GlobalRot;
                            GlobalRot.SetR(GlobalMat.GetR());
                            auto GlobalRotM = FBXMatrixToMatrix4x4(GlobalRot);

                            for (auto& tangent : tangents)
                            {
                                AssetMath::Vector4f globalSpaceTangent = GlobalRotM * AssetMath::Vector4f(tangent.x(), tangent.y(), tangent.z(), 1.0f);
                                tangent = { globalSpaceTangent.x(), globalSpaceTangent.y(), globalSpaceTangent.z() };
                            }
                            for (auto& binormal : binormals)
                            {
                                AssetMath::Vector4f globalSpaceBinormal = GlobalRotM * AssetMath::Vector4f(binormal.x(), binormal.y(), binormal.z(), 1.0f);
                                binormal = { globalSpaceBinormal.x(), globalSpaceBinormal.y(), globalSpaceBinormal.z() };
                            }
                        }

                        // Converting tangents(x,y,z) and binormals(x,y,z) into tangents(x,y,z,sign)
                        mesh.tangents.resize(tangents.size());
                        for (int i = 0, size = static_cast<int>(tangents.size()); i < size; ++i)
                        {
                            const AssetMath::Vector3f& tangent = tangents[i];

                            AssetMath::Vector3f binormal = mesh.normals[i].cross(tangent);
                            float sign = binormal.dot(binormals[i]);

                            mesh.tangents[i] = AssetMath::Vector4f{ tangent.x(), tangent.y(), tangent.z(), (sign > 0 ? 1.f : -1.f) };
                        }
                    }
                }
            }
        }

        // calculate normals from smoothing information only for meshes with blendShapes,
        // because it seems that a lot of meshes have both smoothing and normals information
        if (pFbxLayer && fbxMesh.GetShapeCount() > 0)
        {
            FbxLayerElementSmoothing* smoothing = pFbxLayer->GetSmoothing();

            if (smoothing)
            {
                if (smoothing->GetMappingMode() == FbxLayerElement::eByEdge)
                {
                    FbxGeometryConverter converter(pFbxSdkManager);

                    bool res = converter.ComputePolygonSmoothingFromEdgeSmoothing(&fbxMesh, cnMainLayer);
                    if (!res)
                    {
                        ReportWarning("Failed to convert smoothing information from by-edge, to by-polygon on mesh '%s'. Please report this bug.\n", strMeshName.c_str());
                    }
                }

                ConvertMaterials::Get().ExtractWedgeLayerData(&fbxMesh, *smoothing, mesh.smoothingGroups, indices, polygonIndexCount, polygonSizes, nPolygonCount, cnVertexCount, "smoothing", strMeshName);

                if (!mesh.smoothingGroups.empty())
                {
                    NormalCalculation::CalculateNormalsFromSmoothingGroups(mesh.polygonSizes, mesh.polygons, mesh.smoothingGroups, mesh.vertices, mesh.normals);
                }
            }
        }

        // only import tangents 
        // when setted and import failed and uv[0] is ready
        if (cbImportTangets && !bImportTangentSucc && !mesh.uvs[0].empty())
        {
            GenerateMikkTSpace(mesh);
            fbxMesh.GenerateTangentsData(0, true);
        }

        ConvertMaterials::Get().ConvertFBXMeshMaterials(pFbxSdkManager, fbxScene, fbxNode, fbxMesh, strMeshName, mesh, scene, nPolygonCount, fbxMaterialLookup);

        /*
        if (mImportSettings.GenerateSecondaryUV && uvCount == 1)
        {
            std::vector<std::string> warnings;
            std::string error;
            GenerateSecondaryUVSetImportMesh(mesh, mImportSettings, warnings, error);
        }
        */

        // Import name
        mesh.name = fbxNode.GetNameWithoutNameSpacePrefix();
    }

    // Blend shape import
    static void ConvertFBXShape(const int meshVertexCount, const FBXImportMesh& mesh, const FbxShape& fbxShape, FBXImportBlendShape& meshShape)
    {
        const int count = fbxShape.GetControlPointsCount();
        if (count != meshVertexCount)
        {
            ReportError("Can't import blendshape '%s' on mesh '%s', because vertex count on shape (%d) doesn't match vertex count on mesh (%d)", fbxShape.GetName(), mesh.name.c_str(), count, meshVertexCount);

            meshShape.vertices.resize(mesh.vertices.size(), AssetMath::Vector3f::Zero());
            meshShape.normals.resize(mesh.normals.size(), AssetMath::Vector3f::Zero());
        }
        else
        {
            std::vector<int> invalidVertices;
            ConvertMaterials::Get().ImportVertices(fbxShape, &mesh.vertices, meshShape.vertices, invalidVertices);
            if (!invalidVertices.empty())
            {
                ConvertMaterials::Get().PrintGenericInvalidList(
                    Format("Blendshape '{}' on mesh '{}'", fbxShape.GetName(), mesh.name), 10, static_cast<int>(meshShape.vertices.size()), invalidVertices, "invalid vertices (NaNs). They will be assigned value (0,0,0)");
            }

            if (!mesh.smoothingGroups.empty())
            {
                NormalCalculation::CalculateNormalsFromSmoothingGroups(mesh.polygonSizes, mesh.polygons, mesh.smoothingGroups, meshShape.vertices, meshShape.normals);
            }
        }
    }

    static void ConvertFBXShapes(const FbxMesh& fbxMesh, FBXImportMesh& mesh)
    {
        Assert(mesh.shapes.empty() && mesh.shapeChannels.empty());

        const int cnMeshVertexCount = fbxMesh.GetControlPointsCount();

        mesh.shapes.reserve(fbxMesh.GetShapeCount());
        mesh.shapeChannels.reserve(fbxMesh.GetShapeCount());

        const int cnBlendShapeDeformerCount = fbxMesh.GetDeformerCount(FbxDeformer::eBlendShape);
        for (int i = 0; i < cnBlendShapeDeformerCount; ++i)
        {
            const FbxBlendShape* blendShapeDeformer = (const FbxBlendShape*)fbxMesh.GetDeformer(i, FbxDeformer::eBlendShape);

            const int blendShapeChannelCount = blendShapeDeformer->GetBlendShapeChannelCount();
            for (int j = 0; j < blendShapeChannelCount; ++j)
            {
                const FbxBlendShapeChannel* blendShapeChannel = blendShapeDeformer->GetBlendShapeChannel(j);
                const int targetShapeCount = blendShapeChannel->GetTargetShapeCount();
                const double* weights = const_cast<FbxBlendShapeChannel*>(blendShapeChannel)->GetTargetShapeFullWeights();

                if (targetShapeCount == 0)
                    continue;

                mesh.shapeChannels.push_back(FBXFBXImportBlendShapeChannel());
                FBXFBXImportBlendShapeChannel& channel = mesh.shapeChannels.back();

                channel.name = GenerateBlendShapeName(blendShapeChannel);
                channel.frameIndex = static_cast<int>(mesh.shapes.size());
                channel.frameCount = targetShapeCount;

                for (int k = 0; k < targetShapeCount; ++k)
                {
                    const FbxShape* shape = blendShapeChannel->GetTargetShape(k);

                    mesh.shapes.push_back(FBXImportBlendShape());
                    FBXImportBlendShape& frame = mesh.shapes.back();

                    ConvertFBXShape(cnMeshVertexCount, mesh, *shape, frame);

                    frame.targetWeight = static_cast<float>(weights[k]);
                }
            }
        }
    }

    void FBXNewImporter::ImportBlendShapes(FBXMeshToSharedMeshInfoMap& meshMap, FBXImportScene& scene)
    {
        for (FBXMeshToSharedMeshInfoMap::iterator iter = meshMap.begin(); iter != meshMap.end(); ++iter)
        {
            FbxMesh* pFbxMesh = iter->first;
            FBXImportMesh& mesh = scene.meshes[iter->second.nIndex];

            ConvertFBXShapes(*pFbxMesh, mesh);
        }
    }


    // checks if all indices and weigths are the same
    static void ValidateIdenticalWeigths(const std::vector<ImportVertexBone>& influences, const int boneCount, const char* meshName)
    {
        if (influences.empty())
            return;

        const ImportVertexBone& firstBI = influences[0];

        // checking if weights are the same on first bone
        for (int j = 1; j < 4; ++j)
        {
            if (firstBI.Weights[0] != firstBI.Weights[j])
                return;
        }

        // checking if weights and indices on other bones match the ones on the first bone
        for (int i = 1, size = static_cast<int>(influences.size()); i < size; ++i)
        {
            const ImportVertexBone& bi = influences[i];
            for (int j = 0; j < 4; ++j)
            {
                if (bi.BoneIndices[j] != firstBI.BoneIndices[j] || bi.Weights[j] != firstBI.Weights[j])
                    return;
            }
        }

        ReportWarning("All vertices are affected by same bones (%d, %d, %d, %d) and same weights (%f, %f, %f, %f) on mesh '%s'. Bone count: %d; vertex count: %d.\n",
                      firstBI.BoneIndices[0],
                      firstBI.BoneIndices[1],
                      firstBI.BoneIndices[2],
                      firstBI.BoneIndices[3],
                      firstBI.Weights[0],
                      firstBI.Weights[1],
                      firstBI.Weights[2],
                      firstBI.Weights[3],
                      meshName,
                      boneCount,
                      influences.size());
    }

    // checks if all indices and weigths are in range
    static void ValidateBoneWeightRanges(const std::vector<ImportVertexBone>& influences, const int boneCount, const char* meshName)
    {
        const int kMaxInvalidCount = 10;
        int invalidCount = 0;
        std::ostringstream oss;

        // checking that weights and indices are in range
        for (int i = 0, size = static_cast<int>(influences.size()); i < size; ++i)
        {
            const ImportVertexBone& bi = influences[i];

            std::ostringstream ossBI;
            for (int j = 0; j < 4; ++j)
            {
                // TODO(maxwan) : this useless, we never run in debug mode!
                // Assert(!IsNAN(bi.weight[j]));
                // Assert(bi.weight[j] >= 0 && bi.weight[j] <= 1);
                // Assert(bi.boneIndex[j] >= 0 && bi.boneIndex[j] < boneCount);

                if (!IsFinite(bi.Weights[j]) || bi.BoneIndices[j] < 0 || bi.BoneIndices[j] > (UInt32)boneCount || bi.Weights[j] < 0 || bi.Weights[j] > 1)
                {
                    if (j > 0)
                    {
                        ossBI << ", ";
                    }

                    ossBI << "(" << bi.BoneIndices[j] << "; " << bi.Weights[j] << ")";
                }
            }

            const std::string invalidBI = ossBI.str();
            if (!invalidBI.empty())
            {
                if (invalidCount < kMaxInvalidCount)
                {
                    if (invalidCount > 0)
                    {
                        oss << "\n";
                    }

                    oss << invalidCount << ": (" << invalidBI << ")";
                }
                ++invalidCount;
            }
        }

        if (invalidCount > 0)
        {
            if (invalidCount > kMaxInvalidCount)
            {
                oss << "\nand so on..";
            }

            ReportWarning("Mesh '%s' (bone count: %d) has invalid %d (out of %d) BoneWeights (bone index; weight): \n%s.\n", meshName, boneCount, invalidCount, influences.size(), oss.str().c_str());
        }
    }

    static void ValidateSkin(const std::vector<ImportVertexBone>& influences, const int boneCount, const char* meshName)
    {
        ValidateIdenticalWeigths(influences, boneCount, meshName);
        ValidateBoneWeightRanges(influences, boneCount, meshName);
    }

    void FBXNewImporter::ImportSkin(FbxMesh* pFbxMesh, FBXImportMesh& importMesh, FBXImportScene& scene, const std::map<FbxNode*, FBXImportNode*>& fbxNodeMap, const FBXMeshToSharedMeshInfoMap& fbxMeshToInfoMap)
    {
        FbxSkin* skin = nullptr;
        int boneCount = 0;
        if (pFbxMesh)
        {
            // int deformerCount = mesh->GetDeformerCount();
            int skinDeformerCount = pFbxMesh->GetDeformerCount(FbxDeformer::eSkin);
            if (skinDeformerCount != 0)
            {
                skin = static_cast<FbxSkin*>(pFbxMesh->GetDeformer(0, FbxDeformer::eSkin));
                boneCount = skin->GetClusterCount();
            }
        }

        if (boneCount == 0)
        {
            skin = nullptr;
        }

        // Ignore the skin if the only bone links to the node itself
        if (boneCount == 1)
        {
            auto it = fbxMeshToInfoMap.find(pFbxMesh);
            Assert(it != fbxMeshToInfoMap.end());
            if (it != fbxMeshToInfoMap.end())
            {
                const SharedMeshInfo& smInfo = it->second;

                FbxCluster* cluster = skin->GetCluster(0);
                if (smInfo.vecFbxNodes.size() == 1 && cluster != nullptr && cluster->GetLink() == smInfo.vecFbxNodes[0])
                {
                    skin = nullptr;
                }
            }
        }

        if (skin != nullptr)
        {
            importMesh.bones.resize(boneCount);
            importMesh.skin.resize(importMesh.vertices.size(), ImportVertexBone{});

            std::vector<ImportVertexBone>& influences = importMesh.skin;

            std::set<FbxNode*> duplicates;

            int outBoneIndex = 0;
            for (int inbone = 0; inbone < boneCount; inbone++)
            {
                FbxCluster* cluster = skin->GetCluster(inbone);

                std::map<FbxNode*, FBXImportNode*>::const_iterator itFbxNodeMap = fbxNodeMap.find(cluster->GetLink());
                if (cluster == nullptr || itFbxNodeMap == fbxNodeMap.end())
                {
                    importMesh.bones.resize(importMesh.bones.size() - 1);
                    continue;
                }

                bool isDuplicate = (duplicates.count(cluster->GetLink()) != 0);
                FbxString boneName = cluster->GetLink()->GetNameWithoutNameSpacePrefix();

                auto clusterLink = cluster->GetLink();
                int boneIndex = SK_BONE_INDEX_NONE;
                auto iterNode = mBoneNodeToIDMap.find(clusterLink);
                if (mBoneNodeToIDMap.end() != iterNode)
                {
                    boneIndex = iterNode->second;
                }

                duplicates.insert(cluster->GetLink());
                importMesh.bones[outBoneIndex] = itFbxNodeMap->second->boneNodeIndex;

                // Weights and indices
                const double* weights = cluster->GetControlPointWeights();
                const int* indices = cluster->GetControlPointIndices();
                int controlPoints = cluster->GetControlPointIndicesCount();
                bool importWeights = true;

                if (isDuplicate)
                {
                    ReportWarning("Skipping bone because it is duplicate %s\n", boneName.Buffer());
                    importWeights = false;
                }

                // TODO(maxwan): we dont support eAdditive ( the mode used for deformers)
                FbxCluster::ELinkMode linkMode = cluster->GetLinkMode();
                if (linkMode == FbxCluster::eAdditive)
                {
                    ReportWarning("Skipping influences for '%s' because it is marked as ADDITIVE. This most likely means that "
                                  "you are using a blend shape or custom deformer, which is not supported.\n",
                                  boneName.Buffer());
                    importWeights = false;
                }

                if (importWeights)
                {
                    // Go through all control points
                    // -  Find and replace the smallest weight with the current weight!
                    for (int i = 0; i < controlPoints; i++)
                    {
                        int vertexIndex = indices[i];
                        float weight = static_cast<float>(weights[i]);

                        // Weights are sorted (most important weights first)
                        for (int j = 0; j < 4; j++)
                        {
                            // Check out of range vertex index
                            if (vertexIndex < 0 || vertexIndex >= static_cast<int>(influences.size()))
                                continue;

                            if (weight >= influences[vertexIndex].Weights[j])
                            {
                                // Push back old weights!
                                for (int k = 2; k >= j; k--)
                                {
                                    influences[vertexIndex].Weights[k + 1] = influences[vertexIndex].Weights[k];
                                    influences[vertexIndex].BoneIndices[k + 1] = influences[vertexIndex].BoneIndices[k];
                                }
                                // Replace the weights!
                                influences[vertexIndex].Weights[j] = weight;
                                influences[vertexIndex].BoneIndices[j] = boneIndex;   // importMesh.bones[outBoneIndex];
                                break;
                            }
                        }
                    }
                }

                outBoneIndex++;
            }

            // only invalid links
            if (importMesh.bones.empty())
            {
                importMesh.skin.clear();
                influences.clear();
            }

            std::vector<int> invalidWeights, invalidBones;
            invalidWeights.reserve(influences.size());
            invalidBones.reserve(influences.size());

            ///@TODO(maxwan): NORMALIZE WEIGHTS IF WE SHOULD!
            /////TODO(maxwan): we dont support eTOTAL1 - Is this true? It seems that it would already be normalized.
            // Normalize the skin weights!
            for (UInt32 i = 0; i < influences.size(); i++)
            {
                float weightsum = 0.0F;

                for (int j = 0; j < 4; j++)
                {
                    weightsum += influences[i].Weights[j];
                }

                const float kWeigthEpsilon = 0.00000001F;
                if (weightsum < kWeigthEpsilon)
                {
                    for (int j = 0; j < 4; j++)
                    {
                        if (influences[i].BoneIndices[j] != SK_BONE_INDEX_NONE)
                        {
                            influences[i].Weights[j] = 1.0F;
                        }
                    }

                    weightsum = 0.0F;
                    for (int j = 0; j < 4; j++)
                    {
                        weightsum += influences[i].Weights[j];
                    }

                    if (weightsum < kWeigthEpsilon)
                    {
                        invalidBones.push_back(i);

                        influences[i].Weights[0] = weightsum = 1;
                        influences[i].BoneIndices[0] = 0;
                    }
                    else
                    {
                        invalidWeights.push_back(i);
                    }
                }

                weightsum = 1.0F / weightsum;
                for (int j = 0; j < 4; j++)
                {
                    influences[i].Weights[j] *= weightsum;

                    if (influences[i].BoneIndices[j] == SK_BONE_INDEX_NONE)
                    {
                        influences[i].BoneIndices[j] = 0;
                    }
                }
            }

            ValidateSkin(influences, boneCount, importMesh.name.c_str());
        }
    }

    bool FBXNewImporter::SplitMeshByMaterial(FBXImportScene& scene, const FBXImportNode& in_node, std::vector<FBXImportNode>& subNodes, std::map<int, int>& materialID)
    {
        if (in_node.meshIndex == -1 || scene.meshes[in_node.meshIndex].materials.size() < 2)
        {
            return false;
        }

        const FBXImportMesh* mesh = &scene.meshes[in_node.meshIndex];
        if (mesh->vertices.empty())
            return false;
        AssertIf(mesh->polygonSizes.size() != mesh->materials.size());

        // materialKind Grab same material mesh in key & first - triangle num, follow - triangle vertex indices's index
        std::map<int, std::vector<int>> materialKind;
        unsigned int size = (unsigned int)mesh->polygonSizes.size();
        unsigned int i = 0;
        int index = 0;
        for (i = 0; i < size; ++i)
        {
            int materialIndex = mesh->materials[i];
            if (materialKind.find(materialIndex) == materialKind.end())
            {
                materialKind[materialIndex] = std::move(std::vector<int>());
            }

            // save first info
            materialKind[materialIndex].push_back(mesh->polygonSizes[i]);
            for (int j = 0; j < static_cast<int>(mesh->polygonSizes[i]); ++j)
            {
                // save follow info
                materialKind[materialIndex].push_back(index + j);
            }
            index += mesh->polygonSizes[i];
        }

        if (materialKind.size() == 1)
            materialID[in_node.meshIndex] = (*materialKind.begin()).first;
        if (materialKind.size() < 2)
            return false;

        i = 0;

        scene.meshes.reserve(scene.meshes.size() + materialKind.size());
        for (decltype(materialKind)::const_iterator it = materialKind.begin(); it != materialKind.end(); ++i, ++it)
        {
            subNodes.push_back(FBXImportNode());
            auto& node = subNodes.back();
            node.name = in_node.name + "_" + std::to_string(i);
            node.position = in_node.position;
            node.rotation = in_node.rotation;
            node.scale = in_node.scale;
            node.meshIndex = static_cast<int>(scene.meshes.size());
            node.meshTransform = in_node.meshTransform;
            node.cameraIndex = in_node.cameraIndex;
            node.lightIndex = in_node.lightIndex;
            node.userData = in_node.userData;
            materialID[node.meshIndex] = (*it).first;
            if (i == 0)
            {
                node.children = in_node.children;
            }

            scene.meshes.push_back(FBXImportMesh());
            auto& newMesh = scene.meshes.back();
            newMesh.name = node.name;
            mesh = &scene.meshes[in_node.meshIndex];
            const auto& polygons = it->second;
            int polygonSize = 0;

            std::vector<UInt32> insertedVerteies;

            for (unsigned int j = 0; j < polygons.size();)
            {
                polygonSize = polygons[j++];
                AssertIf(polygonSize != 3);
                newMesh.polygonSizes.push_back(3);
                for (int p = 0; p < polygonSize; ++p)
                {
                    int polygonIndex = polygons[j++];
                    int vertexIndex = mesh->polygons[polygonIndex];

                    auto itr = std::find_if(insertedVerteies.begin(), insertedVerteies.end(), [&](auto const& elem) { return elem == static_cast<UInt32>(vertexIndex); });
                    bool vertexExist = itr != insertedVerteies.end();

                    if (!vertexExist)
                    {
                        insertedVerteies.push_back(vertexIndex);
                        newMesh.vertices.push_back(mesh->vertices[vertexIndex]);

                        if (vertexIndex < mesh->skin.size())
                            newMesh.skin.push_back(mesh->skin[vertexIndex]);

                        newMesh.polygons.push_back((UInt32)newMesh.vertices.size() - 1);
                    }
                    else
                    {
                        UInt32 vertexIndexInMesh = static_cast<UInt32>(std::distance(insertedVerteies.begin(), itr));
                        newMesh.polygons.push_back(vertexIndexInMesh);
                    }

                    newMesh.normals.push_back(mesh->normals[polygonIndex]);

                    if (polygonIndex < mesh->tangents.size())
                        newMesh.tangents.push_back(mesh->tangents[polygonIndex]);
                    if (polygonIndex < mesh->colors.size())
                        newMesh.colors.push_back(mesh->colors[polygonIndex]);
                    if (polygonIndex < mesh->uvs[0].size())
                        newMesh.uvs[0].push_back(mesh->uvs[0][polygonIndex]);
                    if (polygonIndex < mesh->uvs[1].size())
                        newMesh.uvs[1].push_back(mesh->uvs[1][polygonIndex]);
                    if (polygonIndex < mesh->smoothingGroups.size())
                        newMesh.smoothingGroups.push_back(mesh->smoothingGroups[polygonIndex]);
                }
            }

            // Grab new Mesh skeleton
            if (mesh->bones.size() > 0)
                newMesh.bones = mesh->bones;
        }

        return true;
    }


    class MeshLimiter
    {
        // default we will not use uint16 index by default;
         unsigned int kMaxVertexCount = (std::numeric_limits<UInt32>::max)();
         unsigned int kMaxIndexCount = (std::numeric_limits<UInt32>::max)() * 3;
    public:
        template<class T>
        void SetMeshLimit()
        {
            kMaxIndexCount = (std::numeric_limits<T>::max)();
            kMaxIndexCount = (std::numeric_limits<T>::max)() * 3;
        }

        bool ValidateShapes(const FBXImportMesh& mesh)
        {
            for (int i = 0; i < mesh.shapes.size(); ++i)
            {
                const FBXImportBlendShape& shape = mesh.shapes[i];

                Assert(mesh.vertices.size() == shape.vertices.size());
                Assert(shape.normals.empty() || mesh.normals.size() == shape.normals.size());
                Assert(shape.tangents.empty() || mesh.tangents.size() == shape.tangents.size());
            }

            return true;
        }
        void ValidateMesh(const FBXImportMesh& src, const bool validateIndexLimits)
        {
            Assert(src.normals.empty() || src.vertices.size() == src.normals.size());
            Assert(src.tangents.empty() || src.vertices.size() == src.tangents.size());
            Assert(src.colors.empty() || src.vertices.size() == src.colors.size());
            Assert(src.uvs[0].empty() || src.vertices.size() == src.uvs[0].size());
            Assert(src.uvs[1].empty() || src.vertices.size() == src.uvs[1].size());

            Assert(src.materials.empty() || src.polygonSizes.size() == src.materials.size());
            Assert(ValidateShapes(src));

            if (validateIndexLimits)
            {
                for (int i = 0; i < src.polygons.size(); ++i)
                {
                    if (src.polygons[i] >= kMaxVertexCount)
                    {
                        LOG_ERROR("Meshes may not have more than 65000 vertices at the moment");
                    }
                }
                AssertMsg(src.polygons.size() < kMaxIndexCount, "Meshes may not have more than 65000 triangles at the moment");
            }
        }

        void AddVertex(const FBXImportMesh& srcMesh, FBXImportMesh& dstMesh, const int srcVertexIndex, std::vector<int>& srcToDstRemap)
        {
            int dstVertexIndex = srcToDstRemap[srcVertexIndex];

            if (dstVertexIndex < 0)
            {
                AddVertexByIndex(srcMesh, dstMesh, srcVertexIndex);
                AddPolygonAttribute(srcMesh, dstMesh, srcVertexIndex);

                dstVertexIndex = srcToDstRemap[srcVertexIndex] = static_cast<int>(dstMesh.vertices.size()) - 1;
            }

            dstMesh.polygons.push_back(dstVertexIndex);
        }


        FBXImportMesh* InstantiateSplitMesh(const FBXImportMesh& src, std::vector<FBXImportMesh>& dstMeshes, const int currentMesh)
        {
            dstMeshes.push_back(FBXImportMesh());
            FBXImportMesh* dst = &dstMeshes[currentMesh];

            dst->Reserve(kMaxVertexCount, kMaxIndexCount / 3, &src);

            dst->name = src.name;
            dst->bones = src.bones;

            return dst;
        }

        enum MeshLimitResult
        {
            kMeshLimitNone,
            kMeshLimitDidSplit,
            kMeshLimitCantSplit
        };

        MeshLimitResult LimitMeshSize(const FBXImportMesh& src, std::vector<FBXImportMesh>& dstMeshes)
        {
            bool needsSplit = src.polygons.size() >= kMaxIndexCount;

            if (!needsSplit)
            {
                for (int i = 0; i < src.polygons.size(); ++i)
                {
                    if (src.polygons[i] >= kMaxVertexCount)
                    {
                        needsSplit = true;
                        break;
                    }
                }
            }

            if (needsSplit && src.hasAnyQuads)
            {
                // Can not split meshes with quads yet
                return kMeshLimitCantSplit;
            }

            if (!needsSplit)
            {
                return kMeshLimitNone;
            }
            else
            {
                Assert(!src.polygons.empty());
                Assert(!src.hasAnyQuads);
                TriStripper::Adjacencies adjacienies(nullptr, &src.polygons[0], static_cast<int>(src.polygons.size()) / 3);

                // We use two structures for queuing polygons:
                // * queuedPolygons is used as a queue
                // * queuedPolygonSet is used to mark what is in queuedPolygons, so we don't get duplicates
                std::vector<char> queuedPolygonSet(src.polygons.size() / 3, 0);
                std::vector<UInt32> queuedPolygons;
                queuedPolygons.reserve(queuedPolygonSet.size());
                queuedPolygons.push_back(0);
                queuedPolygonSet[0] = 1;

                // currentQueuedPolygon is used for iterating queuedPolygons, so we don't have to do pop_front
                int currentQueuedPolygon = 0;
                int firstNonQueuedPolygon = 0;

                dstMeshes.clear();

                ValidateMesh(src, false);

                const int estimatedCount = static_cast<int>(src.polygons.size()) / kMaxIndexCount + 1;
                dstMeshes.reserve(estimatedCount * 2);

                int currentMesh = 0;
                FBXImportMesh* dst = InstantiateSplitMesh(src, dstMeshes, currentMesh);

                std::vector<int> vertexRemap(src.vertices.size(), -1);

                while (currentQueuedPolygon < queuedPolygons.size())
                {
                    if (dst->polygons.size() + 3 >= kMaxIndexCount || dst->vertices.size() + 3 >= kMaxVertexCount)
                    {
                        dst = InstantiateSplitMesh(src, dstMeshes, ++currentMesh);

                        // clearing up vertexRemap
                        std::fill(vertexRemap.begin(), vertexRemap.end(), -1);
                    }

                    const int currentPolygon = queuedPolygons[currentQueuedPolygon++];

                    AddVertex(src, *dst, src.polygons[currentPolygon * 3 + 0], vertexRemap);
                    AddVertex(src, *dst, src.polygons[currentPolygon * 3 + 1], vertexRemap);
                    AddVertex(src, *dst, src.polygons[currentPolygon * 3 + 2], vertexRemap);
                    dst->polygonSizes.push_back(3);

                    if (!src.materials.empty())
                    {
                        dst->materials.push_back(src.materials[currentPolygon]);
                    }

                    // adding adjacent polygons into queuedPolygons list
                    for (int i = 0; i < 3; ++i)
                    {
                        int adjacentPolygon = adjacienies.mFaces[currentPolygon].ATri[i];
                        if (IS_BOUNDARY(adjacentPolygon))
                            continue;

                        adjacentPolygon = MAKE_ADJ_TRI(adjacentPolygon);
                        Assert(adjacentPolygon >= 0 && adjacentPolygon < queuedPolygonSet.size());

                        if (!queuedPolygonSet[adjacentPolygon])
                        {
                            queuedPolygonSet[adjacentPolygon] = 1;
                            queuedPolygons.push_back(adjacentPolygon);
                        }
                    }

                    // if we ran out of adjacent polygons then just find first unconnected polygon
                    if (currentQueuedPolygon == queuedPolygons.size())
                    {
                        // firstNonQueuedPolygon - start search where we finished last time
                        for (int i = firstNonQueuedPolygon; i < queuedPolygonSet.size(); ++i)
                        {
                            if (!queuedPolygonSet[i])
                            {
                                queuedPolygons.push_back(i);
                                queuedPolygonSet[i] = 1;
                                firstNonQueuedPolygon = i + 1;
                                break;
                            }
                        }
                    }
                }

                for (int i = 0; i < dstMeshes.size(); ++i)
                {
                    ValidateMesh(dstMeshes[i], true);
                }

                return kMeshLimitDidSplit;
            }
        }
    };

   


    void FBXNewImporter::SplitMesh(const FBXImportMesh& src, FBXImportMesh& dst, float splitAngle) { SplitMeshImplementation split(src, dst, splitAngle); }

    void FBXNewImporter::RemapVertex(FBXImportMesh& in_mesh, std::vector<FBXImportMesh>& meshes)
    {
        RemoveDegenerateFaces(in_mesh);
        FBXImportMesh splitMesh, splitMeshChart;
        SplitMesh(in_mesh, splitMesh);
        MeshLimiter mesh_limiter;
        MeshLimiter::MeshLimitResult result = mesh_limiter.LimitMeshSize(splitMesh, meshes);
        AssertIf(result == MeshLimiter::MeshLimitResult::kMeshLimitCantSplit);
        if (result == MeshLimiter::MeshLimitResult::kMeshLimitNone)
            meshes.push_back(splitMesh);
    }

#if CROSSENGINE_WIN
#    include <GL/glu.h>
#    define TESS_FUNCTION_CALLCONV CALLBACK
#elif CROSSENGINE_LINUX
#    include <GL/glu.h>
#    define TESS_FUNCTION_CALLCONV
#else
#    include <OpenGL/glu.h>
#    define TESS_FUNCTION_CALLCONV
#endif

    struct TriangulationData
    {
        const FBXImportMesh* input;
        FBXImportMesh* output;
    };

    // This forces output to be triangles
    static void TESS_FUNCTION_CALLCONV DummyEmitEdgeFlag([[maybe_unused]] GLboolean flag) {}

    union no_warning_int_x_voidp
    {
        no_warning_int_x_voidp(int v)
        {
            pointer = 0;
            value = v;
        }
        no_warning_int_x_voidp(void* p)
            : pointer(p)
        {}
        int value;
        void* pointer;
    };

#define INT_2_POINTER(num)     no_warning_int_x_voidp(num).pointer
#define POINTER_2_INT(pointer) no_warning_int_x_voidp(pointer).value

    // Append one vertex to the output mesh
    static void TESS_FUNCTION_CALLCONV EmitVertex(void* vertexData, void* userData)
    {
        int index = POINTER_2_INT(vertexData);
        TriangulationData* meshes = static_cast<TriangulationData*>(userData);
        const FBXImportMesh& input = *meshes->input;
        FBXImportMesh& output = *meshes->output;

        output.polygons.push_back(input.polygons[index]);

        // Add wedge data
        if (!input.normals.empty())
            output.normals.push_back(input.normals[index]);
        if (!input.tangents.empty())
            output.tangents.push_back(input.tangents[index]);
        if (!input.uvs[0].empty())
            output.uvs[0].push_back(input.uvs[0][index]);
        if (!input.uvs[1].empty())
            output.uvs[1].push_back(input.uvs[1][index]);
        if (!input.colors.empty())
            output.colors.push_back(input.colors[index]);

        for (size_t i = 0; i < input.shapes.size(); ++i)
        {
            const FBXImportBlendShape& inputShape = input.shapes[i];
            FBXImportBlendShape& outputShape = output.shapes[i];

            if (!inputShape.normals.empty())
            {
                outputShape.normals.push_back(inputShape.normals[index]);
            }
            if (!inputShape.tangents.empty())
            {
                outputShape.tangents.push_back(inputShape.tangents[index]);
            }
        }
    }

    bool FBXNewImporter::Triangulate(const FBXImportMesh& input, FBXImportMesh& output, bool allowQuads)
    {
        // Totally mess!! commented by yazhenyuan
        // do a quick jump when all output is triangulated
        if (allowQuads == false)
        {
            bool allTri = std::all_of(input.polygonSizes.begin(), input.polygonSizes.end(), [](UInt32 size) { return size == 3; });
            if (allTri)
            {
                output = input;
                output.hasAnyQuads = false;
                return true;
            }
        }



        // We have a triangulated mesh.
        if (input.polygonSizes.empty())
        {
            output = input;
            output.polygonSizes.resize(input.polygons.size() / 3, 3);   // set all polygon sizes to 3
            output.hasAnyQuads = false;
            return output.polygons.size() % 3 == 0;
        }

        // calculate and reserve as much as it actually will actually need
        int faceCount = 0;
        int vertexCount = 0;
        for (int i = 0, size = static_cast<int>(input.polygonSizes.size()); i < size; ++i)
        {
            // we will skip all polygons which have less than 3 vertices in the next loop
            int faceSize = input.polygonSizes[i];
            if (allowQuads && faceSize == 4)
            {
                ++faceCount;
                vertexCount += 4;
            }
            else if (faceSize >= 3)
            {
                faceCount += faceSize - 2;
                vertexCount += (faceSize - 2) * 3;
            }
        }
        Assert(faceCount >= 0);

        output.polygons.reserve(vertexCount);
        if (!input.normals.empty())
            output.normals.reserve(vertexCount);
        if (!input.tangents.empty())
            output.tangents.reserve(vertexCount);
        if (!input.uvs[0].empty())
            output.uvs[0].reserve(vertexCount);
        if (!input.uvs[1].empty())
            output.uvs[1].reserve(vertexCount);
        if (!input.colors.empty())
            output.colors.reserve(vertexCount);
        if (!input.materials.empty())
            output.materials.reserve(faceCount);
        output.polygonSizes.reserve(faceCount);
        output.hasAnyQuads = false;

        output.shapes.resize(input.shapes.size());
        output.shapeChannels = input.shapeChannels;
        for (size_t i = 0; i < input.shapes.size(); ++i)
        {
            const FBXImportBlendShape& inputShape = input.shapes[i];
            FBXImportBlendShape& outputShape = output.shapes[i];

            outputShape.vertices = inputShape.vertices;
            outputShape.targetWeight = inputShape.targetWeight;

            if (!inputShape.normals.empty())
            {
                outputShape.normals.reserve(vertexCount);
            }
            if (!inputShape.tangents.empty())
            {
                outputShape.tangents.reserve(vertexCount);
            }
        }

        TriangulationData data;
        data.input = &input;
        data.output = &output;

        // Generate tesselator
        GLUtesselator* tesselator = gluNewTess();

#if CROSSENGINE_WIN
        gluTessCallback(tesselator, GLU_TESS_EDGE_FLAG, reinterpret_cast<GLvoid(CALLBACK*)()>(DummyEmitEdgeFlag));
        gluTessCallback(tesselator, GLU_TESS_VERTEX_DATA, reinterpret_cast<GLvoid(CALLBACK*)()>(EmitVertex));
#else
        gluTessCallback(tesselator, GLU_TESS_EDGE_FLAG, reinterpret_cast<GLvoid (*)()>(DummyEmitEdgeFlag));
        gluTessCallback(tesselator, GLU_TESS_VERTEX_DATA, reinterpret_cast<GLvoid (*)()>(EmitVertex));
#endif

        int index = 0;
        int faceIndex = 0;
        for (int p = 0; p < input.polygonSizes.size(); p++)
        {
            int polygonSize = input.polygonSizes[p];
            if (index + polygonSize > input.polygons.size())
                return false;

            int nextFaceIndex = faceIndex;

            if (polygonSize == 3)
            {
                EmitVertex(INT_2_POINTER(index), &data);
                index++;
                EmitVertex(INT_2_POINTER(index), &data);
                index++;
                EmitVertex(INT_2_POINTER(index), &data);
                index++;
                nextFaceIndex += 1;
                output.polygonSizes.push_back(3);
            }
            else if (allowQuads && polygonSize == 4)
            {
                EmitVertex(INT_2_POINTER(index), &data);
                index++;
                EmitVertex(INT_2_POINTER(index), &data);
                index++;
                EmitVertex(INT_2_POINTER(index), &data);
                index++;
                EmitVertex(INT_2_POINTER(index), &data);
                index++;
                nextFaceIndex += 1;
                output.polygonSizes.push_back(4);
                output.hasAnyQuads = true;
            }
            else if (polygonSize == 4)
            {
                int curIndex = index;
                /*
                curIndex = index + 0;
                EmitVertex ((void*)curIndex, &data);
                curIndex = index + 1;
                EmitVertex ((void*)curIndex, &data);
                curIndex = index + 3;
                EmitVertex ((void*)curIndex, &data);

                curIndex = index + 2;
                EmitVertex ((void*)curIndex, &data);
                curIndex = index + 3;
                EmitVertex ((void*)curIndex, &data);
                curIndex = index + 1;
                EmitVertex ((void*)curIndex, &data);
                */

                curIndex = index + 0;
                EmitVertex(INT_2_POINTER(curIndex), &data);
                curIndex = index + 1;
                EmitVertex(INT_2_POINTER(curIndex), &data);
                curIndex = index + 2;
                EmitVertex(INT_2_POINTER(curIndex), &data);

                curIndex = index + 0;
                EmitVertex(INT_2_POINTER(curIndex), &data);
                curIndex = index + 2;
                EmitVertex(INT_2_POINTER(curIndex), &data);
                curIndex = index + 3;
                EmitVertex(INT_2_POINTER(curIndex), &data);

                index += 4;
                nextFaceIndex += 2;
                output.polygonSizes.push_back(3);
                output.polygonSizes.push_back(3);
            }
            else
            {
                size_t curPolySize = output.polygons.size();
                gluTessBeginPolygon(tesselator, &data);
                gluTessBeginContour(tesselator);

                for (int v = 0; v < polygonSize; v++)
                {
                    int vertexIndex = input.polygons[index];
                    AssetMath::Vector3f vertex = input.vertices[vertexIndex];
                    double gluVertex[3] = {vertex.x(), vertex.y(), vertex.z()};
                    gluTessVertex(tesselator, gluVertex, INT_2_POINTER(index));
                    index++;
                }

                gluTessEndContour(tesselator);
                gluTessEndPolygon(tesselator);

                int newTris = static_cast<int>(output.polygons.size() - curPolySize) / 3;
                for (int i = 0; i < newTris; ++i)
                {
                    output.polygonSizes.push_back(3);
                }
                nextFaceIndex += newTris;
            }

            // Fill up material
            if (!input.materials.empty())
            {
                for (int i = faceIndex; i < nextFaceIndex; ++i)
                {
                    output.materials.push_back(input.materials[p]);
                }
            }

            faceIndex = nextFaceIndex;
        }

        gluDeleteTess(tesselator);

        output.vertices = input.vertices;
        output.skin = input.skin;
        output.bones = input.bones;
        output.name = input.name;

        return true;
    }

    enum DegenFace
    {
        kDegenNone,
        kDegenFull,
        kDegenToTri
    };

    static inline DegenFace IsDegenerateFace(const UInt32* f, const int faceSize)
    {
        if (faceSize == 3)
        {
            // triangle
            return (f[0] == f[1] || f[0] == f[2] || f[1] == f[2]) ? kDegenFull : kDegenNone;
        }
        else if (faceSize == 4)
        {
            // quad

            // non-neighbor indices are the same: quad is fully degenerate (produces no output)
            if (f[0] == f[2] || f[1] == f[3])
                return kDegenFull;
            // two opposing sides are collapsed: fully degenerate
            if ((f[0] == f[1] && f[2] == f[3]) || (f[1] == f[2] && f[3] == f[0]))
                return kDegenFull;
            // just one side is collapsed: degenerate to triangle
            if (f[0] == f[1] || f[1] == f[2] || f[2] == f[3] || f[3] == f[0])
                return kDegenToTri;
            // otherwise, regular quad
            return kDegenNone;
        }
        else
        {
            AssertString("unsupported face size");
            return kDegenFull;
        }
    }

    template<typename T> static void AddFace(const typename std::vector<T>& src, typename std::vector<T>& dst, int idx, const UInt32* indices, int faceSize)
    {
        if (src.empty())
            return;
        for (int i = 0; i < faceSize; ++i)
        {
            dst.push_back(src[idx + indices[i]]);
        }
    }

    void FBXNewImporter::RemoveDegenerateFaces(FBXImportMesh& mesh)
    {
        // Check if we have any degenerate faces
        const int faceCount = static_cast<int>(mesh.polygonSizes.size());
        int i, idx;
        for (i = 0, idx = 0; i < faceCount; ++i)
        {
            const int fs = mesh.polygonSizes[i];
            if (IsDegenerateFace(&mesh.polygons[idx], fs) != kDegenNone)
                break;
            idx += fs;
        }
        // No degenerate faces, return
        if (i == faceCount)
            return;

        FBXImportMesh temp;
        const int indexCount = static_cast<int>(mesh.polygons.size());
        temp.polygons.reserve(indexCount);
        temp.materials.reserve(faceCount);
        temp.polygonSizes.reserve(faceCount);

        temp.tangents.reserve(indexCount);
        temp.normals.reserve(indexCount);
        temp.colors.reserve(indexCount);
        temp.uvs[0].reserve(indexCount);
        temp.uvs[1].reserve(indexCount);

        temp.shapes.resize(mesh.shapes.size());
        for (size_t j = 0; j < mesh.shapes.size(); ++j)
        {
            FBXImportBlendShape& tempShape = temp.shapes[j];
            tempShape.normals.reserve(indexCount);
            tempShape.tangents.reserve(indexCount);
        }

        mesh.hasAnyQuads = false;

        // Build result and remove degenerates on the way.
        for (i = 0, idx = 0; i < faceCount; ++i)
        {
            const int fs = mesh.polygonSizes[i];
            const UInt32* face = &mesh.polygons[idx];
            DegenFace degen = IsDegenerateFace(face, fs);

            int addFaceSize = fs;
            UInt32 addIndices[4] = {0, 1, 2, 3};
            if (degen == kDegenToTri)
            {
                DEBUG_ASSERT(fs == 4);
                if (face[0] == face[1])
                    addIndices[0] = 1, addIndices[1] = 2, addIndices[2] = 3;
                else if (face[1] == face[2])
                    addIndices[0] = 0, addIndices[1] = 2, addIndices[2] = 3;
                else if (face[2] == face[3])
                    addIndices[0] = 0, addIndices[1] = 1, addIndices[2] = 3;
                else if (face[3] == face[0])
                    addIndices[0] = 0, addIndices[1] = 1, addIndices[2] = 2;
                else
                {
                    DEBUG_ASSERT(false);
                }

                degen = kDegenNone;
                addFaceSize = 3;
            }

            if (degen == kDegenNone)
            {
                if (!mesh.materials.empty())
                    temp.materials.push_back(mesh.materials[i]);
                temp.polygonSizes.push_back(addFaceSize);

                AddFace(mesh.polygons, temp.polygons, idx, addIndices, addFaceSize);
                AddFace(mesh.normals, temp.normals, idx, addIndices, addFaceSize);
                AddFace(mesh.tangents, temp.tangents, idx, addIndices, addFaceSize);
                AddFace(mesh.colors, temp.colors, idx, addIndices, addFaceSize);
                AddFace(mesh.uvs[0], temp.uvs[0], idx, addIndices, addFaceSize);
                AddFace(mesh.uvs[1], temp.uvs[1], idx, addIndices, addFaceSize);
                if (addFaceSize == 4)
                    mesh.hasAnyQuads = true;

                for (size_t s = 0; s < mesh.shapes.size(); ++s)
                {
                    FBXImportBlendShape& meshShape = mesh.shapes[s];
                    FBXImportBlendShape& tempShape = temp.shapes[s];
                    AddFace(meshShape.normals, tempShape.normals, idx, addIndices, addFaceSize);
                    AddFace(meshShape.tangents, tempShape.tangents, idx, addIndices, addFaceSize);
                }
            }
            idx += fs;
        }

        mesh.polygons.swap(temp.polygons);
        mesh.materials.swap(temp.materials);
        mesh.polygonSizes.swap(temp.polygonSizes);
        mesh.normals.swap(temp.normals);
        mesh.tangents.swap(temp.tangents);
        mesh.colors.swap(temp.colors);
        mesh.uvs[0].swap(temp.uvs[0]);
        mesh.uvs[1].swap(temp.uvs[1]);

        for (size_t j = 0; i < mesh.shapes.size(); ++j)
        {
            FBXImportBlendShape& meshShape = mesh.shapes[j];
            FBXImportBlendShape& tempShape = temp.shapes[j];
            meshShape.normals.swap(tempShape.normals);
            meshShape.tangents.swap(tempShape.tangents);
        }
    }


    struct TSpaceMeshInfo
    {
        UInt32* FaceOffs;
        FBXImportMesh* ImpMesh;

        const AssetMath::Vector3f* vertices;
        const AssetMath::Vector3f* normals;
        AssetMath::Vector4f* outTangents;
    };


    static int getNumFaces(const SMikkTSpaceContext* Context)
    {
        TSpaceMeshInfo* MeshInf = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);
        const FBXImportMesh* ImpMesh = MeshInf->ImpMesh;
        return static_cast<int>(ImpMesh->polygonSizes.size());
    }

    static int getNumVerticesOfFace(const SMikkTSpaceContext* Context, const int FaceIdx)
    {
        TSpaceMeshInfo* MeshInf = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);
        const FBXImportMesh* ImpMesh = MeshInf->ImpMesh;
        return ImpMesh->polygonSizes[FaceIdx];
    }

    // 0, 1, 2 for tri and 3 for quad
    static void getPosition(const SMikkTSpaceContext* Context, float PosOut[], const int FaceIdx, const int VertIdx)
    {
        TSpaceMeshInfo* MeshInf = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);
        const FBXImportMesh* ImpMesh = MeshInf->ImpMesh;
        const UInt32* FaceOffs = MeshInf->FaceOffs;
        // const int iNrVertsOnFace = pImpMesh->polygonSizes[iFace];     // always 3 or 4
        const UInt32* face = &ImpMesh->polygons[FaceOffs[FaceIdx]];
        const UInt32 idx = face[VertIdx];
        AssetMath::Vector3f vP = MeshInf->vertices[idx];   // shape position array
        PosOut[0] = vP.x();
        PosOut[1] = vP.y();
        PosOut[2] = vP.z();
    }

    // 0, 1, 2 for tri and 3 for quad
    static void getNormal(const SMikkTSpaceContext* Context, float NormOut[], const int FaceIdx, const int VertIdx)
    {
        TSpaceMeshInfo* MeshInf = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);
        const UInt32* FaceOffs = MeshInf->FaceOffs;

        AssetMath::Vector3f vN = MeshInf->normals[FaceOffs[FaceIdx] + VertIdx];   // shape normals array
        NormOut[0] = vN.x();
        NormOut[1] = vN.y();
        NormOut[2] = vN.z();
    }

    // 0, 1, 2 for tri and 3 for quad
    static void getTexCoord(const SMikkTSpaceContext* Context, float TexcOut[], const int FaceIdx, const int VertIdx)
    {
        TSpaceMeshInfo* MeshInf = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);
        const FBXImportMesh* ImpMesh = MeshInf->ImpMesh;

        float U = 0.0f, V = 0.0f;
        if (!ImpMesh->uvs[0].empty())
        {
            const UInt32* FaceOffs = MeshInf->FaceOffs;

            const AssetMath::Vector2f* tex = &ImpMesh->uvs[0][0];
            const AssetMath::Vector2f TC = tex[FaceOffs[FaceIdx] + VertIdx];
            U = TC.x();
            V = TC.y();
        }

        TexcOut[0] = U;
        TexcOut[1] = V;
    }

    static void setTSpace(const SMikkTSpaceContext* Context, const float Tangent[], const float BiTangent[], const float fMagS, const float fMagT, const tbool IsOrientationPreserving, const int FaceIdx, const int VertIdx)
    {
        TSpaceMeshInfo* MeshInf = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);
        const UInt32* FaceOffs = MeshInf->FaceOffs;
        AssetMath::Vector4f* outTangents = MeshInf->outTangents;   // shape output tangents

        // fSign * cross(vN, vT) <-- In that order!
        const float fSign = (IsOrientationPreserving != 0) ? 1.0f : -1.0f;
        outTangents[FaceOffs[FaceIdx] + VertIdx] = AssetMath::Vector4f{Tangent[0], Tangent[1], Tangent[2], fSign};
    }

    void FBXNewImporter::GenerateMikkTSpace(FBXImportMesh& mesh)
    {
        const UInt32 uFC = (UInt32)mesh.polygonSizes.size();
        std::vector<UInt32> faceOffsets(uFC);
        for (UInt32 i = 0, idx = 0; i < uFC; i++)
        {
            faceOffsets[i] = idx;
            idx += mesh.polygonSizes[i];
        }

        mesh.tangents.resize(mesh.polygons.size());

        TSpaceMeshInfo mesh_info;
        mesh_info.ImpMesh = &mesh;
        mesh_info.FaceOffs = &faceOffsets[0];

        // base mesh shape
        mesh_info.vertices = &mesh.vertices[0];
        mesh_info.normals = &mesh.normals[0];
        mesh_info.outTangents = &mesh.tangents[0];

        // setup the interface for mikktspace generator
        SMikkTSpaceInterface Interface;
        memset(reinterpret_cast<void*>(&Interface), 0, sizeof(SMikkTSpaceInterface));

        Interface.m_getNumFaces = getNumFaces;
        Interface.m_getNumVerticesOfFace = getNumVerticesOfFace;
        Interface.m_getPosition = getPosition;
        Interface.m_getNormal = getNormal;
        Interface.m_getTexCoord = getTexCoord;
        Interface.m_setTSpace = setTSpace;

        SMikkTSpaceContext Context;
        memset(reinterpret_cast<void*>(&Context), 0, sizeof(SMikkTSpaceContext));
        Context.m_pUserData = reinterpret_cast<void*>(&mesh_info);
        Context.m_pInterface = &Interface;

        // Return result to input Mesh format
        bool bRes = genTangSpaceDefault(&Context) != 0;

        // Generate tangents & binormals for blendShapes
        for (int i = 0; i < mesh.shapes.size(); ++i)
        {
            FBXImportBlendShape& shape = mesh.shapes[i];
            if (!shape.vertices.empty() && !shape.normals.empty())
            {
                std::vector<AssetMath::Vector4f> tangents(mesh.polygons.size());

                // target mesh shape
                mesh_info.vertices = &shape.vertices[0];
                mesh_info.normals = &shape.normals[0];
                mesh_info.outTangents = &tangents[0];

                bRes &= genTangSpaceDefault(&Context) != 0;

                shape.tangents.resize(tangents.size());
                for (int j = 0; j < tangents.size(); ++j)
                {
                    const AssetMath::Vector4f t = tangents[j];
                    shape.tangents[j] = {t.x(), t.y(), t.z()};
                }
            }
        }
    }
}}   // namespace cross::editor
#endif
