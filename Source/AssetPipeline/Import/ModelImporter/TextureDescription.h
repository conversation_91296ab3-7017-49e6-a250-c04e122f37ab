#pragma once

#include <fbxsdk.h>
#include <vector>
#include <unordered_map>
#include "CrossBase/Math/CrossMath.h"

namespace cross::editor {

    using TextureID = SInt32;

    class TextureDescription
    {
    public:
        inline void AddTexture(TextureID id, FbxTexture* tex) { mTextureMap.try_emplace(id, tex); }; 

        inline FbxTexture* FindTexture(TextureID id)
        {
            auto resultIter = mTextureMap.find(id);
            return resultIter != mTextureMap.end() ? resultIter->second : nullptr;
        };

    private:
        std::unordered_map<TextureID, FbxTexture*> mTextureMap;
    };
}