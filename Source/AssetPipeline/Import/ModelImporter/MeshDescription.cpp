#include "MeshDescription.h"
#include "External/mikktspace/mikktspace.h"

namespace cross::editor 
{
    struct TSpaceMeshInfo
    {
        MeshDescription* MeshDesc{nullptr};

        bool ForBlendShape{false};
        ShapeDesc* TargetShape{nullptr};
    };

    static int GetNumFaces(const SMikkTSpaceContext* Context)
    {
        TSpaceMeshInfo* meshInfo = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);
        return meshInfo->MeshDesc->GetTriangleNum();
    }

    static int GetNumVerticesOfFace(const SMikkTSpaceContext* Context, const int FaceIdx)
    {
        // TODO(xujh) Only support triangle ?
        return 3;
    }

    // 0, 1, 2 for tri and 3 for quad
    static void GetPosition(const SMikkTSpaceContext* Context, float PosOut[], const int FaceIdx, const int VertIdx)
    {
        TSpaceMeshInfo* meshInfo = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);

        const Float3* vertexPos = nullptr;
        if (!meshInfo->ForBlendShape)
        {
            const MeshBuildVertex& vertex = meshInfo->MeshDesc->GetMeshBuildVertex(FaceIdx, VertIdx);
            vertexPos = &(vertex.Position);
        }
        else
        {
            const auto vertexId = meshInfo->MeshDesc->GetVertexInstanceVertex(FaceIdx, VertIdx);
            vertexPos = &(meshInfo->TargetShape->Positions[vertexId]);
        }

        PosOut[0] = vertexPos->x;
        PosOut[1] = vertexPos->y;
        PosOut[2] = vertexPos->z;
    }

    // 0, 1, 2 for tri and 3 for quad
    static void GetNormal(const SMikkTSpaceContext* Context, float NormOut[], const int FaceIdx, const int VertIdx)
    {
        TSpaceMeshInfo* meshInfo = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);

        const Float3* vertexNormal = nullptr;
        if (!meshInfo->ForBlendShape)
        {
            const MeshBuildVertex& vertex = meshInfo->MeshDesc->GetMeshBuildVertex(FaceIdx, VertIdx);
            vertexNormal = &(vertex.Normal);
        }
        else
        {
            const auto vertexId = meshInfo->MeshDesc->GetVertexInstanceVertex(FaceIdx, VertIdx);
            vertexNormal = &(meshInfo->TargetShape->Normals[vertexId]);
        }

        NormOut[0] = vertexNormal->x;
        NormOut[1] = vertexNormal->y;
        NormOut[2] = vertexNormal->z;
    }

    // 0, 1, 2 for tri and 3 for quad
    static void GetTexCoord(const SMikkTSpaceContext* Context, float TexcOut[], const int FaceIdx, const int VertIdx)
    {
        TSpaceMeshInfo* meshInfo = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);
        const auto& vertex = meshInfo->MeshDesc->GetMeshBuildVertex(FaceIdx, VertIdx);
        float outU = 0.0f;
        float outV = 0.0f;
        if (vertex.Channels & ImportVertexChannel::UV0)
        {
            outU = vertex.UVs[0].x;
            outV = vertex.UVs[0].y;
        }
        TexcOut[0] = outU;
        TexcOut[1] = outV;
    }

    static void SetTSpace(const SMikkTSpaceContext* Context, const float Tangent[], const float BiTangent[], const float fMagS, const float fMagT, const tbool IsOrientationPreserving, const int FaceIdx, const int VertIdx)
    {
        const float fSign = (IsOrientationPreserving != 0) ? 1.0f : -1.0f;

        TSpaceMeshInfo* meshInfo = reinterpret_cast<TSpaceMeshInfo*>(Context->m_pUserData);

        if (!meshInfo->ForBlendShape)
        {
            MeshBuildVertex& vertex = meshInfo->MeshDesc->GetMeshBuildVertex(FaceIdx, VertIdx);
            vertex.Tangent = Float4(Tangent[0], Tangent[1], Tangent[2], fSign);
            vertex.Channels |= ImportVertexChannel::TANGENT;
        }
        else
        {
            const auto vertexId = meshInfo->MeshDesc->GetVertexInstanceVertex(FaceIdx, VertIdx);
            meshInfo->TargetShape->Tangents[vertexId] = Float4(Tangent[0], Tangent[1], Tangent[2], fSign);
        }
    }

    static void GenerateMikkTSpaceTangent(TSpaceMeshInfo& meshInfo)
    {
        // Setup the interface for mikktspace generator
        SMikkTSpaceInterface Interface;
        memset(reinterpret_cast<void*>(&Interface), 0, sizeof(SMikkTSpaceInterface));

        Interface.m_getNumFaces = GetNumFaces;
        Interface.m_getNumVerticesOfFace = GetNumVerticesOfFace;
        Interface.m_getPosition = GetPosition;
        Interface.m_getNormal = GetNormal;
        Interface.m_getTexCoord = GetTexCoord;
        Interface.m_setTSpace = SetTSpace;

        SMikkTSpaceContext Context;
        memset(reinterpret_cast<void*>(&Context), 0, sizeof(SMikkTSpaceContext));
        Context.m_pUserData = reinterpret_cast<void*>(&meshInfo);
        Context.m_pInterface = &Interface;

        genTangSpaceDefault(&Context);
    }

    void MeshDescription::Init(UInt32 polygonNums)
    {
        ReserveTriangleInstances(polygonNums);
        ReserveNewVertexInstances(polygonNums * 3);
    }

    void MeshDescription::GenerateMikkTSpace()
    {
        TSpaceMeshInfo meshInfo;
        meshInfo.MeshDesc = this;
        meshInfo.ForBlendShape = false;
        meshInfo.TargetShape = nullptr;

        GenerateMikkTSpaceTangent(meshInfo);
    }

    void MeshDescription::GenerateMikkTSpaceForTargetShape(ShapeDesc& targetShape)
    {
        TSpaceMeshInfo meshInfo;
        meshInfo.MeshDesc = this;
        meshInfo.ForBlendShape = true;
        meshInfo.TargetShape = &targetShape;

        GenerateMikkTSpaceTangent(meshInfo);
    }
}   // namespace cross::editor