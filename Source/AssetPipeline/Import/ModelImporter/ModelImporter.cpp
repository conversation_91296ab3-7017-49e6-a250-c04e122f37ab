#include "ModelImporter.h"
#include "CrossBase/uuid/CrossUUID.h"
#include <fstream>

#include "Runtime/Interface/CrossEngineImp.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "Resource/Animation/Skeleton/SkeletonResource.h"
#include "Resource/Animation/Sequence/AnimSequenceRes.h"

namespace cross::editor {
const int CollisionTriangleCountThreshold = 128;

ModelImporter::ModelImporter()
    : AssetImporter(AssetType::Model)
{
    RegisterImporters();
}

bool ModelImporter::ImportAssetImpl(ImportScene const& scene, std::string const& name, const std::string& ndaSavePath)
{
    auto type = DetectImportType(scene);
    auto itr = mImporters.find(type);
    if (itr == mImporters.end())
        return false;
    PersistenceFunctionContainer savers;
    if (!itr->second(scene, savers))
    {
        return false;
    }

    auto ndaFilePath = Resource::GetNDAFilePath(name, ndaSavePath);
    auto result = true;
    LOG_EDITOR_WARNING("[ModelImporter]At 'result &= saver(ndaFilePath)', garbled characters directory will be created if ndaFilePath contains Chinese.");
    for (auto const& saver : savers)
    {
        result &= saver(ndaFilePath);
    }
    return result;
}

bool ModelImporter::ImportStaticMeshData(ImportScene const& scene, PersistenceFunctionContainer& saver)
{
    return false;
}

bool ModelImporter::ImportSkeletonMeshData(ImportScene const& scene, PersistenceFunctionContainer& saver)
{
    return false;
}

void ModelImporter::RegisterImporters()
{
    mImporters.emplace(ModelType::StaticMesh, [this](auto&... args) 
    { 
        return ImportStaticMeshData(args...); 
    });
    mImporters.emplace(ModelType::SkeletalMesh, [this](auto&... args) 
    { 
        return ImportSkeletonMeshData(args...); 
    });
}

ModelType ModelImporter::DetectImportType(ImportScene const& scene)
{
    return ModelType::Unknown;
}

std::string ModelImporter::InsertPostfix(std::string const& path, std::string const& postfix)
{
    auto newPath = path;
    if (!postfix.empty())
    {
        auto pos = path.rfind(".nda");
        if (pos == std::string::npos)
#if CROSSENGINE_OSX
            return "";
#else
            return {};
#endif
        newPath.insert(pos, postfix);
    }
    return newPath;
}

bool ModelImporter::DropStaticMeshes(CrossSchema::ImportMeshesT const* meshes, std::string const& path)
{
    Assert(false);
    return true;
}

bool ModelImporter::DropSkeletalMesh(CrossSchema::ImportMeshesT const* meshes, SkeletonDesc const& skeleton, std::string const& path, std::string const& postfix)
{
    return false;
}

bool ModelImporter::SerializeAnimations(const std::vector<AnimationDesc>& animations, const std::string& assetFilePath, const std::string& ndaSavePath, const SkeletonDesc& skeleton) const
{
    if (skeleton.Bones.empty())
        return false;

    for (auto& curAnim : animations)
    {
        if (mImportSettings->AnimImport == UniformSample || mImportSettings->AnimImport == All) 
        {
            CompressAndSerializeAnimation(curAnim, assetFilePath, ndaSavePath, skeleton, AnimCompressionType::UniformSample);
        }

        if (mImportSettings->AnimImport == LinearKeyReduction || mImportSettings->AnimImport == All)
        {
            CompressAndSerializeAnimation(curAnim, assetFilePath, ndaSavePath, skeleton, AnimCompressionType::LinearKeyReducion);
        }
    }

    return true;
}

void ModelImporter::CompressAndSerializeAnimation(const AnimationDesc& animation, const std::string& assetFilePath, const std::string& ndaSavePath, const SkeletonDesc& skeleton, AnimCompressionType cprType) const
{
    // convert import animation into flat buffer style struct
    CrossSchema::ImportAnimationT csAnim;

    // convert reference skeleton
    std::unique_ptr<CrossSchema::ImportRefSkeletonT> csRefSkelt(new CrossSchema::ImportRefSkeletonT);
    SerializeReferenceSkeleton(csRefSkelt.get(), skeleton);
    csAnim.ref_skelt = std::move(csRefSkelt);

    // anim info
    csAnim.name = animation.Name;
    csAnim.ref_path = assetFilePath;
    csAnim.duration_sec = animation.Duration;
    csAnim.frame_num = animation.GetFrameCount();
    csAnim.skelt_num = static_cast<int>(animation.TracksForAllBones.size());

    // anim save path
    std::string animSavePath = PathHelper::GetDirectoryFromAbsolutePath(ndaSavePath);
    std::string animBaseName = PathHelper::GetBaseFileName(assetFilePath);
    // append animName to animBaseName when animName != FBXFileName
    if (animation.Name != animBaseName && animation.Name != "Unreal Take")
    {
        animBaseName += ("_" + animation.Name);
    }

    if (cprType == AnimCompressionType::UniformSample)
    {
        static acl::ansi_allocator allocator;

        animBaseName.append("_Acl_ANIM.nda");

        csAnim.cpr_type = CrossSchema::ImportAnimCompressType::UniformSample;

        // compress animation using acl
        acl::compressed_tracks* pAclCompressedAnim = CompressAnimation_Acl(allocator, animation, skeleton);
        if (pAclCompressedAnim == nullptr)
        {
            LOG_EDITOR_WARNING("Compressing Animation name:{} Animation is empty ", animation.Name);
        }

        auto cprBufferStartAddress = reinterpret_cast<const char*>(pAclCompressedAnim);
        if (pAclCompressedAnim->is_valid(false).empty())
        {
            Assert(pAclCompressedAnim->get_size() > 0);

            // persist acl byte stream
            csAnim.cpr_ani_buffer.resize(pAclCompressedAnim->get_size());
            memcpy(csAnim.cpr_ani_buffer.data(), cprBufferStartAddress, sizeof(char) * pAclCompressedAnim->get_size());
        }

        // check compression error
        static float cprErrorThreshold = 0.2f;
        float cprError = CompressAnimation_CheckAclError(allocator, pAclCompressedAnim, animation);
        if (cprError < cprErrorThreshold)
        {
            LOG_EDITOR_WARNING("Compressing Animation name:{} got huge deviation which value is {}", animation.Name, cprError);
        }

        allocator.deallocate(pAclCompressedAnim, pAclCompressedAnim->get_size());
    }

    if (cprType == AnimCompressionType::LinearKeyReducion)
    {
        animBaseName.append("_ANIM.nda");

        csAnim.cpr_type = CrossSchema::ImportAnimCompressType::LinearReduction;

        // compress animation using lkr
        CompressAnimation_Lkr(const_cast<AnimationDesc&>(animation));

        const auto& animTracks = animation.TracksForAllBones;
        Assert(animTracks.size() == skeleton.Bones.size());

        // convert import scene anim's track into flat buffer style struct
        std::vector<std::unique_ptr<CrossSchema::ImportAnimTrackPerBoneT>> csAnimTracks;
        for (int boneIndex = 0; boneIndex < animTracks.size(); ++boneIndex)
        {
            std::unique_ptr<CrossSchema::ImportAnimTrackPerBoneT> csAnimTrack(new CrossSchema::ImportAnimTrackPerBoneT);
            AnimationDesc::Track const& curAnimTrack = animTracks[boneIndex];

            // grab translate key frames in particular bone
            for (int frameIndex = 0, frameCount = static_cast<int>(curAnimTrack.TranslationKeys.size()); frameIndex < frameCount; ++frameIndex)
            {
                auto& translate = curAnimTrack.TranslationKeys[frameIndex].Translation;
                auto& time = curAnimTrack.TranslationKeys[frameIndex].Time;

                csAnimTrack->pos.emplace_back(translate.x, translate.y, translate.z);
                csAnimTrack->pos_t.emplace_back(time);
            }

            // grab rotation key frames in particular bone
            for (int frameIndex = 0, frameCount = static_cast<int>(curAnimTrack.RotationKeys.size()); frameIndex < frameCount; ++frameIndex)
            {
                auto& rotation = curAnimTrack.RotationKeys[frameIndex].Rotation;
                auto& time = curAnimTrack.RotationKeys[frameIndex].Time;

                csAnimTrack->rot.emplace_back(rotation.x, rotation.y, rotation.z, rotation.w);
                csAnimTrack->rot_t.emplace_back(time);
            }

            // grab scale key frames in particular bone
            for (int frameIndex = 0, frameCount = static_cast<int>(curAnimTrack.ScaleKeys.size()); frameIndex < frameCount; ++frameIndex)
            {
                auto& scale = curAnimTrack.ScaleKeys[frameIndex].Scale;
                auto& time = curAnimTrack.ScaleKeys[frameIndex].Time;

                csAnimTrack->scl.emplace_back(scale.x, scale.y, scale.z);
                csAnimTrack->scl_t.emplace_back(time);
            }

            // if the last frame's time is smaller than importAnimDurInSec
            // construct a new frame with the same transform as the last frame and the same time as importAnimDurInSec
            const float gap_t = 0.000001f;
            if (animation.Duration - csAnimTrack->pos_t.back() > gap_t)
            {
                csAnimTrack->pos.emplace_back(csAnimTrack->pos.back());
                csAnimTrack->pos_t.emplace_back(animation.Duration);
            }
            if (animation.Duration - csAnimTrack->rot_t.back() > gap_t)
            {
                csAnimTrack->rot.emplace_back(csAnimTrack->rot.back());
                csAnimTrack->rot_t.emplace_back(animation.Duration);
            }
            if (animation.Duration - csAnimTrack->scl_t.back() > gap_t)
            {
                csAnimTrack->scl.emplace_back(csAnimTrack->scl.back());
                csAnimTrack->scl_t.emplace_back(animation.Duration);
            }

            csAnimTracks.emplace_back(std::move(csAnimTrack));
        }

        csAnim.tracks_ani_buffer = std::move(csAnimTracks);
    }

    csAnim.has_rootmotion = true;
    csAnim.root_lock_type = CrossSchema::ImportRootMotionLockType::AnimFirstFrame;

    // Serialize curve data
    if (animation.CurveList.GetCurveNum() > 0)
    {
        animation.CurveList.Serialize(csAnim.curve_set);
    }

    animSavePath += animBaseName;

#if 1
    DropAssetAsSingleFile(animSavePath, [&](filesystem::IFilePtr file) 
    {
        cross::FileArchive archive{file};

        auto classID = ClassID(AnimSequenceRes);
        CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);
        flatbuffers::FlatBufferBuilder builder(10240);

        auto mloc = CrossSchema::CreateImportAnimation(builder, &csAnim);
        auto name = PathHelper::GetBaseFileName(animSavePath);
        auto mloc2 = CrossSchema::CreateResourceAsset(builder, &header, builder.CreateString(name), CrossSchema::ResourceType::ImportAnimation, mloc.Union());
        FinishResourceAssetBuffer(builder, mloc2);

        ResourceMetaHeader resourceHeader;
        resourceHeader.mMagicNumber = ASSET_MAGIC_NUMBER_JMETA;
        resourceHeader.mVersion = Resource::gResourceJsonHeaderVersion;
        resourceHeader.mClassID = classID;
        resourceHeader.mContentType = static_cast<UInt32>(CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER);
        resourceHeader.mGuid = gResourceMgr.GetGuidByPath(PathHelper::GetRelativePath(PathHelper::GetAbsolutePath(animSavePath)));
        resourceHeader.mJsonStringLength = 0;
        resourceHeader.mDataSize = builder.GetSize();

        return Resource::Serialize(resourceHeader, builder.GetBufferPointer(), builder.GetSize(), file->GetPathName());
    });
#else

#if 0
    MeshAssetDataResourcePtr mesh = gResourceMgr.CreateResourceAs<cross::resource::MeshAssetDataResource>();
         Assert(mesh);
         mesh->CreateAsset(ndaFilePath);
         mesh->Serialize(meshAssetDataT);
#endif
    anim::AnimSeqResPtr animSeqRes = gResourceMgr.CreateResourceAs<anim::AnimSequenceRes>();
    Assert(animSeqRes);
    
    animSeqRes->CreateAsset(animSavePath);
    animSeqRes->Serialize(csAnim);
    
#endif
}

void ModelImporter::SerializeReferenceSkeleton(CrossSchema::ImportRefSkeletonT* outFbRefSkelt, const SkeletonDesc& skeleton) const
{
    outFbRefSkelt->name = skeleton.Name;
    for (int i = 0, size = static_cast<int>(skeleton.Bones.size()); i < size; ++i)
    {
        auto const& importBoneNode = skeleton.Bones[i];

        std::unique_ptr<CrossSchema::ImportBoneNodeT> csBoneNode(new CrossSchema::ImportBoneNodeT);
        csBoneNode->name = importBoneNode.BoneName;
        csBoneNode->boneid = importBoneNode.BoneID;
        csBoneNode->parentid = importBoneNode.ParentID;
        csBoneNode->retarget = CrossSchema::ImportBoneTransRetgtMode::Animation;
        csBoneNode->worldmatrix = std::vector<float>(importBoneNode.RefPoseWorld.data(), importBoneNode.RefPoseWorld.data() + 16);
        csBoneNode->bindposedef = std::vector<float>(importBoneNode.RefPoseBind.data(), importBoneNode.RefPoseBind.data() + 16);

        outFbRefSkelt->skelteon.push_back(std::move(csBoneNode));
    }
}

void ModelImporter::SerializeSkeleton(const SkeletonDesc& skeleton, const std::string& assetFileName, const std::string& ndaSavePath) const
{
    if (skeleton.Bones.empty())
        return;

    // convert import skeleton into flat buffer style struct
    CrossSchema::ImportRunSkeletonT csRunSkelt;

    // convert reference skeleton
    std::unique_ptr<CrossSchema::ImportRefSkeletonT> csRefSkelt(new CrossSchema::ImportRefSkeletonT);
    SerializeReferenceSkeleton(csRefSkelt.get(), skeleton);
    csRunSkelt.fref_skelt = std::move(csRefSkelt);

    // convert slot group
    std::unique_ptr<CrossSchema::ImportSlotGroupT> csSlotGroup(new CrossSchema::ImportSlotGroupT);
    csSlotGroup->name = "DefaultGroup";
    csSlotGroup->slot_names.emplace_back("DefaultSlot");
    csRunSkelt.fslots.emplace_back(std::move(csSlotGroup));

    //
    std::string skeletonFilePath = PathHelper::GetDirectoryFromAbsolutePath(ndaSavePath);
    skeletonFilePath.append(PathHelper::GetBaseFileName(assetFileName));
    skeletonFilePath.append("_SK.nda");

#if 1
    DropAssetAsSingleFile(skeletonFilePath, [&](filesystem::IFilePtr file) 
    {
        cross::FileArchive archive{file};

        auto classID = ClassID(SkeletonResource);
        CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);
        flatbuffers::FlatBufferBuilder builder(10240);

        auto mloc = CrossSchema::CreateImportRunSkeleton(builder, &csRunSkelt);
        auto name = PathHelper::GetBaseFileName(skeletonFilePath);
        auto mloc2 = CrossSchema::CreateResourceAsset(builder, &header, builder.CreateString(name), CrossSchema::ResourceType::ImportRunSkeleton, mloc.Union());
        FinishResourceAssetBuffer(builder, mloc2);

        ResourceMetaHeader resourceHeader;
        resourceHeader.mMagicNumber = ASSET_MAGIC_NUMBER_JMETA;
        resourceHeader.mVersion = Resource::gResourceJsonHeaderVersion;
        resourceHeader.mClassID = classID;
        resourceHeader.mGuid = gResourceMgr.GetGuidByPath(PathHelper::GetRelativePath(PathHelper::GetAbsolutePath(skeletonFilePath)));
        resourceHeader.mContentType = static_cast<UInt32>(CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER);
        resourceHeader.mJsonStringLength = 0;
        resourceHeader.mDataSize = builder.GetSize();

        return Resource::Serialize(resourceHeader, builder.GetBufferPointer(), builder.GetSize(), file->GetPathName());
    });
#else
    
    SkeletonResourcePtr skeletonResource = ResourceMgr.CreateResourceAs<anim::SkeletonResource>();
    Assert(skeletonResource);
    
    skeletonResource->CreateAsset(ndaFilePath, ndaFilePath);
    skeletonResource->Serialize(csRunSkelt);
    
#endif
}

void ModelImporter::BuildCollisionTree(CrossSchema::ImportMeshDataT& meshData)
{
    meshData.collisiontree.clear();
    meshData.collisiontree.reserve(1000);
    FillVertexList(meshData);
    FillIndexList(meshData);
    CalculateCentroidList();
    CrossSchema::CollisionNodeT* rootNode = CreateRootCollisionNode(meshData);
    SplitCollisionNode(rootNode, &meshData);
    rootNode = (meshData.collisiontree[0]).get();
    CalculateNodeBound(rootNode, &meshData);
}

void ModelImporter::FillVertexList(CrossSchema::ImportMeshDataT& meshData)
{
    mVertexList.clear();
    auto posattrib = meshData.vertexdata->pos->data;
    int verticescount = static_cast<int>(posattrib.size());
    mVertexList.resize(verticescount);
    for (int i = 0; i < verticescount; i++)
    {
        mVertexList[i] = AssetMath::Vector3f(posattrib[i].x(), posattrib[i].y(), posattrib[i].z());
    }
}

void ModelImporter::FillIndexList(CrossSchema::ImportMeshDataT& meshData)
{
    mIndexList.clear();
    int indicesCount = meshData.indices->indicescount;
    mIndexList.resize(indicesCount);
    if (indicesCount * sizeof(UInt32) == meshData.indices->indexbuffer.size())
    {
        UInt32* indices32 = reinterpret_cast<UInt32*>(meshData.indices->indexbuffer.data());
        for (int i = 0; i < indicesCount; i++)
        {
            mIndexList[i] = indices32[i];
        }
    }
    else
    {
        UInt16* indices16 = reinterpret_cast<UInt16*>(meshData.indices->indexbuffer.data());
        for (int i = 0; i < indicesCount; i++)
        {
            mIndexList[i] = indices16[i];
        }
    }
}

void ModelImporter::CalculateCentroidList()
{
    mCenteroidList.clear();
    int triangleCount = static_cast<int>(mIndexList.size()) / 3;
    mCenteroidList.resize(triangleCount);
    int i1 = 0;
    for (int i = 0; i < triangleCount; i++)
    {
        int index1 = mIndexList[i1++];
        int index2 = mIndexList[i1++];
        int index3 = mIndexList[i1++];
        AssetMath::Vector3f& point1 = mVertexList[index1];
        AssetMath::Vector3f& point2 = mVertexList[index2];
        AssetMath::Vector3f& point3 = mVertexList[index3];
        mCenteroidList[i] = (point1 + point2 + point3) * 0.3333333333f;
    }
}

CrossSchema::CollisionNodeT* ModelImporter::AllocateCollisionNode(CrossSchema::ImportMeshDataT& meshData)
{
    int nodeIndex = static_cast<int>(meshData.collisiontree.size());

    meshData.collisiontree.push_back(std::move(std::unique_ptr<CrossSchema::CollisionNodeT>(new CrossSchema::CollisionNodeT)));
    CrossSchema::CollisionNodeT* node = (meshData.collisiontree[nodeIndex]).get();
    node->minpos = {INFINITY, INFINITY, INFINITY};
    node->maxpos = {-INFINITY, -INFINITY, -INFINITY};
    node->index = nodeIndex;
    node->leftindex = -1;
    node->rightindex = -1;
    return node;
}

CrossSchema::CollisionNodeT* ModelImporter::CreateRootCollisionNode(CrossSchema::ImportMeshDataT& meshData)
{
    CrossSchema::CollisionNodeT* rootNode = AllocateCollisionNode(meshData);
    int triangleCount = static_cast<int>(mIndexList.size()) / 3;
    rootNode->trianglelist.resize(triangleCount);
    for (int i = 0; i < triangleCount; i++)
    {
        rootNode->trianglelist[i] = i;
    }
    return rootNode;
}

CrossSchema::CollisionNodeT* ModelImporter::GetCollisionNode(CrossSchema::ImportMeshDataT& meshData, int nodeIndex)
{
    if (nodeIndex == -1)
    {
        return nullptr;
    }
    return (meshData.collisiontree[nodeIndex]).get();
}

void ModelImporter::SplitCollisionNode(CrossSchema::CollisionNodeT* node, CrossSchema::ImportMeshDataT* meshData)
{
    std::vector<int>& triangleList = node->trianglelist;
    int triangleCount = static_cast<int>(triangleList.size());
    if (triangleCount < CollisionTriangleCountThreshold)
    {
        return;
    }

    int halfTriangleCount = triangleCount / 2;
    std::vector<int> triangleListLeft;
    std::vector<int> triangleListRight;
    triangleListLeft.reserve(halfTriangleCount);
    triangleListRight.reserve(halfTriangleCount);

    AssetMath::Vector3f Center = AssetMath::Vector3f(node->maxpos[0] + node->minpos[0], node->maxpos[1] + node->minpos[1], node->maxpos[2] + node->minpos[2]) * 0.5f;
    AssetMath::Vector3f Extent = AssetMath::Vector3f(node->maxpos[0] - node->minpos[0], node->maxpos[1] - node->minpos[1], node->maxpos[2] - node->minpos[2]);
    float x = Extent.x;
    float y = Extent.y;
    float z = Extent.z;
    if (x >= y && x >= z)
    {
        float centerX = Center.x;
        for (int i = 0; i < triangleCount; i++)
        {
            int triangleIndex = triangleList[i];
            AssetMath::Vector3f& centroid = mCenteroidList[triangleIndex];
            if (centroid.x < centerX)
            {
                triangleListLeft.push_back(triangleIndex);
            }
            else
            {
                triangleListRight.push_back(triangleIndex);
            }
        }
    }
    else if (y >= x && y >= z)
    {
        float centerY = Center.y;
        for (int i = 0; i < triangleCount; i++)
        {
            int triangleIndex = triangleList[i];
            AssetMath::Vector3f& centroid = mCenteroidList[triangleIndex];
            if (centroid.y < centerY)
            {
                triangleListLeft.push_back(triangleIndex);
            }
            else
            {
                triangleListRight.push_back(triangleIndex);
            }
        }
    }
    else
    {
        float centerZ = Center.z;
        for (int i = 0; i < triangleCount; i++)
        {
            int triangleIndex = triangleList[i];
            AssetMath::Vector3f& centroid = mCenteroidList[triangleIndex];
            if (centroid.z < centerZ)
            {
                triangleListLeft.push_back(triangleIndex);
            }
            else
            {
                triangleListRight.push_back(triangleIndex);
            }
        }
    }

    if (triangleListLeft.size() == 0 || triangleListRight.size() == 0)
    {
        triangleListLeft.clear();
        triangleListRight.clear();
        for (int i = 0; i < triangleCount; i++)
        {
            int triangleIndex = triangleList[i];
            if (i <= halfTriangleCount)
            {
                triangleListLeft.push_back(triangleIndex);
            }
            else
            {
                triangleListRight.push_back(triangleIndex);
            }
        }
    }

    int nodeIndex = node->index;

    if (triangleListLeft.size() > 0)
    {
        CrossSchema::CollisionNodeT* left = AllocateCollisionNode(*meshData);
        node = GetCollisionNode(*meshData, nodeIndex);
        node->leftindex = left->index;
        left->trianglelist = triangleListLeft;
        CalculateLeafBound(left);
        SplitCollisionNode(left, meshData);
    }

    if (triangleListRight.size() > 0)
    {
        CrossSchema::CollisionNodeT* right = AllocateCollisionNode(*meshData);
        node = GetCollisionNode(*meshData, nodeIndex);
        node->rightindex = right->index;
        right->trianglelist = triangleListRight;
        CalculateLeafBound(right);
        SplitCollisionNode(right, meshData);
    }
}

void ModelImporter::CalculateNodeBound(CrossSchema::CollisionNodeT* node, CrossSchema::ImportMeshDataT* meshData)
{
    int leftIndex = node->leftindex;
    CrossSchema::CollisionNodeT* left = GetCollisionNode(*meshData, leftIndex);
    int rightIndex = node->rightindex;
    CrossSchema::CollisionNodeT* right = GetCollisionNode(*meshData, rightIndex);

    if (left || right)
    {
        node->trianglelist.clear();
        if (left)
        {
            CalculateNodeBound(left, meshData);
            CombineChildBound(node, left);
        }
        if (right)
        {
            CalculateNodeBound(right, meshData);
            CombineChildBound(node, right);
        }
    }
    else
    {
        CalculateLeafBound(node);
    }
}

void ModelImporter::CombineChildBound(CrossSchema::CollisionNodeT* node, CrossSchema::CollisionNodeT* child)
{
    node->minpos = {(std::min)(node->minpos[0], child->minpos[0]), (std::min)(node->minpos[1], child->minpos[1]), (std::min)(node->minpos[2], child->minpos[2])};
    node->maxpos = {(std::max)(node->maxpos[0], child->maxpos[0]), (std::max)(node->maxpos[1], child->maxpos[1]), (std::max)(node->maxpos[2], child->maxpos[2])};
}

void ModelImporter::CalculateLeafBound(CrossSchema::CollisionNodeT* node)
{
    std::vector<int>& triangleList = node->trianglelist;
    int triangleCount = static_cast<int>(triangleList.size());
    for (int i = 0; i < triangleCount; i++)
    {
        int triangleIndex = triangleList[i];
        int indexIndex = triangleIndex * 3;
        int index1 = mIndexList[indexIndex];
        int index2 = mIndexList[indexIndex + 1];
        int index3 = mIndexList[indexIndex + 2];
        node->minpos = {(std::min<float>)(node->minpos[0], mVertexList[index1].x), (std::min<float>)(node->minpos[1], mVertexList[index1].y), (std::min<float>)(node->minpos[2], mVertexList[index1].z)};
        node->maxpos = {(std::max<float>)(node->maxpos[0], mVertexList[index1].x), (std::max<float>)(node->maxpos[1], mVertexList[index1].y), (std::max<float>)(node->maxpos[2], mVertexList[index1].z)};
        node->minpos = {(std::min<float>)(node->minpos[0], mVertexList[index2].x), (std::min<float>)(node->minpos[1], mVertexList[index2].y), (std::min<float>)(node->minpos[2], mVertexList[index2].z)};
        node->maxpos = {(std::max<float>)(node->maxpos[0], mVertexList[index2].x), (std::max<float>)(node->maxpos[1], mVertexList[index2].y), (std::max<float>)(node->maxpos[2], mVertexList[index2].z)};
        node->minpos = {(std::min<float>)(node->minpos[0], mVertexList[index3].x), (std::min<float>)(node->minpos[1], mVertexList[index3].y), (std::min<float>)(node->minpos[2], mVertexList[index3].z)};
        node->maxpos = {(std::max<float>)(node->maxpos[0], mVertexList[index3].x), (std::max<float>)(node->maxpos[1], mVertexList[index3].y), (std::max<float>)(node->maxpos[2], mVertexList[index3].z)};
    }
}

}   // namespace cross::editor
