#pragma once

#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/ModelImporter/ModelImportSettings.h"
#include "AssetPipeline/Utils/AnimationCompression/AnimCompress.h"
#include "AssetPipeline/Utils/AssetMath.h"
#include "AssetPipeline/Protocol/Model/ImportScene.h"

namespace cross::editor {

class ASSET_API ModelImporter : public AssetImporter
{
    using PersistenceFunction = TFunction<bool(std::string const&)>;
    using PersistenceFunctionContainer = std::vector<PersistenceFunction>;
    using ImportFunction = TFunction<bool(ImportScene const&, PersistenceFunctionContainer&)>;
    using ImportFunctionContainer = std::unordered_map<ModelType, ImportFunction>;

public:
    ModelImporter();
    virtual ~ModelImporter() = default;

    virtual void CleanUp() {}

    // Legacy Begin
protected:
    bool ImportAssetImpl(ImportScene const& scene, std::string const& name, const std::string& ndaSavePath);

    bool ImportStaticMeshData(ImportScene const& scene, PersistenceFunctionContainer& saver);
    bool ImportSkeletonMeshData(ImportScene const& scene, PersistenceFunctionContainer& saver);

protected:
    void RegisterImporters();
    ModelType DetectImportType(ImportScene const& scene);
    std::string InsertPostfix(std::string const& path, std::string const& postfix);

    bool DropStaticMeshes(CrossSchema::ImportMeshesT const* meshes, std::string const& path);
    bool DropSkeletalMesh(CrossSchema::ImportMeshesT const* meshes, SkeletonDesc const& skeleton, std::string const& path, std::string const& postfix = "");

private:
    ImportFunctionContainer mImporters;
    // Legacy End

public:
    void SerializeSkeleton(const SkeletonDesc& skeleton, const std::string& assetFileName, const std::string& ndaSavePath) const;
    bool SerializeAnimations(const std::vector<AnimationDesc>& animations, const std::string& assetFilePath, const std::string& ndaSavePath, const SkeletonDesc& skeleton) const;

protected:
    void BuildCollisionTree(CrossSchema::ImportMeshDataT& meshData);
    void SerializeReferenceSkeleton(CrossSchema::ImportRefSkeletonT* outFbRefSkelt, const SkeletonDesc& skeleton) const;
    void CompressAndSerializeAnimation(const AnimationDesc& animation, const std::string& assetFilePath, const std::string& ndaSavePath, const SkeletonDesc& skeleton, AnimCompressionType cprType) const;

protected:
    ModelImportSettings* mImportSettings;

private:
    // For Mesh Bound Building(Used by FBXNewImporter)
    std::vector<AssetMath::Vector3f> mVertexList;
    std::vector<int> mIndexList;
    std::vector<AssetMath::Vector3f> mCenteroidList;

    void FillVertexList(CrossSchema::ImportMeshDataT& meshData);
    void FillIndexList(CrossSchema::ImportMeshDataT& meshData);
    void CalculateCentroidList();
    CrossSchema::CollisionNodeT* AllocateCollisionNode(CrossSchema::ImportMeshDataT& meshData);
    CrossSchema::CollisionNodeT* CreateRootCollisionNode(CrossSchema::ImportMeshDataT& meshData);
    CrossSchema::CollisionNodeT* GetCollisionNode(CrossSchema::ImportMeshDataT& meshData, int nodeIndex);
    void SplitCollisionNode(CrossSchema::CollisionNodeT* node, CrossSchema::ImportMeshDataT* meshData);
    void CalculateNodeBound(CrossSchema::CollisionNodeT* node, CrossSchema::ImportMeshDataT* meshData);
    void CombineChildBound(CrossSchema::CollisionNodeT* node, CrossSchema::CollisionNodeT* child);
    void CalculateLeafBound(CrossSchema::CollisionNodeT* node);
};

}   // namespace cross::editor
