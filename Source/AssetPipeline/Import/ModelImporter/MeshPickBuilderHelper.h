#pragma once

#include "ImportMesh_generated.h"
#include "CrossBase/Math/CrossMath.h"
#include "AssetPipeline/MeshPickBuilder/MeshPickBuilder.h"

namespace cross::editor {

class MeshDescription;

class MeshPickBuilderHelper 
{
public:
    using CollisionTree = std::vector<std::unique_ptr<CrossSchema::CollisionNodeT>>;
    using TrianglePos = std::tuple<Float3, Float3, Float3>;
    using GetTrianglePos = std::function<TrianglePos(SInt32)>;
    static MeshPickBuilder mBuilder;
    static void BuildCollisionTree(const MeshDescription& mesh, CollisionTree& collisionTree);
};

}   // namespace cross::editor