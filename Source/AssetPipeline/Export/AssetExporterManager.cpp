#include "AssetPipeline/Export/AssetExporterManager.h"
#include "AssetPipeline/Interface/AssetPipeline.h"

namespace cross::editor 
{
    extern AssetExporterManager gAssetExportManager;

    AssetExporterManager::AssetExporterManager() 
    {
        fbxExporter = new FBXExporter();
    }

    AssetExporterManager::~AssetExporterManager() 
    {
        delete fbxExporter;
    }

    bool AssetExporterManager::ExportSelectModelsAsFBX(const char* ndapath, const char* fbxpath)
    {
        bool flag = fbxExporter->ExportSelectModelsAsFBX(ndapath, fbxpath);
        return flag;
    }

    bool AssetExporterManager::MergeSelectModelsAsFBX(std::vector<std::string> fbxPath, std::vector<cross::Float3> fbxPosition, std::vector<cross::Float3> fbxRotation, std::vector<cross::Float3> fbxScale, const char* fbxpath)
    {
        bool flag = fbxExporter->MergeSelectModelsAsFBX(fbxPath, fbxPosition, fbxRotation, fbxScale, fbxpath);
        return flag;
    }

    AssetExporterManager& AssetExporterManager::Instance() noexcept 
    {
        return gAssetExportManager;
    }
}