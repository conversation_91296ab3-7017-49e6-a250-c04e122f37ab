#pragma once

//Included
#include <stdlib.h>
#include <string>
#include <vector>
#include <mutex>

#include "AssetPipeline/Utils/AssetPipelineAPI.h"

namespace cross::editor
{
    enum class TaskStatus : int
    {
        Ready = 1,
        Running,
        Failed,
        Success
    };


    class BaseTask
    {
    public:
        std::string mSrcFile;
        std::string mDesFile;
        TaskStatus  mStatus;

        BaseTask(std::string srcFile, std::string desFile)
            : mSrcFile(srcFile)
            , mDesFile(desFile)
            , mStatus(TaskStatus::Ready)
        {}
    };


    class BatTaskManager
    {
    public:
        ASSET_API BatTaskManager() = default;
        ASSET_API ~BatTaskManager() = default;

        ASSET_API void InitByDirectory(std::string srcDir, std::string desDir);
        ASSET_API void InitByFile(std::string srcFile, std::string desFile);

        ASSET_API void Run();

        ASSET_API virtual void DoTask(BaseTask* task) = 0;

        ASSET_API virtual void CollectDirectoryTasks(std::string srcDir, std::string desDir)
        {
            std::vector<std::string> dirs, files;

            bool ret = cross::FileHelper::GetFiles(srcDir, dirs, files);
            if (ret)
            {
                for (auto file : files)
                    if (FilterTask(srcDir + "/" + file, desDir + "/" + file))
                    {
                        mAllTasks.emplace_back(new BaseTask(srcDir + "/" + file, desDir + "/" + file));
                    }
                for (auto dir : dirs)
                    CollectDirectoryTasks(srcDir + "/" + dir, desDir + "/" + dir);
            }
        }

        ASSET_API virtual bool FilterTask(const std::string& src_file, const std::string& dst_file) { return true; };

        ASSET_API inline bool IsFinished() { return mFinishNum == mAllTasks.size(); };

        ASSET_API void FinishTask(BaseTask* task, bool result);
        void ShowProgress();

        std::vector<BaseTask*>   mAllTasks;
        int                     mFinishNum = 0;
        std::mutex              mMutex;
        bool Async = true;
    };
}