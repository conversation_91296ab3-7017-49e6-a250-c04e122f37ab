#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "TaskManager.h"

namespace cross::editor {
    void BatTaskManager::InitByDirectory(std::string srcDir, std::string desDir)
    {
        cross::PathHelper::Normalize(srcDir);
        cross::PathHelper::Normalize(desDir);
        CollectDirectoryTasks(srcDir, desDir);
    }

    void BatTaskManager::InitByFile(std::string srcFile, std::string desFile) {
        cross::PathHelper::Normalize(srcFile);
        cross::PathHelper::Normalize(desFile);
        mAllTasks.emplace_back(new BaseTask(srcFile, desFile));
    }

    void BatTaskManager::Run()
    {
        printf("Begin Basks: %zd", mAllTasks.size());
        threading::TaskEventArray cookTaskEvents;
        for (auto& task : mAllTasks)
        {
            if(Async)
                cookTaskEvents.Add(threading::Dispatch([&task, this](const auto&) { DoTask(task); }));
            else
            {
                DoTask(task);
            }
        }
        cookTaskEvents.WaitForCompletion();
    }

    void BatTaskManager::FinishTask(BaseTask* task, bool result) {
        std::lock_guard<std::mutex> lockGurad(mMutex);
        // do finish
        {
            task->mStatus = result ? TaskStatus::Failed : TaskStatus::Success;
            mFinishNum++;
        }
        ShowProgress();
    }

    void BatTaskManager::ShowProgress() {
        printf("\rCooking[%d/%zd]", mFinishNum, mAllTasks.size());
        int show_num = mFinishNum * 100 / static_cast<int>(mAllTasks.size());
        for (int j = 1; j <= show_num; j++)
        {
            std::cout << " ";
        }
    }

}


