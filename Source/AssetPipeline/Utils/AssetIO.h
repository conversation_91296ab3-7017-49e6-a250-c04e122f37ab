#pragma once

#include "CECommon/Utilities/NDACommon.h"
#include "AssetPipeline/Protocol/Protocol.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/filesystem.h"
#include "Resource/Resource.h"
#include "Resource/resourceasset.h"

namespace cross::editor::AssetIO {
class ImportAnimatorDescription;

inline void LoadAssetHeader(AssetHeader& header, SimpleSerializer const& serializer)
{
    serializer.Read(header);
}

void LoadAssetHeader(AssetHeader& header, File& file);

void LoadAssetHeader(AssetHeader& header, filesystem::IFilePtr inFile);

template<typename Asset>
bool SerializeAsset(Asset const& asset, int classID, filesystem::IFilePtr file)
{
    Assert(file);
    if constexpr (std::is_same<flatbuffers::FlatBufferBuilder, Asset>::value)
    {
        filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
        Assert(fileSystem);

        fileSystem->Save(file->GetPathName(), reinterpret_cast<char*>(asset.GetBufferPointer()), asset.GetSize());

        file->Close();
        return true;
    }
    return false;
}

template<typename Asset, typename AssetTypePred>
bool DeserializeAsset(Asset& asset, File& file, AssetTypePred pred)
{
    /*
    {
        FileArchive archive{ file };
        auto buffer = archive.Data();
    }
    */
    if constexpr (std::is_base_of<flatbuffers::NativeTable, Asset>::value)
    {

        if constexpr (std::is_same<CrossSchema::TextureAssetT, Asset>::value)
        {
            FileArchive archive{file};
            auto buffer = archive.Data();
            // auto size = archive.Size();
            auto fbasset = CrossSchema::GetResourceAsset(buffer);
            // auto& table = *flatbuffers::GetAnyRoot(buffer);//reflection
            if (pred(fbasset->resource_type()))
            {
                auto texturebuf = fbasset->resource_as_TextureAsset();
                auto dim = static_cast<CrossSchema::TextureDimension>(texturebuf->GetField<uint8_t>(CrossSchema::TextureAsset::VT_DIMENSION, 0));
                auto form = static_cast<CrossSchema::TextureFormat>(texturebuf->GetField<uint8_t>(CrossSchema::TextureAsset::VT_FORMAT, 0));
                auto colspace = static_cast<CrossSchema::ColorSpace>(texturebuf->GetField<uint8_t>(CrossSchema::TextureAsset::VT_COLORSPACE, 0));
                auto mip = texturebuf->GetField<uint32_t>(CrossSchema::TextureAsset::VT_MIPCOUNT, 0);
                auto flgs = texturebuf->GetField<uint32_t>(CrossSchema::TextureAsset::VT_FLAGS, 0);
                {
                    auto _e = dim;
                    asset.dimension = _e;
                }
                {
                    auto _e = form;
                    asset.format = _e;
                }
                {
                    auto _e = colspace;
                    asset.colorspace = _e;
                }
                {
                    auto _e = mip;
                    asset.mipcount = _e;
                }
                {
                    auto _e = flgs;
                    asset.flags = _e;
                }
                {
                    auto _e = texturebuf->images();
                    if (_e)
                    {
                        asset.images.resize(_e->size());
                        for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++)
                        {
                            asset.images[_i] = *_e->Get(_i);
                        }
                    }
                }
                {
                    auto _e = texturebuf->data();
                    if (_e)
                    {
                        asset.data.resize(_e->size());
                        for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++)
                        {
                            asset.data[_i] = _e->Get(_i);
                        }
                    }
                }

                return true;
            }

            return false;
        }
        /// Only used texture fb currenty
        else
        {
            static_assert("Not used flatbuffers now");
        }
    }
    else
    {
        AssetHeader header;
        FileArchive archive{file};
        SimpleSerializer serializer{archive};
        LoadAssetHeader(header, file);
        if (!pred(header.mClassID))
        {
            return false;
        }
        return serializer.Read(asset);
    }
}

template<typename Asset, typename AssetTypePred>
bool DeserializeAsset(Asset& asset, filesystem::IFilePtr inFile, AssetTypePred pred)
{
    /*
    {
        FileArchive archive{ file };
        auto buffer = archive.Data();
    }
    */
    if constexpr (std::is_base_of<flatbuffers::NativeTable, Asset>::value)
    {
        if constexpr (std::is_same<CrossSchema::TextureAssetT, Asset>::value)
        {
            int metalHeaderOffset = 0;
            ResourceMetaHeader header{};
            if (gResourceAssetMgr.GetResourceMetaHeader(inFile->GetPathName().c_str(), header))
            {
                if (header.mVersion == Resource::gResourceJsonHeaderVersion)
                    metalHeaderOffset = header.mJsonStringLength;
            }

            FileArchive archive{inFile};
            auto buffer = archive.Data() + metalHeaderOffset;
            // auto size = archive.Size();
            auto fbasset = CrossSchema::GetResourceAsset(buffer);
            // auto& table = *flatbuffers::GetAnyRoot(buffer);//reflection
            if (pred(fbasset->resource_type()))
            {
                auto texturebuf = fbasset->resource_as_TextureAsset();
                auto dim = static_cast<CrossSchema::TextureDimension>(texturebuf->GetField<uint8_t>(CrossSchema::TextureAsset::VT_DIMENSION, 0));
                auto form = static_cast<CrossSchema::TextureFormat>(texturebuf->GetField<uint8_t>(CrossSchema::TextureAsset::VT_FORMAT, 0));
                auto colspace = static_cast<CrossSchema::ColorSpace>(texturebuf->GetField<uint8_t>(CrossSchema::TextureAsset::VT_COLORSPACE, 0));
                auto mip = texturebuf->GetField<uint32_t>(CrossSchema::TextureAsset::VT_MIPCOUNT, 0);
                auto flgs = texturebuf->GetField<uint32_t>(CrossSchema::TextureAsset::VT_FLAGS, 0);
                {
                    auto _e = dim;
                    asset.dimension = _e;
                }
                {
                    auto _e = form;
                    asset.format = _e;
                }
                {
                    auto _e = colspace;
                    asset.colorspace = _e;
                }
                {
                    auto _e = mip;
                    asset.mipcount = _e;
                }
                {
                    auto _e = flgs;
                    asset.flags = _e;
                }
                {
                    auto _e = texturebuf->images();
                    if (_e)
                    {
                        asset.images.resize(_e->size());
                        for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++)
                        {
                            asset.images[_i] = *_e->Get(_i);
                        }
                    }
                }
                {
                    auto _e = texturebuf->data();
                    if (_e)
                    {
                        asset.data.resize(_e->size());
                        for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++)
                        {
                            asset.data[_i] = _e->Get(_i);
                        }
                    }
                }

                return true;
            }

            return false;
        }
        /// Only used texture fb currenty
        else
        {
            static_assert("Not used flatbuffers now");
        }
    }
    else
    {
        AssetHeader header;
        FileArchive archive{inFile};
        SimpleSerializer serializer{archive};
        LoadAssetHeader(header, inFile);
        if (!pred(header.mClassID))
        {
            return false;
        }
        return serializer.Read(asset);
    }
}

bool DeserializeTextureAsset(CrossSchema::TextureAssetT& tex, File& file);

bool DeserializeTextureAsset(CrossSchema::TextureAssetT& tex, filesystem::IFilePtr inFile);

ASSET_API bool GetFiles(std::string dir, std::vector<std::string>& dirs, std::vector<std::string>& files);
}   // namespace cross::editor
