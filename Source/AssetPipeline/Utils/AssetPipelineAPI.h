#pragma once

#if CROSSENGINE_WIN
    #pragma warning(disable : 4251)
#ifdef AssetPipeline_EXPORTS
        #define ASSET_API __declspec(dllexport)
    #else
#        define ASSET_API __declspec(dllimport)
    #endif
#define ASSET_STDCALL __stdcall
#else
    #if defined(__GNUC__)
        #define ASSET_API __attribute__ ((visibility("default")))
    #else
        #define ASSET_API
    #endif
#define ASSET_STDCALL
#endif
