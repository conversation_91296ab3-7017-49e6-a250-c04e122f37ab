#pragma once
//=============================================================================
// Assets Functions
//==============================================================================

#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImportSettings.h"

/**
* import assets
*
* @param assetFilePath asset file path
* @return integer of enum AsseType
*/
extern "C" ASSET_API int __stdcall GetAssetType(const char* assetFilePath);

/**
* asset import settings
*
* @param assetType asset type
* @param assetImportSettings asset import settings
* @return void
*/
extern "C" ASSET_API void __stdcall SetImportSettings(int assetType, void* assetImportSettings);

/**
* import assets
*
* @param assetFilePath asset file path
* @param ndaSavePath nda file save directory
* @return 0 if succ, otherwise return -1
*/
extern "C" ASSET_API bool __stdcall ImportAsset(const char* assetFilePath, const char* ndaSavePath = nullptr);

/**
* cook asset
*
* &param srcNdaPath
* &param dstNdaPath
* &param cookPlatform
* &return bool
*/
extern "C" ASSET_API bool __stdcall CookAsset(char const* srcNdaPath, char const* dstNdaPath, int platform);

extern "C" ASSET_API char* __stdcall GetMaterialReferenceString(char *srcNdaPath);

extern "C" ASSET_API char* __stdcall GetSkeletalMeshReferenceString(char *srcNdaPath);

extern "C" ASSET_API char* __stdcall GetAnimationReferenceString(char *srcNdaPath);

using ImportAssetCompleteCallback = void(*) (int64_t, bool, int, const int*);

inline ImportAssetCompleteCallback gImportAssetCompleteCallback = nullptr;
extern "C" ASSET_API void __stdcall SetImportAssetCompleteCallback(ImportAssetCompleteCallback callback);

ASSET_API void __stdcall SetShaderImportSettings(cross::editor::ShaderImportSettings* settings);

ASSET_API void __stdcall SetComputeShaderImportSettings(cross::editor::ShaderImportSettings* settings);

extern "C" ASSET_API bool AssetPipeline_ExportTexture(const char* target, const char* source);