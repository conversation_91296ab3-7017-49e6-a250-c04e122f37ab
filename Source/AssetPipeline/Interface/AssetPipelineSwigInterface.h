#pragma once
#include "AssetPipeline/Import/TerrainImporter/TerrainImporter.h"

namespace cross::editor {
    CEFunction(Editor)
    bool UpdateTerrainHeightmap(const std::string& terrainNdaPath, const std::string& updateDir);

    CEFunction(Editor)
    bool UpdateTerrainWeightTexture(const std::string& terrainNdaPath, const std::string& updateDir);

    CEFunction(Editor)
    TextureResourceInfo GetTextureInfoFromResource(cross::Resource* resource);
}