cmake_minimum_required (VERSION 3.8)

project(AssetPipeline)

# Grab all of our include files
file(GLOB_RECURSE ACL_INCLUDE_FILES LIST_DIRECTORIES false
	../External/acl/*.h)

file(GLOB_RECURSE RTM_INCLUDE_FILES LIST_DIRECTORIES false
	../External/rtm/*.h)

# Grab Acl compression files && Create source group
file(GLOB_RECURSE ACL_INCLUDE_FILES_COMPRESSION LIST_DIRECTORIES false
	../External/acl/compression/*.h
)

# Grab Acl core files && Create source group
file(GLOB_RECURSE ACL_INCLUDE_FILES_CORE LIST_DIRECTORIES false
	../External/acl/core/*.h
)

# Grab Acl decompression files && Create source group
file(GLOB_RECURSE ACL_INCLUDE_FILES_DECOP LIST_DIRECTORIES false
	../External/acl/decompression/*.h
)

# Grab Acl io files && Create source group
file(GLOB_RECURSE ACL_INCLUDE_FILES_IO LIST_DIRECTORIES false
	../External/acl/io/*.h
)

# Grab Acl math files && Create source group
file(GLOB_RECURSE ACL_INCLUDE_FILES_MATH LIST_DIRECTORIES false
	../External/acl/math/*.h
)

# Grab Acl math files && Create source group
file(GLOB_RECURSE WODKA_FILES LIST_DIRECTORIES false
	../External/Wodka/*.h
	../External/Wodka/*.cpp
)

# Grab Acl math files && Create source group
file(GLOB_RECURSE MIKKTSPACE_FILES LIST_DIRECTORIES false
	../External/mikktspace/mikktspace.h
	../External/mikktspace/mikktspace.c
)
set_source_files_properties(../External/mikktspace/mikktspace.c PROPERTIES SKIP_PRECOMPILE_HEADERS ON)
list(APPEND EXTERNAL_FILES ${ACL_INCLUDE_FILES} ${RTM_INCLUDE_FILES} ${ACL_INCLUDE_FILES_COMPRESSION} ${ACL_INCLUDE_FILES_CORE} ${ACL_INCLUDE_FILES_IO} ${ACL_INCLUDE_FILES_MATH} ${WODKA_FILES} ${MIKKTSPACE_FILES})
generate_source_files_from_base_dir(../ ${EXTERNAL_FILES})
# get files
file(GLOB_RECURSE ASSETPIPELINE_H *.h *.hpp *.inl)
file(GLOB_RECURSE ASSETPIPELINE_S *.c *.cpp)
list(APPEND ASSETPIPELINE_FILES ${ASSETPIPELINE_H} ${ASSETPIPELINE_S})
# ignore dirs
list(APPEND IGNORES_DIRS "Import/ModelImporter/UsdImporter" "MeshPickBuilder" "DXILReflection" "AssetExchange" "ShaderGen" "MeshSimplifier" "Schema2Json" "Test" "ShaderChecker")
# ffs dir
foreach(ignore_dir ${IGNORES_DIRS})
	file(GLOB_RECURSE  IGORNE_ITEM_FILES ${ignore_dir}/*)
	list(REMOVE_ITEM ASSETPIPELINE_FILES ${IGORNE_ITEM_FILES})
endforeach()
# ignore files
list(APPEND IGNORES_FILES
	"${CMAKE_CURRENT_SOURCE_DIR}/Import/ModelImporter/FBXImporter/FBXConstraints.h"
	"${CMAKE_CURRENT_SOURCE_DIR}/Import/ModelImporter/FBXImporter/FBXConstraints.cpp"
)
list(REMOVE_ITEM ASSETPIPELINE_FILES ${IGNORES_FILES})
# other files
if(COMPILE_ASSET_PIPELINE_BINARY AND (${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32}))
	list(APPEND ASSETPIPELINE_FILES CommandLine/CommandLine.cc)
endif()
list(APPEND ASSETPIPELINE_FILES
	"${CMAKE_CURRENT_SOURCE_DIR}/AssetPipelineMemory.cpp"
	"${CMAKE_CURRENT_SOURCE_DIR}/DXILReflection/DXILReflection.h"
	"${CMAKE_CURRENT_SOURCE_DIR}/DXILReflection/DXILReflection.cpp"
	"${CMAKE_CURRENT_SOURCE_DIR}/MeshSimplifier/meshoptimizer/simplifier.cpp"
	"${CMAKE_CURRENT_SOURCE_DIR}/MeshSimplifier/meshoptimizer/vfetchoptimizer.cpp"
)
# generate group
generate_source_files(${ASSETPIPELINE_FILES})
# get generate code
get_generated_code(${PROJECT_NAME} GENERATED_CODE)
# gen all files
list(APPEND ASSETPIPELINE_FILES ${EXTERNAL_FILES} ${GENERATED_CODE})

if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4 /WX /wd4200 /EHsc /std:c++20 /MP /Zc:__cplusplus")
elseif(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	#set_target_properties(${PROJECT_NAME} PROPERTIES XCODE_ATTRIBUTE_CLANG_ENABLE_OBJC_ARC "YES")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")
elseif(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_ANDROID})
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20")
endif()


if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	FIND_PACKAGE(OpenMP)
	if(OPENMP_FOUND)
			##message(STATUS "OPENMP FOUND")
			set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
			set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
			set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
	endif()
elseif(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	#todo: macos need integrate openmp for texture loader
endif()


if (${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	add_definitions(-DWIN32)
endif()


if(${_generator} STREQUAL "Visual_Studio_17_2022_Win64")
	set(tempgenerator "Visual_Studio_16_2019_Win64")
else()
	set(tempgenerator "Visual_Studio_16_2019_Win64")
endif()
# link directories
if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	if(${MSVC_CXX_ARCHITECTURE_ID} STREQUAL "x64")
    	link_directories(
			${MANAGED_THIRDPARTY_DIR}/OpenEXR/lib
			${MANAGED_THIRDPARTY_DIR}/cmft/libs/${_generator}_${CROSSENGINE_RUNTIME}/$<CONFIG>)

    	include_directories(
			${MANAGED_THIRDPARTY_DIR}/OpenEXR/include/Imath
			${MANAGED_THIRDPARTY_DIR}/OpenEXR/include/OpenEXR
			${MANAGED_THIRDPARTY_DIR}/cmft/include)

		set(LINK_DIRS_DEBUG
			${MANAGED_THIRDPARTY_DIR}/OpenEXR/lib
			${MANAGED_THIRDPARTY_DIR}/cmft/libs/${_generator}_${CROSSENGINE_RUNTIME}/Debug
			${MANAGED_THIRDPARTY_DIR}/../Source/External/FBXSDK/2020.2/lib/vs2019/x64/debug)
		set(LINK_DIRS
			"${MANAGED_THIRDPARTY_DIR}/../Source/External/FBXSDK/2020.2/lib/vs2019/x64/release"
			)
	endif()

elseif(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	set(LINK_DIRS_DEBUG
		"${MANAGED_THIRDPARTY_DIR}/../Source/External/FBXSDK/2018.1.1_mac/lib/clang/debug"
		"Source/External/ispc_texcomp/lib_mac"
		"${MANAGED_THIRDPARTY_DIR}/basisu/lib/MacOS/"
		"${MANAGED_THIRDPARTY_DIR}/cmft/libs/macos.x86_64.release"
		)
	set(LINK_DIRS
		"${MANAGED_THIRDPARTY_DIR}/../Source/External/FBXSDK/2018.1.1_mac/lib/clang/release"
		"Source/External/ispc_texcomp/lib_mac"
		)
endif()



if((${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32}) OR ${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	SET(INCLUDES
		"${MANAGED_THIRDPARTY_DIR}/../Source/External/ispc_texcomp/include"
		"${MANAGED_THIRDPARTY_DIR}/${BASISU_LIB_NAME}/include"
		"${MANAGED_THIRDPARTY_DIR}/cmft/include"
		"${MANAGED_THIRDPARTY_DIR}/nvtt/include")
endif()

foreach(target ${INCLUDES})
	include_directories("${target}")
endforeach()

if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	list(APPEND LINK_DIRS ${MANAGED_THIRDPARTY_DIR}/nvtt/include ${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/ ${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/$<CONFIG>/)
	list(APPEND LINK_DIRS_DEBUG ${MANAGED_THIRDPARTY_DIR}/nvtt/include ${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/ ${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/$<CONFIG>/)

		list(APPEND LINK_DIRS_DEBUG
			"${MANAGED_THIRDPARTY_DIR}/DirectXMesh/DirectXMesh/Bin/Windows10_2017/x64/Debug"
			"${MANAGED_THIRDPARTY_DIR}/UVAtlas/UVAtlas/Bin/Windows10_2017/x64/Debug"
			"${MANAGED_THIRDPARTY_DIR}/cmft/libs/${_generator}_${CROSSENGINE_RUNTIME}"
			"${MANAGED_THIRDPARTY_DIR}/../Source/External/ispc_texcomp/lib/Debug"
			"${MANAGED_THIRDPARTY_DIR}/hash-library/lib_${GENERATOR_NAME}/debug"
			"${MANAGED_THIRDPARTY_DIR}/physfs-3.0.2/lib_${GENERATOR_NAME}/debug"
			"${MANAGED_THIRDPARTY_DIR}/pystring/lib_${GENERATOR_NAME}/debug"
			"${MANAGED_THIRDPARTY_DIR}/${BASISU_LIB_NAME}/libs/${WINDOWS_LIB_PATH}"
			)

		if(${MSVC_CXX_ARCHITECTURE_ID} STREQUAL "x64")
			list(APPEND LINK_DIRS_DEBUG
				"${MANAGED_THIRDPARTY_DIR}/../Source/External/FBXSDK/2020.2/lib/vs2019/x64/debug")
		else()
			list(APPEND LINK_DIRS_DEBUG
				"${MANAGED_THIRDPARTY_DIR}/../Source/External/FBXSDK/2020.2/lib/vs2019/x86/debug")
		endif()

		list(APPEND LINK_DIRS
			"${MANAGED_THIRDPARTY_DIR}/DirectXMesh/DirectXMesh/Bin/Windows10_2017/x64/Release"
			"${MANAGED_THIRDPARTY_DIR}/UVAtlas/UVAtlas/Bin/Windows10_2017/x64/Release"
			"${MANAGED_THIRDPARTY_DIR}/cmft/libs/${_generator}_${CROSSENGINE_RUNTIME}/Release"
			"${MANAGED_THIRDPARTY_DIR}/../Source/External/ispc_texcomp/lib/Release"
			"${MANAGED_THIRDPARTY_DIR}/hash-library/lib_${GENERATOR_NAME}/release"
			"${MANAGED_THIRDPARTY_DIR}/physfs-3.0.2/lib_${GENERATOR_NAME}/release"
			"${MANAGED_THIRDPARTY_DIR}/pystring/lib_${GENERATOR_NAME}/release"
			"${MANAGED_THIRDPARTY_DIR}/${BASISU_LIB_NAME}/libs/${WINDOWS_LIB_PATH}")
		list(APPEND LINK_DIRS
			"${MANAGED_THIRDPARTY_DIR}/../Source/External/FBXSDK/2020.2/lib/vs2019/x64/release")

		elseif(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	list(APPEND LINK_DIRS_DEBUG
		"${MANAGED_THIRDPARTY_DIR}/cmft/libs/${MACOS_LIB_PATH}"
		"${MANAGED_THIRDPARTY_DIR}/basisu/lib/MacOS")
	list(APPEND LINK_DIRS
		"${MANAGED_THIRDPARTY_DIR}/cmft/libs/${MACOS_LIB_PATH}"
		"${MANAGED_THIRDPARTY_DIR}/basisu/lib/MacOS")
endif()



if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	set(ASSETPIPELINE_LIBRARIES
		libfbxsdk.dll
        libxml2-md.lib
        zlib-md.lib
		DirectXMesh.lib
		UVAtlas.lib
		glu32.lib
		libcmft.lib
		d3dcompiler.lib
		ispc_texcomp.lib
		physfs-static.lib
		pystring-static.lib
		hash-library-static.lib
		basisu_transcoder.lib
		basisu_encoder.dll
		nvtt30106.lib

		# openexr
		OpenEXR-3_1.lib
		OpenEXRCore-3_1.lib
		OpenEXRUtil-3_1.lib
		Iex-3_1.lib
		IlmThread-3_1.lib
		Imath-3_1

		# cmft
		libcmft.lib
		)
	add_definitions(-DFBXSDK_SHARED)
elseif(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	set(ASSETPIPELINE_LIBRARIES
		libfbxsdk.dylib
		libispc_texcomp.dylib
		libbasisu_encoder.dylib
		libbasisu_transcoder.a
		libcmft.a
		"-framework CoreFoundation"
		"-framework OpenGL"
		)
endif()

set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CROSSENGINE_BIN_DIR})

add_definitions(-DASSET_DLL_EXPORTS)

# skip assetpipeline code from unity build
set_source_files_properties(${ASSETPIPELINE_FILES} PROPERTIES SKIP_UNITY_BUILD_INCLUSION ON)

if(COMPILE_ASSET_PIPELINE_BINARY AND (${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32}))
	add_executable(${PROJECT_NAME} ${EXTERNAL_FILES} ${ASSETPIPELINE_FILES} ${GENERATED_CODE})
else()
	add_library(${PROJECT_NAME} SHARED ${EXTERNAL_FILES} ${ASSETPIPELINE_FILES} ${GENERATED_CODE})
endif()
target_link_directories(${PROJECT_NAME} PUBLIC "$<IF:$<CONFIG:Debug>,${LINK_DIRS_DEBUG},${LINK_DIRS}>")
# post build
if (COMPILE_ASSET_PIPELINE_BINARY AND (${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32}))
	add_custom_command(
		TARGET ${PROJECT_NAME}
		POST_BUILD COMMAND xcopy /S /I /F /Y \"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/*.dll\" \"$<TARGET_FILE_DIR:${PROJECT_NAME}>\"
		POST_BUILD COMMAND xcopy /S /I /F /Y \"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/*.pdb\" \"$<TARGET_FILE_DIR:${PROJECT_NAME}>\"
		POST_BUILD COMMAND xcopy /S /I /F /Y \"${MANAGED_THIRDPARTY_DIR}/nvtt/bin/*.dll\" \"${CROSSENGINE_BIN_DIR}/$<CONFIG>\"
		POST_BUILD COMMAND xcopy /S /I /F /Y \"${MANAGED_THIRDPARTY_DIR}/${BASISU_LIB_NAME}/libs/${WINDOWS_LIB_PATH}/basisu_encoder.dll\" \"$<TARGET_FILE_DIR:${PROJECT_NAME}>\"
		POST_BUILD COMMAND xcopy /S /I /F /Y \"${MANAGED_THIRDPARTY_DIR}/${BASISU_LIB_NAME}/libs/${WINDOWS_LIB_PATH}/basisu_encoder.pdb\" \"$<TARGET_FILE_DIR:${PROJECT_NAME}>\")

	# if(COMPILE_EDITOR)
	# 	add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD COMMAND xcopy /S /I /F /Y \"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/$<CONFIG>/dxil.dll\" \"${CROSSEDITOR_BIN_DIR}\")
	# endif()

else()
	if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
		if(WIN32)

			if(COMPILE_EDITOR)
				# set(CopyAssetsDLL echo D | xcopy /S /I /F /Y \"${CROSSENGINE_BIN_DIR}/$<CONFIG>/AssetPipeline.dll\" \"${CROSSEDITOR_BIN_DIR}/$<CONFIG>/\")
				# set(CopyAssetsPDB echo D | xcopy /S /I /F /Y \"${CROSSENGINE_BIN_DIR}/$<CONFIG>/AssetPipeline.pdb\" \"${CROSSEDITOR_BIN_DIR}/$<CONFIG>/\")

				add_custom_command(
					TARGET ${PROJECT_NAME}
					POST_BUILD COMMAND ${CopyAssetsDLL}
					POST_BUILD COMMAND "$<$<CONFIG:Debug>:${CopyAssetsPDB}>"
					COMMAND_EXPAND_LISTS
				)
			endif()

			add_custom_command(
				TARGET ${PROJECT_NAME}
				POST_BUILD COMMAND xcopy /S /I /F /Y \"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/*.dll\" \"$<TARGET_FILE_DIR:${PROJECT_NAME}>\"
				POST_BUILD COMMAND xcopy /S /I /F /Y \"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/*.pdb\" \"$<TARGET_FILE_DIR:${PROJECT_NAME}>\"
				POST_BUILD COMMAND xcopy /S /I /F /Y \"${MANAGED_THIRDPARTY_DIR}/${BASISU_LIB_NAME}/libs/${WINDOWS_LIB_PATH}/basisu_encoder.dll\" \"$<TARGET_FILE_DIR:${PROJECT_NAME}>\"
			)

			# if(COMPILE_EDITOR)
			# 	add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD COMMAND xcopy /S /I /F /Y \"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/${tempgenerator}_${CROSSENGINE_RUNTIME}/$<CONFIG>/dxil.dll\" \"${CROSSEDITOR_BIN_DIR}/$<CONFIG>\")
			# endif()

		endif()
	endif()
endif()

foreach(lib ${ASSETPIPELINE_LIBRARIES})
	target_link_libraries(${PROJECT_NAME} PUBLIC ${lib})
endforeach()

target_include_directories(${PROJECT_NAME} PUBLIC
$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/DXILReflection/>
$<INSTALL_INTERFACE:Source/AssetPipeline/DXILReflection>
)

target_include_directories(${PROJECT_NAME} PUBLIC
$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../CrossSchema/>
$<INSTALL_INTERFACE:Source/CrossSchema/>)


target_precompile_headers(${PROJECT_NAME} PRIVATE "$<$<COMPILE_LANGUAGE:CXX>:${CMAKE_CURRENT_SOURCE_DIR}/PCH/AssetPipelinePCH.h>")

set_output_dir(${PROJECT_NAME})

if (NOT ${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	add_definitions(-D__EMULATE_UUID)
endif()

target_include_directories(${PROJECT_NAME} PRIVATE "${CROSSENGINE_SRC_DIR}/ThirdParty/DirectXShaderCompiler/include")
if (${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	target_link_libraries(${PROJECT_NAME} PRIVATE "dxcompiler.lib")
elseif(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	target_link_libraries(${PROJECT_NAME} "${CROSSENGINE_SRC_DIR}/ThirdParty/bin/Mac/$<CONFIG>/libdxcompiler.3.7.dylib")
endif()

target_include_directories(${PROJECT_NAME} PRIVATE "${CROSSENGINE_SRC_DIR}/ThirdParty/SPIRV-Cross")
if (${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})
	target_link_libraries(${PROJECT_NAME} PRIVATE
		"$<IF:$<CONFIG:Debug>,spirv-cross-cored.lib,spirv-cross-core.lib>"
		"$<IF:$<CONFIG:Debug>,spirv-cross-glsld.lib,spirv-cross-glsl.lib>"
		"$<IF:$<CONFIG:Debug>,spirv-cross-msld.lib,spirv-cross-msl.lib>"
	)
elseif(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	target_link_libraries(${PROJECT_NAME}
		"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/Mac/$<CONFIG>/libspirv-cross-core.a"
		"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/Mac/$<CONFIG>/libspirv-cross-glsl.a"
		"${CROSSENGINE_SRC_DIR}/ThirdParty/bin/Mac/$<CONFIG>/libspirv-cross-msl.a"
	)
endif()

if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_MAC})
	foreach(flagVar	CMAKE_C_FLAGS CMAKE_CXX_FLAGS)
		set(${flagVar} "${${flagVar}} -fms-extensions -Wno-language-extension-token")
	endforeach()

	set_target_properties(${PROJECT_NAME} PROPERTIES BUILD_WITH_INSTALL_RPATH ON INSTALL_RPATH "@executable_path/Frameworks")
endif()


add_subdirectory(MeshPickBuilder)
add_subdirectory(DXILReflection)
add_subdirectory(AssetExchange)
add_subdirectory(ShaderGen)
add_subdirectory(ShaderChecker)
add_subdirectory(MeshSimplifier)
add_subdirectory(Schema2Json)

set(dependItems CEResource CrossBase CrossSchema CECommon FileSystem CrossImage CEAnimation MeshPickBuilder tinyxml2 CrossEngine)
foreach(item ${dependItems})
	add_dependencies(${PROJECT_NAME} ${item})
	target_link_libraries(${PROJECT_NAME} PUBLIC ${item})
endforeach()
SET_PROPERTY(TARGET AssetPipeline PROPERTY FOLDER "AssetTools")

SET_TARGET_PROPERTIES(${PROJECT_NAME} PROPERTIES CSHARP_NAME CEAssetPipeline)
SET_PROPERTY(TARGET ${PROJECT_NAME} PROPERTY MODULE_API_STR "ASSET_API")
