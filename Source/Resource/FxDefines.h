#pragma once
#include "Resource/Resource.h"
#include "NativeGraphicsInterface/NGI.h"
#include "CrossBase/Serialization/SerializeNode.h"

namespace cross::resource
{
    // opacity
    Resource_API const NGITargetBlendStateDesc& GetDefaultTargetBlendState();
    Resource_API const NGIBlendStateDesc& GetDefaultBlendState();
    Resource_API const NGIStencilOperation& GetDefaultStencilOperation();
    Resource_API const NGIDepthStencilStateDesc& GetDefaultDepthStencilState();
    Resource_API const NGIDepthStencilStateDesc& GetDefaultDepthStencilStateNoReverseZ();
    Resource_API const NGIRasterizationStateDesc& GetDefaultRasterizationState();
    Resource_API const NGIDynamicStateDesc& GetDefaultDynamicState();
    // transparent
    Resource_API const NGITargetBlendStateDesc& GetTransparentTargetBlendState();
    Resource_API const NGIBlendStateDesc& GetTransparentBlendState();
    Resource_API const NGIDepthStencilStateDesc& GetTransparentDepthStencilState();
    // TODO(aki): additive

    NGIBlendStateDesc DeserializeBlendStateDesc(const DeserializeNode& jData);
    NGIDepthStencilStateDesc DeserializeDepthStencilStateDesc(const DeserializeNode& jData);
    NGIRasterizationStateDesc DeserializeRasterizerStateDesc(const DeserializeNode& jData);
    NGIDynamicStateDesc DeserializeDynamicStateDesc(const DeserializeNode& jData);
}