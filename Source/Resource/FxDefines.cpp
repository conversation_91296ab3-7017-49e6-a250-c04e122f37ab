#include "EnginePrefix.h"
#include "FxDefines.h"
#include "Runtime/Reflection/TypeGet.h"
#include "meta/reflection/builder/enum_builder.hpp"

namespace cross::resource
{
    const NGITargetBlendStateDesc DefaultBlend
    {
        false,
        false,
        BlendFactor::Zero,
        BlendFactor::One,
        BlendOp::Add,
        BlendFactor::Zero,
        BlendFactor::One,
        BlendOp::Add,
        LogicOp::NoOp,
        ColorMask::All,
    };

    const NGITargetBlendStateDesc DefaultBlendMask{
        false,
        false,
        BlendFactor::Zero,
        BlendFactor::One,
        BlendOp::Add,
        BlendFactor::Zero,
        BlendFactor::One,
        BlendOp::Add,
        LogicOp::NoOp,
        ColorMask::None,
    };

    const NGIBlendStateDesc DefaultBlendState
    {
        false,
        false,
        1,
        {
            DefaultBlend,
            DefaultBlendMask,
            Default<PERSON>lendMask,
            DefaultBlendMask,
            DefaultBlendMask,
            DefaultBlendMask,
            DefaultBlendMask,
            DefaultBlendMask
        }
    };

    const NGIStencilOperation DefaultStencilOperation
    {
        StencilOp::Keep,
        StencilOp::Keep,
        StencilOp::Keep,
        NGIComparisonOp::Always
    };

    const NGIDepthStencilStateDesc DefaultDepthStencilState
    {
        true,
        true,
        ComparisonOp::GreaterEqual,
        false,
        DEFAULT_STENCIL_READ_MASK,
        DEFAULT_STENCIL_WRITE_MASK,
        DefaultStencilOperation,
        DefaultStencilOperation,
    };
    const NGIDepthStencilStateDesc DefaultDepthStencilStateNoReverseZ{
        true,
        true,
        ComparisonOp::Less,
        false,
        DEFAULT_STENCIL_READ_MASK,
        DEFAULT_STENCIL_WRITE_MASK,
        DefaultStencilOperation,
        DefaultStencilOperation,
    };
    const NGIRasterizationStateDesc DefaultRasterizerState
    {
        FillMode::Solid,
        CullMode::Back,
        FaceOrder::CW,
        true,
        false,
        false,
        0,
        0,
        0,
        0,
        0,
        RasterizationMode::DefaultRaster,
        1
    };

    const NGIDynamicStateDesc DefaultDynamicState
    {
        DEFAULT_STENCIL_REFERENCE
    };

    const NGITargetBlendStateDesc& GetDefaultTargetBlendState() { return DefaultBlend; }
    const NGIBlendStateDesc& GetDefaultBlendState() { return DefaultBlendState; }
    const NGIStencilOperation& GetDefaultStencilOperation() { return DefaultStencilOperation; }
    const NGIDepthStencilStateDesc& GetDefaultDepthStencilState() { return DefaultDepthStencilState; }
    const NGIDepthStencilStateDesc& GetDefaultDepthStencilStateNoReverseZ() { return DefaultDepthStencilStateNoReverseZ; }
    const NGIRasterizationStateDesc& GetDefaultRasterizationState() { return DefaultRasterizerState; }
    const NGIDynamicStateDesc& GetDefaultDynamicState() { return DefaultDynamicState; }

    const NGITargetBlendStateDesc TransparentBlend
    {
        true,
        false,
        BlendFactor::SrcAlpha,
        BlendFactor::InvSrcAlpha,
        BlendOp::Add,
        BlendFactor::InvDestAlpha,
        BlendFactor::One,
        BlendOp::Add,
        LogicOp::NoOp,
        ColorMask::All,
    };

    const NGIBlendStateDesc TransparentBlendState
    {
        false,
        false,
        2,
        {
            TransparentBlend,
            TransparentBlend,
            TransparentBlend,
            TransparentBlend,
            TransparentBlend,
            TransparentBlend,
            TransparentBlend,
            TransparentBlend
        }
    };

    const NGIDepthStencilStateDesc TransparentDepthStencilState
    {
        true,
        false,
        ComparisonOp::GreaterEqual,
        false,
        DEFAULT_STENCIL_READ_MASK,
        DEFAULT_STENCIL_WRITE_MASK,
        DefaultStencilOperation,
        DefaultStencilOperation
    };

    const NGITargetBlendStateDesc& GetTransparentTargetBlendState() { return TransparentBlend; }
    const NGIBlendStateDesc& GetTransparentBlendState() { return TransparentBlendState; }
    const NGIDepthStencilStateDesc& GetTransparentDepthStencilState() { return TransparentDepthStencilState; }

    // These map are used to be compatible with old version fx.
    const std::unordered_map<std::string_view, BlendFactor> BlendFactorMap
    {
        {"zero", BlendFactor::Zero},
        {"one", BlendFactor::One},
        {"src_color", BlendFactor::SrcColor},
        {"inv_src_color", BlendFactor::InvSrcColor},
        {"src_alpha", BlendFactor::SrcAlpha},
        {"inv_src_alpha", BlendFactor::InvSrcAlpha},
        {"dst_alpha", BlendFactor::DestAlpha},
        {"inv_dst_alpha", BlendFactor::InvDestAlpha},
        {"dst_color", BlendFactor::DestColor},
        {"inv_dst_color", BlendFactor::InvDestColor},
        // to lower case
        {"srccolor",     BlendFactor::SrcColor},
        {"invsrccolor",  BlendFactor::InvSrcColor},
        {"srcalpha",     BlendFactor::SrcAlpha},
        {"invsrcalpha",  BlendFactor::InvSrcAlpha},
        {"destalpha",    BlendFactor::DestAlpha},
        {"invdestalpha", BlendFactor::InvDestAlpha},
        {"destcolor",    BlendFactor::DestColor},
        {"invdestcolor", BlendFactor::InvDestColor},
        {"srcalphasaturate", BlendFactor::SrcAlphaSaturate},
        {"constant",     BlendFactor::Constant},
        {"invconstant",  BlendFactor::InvConstant},
        {"src1color",    BlendFactor::Src1Color},
        {"invsrc1color", BlendFactor::InvSrc1Color},
        {"src1alpha",    BlendFactor::Src1Alpha},
        {"invsrc1alpha", BlendFactor::InvSrc1Alpha}
    };

    const std::unordered_map<std::string_view, BlendOp> BlendOpMap
    {
        {"unknown", BlendOp::Unknown},
        {"add", BlendOp::Add},
        {"subtract", BlendOp::Subtract},
        {"reverse_subtract", BlendOp::ReverseSubtract},
        {"min", BlendOp::Min},
        {"max", BlendOp::Max},
        // to lower case
        {"reversesubtract", BlendOp::ReverseSubtract}
    };

    const std::unordered_map<std::string_view, ComparisonOp> ComparisonOpMap
    {
        {"unknown", ComparisonOp::Unknown},
        {"never", ComparisonOp::Never},
        {"less", ComparisonOp::Less},
        {"equal", ComparisonOp::Equal},
        {"less_equal", ComparisonOp::LessEqual},
        {"greater", ComparisonOp::Greater},
        {"not_equal", ComparisonOp::NotEqual},
        {"greater_equal", ComparisonOp::GreaterEqual},
        {"always", ComparisonOp::Always},
        {"cmp_never", ComparisonOp::Never},
        {"cmp_less", ComparisonOp::Less},
        {"cmp_equal", ComparisonOp::Equal},
        {"cmp_less_equal", ComparisonOp::LessEqual},
        {"cmp_greater", ComparisonOp::Greater},
        {"cmp_not_equal", ComparisonOp::NotEqual},
        {"cmp_greater_equal", ComparisonOp::GreaterEqual},
        {"cmp_always", ComparisonOp::Always},
        // to lower case
        {"lessequal", ComparisonOp::LessEqual},
        {"notequal", ComparisonOp::NotEqual},
        {"greaterequal", ComparisonOp::GreaterEqual},
        {"cmpnever", ComparisonOp::Never},
        {"cmpless", ComparisonOp::Less},
        {"cmpequal", ComparisonOp::Equal},
        {"cmplessequal", ComparisonOp::LessEqual},
        {"cmpgreater", ComparisonOp::Greater},
        {"cmpnotequal", ComparisonOp::NotEqual},
        {"cmpgreaterequal", ComparisonOp::GreaterEqual},
        {"cmpalways", ComparisonOp::Always},
        // to upper case
        {"CMP_Never", ComparisonOp::Never},
        {"CMP_Less", ComparisonOp::Less},
        {"CMP_Equal", ComparisonOp::Equal},
        {"CMP_LessEqual", ComparisonOp::LessEqual},
        {"CMP_Greater", ComparisonOp::Greater},
        {"CMP_NotEqual", ComparisonOp::NotEqual},
        {"CMP_GreaterEqual", ComparisonOp::GreaterEqual},
        {"CMP_Always", ComparisonOp::Always}
    };

    const std::unordered_map<std::string_view, StencilOp> StencilOpMap
    {
        {"unknown", StencilOp::Unknown},
        {"keep", StencilOp::Keep},
        {"zero", StencilOp::Zero},
        {"replace", StencilOp::Replace},
        {"increment_saturate", StencilOp::IncrementSaturate},
        {"decrement_saturate", StencilOp::DecrementSaturate},
        {"invert", StencilOp::Invert},
        {"increment_wrap", StencilOp::IncrementWarp},
        {"decrement_wrap", StencilOp::DecrementWarp},
        // to lower case
        {"incrementsaturate", StencilOp::IncrementSaturate},
        {"decrementsaturate", StencilOp::DecrementSaturate},
        {"incrementwrap", StencilOp::IncrementWarp},
        {"decrementwrap", StencilOp::DecrementWarp}
    };

    const std::unordered_map<std::string_view, FillMode> FillModeMap
    {
        {"unknown", FillMode::Unknown},
        {"wire_frame", FillMode::WireFrame},
        {"solid", FillMode::Solid},
        // to lower case
        {"wireframe", FillMode::WireFrame}
    };

    const std::unordered_map<std::string_view, CullMode> CullModeMap
    {
        {"none", CullMode::None},
        {"front", CullMode::Front},
        {"back", CullMode::Back},
        {"cm_none", CullMode::None},
        {"cm_front", CullMode::Front},
        {"cm_back", CullMode::Back},
        // to lower case
        {"cmnone", CullMode::None},
        {"cmfront", CullMode::Front},
        {"cmback", CullMode::Back},
        // to upper case
        {"CM_None", CullMode::None},
        {"CM_Front", CullMode::Front},
        {"CM_Back", CullMode::Back}
    };

    const std::unordered_map<std::string_view, RasterizationMode> RasterizationModeMap
    {
        {"conservative_raster", RasterizationMode::ConservativeRaster},
        {"default_raster", RasterizationMode::DefaultRaster},
        // to lower case
        {"conservativeraster", RasterizationMode::ConservativeRaster},
        {"defaultraster", RasterizationMode::DefaultRaster}
    };

    template<typename T, typename = std::enable_if_t<std::is_enum_v<T>>>
    T DeserializeEnum(const DeserializeNode& node, std::string_view name, T defaultVal, const std::unordered_map<std::string_view, T>& mapping = {})
    {
        auto mb = node.HasMember(name);
        if (mb.has_value() && mb->IsString())
        {
            // Find in map first
            if (auto iter = mapping.find(mb->AsStringView()); iter != mapping.end())
            {
                return iter->second;
            }
            // Attempt to reflect
            const gbf::reflection::MetaEnum* metaenum = gbf::reflection::query_meta_enum<T>();
            if (metaenum->HasName(mb->AsString()))
            {
                return static_cast<T>(metaenum->GetValue(mb->AsString()));
            }
        }
        // Cast from underlying type
        return static_cast<T>(node.Value(name, ToUnderlying(defaultVal)));
    }

    template<typename T, typename = std::enable_if_t<std::is_enum_v<T>>>
    void SerializeEnum(SerializeNode& node, std::string_view name, T value)
    {
       const gbf::reflection::MetaEnum* meta_enum = gbf::reflection::query_meta_enum<T>();
        if (meta_enum->HasValue(value))
        {
            node[name] = meta_enum->GetItemName(value);
        }
    }

    ColorMask DeserializeColorWriteMask(const DeserializeNode& node, const char* name, ColorMask defaultVal)
    {
        if (auto mb = node.HasMember(name); mb && mb->IsArray())
        {
            Assert(mb->Size() == 4);
            auto& arr = mb.value();
            ColorMask Value = ColorMask::None;
            Value |= static_cast<int>(arr[0].AsFloat()) == 1 ? ColorMask::R : ColorMask::None;
            Value |= static_cast<int>(arr[1].AsFloat()) == 1 ? ColorMask::G : ColorMask::None;
            Value |= static_cast<int>(arr[2].AsFloat()) == 1 ? ColorMask::B : ColorMask::None;
            Value |= static_cast<int>(arr[3].AsFloat()) == 1 ? ColorMask::A : ColorMask::None;

            return Value;
        }
        return defaultVal;
    }

    void SerializeColorWriteMask(SerializeNode& node, const char* name, ColorMask value)
    {
        SerializeNode mask = SerializeNode::EmptyArray();
        mask.PushBack(EnumHasAnyFlags(value, ColorMask::R) ? 1.0f : 0.0f);
        mask.PushBack(EnumHasAnyFlags(value, ColorMask::G) ? 1.0f : 0.0f);
        mask.PushBack(EnumHasAnyFlags(value, ColorMask::B) ? 1.0f : 0.0f);
        mask.PushBack(EnumHasAnyFlags(value, ColorMask::A) ? 1.0f : 0.0f);
        node[name] = std::move(mask);
    }

    NGIBlendStateDesc DeserializeBlendStateDesc(const DeserializeNode& jData)
    {
        NGIBlendStateDesc desc = DefaultBlendState;

        if (!jData.IsNull())
        {
            if (auto ret = jData.HasMember("blend_targets"); ret)
            {
                if (ret->IsArray())
                {
                    desc.TargetCount = 0;
                    auto& arr = *ret;
                    for (int i = 0; i < ret->Size(); i++)
                    {
                        auto& blend = desc.TargetBlendState[desc.TargetCount];
                        blend.EnableBlend = arr[i].Value("blend_enable", false);
                        blend.SrcBlend = DeserializeEnum(arr[i], "blend_src", BlendFactor::Zero, BlendFactorMap);
                        blend.DestBlend = DeserializeEnum(arr[i], "blend_dst", BlendFactor::One, BlendFactorMap);
                        blend.BlendOp = DeserializeEnum(arr[i], "blend_op", cross::BlendOp::Add, BlendOpMap);
                        blend.SrcBlendAlpha = DeserializeEnum(arr[i], "blend_src_alpha", cross::BlendFactor::Zero, BlendFactorMap);
                        blend.DestBlendAlpha = DeserializeEnum(arr[i], "blend_dst_alpha", cross::BlendFactor::One, BlendFactorMap);
                        blend.BlendOpAlpha = DeserializeEnum(arr[i], "blend_op_alpha", cross::BlendOp::Add, BlendOpMap);
                        blend.WriteMask = DeserializeColorWriteMask(arr[i], "color_write_mask", ColorMask::All);
                        ++desc.TargetCount;
                    }
                }
                else if (ret->IsString())
                {
                    if (ret->AsString() == "opacity")
                    {
                        desc = DefaultBlendState;
                    }
                    else if (ret->AsString() == "transparent")
                    {
                        desc = TransparentBlendState;
                    }
                    // TODO(aki): additive
                }
            }

            auto colorMask = DeserializeColorWriteMask(jData, "color_write_mask", desc.TargetBlendState[0].WriteMask);
            for (UInt32 i = 0; i < desc.TargetCount; i++)
            {
                desc.TargetBlendState[i].WriteMask = colorMask;
            }
            
        }

        return desc;
    }

    NGIDepthStencilStateDesc DeserializeDepthStencilStateDesc(const DeserializeNode& jData)
    {
        NGIDepthStencilStateDesc desc = DefaultDepthStencilState;
        if (jData.IsNull())
            return desc;

        desc.EnableDepth = jData.Value("depth_enable", true);
        desc.EnableDepthWrite = jData.Value("depth_write", true);
        desc.DepthCompareOp = DeserializeEnum(jData, "depth_cmp", ComparisonOp::Less, ComparisonOpMap);

        desc.EnableStencil = jData.Value("stencil_enable", false);
        desc.StencilReadMask = jData.Value("stencil_read_mask", DEFAULT_STENCIL_READ_MASK);
        desc.StencilWriteMask = jData.Value("stencil_write_mask", DEFAULT_STENCIL_WRITE_MASK);

        auto DeserializeStencilOperation = [](const auto& node, auto name, auto& out) {
            if (node.HasMember(name))
            {
                auto op = node[name];
                out.StencilFailOp = DeserializeEnum(op, "fail_op", StencilOp::Keep, StencilOpMap);
                out.StencilDepthFailOp = DeserializeEnum(op, "depth_fail_op", StencilOp::Keep, StencilOpMap);
                out.StencilPassOp = DeserializeEnum(op, "pass_op", StencilOp::Keep, StencilOpMap);
                out.StencilCompareOp = DeserializeEnum(op, "compare_op", NGIComparisonOp::Always, ComparisonOpMap);
            }
        };
        DeserializeStencilOperation(jData, "front_face_stencil_op", desc.FrontFace);
        DeserializeStencilOperation(jData, "back_face_stencil_op", desc.BackFace);

        return desc;
    }

    NGIRasterizationStateDesc DeserializeRasterizerStateDesc(const DeserializeNode& jData)
    {
        NGIRasterizationStateDesc desc = DefaultRasterizerState;
        if (jData.IsNull())
            return desc;

        desc.FillMode = DeserializeEnum(jData, "fill_mode", FillMode::Solid, FillModeMap);
        desc.CullMode = DeserializeEnum(jData, "cull", CullMode::Back, CullModeMap);
        
        desc.EnableDepthClip = jData.Value("depth_clip", true);

        desc.EnableDepthBias = jData.Value("enable_depth_bias", false);
        desc.DepthBias = static_cast<SInt16>(jData.Value("depth_bias", 0));
        desc.SlopeScaledDepthBias = jData.Value("slope_scaled_depth_bias", 0.f);
        desc.DepthBiasClamp = jData.Value("depth_bias_clamp", 0.f);

        desc.RasterMode = DeserializeEnum(jData, "rasterization_mode", RasterizationMode::DefaultRaster, RasterizationModeMap);
        desc.RasterOverestimationSize = static_cast<UInt32>(jData.Value("raster_overestimation_size", 1));

        return desc;
    }

    NGIDynamicStateDesc DeserializeDynamicStateDesc(const DeserializeNode& jData)
    {
        NGIDynamicStateDesc desc = DefaultDynamicState;
        if (!jData.IsNull())
        {
            desc.StencilReference = jData.Value("stencil_reference", DEFAULT_STENCIL_REFERENCE);
        }
        return desc;
    }
}
